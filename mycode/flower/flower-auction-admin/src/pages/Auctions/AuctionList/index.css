.auction-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.auction-list-container .ant-typography {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 16px;
}

.search-card .ant-card-body {
  padding: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-card .ant-card-body {
  padding: 12px 16px;
}

/* 统计卡片样式 */
.auction-list-container .ant-statistic {
  text-align: center;
}

.auction-list-container .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.auction-list-container .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 表格样式 */
.auction-list-container .ant-table-wrapper {
  background: white;
  border-radius: 6px;
}

.auction-list-container .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.auction-list-container .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 状态标签样式 */
.auction-list-container .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

.auction-list-container .ant-badge {
  display: flex;
  align-items: center;
}

/* 操作按钮样式 */
.auction-list-container .ant-btn-link {
  padding: 0;
  height: auto;
}

/* 模态框样式 */
.auction-list-container .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.auction-list-container .ant-modal-body {
  padding: 24px;
}

/* 表单样式 */
.auction-list-container .ant-form-item-label > label {
  font-weight: 500;
}

.auction-list-container .ant-input,
.auction-list-container .ant-select-selector {
  border-radius: 4px;
}

.auction-list-container .ant-input:focus,
.auction-list-container .ant-input-focused,
.auction-list-container .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 分页样式 */
.auction-list-container .ant-pagination {
  margin-top: 16px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auction-list-container {
    padding: 16px;
  }
  
  .search-card .ant-form-item {
    margin-bottom: 16px;
  }
  
  .action-card .ant-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .auction-list-container .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .auction-list-container .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
}

@media (max-width: 576px) {
  .auction-list-container {
    padding: 12px;
  }
  
  .auction-list-container .ant-typography {
    margin-bottom: 16px;
    font-size: 20px;
  }
  
  .search-card,
  .action-card {
    margin-bottom: 12px;
  }
  
  .auction-list-container .ant-modal-body {
    padding: 16px;
  }
  
  .auction-list-container .ant-statistic-content {
    font-size: 20px;
  }
}
