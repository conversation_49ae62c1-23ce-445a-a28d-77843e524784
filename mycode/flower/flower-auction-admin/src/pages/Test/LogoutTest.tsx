import React from 'react';
import { Card, Typography, Space, Divider } from 'antd';
import { LogoutButton, UserMenu } from '../../components';

const { Title, Paragraph, Text } = Typography;

const LogoutTest: React.FC = () => {
  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>登出功能测试页面</Title>
      
      <Card title="登出组件测试" style={{ marginBottom: 24 }}>
        <Paragraph>
          这个页面用于测试各种登出组件的功能和样式。
        </Paragraph>
        
        <Divider orientation="left">LogoutButton 组件</Divider>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Text strong>默认样式：</Text>
            <div style={{ marginTop: 8 }}>
              <LogoutButton />
            </div>
          </div>
          
          <div>
            <Text strong>主要按钮样式：</Text>
            <div style={{ marginTop: 8 }}>
              <LogoutButton type="primary" />
            </div>
          </div>
          
          <div>
            <Text strong>危险按钮样式：</Text>
            <div style={{ marginTop: 8 }}>
              <LogoutButton type="primary" style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }} />
            </div>
          </div>
          
          <div>
            <Text strong>文字按钮样式：</Text>
            <div style={{ marginTop: 8 }}>
              <LogoutButton type="text" />
            </div>
          </div>
          
          <div>
            <Text strong>链接按钮样式：</Text>
            <div style={{ marginTop: 8 }}>
              <LogoutButton type="link" />
            </div>
          </div>
          
          <div>
            <Text strong>无图标：</Text>
            <div style={{ marginTop: 8 }}>
              <LogoutButton icon={false} />
            </div>
          </div>
          
          <div>
            <Text strong>自定义文字：</Text>
            <div style={{ marginTop: 8 }}>
              <LogoutButton>安全退出</LogoutButton>
            </div>
          </div>
          
          <div>
            <Text strong>块级按钮：</Text>
            <div style={{ marginTop: 8 }}>
              <LogoutButton block type="primary">
                退出系统
              </LogoutButton>
            </div>
          </div>
        </Space>
        
        <Divider orientation="left">UserMenu 组件</Divider>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Text strong>默认用户菜单：</Text>
            <div style={{ marginTop: 8 }}>
              <UserMenu />
            </div>
          </div>
          
          <div>
            <Text strong>不显示用户名：</Text>
            <div style={{ marginTop: 8 }}>
              <UserMenu showUsername={false} />
            </div>
          </div>
          
          <div>
            <Text strong>大头像：</Text>
            <div style={{ marginTop: 8 }}>
              <UserMenu avatarSize="large" />
            </div>
          </div>
          
          <div>
            <Text strong>小头像：</Text>
            <div style={{ marginTop: 8 }}>
              <UserMenu avatarSize="small" showUsername={false} />
            </div>
          </div>
        </Space>
      </Card>
      
      <Card title="功能说明">
        <Paragraph>
          <Text strong>LogoutButton 组件特性：</Text>
        </Paragraph>
        <ul>
          <li>支持多种按钮样式（primary、default、dashed、link、text）</li>
          <li>支持自定义大小（large、middle、small）</li>
          <li>支持块级按钮（block）</li>
          <li>支持显示/隐藏图标</li>
          <li>支持自定义文字内容</li>
          <li>内置确认弹窗，防止误操作</li>
          <li>自动处理登出流程和错误处理</li>
        </ul>
        
        <Paragraph style={{ marginTop: 16 }}>
          <Text strong>UserMenu 组件特性：</Text>
        </Paragraph>
        <ul>
          <li>集成用户头像和用户名显示</li>
          <li>支持显示/隐藏用户名</li>
          <li>支持自定义头像大小</li>
          <li>支持自定义下拉菜单位置</li>
          <li>包含完整的用户操作菜单（个人中心、账号设置、修改密码、退出登录）</li>
          <li>内置登出确认弹窗</li>
          <li>自动处理登出流程</li>
        </ul>
        
        <Paragraph style={{ marginTop: 16 }}>
          <Text strong>安全特性：</Text>
        </Paragraph>
        <ul>
          <li>登出时调用后端API使token失效</li>
          <li>清除所有本地存储数据</li>
          <li>清除会话存储数据</li>
          <li>清除应用相关缓存数据</li>
          <li>自动跳转到登录页</li>
          <li>完善的错误处理机制</li>
        </ul>
      </Card>
    </div>
  );
};

export default LogoutTest;
