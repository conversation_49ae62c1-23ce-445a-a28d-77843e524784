import React from 'react';
import { Card, Typography, Space, Button, Alert } from 'antd';
import { BellOutlined, UserOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

const HeaderTest: React.FC = () => {
  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>Header显示测试页面</Title>
      
      <Alert
        message="Header显示问题修复"
        description="此页面用于测试Header在侧边栏展开/收起时的显示情况。请检查右上角的个人菜单是否正常显示。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Card title="修复内容" style={{ marginBottom: 24 }}>
        <Paragraph>
          <Text strong>问题描述：</Text>
          左侧栏展开时，右上角的个人菜单不显示或被遮挡。
        </Paragraph>
        
        <Paragraph>
          <Text strong>修复方案：</Text>
        </Paragraph>
        <ul>
          <li>调整Header的z-index从9提升到1000，确保在最上层显示</li>
          <li>设置Header的位置和宽度，根据侧边栏状态动态调整</li>
          <li>添加collapsed类名，支持侧边栏收起时的样式</li>
          <li>优化响应式设计，确保移动端正常显示</li>
          <li>为Header右侧区域设置更高的z-index（1001）</li>
        </ul>
        
        <Paragraph>
          <Text strong>CSS调整：</Text>
        </Paragraph>
        <ul>
          <li>Header宽度：calc(100% - 200px) 展开时，calc(100% - 80px) 收起时</li>
          <li>Header位置：left: 200px 展开时，left: 80px 收起时</li>
          <li>添加transition动画，平滑过渡</li>
          <li>移动端适配：left: 0, width: 100%</li>
        </ul>
      </Card>
      
      <Card title="测试说明">
        <Paragraph>
          <Text strong>测试步骤：</Text>
        </Paragraph>
        <ol>
          <li>点击左上角的菜单折叠按钮 <MenuFoldOutlined /> / <MenuUnfoldOutlined /></li>
          <li>观察右上角的通知图标 <BellOutlined /> 和用户头像 <UserOutlined /> 是否正常显示</li>
          <li>点击用户头像，检查下拉菜单是否能正常弹出</li>
          <li>测试登出功能是否正常工作</li>
          <li>在不同屏幕尺寸下测试响应式效果</li>
        </ol>
        
        <Paragraph style={{ marginTop: 16 }}>
          <Text strong>预期结果：</Text>
        </Paragraph>
        <ul>
          <li>Header始终显示在页面顶部，不被侧边栏遮挡</li>
          <li>个人菜单在任何情况下都能正常点击和操作</li>
          <li>侧边栏展开/收起时，Header宽度平滑调整</li>
          <li>移动端下Header占满整个宽度</li>
        </ul>
        
        <div style={{ marginTop: 16, padding: 16, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
          <Text type="secondary">
            💡 提示：如果仍然遇到显示问题，请检查浏览器开发者工具中的CSS样式是否正确应用。
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default HeaderTest;
