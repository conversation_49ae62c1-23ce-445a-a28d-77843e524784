import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Typography,
  Input,
  Button,
  Space,
  Avatar,
  Badge,
  Row,
  Col,
  Tag,
  Divider,
  Upload,
  message,
  Spin,
} from 'antd';
import {
  CustomerServiceOutlined,
  SendOutlined,
  PaperClipOutlined,
  SmileOutlined,
  PhoneOutlined,
  MailOutlined,
  ClockCircleOutlined,
  UserOutlined,
  RobotOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;

// 消息接口
interface Message {
  id: string;
  content: string;
  type: 'text' | 'image' | 'file';
  sender: 'user' | 'service' | 'system';
  timestamp: string;
  avatar?: string;
  senderName?: string;
  fileUrl?: string;
  fileName?: string;
}

// 客服信息接口
interface ServiceAgent {
  id: string;
  name: string;
  avatar: string;
  status: 'online' | 'busy' | 'offline';
  title: string;
  rating: number;
}

const OnlineSupport: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [connected, setConnected] = useState(false);
  const [currentAgent, setCurrentAgent] = useState<ServiceAgent | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [typing, setTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 模拟客服信息
  const [agents] = useState<ServiceAgent[]>([
    {
      id: '1',
      name: '小花',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=agent1',
      status: 'online',
      title: '高级客服专员',
      rating: 4.9,
    },
    {
      id: '2',
      name: '小草',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=agent2',
      status: 'busy',
      title: '技术支持专员',
      rating: 4.8,
    },
  ]);

  // 初始化消息
  useEffect(() => {
    const initialMessages: Message[] = [
      {
        id: '1',
        content: '欢迎使用昆明花卉拍卖系统在线客服！我是智能助手小花，很高兴为您服务。',
        type: 'text',
        sender: 'system',
        timestamp: new Date().toISOString(),
        senderName: '系统消息',
      },
      {
        id: '2',
        content: '请问有什么可以帮助您的吗？您可以：\n1. 咨询系统使用问题\n2. 反馈技术故障\n3. 了解业务流程\n4. 其他问题咨询',
        type: 'text',
        sender: 'service',
        timestamp: new Date().toISOString(),
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=agent1',
        senderName: '小花',
      },
    ];
    setMessages(initialMessages);
  }, []);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 连接客服
  const handleConnect = () => {
    setLoading(true);
    setTimeout(() => {
      setConnected(true);
      setCurrentAgent(agents[0]);
      setLoading(false);
      
      const connectMessage: Message = {
        id: Date.now().toString(),
        content: '已为您接入人工客服，我是小花，请问有什么可以帮助您的？',
        type: 'text',
        sender: 'service',
        timestamp: new Date().toISOString(),
        avatar: agents[0].avatar,
        senderName: agents[0].name,
      };
      setMessages(prev => [...prev, connectMessage]);
    }, 2000);
  };

  // 发送消息
  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      type: 'text',
      sender: 'user',
      timestamp: new Date().toISOString(),
      senderName: '我',
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');

    // 模拟客服回复
    if (connected && currentAgent) {
      setTyping(true);
      setTimeout(() => {
        const replyMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: getAutoReply(inputValue),
          type: 'text',
          sender: 'service',
          timestamp: new Date().toISOString(),
          avatar: currentAgent.avatar,
          senderName: currentAgent.name,
        };
        setMessages(prev => [...prev, replyMessage]);
        setTyping(false);
      }, 1000 + Math.random() * 2000);
    }
  };

  // 自动回复逻辑
  const getAutoReply = (userMessage: string): string => {
    const message = userMessage.toLowerCase();
    
    if (message.includes('密码') || message.includes('登录')) {
      return '关于密码和登录问题，您可以：\n1. 点击登录页面的"忘记密码"重置\n2. 检查用户名是否正确\n3. 确认账户是否被锁定\n\n如果仍有问题，我可以帮您进一步处理。';
    }
    
    if (message.includes('拍卖') || message.includes('出价')) {
      return '关于拍卖功能，我来为您介绍：\n1. 荷兰式拍卖：价格递减，先出价者得\n2. 英式拍卖：价格递增，最高价者得\n3. 出价前请确保账户余额充足\n\n需要了解具体哪个方面呢？';
    }
    
    if (message.includes('支付') || message.includes('付款')) {
      return '我们支持多种支付方式：\n1. 支付宝、微信支付\n2. 银联在线支付\n3. 银行转账\n\n支付遇到问题了吗？请详细描述一下情况。';
    }
    
    if (message.includes('审核') || message.includes('认证')) {
      return '关于审核认证：\n1. 个人认证：1-3个工作日\n2. 企业认证：3-5个工作日\n3. 商品审核：1-3个工作日\n\n您的审核遇到什么问题了吗？';
    }
    
    return '我理解您的问题，让我为您详细解答。根据您的描述，建议您可以先查看用户手册中的相关章节。如果问题仍未解决，我会为您转接专业技术人员。';
  };

  // 文件上传
  const handleFileUpload = (file: any) => {
    const fileMessage: Message = {
      id: Date.now().toString(),
      content: `上传了文件：${file.name}`,
      type: 'file',
      sender: 'user',
      timestamp: new Date().toISOString(),
      fileName: file.name,
      fileUrl: URL.createObjectURL(file),
    };
    setMessages(prev => [...prev, fileMessage]);
    message.success('文件上传成功');
    return false; // 阻止默认上传行为
  };

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    const config = {
      online: { status: 'success', text: '在线' },
      busy: { status: 'warning', text: '忙碌' },
      offline: { status: 'default', text: '离线' },
    };
    const { status: badgeStatus, text } = config[status as keyof typeof config];
    return <Badge status={badgeStatus as any} text={text} />;
  };

  // 渲染消息
  const renderMessage = (message: Message) => {
    const isUser = message.sender === 'user';
    const isSystem = message.sender === 'system';

    return (
      <div
        key={message.id}
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginBottom: 16,
        }}
      >
        {!isUser && (
          <Avatar
            src={message.avatar}
            icon={isSystem ? <RobotOutlined /> : <UserOutlined />}
            style={{ marginRight: 8 }}
          />
        )}
        
        <div style={{ maxWidth: '70%' }}>
          {!isUser && (
            <div style={{ marginBottom: 4 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {message.senderName} {new Date(message.timestamp).toLocaleTimeString()}
              </Text>
            </div>
          )}
          
          <div
            style={{
              padding: '8px 12px',
              borderRadius: 8,
              backgroundColor: isUser ? '#1890ff' : '#f5f5f5',
              color: isUser ? 'white' : 'black',
              whiteSpace: 'pre-line',
            }}
          >
            {message.type === 'file' ? (
              <Space>
                <PaperClipOutlined />
                <Text style={{ color: isUser ? 'white' : 'inherit' }}>
                  {message.fileName}
                </Text>
              </Space>
            ) : (
              message.content
            )}
          </div>
          
          {isUser && (
            <div style={{ textAlign: 'right', marginTop: 4 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {new Date(message.timestamp).toLocaleTimeString()}
              </Text>
            </div>
          )}
        </div>
        
        {isUser && (
          <Avatar
            icon={<UserOutlined />}
            style={{ marginLeft: 8, backgroundColor: '#1890ff' }}
          />
        )}
      </div>
    );
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <CustomerServiceOutlined /> 在线客服
      </Title>

      <Row gutter={24}>
        <Col span={16}>
          <Card
            title={
              <Space>
                <span>客服对话</span>
                {connected && currentAgent && (
                  <Tag color="green">
                    已连接 - {currentAgent.name}
                  </Tag>
                )}
              </Space>
            }
            extra={
              !connected && (
                <Button
                  type="primary"
                  icon={<CustomerServiceOutlined />}
                  onClick={handleConnect}
                  loading={loading}
                >
                  连接人工客服
                </Button>
              )
            }
          >
            {/* 消息列表 */}
            <div
              style={{
                height: 400,
                overflowY: 'auto',
                padding: '0 16px',
                border: '1px solid #f0f0f0',
                borderRadius: 6,
                marginBottom: 16,
              }}
            >
              {messages.map(renderMessage)}
              
              {typing && (
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                  <Avatar
                    src={currentAgent?.avatar}
                    style={{ marginRight: 8 }}
                  />
                  <div
                    style={{
                      padding: '8px 12px',
                      borderRadius: 8,
                      backgroundColor: '#f5f5f5',
                    }}
                  >
                    <Spin size="small" />
                    <Text style={{ marginLeft: 8 }}>正在输入...</Text>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* 输入区域 */}
            <Space.Compact style={{ width: '100%' }}>
              <TextArea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="请输入您的问题..."
                autoSize={{ minRows: 1, maxRows: 3 }}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />
              <Upload
                beforeUpload={handleFileUpload}
                showUploadList={false}
              >
                <Button icon={<PaperClipOutlined />} />
              </Upload>
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSendMessage}
                disabled={!inputValue.trim()}
              >
                发送
              </Button>
            </Space.Compact>
          </Card>
        </Col>

        <Col span={8}>
          {/* 客服信息 */}
          {connected && currentAgent && (
            <Card title="当前客服" style={{ marginBottom: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar size={64} src={currentAgent.avatar} />
                  <div style={{ marginTop: 8 }}>
                    <Text strong>{currentAgent.name}</Text>
                    <br />
                    <Text type="secondary">{currentAgent.title}</Text>
                    <br />
                    {getStatusBadge(currentAgent.status)}
                  </div>
                </div>
                <Divider />
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button block>评价服务</Button>
                  <Button block>转接其他客服</Button>
                  <Button block type="text" danger>
                    结束对话
                  </Button>
                </Space>
              </Space>
            </Card>
          )}

          {/* 联系方式 */}
          <Card title="其他联系方式">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <PhoneOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                <Text strong>客服热线</Text>
                <br />
                <Text>************</Text>
                <br />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  工作时间：8:00-20:00
                </Text>
              </div>
              
              <Divider style={{ margin: '12px 0' }} />
              
              <div>
                <MailOutlined style={{ marginRight: 8, color: '#52c41a' }} />
                <Text strong>邮箱支持</Text>
                <br />
                <Text><EMAIL></Text>
                <br />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  24小时内回复
                </Text>
              </div>
              
              <Divider style={{ margin: '12px 0' }} />
              
              <div>
                <ClockCircleOutlined style={{ marginRight: 8, color: '#faad14' }} />
                <Text strong>服务时间</Text>
                <br />
                <Text>周一至周日</Text>
                <br />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  在线客服：9:00-18:00
                </Text>
              </div>
            </Space>
          </Card>

          {/* 快速问题 */}
          <Card title="快速问题" style={{ marginTop: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {[
                '如何重置密码？',
                '如何参与拍卖？',
                '支付方式有哪些？',
                '商品审核要多久？',
                '如何联系卖家？',
              ].map((question, index) => (
                <Button
                  key={index}
                  type="link"
                  block
                  style={{ textAlign: 'left', padding: '4px 0' }}
                  onClick={() => setInputValue(question)}
                >
                  {question}
                </Button>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OnlineSupport;
