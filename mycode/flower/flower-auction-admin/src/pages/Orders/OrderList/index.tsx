import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  message,
  Typography,
  Row,
  Col,
  DatePicker,
  Statistic,
  Descriptions,
  Badge,
  Form,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  ExportOutlined,
  ReloadOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { orderService } from '../../../services/orderService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 订单状态枚举
export enum OrderStatus {
  PENDING_PAYMENT = 1,    // 待付款
  PAID = 2,              // 已付款
  SHIPPED = 3,           // 已发货
  DELIVERED = 4,         // 已送达
  COMPLETED = 5,         // 已完成
  CANCELLED = 6,         // 已取消
  REFUNDED = 7,          // 已退款
}

// 支付方式枚举
export enum PaymentMethod {
  ALIPAY = 'alipay',     // 支付宝
  WECHAT = 'wechat',     // 微信支付
  BANK = 'bank',         // 银行转账
  CASH = 'cash',         // 现金
}

// 订单数据接口
export interface Order {
  id: number;
  orderNo: string;
  buyerId: number;
  buyerName: string;
  sellerId: number;
  sellerName: string;
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  paymentMethod: PaymentMethod;
  status: OrderStatus;
  shippingAddress: string;
  shippingPhone: string;
  remark?: string;
  createdAt: string;
  updatedAt: string;
  paidAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
}

// 查询参数接口
interface OrderQueryParams {
  orderNo?: string;
  buyerName?: string;
  sellerName?: string;
  status?: OrderStatus;
  paymentMethod?: PaymentMethod;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

const OrderList: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<OrderQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [viewingOrder, setViewingOrder] = useState<Order | null>(null);
  const [statistics, setStatistics] = useState({
    totalOrders: 0,
    totalAmount: 0,
    todayOrders: 0,
    pendingOrders: 0,
  });

  // 防抖定时器引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 订单状态映射
  const orderStatusMap = {
    [OrderStatus.PENDING_PAYMENT]: { label: '待付款', color: 'orange' },
    [OrderStatus.PAID]: { label: '已付款', color: 'blue' },
    [OrderStatus.SHIPPED]: { label: '已发货', color: 'cyan' },
    [OrderStatus.DELIVERED]: { label: '已送达', color: 'purple' },
    [OrderStatus.COMPLETED]: { label: '已完成', color: 'green' },
    [OrderStatus.CANCELLED]: { label: '已取消', color: 'red' },
    [OrderStatus.REFUNDED]: { label: '已退款', color: 'magenta' },
  };

  // 支付方式映射
  const paymentMethodMap = {
    [PaymentMethod.ALIPAY]: { label: '支付宝', color: 'blue' },
    [PaymentMethod.WECHAT]: { label: '微信支付', color: 'green' },
    [PaymentMethod.BANK]: { label: '银行转账', color: 'orange' },
    [PaymentMethod.CASH]: { label: '现金', color: 'gray' },
  };

  // 获取订单列表（带防抖）
  const fetchOrders = useCallback(async () => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 设置新的定时器
    debounceTimerRef.current = setTimeout(async () => {
      setLoading(true);
      try {
        console.log('获取订单列表，参数:', queryParams);
        const response = await orderService.getOrderList(queryParams);
        if (response.success) {
          setOrders(response.data.list);
          setTotal(response.data.total);
        } else {
          message.error(response.message || '获取订单列表失败');
        }
      } catch (error: any) {
        console.error('获取订单列表失败:', error);
        message.error(error.message || '获取订单列表失败');
      } finally {
        setLoading(false);
      }
    }, 300); // 300ms防抖延迟
  }, [queryParams]);

  // 获取订单统计
  const fetchStatistics = async () => {
    try {
      const response = await orderService.getOrderStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error: any) {
      console.error('获取订单统计失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchOrders();
  }, [queryParams]);

  // 统计数据只在组件初始化时加载一次
  useEffect(() => {
    fetchStatistics();
  }, []);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 查看订单详情
  const handleView = (order: Order) => {
    setViewingOrder(order);
    setIsDetailVisible(true);
  };

  // 导出订单数据
  const handleExport = async () => {
    try {
      const response = await orderService.exportOrders(queryParams);
      if (response.success) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `orders_${new Date().getTime()}.xlsx`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
        message.success('导出成功');
      } else {
        message.error(response.message || '导出失败');
      }
    } catch (error: any) {
      message.error(error.message || '导出失败');
    }
  };

  // 更新订单状态
  const handleUpdateStatus = async (orderId: number, status: OrderStatus, remark?: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    const statusText = {
      [OrderStatus.PENDING_PAYMENT]: '待付款',
      [OrderStatus.PAID]: '已付款',
      [OrderStatus.SHIPPED]: '已发货',
      [OrderStatus.DELIVERED]: '已送达',
      [OrderStatus.COMPLETED]: '已完成',
      [OrderStatus.CANCELLED]: '已取消',
      [OrderStatus.REFUNDED]: '已退款',
    };

    // 只定义需要特殊操作文本的状态
    const actionText: Partial<Record<OrderStatus, string>> = {
      [OrderStatus.CANCELLED]: '取消',
      [OrderStatus.SHIPPED]: '发货',
      [OrderStatus.DELIVERED]: '确认送达',
      [OrderStatus.COMPLETED]: '完成',
      [OrderStatus.REFUNDED]: '退款',
    };

    Modal.confirm({
      title: `确认${actionText[status] || '更新'}订单`,
      content: (
        <div>
          <p>确定要将订单 <strong>"{order.orderNo}"</strong> 状态更新为 <strong>"{statusText[status]}"</strong> 吗？</p>
          {status === OrderStatus.CANCELLED && (
            <p style={{ color: '#ff4d4f', fontSize: '12px' }}>
              ⚠️ 取消后订单将无法恢复，请确认操作
            </p>
          )}
          {status === OrderStatus.SHIPPED && (
            <p style={{ color: '#1890ff', fontSize: '12px' }}>
              📦 发货后买家将收到物流信息通知
            </p>
          )}
          {status === OrderStatus.DELIVERED && (
            <p style={{ color: '#52c41a', fontSize: '12px' }}>
              ✅ 确认送达后订单将进入完成状态
            </p>
          )}
        </div>
      ),
      okText: `确认${actionText[status] || '更新'}`,
      cancelText: '取消',
      okButtonProps: {
        href: status === OrderStatus.CANCELLED ? 'danger' : 'primary' // todo...
      },
      onOk: async () => {
        try {
          const response = await orderService.updateOrderStatus(orderId, status, remark);
          if (response.success) {
            message.success({
              content: `订单"${order.orderNo}"状态已更新为"${statusText[status]}"`,
              duration: 3,
            });
            fetchOrders();
          } else {
            message.error({
              content: response.message || '状态更新失败，请稍后重试',
              duration: 5,
            });
          }
        } catch (error: any) {
          console.error('更新订单状态失败:', error);
          let errorMsg = '状态更新失败';
          if (error.response?.status === 400) {
            errorMsg = '订单状态不允许此操作，请检查订单当前状态';
          } else if (error.response?.status === 409) {
            errorMsg = '订单状态冲突，请刷新后重试';
          } else {
            errorMsg = error.message || '网络错误，请检查连接后重试';
          }
          message.error({
            content: errorMsg,
            duration: 5,
          });
        }
      },
    });
  };

  // 表格列定义
  const columns: ColumnsType<Order> = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      width: 150,
      render: (text: string) => (
        <div style={{ fontWeight: 500, color: '#1890ff' }}>{text}</div>
      ),
    },
    {
      title: '商品信息',
      dataIndex: 'productName',
      key: 'productName',
      width: 150,
      render: (text: string, record: Order) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            数量: {record.quantity} | 单价: ¥{record.unitPrice}
          </div>
        </div>
      ),
    },
    {
      title: '买家',
      dataIndex: 'buyerName',
      key: 'buyerName',
      width: 100,
    },
    {
      title: '卖家',
      dataIndex: 'sellerName',
      key: 'sellerName',
      width: 100,
    },
    {
      title: '订单金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => (
        <div style={{ fontWeight: 500, color: '#f50' }}>
          ¥{amount.toFixed(2)}
        </div>
      ),
    },
    {
      title: '支付方式',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      width: 100,
      render: (method: PaymentMethod) => {
        const methodInfo = paymentMethodMap[method];
        return (
          <Tag color={methodInfo?.color || 'default'}>
            {methodInfo?.label || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: OrderStatus, record: Order) => {
        const statusInfo = orderStatusMap[status];
        return (
          <Badge
            status={
              status === OrderStatus.COMPLETED ? 'success' :
              status === OrderStatus.CANCELLED || status === OrderStatus.REFUNDED ? 'error' :
              status === OrderStatus.PENDING_PAYMENT ? 'warning' : 'processing'
            }
            text={
              <Tag color={statusInfo?.color || 'default'}>
                {statusInfo?.label || '未知'}
              </Tag>
            }
          />
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record: Order) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          {record.status === OrderStatus.PENDING_PAYMENT && (
            <Button
              type="link"
              size="small"
              onClick={() => handleUpdateStatus(record.id, OrderStatus.CANCELLED, '超时未付款')}
            >
              取消
            </Button>
          )}
          {record.status === OrderStatus.PAID && (
            <Button
              type="link"
              size="small"
              onClick={() => handleUpdateStatus(record.id, OrderStatus.SHIPPED)}
            >
              发货
            </Button>
          )}
          {record.status === OrderStatus.SHIPPED && (
            <Button
              type="link"
              size="small"
              onClick={() => handleUpdateStatus(record.id, OrderStatus.DELIVERED)}
            >
              确认送达
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="order-list-container">
      <Title level={2}>订单管理</Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={statistics.totalOrders}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日订单"
              value={statistics.todayOrders}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="待处理订单"
              value={statistics.pendingOrders}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总交易额"
              value={statistics.totalAmount}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#722ed1' }}
              suffix="元"
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="orderNo" label="订单号">
                <Input placeholder="请输入订单号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="buyerName" label="买家">
                <Input placeholder="请输入买家姓名" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="sellerName" label="卖家">
                <Input placeholder="请输入卖家姓名" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="订单状态">
                <Select placeholder="请选择订单状态" allowClear>
                  <Option value={OrderStatus.PENDING_PAYMENT}>待付款</Option>
                  <Option value={OrderStatus.PAID}>已付款</Option>
                  <Option value={OrderStatus.SHIPPED}>已发货</Option>
                  <Option value={OrderStatus.DELIVERED}>已送达</Option>
                  <Option value={OrderStatus.COMPLETED}>已完成</Option>
                  <Option value={OrderStatus.CANCELLED}>已取消</Option>
                  <Option value={OrderStatus.REFUNDED}>已退款</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="paymentMethod" label="支付方式">
                <Select placeholder="请选择支付方式" allowClear>
                  <Option value={PaymentMethod.ALIPAY}>支付宝</Option>
                  <Option value={PaymentMethod.WECHAT}>微信支付</Option>
                  <Option value={PaymentMethod.BANK}>银行转账</Option>
                  <Option value={PaymentMethod.CASH}>现金</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="dateRange" label="创建时间">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExport}
            >
              导出订单
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchOrders}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 订单列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              // 只有当页码或页面大小真正改变时才更新状态
              if (page !== queryParams.page || pageSize !== queryParams.pageSize) {
                setQueryParams(prev => ({
                  ...prev,
                  page,
                  pageSize: pageSize || 10,
                }));
              }
            },
          }}
        />
      </Card>

      {/* 订单详情模态框 */}
      <Modal
        title="订单详情"
        open={isDetailVisible}
        onCancel={() => setIsDetailVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        {viewingOrder && (
          <div>
            <Descriptions title="基本信息" column={2} size="small">
              <Descriptions.Item label="订单号">{viewingOrder.orderNo}</Descriptions.Item>
              <Descriptions.Item label="订单状态">
                <Tag color={orderStatusMap[viewingOrder.status]?.color}>
                  {orderStatusMap[viewingOrder.status]?.label}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="商品名称">{viewingOrder.productName}</Descriptions.Item>
              <Descriptions.Item label="商品数量">{viewingOrder.quantity}</Descriptions.Item>
              <Descriptions.Item label="单价">¥{viewingOrder.unitPrice}</Descriptions.Item>
              <Descriptions.Item label="总金额">
                <span style={{ fontWeight: 500, color: '#f50' }}>
                  ¥{viewingOrder.totalAmount.toFixed(2)}
                </span>
              </Descriptions.Item>
            </Descriptions>

            <Descriptions title="交易信息" column={2} size="small" style={{ marginTop: 24 }}>
              <Descriptions.Item label="买家">{viewingOrder.buyerName}</Descriptions.Item>
              <Descriptions.Item label="卖家">{viewingOrder.sellerName}</Descriptions.Item>
              <Descriptions.Item label="支付方式">
                <Tag color={paymentMethodMap[viewingOrder.paymentMethod]?.color}>
                  {paymentMethodMap[viewingOrder.paymentMethod]?.label}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(viewingOrder.createdAt).toLocaleString()}
              </Descriptions.Item>
              {viewingOrder.paidAt && (
                <Descriptions.Item label="付款时间">
                  {new Date(viewingOrder.paidAt).toLocaleString()}
                </Descriptions.Item>
              )}
              {viewingOrder.shippedAt && (
                <Descriptions.Item label="发货时间">
                  {new Date(viewingOrder.shippedAt).toLocaleString()}
                </Descriptions.Item>
              )}
              {viewingOrder.deliveredAt && (
                <Descriptions.Item label="送达时间">
                  {new Date(viewingOrder.deliveredAt).toLocaleString()}
                </Descriptions.Item>
              )}
            </Descriptions>

            <Descriptions title="收货信息" column={1} size="small" style={{ marginTop: 24 }}>
              <Descriptions.Item label="收货地址">{viewingOrder.shippingAddress}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{viewingOrder.shippingPhone}</Descriptions.Item>
              {viewingOrder.remark && (
                <Descriptions.Item label="备注">{viewingOrder.remark}</Descriptions.Item>
              )}
            </Descriptions>

            {/* 订单状态操作 */}
            <div style={{ marginTop: 24, textAlign: 'center' }}>
              <Space>
                {viewingOrder.status === OrderStatus.PENDING_PAYMENT && (
                  <Button
                    type="primary"
                    danger
                    onClick={() => {
                      handleUpdateStatus(viewingOrder.id, OrderStatus.CANCELLED, '超时未付款');
                      setIsDetailVisible(false);
                    }}
                  >
                    取消订单
                  </Button>
                )}
                {viewingOrder.status === OrderStatus.PAID && (
                  <Button
                    type="primary"
                    onClick={() => {
                      handleUpdateStatus(viewingOrder.id, OrderStatus.SHIPPED);
                      setIsDetailVisible(false);
                    }}
                  >
                    确认发货
                  </Button>
                )}
                {viewingOrder.status === OrderStatus.SHIPPED && (
                  <Button
                    type="primary"
                    onClick={() => {
                      handleUpdateStatus(viewingOrder.id, OrderStatus.DELIVERED);
                      setIsDetailVisible(false);
                    }}
                  >
                    确认送达
                  </Button>
                )}
                {viewingOrder.status === OrderStatus.DELIVERED && (
                  <Button
                    type="primary"
                    onClick={() => {
                      handleUpdateStatus(viewingOrder.id, OrderStatus.COMPLETED);
                      setIsDetailVisible(false);
                    }}
                  >
                    完成订单
                  </Button>
                )}
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrderList;