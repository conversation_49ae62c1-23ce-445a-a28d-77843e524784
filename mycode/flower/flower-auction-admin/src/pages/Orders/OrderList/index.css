.order-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.order-list-container .ant-typography {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 16px;
}

.search-card .ant-card-body {
  padding: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-card .ant-card-body {
  padding: 12px 16px;
}

/* 统计卡片样式 */
.order-list-container .ant-statistic {
  text-align: center;
}

.order-list-container .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.order-list-container .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 表格样式 */
.order-list-container .ant-table-wrapper {
  background: white;
  border-radius: 6px;
}

.order-list-container .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.order-list-container .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 状态标签样式 */
.order-list-container .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

.order-list-container .ant-badge {
  display: flex;
  align-items: center;
}

/* 操作按钮样式 */
.order-list-container .ant-btn-link {
  padding: 0;
  height: auto;
}

/* 模态框样式 */
.order-list-container .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.order-list-container .ant-modal-body {
  padding: 24px;
}

/* 描述列表样式 */
.order-list-container .ant-descriptions-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.order-list-container .ant-descriptions-item-label {
  font-weight: 500;
  color: #666;
  width: 120px;
}

.order-list-container .ant-descriptions-item-content {
  color: #333;
}

/* 表单样式 */
.order-list-container .ant-form-item-label > label {
  font-weight: 500;
}

.order-list-container .ant-input,
.order-list-container .ant-select-selector {
  border-radius: 4px;
}

.order-list-container .ant-input:focus,
.order-list-container .ant-input-focused,
.order-list-container .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 分页样式 */
.order-list-container .ant-pagination {
  margin-top: 16px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-list-container {
    padding: 16px;
  }
  
  .search-card .ant-form-item {
    margin-bottom: 16px;
  }
  
  .action-card .ant-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .order-list-container .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .order-list-container .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
  
  .order-list-container .ant-descriptions {
    overflow-x: auto;
  }
}

@media (max-width: 576px) {
  .order-list-container {
    padding: 12px;
  }
  
  .order-list-container .ant-typography {
    margin-bottom: 16px;
    font-size: 20px;
  }
  
  .search-card,
  .action-card {
    margin-bottom: 12px;
  }
  
  .order-list-container .ant-modal-body {
    padding: 16px;
  }
  
  .order-list-container .ant-statistic-content {
    font-size: 20px;
  }
  
  .order-list-container .ant-descriptions-item-label {
    width: 80px;
  }
}
