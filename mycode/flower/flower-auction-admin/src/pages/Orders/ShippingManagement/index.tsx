import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  DatePicker,
  Statistic,
  Descriptions,
  Badge,
  Timeline,
  InputNumber,
  Divider,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ReloadOutlined,
  TruckOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons';
// import type { ColumnsType } from 'antd/es/table';
import {
  shippingService,
  Shipping,
  ShippingStatus,
  ShippingQueryParams,
  ShippingCompany,
  ShippingTrack,
  ShippingInfo,
  TrackingRecord,
  ShippingStatistics,
} from '../../../services/shippingService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const ShippingManagement: React.FC = () => {
  const [shippings, setShippings] = useState<Shipping[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<ShippingQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [isEditVisible, setIsEditVisible] = useState(false);
  const [isShipVisible, setIsShipVisible] = useState(false);
  const [isTrackVisible, setIsTrackVisible] = useState(false);
  const [viewingShipping, setViewingShipping] = useState<Shipping | null>(null);
  const [editingShipping, setEditingShipping] = useState<Shipping | null>(null);
  const [shippingOrder, setShippingOrder] = useState<number | null>(null);
  const [trackingHistory, setTrackingHistory] = useState<ShippingTrack[]>([]);
  const [companies, setCompanies] = useState<ShippingCompany[]>([]);
  const [statistics, setStatistics] = useState<ShippingStatistics>({
    totalShippings: 0,
    pendingShippings: 0,
    inTransitShippings: 0,
    deliveredShippings: 0,
    exceptionShippings: 0,
    statusDistribution: {
      [ShippingStatus.PENDING]: 0,
      [ShippingStatus.SHIPPED]: 0,
      [ShippingStatus.IN_TRANSIT]: 0,
      [ShippingStatus.DELIVERED]: 0,
      [ShippingStatus.EXCEPTION]: 0,
      [ShippingStatus.RETURNED]: 0,
    },
    companyDistribution: {},
    avgDeliveryDays: 0,
    onTimeRate: 0,
  });

  const [form] = Form.useForm();
  const [shipForm] = Form.useForm();
  const [trackForm] = Form.useForm();

  // 防抖定时器引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 物流状态映射
  const shippingStatusMap = {
    [ShippingStatus.PENDING]: { label: '待发货', color: 'orange', icon: <ClockCircleOutlined /> },
    [ShippingStatus.SHIPPED]: { label: '已发货', color: 'blue', icon: <TruckOutlined /> },
    [ShippingStatus.IN_TRANSIT]: { label: '运输中', color: 'processing', icon: <SyncOutlined spin /> },
    [ShippingStatus.DELIVERED]: { label: '已送达', color: 'green', icon: <CheckCircleOutlined /> },
    [ShippingStatus.EXCEPTION]: { label: '异常', color: 'red', icon: <ExclamationCircleOutlined /> },
    [ShippingStatus.RETURNED]: { label: '已退回', color: 'default', icon: <EnvironmentOutlined /> },
  };

  // 获取物流列表（带防抖）
  const fetchShippings = useCallback(async () => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 设置新的定时器
    debounceTimerRef.current = setTimeout(async () => {
      setLoading(true);
      try {
        console.log('获取物流列表，参数:', queryParams);
        const response = await shippingService.getShippingList(queryParams);
        if (response.success) {
          setShippings(response.data.list);
          setTotal(response.data.total);
        } else {
          message.error(response.message || '获取物流列表失败');
        }
      } catch (error: any) {
        console.error('获取物流列表失败:', error);
        message.error(error.message || '获取物流列表失败');
      } finally {
        setLoading(false);
      }
    }, 300); // 300ms防抖延迟
  }, [queryParams]);

  // 获取物流公司列表
  const fetchShippingCompanies = async () => {
    try {
      const response = await shippingService.getShippingCompanies(1); // 只获取启用的公司
      if (response.success) {
        setCompanies(response.data);
      }
    } catch (error: any) {
      console.error('获取物流公司列表失败:', error);
    }
  };

  // 获取物流统计
  const fetchStatistics = async () => {
    try {
      const response = await shippingService.getShippingStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error: any) {
      console.error('获取物流统计失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchShippings();
  }, [queryParams]);

  // 统计数据和物流公司只在组件初始化时加载一次
  useEffect(() => {
    fetchStatistics();
    fetchShippingCompanies();
  }, []);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      page: 1,
      ...values,
      dateRange: values.dateRange ? [
        values.dateRange[0].format('YYYY-MM-DD'),
        values.dateRange[1].format('YYYY-MM-DD'),
      ] : undefined,
    });
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 查看详情
  const handleView = async (record: Shipping) => {
    setViewingShipping(record);
    setIsDetailVisible(true);

    // 获取跟踪历史
    try {
      const response = await shippingService.getTrackingHistory(record.id);
      if (response.success) {
        setTrackingHistory(response.data);
      }
    } catch (error: any) {
      console.error('获取跟踪历史失败:', error);
    }
  };

  // 编辑物流信息
  const handleEdit = (record: Shipping) => {
    setEditingShipping(record);
    form.setFieldsValue({
      ...record,
      shippedAt: record.shippedAt ? new Date(record.shippedAt) : null,
      deliveredAt: record.deliveredAt ? new Date(record.deliveredAt) : null,
    });
    setIsEditVisible(true);
  };

  // 发货
  const handleShip = (orderId: number) => {
    setShippingOrder(orderId);
    shipForm.resetFields();
    setIsShipVisible(true);
  };

  // 添加跟踪记录
  const handleAddTrack = (record: Shipping) => {
    setViewingShipping(record);
    trackForm.resetFields();
    setIsTrackVisible(true);
  };

  // 更新物流状态
  const handleUpdateStatus = async (id: number, status: ShippingStatus, remark?: string) => {
    try {
      const response = await shippingService.updateShippingStatus(id, status, remark);
      if (response.success) {
        message.success('更新物流状态成功');
        fetchShippings();
      } else {
        message.error(response.message || '更新物流状态失败');
      }
    } catch (error: any) {
      message.error(error.message || '更新物流状态失败');
    }
  };

  // 删除物流信息
  const handleDelete = (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条物流信息吗？',
      onOk: async () => {
        try {
          const response = await shippingService.deleteShipping(id);
          if (response.success) {
            message.success('删除成功');
            fetchShippings();
          } else {
            message.error(response.message || '删除失败');
          }
        } catch (error: any) {
          message.error(error.message || '删除失败');
        }
      },
    });
  };

  // 保存编辑
  const handleSaveEdit = async (values: any) => {
    if (!editingShipping) return;

    try {
      const response = await shippingService.updateShipping(editingShipping.id, values);
      if (response.success) {
        message.success('更新物流信息成功');
        setIsEditVisible(false);
        setEditingShipping(null);
        fetchShippings();
      } else {
        message.error(response.message || '更新物流信息失败');
      }
    } catch (error: any) {
      message.error(error.message || '更新物流信息失败');
    }
  };

  // 确认发货
  const handleConfirmShip = async (values: any) => {
    if (!shippingOrder) return;

    try {
      const shippingInfo: ShippingInfo = {
        trackingNumber: values.trackingNumber,
        shippingCompanyId: values.shippingCompanyId,
        receiverName: values.receiverName,
        receiverPhone: values.receiverPhone,
        receiverAddress: values.receiverAddress,
        weight: values.weight || 0,
        volume: values.volume || 0,
        shippingFee: values.shippingFee || 0,
        insuranceFee: values.insuranceFee || 0,
        estimatedDays: values.estimatedDays || 3,
        remark: values.remark,
      };

      const response = await shippingService.shipOrder(shippingOrder, shippingInfo);
      if (response.success) {
        message.success('发货成功');
        setIsShipVisible(false);
        setShippingOrder(null);
        fetchShippings();
      } else {
        message.error(response.message || '发货失败');
      }
    } catch (error: any) {
      message.error(error.message || '发货失败');
    }
  };

  // 添加跟踪记录
  const handleConfirmAddTrack = async (values: any) => {
    if (!viewingShipping) return;

    try {
      const trackRecord: TrackingRecord = {
        status: values.status,
        location: values.location,
        description: values.description,
        operator: values.operator || '系统',
        trackTime: values.trackTime ? values.trackTime.toISOString() : new Date().toISOString(),
      };

      const response = await shippingService.addTrackingRecord(viewingShipping.id, trackRecord);
      if (response.success) {
        message.success('添加跟踪记录成功');
        setIsTrackVisible(false);

        // 刷新跟踪历史
        const historyResponse = await shippingService.getTrackingHistory(viewingShipping.id);
        if (historyResponse.success) {
          setTrackingHistory(historyResponse.data);
        }
      } else {
        message.error(response.message || '添加跟踪记录失败');
      }
    } catch (error: any) {
      message.error(error.message || '添加跟踪记录失败');
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>物流管理</Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总物流单数"
              value={statistics.totalShippings}
              prefix={<TruckOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待发货"
              value={statistics.pendingShippings}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运输中"
              value={statistics.inTransitShippings}
              prefix={<SyncOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已送达"
              value={statistics.deliveredShippings}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="trackingNumber" label="快递单号">
            <Input placeholder="请输入快递单号" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="receiverName" label="收件人">
            <Input placeholder="请输入收件人姓名" style={{ width: 150 }} />
          </Form.Item>
          <Form.Item name="receiverPhone" label="收件人电话">
            <Input placeholder="请输入收件人电话" style={{ width: 150 }} />
          </Form.Item>
          <Form.Item name="shippingCompanyId" label="物流公司">
            <Select placeholder="请选择物流公司" style={{ width: 150 }} allowClear>
              {companies.map(company => (
                <Option key={company.id} value={company.id}>
                  {company.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="status" label="物流状态">
            <Select placeholder="请选择状态" style={{ width: 120 }} allowClear>
              {Object.entries(shippingStatusMap).map(([key, value]) => (
                <Option key={key} value={parseInt(key)}>
                  {value.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="dateRange" label="创建时间">
            <RangePicker />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchShippings}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleShip(1)} // 这里应该从订单选择
            >
              新增发货
            </Button>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchShippings}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 物流列表表格 */}
      <Card>
        <Table
          columns={[
            {
              title: '快递单号',
              dataIndex: 'trackingNumber',
              key: 'trackingNumber',
              width: 150,
              render: (text: string) => (
                <div style={{ fontWeight: 500, color: '#1890ff' }}>{text}</div>
              ),
            },
            {
              title: '订单ID',
              dataIndex: 'orderId',
              key: 'orderId',
              width: 100,
            },
            {
              title: '物流公司',
              dataIndex: 'shippingCompany',
              key: 'shippingCompany',
              width: 120,
              render: (company: ShippingCompany) => company?.name || '-',
            },
            {
              title: '收件人信息',
              key: 'receiver',
              width: 200,
              render: (_, record: Shipping) => (
                <div>
                  <div style={{ fontWeight: 500 }}>{record.receiverName}</div>
                  <div style={{ fontSize: 12, color: '#999' }}>
                    {record.receiverPhone}
                  </div>
                </div>
              ),
            },
            {
              title: '物流状态',
              dataIndex: 'status',
              key: 'status',
              width: 120,
              render: (status: ShippingStatus) => {
                const statusInfo = shippingStatusMap[status];
                return (
                  <Tag color={statusInfo.color} icon={statusInfo.icon}>
                    {statusInfo.label}
                  </Tag>
                );
              },
            },
            {
              title: '发货时间',
              dataIndex: 'shippedAt',
              key: 'shippedAt',
              width: 150,
              render: (time: string) => time ? new Date(time).toLocaleString() : '-',
            },
            {
              title: '预计天数',
              dataIndex: 'estimatedDays',
              key: 'estimatedDays',
              width: 100,
              render: (days: number) => `${days}天`,
            },
            {
              title: '运费',
              dataIndex: 'shippingFee',
              key: 'shippingFee',
              width: 100,
              render: (fee: number) => `¥${fee.toFixed(2)}`,
            },
            {
              title: '操作',
              key: 'action',
              width: 250,
              fixed: 'right',
              render: (_, record: Shipping) => (
                <Space size="small">
                  <Button
                    type="link"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => handleView(record)}
                  >
                    查看
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEdit(record)}
                  >
                    编辑
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => handleAddTrack(record)}
                  >
                    跟踪
                  </Button>
                  {record.status === ShippingStatus.PENDING && (
                    <Button
                      type="link"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDelete(record.id)}
                    >
                      删除
                    </Button>
                  )}
                </Space>
              ),
            },
          ]}
          dataSource={shippings}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              // 只有当页码或页面大小真正改变时才更新状态
              if (page !== queryParams.page || pageSize !== queryParams.pageSize) {
                setQueryParams(prev => ({
                  ...prev,
                  page,
                  pageSize: pageSize || 10,
                }));
              }
            },
          }}
        />
      </Card>

      {/* 物流详情模态框 */}
      <Modal
        title="物流详情"
        open={isDetailVisible}
        onCancel={() => setIsDetailVisible(false)}
        footer={null}
        width={800}
      >
        {viewingShipping && (
          <div>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="快递单号">
                {viewingShipping.trackingNumber}
              </Descriptions.Item>
              <Descriptions.Item label="订单ID">
                {viewingShipping.orderId}
              </Descriptions.Item>
              <Descriptions.Item label="物流公司">
                {viewingShipping.shippingCompany?.name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="物流状态">
                <Badge
                  status={shippingStatusMap[viewingShipping.status].color as any}
                  text={shippingStatusMap[viewingShipping.status].label}
                />
              </Descriptions.Item>
              <Descriptions.Item label="发件人">
                {viewingShipping.senderName} - {viewingShipping.senderPhone}
              </Descriptions.Item>
              <Descriptions.Item label="收件人">
                {viewingShipping.receiverName} - {viewingShipping.receiverPhone}
              </Descriptions.Item>
              <Descriptions.Item label="发件地址" span={2}>
                {viewingShipping.senderAddress}
              </Descriptions.Item>
              <Descriptions.Item label="收件地址" span={2}>
                {viewingShipping.receiverAddress}
              </Descriptions.Item>
              <Descriptions.Item label="重量">
                {viewingShipping.weight}kg
              </Descriptions.Item>
              <Descriptions.Item label="体积">
                {viewingShipping.volume}m³
              </Descriptions.Item>
              <Descriptions.Item label="运费">
                ¥{viewingShipping.shippingFee.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="保险费">
                ¥{viewingShipping.insuranceFee.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="发货时间">
                {viewingShipping.shippedAt ? new Date(viewingShipping.shippedAt).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="送达时间">
                {viewingShipping.deliveredAt ? new Date(viewingShipping.deliveredAt).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>
                {viewingShipping.remark || '-'}
              </Descriptions.Item>
            </Descriptions>

            <Divider>物流跟踪</Divider>
            <Timeline>
              {trackingHistory.map((track, index) => (
                <Timeline.Item
                  key={track.id}
                  color={index === 0 ? 'green' : 'blue'}
                  dot={index === 0 ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
                >
                  <div style={{ marginBottom: 8 }}>
                    <div style={{ fontWeight: 500, fontSize: 14 }}>
                      {track.status}
                      {track.location && (
                        <Tag color="blue" style={{ marginLeft: 8 }}>
                          <EnvironmentOutlined /> {track.location}
                        </Tag>
                      )}
                    </div>
                    <div style={{ color: '#666', fontSize: 12, marginTop: 4 }}>
                      {track.description}
                    </div>
                    <div style={{ color: '#999', fontSize: 12, marginTop: 4 }}>
                      {new Date(track.trackTime).toLocaleString()} - {track.operator}
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </div>
        )}
      </Modal>

      {/* 编辑物流信息模态框 */}
      <Modal
        title="编辑物流信息"
        open={isEditVisible}
        onCancel={() => setIsEditVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveEdit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="trackingNumber"
                label="快递单号"
                rules={[{ required: true, message: '请输入快递单号' }]}
              >
                <Input placeholder="请输入快递单号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="shippingCompanyId"
                label="物流公司"
                rules={[{ required: true, message: '请选择物流公司' }]}
              >
                <Select placeholder="请选择物流公司">
                  {companies.map(company => (
                    <Option key={company.id} value={company.id}>
                      {company.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="receiverName"
                label="收件人姓名"
                rules={[{ required: true, message: '请输入收件人姓名' }]}
              >
                <Input placeholder="请输入收件人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="receiverPhone"
                label="收件人电话"
                rules={[{ required: true, message: '请输入收件人电话' }]}
              >
                <Input placeholder="请输入收件人电话" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="receiverAddress"
            label="收件人地址"
            rules={[{ required: true, message: '请输入收件人地址' }]}
          >
            <TextArea rows={2} placeholder="请输入收件人地址" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="weight" label="重量(kg)">
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="shippingFee" label="运费(元)">
                <InputNumber min={0} step={0.01} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="estimatedDays" label="预计天数">
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="remark" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 发货模态框 */}
      <Modal
        title="新增发货"
        open={isShipVisible}
        onCancel={() => setIsShipVisible(false)}
        onOk={() => shipForm.submit()}
        width={600}
      >
        <Form
          form={shipForm}
          layout="vertical"
          onFinish={handleConfirmShip}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="trackingNumber"
                label="快递单号"
                rules={[{ required: true, message: '请输入快递单号' }]}
              >
                <Input placeholder="请输入快递单号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="shippingCompanyId"
                label="物流公司"
                rules={[{ required: true, message: '请选择物流公司' }]}
              >
                <Select placeholder="请选择物流公司">
                  {companies.map(company => (
                    <Option key={company.id} value={company.id}>
                      {company.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="receiverName"
                label="收件人姓名"
                rules={[{ required: true, message: '请输入收件人姓名' }]}
              >
                <Input placeholder="请输入收件人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="receiverPhone"
                label="收件人电话"
                rules={[{ required: true, message: '请输入收件人电话' }]}
              >
                <Input placeholder="请输入收件人电话" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="receiverAddress"
            label="收件人地址"
            rules={[{ required: true, message: '请输入收件人地址' }]}
          >
            <TextArea rows={2} placeholder="请输入收件人地址" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="weight" label="重量(kg)" initialValue={1}>
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="shippingFee" label="运费(元)" initialValue={10}>
                <InputNumber min={0} step={0.01} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="estimatedDays" label="预计天数" initialValue={3}>
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="remark" label="备注">
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加跟踪记录模态框 */}
      <Modal
        title="添加跟踪记录"
        open={isTrackVisible}
        onCancel={() => setIsTrackVisible(false)}
        onOk={() => trackForm.submit()}
        width={500}
      >
        <Form
          form={trackForm}
          layout="vertical"
          onFinish={handleConfirmAddTrack}
        >
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请输入状态' }]}
          >
            <Input placeholder="请输入状态，如：运输中、已到达中转站等" />
          </Form.Item>
          <Form.Item name="location" label="位置">
            <Input placeholder="请输入当前位置" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={3} placeholder="请输入详细描述" />
          </Form.Item>
          <Form.Item name="operator" label="操作员" initialValue="系统">
            <Input placeholder="请输入操作员" />
          </Form.Item>
          <Form.Item name="trackTime" label="跟踪时间">
            <DatePicker
              showTime
              style={{ width: '100%' }}
              placeholder="请选择跟踪时间"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ShippingManagement;
