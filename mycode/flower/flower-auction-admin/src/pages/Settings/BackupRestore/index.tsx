import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Progress,
  Alert,
  Row,
  Col,
  Statistic,
  Tag,
  Upload,
  Divider,
} from 'antd';
import {
  CloudDownloadOutlined,
  CloudUploadOutlined,
  ReloadOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  DatabaseOutlined,
  HistoryOutlined,
  SafetyOutlined,
  UploadOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;
const { Dragger } = Upload;

// 备份类型枚举
enum BackupType {
  FULL = 'full',
  INCREMENTAL = 'incremental',
  DIFFERENTIAL = 'differential',
}

// 备份状态枚举
enum BackupStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// 备份记录接口
interface BackupRecord {
  id: number;
  name: string;
  type: BackupType;
  status: BackupStatus;
  size: number; // 文件大小（字节）
  createdAt: string;
  completedAt?: string;
  duration?: number; // 耗时（秒）
  description?: string;
  filePath: string;
  checksum?: string;
}

// 备份配置接口
interface BackupConfig {
  autoBackup: boolean;
  backupInterval: number; // 自动备份间隔（小时）
  maxBackups: number; // 最大保留备份数
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  backupPath: string;
}

const BackupRestore: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [backupRecords, setBackupRecords] = useState<BackupRecord[]>([]);
  const [config, setConfig] = useState<BackupConfig>({
    autoBackup: true,
    backupInterval: 24,
    maxBackups: 7,
    compressionEnabled: true,
    encryptionEnabled: false,
    backupPath: '/data/backups',
  });

  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [restoreModalVisible, setRestoreModalVisible] = useState(false);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [currentBackup, setCurrentBackup] = useState<BackupRecord | null>(null);
  const [backupProgress, setBackupProgress] = useState(0);
  const [restoreProgress, setRestoreProgress] = useState(0);

  const [createForm] = Form.useForm();
  const [configForm] = Form.useForm();

  // 获取备份记录
  const fetchBackupRecords = async () => {
    setLoading(true);
    try {
      // 模拟备份记录数据
      const mockRecords: BackupRecord[] = [
        {
          id: 1,
          name: 'full_backup_20231215',
          type: BackupType.FULL,
          status: BackupStatus.COMPLETED,
          size: 1024 * 1024 * 500, // 500MB
          createdAt: '2023-12-15 02:00:00',
          completedAt: '2023-12-15 02:15:30',
          duration: 930,
          description: '每日自动全量备份',
          filePath: '/data/backups/full_backup_20231215.sql.gz',
          checksum: 'sha256:abc123...',
        },
        {
          id: 2,
          name: 'manual_backup_20231214',
          type: BackupType.FULL,
          status: BackupStatus.COMPLETED,
          size: 1024 * 1024 * 480,
          createdAt: '2023-12-14 15:30:00',
          completedAt: '2023-12-14 15:42:15',
          duration: 735,
          description: '手动创建的备份',
          filePath: '/data/backups/manual_backup_20231214.sql.gz',
          checksum: 'sha256:def456...',
        },
        {
          id: 3,
          name: 'incremental_backup_20231214',
          type: BackupType.INCREMENTAL,
          status: BackupStatus.FAILED,
          size: 0,
          createdAt: '2023-12-14 12:00:00',
          description: '增量备份失败',
          filePath: '',
        },
      ];
      setBackupRecords(mockRecords);
    } catch (error: any) {
      console.error('获取备份记录失败:', error);
      message.error('获取备份记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBackupRecords();
    configForm.setFieldsValue(config);
  }, []);

  // 创建备份
  const handleCreateBackup = async (values: any) => {
    try {
      setBackupProgress(0);
      
      // 模拟备份进度
      const interval = setInterval(() => {
        setBackupProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            message.success('备份创建成功');
            setCreateModalVisible(false);
            createForm.resetFields();
            fetchBackupRecords();
            return 100;
          }
          return prev + 10;
        });
      }, 500);

    } catch (error: any) {
      console.error('创建备份失败:', error);
      message.error('创建备份失败');
    }
  };

  // 恢复备份
  const handleRestore = (record: BackupRecord) => {
    setCurrentBackup(record);
    setRestoreModalVisible(true);
  };

  // 执行恢复
  const handleExecuteRestore = async () => {
    try {
      setRestoreProgress(0);
      
      // 模拟恢复进度
      const interval = setInterval(() => {
        setRestoreProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            message.success('数据恢复成功');
            setRestoreModalVisible(false);
            setCurrentBackup(null);
            return 100;
          }
          return prev + 8;
        });
      }, 600);

    } catch (error: any) {
      console.error('恢复备份失败:', error);
      message.error('恢复备份失败');
    }
  };

  // 删除备份
  const handleDelete = (record: BackupRecord) => {
    confirm({
      title: '确认删除',
      content: `确定要删除备份 "${record.name}" 吗？此操作不可恢复。`,
      icon: <ExclamationCircleOutlined />,
      onOk() {
        message.success('备份删除成功');
        fetchBackupRecords();
      },
    });
  };

  // 下载备份
  const handleDownload = (record: BackupRecord) => {
    message.success('备份下载功能开发中...');
  };

  // 保存配置
  const handleSaveConfig = async (values: any) => {
    try {
      setConfig(values);
      message.success('备份配置保存成功');
      setConfigModalVisible(false);
    } catch (error: any) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取状态标签
  const getStatusTag = (status: BackupStatus) => {
    const statusMap = {
      [BackupStatus.PENDING]: { color: 'default', text: '等待中' },
      [BackupStatus.RUNNING]: { color: 'processing', text: '进行中' },
      [BackupStatus.COMPLETED]: { color: 'success', text: '已完成' },
      [BackupStatus.FAILED]: { color: 'error', text: '失败' },
    };
    const config = statusMap[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取类型标签
  const getTypeTag = (type: BackupType) => {
    const typeMap = {
      [BackupType.FULL]: { color: 'blue', text: '全量备份' },
      [BackupType.INCREMENTAL]: { color: 'green', text: '增量备份' },
      [BackupType.DIFFERENTIAL]: { color: 'orange', text: '差异备份' },
    };
    const config = typeMap[type];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '备份名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: BackupType) => getTypeTag(type),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: BackupStatus) => getStatusTag(status),
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      render: (size: number) => formatFileSize(size),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration?: number) => duration ? `${duration}秒` : '-',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: BackupRecord) => (
        <Space>
          {record.status === BackupStatus.COMPLETED && (
            <>
              <Button
                type="link"
                size="small"
                icon={<HistoryOutlined />}
                onClick={() => handleRestore(record)}
              >
                恢复
              </Button>
              <Button
                type="link"
                size="small"
                icon={<DownloadOutlined />}
                onClick={() => handleDownload(record)}
              >
                下载
              </Button>
            </>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 计算统计数据
  const completedBackups = backupRecords.filter(r => r.status === BackupStatus.COMPLETED);
  const totalSize = completedBackups.reduce((sum, record) => sum + record.size, 0);
  const latestBackup = completedBackups.sort((a, b) => 
    dayjs(b.createdAt).valueOf() - dayjs(a.createdAt).valueOf()
  )[0];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <DatabaseOutlined /> 备份恢复
      </Title>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="备份总数"
              value={backupRecords.length}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功备份"
              value={completedBackups.length}
              valueStyle={{ color: '#3f8600' }}
              prefix={<SafetyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总大小"
              value={formatFileSize(totalSize)}
              prefix={<CloudDownloadOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="最新备份"
              value={latestBackup ? dayjs(latestBackup.createdAt).format('MM-DD HH:mm') : '无'}
              prefix={<HistoryOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<CloudDownloadOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建备份
          </Button>
          <Button
            icon={<CloudUploadOutlined />}
            onClick={() => message.info('上传备份功能开发中...')}
          >
            上传备份
          </Button>
          <Button
            icon={<DatabaseOutlined />}
            onClick={() => setConfigModalVisible(true)}
          >
            备份配置
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchBackupRecords}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </Card>

      {/* 备份列表 */}
      <Card title="备份记录">
        <Table
          columns={columns}
          dataSource={backupRecords}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      {/* 创建备份模态框 */}
      <Modal
        title="创建备份"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
          setBackupProgress(0);
        }}
        footer={null}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateBackup}
        >
          <Form.Item
            name="name"
            label="备份名称"
            rules={[{ required: true, message: '请输入备份名称' }]}
          >
            <Input placeholder="请输入备份名称" />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="备份类型"
            rules={[{ required: true, message: '请选择备份类型' }]}
          >
            <Select placeholder="请选择备份类型">
              <Option value={BackupType.FULL}>全量备份</Option>
              <Option value={BackupType.INCREMENTAL}>增量备份</Option>
              <Option value={BackupType.DIFFERENTIAL}>差异备份</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="description"
            label="备份描述"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入备份描述"
            />
          </Form.Item>

          {backupProgress > 0 && (
            <div style={{ marginBottom: 16 }}>
              <Text>备份进度:</Text>
              <Progress percent={backupProgress} />
            </div>
          )}

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={backupProgress > 0 && backupProgress < 100}
                disabled={backupProgress > 0 && backupProgress < 100}
              >
                开始备份
              </Button>
              <Button
                onClick={() => {
                  setCreateModalVisible(false);
                  createForm.resetFields();
                  setBackupProgress(0);
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 恢复备份模态框 */}
      <Modal
        title="恢复备份"
        open={restoreModalVisible}
        onCancel={() => {
          setRestoreModalVisible(false);
          setCurrentBackup(null);
          setRestoreProgress(0);
        }}
        footer={null}
      >
        {currentBackup && (
          <div>
            <Alert
              message="警告"
              description="恢复备份将覆盖当前数据，请确保已做好数据备份。此操作不可恢复！"
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>备份信息:</Text>
              <div style={{ marginTop: 8 }}>
                <p>名称: {currentBackup.name}</p>
                <p>类型: {getTypeTag(currentBackup.type)}</p>
                <p>大小: {formatFileSize(currentBackup.size)}</p>
                <p>创建时间: {currentBackup.createdAt}</p>
                <p>描述: {currentBackup.description || '无'}</p>
              </div>
            </div>

            {restoreProgress > 0 && (
              <div style={{ marginBottom: 16 }}>
                <Text>恢复进度:</Text>
                <Progress percent={restoreProgress} />
              </div>
            )}

            <Space>
              <Button
                type="primary"
                danger
                onClick={handleExecuteRestore}
                loading={restoreProgress > 0 && restoreProgress < 100}
                disabled={restoreProgress > 0 && restoreProgress < 100}
              >
                确认恢复
              </Button>
              <Button
                onClick={() => {
                  setRestoreModalVisible(false);
                  setCurrentBackup(null);
                  setRestoreProgress(0);
                }}
              >
                取消
              </Button>
            </Space>
          </div>
        )}
      </Modal>

      {/* 备份配置模态框 */}
      <Modal
        title="备份配置"
        open={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        footer={null}
      >
        <Form
          form={configForm}
          layout="vertical"
          onFinish={handleSaveConfig}
          initialValues={config}
        >
          <Form.Item
            name="autoBackup"
            label="启用自动备份"
            valuePropName="checked"
          >
            <input type="checkbox" />
          </Form.Item>
          
          <Form.Item
            name="backupInterval"
            label="备份间隔（小时）"
            rules={[{ required: true, message: '请输入备份间隔' }]}
          >
            <Select>
              <Option value={6}>6小时</Option>
              <Option value={12}>12小时</Option>
              <Option value={24}>24小时</Option>
              <Option value={72}>3天</Option>
              <Option value={168}>7天</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="maxBackups"
            label="最大保留备份数"
            rules={[{ required: true, message: '请输入最大保留备份数' }]}
          >
            <Select>
              <Option value={3}>3个</Option>
              <Option value={7}>7个</Option>
              <Option value={14}>14个</Option>
              <Option value={30}>30个</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="compressionEnabled"
            label="启用压缩"
            valuePropName="checked"
          >
            <input type="checkbox" />
          </Form.Item>
          
          <Form.Item
            name="encryptionEnabled"
            label="启用加密"
            valuePropName="checked"
          >
            <input type="checkbox" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存配置
              </Button>
              <Button onClick={() => setConfigModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BackupRestore;
