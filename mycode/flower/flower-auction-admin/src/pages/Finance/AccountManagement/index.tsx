import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  Button,
  Table,
  Modal,
  Form,
  InputNumber,
  Input,
  Select,
  message,
  Space,
  Tag,
  Descriptions,
  Alert,
} from 'antd';
import {
  WalletOutlined,
  LockOutlined,
  UnlockOutlined,
  PlusOutlined,
  ReloadOutlined,
  EyeOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  financeService,
  Account,
  AccountType,
} from '../../../services/financeService';

const { Title } = Typography;
const { confirm } = Modal;

const AccountManagement: React.FC = () => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [freezeVisible, setFreezeVisible] = useState(false);
  const [unfreezeVisible, setUnfreezeVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);

  const [freezeForm] = Form.useForm();
  const [unfreezeForm] = Form.useForm();
  const [createForm] = Form.useForm();

  // 账户类型映射
  const accountTypeMap = {
    [AccountType.DEPOSIT]: { name: '保证金账户', color: 'blue', icon: <LockOutlined /> },
    [AccountType.TRADING]: { name: '交易账户', color: 'green', icon: <WalletOutlined /> },
    [AccountType.COMMISSION]: { name: '佣金账户', color: 'purple', icon: <WalletOutlined /> },
  };

  // 获取账户列表
  const fetchAccounts = async () => {
    setLoading(true);
    try {
      // 获取所有类型的账户
      const accountPromises = Object.values(AccountType)
        .filter(type => typeof type === 'number')
        .map(async (type) => {
          try {
            const response = await financeService.getAccount(type as AccountType);
            return response.success ? response.data : null;
          } catch (error) {
            // 如果账户不存在，返回null
            return null;
          }
        });

      const results = await Promise.all(accountPromises);
      const validAccounts = results.filter(account => account !== null) as Account[];
      setAccounts(validAccounts);
    } catch (error: any) {
      console.error('获取账户列表失败:', error);
      message.error('获取账户列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccounts();
  }, []);

  // 创建账户
  const handleCreateAccount = async (values: any) => {
    try {
      const response = await financeService.createAccount(values.accountType);
      if (response.success) {
        message.success('创建账户成功');
        setCreateVisible(false);
        createForm.resetFields();
        fetchAccounts();
      } else {
        message.error(response.message || '创建账户失败');
      }
    } catch (error: any) {
      message.error(error.message || '创建账户失败');
    }
  };

  // 冻结金额
  const handleFreeze = async (values: any) => {
    try {
      const response = await financeService.freezeAmount(values.amount, values.reason);
      if (response.success) {
        message.success('冻结金额成功');
        setFreezeVisible(false);
        freezeForm.resetFields();
        fetchAccounts();
      } else {
        message.error(response.message || '冻结金额失败');
      }
    } catch (error: any) {
      message.error(error.message || '冻结金额失败');
    }
  };

  // 解冻金额
  const handleUnfreeze = async (values: any) => {
    try {
      const response = await financeService.unfreezeAmount(values.amount, values.reason);
      if (response.success) {
        message.success('解冻金额成功');
        setUnfreezeVisible(false);
        unfreezeForm.resetFields();
        fetchAccounts();
      } else {
        message.error(response.message || '解冻金额失败');
      }
    } catch (error: any) {
      message.error(error.message || '解冻金额失败');
    }
  };

  // 查看账户详情
  const handleViewDetail = (account: Account) => {
    setSelectedAccount(account);
    setDetailVisible(true);
  };

  // 计算总资产
  const totalAssets = accounts.reduce((sum, account) => sum + account.balance, 0);
  const totalFrozen = accounts.reduce((sum, account) => sum + account.frozenAmount, 0);
  const totalIncome = accounts.reduce((sum, account) => sum + account.totalIncome, 0);
  const totalExpense = accounts.reduce((sum, account) => sum + account.totalExpense, 0);

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>账户管理</Title>

      {/* 总览统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总资产"
              value={totalAssets}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="冻结金额"
              value={totalFrozen}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收入"
              value={totalIncome}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总支出"
              value={totalExpense}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateVisible(true)}
              >
                创建账户
              </Button>
              <Button
                icon={<LockOutlined />}
                onClick={() => setFreezeVisible(true)}
              >
                冻结金额
              </Button>
              <Button
                icon={<UnlockOutlined />}
                onClick={() => setUnfreezeVisible(true)}
              >
                解冻金额
              </Button>
            </Space>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchAccounts}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 账户列表 */}
      <Card>
        <Table
          columns={[
            {
              title: '账户类型',
              dataIndex: 'accountType',
              key: 'accountType',
              width: 150,
              render: (type: AccountType) => {
                const typeInfo = accountTypeMap[type];
                return (
                  <Tag color={typeInfo.color} icon={typeInfo.icon}>
                    {typeInfo.name}
                  </Tag>
                );
              },
            },
            {
              title: '账户余额',
              dataIndex: 'balance',
              key: 'balance',
              width: 150,
              render: (balance: number) => (
                <span style={{ color: '#3f8600', fontWeight: 'bold' }}>
                  ¥{balance.toLocaleString()}
                </span>
              ),
            },
            {
              title: '冻结金额',
              dataIndex: 'frozenAmount',
              key: 'frozenAmount',
              width: 150,
              render: (amount: number) => (
                <span style={{ color: amount > 0 ? '#cf1322' : '#666' }}>
                  ¥{amount.toLocaleString()}
                </span>
              ),
            },
            {
              title: '可用余额',
              key: 'availableBalance',
              width: 150,
              render: (_, record: Account) => (
                <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                  ¥{(record.balance - record.frozenAmount).toLocaleString()}
                </span>
              ),
            },
            {
              title: '总收入',
              dataIndex: 'totalIncome',
              key: 'totalIncome',
              width: 150,
              render: (income: number) => `¥${income.toLocaleString()}`,
            },
            {
              title: '总支出',
              dataIndex: 'totalExpense',
              key: 'totalExpense',
              width: 150,
              render: (expense: number) => `¥${expense.toLocaleString()}`,
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              key: 'createdAt',
              width: 180,
              render: (time: string) => new Date(time).toLocaleString(),
            },
            {
              title: '操作',
              key: 'action',
              width: 100,
              fixed: 'right',
              render: (_, record: Account) => (
                <Button
                  type="link"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handleViewDetail(record)}
                >
                  详情
                </Button>
              ),
            },
          ]}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={false}
        />
      </Card>

      {/* 创建账户模态框 */}
      <Modal
        title="创建账户"
        open={createVisible}
        onCancel={() => setCreateVisible(false)}
        onOk={() => createForm.submit()}
        width={500}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateAccount}
        >
          <Form.Item
            name="accountType"
            label="账户类型"
            rules={[{ required: true, message: '请选择账户类型' }]}
          >
            <Select placeholder="请选择账户类型">
              {Object.entries(accountTypeMap).map(([key, value]) => (
                <Select.Option key={key} value={parseInt(key)}>
                  <Tag color={value.color} icon={value.icon}>
                    {value.name}
                  </Tag>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Alert
            message="提示"
            description="创建账户后，系统将自动为您开通对应的账户服务。"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        </Form>
      </Modal>

      {/* 冻结金额模态框 */}
      <Modal
        title="冻结金额"
        open={freezeVisible}
        onCancel={() => setFreezeVisible(false)}
        onOk={() => freezeForm.submit()}
        width={500}
      >
        <Form
          form={freezeForm}
          layout="vertical"
          onFinish={handleFreeze}
        >
          <Form.Item
            name="amount"
            label="冻结金额"
            rules={[
              { required: true, message: '请输入冻结金额' },
              { type: 'number', min: 0.01, message: '冻结金额必须大于0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入冻结金额"
              precision={2}
              min={0.01}
              addonBefore="¥"
            />
          </Form.Item>
          <Form.Item
            name="reason"
            label="冻结原因"
            rules={[{ required: true, message: '请输入冻结原因' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入冻结原因"
              maxLength={200}
              showCount
            />
          </Form.Item>
          <Alert
            message="警告"
            description="冻结后的金额将无法用于交易，请谨慎操作。"
            type="warning"
            showIcon
          />
        </Form>
      </Modal>

      {/* 解冻金额模态框 */}
      <Modal
        title="解冻金额"
        open={unfreezeVisible}
        onCancel={() => setUnfreezeVisible(false)}
        onOk={() => unfreezeForm.submit()}
        width={500}
      >
        <Form
          form={unfreezeForm}
          layout="vertical"
          onFinish={handleUnfreeze}
        >
          <Form.Item
            name="amount"
            label="解冻金额"
            rules={[
              { required: true, message: '请输入解冻金额' },
              { type: 'number', min: 0.01, message: '解冻金额必须大于0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入解冻金额"
              precision={2}
              min={0.01}
              addonBefore="¥"
            />
          </Form.Item>
          <Form.Item
            name="reason"
            label="解冻原因"
            rules={[{ required: true, message: '请输入解冻原因' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入解冻原因"
              maxLength={200}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 账户详情模态框 */}
      <Modal
        title="账户详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={600}
      >
        {selectedAccount && (
          <Descriptions bordered column={2}>
            <Descriptions.Item label="账户类型" span={2}>
              <Tag
                color={accountTypeMap[selectedAccount.accountType].color}
                icon={accountTypeMap[selectedAccount.accountType].icon}
              >
                {accountTypeMap[selectedAccount.accountType].name}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="账户余额">
              <span style={{ color: '#3f8600', fontWeight: 'bold' }}>
                ¥{selectedAccount.balance.toLocaleString()}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="冻结金额">
              <span style={{ color: selectedAccount.frozenAmount > 0 ? '#cf1322' : '#666' }}>
                ¥{selectedAccount.frozenAmount.toLocaleString()}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="可用余额">
              <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                ¥{(selectedAccount.balance - selectedAccount.frozenAmount).toLocaleString()}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="总收入">
              ¥{selectedAccount.totalIncome.toLocaleString()}
            </Descriptions.Item>
            <Descriptions.Item label="总支出">
              ¥{selectedAccount.totalExpense.toLocaleString()}
            </Descriptions.Item>
            <Descriptions.Item label="净收益">
              <span style={{
                color: (selectedAccount.totalIncome - selectedAccount.totalExpense) >= 0 ? '#3f8600' : '#cf1322',
                fontWeight: 'bold'
              }}>
                ¥{(selectedAccount.totalIncome - selectedAccount.totalExpense).toLocaleString()}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {new Date(selectedAccount.createdAt).toLocaleString()}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {new Date(selectedAccount.updatedAt).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default AccountManagement;
