import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Table,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Row,
  Col,
  Tag,
  message,
  Statistic,
  Tooltip,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  financeService,
  Transaction,
  TransactionType,
  TransactionStatus,
  TransactionQueryParams,
  FinanceStatistics,
} from '../../../services/financeService';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 使用从服务中导入的接口类型
type TransactionRecord = Transaction;

const TransactionRecords: React.FC = () => {
  const [records, setRecords] = useState<TransactionRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<TransactionQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [statistics, setStatistics] = useState<FinanceStatistics>({
    totalRevenue: 0,
    totalCommission: 0,
    totalTransactions: 0,
    todayRevenue: 0,
    todayTransactions: 0,
    monthlyRevenue: 0,
    typeDistribution: {} as Record<TransactionType, number>,
    statusDistribution: {} as Record<TransactionStatus, number>,
  });
  const [searchForm] = Form.useForm();

  // 获取交易记录列表
  const fetchRecords = useCallback(async () => {
    setLoading(true);
    try {
      const response = await financeService.getTransactionHistory(queryParams);
      if (response.success) {
        setRecords(response.data.transactions);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取交易记录失败');
      }
    } catch (error: any) {
      console.error('获取交易记录失败:', error);
      message.error(error.message || '获取交易记录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [queryParams]);

  // 获取统计数据
  const fetchStatistics = useCallback(async () => {
    try {
      const response = await financeService.getFinanceStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error);
    }
  }, []);

  useEffect(() => {
    fetchRecords();
    fetchStatistics();
  }, [fetchRecords, fetchStatistics]);

  // 搜索处理
  const handleSearch = (values: any) => {
    const { dateRange, ...otherValues } = values;
    setQueryParams({
      ...queryParams,
      ...otherValues,
      startDate: dateRange ? dateRange[0].format('YYYY-MM-DD') : undefined,
      endDate: dateRange ? dateRange[1].format('YYYY-MM-DD') : undefined,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 导出数据
  const handleExport = () => {
    message.info('导出功能开发中...');
  };

  // 查看详情
  const handleViewDetail = (record: TransactionRecord) => {
    message.info(`查看交易详情: ${record.transactionNo}`);
  };

  // 交易类型映射
  const typeMap = {
    [TransactionType.DEPOSIT]: { text: '充值', color: 'green' },
    [TransactionType.WITHDRAWAL]: { text: '提现', color: 'orange' },
    [TransactionType.AUCTION_PAYMENT]: { text: '拍卖支付', color: 'blue' },
    [TransactionType.AUCTION_REFUND]: { text: '拍卖退款', color: 'red' },
    [TransactionType.COMMISSION_EARN]: { text: '佣金收入', color: 'purple' },
    [TransactionType.COMMISSION_PAY]: { text: '佣金支出', color: 'magenta' },
  };

  // 状态映射
  const statusMap = {
    [TransactionStatus.PENDING]: { text: '处理中', color: 'processing' },
    [TransactionStatus.SUCCESS]: { text: '成功', color: 'success' },
    [TransactionStatus.FAILED]: { text: '失败', color: 'error' },
    [TransactionStatus.CANCELLED]: { text: '已取消', color: 'default' },
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>交易记录</Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收入"
              value={statistics.totalRevenue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总交易笔数"
              value={statistics.totalTransactions}
              suffix="笔"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日收入"
              value={statistics.todayRevenue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日交易笔数"
              value={statistics.todayTransactions}
              suffix="笔"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card className="search-card" size="small" style={{ marginBottom: 16 }}>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="transactionNo" label="交易单号">
                <Input placeholder="请输入交易单号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="userId" label="用户ID">
                <Input placeholder="请输入用户ID" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="type" label="交易类型">
                <Select placeholder="请选择交易类型" allowClear>
                  {Object.entries(typeMap).map(([key, value]) => (
                    <Option key={key} value={key}>
                      {value.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="交易状态">
                <Select placeholder="请选择交易状态" allowClear>
                  {Object.entries(statusMap).map(([key, value]) => (
                    <Option key={key} value={key}>
                      {value.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="dateRange" label="交易时间">
                <RangePicker
                  style={{ width: '100%' }}
                  placeholder={['开始日期', '结束日期']}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small" style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出数据
              </Button>
            </Space>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchRecords}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 交易记录表格 */}
      <Card>
        <Table
          columns={[
            {
              title: '交易单号',
              dataIndex: 'transactionNo',
              key: 'transactionNo',
              width: 150,
              render: (text: string) => (
                <Tooltip title={text}>
                  <span style={{ fontFamily: 'monospace' }}>{text}</span>
                </Tooltip>
              ),
            },
            {
              title: '用户名称',
              dataIndex: 'userName',
              key: 'userName',
              width: 120,
            },
            {
              title: '交易类型',
              dataIndex: 'type',
              key: 'type',
              width: 100,
              render: (type: TransactionType) => {
                const typeInfo = typeMap[type];
                return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
              },
            },
            {
              title: '交易金额',
              dataIndex: 'amount',
              key: 'amount',
              width: 120,
              render: (amount: number) => (
                <span style={{ color: '#3f8600', fontWeight: 'bold' }}>
                  ¥{amount.toLocaleString()}
                </span>
              ),
            },
            {
              title: '交易状态',
              dataIndex: 'status',
              key: 'status',
              width: 100,
              render: (status: TransactionStatus) => {
                const statusInfo = statusMap[status];
                return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
              },
            },
            {
              title: '交易描述',
              dataIndex: 'description',
              key: 'description',
              width: 200,
              ellipsis: {
                showTitle: false,
              },
              render: (text: string) => (
                <Tooltip title={text}>
                  {text}
                </Tooltip>
              ),
            },
            {
              title: '支付方式',
              dataIndex: 'paymentMethod',
              key: 'paymentMethod',
              width: 100,
              render: (text: string) => text || '-',
            },
            {
              title: '交易时间',
              dataIndex: 'createdAt',
              key: 'createdAt',
              width: 160,
              render: (text: string) => new Date(text).toLocaleString(),
            },
            {
              title: '操作',
              key: 'action',
              width: 100,
              fixed: 'right',
              render: (_, record: TransactionRecord) => (
                <Button
                  type="link"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handleViewDetail(record)}
                >
                  详情
                </Button>
              ),
            },
          ]}
          dataSource={records}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>
    </div>
  );
};

export default TransactionRecords;
