import React, { useState } from 'react';
import {
  Card,
  List,
  Typography,
  Space,
  Button,
  Tag,
  Avatar,
  Tabs,
  Badge,
  Empty,
  Pagination,
  Modal,
  message,
} from 'antd';
import {
  BellOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface Notification {
  id: string;
  type: 'system' | 'auction' | 'order' | 'security';
  level: 'info' | 'warning' | 'error' | 'success';
  title: string;
  content: string;
  time: string;
  read: boolean;
  important: boolean;
}

const Notifications: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  
  const [notifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'system',
      level: 'info',
      title: '系统维护通知',
      content: '系统将于今晚23:00-01:00进行例行维护，期间可能无法正常访问。',
      time: '2024-01-15 14:30:00',
      read: false,
      important: true,
    },
    {
      id: '2',
      type: 'auction',
      level: 'success',
      title: '拍卖会创建成功',
      content: '您创建的"春季花卉专场拍卖会"已审核通过，将于明日开始。',
      time: '2024-01-15 10:15:00',
      read: true,
      important: false,
    },
    {
      id: '3',
      type: 'order',
      level: 'warning',
      title: '订单待处理',
      content: '您有3个订单需要处理，请及时查看。',
      time: '2024-01-15 09:00:00',
      read: false,
      important: false,
    },
    {
      id: '4',
      type: 'security',
      level: 'error',
      title: '异常登录提醒',
      content: '检测到您的账户在异地登录，如非本人操作请立即修改密码。',
      time: '2024-01-14 22:30:00',
      read: true,
      important: true,
    },
    {
      id: '5',
      type: 'system',
      level: 'info',
      title: '功能更新',
      content: '系统新增了批量导出功能，您可以在相关页面体验。',
      time: '2024-01-14 16:00:00',
      read: true,
      important: false,
    },
  ]);

  const getTypeIcon = (type: string) => {
    const iconMap = {
      system: <InfoCircleOutlined />,
      auction: <BellOutlined />,
      order: <ExclamationCircleOutlined />,
      security: <WarningOutlined />,
    };
    return iconMap[type as keyof typeof iconMap] || <InfoCircleOutlined />;
  };

  const getLevelColor = (level: string) => {
    const colorMap = {
      info: '#1890ff',
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f',
    };
    return colorMap[level as keyof typeof colorMap] || '#1890ff';
  };

  const getTypeLabel = (type: string) => {
    const labelMap = {
      system: '系统',
      auction: '拍卖',
      order: '订单',
      security: '安全',
    };
    return labelMap[type as keyof typeof labelMap] || '未知';
  };

  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.read;
    if (activeTab === 'important') return notification.important;
    return notification.type === activeTab;
  });

  const unreadCount = notifications.filter(n => !n.read).length;
  const importantCount = notifications.filter(n => n.important).length;

  const handleMarkAsRead = (id: string) => {
    // 这里应该调用后端API标记为已读
    message.success('已标记为已读');
  };

  const handleMarkAllAsRead = () => {
    Modal.confirm({
      title: '标记全部为已读',
      content: '确定要将所有通知标记为已读吗？',
      onOk: () => {
        // 这里应该调用后端API标记全部为已读
        message.success('已标记全部为已读');
      },
    });
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '删除通知',
      content: '确定要删除这条通知吗？',
      onOk: () => {
        // 这里应该调用后端API删除通知
        message.success('通知已删除');
      },
    });
  };

  const handleBatchDelete = () => {
    if (selectedNotifications.length === 0) {
      message.warning('请先选择要删除的通知');
      return;
    }
    
    Modal.confirm({
      title: '批量删除',
      content: `确定要删除选中的 ${selectedNotifications.length} 条通知吗？`,
      onOk: () => {
        // 这里应该调用后端API批量删除
        setSelectedNotifications([]);
        message.success('批量删除成功');
      },
    });
  };

  const handleViewDetail = (notification: Notification) => {
    Modal.info({
      title: notification.title,
      content: (
        <div>
          <div style={{ marginBottom: 8 }}>
            <Tag color={getLevelColor(notification.level)}>
              {getTypeLabel(notification.type)}
            </Tag>
            <span style={{ marginLeft: 8, color: '#666' }}>
              {notification.time}
            </span>
          </div>
          <div>{notification.content}</div>
        </div>
      ),
      width: 600,
    });
    
    if (!notification.read) {
      handleMarkAsRead(notification.id);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>
          <BellOutlined /> 消息通知
        </Title>
        <Space>
          <Button onClick={handleMarkAllAsRead}>
            全部已读
          </Button>
          <Button danger onClick={handleBatchDelete}>
            批量删除
          </Button>
        </Space>
      </div>

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="全部" key="all" />
          <TabPane 
            tab={
              <Badge count={unreadCount} size="small">
                <span>未读</span>
              </Badge>
            } 
            key="unread" 
          />
          <TabPane 
            tab={
              <Badge count={importantCount} size="small">
                <span>重要</span>
              </Badge>
            } 
            key="important" 
          />
          <TabPane tab="系统" key="system" />
          <TabPane tab="拍卖" key="auction" />
          <TabPane tab="订单" key="order" />
          <TabPane tab="安全" key="security" />
        </Tabs>

        {filteredNotifications.length === 0 ? (
          <Empty description="暂无通知" />
        ) : (
          <>
            <List
              itemLayout="horizontal"
              dataSource={filteredNotifications}
              renderItem={(item) => (
                <List.Item
                  style={{
                    backgroundColor: item.read ? 'transparent' : '#f6f8fa',
                    padding: '16px',
                    borderRadius: '6px',
                    marginBottom: '8px',
                    border: item.important ? '1px solid #faad14' : '1px solid #f0f0f0',
                  }}
                  actions={[
                    <Button
                      type="link"
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => handleViewDetail(item)}
                    >
                      查看
                    </Button>,
                    !item.read && (
                      <Button
                        type="link"
                        size="small"
                        icon={<CheckOutlined />}
                        onClick={() => handleMarkAsRead(item.id)}
                      >
                        已读
                      </Button>
                    ),
                    <Button
                      type="link"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDelete(item.id)}
                    >
                      删除
                    </Button>,
                  ].filter(Boolean)}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        style={{ backgroundColor: getLevelColor(item.level) }}
                        icon={getTypeIcon(item.type)}
                      />
                    }
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <span style={{ fontWeight: item.read ? 'normal' : 'bold' }}>
                          {item.title}
                        </span>
                        {item.important && <Tag color="orange">重要</Tag>}
                        {!item.read && <Badge status="processing" />}
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: 4 }}>
                          {item.content}
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                          <Tag color={getLevelColor(item.level)} style={{ fontSize: '12px' }}>
                            {getTypeLabel(item.type)}
                          </Tag>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {item.time}
                          </Text>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
            
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Pagination
                current={1}
                total={filteredNotifications.length}
                pageSize={10}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
              />
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default Notifications;
