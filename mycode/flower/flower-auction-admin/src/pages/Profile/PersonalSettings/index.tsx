import React, { useState } from 'react';
import {
  Card,
  Form,
  Switch,
  Select,
  Button,
  message,
  Typography,
  Divider,
  Space,
  Alert,
  TimePicker,
  Checkbox,
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  BellOutlined,
  EyeOutlined,
  GlobalOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

interface UserSettings {
  // 界面设置
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  sidebarCollapsed: boolean;
  
  // 通知设置
  emailNotifications: boolean;
  smsNotifications: boolean;
  systemNotifications: boolean;
  auctionNotifications: boolean;
  orderNotifications: boolean;
  
  // 工作时间设置
  workStartTime: string;
  workEndTime: string;
  workDays: string[];
  
  // 隐私设置
  showOnlineStatus: boolean;
  showLastLogin: boolean;
  allowDirectMessage: boolean;
  
  // 其他设置
  autoSave: boolean;
  autoLogout: boolean;
  logoutTime: number; // 分钟
}

const PersonalSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<UserSettings>({
    theme: 'light',
    language: 'zh-CN',
    sidebarCollapsed: false,
    
    emailNotifications: true,
    smsNotifications: false,
    systemNotifications: true,
    auctionNotifications: true,
    orderNotifications: true,
    
    workStartTime: '09:00',
    workEndTime: '18:00',
    workDays: ['1', '2', '3', '4', '5'],
    
    showOnlineStatus: true,
    showLastLogin: true,
    allowDirectMessage: true,
    
    autoSave: true,
    autoLogout: false,
    logoutTime: 30,
  });

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      // 这里应该调用后端API保存设置
      // await userService.updateSettings(values);
      
      setSettings({ ...settings, ...values });
      message.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue(settings);
    message.info('已重置为当前保存的设置');
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <SettingOutlined /> 个人设置
      </Title>

      <Form
        form={form}
        layout="vertical"
        initialValues={settings}
        onFinish={handleSave}
      >
        <Card title="界面设置" style={{ marginBottom: 24 }}>
          <Form.Item
            name="theme"
            label="主题模式"
            tooltip="选择您喜欢的界面主题"
          >
            <Select>
              <Option value="light">浅色主题</Option>
              <Option value="dark">深色主题</Option>
              <Option value="auto">跟随系统</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="language"
            label="语言设置"
          >
            <Select>
              <Option value="zh-CN">简体中文</Option>
              <Option value="en-US">English</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="sidebarCollapsed"
            label="侧边栏默认状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="收起" unCheckedChildren="展开" />
          </Form.Item>
        </Card>

        <Card title="通知设置" style={{ marginBottom: 24 }}>
          <Alert
            message="通知提醒"
            description="您可以选择接收哪些类型的通知提醒"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            name="emailNotifications"
            label="邮件通知"
            valuePropName="checked"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="smsNotifications"
            label="短信通知"
            valuePropName="checked"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="systemNotifications"
            label="系统通知"
            valuePropName="checked"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="auctionNotifications"
            label="拍卖通知"
            valuePropName="checked"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="orderNotifications"
            label="订单通知"
            valuePropName="checked"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>
        </Card>

        <Card title="工作时间设置" style={{ marginBottom: 24 }}>
          <Form.Item
            name="workStartTime"
            label="工作开始时间"
          >
            <TimePicker
              format="HH:mm"
              placeholder="选择开始时间"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="workEndTime"
            label="工作结束时间"
          >
            <TimePicker
              format="HH:mm"
              placeholder="选择结束时间"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="workDays"
            label="工作日"
          >
            <Checkbox.Group>
              <Checkbox value="1">周一</Checkbox>
              <Checkbox value="2">周二</Checkbox>
              <Checkbox value="3">周三</Checkbox>
              <Checkbox value="4">周四</Checkbox>
              <Checkbox value="5">周五</Checkbox>
              <Checkbox value="6">周六</Checkbox>
              <Checkbox value="0">周日</Checkbox>
            </Checkbox.Group>
          </Form.Item>
        </Card>

        <Card title="隐私设置" style={{ marginBottom: 24 }}>
          <Form.Item
            name="showOnlineStatus"
            label="显示在线状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="显示" unCheckedChildren="隐藏" />
          </Form.Item>

          <Form.Item
            name="showLastLogin"
            label="显示最后登录时间"
            valuePropName="checked"
          >
            <Switch checkedChildren="显示" unCheckedChildren="隐藏" />
          </Form.Item>

          <Form.Item
            name="allowDirectMessage"
            label="允许私信"
            valuePropName="checked"
          >
            <Switch checkedChildren="允许" unCheckedChildren="禁止" />
          </Form.Item>
        </Card>

        <Card title="其他设置" style={{ marginBottom: 24 }}>
          <Form.Item
            name="autoSave"
            label="自动保存"
            valuePropName="checked"
            tooltip="自动保存您的工作内容"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="autoLogout"
            label="自动登出"
            valuePropName="checked"
            tooltip="长时间无操作时自动登出"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>

          <Form.Item
            name="logoutTime"
            label="自动登出时间（分钟）"
            tooltip="无操作多长时间后自动登出"
          >
            <Select>
              <Option value={15}>15分钟</Option>
              <Option value={30}>30分钟</Option>
              <Option value={60}>1小时</Option>
              <Option value={120}>2小时</Option>
              <Option value={240}>4小时</Option>
            </Select>
          </Form.Item>
        </Card>

        <Card>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={loading}
            >
              保存设置
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </Card>
      </Form>
    </div>
  );
};

export default PersonalSettings;
