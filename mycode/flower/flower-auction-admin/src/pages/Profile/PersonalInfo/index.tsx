import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  message,
  Row,
  Col,
  Typography,
  Divider,
  Space,
  Tag,
  Descriptions,
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  SaveOutlined,
  CameraOutlined,
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../../hooks/useAuth';
import type { UploadProps } from 'antd';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface UserProfile {
  id: number;
  username: string;
  realName: string;
  email: string;
  phone: string;
  avatar?: string;
  department: string;
  position: string;
  address: string;
  bio: string;
  joinDate: string;
  lastLoginTime: string;
  status: 'active' | 'inactive';
}

const PersonalInfo: React.FC = () => {
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile>({
    id: 1,
    username: user?.username || 'admin',
    realName: user?.realName || '系统管理员',
    email: user?.email || '<EMAIL>',
    phone: '138****8888',
    avatar: user?.avatar,
    department: '技术部',
    position: '系统管理员',
    address: '云南省昆明市',
    bio: '负责昆明花卉拍卖系统的运维和管理工作',
    joinDate: '2023-01-01',
    lastLoginTime: '2024-01-15 10:30:00',
    status: 'active',
  });

  useEffect(() => {
    form.setFieldsValue(userProfile);
  }, [userProfile, form]);

  const handleEdit = () => {
    setEditing(true);
  };

  const handleCancel = () => {
    setEditing(false);
    form.setFieldsValue(userProfile);
  };

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      // 这里应该调用后端API更新用户信息
      // await userService.updateProfile(values);
      
      const updatedProfile = { ...userProfile, ...values };
      setUserProfile(updatedProfile);
      setEditing(false);
      message.success('个人信息更新成功');
    } catch (error) {
      console.error('更新个人信息失败:', error);
      message.error('更新个人信息失败');
    } finally {
      setLoading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'avatar',
    action: '/api/upload/avatar',
    headers: {
      authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    onChange: (info) => {
      if (info.file.status === 'uploading') {
        setUploading(true);
        return;
      }
      if (info.file.status === 'done') {
        setUploading(false);
        if (info.file.response?.success) {
          const newAvatar = info.file.response.data.url;
          setUserProfile(prev => ({ ...prev, avatar: newAvatar }));
          message.success('头像上传成功');
        } else {
          message.error('头像上传失败');
        }
      }
      if (info.file.status === 'error') {
        setUploading(false);
        message.error('头像上传失败');
      }
    },
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <UserOutlined /> 个人信息
      </Title>

      <Row gutter={24}>
        <Col span={8}>
          <Card title="头像信息" style={{ textAlign: 'center' }}>
            <Space direction="vertical" size="large">
              <Avatar
                size={120}
                src={userProfile.avatar}
                icon={<UserOutlined />}
              />
              
              <Upload {...uploadProps} showUploadList={false}>
                <Button
                  icon={<CameraOutlined />}
                  loading={uploading}
                  type="primary"
                  ghost
                >
                  {uploading ? '上传中...' : '更换头像'}
                </Button>
              </Upload>
              
              <div>
                <Title level={4} style={{ margin: 0 }}>
                  {userProfile.realName}
                </Title>
                <Text type="secondary">@{userProfile.username}</Text>
              </div>
              
              <Tag color={userProfile.status === 'active' ? 'green' : 'red'}>
                {userProfile.status === 'active' ? '在线' : '离线'}
              </Tag>
            </Space>
          </Card>

          <Card title="基本统计" style={{ marginTop: 16 }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="加入时间">
                <CalendarOutlined style={{ marginRight: 4 }} />
                {userProfile.joinDate}
              </Descriptions.Item>
              <Descriptions.Item label="最后登录">
                <CalendarOutlined style={{ marginRight: 4 }} />
                {userProfile.lastLoginTime}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col span={16}>
          <Card
            title="详细信息"
            extra={
              !editing ? (
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={handleEdit}
                >
                  编辑信息
                </Button>
              ) : (
                <Space>
                  <Button onClick={handleCancel}>取消</Button>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => form.submit()}
                    loading={loading}
                  >
                    保存
                  </Button>
                </Space>
              )
            }
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              disabled={!editing}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="realName"
                    label="真实姓名"
                    rules={[{ required: true, message: '请输入真实姓名' }]}
                  >
                    <Input prefix={<UserOutlined />} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="username"
                    label="用户名"
                    rules={[{ required: true, message: '请输入用户名' }]}
                  >
                    <Input prefix={<UserOutlined />} disabled />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="email"
                    label="邮箱地址"
                    rules={[
                      { required: true, message: '请输入邮箱地址' },
                      { type: 'email', message: '请输入有效的邮箱地址' },
                    ]}
                  >
                    <Input prefix={<MailOutlined />} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="phone"
                    label="手机号码"
                    rules={[{ required: true, message: '请输入手机号码' }]}
                  >
                    <Input prefix={<PhoneOutlined />} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="department"
                    label="所属部门"
                    rules={[{ required: true, message: '请输入所属部门' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="position"
                    label="职位"
                    rules={[{ required: true, message: '请输入职位' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="address"
                label="地址"
              >
                <Input prefix={<EnvironmentOutlined />} />
              </Form.Item>

              <Form.Item
                name="bio"
                label="个人简介"
              >
                <TextArea
                  rows={4}
                  placeholder="请输入个人简介..."
                  maxLength={200}
                  showCount
                />
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PersonalInfo;
