import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  message,
  Typography,
  Space,
  Alert,
  Table,
  Tag,
  Modal,
  Steps,
  QRCode,
} from 'antd';
import {
  LockOutlined,
  SafetyOutlined,
  MobileOutlined,
  MailOutlined,
  EyeOutlined,
  DeleteOutlined,
  PlusOutlined,
  ShieldOutlined,
} from '@ant-design/icons';
import { LogoutButton } from '../../../components';

const { Title, Text } = Typography;
const { Step } = Steps;

interface LoginDevice {
  id: string;
  deviceName: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  browser: string;
  os: string;
  ip: string;
  location: string;
  lastActive: string;
  isCurrent: boolean;
}

const PersonalSecurity: React.FC = () => {
  const [passwordForm] = Form.useForm();
  const [phoneForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [twoFactorModalVisible, setTwoFactorModalVisible] = useState(false);
  const [twoFactorStep, setTwoFactorStep] = useState(0);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  const [loginDevices] = useState<LoginDevice[]>([
    {
      id: '1',
      deviceName: 'MacBook Pro',
      deviceType: 'desktop',
      browser: 'Chrome 120.0',
      os: 'macOS 14.0',
      ip: '*************',
      location: '昆明市',
      lastActive: '2024-01-15 10:30:00',
      isCurrent: true,
    },
    {
      id: '2',
      deviceName: 'iPhone 15',
      deviceType: 'mobile',
      browser: 'Safari 17.0',
      os: 'iOS 17.0',
      ip: '*************',
      location: '昆明市',
      lastActive: '2024-01-14 18:45:00',
      isCurrent: false,
    },
  ]);

  const handleChangePassword = async (values: any) => {
    setLoading(true);
    try {
      // 这里应该调用后端API修改密码
      // await authService.changePassword(values);
      
      message.success('密码修改成功，请重新登录');
      passwordForm.resetFields();
      
      // 延迟后跳转到登录页
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('修改密码失败');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePhone = async (values: any) => {
    setLoading(true);
    try {
      // 这里应该调用后端API修改手机号
      // await userService.changePhone(values);
      
      message.success('手机号修改成功');
      phoneForm.resetFields();
    } catch (error) {
      console.error('修改手机号失败:', error);
      message.error('修改手机号失败');
    } finally {
      setLoading(false);
    }
  };

  const handleChangeEmail = async (values: any) => {
    setLoading(true);
    try {
      // 这里应该调用后端API修改邮箱
      // await userService.changeEmail(values);
      
      message.success('邮箱修改成功');
      emailForm.resetFields();
    } catch (error) {
      console.error('修改邮箱失败:', error);
      message.error('修改邮箱失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveDevice = (deviceId: string) => {
    Modal.confirm({
      title: '移除设备',
      content: '确定要移除此设备的登录状态吗？该设备将被强制登出。',
      onOk: async () => {
        try {
          // 这里应该调用后端API移除设备
          // await authService.removeDevice(deviceId);
          
          message.success('设备已移除');
        } catch (error) {
          console.error('移除设备失败:', error);
          message.error('移除设备失败');
        }
      },
    });
  };

  const handleEnableTwoFactor = () => {
    setTwoFactorModalVisible(true);
    setTwoFactorStep(0);
  };

  const deviceColumns = [
    {
      title: '设备信息',
      key: 'device',
      render: (record: LoginDevice) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            {record.deviceName}
            {record.isCurrent && <Tag color="green" style={{ marginLeft: 8 }}>当前设备</Tag>}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.browser} · {record.os}
          </div>
        </div>
      ),
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '最后活跃',
      dataIndex: 'lastActive',
      key: 'lastActive',
    },
    {
      title: '操作',
      key: 'action',
      render: (record: LoginDevice) => (
        <Space>
          {!record.isCurrent && (
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveDevice(record.id)}
            >
              移除
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <SafetyOutlined /> 安全设置
      </Title>

      {/* 密码修改 */}
      <Card title="修改密码" style={{ marginBottom: 24 }}>
        <Alert
          message="密码安全提示"
          description="为了您的账户安全，建议定期更换密码，密码应包含大小写字母、数字和特殊字符。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
          style={{ maxWidth: 400 }}
        >
          <Form.Item
            name="currentPassword"
            label="当前密码"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password prefix={<LockOutlined />} />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码至少8个字符' },
            ]}
          >
            <Input.Password prefix={<LockOutlined />} />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              修改密码
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 双因子认证 */}
      <Card title="双因子认证" style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>
              双因子认证 {twoFactorEnabled ? <Tag color="green">已启用</Tag> : <Tag color="red">未启用</Tag>}
            </div>
            <div style={{ color: '#666' }}>
              启用双因子认证可以大大提高您账户的安全性
            </div>
          </div>
          <Button
            type={twoFactorEnabled ? 'default' : 'primary'}
            icon={<ShieldOutlined />}
            onClick={handleEnableTwoFactor}
          >
            {twoFactorEnabled ? '管理' : '启用'}
          </Button>
        </div>
      </Card>

      {/* 登录设备管理 */}
      <Card title="登录设备管理" style={{ marginBottom: 24 }}>
        <Alert
          message="设备安全"
          description="定期检查登录设备，如发现异常设备请及时移除。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Table
          columns={deviceColumns}
          dataSource={loginDevices}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>

      {/* 安全操作 */}
      <Card title="安全操作">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="危险操作"
            description="以下操作可能会影响您的账户安全，请谨慎操作。"
            type="error"
            showIcon
          />
          
          <div style={{ padding: '16px 0' }}>
            <LogoutButton type="primary" danger>
              立即登出
            </LogoutButton>
          </div>
        </Space>
      </Card>

      {/* 双因子认证设置弹窗 */}
      <Modal
        title="设置双因子认证"
        open={twoFactorModalVisible}
        onCancel={() => setTwoFactorModalVisible(false)}
        footer={null}
        width={600}
      >
        <Steps current={twoFactorStep} style={{ marginBottom: 24 }}>
          <Step title="下载应用" />
          <Step title="扫描二维码" />
          <Step title="验证绑定" />
        </Steps>

        {twoFactorStep === 0 && (
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 16 }}>
              请先下载并安装身份验证器应用，推荐使用：
            </div>
            <Space>
              <Button>Google Authenticator</Button>
              <Button>Microsoft Authenticator</Button>
              <Button>Authy</Button>
            </Space>
            <div style={{ marginTop: 16 }}>
              <Button type="primary" onClick={() => setTwoFactorStep(1)}>
                下一步
              </Button>
            </div>
          </div>
        )}

        {twoFactorStep === 1 && (
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 16 }}>
              请使用身份验证器应用扫描下方二维码：
            </div>
            <QRCode value="otpauth://totp/FlowerAuction:admin?secret=JBSWY3DPEHPK3PXP&issuer=FlowerAuction" />
            <div style={{ marginTop: 16 }}>
              <Button onClick={() => setTwoFactorStep(0)} style={{ marginRight: 8 }}>
                上一步
              </Button>
              <Button type="primary" onClick={() => setTwoFactorStep(2)}>
                下一步
              </Button>
            </div>
          </div>
        )}

        {twoFactorStep === 2 && (
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 16 }}>
              请输入身份验证器应用中显示的6位数字验证码：
            </div>
            <Input
              style={{ width: 200, textAlign: 'center', fontSize: '18px' }}
              maxLength={6}
              placeholder="000000"
            />
            <div style={{ marginTop: 16 }}>
              <Button onClick={() => setTwoFactorStep(1)} style={{ marginRight: 8 }}>
                上一步
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  setTwoFactorEnabled(true);
                  setTwoFactorModalVisible(false);
                  message.success('双因子认证设置成功');
                }}
              >
                完成设置
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PersonalSecurity;
