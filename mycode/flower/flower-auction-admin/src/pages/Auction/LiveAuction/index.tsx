import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Typography,
  Button,
  Input,
  Space,
  Tag,
  Avatar,
  List,
  Row,
  Col,
  Statistic,
  Badge,
  message,
  Modal,
  Form,
  InputNumber,
  Divider,
} from 'antd';
import {
  TrophyOutlined,
  FireOutlined,
  UserOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SoundOutlined,
} from '@ant-design/icons';
import { auctionWebSocket, AuctionEventType, AuctionStatus, BidInfo } from '../../../services/websocketService';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const LiveAuction: React.FC = () => {
  const [connected, setConnected] = useState(false);
  const [auctionStatus, setAuctionStatus] = useState<AuctionStatus | null>(null);
  const [bidHistory, setBidHistory] = useState<BidInfo[]>([]);
  const [participants, setParticipants] = useState<string[]>([]);
  const [messages, setMessages] = useState<any[]>([]);
  const [bidModalVisible, setBidModalVisible] = useState(false);
  const [bidAmount, setBidAmount] = useState<number>(0);
  const [currentUser] = useState({
    id: 'user-123',
    name: '管理员',
    role: 'admin',
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [form] = Form.useForm();

  // 当前拍卖ID
  const auctionId = 'auction-1';

  // 连接WebSocket
  useEffect(() => {
    const connectWebSocket = async () => {
      try {
        await auctionWebSocket.connect();
        setConnected(true);
        message.success('WebSocket连接成功');
        
        // 加入拍卖房间
        auctionWebSocket.joinAuction(auctionId);
      } catch (error) {
        console.error('WebSocket连接失败:', error);
        message.error('WebSocket连接失败');
      }
    };

    connectWebSocket();

    // 设置事件监听器
    auctionWebSocket.on('connected', () => {
      setConnected(true);
      addMessage('系统', '已连接到拍卖服务器', 'system');
    });

    auctionWebSocket.on('disconnected', () => {
      setConnected(false);
      addMessage('系统', '与拍卖服务器断开连接', 'system');
    });

    auctionWebSocket.on(AuctionEventType.AUCTION_STARTED, (data: any) => {
      addMessage('系统', '拍卖已开始！', 'system');
      if (auctionStatus) {
        setAuctionStatus({ ...auctionStatus, status: 'active' });
      }
    });

    auctionWebSocket.on(AuctionEventType.AUCTION_ENDED, (data: any) => {
      addMessage('系统', '拍卖已结束！', 'system');
      if (auctionStatus) {
        setAuctionStatus({ ...auctionStatus, status: 'ended' });
      }
    });

    auctionWebSocket.on(AuctionEventType.AUCTION_PAUSED, (data: any) => {
      addMessage('系统', '拍卖已暂停', 'system');
      if (auctionStatus) {
        setAuctionStatus({ ...auctionStatus, status: 'paused' });
      }
    });

    auctionWebSocket.on(AuctionEventType.AUCTION_RESUMED, (data: any) => {
      addMessage('系统', '拍卖已恢复', 'system');
      if (auctionStatus) {
        setAuctionStatus({ ...auctionStatus, status: 'active' });
      }
    });

    auctionWebSocket.on(AuctionEventType.BID_PLACED, (data: BidInfo) => {
      setBidHistory(prev => [data, ...prev]);
      addMessage(data.bidderName, `出价 ¥${data.bidAmount}`, 'bid');
      
      // 更新拍卖状态
      if (auctionStatus) {
        setAuctionStatus({
          ...auctionStatus,
          currentPrice: data.bidAmount,
          bidCount: auctionStatus.bidCount + 1,
          lastBidder: {
            id: data.bidderId,
            name: data.bidderName,
            bidAmount: data.bidAmount,
            bidTime: data.bidTime,
          },
        });
      }
    });

    auctionWebSocket.on(AuctionEventType.PRICE_UPDATED, (data: any) => {
      if (auctionStatus) {
        setAuctionStatus({
          ...auctionStatus,
          currentPrice: data.currentPrice,
          lastBidder: data.lastBidder,
        });
      }
    });

    auctionWebSocket.on(AuctionEventType.USER_JOINED, (data: any) => {
      addMessage('系统', `${data.userId} 加入了拍卖`, 'system');
      if (auctionStatus) {
        setAuctionStatus({
          ...auctionStatus,
          participantCount: data.participantCount,
        });
      }
    });

    auctionWebSocket.on(AuctionEventType.USER_LEFT, (data: any) => {
      addMessage('系统', `${data.userId} 离开了拍卖`, 'system');
      if (auctionStatus) {
        setAuctionStatus({
          ...auctionStatus,
          participantCount: data.participantCount,
        });
      }
    });

    auctionWebSocket.on('auction_status', (data: AuctionStatus) => {
      setAuctionStatus(data);
    });

    auctionWebSocket.on('error', (data: any) => {
      message.error(data.message || '发生错误');
    });

    return () => {
      auctionWebSocket.leaveAuction(auctionId);
      auctionWebSocket.disconnect();
    };
  }, []);

  // 添加消息
  const addMessage = (sender: string, content: string, type: 'system' | 'bid' | 'chat' = 'chat') => {
    const newMessage = {
      id: Date.now(),
      sender,
      content,
      type,
      timestamp: new Date().toLocaleTimeString(),
    };
    setMessages(prev => [...prev, newMessage]);
  };

  // 滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 开始拍卖
  const handleStartAuction = () => {
    auctionWebSocket.startAuction(auctionId);
  };

  // 结束拍卖
  const handleEndAuction = () => {
    Modal.confirm({
      title: '确认结束拍卖',
      content: '确定要结束当前拍卖吗？此操作不可撤销。',
      onOk: () => {
        auctionWebSocket.endAuction(auctionId);
      },
    });
  };

  // 暂停拍卖
  const handlePauseAuction = () => {
    auctionWebSocket.pauseAuction(auctionId);
  };

  // 恢复拍卖
  const handleResumeAuction = () => {
    auctionWebSocket.resumeAuction(auctionId);
  };

  // 提交出价
  const handlePlaceBid = () => {
    if (!bidAmount || bidAmount <= (auctionStatus?.currentPrice || 0)) {
      message.error('出价必须高于当前价格');
      return;
    }

    auctionWebSocket.placeBid(auctionId, bidAmount);
    setBidModalVisible(false);
    form.resetFields();
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      waiting: { color: 'default', text: '等待开始' },
      active: { color: 'green', text: '进行中' },
      paused: { color: 'orange', text: '已暂停' },
      ended: { color: 'red', text: '已结束' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.waiting;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  return (
    <div style={{ padding: 24 }}>
      <Row gutter={24}>
        <Col span={16}>
          {/* 拍卖信息 */}
          <Card
            title={
              <Space>
                <TrophyOutlined />
                <span>精品红玫瑰专场拍卖</span>
                {auctionStatus && getStatusTag(auctionStatus.status)}
                <Badge
                  status={connected ? 'success' : 'error'}
                  text={connected ? '已连接' : '未连接'}
                />
              </Space>
            }
            extra={
              <Space>
                {auctionStatus?.status === 'waiting' && (
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={handleStartAuction}
                  >
                    开始拍卖
                  </Button>
                )}
                {auctionStatus?.status === 'active' && (
                  <>
                    <Button
                      icon={<PauseCircleOutlined />}
                      onClick={handlePauseAuction}
                    >
                      暂停
                    </Button>
                    <Button
                      danger
                      icon={<StopOutlined />}
                      onClick={handleEndAuction}
                    >
                      结束
                    </Button>
                  </>
                )}
                {auctionStatus?.status === 'paused' && (
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={handleResumeAuction}
                  >
                    恢复
                  </Button>
                )}
              </Space>
            }
          >
            {auctionStatus && (
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic
                    title="当前价格"
                    value={auctionStatus.currentPrice}
                    prefix={<DollarOutlined />}
                    suffix="元"
                    valueStyle={{ color: '#cf1322' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="起拍价"
                    value={auctionStatus.startPrice}
                    prefix={<DollarOutlined />}
                    suffix="元"
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="出价次数"
                    value={auctionStatus.bidCount}
                    prefix={<FireOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="参与人数"
                    value={auctionStatus.participantCount}
                    prefix={<UserOutlined />}
                  />
                </Col>
              </Row>
            )}

            {auctionStatus?.lastBidder && (
              <div style={{ marginTop: 16, padding: 16, backgroundColor: '#f6ffed', borderRadius: 6 }}>
                <Text strong>最新出价：</Text>
                <Text>{auctionStatus.lastBidder.name}</Text>
                <Text> 出价 </Text>
                <Text strong style={{ color: '#cf1322' }}>¥{auctionStatus.lastBidder.bidAmount}</Text>
                <Text type="secondary"> ({auctionStatus.lastBidder.bidTime})</Text>
              </div>
            )}

            <div style={{ marginTop: 16 }}>
              <Button
                type="primary"
                size="large"
                icon={<DollarOutlined />}
                onClick={() => setBidModalVisible(true)}
                disabled={!connected || auctionStatus?.status !== 'active'}
              >
                我要出价
              </Button>
            </div>
          </Card>

          {/* 出价历史 */}
          <Card title="出价历史" style={{ marginTop: 16 }}>
            <List
              dataSource={bidHistory}
              renderItem={(bid) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={
                      <Space>
                        <Text strong>{bid.bidderName}</Text>
                        <Text>出价</Text>
                        <Text strong style={{ color: '#cf1322' }}>¥{bid.bidAmount}</Text>
                        {bid.isWinning && <Tag color="gold">领先</Tag>}
                      </Space>
                    }
                    description={bid.bidTime}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        <Col span={8}>
          {/* 实时消息 */}
          <Card title={<Space><SoundOutlined />实时消息</Space>}>
            <div
              style={{
                height: 400,
                overflowY: 'auto',
                padding: 8,
                border: '1px solid #f0f0f0',
                borderRadius: 6,
              }}
            >
              {messages.map((msg) => (
                <div key={msg.id} style={{ marginBottom: 8 }}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    [{msg.timestamp}]
                  </Text>
                  <Text strong style={{ marginLeft: 4 }}>
                    {msg.sender}:
                  </Text>
                  <Text
                    style={{
                      marginLeft: 4,
                      color: msg.type === 'system' ? '#1890ff' : msg.type === 'bid' ? '#cf1322' : 'inherit',
                    }}
                  >
                    {msg.content}
                  </Text>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 出价模态框 */}
      <Modal
        title="提交出价"
        open={bidModalVisible}
        onOk={handlePlaceBid}
        onCancel={() => setBidModalVisible(false)}
        okText="确认出价"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="出价金额"
            name="bidAmount"
            rules={[
              { required: true, message: '请输入出价金额' },
              {
                validator: (_, value) => {
                  if (value <= (auctionStatus?.currentPrice || 0)) {
                    return Promise.reject('出价必须高于当前价格');
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              step={10}
              placeholder="请输入出价金额"
              addonBefore="¥"
              addonAfter="元"
              value={bidAmount}
              onChange={(value) => setBidAmount(value || 0)}
            />
          </Form.Item>
          {auctionStatus && (
            <div>
              <Text type="secondary">当前价格：¥{auctionStatus.currentPrice}</Text>
              <br />
              <Text type="secondary">最低出价：¥{auctionStatus.currentPrice + 10}</Text>
            </div>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default LiveAuction;
