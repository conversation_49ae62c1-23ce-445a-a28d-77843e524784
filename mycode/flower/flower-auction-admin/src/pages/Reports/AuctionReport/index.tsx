import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  DatePicker,
  Select,
  Button,
  Table,
  Space,
  message,
  Spin,
  Empty,
  Tag,
  Progress,
} from 'antd';
import {
  TrophyOutlined,
  FireOutlined,
  DollarOutlined,
  DownloadOutlined,
  ReloadOutlined,
  RiseOutlined,
} from '@ant-design/icons';
import {
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import dayjs from 'dayjs';
import { auctionReportAPI } from '../../../services/reportService';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 拍卖统计数据接口
interface AuctionStatistics {
  totalAuctions: number;
  activeAuctions: number;
  completedAuctions: number;
  totalBids: number;
  avgBidsPerAuction: number;
  successRate: number;
  avgDuration: number;
  totalRevenue: number;
}

// 拍卖趋势数据接口
interface AuctionTrendData {
  date: string;
  auctions: number;
  bids: number;
  revenue: number;
  participants: number;
}

// 拍卖性能排行接口
interface AuctionPerformanceRank {
  id: number;
  auctionTitle: string;
  productName: string;
  startPrice: number;
  finalPrice: number;
  bidCount: number;
  participants: number;
  duration: number;
  status: 'active' | 'completed' | 'cancelled';
  successRate: number;
}

// 拍卖状态分布接口
interface AuctionStatusDistribution {
  status: string;
  count: number;
  percentage: number;
}

const AuctionReport: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [auctionType, setAuctionType] = useState<string>('all');
  
  const [statistics, setStatistics] = useState<AuctionStatistics>({
    totalAuctions: 0,
    activeAuctions: 0,
    completedAuctions: 0,
    totalBids: 0,
    avgBidsPerAuction: 0,
    successRate: 0,
    avgDuration: 0,
    totalRevenue: 0,
  });

  const [trendData, setTrendData] = useState<AuctionTrendData[]>([]);
  const [performanceRanks, setPerformanceRanks] = useState<AuctionPerformanceRank[]>([]);
  const [statusDistribution, setStatusDistribution] = useState<AuctionStatusDistribution[]>([]);

  // 获取拍卖报表数据
  const fetchAuctionData = useCallback(async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      // 并行获取所有数据
      const [
        statisticsResponse,
        trendResponse,
        statusResponse,
        performanceResponse
      ] = await Promise.all([
        auctionReportAPI.getAuctionReport({
          start_date: startDate,
          end_date: endDate,
          auction_type: auctionType,
        }),
        auctionReportAPI.getAuctionTrend({
          start_date: startDate,
          end_date: endDate,
        }),
        auctionReportAPI.getAuctionStatusDistribution(),
        auctionReportAPI.getAuctionPerformance({
          start_date: startDate,
          end_date: endDate,
          limit: 20,
        })
      ]);

      // 设置统计数据
      if (statisticsResponse.data?.success) {
        setStatistics(statisticsResponse.data.data);
      }

      // 设置趋势数据
      if (trendResponse.data?.success) {
        setTrendData(trendResponse.data.data);
      }

      // 设置状态分布数据
      if (statusResponse.data?.success) {
        setStatusDistribution(statusResponse.data.data);
      }

      // 设置性能排行数据
      if (performanceResponse.data?.success) {
        setPerformanceRanks(performanceResponse.data.data);
      }

    } catch (error: any) {
      console.error('获取拍卖报表数据失败:', error);
      message.error('获取拍卖报表数据失败');
    } finally {
      setLoading(false);
    }
  }, [dateRange, auctionType]);

  useEffect(() => {
    const timer = setTimeout(() => {
      fetchAuctionData();
    }, 300);

    return () => clearTimeout(timer);
  }, [fetchAuctionData]);

  const handleRefresh = () => {
    fetchAuctionData();
  };

  const handleExport = () => {
    message.success('拍卖报表导出功能开发中...');
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'processing', text: '进行中' },
      completed: { color: 'success', text: '已完成' },
      cancelled: { color: 'error', text: '已取消' },
    };
    const config = statusMap[status as keyof typeof statusMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 拍卖性能表格列
  const performanceColumns = [
    {
      title: '拍卖标题',
      dataIndex: 'auctionTitle',
      key: 'auctionTitle',
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '起拍价',
      dataIndex: 'startPrice',
      key: 'startPrice',
      render: (price: number) => `¥${price.toFixed(2)}`,
      sorter: (a: AuctionPerformanceRank, b: AuctionPerformanceRank) => a.startPrice - b.startPrice,
    },
    {
      title: '成交价',
      dataIndex: 'finalPrice',
      key: 'finalPrice',
      render: (price: number) => price > 0 ? `¥${price.toFixed(2)}` : '-',
      sorter: (a: AuctionPerformanceRank, b: AuctionPerformanceRank) => a.finalPrice - b.finalPrice,
    },
    {
      title: '出价次数',
      dataIndex: 'bidCount',
      key: 'bidCount',
      sorter: (a: AuctionPerformanceRank, b: AuctionPerformanceRank) => a.bidCount - b.bidCount,
    },
    {
      title: '参与人数',
      dataIndex: 'participants',
      key: 'participants',
      sorter: (a: AuctionPerformanceRank, b: AuctionPerformanceRank) => a.participants - b.participants,
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => `${duration.toFixed(1)}小时`,
      sorter: (a: AuctionPerformanceRank, b: AuctionPerformanceRank) => a.duration - b.duration,
    },
    {
      title: '成功率',
      dataIndex: 'successRate',
      key: 'successRate',
      render: (rate: number) => (
        <div>
          <Progress
            percent={rate}
            size="small"
            format={(percent) => `${percent}%`}
            strokeColor={rate > 80 ? '#52c41a' : rate > 50 ? '#faad14' : '#f5222d'}
          />
        </div>
      ),
      sorter: (a: AuctionPerformanceRank, b: AuctionPerformanceRank) => a.successRate - b.successRate,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
  ];

  const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <TrophyOutlined /> 拍卖报表
      </Title>

      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col>
            <span style={{ marginRight: 8 }}>时间范围:</span>
            <RangePicker
              value={dateRange}
              onChange={(dates) => {
                if (dates && dates[0] && dates[1]) {
                  setDateRange([dates[0], dates[1]]);
                }
              }}
              style={{ marginRight: 16 }}
            />
          </Col>
          <Col>
            <span style={{ marginRight: 8 }}>拍卖类型:</span>
            <Select
              value={auctionType}
              onChange={setAuctionType}
              style={{ width: 120, marginRight: 16 }}
            >
              <Option value="all">全部</Option>
              <Option value="regular">普通拍卖</Option>
              <Option value="special">专场拍卖</Option>
              <Option value="flash">闪拍</Option>
            </Select>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="拍卖总数"
                value={statistics.totalAuctions}
                valueStyle={{ color: '#1890ff' }}
                prefix={<TrophyOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                进行中 {statistics.activeAuctions} 场
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总出价次数"
                value={statistics.totalBids}
                valueStyle={{ color: '#52c41a' }}
                prefix={<FireOutlined />}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                平均每场 {statistics.avgBidsPerAuction} 次
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="成功率"
                value={statistics.successRate}
                precision={1}
                valueStyle={{ color: '#722ed1' }}
                prefix={<RiseOutlined />}
                suffix="%"
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                已完成 {statistics.completedAuctions} 场
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总收入"
                value={statistics.totalRevenue}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<DollarOutlined />}
                suffix="元"
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                平均时长 {statistics.avgDuration} 小时
              </div>
            </Card>
          </Col>
        </Row>

        {/* 图表区域 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          {/* 拍卖趋势 */}
          <Col span={16}>
            <Card title="拍卖趋势">
              {trendData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="auctions"
                      stroke="#1890ff"
                      name="拍卖场次"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="participants"
                      stroke="#52c41a"
                      name="参与人数"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 拍卖状态分布 */}
          <Col span={8}>
            <Card title="拍卖状态分布">
              {statusDistribution.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statusDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ status, percentage }: any) => `${status} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {statusDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => [value.toLocaleString(), '拍卖数量']} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>
        </Row>

        {/* 拍卖性能排行 */}
        <Card title="拍卖性能排行">
          <Table
            columns={performanceColumns}
            dataSource={performanceRanks}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            size="small"
          />
        </Card>
      </Spin>
    </div>
  );
};

export default AuctionReport;
