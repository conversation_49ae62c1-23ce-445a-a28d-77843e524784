.category-management-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.category-management-container .ant-typography {
  margin-bottom: 24px;
}

/* 分类树节点样式 */
.category-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.category-tree-node .category-name {
  font-weight: 500;
  color: #333;
}

.category-tree-node .category-code {
  color: #666;
  font-size: 12px;
}

.category-tree-node .product-count {
  color: #999;
  font-size: 12px;
}

.category-tree-node .status-disabled {
  color: #ff4d4f;
  font-size: 12px;
  background: #fff2f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.category-tree-node .category-actions {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.category-tree-node:hover .category-actions {
  opacity: 1;
}

/* 操作按钮样式 */
.category-actions .ant-btn {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.category-actions .ant-btn:hover {
  background: rgba(0, 0, 0, 0.04) !important;
  transform: scale(1.1);
  transition: all 0.2s ease;
}

.category-actions .ant-btn:focus {
  background: rgba(0, 0, 0, 0.04) !important;
}

/* 树组件样式 */
.category-management-container .ant-tree {
  background: transparent;
}

.category-management-container .ant-tree .ant-tree-node-content-wrapper {
  border-radius: 4px;
  padding: 4px 8px;
}

.category-management-container .ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: #f5f5f5;
}

.category-management-container .ant-tree .ant-tree-node-selected .ant-tree-node-content-wrapper {
  background-color: #e6f7ff;
}

.category-management-container .ant-tree .ant-tree-switcher {
  color: #666;
}

.category-management-container .ant-tree .ant-tree-switcher:hover {
  color: #1890ff;
}

/* 卡片样式 */
.category-management-container .ant-card {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-management-container .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.category-management-container .ant-card-head-title {
  font-weight: 600;
}

.category-management-container .ant-card-body {
  padding: 24px;
}

/* 描述列表样式 */
.category-management-container .ant-descriptions-item-label {
  font-weight: 500;
  color: #666;
  width: 80px;
}

.category-management-container .ant-descriptions-item-content {
  color: #333;
}

/* 模态框样式 */
.category-management-container .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.category-management-container .ant-modal-body {
  padding: 24px;
}

/* 表单样式 */
.category-management-container .ant-form-item-label > label {
  font-weight: 500;
}

.category-management-container .ant-input,
.category-management-container .ant-select-selector,
.category-management-container .ant-input-number {
  border-radius: 4px;
}

.category-management-container .ant-input:focus,
.category-management-container .ant-input-focused,
.category-management-container .ant-select-focused .ant-select-selector,
.category-management-container .ant-input-number:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.category-management-container .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 12px;
}

.category-management-container .ant-btn-link:hover {
  background: transparent;
}

/* 标签样式 */
.category-management-container .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .category-management-container .ant-row {
    flex-direction: column;
  }
  
  .category-management-container .ant-col:first-child {
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .category-management-container {
    padding: 16px;
  }
  
  .category-tree-node {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .category-tree-node .category-actions {
    opacity: 1;
  }
  
  .category-management-container .ant-card-body {
    padding: 16px;
  }
  
  .category-management-container .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
}

@media (max-width: 576px) {
  .category-management-container {
    padding: 12px;
  }
  
  .category-management-container .ant-typography {
    margin-bottom: 16px;
    font-size: 20px;
  }
  
  .category-management-container .ant-card-body {
    padding: 12px;
  }
  
  .category-management-container .ant-modal-body {
    padding: 16px;
  }
  
  .category-tree-node .category-actions .ant-btn {
    font-size: 11px;
    padding: 2px 6px;
  }
}
