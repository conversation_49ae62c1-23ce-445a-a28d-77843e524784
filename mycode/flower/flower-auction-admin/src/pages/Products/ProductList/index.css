.product-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.product-list-container .ant-typography {
  margin-bottom: 24px;
  color: #262626;
  font-weight: 600;
}

.search-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
}

.search-card .ant-card-body {
  padding: 20px;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
}

.action-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
}

.action-card .ant-card-body {
  padding: 16px 20px;
  background: #ffffff;
}

/* 表格样式 */
.product-list-container .ant-table-wrapper {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.product-list-container .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #e8e8e8;
  padding: 16px 12px;
}

.product-list-container .ant-table-tbody > tr > td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.product-list-container .ant-table-tbody > tr:hover > td {
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

.product-list-container .ant-table-tbody > tr:last-child > td {
  border-bottom: none;
}

/* 状态标签样式 */
.product-list-container .ant-tag {
  border-radius: 6px;
  font-size: 12px;
  padding: 4px 10px;
  font-weight: 500;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 操作按钮样式 */
.product-list-container .ant-btn-link {
  padding: 4px 8px;
  height: auto;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.product-list-container .ant-btn-link:hover {
  background-color: rgba(24, 144, 255, 0.06);
  transform: translateY(-1px);
}

.product-list-container .ant-btn-link.ant-btn-dangerous:hover {
  background-color: rgba(255, 77, 79, 0.06);
}

/* 开关样式 */
.product-list-container .ant-switch {
  min-width: 48px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 图片样式 */
.product-list-container .ant-image {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.product-list-container .ant-image:hover {
  transform: scale(1.05);
}

/* 表单样式 */
.product-list-container .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.product-list-container .ant-input,
.product-list-container .ant-select-selector {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
}

.product-list-container .ant-input:hover,
.product-list-container .ant-select-selector:hover {
  border-color: #40a9ff;
}

.product-list-container .ant-input:focus,
.product-list-container .ant-input-focused,
.product-list-container .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.15);
}

.product-list-container .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.product-list-container .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product-list-container .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

/* 分页样式 */
.product-list-container .ant-pagination {
  margin-top: 20px;
  text-align: right;
}

.product-list-container .ant-pagination-item {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.product-list-container .ant-pagination-item:hover {
  border-color: #1890ff;
  transform: translateY(-1px);
}

/* 加载状态样式 */
.product-list-container .ant-spin-container {
  min-height: 200px;
}

/* 模态框样式 */
.product-list-container .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px 16px;
}

.product-list-container .ant-modal-title {
  font-weight: 600;
  color: #262626;
}

.product-list-container .ant-modal-body {
  padding: 24px;
}

/* 描述列表样式 */
.product-list-container .ant-descriptions-item-label {
  font-weight: 500;
  color: #595959;
  background-color: #fafafa;
}

.product-list-container .ant-descriptions-item-content {
  color: #262626;
}

/* 上传组件样式 */
.product-list-container .ant-upload-list-picture-card .ant-upload-list-item {
  border-radius: 6px;
}

.product-list-container .ant-upload-select {
  border-radius: 6px;
  border: 2px dashed #d9d9d9;
  transition: all 0.2s ease;
}

.product-list-container .ant-upload-select:hover {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.02);
}

/* 搜索结果提示样式 */
.search-result-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.search-result-info .anticon {
  color: #1890ff;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-state-description {
  font-size: 14px;
  color: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-list-container {
    padding: 16px;
  }

  .search-card .ant-form-item {
    margin-bottom: 16px;
  }

  .action-card .ant-row {
    flex-direction: column;
    gap: 12px;
  }

  .product-list-container .ant-table-wrapper {
    overflow-x: auto;
  }
}

@media (max-width: 576px) {
  .product-list-container {
    padding: 12px;
  }

  .product-list-container .ant-typography {
    margin-bottom: 16px;
    font-size: 20px;
  }

  .search-card,
  .action-card {
    margin-bottom: 12px;
  }
}
