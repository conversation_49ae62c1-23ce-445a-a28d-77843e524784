import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  Image,
  Descriptions,
  Radio,
  Badge,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { productService } from '../../../services/productService';
import FormMessage from '../../../components/FormMessage';
import { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 审核状态枚举
export enum AuditStatus {
  PENDING = 'pending',    // 待审核
  APPROVED = 'approved',  // 已通过
  REJECTED = 'rejected',  // 已拒绝
}

// 商品数据接口
export interface AuditProduct {
  id: number;
  name: string;
  category?: { name: string };
  categoryName?: string; // 兼容字段
  description?: string;
  qualityLevel: number;
  origin: string;
  supplierName?: string;
  images?: string[];
  auditStatus: AuditStatus;
  auditReason?: string;
  auditTime?: string;
  auditorName?: string;
  createdAt: string;
  updatedAt: string;
}

// 查询参数接口
interface AuditQueryParams {
  name?: string;
  auditStatus?: AuditStatus;
  supplierName?: string;
  page: number;
  pageSize: number;
}

const ProductAudit: React.FC = () => {
  const [products, setProducts] = useState<AuditProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<AuditQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [isAuditVisible, setIsAuditVisible] = useState(false);
  const [viewingProduct, setViewingProduct] = useState<AuditProduct | null>(null);
  const [auditingProduct, setAuditingProduct] = useState<AuditProduct | null>(null);
  const [saving, setSaving] = useState(false);
  const [form] = Form.useForm();

  const {
    formError,
    formSuccess,
    setFormError,
    setFormSuccess,
    clearAllMessages
  } = useFormMessage();

  // 审核状态映射
  const auditStatusMap = {
    [AuditStatus.PENDING]: { label: '待审核', color: 'orange' },
    [AuditStatus.APPROVED]: { label: '已通过', color: 'green' },
    [AuditStatus.REJECTED]: { label: '已拒绝', color: 'red' },
  };

  // 质量等级映射
  const qualityLevelMap = {
    1: { label: '优', color: 'green' },
    2: { label: '良', color: 'blue' },
    3: { label: '中', color: 'orange' },
  };

  // 获取待审核商品列表
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await productService.getProductList(queryParams);
      if (response.success) {
        // 将Product类型转换为AuditProduct类型
        const auditProducts: AuditProduct[] = response.data.list.map((product: any) => ({
          ...product,
          auditStatus: product.auditStatus || AuditStatus.PENDING,
          auditReason: product.auditReason || '',
          auditTime: product.auditTime || '',
          auditorName: product.auditorName || '',
        }));
        setProducts(auditProducts);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取商品列表失败');
      }
    } catch (error: any) {
      message.error(error.message || '获取商品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchProducts();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 查看商品详情
  const handleView = (product: AuditProduct) => {
    setViewingProduct(product);
    setIsDetailVisible(true);
  };

  // 开始审核
  const handleStartAudit = (product: AuditProduct) => {
    setAuditingProduct(product);
    form.resetFields();
    clearAllMessages();
    setIsAuditVisible(true);
  };

  // 提交审核
  const handleSubmitAudit = async (values: any) => {
    if (!auditingProduct) return;

    setSaving(true);
    clearAllMessages();

    try {
      const response = await productService.auditProduct(
        auditingProduct.id,
        values.status,
        values.reason
      );

      const successMsg = '商品审核完成！';

      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {
        // 成功：延迟关闭模态框
        setTimeout(() => {
          setIsAuditVisible(false);
          form.resetFields();
          setAuditingProduct(null);
          clearAllMessages();
          fetchProducts();
        }, 1500);
      }
    } catch (error: any) {
      handleApiError(error, setFormError);
    } finally {
      setSaving(false);
    }
  };

  // 快速审核通过
  const handleQuickApprove = async (product: AuditProduct) => {
    try {
      const response = await productService.auditProduct(product.id, 'approved');
      if (response.success) {
        message.success('审核通过');
        fetchProducts();
      } else {
        message.error(response.message || '审核失败');
      }
    } catch (error: any) {
      message.error(error.message || '审核失败');
    }
  };

  // 快速审核拒绝
  const handleQuickReject = (product: AuditProduct) => {
    Modal.confirm({
      title: '确认拒绝',
      icon: <ExclamationCircleOutlined />,
      content: '确定要拒绝这个商品吗？请输入拒绝原因：',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        // 这里应该弹出一个输入框让用户输入拒绝原因
        // 为了简化，这里直接使用默认原因
        try {
          const response = await productService.auditProduct(
            product.id,
            'rejected',
            '商品信息不符合要求'
          );
          if (response.success) {
            message.success('审核拒绝');
            fetchProducts();
          } else {
            message.error(response.message || '审核失败');
          }
        } catch (error: any) {
          message.error(error.message || '审核失败');
        }
      },
    });
  };

  // 表格列定义
  const columns: ColumnsType<AuditProduct> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '商品图片',
      dataIndex: 'images',
      key: 'images',
      width: 100,
      render: (images: string[]) => (
        images && images.length > 0 ? (
          <Image
            width={60}
            height={60}
            src={images[0]}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            无图片
          </div>
        )
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string) => (
        <div style={{ fontWeight: 500 }}>{text}</div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category: any, record: AuditProduct) => (
        <Tag color="blue">{category?.name || record.categoryName || '未分类'}</Tag>
      ),
    },
    {
      title: '质量等级',
      dataIndex: 'qualityLevel',
      key: 'qualityLevel',
      width: 100,
      render: (level: number) => {
        const levelInfo = qualityLevelMap[level as keyof typeof qualityLevelMap];
        return (
          <Tag color={levelInfo?.color || 'default'}>
            {levelInfo?.label || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '产地',
      dataIndex: 'origin',
      key: 'origin',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 120,
      render: (text: string) => <Tag color="green">{text || '未知供应商'}</Tag>,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      width: 100,
      render: (status: AuditStatus) => {
        const statusInfo = auditStatusMap[status];
        return (
          <Badge
            status={status === AuditStatus.PENDING ? 'processing' : status === AuditStatus.APPROVED ? 'success' : 'error'}
            text={
              <Tag color={statusInfo?.color || 'default'}>
                {statusInfo?.label || '未知'}
              </Tag>
            }
          />
        );
      },
    },
    {
      title: '提交时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record: AuditProduct) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          {record.auditStatus === AuditStatus.PENDING && (
            <>
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleQuickApprove(record)}
              >
                通过
              </Button>
              <Button
                type="link"
                size="small"
                icon={<CloseOutlined />}
                danger
                onClick={() => handleQuickReject(record)}
              >
                拒绝
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => handleStartAudit(record)}
              >
                详细审核
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="product-audit-container">
      <Title level={2}>商品审核</Title>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="name" label="商品名称">
                <Input placeholder="请输入商品名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="auditStatus" label="审核状态">
                <Select placeholder="请选择审核状态" allowClear>
                  <Option value={AuditStatus.PENDING}>待审核</Option>
                  <Option value={AuditStatus.APPROVED}>已通过</Option>
                  <Option value={AuditStatus.REJECTED}>已拒绝</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="supplierName" label="供应商">
                <Input placeholder="请输入供应商名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Badge count={products.filter(p => p.auditStatus === AuditStatus.PENDING).length}>
                <Button>待审核商品</Button>
              </Badge>
            </Space>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchProducts}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 商品列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>

      {/* 商品详情模态框 */}
      <Modal
        title="商品详情"
        open={isDetailVisible}
        onCancel={() => setIsDetailVisible(false)}
        footer={null}
        width={800}
      >
        {viewingProduct && (
          <div>
            <Descriptions title="基本信息" column={2} size="small">
              <Descriptions.Item label="商品名称">{viewingProduct.name}</Descriptions.Item>
              <Descriptions.Item label="商品分类">{viewingProduct.category?.name || viewingProduct.categoryName || '未分类'}</Descriptions.Item>
              <Descriptions.Item label="质量等级">
                <Tag color={qualityLevelMap[viewingProduct.qualityLevel as keyof typeof qualityLevelMap]?.color}>
                  {qualityLevelMap[viewingProduct.qualityLevel as keyof typeof qualityLevelMap]?.label}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="产地">{viewingProduct.origin}</Descriptions.Item>
              <Descriptions.Item label="供应商">{viewingProduct.supplierName || '未知供应商'}</Descriptions.Item>
              <Descriptions.Item label="审核状态">
                <Tag color={auditStatusMap[viewingProduct.auditStatus]?.color}>
                  {auditStatusMap[viewingProduct.auditStatus]?.label}
                </Tag>
              </Descriptions.Item>
            </Descriptions>

            {viewingProduct.description && (
              <Descriptions title="商品描述" column={1} size="small" style={{ marginTop: 24 }}>
                <Descriptions.Item label="描述">{viewingProduct.description}</Descriptions.Item>
              </Descriptions>
            )}

            {viewingProduct.images && viewingProduct.images.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <h4>商品图片</h4>
                <Space wrap>
                  {viewingProduct.images.map((image, index) => (
                    <Image
                      key={index}
                      width={100}
                      height={100}
                      src={image}
                      style={{ objectFit: 'cover', borderRadius: 4 }}
                    />
                  ))}
                </Space>
              </div>
            )}

            {viewingProduct.auditReason && (
              <Descriptions title="审核信息" column={1} size="small" style={{ marginTop: 24 }}>
                <Descriptions.Item label="审核原因">{viewingProduct.auditReason}</Descriptions.Item>
                {viewingProduct.auditTime && (
                  <Descriptions.Item label="审核时间">
                    {new Date(viewingProduct.auditTime).toLocaleString()}
                  </Descriptions.Item>
                )}
                {viewingProduct.auditorName && (
                  <Descriptions.Item label="审核人">{viewingProduct.auditorName}</Descriptions.Item>
                )}
              </Descriptions>
            )}
          </div>
        )}
      </Modal>

      {/* 审核模态框 */}
      <Modal
        title="商品审核"
        open={isAuditVisible}
        onCancel={() => setIsAuditVisible(false)}
        footer={null}
        width={600}
      >
        {auditingProduct && (
          <div>
            <Descriptions title="商品信息" column={2} size="small" style={{ marginBottom: 24 }}>
              <Descriptions.Item label="商品名称">{auditingProduct.name}</Descriptions.Item>
              <Descriptions.Item label="供应商">{auditingProduct.supplierName || '未知供应商'}</Descriptions.Item>
            </Descriptions>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmitAudit}
              autoComplete="off"
            >
              <Form.Item
                name="status"
                label="审核结果"
                rules={[{ required: true, message: '请选择审核结果' }]}
              >
                <Radio.Group>
                  <Radio value="approved">通过</Radio>
                  <Radio value="rejected">拒绝</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                name="reason"
                label="审核意见"
                rules={[
                  { required: true, message: '请输入审核意见' },
                  { max: 500, message: '审核意见不能超过500个字符' },
                ]}
              >
                <TextArea
                  placeholder="请输入审核意见"
                  rows={4}
                  showCount
                  maxLength={500}
                />
              </Form.Item>

              {/* 错误和成功消息显示 */}
              <FormMessage type="error" message={formError} visible={!!formError} />
              <FormMessage type="success" message={formSuccess} visible={!!formSuccess} />

              <Form.Item>
                <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
                  <Button
                    onClick={() => {
                      setIsAuditVisible(false);
                      form.resetFields();
                      setAuditingProduct(null);
                      clearAllMessages();
                    }}
                    disabled={saving}
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    disabled={saving}
                  >
                    {saving ? '提交中...' : '提交审核'}
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ProductAudit;