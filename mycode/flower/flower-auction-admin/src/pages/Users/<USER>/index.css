.role-management-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.role-management-container .ant-typography {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 16px;
}

.search-card .ant-card-body {
  padding: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-card .ant-card-body {
  padding: 12px 16px;
}

/* 表格样式 */
.role-management-container .ant-table-wrapper {
  background: white;
  border-radius: 6px;
}

.role-management-container .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.role-management-container .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 标签样式 */
.role-management-container .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 操作按钮样式 */
.role-management-container .ant-btn-link {
  padding: 0;
  height: auto;
}

/* 模态框样式 */
.role-management-container .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.role-management-container .ant-modal-body {
  padding: 24px;
}

/* 表单样式 */
.role-management-container .ant-form-item-label > label {
  font-weight: 500;
}

.role-management-container .ant-input,
.role-management-container .ant-select-selector {
  border-radius: 4px;
}

.role-management-container .ant-input:focus,
.role-management-container .ant-input-focused,
.role-management-container .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 权限树样式 */
.role-management-container .ant-tree {
  background: transparent;
}

.role-management-container .ant-tree .ant-tree-node-content-wrapper {
  border-radius: 4px;
}

.role-management-container .ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: #f5f5f5;
}

.role-management-container .ant-tree .ant-tree-node-selected .ant-tree-node-content-wrapper {
  background-color: #e6f7ff;
}

/* 描述列表样式 */
.role-management-container .ant-descriptions-item-label {
  font-weight: 500;
  color: #666;
}

.role-management-container .ant-descriptions-item-content {
  color: #333;
}

/* 分页样式 */
.role-management-container .ant-pagination {
  margin-top: 16px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .role-management-container {
    padding: 16px;
  }
  
  .search-card .ant-form-item {
    margin-bottom: 16px;
  }
  
  .action-card .ant-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .role-management-container .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .role-management-container .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
}

@media (max-width: 576px) {
  .role-management-container {
    padding: 12px;
  }
  
  .role-management-container .ant-typography {
    margin-bottom: 16px;
    font-size: 20px;
  }
  
  .search-card,
  .action-card {
    margin-bottom: 12px;
  }
  
  .role-management-container .ant-modal-body {
    padding: 16px;
  }
}
