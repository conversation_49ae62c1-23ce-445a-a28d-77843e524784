import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Tree,
  Switch,
  Tag,
  Descriptions,
  Select,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  ReloadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { DataNode } from 'antd/es/tree';
import { roleService } from '../../../services/roleService';
// import { handleError, handleSuccess } from '../../../utils/errorHandler';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 角色数据接口
export interface Role {
  id: number;
  name: string;
  code: string;
  description?: string;
  status: number;
  userCount: number;
  permissions: number[];
  createdAt: string;
  updatedAt: string;
}

// 权限数据接口
export interface Permission {
  id: number;
  name: string;
  code: string;
  type: 'menu' | 'button' | 'api';
  parentId?: number;
  path?: string;
  children?: Permission[];
}

// 查询参数接口
interface RoleQueryParams {
  name?: string;
  code?: string;
  status?: number;
  page: number;
  pageSize: number;
}

const RoleManagement: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<RoleQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [checkedPermissions, setCheckedPermissions] = useState<number[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [saving, setSaving] = useState(false);
  const [formError, setFormError] = useState<string>('');
  const [formSuccess, setFormSuccess] = useState<string>('');
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // 预定义角色编码选项
  const roleCodeOptions = [
    { value: 'ADMIN', label: '管理员 (ADMIN)', description: '系统管理员，拥有所有权限' },
    { value: 'AUCTIONEER', label: '拍卖师 (AUCTIONEER)', description: '负责拍卖活动的管理和执行' },
    { value: 'BUYER', label: '买家 (BUYER)', description: '参与拍卖的买家用户' },
    { value: 'SELLER', label: '卖家 (SELLER)', description: '提供商品的卖家用户' },
    { value: 'QUALITY_INSPECTOR', label: '质检员 (QUALITY_INSPECTOR)', description: '负责商品质量检查' },
    { value: 'FINANCE', label: '财务 (FINANCE)', description: '负责财务管理和结算' },
    { value: 'CUSTOMER_SERVICE', label: '客服 (CUSTOMER_SERVICE)', description: '负责客户服务和支持' },
    { value: 'OPERATOR', label: '运营 (OPERATOR)', description: '负责平台运营管理' },
  ];

  // 获取角色列表
  const fetchRoles = async () => {
    setLoading(true);
    try {
      const response = await roleService.getRoleList(queryParams);
      console.log('角色列表响应:', response); // 调试日志

      if (response.success) {
        // 处理后端返回的数据
        const roleData = response.data;
        let roleList: any[] = [];
        let totalCount = 0;

        // 兼容不同的数据结构
        if (Array.isArray(roleData)) {
          // 如果直接返回数组
          roleList = roleData;
          totalCount = roleData.length;
        } else if (roleData && roleData.list) {
          // 如果返回对象包含list字段
          roleList = roleData.list;
          totalCount = roleData.total || roleData.list.length;
        } else {
          console.warn('未知的数据结构:', roleData);
          roleList = [];
          totalCount = 0;
        }

        // 处理角色数据，添加缺失的字段
        const processedRoles = roleList.map((role: any) => ({
          ...role,
          userCount: role.userCount || role.user_count || 0,
          permissions: role.permissions || [],
          createdAt: role.createdAt || role.created_at,
          updatedAt: role.updatedAt || role.updated_at,
        }));

        console.log('处理后的角色数据:', processedRoles); // 调试日志
        setRoles(processedRoles);
        setTotal(totalCount);
      } else {
        console.error('获取角色列表失败:', response.message);
        message.error(response.message || '获取角色列表失败');
        setRoles([]);
        setTotal(0);
      }
    } catch (error: any) {
      console.error('获取角色列表异常:', error);
      let errorMsg = '获取角色列表失败';
      if (error.response) {
        const { status } = error.response;
        console.error('HTTP错误状态:', status, error.response.data);
        if (status === 401) {
          errorMsg = '登录已过期，请重新登录';
        } else if (status === 403) {
          errorMsg = '没有权限访问角色列表';
        } else if (status === 500) {
          errorMsg = '服务器内部错误，请稍后重试';
        }
      }
      message.error(errorMsg);
      setRoles([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      const response = await roleService.getPermissionList();
      console.log('权限列表响应:', response); // 调试日志

      if (response.success) {
        // 处理后端返回的权限数据，转换为前端期望的格式
        const rawData = response.data as any;
        let permissionList = [];

        // 兼容不同的数据结构
        if (Array.isArray(rawData)) {
          permissionList = rawData;
        } else if (rawData && rawData.list) {
          permissionList = rawData.list;
        } else {
          console.warn('权限数据结构未知:', rawData);
          permissionList = [];
        }

        const processedPermissions = permissionList.map((permission: any) => ({
          id: permission.id,
          name: permission.name,
          code: permission.code,
          type: permission.type || 'menu',
          parentId: permission.parent_id || permission.parentId,
          path: permission.path,
          children: [],
        }));

        // 构建树形结构
        const permissionTree = buildPermissionTree(processedPermissions);
        console.log('处理后的权限树:', permissionTree); // 调试日志
        setPermissions(permissionTree);
      } else {
        console.warn('获取权限列表失败:', response.message);
        // 权限获取失败不显示错误消息，因为这不是关键功能
        setPermissions([]);
      }
    } catch (error: any) {
      console.error('获取权限列表异常:', error);
      // 权限获取失败不显示错误消息，因为这不是关键功能
      setPermissions([]);
    }
  };

  // 构建权限树形结构
  const buildPermissionTree = (permissions: any[]): Permission[] => {
    const map = new Map();
    const roots: Permission[] = [];

    // 创建映射
    permissions.forEach(permission => {
      map.set(permission.id, { ...permission, children: [] });
    });

    // 构建树形结构
    permissions.forEach(permission => {
      const node = map.get(permission.id);
      if (permission.parentId && map.has(permission.parentId)) {
        map.get(permission.parentId).children.push(node);
      } else {
        roots.push(node);
      }
    });

    return roots;
  };

  // 初始化加载权限列表（只加载一次）
  useEffect(() => {
    fetchPermissions();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 监听查询参数变化，重新获取角色列表
  useEffect(() => {
    fetchRoles();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增角色
  const handleAdd = () => {
    setEditingRole(null);
    form.resetFields();
    setFormError('');
    setFormSuccess('');
    setIsModalVisible(true);
  };

  // 编辑角色
  const handleEdit = (role: Role) => {
    setEditingRole(role);
    form.setFieldsValue({
      ...role,
      status: role.status === 1,
    });
    setFormError('');
    setFormSuccess('');
    setIsModalVisible(true);
  };

  // 删除角色
  const handleDelete = async (id: number) => {
    try {
      const response = await roleService.deleteRole(id);
      if (response.success) {
        message.success('删除成功');
        fetchRoles();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存角色
  const handleSave = async (values: any) => {
    setSaving(true);
    setFormError('');
    setFormSuccess('');

    try {
      const roleData = {
        ...values,
        status: values.status ? 1 : 0,
      };

      let response;
      if (editingRole) {
        response = await roleService.updateRole(editingRole.id, roleData);
      } else {
        response = await roleService.createRole(roleData);
      }

      if (response.success) {
        const successMsg = editingRole ? '角色信息更新成功！' : '角色创建成功！';
        setFormSuccess(successMsg);

        // 延迟关闭模态框，让用户看到成功消息
        setTimeout(() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingRole(null);
          setFormSuccess('');
          fetchRoles();
        }, 1500);
      } else {
        const errorMsg = response.message || '操作失败，请稍后重试';
        setFormError(errorMsg);
      }
    } catch (error: any) {
      console.error('保存角色失败:', error);
      const errorMsg = error.response?.data?.message || error.message || '网络错误，请检查连接后重试';
      setFormError(errorMsg);
    } finally {
      setSaving(false);
    }
  };

  // 配置权限
  const handleConfigPermissions = async (role: Role) => {
    setSelectedRole(role);
    setIsPermissionModalVisible(true);

    // 获取角色的当前权限
    try {
      const response = await roleService.getRolePermissions(role.id);
      if (response.success) {
        console.log('获取到的角色权限:', response.data); // 调试日志
        setCheckedPermissions(response.data || []);
      } else {
        console.warn('获取角色权限失败:', response.message);
        // 如果获取失败，使用角色对象中的权限数据作为备选
        setCheckedPermissions(role.permissions || []);
      }
    } catch (error: any) {
      console.error('获取角色权限异常:', error);
      // 如果获取失败，使用角色对象中的权限数据作为备选
      setCheckedPermissions(role.permissions || []);
      message.warning('获取当前权限配置失败，显示可能不是最新数据');
    }
  };

  // 保存权限配置
  const handleSavePermissions = async () => {
    if (!selectedRole) return;

    try {
      const response = await roleService.updateRolePermissions(
        selectedRole.id,
        checkedPermissions
      );
      if (response.success) {
        message.success({
          content: `角色"${selectedRole.name}"权限配置成功，已分配${checkedPermissions.length}个权限`,
          duration: 3,
        });
        setIsPermissionModalVisible(false);
        setSelectedRole(null);
        setCheckedPermissions([]);
        fetchRoles(); // 刷新角色列表
      } else {
        message.error({
          content: response.message || '权限配置失败，请稍后重试',
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('权限配置失败:', error);
      message.error({
        content: error.message || '权限配置失败，请检查网络连接后重试',
        duration: 5,
      });
    }
  };

  // 转换权限数据为树形结构
  const convertPermissionsToTreeData = (permissions: Permission[]): DataNode[] => {
    return permissions.map(permission => ({
      title: permission.name,
      key: permission.id,
      children: permission.children ? convertPermissionsToTreeData(permission.children) : undefined,
    }));
  };

  // 权限树选择处理
  const handlePermissionCheck = (checkedKeys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    if (Array.isArray(checkedKeys)) {
      setCheckedPermissions(checkedKeys as number[]);
    } else {
      setCheckedPermissions(checkedKeys.checked as number[]);
    }
  };

  // 表格列定义
  const columns: ColumnsType<Role> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      ellipsis: true,
    },
    {
      title: '角色编码',
      dataIndex: 'code',
      key: 'code',
      width: 160,
      ellipsis: true,
      render: (text: string) => (
        <Tag color="blue" style={{ maxWidth: '140px', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {text}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: {
        showTitle: true,
      },
      render: (text: string) => text || '-',
    },
    {
      title: '用户数量',
      dataIndex: 'userCount',
      key: 'userCount',
      width: 100,
      render: (count: number) => (
        <Space>
          <UserOutlined />
          {count}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record: Role) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<SettingOutlined />}
            onClick={() => handleConfigPermissions(record)}
          >
            配置权限
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个角色吗？"
            description="删除后该角色下的用户将失去相应权限"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              disabled={record.userCount > 0}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="role-management-container">
      <Title level={2}>角色管理</Title>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="name" label="角色名称">
                <Input placeholder="请输入角色名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="code" label="角色编码">
                <Select
                  placeholder="请选择角色编码"
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    String(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {roleCodeOptions.map(option => (
                    <Option key={option.value} value={option.value} label={option.label}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="状态">
                <Select placeholder="请选择状态" allowClear>
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增角色
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchRoles}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 角色列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>

      {/* 角色编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '新增角色'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色名称"
                rules={[
                  { required: true, message: '请输入角色名称' },
                  { min: 2, max: 50, message: '角色名称长度为2-50个字符' },
                ]}
              >
                <Input
                  placeholder="请输入角色名称"
                  showCount
                  maxLength={50}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="角色编码"
                rules={[
                  { required: true, message: '请选择角色编码' },
                ]}
                extra={editingRole ? "编辑时不能修改角色编码" : "请选择预定义的角色编码"}
              >
                <Select
                  placeholder="请选择角色编码"
                  disabled={!!editingRole}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    String(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  notFoundContent="没有找到匹配的角色编码"
                  optionLabelProp="label"
                >
                  {roleCodeOptions.map(option => (
                    <Option key={option.value} value={option.value} label={option.label}>
                      <div>
                        <div style={{ fontWeight: 'bold' }}>{option.label}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>{option.description}</div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="角色描述"
            rules={[
              { max: 200, message: '角色描述不能超过200个字符' },
            ]}
          >
            <TextArea
              placeholder="请输入角色描述"
              rows={4}
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          {/* 错误消息显示 */}
          {formError && (
            <Form.Item>
              <div style={{
                padding: '12px 16px',
                backgroundColor: '#fff2f0',
                border: '1px solid #ffccc7',
                borderRadius: '6px',
                color: '#ff4d4f',
                fontSize: '14px',
                lineHeight: '1.5'
              }}>
                <strong>❌ 操作失败：</strong>{formError}
              </div>
            </Form.Item>
          )}

          {/* 成功消息显示 */}
          {formSuccess && (
            <Form.Item>
              <div style={{
                padding: '12px 16px',
                backgroundColor: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: '6px',
                color: '#52c41a',
                fontSize: '14px',
                lineHeight: '1.5'
              }}>
                <strong>✅ 操作成功：</strong>{formSuccess}
              </div>
            </Form.Item>
          )}

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                  setEditingRole(null);
                  setFormError('');
                  setFormSuccess('');
                }}
                disabled={saving}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={saving}
                disabled={saving}
              >
                {saving ? '保存中...' : (editingRole ? '更新' : '创建')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 权限配置模态框 */}
      <Modal
        title={`配置权限 - ${selectedRole?.name}`}
        open={isPermissionModalVisible}
        onCancel={() => setIsPermissionModalVisible(false)}
        onOk={handleSavePermissions}
        width={800}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Descriptions size="small" column={2}>
            <Descriptions.Item label="角色名称">{selectedRole?.name}</Descriptions.Item>
            <Descriptions.Item label="角色编码">{selectedRole?.code}</Descriptions.Item>
            <Descriptions.Item label="用户数量">{selectedRole?.userCount}</Descriptions.Item>
            <Descriptions.Item label="当前权限数量">{checkedPermissions.length}</Descriptions.Item>
          </Descriptions>
        </div>

        <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16, maxHeight: 400, overflow: 'auto' }}>
          <Tree
            checkable
            checkedKeys={checkedPermissions}
            expandedKeys={expandedKeys}
            onCheck={handlePermissionCheck}
            onExpand={setExpandedKeys}
            treeData={convertPermissionsToTreeData(permissions)}
            height={350}
          />
        </div>
      </Modal>
    </div>
  );
};

export default RoleManagement;