import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  DatePicker,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  ExportOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { userService } from '../../../services/userService';
// import { handleError, handleSuccess } from '../../../utils/errorHandler';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 用户类型枚举
export enum UserType {
  AUCTIONEER = 1, // 拍卖师
  BUYER = 2,      // 买家
  ADMIN = 3,      // 管理员
  QUALITY_INSPECTOR = 4, // 质检员
}

// 用户状态枚举
export enum UserStatus {
  DISABLED = 0, // 禁用
  ENABLED = 1,  // 启用
}

// 用户数据接口
export interface User {
  id: number;
  username: string;
  realName?: string;
  phone: string;
  email?: string;
  userType: UserType;
  status: UserStatus;
  createdAt: string;
  updatedAt: string;
}

// 查询参数接口
interface UserQueryParams {
  username?: string;
  phone?: string;
  userType?: UserType;
  status?: UserStatus;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

const UserList: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<UserQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchLoading, setBatchLoading] = useState(false);
  const [formError, setFormError] = useState<string>('');
  const [formSuccess, setFormSuccess] = useState<string>('');

  // 用户类型映射
  const userTypeMap = {
    [UserType.AUCTIONEER]: { label: '拍卖师', color: 'blue' },
    [UserType.BUYER]: { label: '买家', color: 'green' },
    [UserType.ADMIN]: { label: '管理员', color: 'red' },
    [UserType.QUALITY_INSPECTOR]: { label: '质检员', color: 'orange' },
  };

  // 获取用户列表
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await userService.getUserList(queryParams);
      if (response.success) {
        setUsers(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取用户列表失败');
        setUsers([]);
        setTotal(0);
      }
    } catch (error: any) {
      message.error(error.message || '获取用户列表失败');
      setUsers([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchUsers();
  }, [queryParams]);



  // 保存用户
  const handleSave = async (values: any) => {
    setSaving(true);
    setFormError('');
    setFormSuccess('');

    try {
      const userData = {
        ...values,
        status: values.status ? UserStatus.ENABLED : UserStatus.DISABLED,
      };

      // 删除确认密码字段，后端不需要
      delete userData.confirmPassword;

      let response;
      if (editingUser) {
        response = await userService.updateUser(editingUser.id, userData);
      } else {
        response = await userService.createUser(userData);
      }

      if (response.success) {
        const successMsg = editingUser ? '用户信息更新成功！' : '用户创建成功！';
        setFormSuccess(successMsg);

        // 延迟关闭模态框，让用户看到成功消息
        setTimeout(() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingUser(null);
          setFormSuccess('');
          fetchUsers();
        }, 1500);
      } else {
        const errorMsg = response.message || '操作失败，请稍后重试';
        setFormError(errorMsg);
      }
    } catch (error: any) {
      console.error('保存用户失败:', error);
      const errorMsg = error.response?.data?.message || error.message || '网络错误，请检查连接后重试';
      setFormError(errorMsg);
    } finally {
      setSaving(false);
    }
  };

  // 切换用户状态
  const handleToggleStatus = async (user: User) => {
    try {
      const newStatus = user.status === UserStatus.ENABLED ? UserStatus.DISABLED : UserStatus.ENABLED;
      const statusText = newStatus === UserStatus.ENABLED ? '启用' : '禁用';

      const response = await userService.updateUserStatus(user.id, newStatus);
      if (response.success) {
        message.success({
          content: `用户"${user.username}"已成功${statusText}`,
          duration: 3,
        });
        fetchUsers(); // 刷新列表
      } else {
        message.error({
          content: response.message || `${statusText}失败，请稍后重试`,
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('更新用户状态失败:', error);
      message.error({
        content: error.message || '状态更新失败，请检查网络连接后重试',
        duration: 5,
      });
    }
  };

  // 打开新增用户模态框
  const handleAdd = () => {
    setEditingUser(null);
    setIsModalVisible(true);
    form.resetFields();
    setFormError('');
    setFormSuccess('');
    // 设置默认值
    form.setFieldsValue({
      status: true, // 默认启用
      userType: UserType.BUYER, // 默认买家
    });
  };

  // 编辑用户
  const handleEdit = (user: User) => {
    setEditingUser(user);
    setIsModalVisible(true);
    setFormError('');
    setFormSuccess('');
    form.setFieldsValue({
      ...user,
      status: user.status === UserStatus.ENABLED,
    });
  };

  // 删除用户
  const handleDelete = async (userId: number) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    try {
      const response = await userService.deleteUser(userId);
      if (response.success) {
        message.success({
          content: `用户"${user.username}"删除成功`,
          duration: 3,
        });
        fetchUsers(); // 刷新列表
      } else {
        message.error({
          content: response.message || '删除失败，请稍后重试',
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('删除用户失败:', error);
      message.error({
        content: error.message || '删除失败，请检查网络连接后重试',
        duration: 5,
      });
    }
  };

  // 搜索用户
  const handleSearch = (values: any) => {
    const newParams = {
      ...queryParams,
      page: 1, // 重置到第一页
      ...values,
    };

    // 处理日期范围
    if (values.dateRange && values.dateRange.length === 2) {
      newParams.startDate = values.dateRange[0].format('YYYY-MM-DD');
      newParams.endDate = values.dateRange[1].format('YYYY-MM-DD');
      delete newParams.dateRange; // 删除原始的dateRange字段
    } else {
      // 如果没有选择日期范围，清除之前的日期筛选
      delete newParams.startDate;
      delete newParams.endDate;
      delete newParams.dateRange;
    }

    setQueryParams(newParams);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    const resetParams = {
      page: 1,
      pageSize: 10,
    };
    setQueryParams(resetParams);
  };

  // 导出用户数据
  const handleExport = async () => {
    try {
      message.loading('正在导出数据，请稍候...', 0);

      // 使用userService导出数据
      const response = await userService.exportUsers(queryParams);

      if (response.success) {
        const blob = response.data;
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 生成文件名
        const filename = `用户列表_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        message.destroy();
        message.success('导出成功！');
      } else {
        message.destroy();
        message.error('导出失败，请稍后重试');
      }
    } catch (error: any) {
      message.destroy();
      message.error(error.message || '导出失败');
    }
  };

  // 批量启用用户
  const handleBatchEnable = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要启用的用户');
      return;
    }

    setBatchLoading(true);
    try {
      const promises = selectedRowKeys.map(id =>
        userService.updateUserStatus(Number(id), UserStatus.ENABLED)
      );
      await Promise.all(promises);
      message.success(`成功启用 ${selectedRowKeys.length} 个用户`);
      setSelectedRowKeys([]);
      fetchUsers();
    } catch (error: any) {
      message.error(error.message || '批量启用失败');
    } finally {
      setBatchLoading(false);
    }
  };

  // 批量禁用用户
  const handleBatchDisable = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要禁用的用户');
      return;
    }

    setBatchLoading(true);
    try {
      const promises = selectedRowKeys.map(id =>
        userService.updateUserStatus(Number(id), UserStatus.DISABLED)
      );
      await Promise.all(promises);
      message.success(`成功禁用 ${selectedRowKeys.length} 个用户`);
      setSelectedRowKeys([]);
      fetchUsers();
    } catch (error: any) {
      message.error(error.message || '批量禁用失败');
    } finally {
      setBatchLoading(false);
    }
  };

  // 批量删除用户
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的用户');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个用户吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        setBatchLoading(true);
        try {
          await userService.batchDeleteUsers(selectedRowKeys.map(id => Number(id)));
          message.success(`成功删除 ${selectedRowKeys.length} 个用户`);
          setSelectedRowKeys([]);
          fetchUsers();
        } catch (error: any) {
          message.error(error.message || '批量删除失败');
        } finally {
          setBatchLoading(false);
        }
      },
    });
  };

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      ellipsis: true,
      render: (text: string) => (
        <Space>
          <UserOutlined />
          <span title={text}>{text}</span>
        </Space>
      ),
    },
    {
      title: '真实姓名',
      dataIndex: 'realName',
      key: 'realName',
      width: 100,
      ellipsis: true,
      render: (text: string) => <span title={text || '-'}>{text || '-'}</span>,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      ellipsis: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      ellipsis: {
        showTitle: true,
      },
      render: (text: string) => text || '-',
    },
    {
      title: '用户类型',
      dataIndex: 'userType',
      key: 'userType',
      width: 100,
      render: (userType: UserType) => {
        const typeInfo = userTypeMap[userType];
        return (
          <Tag color={typeInfo?.color || 'default'}>
            {typeInfo?.label || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: UserStatus, record: User) => (
        <Switch
          checked={status === UserStatus.ENABLED}
          onChange={() => handleToggleStatus(record)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: User) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="user-list-container">
      <Title level={2}>用户管理</Title>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="username" label="用户名">
                <Input placeholder="请输入用户名" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="phone" label="手机号">
                <Input placeholder="请输入手机号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="userType" label="用户类型">
                <Select placeholder="请选择用户类型" allowClear>
                  <Option value={UserType.AUCTIONEER}>拍卖师</Option>
                  <Option value={UserType.BUYER}>买家</Option>
                  <Option value={UserType.ADMIN}>管理员</Option>
                  <Option value={UserType.QUALITY_INSPECTOR}>质检员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="状态">
                <Select placeholder="请选择状态" allowClear>
                  <Option value={UserStatus.ENABLED}>启用</Option>
                  <Option value={UserStatus.DISABLED}>禁用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item name="dateRange" label="创建时间">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                新增用户
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={handleExport}
              >
                导出数据
              </Button>
            </Space>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchUsers}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 用户列表表格 */}
      <Card>
        {/* 批量操作工具栏 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16, padding: '8px 16px', backgroundColor: '#f0f2f5', borderRadius: 6 }}>
            <Space>
              <span>已选择 {selectedRowKeys.length} 项</span>
              <Button
                type="primary"
                size="small"
                loading={batchLoading}
                onClick={handleBatchEnable}
              >
                批量启用
              </Button>
              <Button
                size="small"
                loading={batchLoading}
                onClick={handleBatchDisable}
              >
                批量禁用
              </Button>
              <Button
                danger
                size="small"
                loading={batchLoading}
                onClick={handleBatchDelete}
              >
                批量删除
              </Button>
              <Button
                size="small"
                onClick={() => setSelectedRowKeys([])}
              >
                取消选择
              </Button>
            </Space>
          </div>
        )}

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            getCheckboxProps: (record: User) => ({
              disabled: record.userType === UserType.ADMIN, // 禁止选择管理员用户
            }),
          }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>

      {/* 用户编辑模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '新增用户'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setEditingUser(null);
        }}
        footer={null}
        width={600}
        destroyOnClose
        maskClosable={false}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, max: 20, message: '用户名长度为3-20个字符' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
                ]}
                extra={!editingUser ? "用户名创建后不可修改，请谨慎填写" : undefined}
              >
                <Input
                  placeholder="请输入用户名（3-20个字符）"
                  disabled={!!editingUser}
                  showCount
                  maxLength={20}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="realName"
                label="真实姓名"
                rules={[
                  { required: true, message: '请输入真实姓名' },
                  { max: 20, message: '真实姓名不能超过20个字符' },
                  { pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/, message: '真实姓名只能包含中文、英文和空格' },
                ]}
              >
                <Input
                  placeholder="请输入真实姓名"
                  showCount
                  maxLength={20}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号码' },
                ]}
              >
                <Input
                  placeholder="请输入11位手机号码"
                  maxLength={11}
                  addonBefore="+86"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入正确的邮箱地址' },
                  { max: 50, message: '邮箱长度不能超过50个字符' },
                ]}
              >
                <Input
                  placeholder="请输入邮箱地址"
                  type="email"
                  maxLength={50}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="userType"
                label="用户类型"
                rules={[{ required: true, message: '请选择用户类型' }]}
              >
                <Select placeholder="请选择用户类型">
                  <Option value={UserType.AUCTIONEER}>拍卖师</Option>
                  <Option value={UserType.BUYER}>买家</Option>
                  <Option value={UserType.ADMIN}>管理员</Option>
                  <Option value={UserType.QUALITY_INSPECTOR}>质检员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="password"
                  label="密码"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, max: 32, message: '密码长度为6-32个字符' },
                    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字' },
                  ]}
                  extra="密码需包含字母和数字，长度6-32位"
                >
                  <Input.Password
                    placeholder="请输入密码（6-32位，包含字母和数字）"
                    showCount
                    maxLength={32}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="confirmPassword"
                  label="确认密码"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致，请重新输入'));
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    placeholder="请再次输入密码确认"
                    maxLength={32}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}

          {/* 错误消息显示 */}
          {formError && (
            <Form.Item>
              <div style={{
                padding: '12px 16px',
                backgroundColor: '#fff2f0',
                border: '1px solid #ffccc7',
                borderRadius: '6px',
                color: '#ff4d4f',
                fontSize: '14px',
                lineHeight: '1.5'
              }}>
                <strong>❌ 操作失败：</strong>{formError}
              </div>
            </Form.Item>
          )}

          {/* 成功消息显示 */}
          {formSuccess && (
            <Form.Item>
              <div style={{
                padding: '12px 16px',
                backgroundColor: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: '6px',
                color: '#52c41a',
                fontSize: '14px',
                lineHeight: '1.5'
              }}>
                <strong>✅ 操作成功：</strong>{formSuccess}
              </div>
            </Form.Item>
          )}

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                  setEditingUser(null);
                  setFormError('');
                  setFormSuccess('');
                }}
                disabled={saving}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={saving}
                disabled={saving}
              >
                {saving ? '保存中...' : (editingUser ? '更新' : '创建')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserList;