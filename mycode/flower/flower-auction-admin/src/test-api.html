<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>API连接测试</h1>
    <button onclick="testAPI()">测试拍卖商品API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            try {
                // 从环境变量获取API地址，如果没有则使用默认值
                const apiBaseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api/v1';
                const response = await fetch(`${apiBaseUrl}/auction-items?page=1&pageSize=5`);
                const data = await response.json();
                
                document.getElementById('result').innerHTML = `
                    <h2>API响应:</h2>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                console.log('API响应:', data);
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h2>错误:</h2>
                    <p>${error.message}</p>
                `;
                console.error('API调用失败:', error);
            }
        }
    </script>
</body>
</html>
