// 在浏览器控制台中运行这个脚本来调试角色创建错误
console.log('🔍 开始调试角色创建错误处理...');

// 模拟创建重复角色
async function testRoleCreation() {
  try {
    console.log('📤 发送角色创建请求...');
    
    const apiBaseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1';
    const response = await fetch(`${apiBaseUrl}/roles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: '系统管理员',
        code: 'ADMIN',
        description: '测试重复角色',
        status: 1
      })
    });

    console.log('📥 HTTP响应状态:', response.status);
    console.log('📥 HTTP响应OK:', response.ok);
    
    const data = await response.json();
    console.log('📥 响应数据:', data);
    
    // 模拟前端roleService的处理逻辑
    if (response.ok) {
      console.log('✅ HTTP请求成功，检查业务逻辑...');
      
      if (data && data.success) {
        console.log('✅ 业务逻辑成功');
        return {
          success: true,
          data: data.data || data,
        };
      } else {
        console.log('❌ 业务逻辑失败');
        console.log('❌ 错误信息:', data?.message);
        return {
          success: false,
          data: {},
          message: data?.message || '创建角色失败',
        };
      }
    } else {
      console.log('❌ HTTP请求失败');
      return {
        success: false,
        data: {},
        message: '网络请求失败',
      };
    }
  } catch (error) {
    console.error('💥 请求异常:', error);
    return {
      success: false,
      data: {},
      message: error.message,
    };
  }
}

// 模拟前端错误处理
function simulateErrorHandling(response) {
  console.log('🔧 模拟前端错误处理...');
  console.log('🔧 输入参数:', response);
  
  if (response.success === false) {
    const errorMessage = response.message || '操作失败';
    console.log('🔴 应该显示Toast错误:', errorMessage);
    
    // 模拟Toast显示
    console.log('%c🍞 Toast错误消息: ' + errorMessage, 'background: #ff4d4f; color: white; padding: 4px 8px; border-radius: 4px;');
    
    return errorMessage;
  } else {
    console.log('✅ 没有错误需要处理');
    return null;
  }
}

// 执行测试
testRoleCreation().then(result => {
  console.log('🎯 最终结果:', result);
  simulateErrorHandling(result);
});

console.log('📝 请在网络面板中查看实际的HTTP请求和响应');
console.log('📝 然后检查前端代码是否正确处理了响应');
