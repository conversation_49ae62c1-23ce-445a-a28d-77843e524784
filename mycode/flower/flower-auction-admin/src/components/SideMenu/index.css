.side-menu {
  height: 100vh;
  position: fixed;
  left: 0;
  z-index: 10;
  overflow-y: auto;
  overflow-x: hidden;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  background-color: #001529;
  overflow: hidden;
  white-space: nowrap;
  flex-shrink: 0; /* 防止logo被压缩 */
}

/* 确保菜单容器可以滚动 */
.side-menu .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 菜单区域可滚动 */
.side-menu .ant-menu {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  /* 确保在移动设备上也能正常滚动 */
  -webkit-overflow-scrolling: touch;
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

/* 自定义滚动条样式 */
.side-menu .ant-menu::-webkit-scrollbar {
  width: 6px;
}

.side-menu .ant-menu::-webkit-scrollbar-track {
  background: #001529;
}

.side-menu .ant-menu::-webkit-scrollbar-thumb {
  background: #1890ff;
  border-radius: 3px;
}

.side-menu .ant-menu::-webkit-scrollbar-thumb:hover {
  background: #40a9ff;
}

/* Firefox 滚动条样式 */
.side-menu .ant-menu {
  scrollbar-width: thin;
  scrollbar-color: #1890ff #001529;
}

/* 确保菜单项在滚动时不会被遮挡 */
.side-menu .ant-menu-item,
.side-menu .ant-menu-submenu {
  margin: 0;
}

/* 收缩状态下的滚动优化 */
.side-menu.ant-layout-sider-collapsed .ant-menu {
  overflow-y: auto;
  overflow-x: hidden;
}