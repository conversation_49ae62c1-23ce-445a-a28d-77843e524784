import React from 'react';
import { Layout, Menu } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  AuditOutlined,
  OrderedListOutlined,
  BankOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import './index.css';

const { Sider } = Layout;

interface MenuItem {
  key: string;
  icon?: React.ReactNode;
  label: string;
  children?: MenuItem[];
}

interface SideMenuProps {
  collapsed: boolean;
  onCollapse?: (collapsed: boolean) => void;
}

const SideMenu: React.FC<SideMenuProps> = ({ collapsed, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems: MenuItem[] = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
      children: [
        {
          key: '/users/list',
          label: '用户列表',
        },
        {
          key: '/users/roles',
          label: '角色管理',
        },
      ],
    },
    {
      key: '/products',
      icon: <ShoppingOutlined />,
      label: '商品管理',
      children: [
        {
          key: '/products/list',
          label: '商品列表',
        },
        {
          key: '/products/categories',
          label: '分类管理',
        },
        {
          key: '/products/audit',
          label: '商品审核',
        },
      ],
    },
    {
      key: '/auctions',
      icon: <AuditOutlined />,
      label: '拍卖管理',
      children: [
        {
          key: '/auctions/list',
          label: '拍卖会列表',
        },
        {
          key: '/auctions/items',
          label: '拍卖商品',
        },
        {
          key: '/auctions/live',
          label: '实时竞价',
        },
        {
          key: '/auctions/bids',
          label: '竞价记录',
        },
      ],
    },
    {
      key: '/orders',
      icon: <OrderedListOutlined />,
      label: '订单管理',
      children: [
        {
          key: '/orders/list',
          label: '订单列表',
        },
        {
          key: '/orders/shipping',
          label: '物流管理',
        },
      ],
    },
    {
      key: '/finance',
      icon: <BankOutlined />,
      label: '财务管理',
      children: [
        {
          key: '/finance/accounts',
          label: '账户管理',
        },
        {
          key: '/finance/transactions',
          label: '交易记录',
        },
        {
          key: '/finance/reports',
          label: '财务报表',
        },
      ],
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      children: [
        {
          key: '/settings/system',
          label: '系统配置',
        },
        {
          key: '/settings/security',
          label: '安全设置',
        },
        {
          key: '/settings/system-logs',
          label: '系统日志',
        },
        {
          key: '/settings/backup-restore',
          label: '备份恢复',
        },
        {
          key: '/settings/logs',
          label: '操作日志',
        },
      ],
    },
    {
      key: '/reports',
      icon: <AuditOutlined />,
      label: '报表中心',
      children: [
        {
          key: '/reports/sales',
          label: '销售报表',
        },
        {
          key: '/reports/users',
          label: '用户报表',
        },
        {
          key: '/reports/products',
          label: '商品报表',
        },
        {
          key: '/reports/auctions',
          label: '拍卖报表',
        },
      ],
    },
    {
      key: '/help',
      icon: <SettingOutlined />,
      label: '帮助中心',
      children: [
        {
          key: '/help/docs',
          label: '使用文档',
        },
        {
          key: '/help/faq',
          label: '常见问题',
        },
        {
          key: '/help/contact',
          label: '联系我们',
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const pathname = location.pathname;
    return [pathname];
  };

  // 获取当前展开的子菜单
  const getOpenKeys = () => {
    const pathname = location.pathname;
    const parts = pathname.split('/').filter(Boolean);
    if (parts.length >= 1) {
      return [`/${parts[0]}`];
    }
    return [];
  };

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={onCollapse}
      className="side-menu"
    >
      <div className="logo">
        {!collapsed ? '昆明花卉拍卖系统' : '花卉'}
      </div>
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={getSelectedKeys()}
        defaultOpenKeys={getOpenKeys()}
        items={menuItems}
        onClick={handleMenuClick}
      />
    </Sider>
  );
};

export default SideMenu;