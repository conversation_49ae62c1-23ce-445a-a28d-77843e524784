import React from 'react';
import { Result, Button, Space, Modal, message } from 'antd';
import { ExclamationCircleOutlined, LoginOutlined, ReloadOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

interface PermissionErrorProps {
  title?: string;
  subTitle?: string;
  showRelogin?: boolean;
  showRefresh?: boolean;
  onRelogin?: () => void;
  onRefresh?: () => void;
}

const PermissionError: React.FC<PermissionErrorProps> = ({
  title = '权限不足',
  subTitle = '抱歉，您没有权限访问此页面或执行此操作。',
  showRelogin = true,
  showRefresh = true,
  onRelogin,
  onRefresh,
}) => {
  const navigate = useNavigate();

  const handleRelogin = () => {
    Modal.confirm({
      title: '重新登录',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您将被重定向到登录页面，当前页面的数据可能会丢失。</p>
          <p>确定要重新登录吗？</p>
        </div>
      ),
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        // 清除所有认证信息
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        localStorage.removeItem('tokenExpiresAt');
        
        // 清除会话存储
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('refreshToken');
        sessionStorage.removeItem('user');
        
        // 清除管理端相关的缓存数据
        localStorage.removeItem('admin_preferences');
        localStorage.removeItem('admin_dashboard_config');
        localStorage.removeItem('admin_recent_actions');
        
        message.success('正在跳转到登录页...');
        
        // 执行自定义回调
        if (onRelogin) {
          onRelogin();
        } else {
          // 跳转到登录页
          window.location.href = '/login';
        }
      },
    });
  };

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    } else {
      window.location.reload();
    }
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    navigate('/dashboard');
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '60vh',
      padding: '20px'
    }}>
      <Result
        status="403"
        title={title}
        subTitle={subTitle}
        extra={
          <Space direction="vertical" size="middle">
            <Space wrap>
              {showRelogin && (
                <Button 
                  type="primary" 
                  icon={<LoginOutlined />}
                  onClick={handleRelogin}
                >
                  重新登录
                </Button>
              )}
              
              {showRefresh && (
                <Button 
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                >
                  刷新页面
                </Button>
              )}
              
              <Button onClick={handleGoBack}>
                返回上页
              </Button>
              
              <Button type="default" onClick={handleGoHome}>
                返回首页
              </Button>
            </Space>
            
            <div style={{ 
              marginTop: '20px', 
              padding: '16px', 
              backgroundColor: '#f6f8fa', 
              borderRadius: '6px',
              maxWidth: '500px'
            }}>
              <h4 style={{ margin: '0 0 8px 0', color: '#24292e' }}>可能的解决方案：</h4>
              <ul style={{ margin: 0, paddingLeft: '20px', color: '#586069' }}>
                <li>检查您的账户权限是否正确</li>
                <li>联系系统管理员分配相应权限</li>
                <li>尝试重新登录以刷新权限信息</li>
                <li>确认您使用的是正确的账户</li>
              </ul>
            </div>
          </Space>
        }
      />
    </div>
  );
};

export default PermissionError;
