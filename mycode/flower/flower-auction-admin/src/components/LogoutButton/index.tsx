import React, { useState } from 'react';
import { Button, Modal, message } from 'antd';
import { LogoutOutlined } from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';

interface LogoutButtonProps {
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
  size?: 'large' | 'middle' | 'small';
  block?: boolean;
  icon?: boolean;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const LogoutButton: React.FC<LogoutButtonProps> = ({
  type = 'default',
  size = 'middle',
  block = false,
  icon = true,
  children,
  className,
  style,
}) => {
  const { logout } = useAuth();
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      await logout();
      message.success('登出成功');
      // 跳转到登录页
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout error:', error);
      message.error('登出失败，请重试');
    } finally {
      setLogoutLoading(false);
      setLogoutModalVisible(false);
    }
  };

  const showLogoutModal = () => {
    setLogoutModalVisible(true);
  };

  return (
    <>
      <Button
        type={type}
        size={size}
        block={block}
        icon={icon ? <LogoutOutlined /> : undefined}
        onClick={showLogoutModal}
        className={className}
        style={style}
      >
        {children || '退出登录'}
      </Button>

      {/* 登出确认弹窗 */}
      <Modal
        title="确认登出"
        open={logoutModalVisible}
        onOk={handleLogout}
        onCancel={() => setLogoutModalVisible(false)}
        okText="确认登出"
        cancelText="取消"
        okType="danger"
        confirmLoading={logoutLoading}
        centered
      >
        <div style={{ padding: '20px 0' }}>
          <p>您确定要退出登录吗？</p>
          <p style={{ color: '#666', fontSize: '14px' }}>
            退出后需要重新登录才能继续使用系统功能。
          </p>
        </div>
      </Modal>
    </>
  );
};

export default LogoutButton;
