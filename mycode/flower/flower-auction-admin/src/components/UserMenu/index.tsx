import React, { useState } from 'react';
import { Dropdown, Avatar, Space, Modal, message } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  LockOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import { useNavigate } from 'react-router-dom';

interface UserMenuProps {
  showUsername?: boolean;
  avatarSize?: number | 'large' | 'small' | 'default';
  placement?: 'bottomLeft' | 'bottomCenter' | 'bottomRight' | 'topLeft' | 'topCenter' | 'topRight';
}

const UserMenu: React.FC<UserMenuProps> = ({
  showUsername = true,
  avatarSize = 'default',
  placement = 'bottomRight',
}) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      await logout();
      message.success('登出成功');
      // 跳转到登录页
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout error:', error);
      message.error('登出失败，请重试');
    } finally {
      setLogoutLoading(false);
      setLogoutModalVisible(false);
    }
  };

  const showLogoutModal = () => {
    setLogoutModalVisible(true);
  };

  const handleChangePassword = () => {
    message.info('修改密码功能开发中...');
  };

  const handleProfile = () => {
    message.info('个人中心功能开发中...');
  };

  const handleSettings = () => {
    message.info('账号设置功能开发中...');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: handleProfile,
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账号设置',
      onClick: handleSettings,
    },
    {
      key: 'change-password',
      icon: <LockOutlined />,
      label: '修改密码',
      onClick: handleChangePassword,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: showLogoutModal,
    },
  ];

  return (
    <>
      <Dropdown
        menu={{ items: userMenuItems }}
        placement={placement}
        arrow
      >
        <div style={{ cursor: 'pointer' }}>
          <Space>
            <Avatar 
              size={avatarSize}
              icon={<UserOutlined />}
              src={user?.avatar}
            />
            {showUsername && (
              <span style={{ 
                fontWeight: 500, 
                color: '#262626',
                userSelect: 'none'
              }}>
                {user?.realName || user?.username || '用户'}
              </span>
            )}
          </Space>
        </div>
      </Dropdown>

      {/* 登出确认弹窗 */}
      <Modal
        title="确认登出"
        open={logoutModalVisible}
        onOk={handleLogout}
        onCancel={() => setLogoutModalVisible(false)}
        okText="确认登出"
        cancelText="取消"
        okType="danger"
        confirmLoading={logoutLoading}
        centered
      >
        <div style={{ padding: '20px 0' }}>
          <p>您确定要退出登录吗？</p>
          <p style={{ color: '#666', fontSize: '14px' }}>
            退出后需要重新登录才能继续使用系统功能。
          </p>
        </div>
      </Modal>
    </>
  );
};

export default UserMenu;
