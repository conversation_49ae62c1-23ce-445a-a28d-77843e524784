import React from 'react';
import { Result, Button } from 'antd';
import { usePermissions } from '../../hooks/usePermissions';

interface PermissionGuardProps {
  children: React.ReactNode;
  // 权限检查配置
  permission?: string;
  permissions?: string[];
  role?: string;
  roles?: string[];
  module?: string;
  // 检查模式
  requireAll?: boolean; // 是否需要所有权限，默认false（任意一个即可）
  // 超级管理员是否绕过检查
  allowSuperAdmin?: boolean; // 默认true
  // 无权限时的显示
  fallback?: React.ReactNode;
  // 无权限时的操作
  onUnauthorized?: () => void;
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  permissions = [],
  role,
  roles = [],
  module,
  requireAll = false,
  allowSuperAdmin = true,
  fallback,
  onUnauthorized
}) => {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasModulePermission,
    isSuperAdmin,
    loading
  } = usePermissions();

  // 如果正在加载权限信息，显示加载状态
  if (loading) {
    return <div>加载中...</div>;
  }

  // 超级管理员绕过权限检查
  if (allowSuperAdmin && isSuperAdmin()) {
    return <>{children}</>;
  }

  // 构建需要检查的权限列表
  const permissionsToCheck = [
    ...(permission ? [permission] : []),
    ...permissions
  ];

  // 构建需要检查的角色列表
  const rolesToCheck = [
    ...(role ? [role] : []),
    ...roles
  ];

  // 权限检查逻辑
  let hasRequiredPermission = true;

  // 检查权限
  if (permissionsToCheck.length > 0) {
    if (requireAll) {
      hasRequiredPermission = hasAllPermissions(permissionsToCheck);
    } else {
      hasRequiredPermission = hasAnyPermission(permissionsToCheck);
    }
  }

  // 检查角色
  if (hasRequiredPermission && rolesToCheck.length > 0) {
    if (requireAll) {
      hasRequiredPermission = rolesToCheck.every(r => hasRole(r));
    } else {
      hasRequiredPermission = rolesToCheck.some(r => hasRole(r));
    }
  }

  // 检查模块权限
  if (hasRequiredPermission && module) {
    hasRequiredPermission = hasModulePermission(module);
  }

  // 如果没有权限
  if (!hasRequiredPermission) {
    // 执行无权限回调
    if (onUnauthorized) {
      onUnauthorized();
    }

    // 显示自定义fallback或默认无权限页面
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <Result
        status="403"
        title="权限不足"
        subTitle="您没有访问此功能的权限，请联系管理员。"
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            返回上一页
          </Button>
        }
      />
    );
  }

  // 权限检查通过，渲染子组件
  return <>{children}</>;
};

// 权限检查Hook，用于在组件内部进行权限检查
export const usePermissionCheck = () => {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasModulePermission,
    isSuperAdmin,
    getUserRoles,
    PERMISSIONS,
    ROLES
  } = usePermissions();

  const checkPermission = (
    permission?: string,
    permissions?: string[],
    role?: string,
    roles?: string[],
    module?: string,
    requireAll = false,
    allowSuperAdmin = true
  ): boolean => {
    // 超级管理员绕过权限检查
    if (allowSuperAdmin && isSuperAdmin()) {
      return true;
    }

    // 构建需要检查的权限列表
    const permissionsToCheck = [
      ...(permission ? [permission] : []),
      ...(permissions || [])
    ];

    // 构建需要检查的角色列表
    const rolesToCheck = [
      ...(role ? [role] : []),
      ...(roles || [])
    ];

    // 权限检查逻辑
    let hasRequiredPermission = true;

    // 检查权限
    if (permissionsToCheck.length > 0) {
      if (requireAll) {
        hasRequiredPermission = hasAllPermissions(permissionsToCheck);
      } else {
        hasRequiredPermission = hasAnyPermission(permissionsToCheck);
      }
    }

    // 检查角色
    if (hasRequiredPermission && rolesToCheck.length > 0) {
      if (requireAll) {
        hasRequiredPermission = rolesToCheck.every(r => hasRole(r));
      } else {
        hasRequiredPermission = rolesToCheck.some(r => hasRole(r));
      }
    }

    // 检查模块权限
    if (hasRequiredPermission && module) {
      hasRequiredPermission = hasModulePermission(module);
    }

    return hasRequiredPermission;
  };

  return {
    checkPermission,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasModulePermission,
    isSuperAdmin,
    getUserRoles,
    PERMISSIONS,
    ROLES
  };
};

// 权限按钮组件
interface PermissionButtonProps {
  children: React.ReactNode;
  permission?: string;
  permissions?: string[];
  role?: string;
  roles?: string[];
  module?: string;
  requireAll?: boolean;
  allowSuperAdmin?: boolean;
  fallback?: React.ReactNode;
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  children,
  permission,
  permissions,
  role,
  roles,
  module,
  requireAll = false,
  allowSuperAdmin = true,
  fallback = null
}) => {
  const { checkPermission } = usePermissionCheck();

  const hasAccess = checkPermission(
    permission,
    permissions,
    role,
    roles,
    module,
    requireAll,
    allowSuperAdmin
  );

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default PermissionGuard;
