import React from 'react';
import { Breadcrumb } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import './index.css';

// 路由路径与名称的映射
const breadcrumbNameMap: Record<string, string> = {
  '/dashboard': '仪表盘',
  '/users': '用户管理',
  '/users/list': '用户列表',
  '/users/roles': '角色管理',
  '/products': '商品管理',
  '/products/list': '商品列表',
  '/products/categories': '分类管理',
  '/products/audit': '商品审核',
  '/auctions': '拍卖管理',
  '/auctions/list': '拍卖会列表',
  '/auctions/items': '拍卖商品',
  '/auctions/bids': '竞价记录',
  '/orders': '订单管理',
  '/orders/list': '订单列表',
  '/orders/shipping': '物流管理',
  '/finance': '财务管理',
  '/finance/accounts': '账户管理',
  '/finance/transactions': '交易记录',
  '/finance/reports': '财务报表',
  '/settings': '系统设置',
  '/settings/system': '系统配置',
  '/settings/logs': '操作日志',
};

const BreadcrumbComponent: React.FC = () => {
  const location = useLocation();
  const pathSnippets = location.pathname.split('/').filter(i => i);
  
  // 构建面包屑项
  const extraBreadcrumbItems = pathSnippets.map((_, index) => {
    const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
    return {
      key: url,
      title: <Link to={url}>{breadcrumbNameMap[url] || url}</Link>,
    };
  });

  // 添加首页
  const breadcrumbItems = [
    {
      key: '/',
      title: <Link to="/">首页</Link>,
    },
  ].concat(extraBreadcrumbItems);

  return (
    <div className="breadcrumb-container">
      <Breadcrumb items={breadcrumbItems} />
    </div>
  );
};

export default BreadcrumbComponent;