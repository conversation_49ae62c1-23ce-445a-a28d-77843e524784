import React from 'react';
import { Form } from 'antd';

interface FormMessageProps {
  type: 'error' | 'success' | 'warning' | 'info';
  message: string;
  visible?: boolean;
}

const FormMessage: React.FC<FormMessageProps> = ({ type, message, visible = true }) => {
  if (!visible || !message) {
    return null;
  }

  const getMessageStyle = () => {
    const baseStyle = {
      padding: '12px 16px',
      borderRadius: '6px',
      fontSize: '14px',
      lineHeight: '1.5',
      border: '1px solid',
    };

    switch (type) {
      case 'error':
        return {
          ...baseStyle,
          backgroundColor: '#fff2f0',
          borderColor: '#ffccc7',
          color: '#ff4d4f',
        };
      case 'success':
        return {
          ...baseStyle,
          backgroundColor: '#f6ffed',
          borderColor: '#b7eb8f',
          color: '#52c41a',
        };
      case 'warning':
        return {
          ...baseStyle,
          backgroundColor: '#fffbe6',
          borderColor: '#ffe58f',
          color: '#faad14',
        };
      case 'info':
        return {
          ...baseStyle,
          backgroundColor: '#e6f7ff',
          borderColor: '#91d5ff',
          color: '#1890ff',
        };
      default:
        return baseStyle;
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'error':
        return '❌';
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '';
    }
  };

  const getLabel = () => {
    switch (type) {
      case 'error':
        return '操作失败：';
      case 'success':
        return '操作成功：';
      case 'warning':
        return '警告：';
      case 'info':
        return '提示：';
      default:
        return '';
    }
  };

  return (
    <Form.Item>
      <div style={getMessageStyle()}>
        <strong>{getIcon()} {getLabel()}</strong>{message}
      </div>
    </Form.Item>
  );
};

export default FormMessage;
