import { apiClient } from './apiClient';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

// 账户类型枚举
export enum AccountType {
  DEPOSIT = 1,    // 保证金账户
  TRADING = 2,    // 交易账户
  COMMISSION = 3, // 佣金账户
}

// 交易类型枚举
export enum TransactionType {
  DEPOSIT = 1,           // 充值
  WITHDRAWAL = 2,        // 提现
  AUCTION_PAYMENT = 3,   // 拍卖支付
  AUCTION_REFUND = 4,    // 拍卖退款
  COMMISSION_EARN = 5,   // 佣金收入
  COMMISSION_PAY = 6,    // 佣金支出
}

// 交易状态枚举
export enum TransactionStatus {
  PENDING = 0,   // 待处理
  SUCCESS = 1,   // 成功
  FAILED = 2,    // 失败
  CANCELLED = 3, // 已取消
}

// 账户信息接口
export interface Account {
  id: number;
  userId: number;
  accountType: AccountType;
  balance: number;
  frozenAmount: number;
  totalIncome: number;
  totalExpense: number;
  createdAt: string;
  updatedAt: string;
}

// 交易记录接口
export interface Transaction {
  id: number;
  transactionNo: string;
  userId: number;
  userName?: string;
  type: TransactionType;
  amount: number;
  status: TransactionStatus;
  description: string;
  relatedOrderId?: number;
  relatedOrderNo?: string;
  paymentMethod?: string;
  createdAt: string;
  updatedAt: string;
}

// 佣金记录接口
export interface Commission {
  id: number;
  orderId: number;
  orderNo: string;
  sellerId: number;
  sellerName: string;
  buyerId: number;
  buyerName: string;
  orderAmount: number;
  commissionRate: number;
  commissionAmount: number;
  status: number; // 0-待结算 1-已结算
  settledAt?: string;
  createdAt: string;
}

// 财务统计接口
export interface FinanceStatistics {
  totalRevenue: number;
  totalCommission: number;
  totalTransactions: number;
  todayRevenue: number;
  todayTransactions: number;
  monthlyRevenue: number;
  typeDistribution: Record<TransactionType, number>;
  statusDistribution: Record<TransactionStatus, number>;
}

// 日报表接口
export interface DailyReport {
  date: string;
  totalRevenue: number;
  totalTransactions: number;
  totalCommission: number;
  transactionsByType: Record<TransactionType, number>;
  revenueByType: Record<TransactionType, number>;
}

// 月报表接口
export interface MonthlyReport {
  year: number;
  month: number;
  totalRevenue: number;
  totalTransactions: number;
  totalCommission: number;
  dailyData: DailyReport[];
  typeDistribution: Record<TransactionType, number>;
}

// 查询参数接口
export interface TransactionQueryParams {
  page: number;
  pageSize: number;
  transactionNo?: string;
  userId?: number;
  type?: TransactionType;
  status?: TransactionStatus;
  startDate?: string;
  endDate?: string;
}

export interface CommissionQueryParams {
  page: number;
  pageSize: number;
  orderNo?: string;
  sellerId?: number;
  buyerId?: number;
  status?: number;
  startDate?: string;
  endDate?: string;
}

// 列表响应接口
export interface TransactionListResponse {
  transactions: Transaction[];
  total: number;
  page: number;
  size: number;
}

export interface CommissionListResponse {
  commissions: Commission[];
  total: number;
  page: number;
  size: number;
}

export const financeService = {
  // 账户管理
  createAccount: async (accountType: AccountType): Promise<ApiResponse<Account>> => {
    try {
      const response = await apiClient.post('/finance/account', { accountType });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建账户失败');
    }
  },

  getAccount: async (accountType: AccountType): Promise<ApiResponse<Account>> => {
    try {
      const response = await apiClient.get(`/finance/account/${accountType}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取账户信息失败');
    }
  },

  getAccountBalance: async (accountType: AccountType): Promise<ApiResponse<{ balance: number }>> => {
    try {
      const response = await apiClient.get(`/finance/balance/${accountType}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取账户余额失败');
    }
  },

  freezeAmount: async (amount: number, reason: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/finance/freeze', { amount, reason });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '冻结金额失败');
    }
  },

  unfreezeAmount: async (amount: number, reason: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/finance/unfreeze', { amount, reason });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '解冻金额失败');
    }
  },

  // 交易记录管理
  createTransaction: async (transaction: Partial<Transaction>): Promise<ApiResponse<Transaction>> => {
    try {
      const response = await apiClient.post('/finance/transaction', transaction);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建交易记录失败');
    }
  },

  getTransactionHistory: async (params: TransactionQueryParams): Promise<ApiResponse<TransactionListResponse>> => {
    try {
      const response = await apiClient.get('/finance/transactions', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取交易历史失败');
    }
  },

  getTransactionByNo: async (transactionNo: string): Promise<ApiResponse<Transaction>> => {
    try {
      const response = await apiClient.get(`/finance/transaction/${transactionNo}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取交易详情失败');
    }
  },

  // 统计报表
  getFinanceStatistics: async (): Promise<ApiResponse<FinanceStatistics>> => {
    try {
      const response = await apiClient.get('/finance/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取财务统计失败');
    }
  },

  getIncomeStatistics: async (startDate: string, endDate: string): Promise<ApiResponse<any>> => {
    try {
      const response = await apiClient.get('/finance/statistics/income', {
        params: { startDate, endDate }
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取收入统计失败');
    }
  },

  getDailyReport: async (date: string): Promise<ApiResponse<DailyReport>> => {
    try {
      const response = await apiClient.get('/finance/report/daily', { params: { date } });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取日报表失败');
    }
  },

  getMonthlyReport: async (year: number, month: number): Promise<ApiResponse<MonthlyReport>> => {
    try {
      const response = await apiClient.get('/finance/report/monthly', {
        params: { year, month }
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取月报表失败');
    }
  },

  // 佣金管理（管理员功能）
  calculateCommission: async (orderId: number): Promise<ApiResponse<Commission>> => {
    try {
      const response = await apiClient.post(`/admin/finance/commission/${orderId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '计算佣金失败');
    }
  },

  settleCommission: async (commissionId: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.put(`/admin/finance/commission/${commissionId}/settle`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '结算佣金失败');
    }
  },

  getCommissionList: async (params: CommissionQueryParams): Promise<ApiResponse<CommissionListResponse>> => {
    try {
      const response = await apiClient.get('/admin/finance/commissions', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取佣金列表失败');
    }
  },
};
