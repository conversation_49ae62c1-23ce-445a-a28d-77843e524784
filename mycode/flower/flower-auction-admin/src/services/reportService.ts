import axios from 'axios';

const API_BASE_URL = 'http://localhost:8081/api/v1';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 销售报表相关接口
export const salesReportAPI = {
  // 获取销售报表
  getSalesReport: (params: {
    start_date: string;
    end_date: string;
    granularity?: string;
  }) => api.get('/reports/sales', { params }),

  // 获取销售趋势
  getSalesTrend: (params: {
    start_date: string;
    end_date: string;
    granularity?: string;
  }) => api.get('/reports/sales/trend', { params }),

  // 获取商品销售排行
  getProductSalesRank: (params: {
    start_date: string;
    end_date: string;
    limit?: number;
  }) => api.get('/reports/sales/products', { params }),

  // 获取销售渠道分布
  getSalesChannelDistribution: (params: {
    start_date: string;
    end_date: string;
  }) => api.get('/reports/sales/channels', { params }),
};

// 用户报表相关接口
export const userReportAPI = {
  // 获取用户报表
  getUserReport: (params: {
    start_date: string;
    end_date: string;
    user_type?: string;
  }) => api.get('/reports/users', { params }),

  // 获取用户增长趋势
  getUserGrowthTrend: (params: {
    start_date: string;
    end_date: string;
  }) => api.get('/reports/users/growth', { params }),

  // 获取用户分布
  getUserDistribution: () => api.get('/reports/users/distribution'),

  // 获取用户活跃度排行
  getUserActivityRank: (params: {
    user_type?: string;
    limit?: number;
  }) => api.get('/reports/users/activity', { params }),
};

// 商品报表相关接口
export const productReportAPI = {
  // 获取商品报表
  getProductReport: (params: {
    start_date: string;
    end_date: string;
    category?: string;
  }) => api.get('/reports/products', { params }),

  // 获取分类销售数据
  getCategorySales: (params: {
    start_date: string;
    end_date: string;
  }) => api.get('/reports/products/categories', { params }),

  // 获取商品性能数据
  getProductPerformance: (params: {
    start_date: string;
    end_date: string;
    limit?: number;
  }) => api.get('/reports/products/performance', { params }),

  // 获取价格分布
  getPriceDistribution: () => api.get('/reports/products/price-distribution'),
};

// 拍卖报表相关接口
export const auctionReportAPI = {
  // 获取拍卖报表
  getAuctionReport: (params: {
    start_date: string;
    end_date: string;
    auction_type?: string;
  }) => api.get('/reports/auctions', { params }),

  // 获取拍卖趋势
  getAuctionTrend: (params: {
    start_date: string;
    end_date: string;
  }) => api.get('/reports/auctions/trend', { params }),

  // 获取拍卖性能数据
  getAuctionPerformance: (params: {
    start_date: string;
    end_date: string;
    limit?: number;
  }) => api.get('/reports/auctions/performance', { params }),

  // 获取拍卖状态分布
  getAuctionStatusDistribution: () => api.get('/reports/auctions/status'),
};

// 导出报表
export const exportReportAPI = {
  exportReport: (type: string, params: {
    start_date: string;
    end_date: string;
    format?: string;
  }) => {
    const url = `/reports/export/${type}`;
    return api.get(url, { 
      params,
      responseType: 'blob' // 用于文件下载
    });
  },
};

const reportService = {
  salesReportAPI,
  userReportAPI,
  productReportAPI,
  auctionReportAPI,
  exportReportAPI,
};

export default reportService;
