import { apiClient } from './apiClient';
import { Role, Permission } from '../pages/Users/<USER>';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface RoleListResponse {
  list: Role[];
  total: number;
  page: number;
  pageSize: number;
}

export interface RoleQueryParams {
  name?: string;
  code?: string;
  status?: number;
  page: number;
  pageSize: number;
}

export interface CreateRoleRequest {
  name: string;
  code: string;
  description?: string;
  status: number;
}

export interface UpdateRoleRequest {
  name: string;
  description?: string;
  status: number;
}

export const roleService = {
  // 获取角色列表
  getRoleList: async (params: RoleQueryParams): Promise<ApiResponse<RoleListResponse>> => {
    try {
      const response = await apiClient.get('/roles', { params });
      console.log('原始API响应:', response.data); // 调试日志

      // 后端返回的数据结构是 { success: true, data: { list: [], total: 0, page: 1, size: 10 } }
      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data, // 取出嵌套的data字段
        };
      } else {
        return {
          success: false,
          data: { list: [], total: 0, page: 1, pageSize: 10 },
          message: response.data?.message || '获取角色列表失败',
        };
      }
    } catch (error: any) {
      console.error('角色列表API错误:', error);
      const errorMessage = error.response?.data?.error || error.response?.data?.message || '获取角色列表失败';
      return {
        success: false,
        data: { list: [], total: 0, page: 1, pageSize: 10 },
        message: errorMessage,
      };
    }
  },

  // 创建角色
  createRole: async (roleData: CreateRoleRequest): Promise<ApiResponse<Role>> => {
    try {
      console.log('创建角色请求数据:', roleData); // 调试日志
      const response = await apiClient.post('/roles', roleData);
      console.log('创建角色API响应:', response.data); // 调试日志

      // 处理后端返回的数据结构
      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data || response.data,
        };
      } else {
        return {
          success: false,
          data: {} as Role,
          message: response.data?.message || '创建角色失败',
        };
      }
    } catch (error: any) {
      console.error('创建角色API错误:', error);
      const errorMessage = error.response?.data?.error || error.response?.data?.message || '创建角色失败';
      return {
        success: false,
        data: {} as Role,
        message: errorMessage,
      };
    }
  },

  // 更新角色
  updateRole: async (id: number, roleData: UpdateRoleRequest): Promise<ApiResponse<Role>> => {
    try {
      console.log('更新角色请求数据:', { id, roleData }); // 调试日志
      const response = await apiClient.put(`/roles/${id}`, roleData);
      console.log('更新角色API响应:', response.data); // 调试日志

      // 处理后端返回的数据结构
      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data || response.data,
        };
      } else {
        return {
          success: false,
          data: {} as Role,
          message: response.data?.message || '更新角色失败',
        };
      }
    } catch (error: any) {
      console.error('更新角色API错误:', error);
      const errorMessage = error.response?.data?.error || error.response?.data?.message || '更新角色失败';
      return {
        success: false,
        data: {} as Role,
        message: errorMessage,
      };
    }
  },

  // 删除角色
  deleteRole: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/roles/${id}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.response?.data?.message || '删除角色失败';
      return {
        success: false,
        data: null,
        message: errorMessage,
      };
    }
  },

  // 获取角色详情
  getRoleDetail: async (id: number): Promise<ApiResponse<Role>> => {
    try {
      const response = await apiClient.get(`/roles/${id}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.response?.data?.message || '获取角色详情失败';
      return {
        success: false,
        data: {} as Role,
        message: errorMessage,
      };
    }
  },

  // 获取权限列表
  getPermissionList: async (): Promise<ApiResponse<Permission[]>> => {
    try {
      const response = await apiClient.get('/permissions');
      console.log('权限列表API响应:', response.data); // 调试日志

      // 处理后端返回的权限数据
      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data || response.data.list || [],
        };
      } else {
        return {
          success: false,
          data: [],
          message: response.data?.message || '获取权限列表失败',
        };
      }
    } catch (error: any) {
      console.error('权限列表API错误:', error);
      // 权限列表获取失败不应该阻止角色列表的显示
      return {
        success: false,
        data: [],
        message: '权限列表暂时无法获取',
      };
    }
  },

  // 更新角色权限
  updateRolePermissions: async (roleId: number, permissionIds: number[]): Promise<ApiResponse> => {
    try {
      console.log('更新角色权限请求:', { roleId, permissionIds }); // 调试日志
      const response = await apiClient.put(`/roles/${roleId}/permissions`, {
        permissionIds,
      });
      console.log('更新角色权限API响应:', response.data); // 调试日志

      // 统一返回格式处理
      if (response.data && response.data.success !== undefined) {
        return response.data;
      } else {
        // 如果后端没有返回success字段，默认认为成功
        return {
          success: true,
          message: '权限分配成功',
          data: response.data,
        };
      }
    } catch (error: any) {
      console.error('更新角色权限API错误:', error);
      const errorMessage = error.response?.data?.error || error.response?.data?.message || '更新角色权限失败';
      return {
        success: false,
        data: null,
        message: errorMessage,
      };
    }
  },

  // 获取角色权限
  getRolePermissions: async (roleId: number): Promise<ApiResponse<number[]>> => {
    try {
      const response = await apiClient.get(`/roles/${roleId}/permissions`);
      console.log('获取角色权限API响应:', response.data); // 调试日志

      // 处理后端返回的数据结构
      if (response.data && response.data.success) {
        const roleWithPermissions = response.data.data;
        // 如果返回的是角色对象包含权限数组
        if (roleWithPermissions && roleWithPermissions.permissions) {
          // 提取权限ID数组
          const permissionIds = roleWithPermissions.permissions.map((p: any) => p.id || p);
          return {
            success: true,
            data: permissionIds,
          };
        } else if (Array.isArray(roleWithPermissions)) {
          // 如果直接返回权限数组
          const permissionIds = roleWithPermissions.map((p: any) => p.id || p);
          return {
            success: true,
            data: permissionIds,
          };
        } else {
          return {
            success: true,
            data: [],
          };
        }
      } else {
        return {
          success: false,
          data: [],
          message: response.data?.message || '获取角色权限失败',
        };
      }
    } catch (error: any) {
      console.error('获取角色权限API错误:', error);
      const errorMessage = error.response?.data?.error || error.response?.data?.message || '获取角色权限失败';
      return {
        success: false,
        data: [],
        message: errorMessage,
      };
    }
  },

  // 批量删除角色
  batchDeleteRoles: async (ids: number[]): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete('/roles/batch', { data: { ids } });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '批量删除角色失败');
    }
  },

  // 复制角色
  copyRole: async (id: number, newName: string, newCode: string): Promise<ApiResponse<Role>> => {
    try {
      const response = await apiClient.post(`/roles/${id}/copy`, {
        name: newName,
        code: newCode,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '复制角色失败');
    }
  },

  // 获取角色统计信息
  getRoleStatistics: async (): Promise<ApiResponse<{
    total: number;
    activeRoles: number;
    totalPermissions: number;
    roleDistribution: Record<string, number>;
  }>> => {
    try {
      const response = await apiClient.get('/roles/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取角色统计信息失败');
    }
  },

  // 检查角色编码是否存在
  checkRoleCodeExists: async (code: string, excludeId?: number): Promise<ApiResponse<boolean>> => {
    try {
      const params = excludeId ? { code, excludeId } : { code };
      const response = await apiClient.get('/roles/check-code', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '检查角色编码失败');
    }
  },
};
