import { apiClient } from './apiClient';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

// 物流状态枚举
export enum ShippingStatus {
  PENDING = 0,    // 待发货
  SHIPPED = 1,    // 已发货
  IN_TRANSIT = 2, // 运输中
  DELIVERED = 3,  // 已送达
  EXCEPTION = 4,  // 异常
  RETURNED = 5,   // 已退回
}

// 物流公司接口
export interface ShippingCompany {
  id: number;
  name: string;
  code: string;
  phone?: string;
  website?: string;
  apiEndpoint?: string;
  apiKey?: string;
  status: number;
  createdAt: string;
  updatedAt: string;
}

// 物流信息接口
export interface Shipping {
  id: number;
  orderId: number;
  trackingNumber: string;
  shippingCompanyId: number;
  shippingCompany?: ShippingCompany;
  senderName: string;
  senderPhone: string;
  senderAddress: string;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  status: ShippingStatus;
  shippedAt?: string;
  deliveredAt?: string;
  estimatedDays: number;
  weight: number;
  volume: number;
  shippingFee: number;
  insuranceFee: number;
  remark?: string;
  createdAt: string;
  updatedAt: string;
}

// 物流跟踪记录接口
export interface ShippingTrack {
  id: number;
  shippingId: number;
  status: string;
  location: string;
  description: string;
  operator: string;
  trackTime: string;
  createdAt: string;
}

// 物流模板接口
export interface ShippingTemplate {
  id: number;
  name: string;
  shippingCompanyId: number;
  senderName: string;
  senderPhone: string;
  senderAddress: string;
  defaultWeight: number;
  defaultVolume: number;
  estimatedDays: number;
  isDefault: boolean;
  status: number;
  createdAt: string;
  updatedAt: string;
}

// 查询参数接口
export interface ShippingQueryParams {
  page: number;
  pageSize: number;
  orderId?: number;
  trackingNumber?: string;
  shippingCompanyId?: number;
  status?: ShippingStatus;
  receiverName?: string;
  receiverPhone?: string;
  dateRange?: [string, string];
}

// 发货信息接口
export interface ShippingInfo {
  trackingNumber: string;
  shippingCompanyId: number;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  weight: number;
  volume: number;
  shippingFee: number;
  insuranceFee: number;
  estimatedDays: number;
  remark?: string;
}

// 跟踪记录接口
export interface TrackingRecord {
  status: string;
  location: string;
  description: string;
  operator: string;
  trackTime?: string;
}

// 物流统计接口
export interface ShippingStatistics {
  totalShippings: number;
  pendingShippings: number;
  inTransitShippings: number;
  deliveredShippings: number;
  exceptionShippings: number;
  statusDistribution: Record<ShippingStatus, number>;
  companyDistribution: Record<string, number>;
  avgDeliveryDays: number;
  onTimeRate: number;
}

// 列表响应接口
export interface ShippingListResponse {
  list: Shipping[];
  total: number;
  page: number;
  pageSize: number;
}

export const shippingService = {
  // 物流信息管理
  getShippingList: async (params: ShippingQueryParams): Promise<ApiResponse<ShippingListResponse>> => {
    try {
      const response = await apiClient.get('/shipping', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流列表失败');
    }
  },

  getShippingById: async (id: number): Promise<ApiResponse<Shipping>> => {
    try {
      const response = await apiClient.get(`/shipping/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流信息失败');
    }
  },

  getShippingByOrderId: async (orderId: number): Promise<ApiResponse<Shipping>> => {
    try {
      const response = await apiClient.get(`/shipping/order/${orderId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取订单物流信息失败');
    }
  },

  getShippingByTrackingNumber: async (trackingNumber: string): Promise<ApiResponse<Shipping>> => {
    try {
      const response = await apiClient.get(`/shipping/tracking/${trackingNumber}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流信息失败');
    }
  },

  createShipping: async (shipping: Partial<Shipping>): Promise<ApiResponse<Shipping>> => {
    try {
      const response = await apiClient.post('/shipping', shipping);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建物流信息失败');
    }
  },

  updateShipping: async (id: number, shipping: Partial<Shipping>): Promise<ApiResponse<Shipping>> => {
    try {
      const response = await apiClient.put(`/shipping/${id}`, shipping);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新物流信息失败');
    }
  },

  deleteShipping: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/shipping/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '删除物流信息失败');
    }
  },

  // 发货操作
  shipOrder: async (orderId: number, shippingInfo: ShippingInfo): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/shipping/ship', {
        orderId,
        shippingInfo,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '发货失败');
    }
  },

  updateShippingStatus: async (id: number, status: ShippingStatus, remark?: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.put(`/shipping/${id}/status`, {
        status,
        remark,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新物流状态失败');
    }
  },

  // 物流跟踪
  addTrackingRecord: async (shippingId: number, track: TrackingRecord): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/shipping/${shippingId}/track`, track);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '添加跟踪记录失败');
    }
  },

  getTrackingHistory: async (shippingId: number): Promise<ApiResponse<ShippingTrack[]>> => {
    try {
      const response = await apiClient.get(`/shipping/${shippingId}/tracks`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取跟踪历史失败');
    }
  },

  syncTrackingInfo: async (trackingNumber: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/shipping/sync/${trackingNumber}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '同步跟踪信息失败');
    }
  },

  // 统计信息
  getShippingStatistics: async (): Promise<ApiResponse<ShippingStatistics>> => {
    try {
      const response = await apiClient.get('/shipping/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流统计失败');
    }
  },

  // 物流公司管理
  getShippingCompanies: async (status?: number): Promise<ApiResponse<ShippingCompany[]>> => {
    try {
      const params = status !== undefined ? { status } : {};
      const response = await apiClient.get('/shipping-companies', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流公司列表失败');
    }
  },

  getShippingCompanyById: async (id: number): Promise<ApiResponse<ShippingCompany>> => {
    try {
      const response = await apiClient.get(`/shipping-companies/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流公司信息失败');
    }
  },

  createShippingCompany: async (company: Partial<ShippingCompany>): Promise<ApiResponse<ShippingCompany>> => {
    try {
      const response = await apiClient.post('/shipping-companies', company);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建物流公司失败');
    }
  },

  updateShippingCompany: async (id: number, company: Partial<ShippingCompany>): Promise<ApiResponse<ShippingCompany>> => {
    try {
      const response = await apiClient.put(`/shipping-companies/${id}`, company);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新物流公司失败');
    }
  },

  deleteShippingCompany: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/shipping-companies/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '删除物流公司失败');
    }
  },

  // 物流模板管理
  getShippingTemplates: async (status?: number): Promise<ApiResponse<ShippingTemplate[]>> => {
    try {
      const params = status !== undefined ? { status } : {};
      const response = await apiClient.get('/shipping-templates', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流模板列表失败');
    }
  },

  getShippingTemplateById: async (id: number): Promise<ApiResponse<ShippingTemplate>> => {
    try {
      const response = await apiClient.get(`/shipping-templates/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流模板信息失败');
    }
  },

  getDefaultTemplate: async (): Promise<ApiResponse<ShippingTemplate>> => {
    try {
      const response = await apiClient.get('/shipping-templates/default');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取默认物流模板失败');
    }
  },

  createShippingTemplate: async (template: Partial<ShippingTemplate>): Promise<ApiResponse<ShippingTemplate>> => {
    try {
      const response = await apiClient.post('/shipping-templates', template);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建物流模板失败');
    }
  },

  updateShippingTemplate: async (id: number, template: Partial<ShippingTemplate>): Promise<ApiResponse<ShippingTemplate>> => {
    try {
      const response = await apiClient.put(`/shipping-templates/${id}`, template);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新物流模板失败');
    }
  },

  deleteShippingTemplate: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/shipping-templates/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '删除物流模板失败');
    }
  },
};
