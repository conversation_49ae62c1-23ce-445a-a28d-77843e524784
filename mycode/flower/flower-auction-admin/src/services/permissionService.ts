import { apiClient } from './apiClient';

export interface Permission {
  id: number;
  name: string;
  code: string;
  description: string;
  module: string;
  action: string;
  resource: string;
  status: number;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: number;
  name: string;
  code: string;
  description: string;
  status: number;
  createdAt: string;
  updatedAt: string;
}

export interface RoleWithPermissions extends Role {
  permissions: Permission[];
}

export interface UserPermission {
  userId: number;
  roleId: number;
  roleName: string;
  roleCode: string;
  permissionId: number;
  permissionName: string;
  permissionCode: string;
  module: string;
  action: string;
  resource: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

class PermissionService {
  private baseURL = '/api/v1';

  // 权限管理
  async getPermissions(params?: {
    page?: number;
    pageSize?: number;
    module?: string;
    search?: string;
  }): Promise<ApiResponse<PaginatedResponse<Permission>>> {
    const response = await apiClient.get(`${this.baseURL}/permissions`, { params });
    return response.data;
  }

  async getPermissionById(id: number): Promise<ApiResponse<Permission>> {
    const response = await apiClient.get(`${this.baseURL}/permissions/${id}`);
    return response.data;
  }

  async createPermission(data: Partial<Permission>): Promise<ApiResponse<Permission>> {
    const response = await apiClient.post(`${this.baseURL}/permissions`, data);
    return response.data;
  }

  async updatePermission(id: number, data: Partial<Permission>): Promise<ApiResponse<Permission>> {
    const response = await apiClient.put(`${this.baseURL}/permissions/${id}`, data);
    return response.data;
  }

  async deletePermission(id: number): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`${this.baseURL}/permissions/${id}`);
    return response.data;
  }

  async initPermissions(): Promise<ApiResponse<void>> {
    const response = await apiClient.post(`${this.baseURL}/permissions/init`);
    return response.data;
  }

  // 角色管理
  async getRoles(params?: {
    page?: number;
    pageSize?: number;
    search?: string;
  }): Promise<ApiResponse<PaginatedResponse<Role>>> {
    const response = await apiClient.get(`${this.baseURL}/roles`, { params });
    return response.data;
  }

  async getRoleById(id: number): Promise<ApiResponse<Role>> {
    const response = await apiClient.get(`${this.baseURL}/roles/${id}`);
    return response.data;
  }

  async createRole(data: Partial<Role>): Promise<ApiResponse<Role>> {
    const response = await apiClient.post(`${this.baseURL}/roles`, data);
    return response.data;
  }

  async updateRole(id: number, data: Partial<Role>): Promise<ApiResponse<Role>> {
    const response = await apiClient.put(`${this.baseURL}/roles/${id}`, data);
    return response.data;
  }

  async deleteRole(id: number): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`${this.baseURL}/roles/${id}`);
    return response.data;
  }

  // 角色权限管理
  async getRolePermissions(roleId: number): Promise<ApiResponse<RoleWithPermissions>> {
    const response = await apiClient.get(`${this.baseURL}/roles/${roleId}/permissions`);
    return response.data;
  }

  async assignRolePermissions(roleId: number, permissionIds: number[]): Promise<ApiResponse<void>> {
    const response = await apiClient.put(`${this.baseURL}/roles/${roleId}/permissions`, {
      permissionIds
    });
    return response.data;
  }

  // 用户权限管理
  async getUserPermissions(userId: number): Promise<ApiResponse<Permission[]>> {
    const response = await apiClient.get(`${this.baseURL}/users/${userId}/permissions`);
    return response.data;
  }

  async checkUserPermission(userId: number, permissionCode: string): Promise<ApiResponse<{
    hasPermission: boolean;
    permissionCode: string;
  }>> {
    const response = await apiClient.get(`${this.baseURL}/users/${userId}/permissions/${permissionCode}/check`);
    return response.data;
  }

  // 用户角色管理
  async getUserRoles(userId: number): Promise<ApiResponse<Role[]>> {
    const response = await apiClient.get(`${this.baseURL}/users/${userId}/roles`);
    return response.data;
  }

  async assignUserRoles(userId: number, roleIds: number[]): Promise<ApiResponse<void>> {
    const response = await apiClient.put(`${this.baseURL}/users/${userId}/roles`, {
      roleIds
    });
    return response.data;
  }

  // 权限模块统计
  async getPermissionModules(): Promise<ApiResponse<Array<{
    module: string;
    count: number;
    permissions: Permission[];
  }>>> {
    const response = await apiClient.get(`${this.baseURL}/permissions/modules`);
    return response.data;
  }

  // 角色权限统计
  async getRolePermissionStats(): Promise<ApiResponse<Array<{
    roleId: number;
    roleName: string;
    roleCode: string;
    permissionCount: number;
    modules: string[];
  }>>> {
    const response = await apiClient.get(`${this.baseURL}/roles/permission-stats`);
    return response.data;
  }

  // 权限检查工具方法
  hasPermission(userPermissions: Permission[], permissionCode: string): boolean {
    return userPermissions.some(p => p.code === permissionCode);
  }

  hasModulePermission(userPermissions: Permission[], module: string): boolean {
    return userPermissions.some(p => p.module === module);
  }

  hasAnyPermission(userPermissions: Permission[], permissionCodes: string[]): boolean {
    return permissionCodes.some(code => this.hasPermission(userPermissions, code));
  }

  hasAllPermissions(userPermissions: Permission[], permissionCodes: string[]): boolean {
    return permissionCodes.every(code => this.hasPermission(userPermissions, code));
  }

  // 权限分组工具方法
  groupPermissionsByModule(permissions: Permission[]): Record<string, Permission[]> {
    return permissions.reduce((groups, permission) => {
      const module = permission.module;
      if (!groups[module]) {
        groups[module] = [];
      }
      groups[module].push(permission);
      return groups;
    }, {} as Record<string, Permission[]>);
  }

  // 获取模块列表
  getModules(permissions: Permission[]): string[] {
    return [...new Set(permissions.map(p => p.module))].sort();
  }

  // 权限代码生成工具
  generatePermissionCode(module: string, action: string, resource?: string): string {
    if (resource && resource !== module) {
      return `${module}:${action}:${resource}`;
    }
    return `${module}:${action}`;
  }

  // 权限描述生成工具
  generatePermissionDescription(action: string, resource: string): string {
    const actionMap: Record<string, string> = {
      'view': '查看',
      'create': '创建',
      'edit': '编辑',
      'update': '更新',
      'delete': '删除',
      'manage': '管理',
      'list': '列表',
      'audit': '审核',
      'control': '控制',
      'export': '导出',
      'import': '导入'
    };

    const resourceMap: Record<string, string> = {
      'user': '用户',
      'role': '角色',
      'permission': '权限',
      'product': '商品',
      'category': '分类',
      'auction': '拍卖',
      'order': '订单',
      'finance': '财务',
      'report': '报表',
      'system': '系统'
    };

    const actionText = actionMap[action] || action;
    const resourceText = resourceMap[resource] || resource;

    return `${actionText}${resourceText}`;
  }
}

export const permissionService = new PermissionService();
export default permissionService;
