import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { permissionService } from '../services/permissionService';

export interface Permission {
  id: number;
  name: string;
  code: string;
  description: string;
  module: string;
  action: string;
  resource: string;
  status: number;
}

export interface UserPermissions {
  permissions: Permission[];
  permissionCodes: string[];
  modules: string[];
}

export const usePermissions = () => {
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const [userPermissions, setUserPermissions] = useState<UserPermissions>({
    permissions: [],
    permissionCodes: [],
    modules: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户权限
  const fetchUserPermissions = useCallback(async () => {
    if (!user?.id || !isAuthenticated) {
      setUserPermissions({ permissions: [], permissionCodes: [], modules: [] });
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await permissionService.getUserPermissions(user.id);
      if (response.success && response.data) {
        const permissions = response.data;
        const permissionCodes = permissions.map(p => p.code);
        const moduleSet = new Set(permissions.map(p => p.module));
        const modules = Array.from(moduleSet);
        
        setUserPermissions({
          permissions,
          permissionCodes,
          modules
        });
      }
    } catch (err) {
      console.error('获取用户权限失败:', err);
      setError('获取权限信息失败');
    } finally {
      setLoading(false);
    }
  }, [user?.id, isAuthenticated]);

  // 检查是否有指定权限
  const hasPermission = useCallback((permissionCode: string): boolean => {
    return userPermissions.permissionCodes.includes(permissionCode);
  }, [userPermissions.permissionCodes]);

  // 检查是否有指定模块的权限
  const hasModulePermission = useCallback((module: string): boolean => {
    return userPermissions.modules.includes(module);
  }, [userPermissions.modules]);

  // 检查是否有任意一个权限
  const hasAnyPermission = useCallback((permissionCodes: string[]): boolean => {
    return permissionCodes.some(code => userPermissions.permissionCodes.includes(code));
  }, [userPermissions.permissionCodes]);

  // 检查是否有所有权限
  const hasAllPermissions = useCallback((permissionCodes: string[]): boolean => {
    return permissionCodes.every(code => userPermissions.permissionCodes.includes(code));
  }, [userPermissions.permissionCodes]);

  // 检查是否是超级管理员
  const isSuperAdmin = useCallback((): boolean => {
    if (!user?.roles) return false;
    return user.roles.some(role => role.code === 'ADMIN' || role.code === 'OPERATOR');
  }, [user?.roles]);

  // 检查角色权限
  const hasRole = useCallback((roleCode: string): boolean => {
    if (!user?.roles) return false;
    return user.roles.some(role => role.code === roleCode);
  }, [user?.roles]);

  // 获取用户角色代码列表
  const getUserRoles = useCallback((): string[] => {
    if (!user?.roles) return [];
    return user.roles.map(role => role.code || '');
  }, [user?.roles]);

  // 权限常量定义
  const PERMISSIONS = {
    // 用户管理
    USER_MANAGE: 'USER_MANAGE',
    USER_LIST: 'USER_LIST',
    USER_CREATE: 'USER_CREATE',
    USER_EDIT: 'USER_EDIT',
    USER_DELETE: 'USER_DELETE',
    USER_EXPORT: 'USER_EXPORT',
    
    // 角色管理
    ROLE_MANAGE: 'ROLE_MANAGE',
    ROLE_LIST: 'ROLE_LIST',
    ROLE_CREATE: 'ROLE_CREATE',
    ROLE_EDIT: 'ROLE_EDIT',
    ROLE_DELETE: 'ROLE_DELETE',
    ROLE_PERMISSION: 'ROLE_PERMISSION',
    
    // 商品管理
    PRODUCT_MANAGE: 'PRODUCT_MANAGE',
    PRODUCT_LIST: 'PRODUCT_LIST',
    PRODUCT_CREATE: 'PRODUCT_CREATE',
    PRODUCT_EDIT: 'PRODUCT_EDIT',
    PRODUCT_DELETE: 'PRODUCT_DELETE',
    PRODUCT_AUDIT: 'PRODUCT_AUDIT',
    
    // 分类管理
    CATEGORY_MANAGE: 'CATEGORY_MANAGE',
    CATEGORY_LIST: 'CATEGORY_LIST',
    CATEGORY_CREATE: 'CATEGORY_CREATE',
    CATEGORY_EDIT: 'CATEGORY_EDIT',
    CATEGORY_DELETE: 'CATEGORY_DELETE',
    
    // 拍卖管理
    AUCTION_MANAGE: 'AUCTION_MANAGE',
    AUCTION_LIST: 'AUCTION_LIST',
    AUCTION_CREATE: 'AUCTION_CREATE',
    AUCTION_EDIT: 'AUCTION_EDIT',
    AUCTION_DELETE: 'AUCTION_DELETE',
    AUCTION_CONTROL: 'AUCTION_CONTROL',
    
    // 订单管理
    ORDER_VIEW: 'order:view',
    ORDER_PROCESS: 'order:process',
    ORDER_CANCEL: 'order:cancel',
    ORDER_REFUND: 'order:refund',
    ORDER_SHIP: 'order:ship',
    
    // 财务管理
    FINANCE_VIEW: 'finance:view',
    FINANCE_AUDIT: 'finance:audit',
    FINANCE_COMMISSION: 'finance:commission',
    FINANCE_SETTLEMENT: 'finance:settlement',
    FINANCE_REPORT: 'finance:report',
    
    // 报表统计
    REPORT_SALES: 'report:sales',
    REPORT_USER: 'report:user',
    REPORT_AUCTION: 'report:auction',
    REPORT_FINANCE: 'report:finance',
    REPORT_EXPORT: 'report:export',
    
    // 系统管理
    SYSTEM_CONFIG: 'system:config',
    SYSTEM_LOG: 'system:log',
    SYSTEM_MONITOR: 'system:monitor',
    SYSTEM_BACKUP: 'system:backup',
    
    // 权限管理
    PERMISSION_VIEW: 'permission:view',
    PERMISSION_CREATE: 'permission:create',
    PERMISSION_EDIT: 'permission:edit',
    PERMISSION_DELETE: 'permission:delete'
  };

  // 角色常量定义
  const ROLES = {
    ADMIN: 'ADMIN',
    AUCTIONEER: 'AUCTIONEER',
    BUYER: 'BUYER',
    QUALITY_INSPECTOR: 'QUALITY_INSPECTOR',
    DISPLAY_OPERATOR: 'DISPLAY_OPERATOR',
    FINANCE: 'FINANCE',
    OPERATOR: 'OPERATOR'
  };

  // 初始化时获取权限
  useEffect(() => {
    fetchUserPermissions();
  }, [fetchUserPermissions]);

  return {
    // 权限数据
    userPermissions,
    loading,
    error,
    
    // 权限检查方法
    hasPermission,
    hasModulePermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    isSuperAdmin,
    getUserRoles,
    
    // 刷新权限
    refreshPermissions: fetchUserPermissions,
    
    // 权限常量
    PERMISSIONS,
    ROLES
  };
};
