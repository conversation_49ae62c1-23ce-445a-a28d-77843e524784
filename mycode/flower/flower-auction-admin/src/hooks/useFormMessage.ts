import { useState } from 'react';

interface UseFormMessageReturn {
  formError: string;
  formSuccess: string;
  formWarning: string;
  formInfo: string;
  setFormError: (message: string) => void;
  setFormSuccess: (message: string) => void;
  setFormWarning: (message: string) => void;
  setFormInfo: (message: string) => void;
  clearAllMessages: () => void;
  clearError: () => void;
  clearSuccess: () => void;
  clearWarning: () => void;
  clearInfo: () => void;
}

/**
 * 表单消息管理Hook
 * 用于统一管理表单中的错误、成功、警告和信息提示
 */
export const useFormMessage = (): UseFormMessageReturn => {
  const [formError, setFormError] = useState<string>('');
  const [formSuccess, setFormSuccess] = useState<string>('');
  const [formWarning, setFormWarning] = useState<string>('');
  const [formInfo, setFormInfo] = useState<string>('');

  const clearAllMessages = () => {
    setFormError('');
    setFormSuccess('');
    setFormWarning('');
    setFormInfo('');
  };

  const clearError = () => setFormError('');
  const clearSuccess = () => setFormSuccess('');
  const clearWarning = () => setFormWarning('');
  const clearInfo = () => setFormInfo('');

  return {
    formError,
    formSuccess,
    formWarning,
    formInfo,
    setFormError,
    setFormSuccess,
    setFormWarning,
    setFormInfo,
    clearAllMessages,
    clearError,
    clearSuccess,
    clearWarning,
    clearInfo,
  };
};

/**
 * 处理API响应的通用函数
 * @param response API响应
 * @param setError 设置错误消息的函数
 * @param setSuccess 设置成功消息的函数
 * @param successMessage 成功时的消息
 * @returns 是否成功
 */
export const handleApiResponse = (
  response: any,
  setError: (message: string) => void,
  setSuccess: (message: string) => void,
  successMessage: string
): boolean => {
  if (response.success) {
    setSuccess(successMessage);
    return true;
  } else {
    const errorMsg = response.message || '操作失败，请稍后重试';
    setError(errorMsg);
    return false;
  }
};

/**
 * 处理API错误的通用函数
 * @param error 错误对象
 * @param setError 设置错误消息的函数
 */
export const handleApiError = (
  error: any,
  setError: (message: string) => void
): void => {
  console.error('API调用失败:', error);
  const errorMsg = error.response?.data?.message || error.message || '网络错误，请检查连接后重试';
  setError(errorMsg);
};
