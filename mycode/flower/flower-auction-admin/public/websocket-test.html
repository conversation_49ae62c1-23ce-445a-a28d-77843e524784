<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 拍卖测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .system {
            background-color: #e3f2fd;
        }
        .bid {
            background-color: #fff3e0;
        }
        .error {
            background-color: #ffebee;
        }
    </style>
</head>
<body>
    <h1>WebSocket 拍卖系统测试</h1>
    
    <div class="container">
        <div class="panel">
            <h2>连接控制</h2>
            <div id="status" class="status disconnected">未连接</div>
            
            <div>
                <button id="connectBtn" onclick="connect()">连接</button>
                <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            </div>
            
            <h3>拍卖控制</h3>
            <div>
                <select id="auctionId">
                    <option value="auction-1">拍卖1 - 精品红玫瑰</option>
                    <option value="auction-2">拍卖2 - 白玫瑰花束</option>
                </select>
            </div>
            
            <div>
                <button onclick="joinAuction()" disabled id="joinBtn">加入拍卖</button>
                <button onclick="leaveAuction()" disabled id="leaveBtn">离开拍卖</button>
            </div>
            
            <div>
                <button onclick="startAuction()" disabled id="startBtn">开始拍卖</button>
                <button onclick="endAuction()" disabled id="endBtn">结束拍卖</button>
                <button onclick="pauseAuction()" disabled id="pauseBtn">暂停拍卖</button>
                <button onclick="resumeAuction()" disabled id="resumeBtn">恢复拍卖</button>
            </div>
            
            <h3>出价</h3>
            <div>
                <input type="number" id="bidAmount" placeholder="出价金额" min="1" step="10">
                <button onclick="placeBid()" disabled id="bidBtn">出价</button>
            </div>
        </div>
        
        <div class="panel">
            <h2>实时消息</h2>
            <div id="messages" class="messages"></div>
            <button onclick="clearMessages()">清空消息</button>
        </div>
    </div>

    <script>
        let ws = null;
        let connected = false;
        let currentAuctionId = null;

        function updateStatus(message, isConnected) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${isConnected ? 'connected' : 'disconnected'}`;
            connected = isConnected;
            updateButtons();
        }

        function updateButtons() {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('joinBtn').disabled = !connected;
            document.getElementById('leaveBtn').disabled = !connected || !currentAuctionId;
            document.getElementById('startBtn').disabled = !connected || !currentAuctionId;
            document.getElementById('endBtn').disabled = !connected || !currentAuctionId;
            document.getElementById('pauseBtn').disabled = !connected || !currentAuctionId;
            document.getElementById('resumeBtn').disabled = !connected || !currentAuctionId;
            document.getElementById('bidBtn').disabled = !connected || !currentAuctionId;
        }

        function addMessage(content, type = 'system') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `<small>${new Date().toLocaleTimeString()}</small> ${content}`;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function connect() {
            try {
                ws = new WebSocket('ws://localhost:8080/ws/auction?userId=test-user&userName=测试用户&userRole=admin');
                
                ws.onopen = function() {
                    updateStatus('已连接到拍卖服务器', true);
                    addMessage('WebSocket连接成功', 'system');
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleMessage(data);
                    } catch (error) {
                        addMessage(`解析消息失败: ${error.message}`, 'error');
                    }
                };

                ws.onclose = function(event) {
                    updateStatus('连接已断开', false);
                    addMessage(`连接关闭: ${event.code} - ${event.reason}`, 'system');
                    currentAuctionId = null;
                };

                ws.onerror = function(error) {
                    addMessage(`WebSocket错误: ${error}`, 'error');
                };
            } catch (error) {
                addMessage(`连接失败: ${error.message}`, 'error');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage(type, data) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: type,
                    data: data,
                    timestamp: Date.now()
                };
                ws.send(JSON.stringify(message));
                addMessage(`发送: ${type}`, 'system');
            } else {
                addMessage('WebSocket未连接', 'error');
            }
        }

        function handleMessage(message) {
            const { type, data } = message;
            
            switch (type) {
                case 'connected':
                    addMessage(`服务器响应: ${data.message}`, 'system');
                    break;
                case 'auction_started':
                    addMessage(`拍卖 ${data.auctionId} 已开始`, 'system');
                    break;
                case 'auction_ended':
                    addMessage(`拍卖 ${data.auctionId} 已结束`, 'system');
                    break;
                case 'auction_paused':
                    addMessage(`拍卖 ${data.auctionId} 已暂停`, 'system');
                    break;
                case 'auction_resumed':
                    addMessage(`拍卖 ${data.auctionId} 已恢复`, 'system');
                    break;
                case 'bid_placed':
                    addMessage(`${data.bidderName} 出价 ¥${data.bidAmount}`, 'bid');
                    break;
                case 'price_updated':
                    addMessage(`价格更新: ¥${data.currentPrice}`, 'bid');
                    break;
                case 'user_joined':
                    addMessage(`用户 ${data.userId} 加入拍卖，当前参与人数: ${data.participantCount}`, 'system');
                    break;
                case 'user_left':
                    addMessage(`用户 ${data.userId} 离开拍卖，当前参与人数: ${data.participantCount}`, 'system');
                    break;
                case 'auction_status':
                    addMessage(`拍卖状态: ${JSON.stringify(data)}`, 'system');
                    break;
                case 'error':
                    addMessage(`错误: ${data.message}`, 'error');
                    break;
                case 'pong':
                    addMessage('收到心跳响应', 'system');
                    break;
                default:
                    addMessage(`未知消息类型: ${type}`, 'system');
            }
        }

        function joinAuction() {
            const auctionId = document.getElementById('auctionId').value;
            sendMessage('join_auction', { auctionId });
            currentAuctionId = auctionId;
            updateButtons();
        }

        function leaveAuction() {
            if (currentAuctionId) {
                sendMessage('leave_auction', { auctionId: currentAuctionId });
                currentAuctionId = null;
                updateButtons();
            }
        }

        function startAuction() {
            if (currentAuctionId) {
                sendMessage('start_auction', { auctionId: currentAuctionId });
            }
        }

        function endAuction() {
            if (currentAuctionId) {
                sendMessage('end_auction', { auctionId: currentAuctionId });
            }
        }

        function pauseAuction() {
            if (currentAuctionId) {
                sendMessage('pause_auction', { auctionId: currentAuctionId });
            }
        }

        function resumeAuction() {
            if (currentAuctionId) {
                sendMessage('resume_auction', { auctionId: currentAuctionId });
            }
        }

        function placeBid() {
            const bidAmount = parseFloat(document.getElementById('bidAmount').value);
            if (currentAuctionId && bidAmount > 0) {
                sendMessage('place_bid', { 
                    auctionId: currentAuctionId, 
                    bidAmount: bidAmount 
                });
                document.getElementById('bidAmount').value = '';
            } else {
                addMessage('请输入有效的出价金额', 'error');
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 初始化
        updateButtons();
        addMessage('WebSocket拍卖测试页面已加载', 'system');
    </script>
</body>
</html>
