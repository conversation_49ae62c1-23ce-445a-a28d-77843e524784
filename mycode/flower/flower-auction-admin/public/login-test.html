<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-accounts {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-accounts h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .account-item {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #ddd;
        }
        .account-item:hover {
            background: #f0f8ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 花卉拍卖系统 - 登录功能测试</h1>
        
        <div class="test-accounts">
            <h3>测试账户（点击快速填入）</h3>
            <div class="account-item" onclick="fillAccount('admin01', '123456')">
                <strong>管理员:</strong> admin01 / 123456 (管理员)
            </div>
            <div class="account-item" onclick="fillAccount('auctioneer01', '123456')">
                <strong>拍卖师:</strong> auctioneer01 / 123456 (张拍卖师)
            </div>
            <div class="account-item" onclick="fillAccount('buyer01', '123456')">
                <strong>购买商:</strong> buyer01 / 123456 (李购买商)
            </div>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="apiUrl">API地址:</label>
                <input type="text" id="apiUrl" value="http://localhost:8081/api/v1/auth/login">
            </div>
            
            <button type="submit">🔐 登录测试</button>
            <button type="button" onclick="testUserInfo()">👤 获取用户信息</button>
            <button type="button" onclick="testLogout()">🚪 登出测试</button>
            <button type="button" onclick="clearResult()">🧹 清空结果</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        let currentToken = null;

        // 快速填入账户信息
        function fillAccount(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        // 显示结果
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
        }

        // 清空结果
        function clearResult() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = '';
        }

        // 登录测试
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const apiUrl = document.getElementById('apiUrl').value;
            
            try {
                showResult('正在登录...', true);
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    currentToken = data.data.token;
                    localStorage.setItem('token', currentToken);
                    localStorage.setItem('refreshToken', data.data.refreshToken);
                    
                    showResult(`✅ 登录成功！
                    
响应数据:
${JSON.stringify(data, null, 2)}

Token已保存，可以测试其他功能。`, true);
                } else {
                    showResult(`❌ 登录失败！
                    
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                showResult(`❌ 网络错误！
                
错误信息: ${error.message}
请检查：
1. 后端服务是否启动 (http://localhost:8080)
2. 网络连接是否正常
3. API地址是否正确`, false);
            }
        });

        // 获取用户信息测试
        async function testUserInfo() {
            const token = currentToken || localStorage.getItem('token');
            
            if (!token) {
                showResult('❌ 请先登录获取Token！', false);
                return;
            }
            
            try {
                showResult('正在获取用户信息...', true);
                
                const response = await fetch('http://localhost:8081/api/v1/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult(`✅ 获取用户信息成功！
                    
响应数据:
${JSON.stringify(data, null, 2)}`, true);
                } else {
                    showResult(`❌ 获取用户信息失败！
                    
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                showResult(`❌ 网络错误！
                
错误信息: ${error.message}`, false);
            }
        }

        // 登出测试
        async function testLogout() {
            const token = currentToken || localStorage.getItem('token');
            
            try {
                showResult('正在登出...', true);
                
                const response = await fetch('http://localhost:8081/api/v1/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': token ? `Bearer ${token}` : '',
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    currentToken = null;
                    localStorage.removeItem('token');
                    localStorage.removeItem('refreshToken');
                    
                    showResult(`✅ 登出成功！
                    
响应数据:
${JSON.stringify(data, null, 2)}

Token已清除。`, true);
                } else {
                    showResult(`❌ 登出失败！
                    
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                showResult(`❌ 网络错误！
                
错误信息: ${error.message}`, false);
            }
        }

        // 页面加载时检查是否有保存的token
        window.addEventListener('load', function() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                currentToken = savedToken;
                showResult(`📝 检测到已保存的Token: ${savedToken.substring(0, 50)}...
                
可以直接测试获取用户信息功能。`, true);
            }
        });
    </script>
</body>
</html>
