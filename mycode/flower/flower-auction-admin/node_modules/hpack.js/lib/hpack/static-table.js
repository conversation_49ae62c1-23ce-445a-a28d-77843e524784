exports.table = [
  {
    "name": ":authority",
    "value": "",
    "nameSize": 10,
    "totalSize": 42
  },
  {
    "name": ":method",
    "value": "GET",
    "nameSize": 7,
    "totalSize": 42
  },
  {
    "name": ":method",
    "value": "POST",
    "nameSize": 7,
    "totalSize": 43
  },
  {
    "name": ":path",
    "value": "/",
    "nameSize": 5,
    "totalSize": 38
  },
  {
    "name": ":path",
    "value": "/index.html",
    "nameSize": 5,
    "totalSize": 48
  },
  {
    "name": ":scheme",
    "value": "http",
    "nameSize": 7,
    "totalSize": 43
  },
  {
    "name": ":scheme",
    "value": "https",
    "nameSize": 7,
    "totalSize": 44
  },
  {
    "name": ":status",
    "value": "200",
    "nameSize": 7,
    "totalSize": 42
  },
  {
    "name": ":status",
    "value": "204",
    "nameSize": 7,
    "totalSize": 42
  },
  {
    "name": ":status",
    "value": "206",
    "nameSize": 7,
    "totalSize": 42
  },
  {
    "name": ":status",
    "value": "304",
    "nameSize": 7,
    "totalSize": 42
  },
  {
    "name": ":status",
    "value": "400",
    "nameSize": 7,
    "totalSize": 42
  },
  {
    "name": ":status",
    "value": "404",
    "nameSize": 7,
    "totalSize": 42
  },
  {
    "name": ":status",
    "value": "500",
    "nameSize": 7,
    "totalSize": 42
  },
  {
    "name": "accept-charset",
    "value": "",
    "nameSize": 14,
    "totalSize": 46
  },
  {
    "name": "accept-encoding",
    "value": "gzip, deflate",
    "nameSize": 15,
    "totalSize": 60
  },
  {
    "name": "accept-language",
    "value": "",
    "nameSize": 15,
    "totalSize": 47
  },
  {
    "name": "accept-ranges",
    "value": "",
    "nameSize": 13,
    "totalSize": 45
  },
  {
    "name": "accept",
    "value": "",
    "nameSize": 6,
    "totalSize": 38
  },
  {
    "name": "access-control-allow-origin",
    "value": "",
    "nameSize": 27,
    "totalSize": 59
  },
  {
    "name": "age",
    "value": "",
    "nameSize": 3,
    "totalSize": 35
  },
  {
    "name": "allow",
    "value": "",
    "nameSize": 5,
    "totalSize": 37
  },
  {
    "name": "authorization",
    "value": "",
    "nameSize": 13,
    "totalSize": 45
  },
  {
    "name": "cache-control",
    "value": "",
    "nameSize": 13,
    "totalSize": 45
  },
  {
    "name": "content-disposition",
    "value": "",
    "nameSize": 19,
    "totalSize": 51
  },
  {
    "name": "content-encoding",
    "value": "",
    "nameSize": 16,
    "totalSize": 48
  },
  {
    "name": "content-language",
    "value": "",
    "nameSize": 16,
    "totalSize": 48
  },
  {
    "name": "content-length",
    "value": "",
    "nameSize": 14,
    "totalSize": 46
  },
  {
    "name": "content-location",
    "value": "",
    "nameSize": 16,
    "totalSize": 48
  },
  {
    "name": "content-range",
    "value": "",
    "nameSize": 13,
    "totalSize": 45
  },
  {
    "name": "content-type",
    "value": "",
    "nameSize": 12,
    "totalSize": 44
  },
  {
    "name": "cookie",
    "value": "",
    "nameSize": 6,
    "totalSize": 38
  },
  {
    "name": "date",
    "value": "",
    "nameSize": 4,
    "totalSize": 36
  },
  {
    "name": "etag",
    "value": "",
    "nameSize": 4,
    "totalSize": 36
  },
  {
    "name": "expect",
    "value": "",
    "nameSize": 6,
    "totalSize": 38
  },
  {
    "name": "expires",
    "value": "",
    "nameSize": 7,
    "totalSize": 39
  },
  {
    "name": "from",
    "value": "",
    "nameSize": 4,
    "totalSize": 36
  },
  {
    "name": "host",
    "value": "",
    "nameSize": 4,
    "totalSize": 36
  },
  {
    "name": "if-match",
    "value": "",
    "nameSize": 8,
    "totalSize": 40
  },
  {
    "name": "if-modified-since",
    "value": "",
    "nameSize": 17,
    "totalSize": 49
  },
  {
    "name": "if-none-match",
    "value": "",
    "nameSize": 13,
    "totalSize": 45
  },
  {
    "name": "if-range",
    "value": "",
    "nameSize": 8,
    "totalSize": 40
  },
  {
    "name": "if-unmodified-since",
    "value": "",
    "nameSize": 19,
    "totalSize": 51
  },
  {
    "name": "last-modified",
    "value": "",
    "nameSize": 13,
    "totalSize": 45
  },
  {
    "name": "link",
    "value": "",
    "nameSize": 4,
    "totalSize": 36
  },
  {
    "name": "location",
    "value": "",
    "nameSize": 8,
    "totalSize": 40
  },
  {
    "name": "max-forwards",
    "value": "",
    "nameSize": 12,
    "totalSize": 44
  },
  {
    "name": "proxy-authenticate",
    "value": "",
    "nameSize": 18,
    "totalSize": 50
  },
  {
    "name": "proxy-authorization",
    "value": "",
    "nameSize": 19,
    "totalSize": 51
  },
  {
    "name": "range",
    "value": "",
    "nameSize": 5,
    "totalSize": 37
  },
  {
    "name": "referer",
    "value": "",
    "nameSize": 7,
    "totalSize": 39
  },
  {
    "name": "refresh",
    "value": "",
    "nameSize": 7,
    "totalSize": 39
  },
  {
    "name": "retry-after",
    "value": "",
    "nameSize": 11,
    "totalSize": 43
  },
  {
    "name": "server",
    "value": "",
    "nameSize": 6,
    "totalSize": 38
  },
  {
    "name": "set-cookie",
    "value": "",
    "nameSize": 10,
    "totalSize": 42
  },
  {
    "name": "strict-transport-security",
    "value": "",
    "nameSize": 25,
    "totalSize": 57
  },
  {
    "name": "transfer-encoding",
    "value": "",
    "nameSize": 17,
    "totalSize": 49
  },
  {
    "name": "user-agent",
    "value": "",
    "nameSize": 10,
    "totalSize": 42
  },
  {
    "name": "vary",
    "value": "",
    "nameSize": 4,
    "totalSize": 36
  },
  {
    "name": "via",
    "value": "",
    "nameSize": 3,
    "totalSize": 35
  },
  {
    "name": "www-authenticate",
    "value": "",
    "nameSize": 16,
    "totalSize": 48
  }
];
exports.map = {
  ":authority": {
    "index": 1,
    "values": {
      "": 1
    }
  },
  ":method": {
    "index": 2,
    "values": {
      "GET": 2,
      "POST": 3
    }
  },
  ":path": {
    "index": 4,
    "values": {
      "/": 4,
      "/index.html": 5
    }
  },
  ":scheme": {
    "index": 6,
    "values": {
      "http": 6,
      "https": 7
    }
  },
  ":status": {
    "index": 8,
    "values": {
      "200": 8,
      "204": 9,
      "206": 10,
      "304": 11,
      "400": 12,
      "404": 13,
      "500": 14
    }
  },
  "accept-charset": {
    "index": 15,
    "values": {
      "": 15
    }
  },
  "accept-encoding": {
    "index": 16,
    "values": {
      "gzip, deflate": 16
    }
  },
  "accept-language": {
    "index": 17,
    "values": {
      "": 17
    }
  },
  "accept-ranges": {
    "index": 18,
    "values": {
      "": 18
    }
  },
  "accept": {
    "index": 19,
    "values": {
      "": 19
    }
  },
  "access-control-allow-origin": {
    "index": 20,
    "values": {
      "": 20
    }
  },
  "age": {
    "index": 21,
    "values": {
      "": 21
    }
  },
  "allow": {
    "index": 22,
    "values": {
      "": 22
    }
  },
  "authorization": {
    "index": 23,
    "values": {
      "": 23
    }
  },
  "cache-control": {
    "index": 24,
    "values": {
      "": 24
    }
  },
  "content-disposition": {
    "index": 25,
    "values": {
      "": 25
    }
  },
  "content-encoding": {
    "index": 26,
    "values": {
      "": 26
    }
  },
  "content-language": {
    "index": 27,
    "values": {
      "": 27
    }
  },
  "content-length": {
    "index": 28,
    "values": {
      "": 28
    }
  },
  "content-location": {
    "index": 29,
    "values": {
      "": 29
    }
  },
  "content-range": {
    "index": 30,
    "values": {
      "": 30
    }
  },
  "content-type": {
    "index": 31,
    "values": {
      "": 31
    }
  },
  "cookie": {
    "index": 32,
    "values": {
      "": 32
    }
  },
  "date": {
    "index": 33,
    "values": {
      "": 33
    }
  },
  "etag": {
    "index": 34,
    "values": {
      "": 34
    }
  },
  "expect": {
    "index": 35,
    "values": {
      "": 35
    }
  },
  "expires": {
    "index": 36,
    "values": {
      "": 36
    }
  },
  "from": {
    "index": 37,
    "values": {
      "": 37
    }
  },
  "host": {
    "index": 38,
    "values": {
      "": 38
    }
  },
  "if-match": {
    "index": 39,
    "values": {
      "": 39
    }
  },
  "if-modified-since": {
    "index": 40,
    "values": {
      "": 40
    }
  },
  "if-none-match": {
    "index": 41,
    "values": {
      "": 41
    }
  },
  "if-range": {
    "index": 42,
    "values": {
      "": 42
    }
  },
  "if-unmodified-since": {
    "index": 43,
    "values": {
      "": 43
    }
  },
  "last-modified": {
    "index": 44,
    "values": {
      "": 44
    }
  },
  "link": {
    "index": 45,
    "values": {
      "": 45
    }
  },
  "location": {
    "index": 46,
    "values": {
      "": 46
    }
  },
  "max-forwards": {
    "index": 47,
    "values": {
      "": 47
    }
  },
  "proxy-authenticate": {
    "index": 48,
    "values": {
      "": 48
    }
  },
  "proxy-authorization": {
    "index": 49,
    "values": {
      "": 49
    }
  },
  "range": {
    "index": 50,
    "values": {
      "": 50
    }
  },
  "referer": {
    "index": 51,
    "values": {
      "": 51
    }
  },
  "refresh": {
    "index": 52,
    "values": {
      "": 52
    }
  },
  "retry-after": {
    "index": 53,
    "values": {
      "": 53
    }
  },
  "server": {
    "index": 54,
    "values": {
      "": 54
    }
  },
  "set-cookie": {
    "index": 55,
    "values": {
      "": 55
    }
  },
  "strict-transport-security": {
    "index": 56,
    "values": {
      "": 56
    }
  },
  "transfer-encoding": {
    "index": 57,
    "values": {
      "": 57
    }
  },
  "user-agent": {
    "index": 58,
    "values": {
      "": 58
    }
  },
  "vary": {
    "index": 59,
    "values": {
      "": 59
    }
  },
  "via": {
    "index": 60,
    "values": {
      "": 60
    }
  },
  "www-authenticate": {
    "index": 61,
    "values": {
      "": 61
    }
  }
};
