"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _typeof = require("@babel/runtime/helpers/typeof");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _context = require("@rc-component/context");
var _classnames = _interopRequireDefault(require("classnames"));
var React = _interopRequireWildcard(require("react"));
var _Cell = _interopRequireDefault(require("../Cell"));
var _TableContext = _interopRequireWildcard(require("../context/TableContext"));
var _useRowInfo = _interopRequireDefault(require("../hooks/useRowInfo"));
var _VirtualCell = _interopRequireDefault(require("./VirtualCell"));
var _context2 = require("./context");
var _expandUtil = require("../utils/expandUtil");
var _excluded = ["data", "index", "className", "rowKey", "style", "extra", "getHeight"];
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var BodyLine = /*#__PURE__*/React.forwardRef(function (props, ref) {
  var data = props.data,
    index = props.index,
    className = props.className,
    rowKey = props.rowKey,
    style = props.style,
    extra = props.extra,
    getHeight = props.getHeight,
    restProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  var record = data.record,
    indent = data.indent,
    renderIndex = data.index;
  var _useContext = (0, _context.useContext)(_TableContext.default, ['prefixCls', 'flattenColumns', 'fixColumn', 'componentWidth', 'scrollX']),
    scrollX = _useContext.scrollX,
    flattenColumns = _useContext.flattenColumns,
    prefixCls = _useContext.prefixCls,
    fixColumn = _useContext.fixColumn,
    componentWidth = _useContext.componentWidth;
  var _useContext2 = (0, _context.useContext)(_context2.StaticContext, ['getComponent']),
    getComponent = _useContext2.getComponent;
  var rowInfo = (0, _useRowInfo.default)(record, rowKey, index, indent);
  var RowComponent = getComponent(['body', 'row'], 'div');
  var cellComponent = getComponent(['body', 'cell'], 'div');

  // ========================== Expand ==========================
  var rowSupportExpand = rowInfo.rowSupportExpand,
    expanded = rowInfo.expanded,
    rowProps = rowInfo.rowProps,
    expandedRowRender = rowInfo.expandedRowRender,
    expandedRowClassName = rowInfo.expandedRowClassName;
  var expandRowNode;
  if (rowSupportExpand && expanded) {
    var expandContent = expandedRowRender(record, index, indent + 1, expanded);
    var expandedClsName = (0, _expandUtil.computedExpandedClassName)(expandedRowClassName, record, index, indent);
    var additionalProps = {};
    if (fixColumn) {
      additionalProps = {
        style: (0, _defineProperty2.default)({}, '--virtual-width', "".concat(componentWidth, "px"))
      };
    }
    var rowCellCls = "".concat(prefixCls, "-expanded-row-cell");
    expandRowNode = /*#__PURE__*/React.createElement(RowComponent, {
      className: (0, _classnames.default)("".concat(prefixCls, "-expanded-row"), "".concat(prefixCls, "-expanded-row-level-").concat(indent + 1), expandedClsName)
    }, /*#__PURE__*/React.createElement(_Cell.default, {
      component: cellComponent,
      prefixCls: prefixCls,
      className: (0, _classnames.default)(rowCellCls, (0, _defineProperty2.default)({}, "".concat(rowCellCls, "-fixed"), fixColumn)),
      additionalProps: additionalProps
    }, expandContent));
  }

  // ========================== Render ==========================
  var rowStyle = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, style), {}, {
    width: scrollX
  });
  if (extra) {
    rowStyle.position = 'absolute';
    rowStyle.pointerEvents = 'none';
  }
  var rowNode = /*#__PURE__*/React.createElement(RowComponent, (0, _extends2.default)({}, rowProps, restProps, {
    "data-row-key": rowKey,
    ref: rowSupportExpand ? null : ref,
    className: (0, _classnames.default)(className, "".concat(prefixCls, "-row"), rowProps === null || rowProps === void 0 ? void 0 : rowProps.className, (0, _defineProperty2.default)({}, "".concat(prefixCls, "-row-extra"), extra)),
    style: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, rowStyle), rowProps === null || rowProps === void 0 ? void 0 : rowProps.style)
  }), flattenColumns.map(function (column, colIndex) {
    return /*#__PURE__*/React.createElement(_VirtualCell.default, {
      key: colIndex,
      component: cellComponent,
      rowInfo: rowInfo,
      column: column,
      colIndex: colIndex,
      indent: indent,
      index: index,
      renderIndex: renderIndex,
      record: record,
      inverse: extra,
      getHeight: getHeight
    });
  }));
  if (rowSupportExpand) {
    return /*#__PURE__*/React.createElement("div", {
      ref: ref
    }, rowNode, expandRowNode);
  }
  return rowNode;
});
var ResponseBodyLine = (0, _TableContext.responseImmutable)(BodyLine);
if (process.env.NODE_ENV !== 'production') {
  ResponseBodyLine.displayName = 'BodyLine';
}
var _default = exports.default = ResponseBodyLine;