import CalendarLocale from "rc-picker/es/locale/hr_HR";
import TimePickerLocale from '../../time-picker/locale/hr_HR';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: 'Odaberite datum',
    yearPlaceholder: 'Odaberite godinu',
    quarterPlaceholder: 'Odaberite četvrtinu',
    monthPlaceholder: 'Odaberite mjesec',
    weekPlaceholder: 'Odaberite tjedan',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON> datum', 'Završni datum'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> godina', '<PERSON>av<PERSON><PERSON><PERSON> godina'],
    rangeMonthPlaceholder: ['<PERSON>č<PERSON>ni mjesec', '<PERSON><PERSON><PERSON><PERSON><PERSON> mjesec'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON>ni tjedan', '<PERSON>avrš<PERSON> tjedan']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;