import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MergeOutlinedSvg from "@ant-design/icons-svg/es/asn/MergeOutlined";
import AntdIcon from "../components/AntdIcon";
var MergeOutlined = function MergeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MergeOutlinedSvg
  }));
};

/**![merge](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMjQ4IDc1Mmg3MlYyNjRoLTcyeiIgLz48cGF0aCBkPSJNNzQwIDg2M2M2MS44NiAwIDExMi01MC4xNCAxMTItMTEyIDAtNDguMzMtMzAuNi04OS41LTczLjUtMTA1LjJsLS4wMS0xMTMuMDRhNTAuNzMgNTAuNzMgMCAwMC0zNC45NS00OC4ybC00MzQuOS0xNDIuNDEtMjIuNCA2OC40MiA0MjAuMjUgMTM3LjYxLjAxIDk1LjkyQzY2MSA2NTguMzQgNjI4IDcwMC44IDYyOCA3NTFjMCA2MS44NiA1MC4xNCAxMTIgMTEyIDExMm0tNDU2IDYxYzYxLjg2IDAgMTEyLTUwLjE0IDExMi0xMTJzLTUwLjE0LTExMi0xMTItMTEyLTExMiA1MC4xNC0xMTIgMTEyIDUwLjE0IDExMiAxMTIgMTEybTQ1Ni0xMjVhNDggNDggMCAxMTAtOTYgNDggNDggMCAwMTAgOTZtLTQ1NiA2MWE0OCA0OCAwIDExMC05NiA0OCA0OCAwIDAxMCA5Nm0wLTUzNmM2MS44NiAwIDExMi01MC4xNCAxMTItMTEycy01MC4xNC0xMTItMTEyLTExMi0xMTIgNTAuMTQtMTEyIDExMiA1MC4xNCAxMTIgMTEyIDExMm0wLTY0YTQ4IDQ4IDAgMTEwLTk2IDQ4IDQ4IDAgMDEwIDk2IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(MergeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MergeOutlined';
}
export default RefIcon;