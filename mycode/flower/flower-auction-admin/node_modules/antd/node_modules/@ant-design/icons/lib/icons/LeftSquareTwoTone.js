"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _LeftSquareTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/LeftSquareTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var LeftSquareTwoTone = function LeftSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _LeftSquareTwoTone.default
  }));
};

/**![left-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMTgxLjMtMzM0LjVsMjQ2LTE3OGM1LjMtMy44IDEyLjcgMCAxMi43IDYuNXY0Ni45YzAgMTAuMy00LjkgMTkuOS0xMy4yIDI1LjlMNDY1LjQgNTEybDE0NS40IDEwNS4yYzguMyA2IDEzLjIgMTUuNyAxMy4yIDI1LjlWNjkwYzAgNi41LTcuNCAxMC4zLTEyLjcgNi40bC0yNDYtMTc4YTcuOTUgNy45NSAwIDAxMC0xMi45eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNMzY1LjMgNTE4LjRsMjQ2IDE3OGM1LjMgMy45IDEyLjcuMSAxMi43LTYuNHYtNDYuOWMwLTEwLjItNC45LTE5LjktMTMuMi0yNS45TDQ2NS40IDUxMmwxNDUuNC0xMDUuMmM4LjMtNiAxMy4yLTE1LjYgMTMuMi0yNS45VjMzNGMwLTYuNS03LjQtMTAuMy0xMi43LTYuNWwtMjQ2IDE3OGE3Ljk1IDcuOTUgMCAwMDAgMTIuOXoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(LeftSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftSquareTwoTone';
}
var _default = exports.default = RefIcon;