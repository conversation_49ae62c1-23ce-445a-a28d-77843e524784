function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ItalicOutlinedSvg from "@ant-design/icons-svg/es/asn/ItalicOutlined";
import AntdIcon from "../components/AntdIcon";
const ItalicOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ItalicOutlinedSvg
}));

/**![italic](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc5OCAxNjBIMzY2Yy00LjQgMC04IDMuNi04IDh2NjRjMCA0LjQgMy42IDggOCA4aDE4MS4ybC0xNTYgNTQ0SDIyOWMtNC40IDAtOCAzLjYtOCA4djY0YzAgNC40IDMuNiA4IDggOGg0MzJjNC40IDAgOC0zLjYgOC04di02NGMwLTQuNC0zLjYtOC04LThINDc0LjRsMTU2LTU0NEg3OThjNC40IDAgOC0zLjYgOC04di02NGMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(ItalicOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ItalicOutlined';
}
export default RefIcon;