"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _NodeIndexOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/NodeIndexOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const NodeIndexOutlined = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _NodeIndexOutlined.default
}));

/**![node-index](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NDMuNSA3MzcuNGMtMTIuNC03NS4yLTc5LjItMTI5LjEtMTU1LjMtMTI1LjRTNTUwLjkgNjc2IDU0NiA3NTJjLTE1My41LTQuOC0yMDgtNDAuNy0xOTkuMS0xMTMuNyAzLjMtMjcuMyAxOS44LTQxLjkgNTAuMS00OSAxOC40LTQuMyAzOC44LTQuOSA1Ny4zLTMuMiAxLjcuMiAzLjUuMyA1LjIuNSAxMS4zIDIuNyAyMi44IDUgMzQuMyA2LjggMzQuMSA1LjYgNjguOCA4LjQgMTAxLjggNi42IDkyLjgtNSAxNTYtNDUuOSAxNTkuMi0xMzIuNyAzLjEtODQuMS01NC43LTE0My43LTE0Ny45LTE4My42LTI5LjktMTIuOC02MS42LTIyLjctOTMuMy0zMC4yLTE0LjMtMy40LTI2LjMtNS43LTM1LjItNy4yLTcuOS03NS45LTcxLjUtMTMzLjgtMTQ3LjgtMTM0LjQtNzYuMy0uNi0xNDAuOSA1Ni4xLTE1MC4xIDEzMS45czQwIDE0Ni4zIDExNC4yIDE2My45Yzc0LjIgMTcuNiAxNDkuOS0yMy4zIDE3NS43LTk1LjEgOS40IDEuNyAxOC43IDMuNiAyOCA1LjggMjguMiA2LjYgNTYuNCAxNS40IDgyLjQgMjYuNiA3MC43IDMwLjIgMTA5LjMgNzAuMSAxMDcuNSAxMTkuOS0xLjYgNDQuNi0zMy42IDY1LjItOTYuMiA2OC42LTI3LjUgMS41LTU3LjYtLjktODcuMy01LjgtOC4zLTEuNC0xNS45LTIuOC0yMi42LTQuMy0zLjktLjgtNi42LTEuNS03LjgtMS44bC0zLjEtLjZjLTIuMi0uMy01LjktLjgtMTAuNy0xLjMtMjUtMi4zLTUyLjEtMS41LTc4LjUgNC42LTU1LjIgMTIuOS05My45IDQ3LjItMTAxLjEgMTA1LjgtMTUuNyAxMjYuMiA3OC42IDE4NC43IDI3NiAxODguOSAyOS4xIDcwLjQgMTA2LjQgMTA3LjkgMTc5LjYgODcgNzMuMy0yMC45IDExOS4zLTkzLjQgMTA2LjktMTY4LjZ6TTMyOS4xIDM0NS4yYTgzLjMgODMuMyAwIDExLjAxLTE2Ni42MSA4My4zIDgzLjMgMCAwMS0uMDEgMTY2LjYxek02OTUuNiA4NDVhODMuMyA4My4zIDAgMTEuMDEtMTY2LjYxQTgzLjMgODMuMyAwIDAxNjk1LjYgODQ1eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(NodeIndexOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'NodeIndexOutlined';
}
var _default = exports.default = RefIcon;