"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _AlertFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/AlertFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const AlertFilled = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _AlertFilled.default
}));

/**![alert](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAyNDRjMTc2LjE4IDAgMzE5IDE0Mi44MiAzMTkgMzE5djIzM2EzMiAzMiAwIDAxLTMyIDMySDIyNWEzMiAzMiAwIDAxLTMyLTMyVjU2M2MwLTE3Ni4xOCAxNDIuODItMzE5IDMxOS0zMTl6TTQ4NCA2OGg1NmE4IDggMCAwMTggOHY5NmE4IDggMCAwMS04IDhoLTU2YTggOCAwIDAxLTgtOFY3NmE4IDggMCAwMTgtOHpNMTc3LjI1IDE5MS42NmE4IDggMCAwMTExLjMyIDBsNjcuODggNjcuODhhOCA4IDAgMDEwIDExLjMxbC0zOS42IDM5LjZhOCA4IDAgMDEtMTEuMzEgMGwtNjcuODgtNjcuODhhOCA4IDAgMDEwLTExLjMxbDM5LjYtMzkuNnptNjY5LjYgMGwzOS42IDM5LjZhOCA4IDAgMDEwIDExLjNsLTY3Ljg4IDY3LjlhOCA4IDAgMDEtMTEuMzIgMGwtMzkuNi0zOS42YTggOCAwIDAxMC0xMS4zMmw2Ny44OS02Ny44OGE4IDggMCAwMTExLjMxIDB6TTE5MiA4OTJoNjQwYTMyIDMyIDAgMDEzMiAzMnYyNGE4IDggMCAwMS04IDhIMTY4YTggOCAwIDAxLTgtOHYtMjRhMzIgMzIgMCAwMTMyLTMyem0xNDgtMzE3djI1M2g2NFY1NzVoLTY0eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(AlertFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AlertFilled';
}
var _default = exports.default = RefIcon;