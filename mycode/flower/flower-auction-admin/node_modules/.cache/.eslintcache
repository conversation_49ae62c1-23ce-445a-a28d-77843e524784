[{"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/index.tsx": "1", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/App.tsx": "3", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/store/index.ts": "4", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/routes/index.tsx": "5", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/AuthProvider/index.tsx": "6", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/store/slices/authSlice.ts": "7", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/authService.ts": "8", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/layouts/MainLayout/index.tsx": "9", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Login/index.tsx": "10", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Dashboard/index.tsx": "11", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Users/<USER>/index.tsx": "13", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx": "14", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx": "15", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/ProductList/index.tsx": "16", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx": "17", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx": "18", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx": "19", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx": "20", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Orders/OrderList/index.tsx": "21", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Orders/ShippingManagement/index.tsx": "22", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Finance/TransactionRecords/index.tsx": "23", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Finance/AccountManagement/index.tsx": "24", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx": "25", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SecuritySettings/index.tsx": "26", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SystemSettings/index.tsx": "27", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SystemLogs/index.tsx": "28", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/BackupRestore/index.tsx": "29", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/OperationLogs/index.tsx": "30", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Reports/UserReport/index.tsx": "31", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Reports/SalesReport/index.tsx": "32", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Reports/AuctionReport/index.tsx": "33", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Reports/ProductReport/index.tsx": "34", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Help/UserManual/index.tsx": "35", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Help/HelpCenter/index.tsx": "36", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Help/FAQ/index.tsx": "37", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Help/OnlineSupport/index.tsx": "38", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auction/LiveAuction/index.tsx": "39", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/apiClient.ts": "40", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/userService.ts": "41", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/hooks/useAuth.ts": "42", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/roleService.ts": "43", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/productService.ts": "44", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/hooks/useFormMessage.ts": "45", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/categoryService.ts": "46", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/SideMenu/index.tsx": "47", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/uploadService.ts": "48", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/auctionService.ts": "49", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx": "50", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Breadcrumb/index.tsx": "51", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/orderService.ts": "52", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/shippingService.ts": "53", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/financeService.ts": "54", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/FormMessage/index.tsx": "55", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/reportService.ts": "56", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/websocketService.ts": "57", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/UserMenu/index.tsx": "58", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalSecurity/index.tsx": "59", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/Notifications/index.tsx": "60", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalInfo/index.tsx": "61", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalSettings/index.tsx": "62", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/index.ts": "63", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/LogoutButton/index.tsx": "64", "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/UserInfo/index.tsx": "65"}, {"size": 554, "mtime": 1748933646966, "results": "66", "hashOfConfig": "67"}, {"size": 425, "mtime": 1748933646987, "results": "68", "hashOfConfig": "67"}, {"size": 651, "mtime": 1748933646961, "results": "69", "hashOfConfig": "67"}, {"size": 442, "mtime": 1748933646990, "results": "70", "hashOfConfig": "67"}, {"size": 6489, "mtime": 1748936935457, "results": "71", "hashOfConfig": "67"}, {"size": 4090, "mtime": 1748933646961, "results": "72", "hashOfConfig": "67"}, {"size": 883, "mtime": 1748933646991, "results": "73", "hashOfConfig": "67"}, {"size": 6436, "mtime": 1748935407736, "results": "74", "hashOfConfig": "67"}, {"size": 2767, "mtime": 1748933646967, "results": "75", "hashOfConfig": "67"}, {"size": 7911, "mtime": 1748933646977, "results": "76", "hashOfConfig": "67"}, {"size": 3004, "mtime": 1748933646973, "results": "77", "hashOfConfig": "67"}, {"size": 26978, "mtime": 1748933646987, "results": "78", "hashOfConfig": "67"}, {"size": 25539, "mtime": 1748933646986, "results": "79", "hashOfConfig": "67"}, {"size": 20134, "mtime": 1748933646981, "results": "80", "hashOfConfig": "67"}, {"size": 25521, "mtime": 1748933646980, "results": "81", "hashOfConfig": "67"}, {"size": 34468, "mtime": 1748933646982, "results": "82", "hashOfConfig": "67"}, {"size": 19098, "mtime": 1748933646971, "results": "83", "hashOfConfig": "67"}, {"size": 25356, "mtime": 1748933646972, "results": "84", "hashOfConfig": "67"}, {"size": 35891, "mtime": 1748942953830, "results": "85", "hashOfConfig": "67"}, {"size": 36480, "mtime": 1748933646971, "results": "86", "hashOfConfig": "67"}, {"size": 24066, "mtime": 1748933646978, "results": "87", "hashOfConfig": "67"}, {"size": 31465, "mtime": 1748933646979, "results": "88", "hashOfConfig": "67"}, {"size": 12257, "mtime": 1748933646974, "results": "89", "hashOfConfig": "67"}, {"size": 16342, "mtime": 1748933646973, "results": "90", "hashOfConfig": "67"}, {"size": 14970, "mtime": 1748933646974, "results": "91", "hashOfConfig": "67"}, {"size": 20982, "mtime": 1748936345891, "results": "92", "hashOfConfig": "67"}, {"size": 25443, "mtime": 1748933646985, "results": "93", "hashOfConfig": "67"}, {"size": 15596, "mtime": 1748933646985, "results": "94", "hashOfConfig": "67"}, {"size": 18996, "mtime": 1748933646984, "results": "95", "hashOfConfig": "67"}, {"size": 358, "mtime": 1748933646984, "results": "96", "hashOfConfig": "67"}, {"size": 12777, "mtime": 1748933646983, "results": "97", "hashOfConfig": "67"}, {"size": 11863, "mtime": 1748933646983, "results": "98", "hashOfConfig": "67"}, {"size": 13553, "mtime": 1748933646982, "results": "99", "hashOfConfig": "67"}, {"size": 13163, "mtime": 1748933646983, "results": "100", "hashOfConfig": "67"}, {"size": 12944, "mtime": 1748933646976, "results": "101", "hashOfConfig": "67"}, {"size": 12834, "mtime": 1748933646975, "results": "102", "hashOfConfig": "67"}, {"size": 14634, "mtime": 1748933646975, "results": "103", "hashOfConfig": "67"}, {"size": 15326, "mtime": 1748933646976, "results": "104", "hashOfConfig": "67"}, {"size": 14313, "mtime": 1748933646968, "results": "105", "hashOfConfig": "67"}, {"size": 4666, "mtime": 1748942796066, "results": "106", "hashOfConfig": "67"}, {"size": 7603, "mtime": 1748933646990, "results": "107", "hashOfConfig": "67"}, {"size": 3762, "mtime": 1748936049749, "results": "108", "hashOfConfig": "67"}, {"size": 10054, "mtime": 1748933646989, "results": "109", "hashOfConfig": "67"}, {"size": 8481, "mtime": 1748933646989, "results": "110", "hashOfConfig": "67"}, {"size": 2460, "mtime": 1748933646965, "results": "111", "hashOfConfig": "67"}, {"size": 4986, "mtime": 1748933646988, "results": "112", "hashOfConfig": "67"}, {"size": 5638, "mtime": 1748936672383, "results": "113", "hashOfConfig": "67"}, {"size": 4949, "mtime": 1748933646990, "results": "114", "hashOfConfig": "67"}, {"size": 12749, "mtime": 1748933646988, "results": "115", "hashOfConfig": "67"}, {"size": 1552, "mtime": 1748937184749, "results": "116", "hashOfConfig": "67"}, {"size": 1719, "mtime": 1748933646962, "results": "117", "hashOfConfig": "67"}, {"size": 6335, "mtime": 1748933646989, "results": "118", "hashOfConfig": "67"}, {"size": 11309, "mtime": 1748933646990, "results": "119", "hashOfConfig": "67"}, {"size": 8461, "mtime": 1748933646989, "results": "120", "hashOfConfig": "67"}, {"size": 2041, "mtime": 1748933646962, "results": "121", "hashOfConfig": "67"}, {"size": 3961, "mtime": 1748933646989, "results": "122", "hashOfConfig": "67"}, {"size": 6321, "mtime": 1748942796058, "results": "123", "hashOfConfig": "67"}, {"size": 3468, "mtime": 1748936987085, "results": "124", "hashOfConfig": "67"}, {"size": 11826, "mtime": 1748940246911, "results": "125", "hashOfConfig": "67"}, {"size": 10307, "mtime": 1748940265123, "results": "126", "hashOfConfig": "67"}, {"size": 9380, "mtime": 1748936720769, "results": "127", "hashOfConfig": "67"}, {"size": 8615, "mtime": 1748936763586, "results": "128", "hashOfConfig": "67"}, {"size": 444, "mtime": 1748936228807, "results": "129", "hashOfConfig": "67"}, {"size": 2205, "mtime": 1748936102890, "results": "130", "hashOfConfig": "67"}, {"size": 1931, "mtime": 1748933646964, "results": "131", "hashOfConfig": "67"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1r9wn2v", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/App.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/store/index.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/routes/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/AuthProvider/index.tsx", ["327"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/store/slices/authSlice.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/authService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/layouts/MainLayout/index.tsx", ["328"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Login/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Dashboard/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Users/<USER>/index.tsx", ["329"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Users/<USER>/index.tsx", [], ["330", "331"], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx", ["332"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx", ["333", "334", "335"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/ProductList/index.tsx", [], ["336"], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx", ["337"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx", ["338", "339"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx", ["340", "341"], ["342"], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx", [], ["343"], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Orders/OrderList/index.tsx", ["344"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Orders/ShippingManagement/index.tsx", ["345", "346"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Finance/TransactionRecords/index.tsx", ["347"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Finance/AccountManagement/index.tsx", ["348", "349"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx", ["350", "351", "352", "353", "354", "355", "356"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SecuritySettings/index.tsx", ["357", "358", "359", "360", "361"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SystemSettings/index.tsx", ["362", "363", "364", "365", "366"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SystemLogs/index.tsx", ["367", "368"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/BackupRestore/index.tsx", ["369", "370", "371", "372"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/OperationLogs/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Reports/UserReport/index.tsx", ["373"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Reports/SalesReport/index.tsx", ["374"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Reports/AuctionReport/index.tsx", ["375"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Reports/ProductReport/index.tsx", ["376"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Help/UserManual/index.tsx", ["377", "378", "379", "380"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Help/HelpCenter/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Help/FAQ/index.tsx", ["381"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Help/OnlineSupport/index.tsx", ["382"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auction/LiveAuction/index.tsx", ["383", "384", "385", "386", "387", "388", "389", "390", "391"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/apiClient.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/userService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/roleService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/productService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/hooks/useFormMessage.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/categoryService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/SideMenu/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/uploadService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/auctionService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Breadcrumb/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/orderService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/shippingService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/financeService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/FormMessage/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/reportService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/websocketService.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/UserMenu/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalSecurity/index.tsx", ["392", "393"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/Notifications/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalInfo/index.tsx", ["394"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalSettings/index.tsx", ["395", "396", "397", "398", "399", "400", "401"], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/index.ts", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/LogoutButton/index.tsx", [], [], "/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/UserInfo/index.tsx", ["402"], [], {"ruleId": "403", "severity": 1, "message": "404", "line": 107, "column": 6, "nodeType": "405", "endLine": 107, "endColumn": 8, "suggestions": "406"}, {"ruleId": "407", "severity": 1, "message": "408", "line": 15, "column": 10, "nodeType": "409", "messageId": "410", "endLine": 15, "endColumn": 18}, {"ruleId": "403", "severity": 1, "message": "411", "line": 127, "column": 6, "nodeType": "405", "endLine": 127, "endColumn": 19, "suggestions": "412"}, {"ruleId": "403", "severity": 1, "message": "413", "line": 248, "column": 6, "nodeType": "405", "endLine": 248, "endColumn": 8, "suggestions": "414", "suppressions": "415"}, {"ruleId": "403", "severity": 1, "message": "416", "line": 254, "column": 6, "nodeType": "405", "endLine": 254, "endColumn": 19, "suggestions": "417", "suppressions": "418"}, {"ruleId": "403", "severity": 1, "message": "419", "line": 140, "column": 6, "nodeType": "405", "endLine": 140, "endColumn": 19, "suggestions": "420"}, {"ruleId": "407", "severity": 1, "message": "421", "line": 30, "column": 25, "nodeType": "409", "messageId": "410", "endLine": 30, "endColumn": 38}, {"ruleId": "403", "severity": 1, "message": "422", "line": 189, "column": 6, "nodeType": "405", "endLine": 189, "endColumn": 8, "suggestions": "423"}, {"ruleId": "403", "severity": 1, "message": "424", "line": 199, "column": 6, "nodeType": "405", "endLine": 199, "endColumn": 18, "suggestions": "425"}, {"ruleId": "403", "severity": 1, "message": "419", "line": 198, "column": 6, "nodeType": "405", "endLine": 198, "endColumn": 19, "suggestions": "426", "suppressions": "427"}, {"ruleId": "403", "severity": 1, "message": "428", "line": 247, "column": 6, "nodeType": "405", "endLine": 247, "endColumn": 19, "suggestions": "429"}, {"ruleId": "403", "severity": 1, "message": "430", "line": 255, "column": 6, "nodeType": "405", "endLine": 255, "endColumn": 8, "suggestions": "431"}, {"ruleId": "403", "severity": 1, "message": "432", "line": 333, "column": 15, "nodeType": "409", "endLine": 333, "endColumn": 22}, {"ruleId": "407", "severity": 1, "message": "433", "line": 229, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 229, "endColumn": 22}, {"ruleId": "407", "severity": 1, "message": "434", "line": 251, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 251, "endColumn": 22}, {"ruleId": "403", "severity": 1, "message": "435", "line": 467, "column": 6, "nodeType": "405", "endLine": 467, "endColumn": 19, "suggestions": "436", "suppressions": "437"}, {"ruleId": "403", "severity": 1, "message": "438", "line": 216, "column": 6, "nodeType": "405", "endLine": 216, "endColumn": 19, "suggestions": "439", "suppressions": "440"}, {"ruleId": "403", "severity": 1, "message": "441", "line": 175, "column": 6, "nodeType": "405", "endLine": 175, "endColumn": 19, "suggestions": "442"}, {"ruleId": "403", "severity": 1, "message": "443", "line": 165, "column": 6, "nodeType": "405", "endLine": 165, "endColumn": 19, "suggestions": "444"}, {"ruleId": "407", "severity": 1, "message": "445", "line": 246, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 246, "endColumn": 27}, {"ruleId": "407", "severity": 1, "message": "446", "line": 25, "column": 15, "nodeType": "409", "messageId": "410", "endLine": 25, "endColumn": 26}, {"ruleId": "407", "severity": 1, "message": "447", "line": 28, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 28, "endColumn": 28}, {"ruleId": "407", "severity": 1, "message": "448", "line": 37, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 37, "endColumn": 16}, {"ruleId": "407", "severity": 1, "message": "449", "line": 21, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 21, "endColumn": 15}, {"ruleId": "407", "severity": 1, "message": "450", "line": 80, "column": 10, "nodeType": "409", "messageId": "410", "endLine": 80, "endColumn": 21}, {"ruleId": "407", "severity": 1, "message": "451", "line": 81, "column": 10, "nodeType": "409", "messageId": "410", "endLine": 81, "endColumn": 23}, {"ruleId": "407", "severity": 1, "message": "452", "line": 85, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 85, "endColumn": 37}, {"ruleId": "407", "severity": 1, "message": "453", "line": 94, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 94, "endColumn": 47}, {"ruleId": "407", "severity": 1, "message": "454", "line": 102, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 102, "endColumn": 50}, {"ruleId": "403", "severity": 1, "message": "455", "line": 174, "column": 6, "nodeType": "405", "endLine": 174, "endColumn": 29, "suggestions": "456"}, {"ruleId": "407", "severity": 1, "message": "457", "line": 19, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 19, "endColumn": 10}, {"ruleId": "407", "severity": 1, "message": "458", "line": 33, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 33, "endColumn": 21}, {"ruleId": "407", "severity": 1, "message": "459", "line": 37, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 37, "endColumn": 17}, {"ruleId": "407", "severity": 1, "message": "460", "line": 38, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 38, "endColumn": 15}, {"ruleId": "403", "severity": 1, "message": "461", "line": 173, "column": 6, "nodeType": "405", "endLine": 173, "endColumn": 8, "suggestions": "462"}, {"ruleId": "407", "severity": 1, "message": "463", "line": 15, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 15, "endColumn": 9}, {"ruleId": "407", "severity": 1, "message": "464", "line": 16, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 16, "endColumn": 8}, {"ruleId": "407", "severity": 1, "message": "465", "line": 17, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 17, "endColumn": 10}, {"ruleId": "407", "severity": 1, "message": "466", "line": 35, "column": 16, "nodeType": "409", "messageId": "410", "endLine": 35, "endColumn": 20}, {"ruleId": "403", "severity": 1, "message": "461", "line": 193, "column": 6, "nodeType": "405", "endLine": 193, "endColumn": 8, "suggestions": "467"}, {"ruleId": "407", "severity": 1, "message": "468", "line": 32, "column": 8, "nodeType": "409", "messageId": "410", "endLine": 32, "endColumn": 13}, {"ruleId": "403", "severity": 1, "message": "469", "line": 202, "column": 6, "nodeType": "405", "endLine": 202, "endColumn": 61, "suggestions": "470"}, {"ruleId": "407", "severity": 1, "message": "465", "line": 20, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 20, "endColumn": 10}, {"ruleId": "407", "severity": 1, "message": "471", "line": 31, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 31, "endColumn": 17}, {"ruleId": "407", "severity": 1, "message": "472", "line": 39, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 39, "endColumn": 16}, {"ruleId": "403", "severity": 1, "message": "473", "line": 158, "column": 6, "nodeType": "405", "endLine": 158, "endColumn": 8, "suggestions": "474"}, {"ruleId": "403", "severity": 1, "message": "475", "line": 168, "column": 6, "nodeType": "405", "endLine": 168, "endColumn": 27, "suggestions": "476"}, {"ruleId": "403", "severity": 1, "message": "475", "line": 172, "column": 6, "nodeType": "405", "endLine": 172, "endColumn": 34, "suggestions": "477"}, {"ruleId": "403", "severity": 1, "message": "475", "line": 171, "column": 6, "nodeType": "405", "endLine": 171, "endColumn": 30, "suggestions": "478"}, {"ruleId": "403", "severity": 1, "message": "475", "line": 170, "column": 6, "nodeType": "405", "endLine": 170, "endColumn": 27, "suggestions": "479"}, {"ruleId": "407", "severity": 1, "message": "465", "line": 11, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 11, "endColumn": 10}, {"ruleId": "407", "severity": 1, "message": "464", "line": 14, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 14, "endColumn": 8}, {"ruleId": "407", "severity": 1, "message": "480", "line": 27, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 27, "endColumn": 17}, {"ruleId": "407", "severity": 1, "message": "481", "line": 32, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 32, "endColumn": 13}, {"ruleId": "407", "severity": 1, "message": "482", "line": 24, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 24, "endColumn": 17}, {"ruleId": "407", "severity": 1, "message": "483", "line": 22, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 22, "endColumn": 16}, {"ruleId": "407", "severity": 1, "message": "465", "line": 19, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 19, "endColumn": 10}, {"ruleId": "407", "severity": 1, "message": "484", "line": 26, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 26, "endColumn": 22}, {"ruleId": "407", "severity": 1, "message": "485", "line": 34, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 34, "endColumn": 14}, {"ruleId": "407", "severity": 1, "message": "486", "line": 34, "column": 22, "nodeType": "409", "messageId": "410", "endLine": 34, "endColumn": 31}, {"ruleId": "407", "severity": 1, "message": "459", "line": 35, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 35, "endColumn": 17}, {"ruleId": "407", "severity": 1, "message": "487", "line": 41, "column": 10, "nodeType": "409", "messageId": "410", "endLine": 41, "endColumn": 22}, {"ruleId": "407", "severity": 1, "message": "488", "line": 41, "column": 24, "nodeType": "409", "messageId": "410", "endLine": 41, "endColumn": 39}, {"ruleId": "407", "severity": 1, "message": "489", "line": 45, "column": 10, "nodeType": "409", "messageId": "410", "endLine": 45, "endColumn": 21}, {"ruleId": "403", "severity": 1, "message": "490", "line": 176, "column": 6, "nodeType": "405", "endLine": 176, "endColumn": 8, "suggestions": "491"}, {"ruleId": "407", "severity": 1, "message": "492", "line": 95, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 95, "endColumn": 26}, {"ruleId": "407", "severity": 1, "message": "493", "line": 111, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 111, "endColumn": 26}, {"ruleId": "407", "severity": 1, "message": "465", "line": 13, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 13, "endColumn": 10}, {"ruleId": "407", "severity": 1, "message": "465", "line": 10, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 10, "endColumn": 10}, {"ruleId": "407", "severity": 1, "message": "494", "line": 19, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 19, "endColumn": 15}, {"ruleId": "407", "severity": 1, "message": "495", "line": 20, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 20, "endColumn": 14}, {"ruleId": "407", "severity": 1, "message": "496", "line": 21, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 21, "endColumn": 17}, {"ruleId": "407", "severity": 1, "message": "484", "line": 22, "column": 3, "nodeType": "409", "messageId": "410", "endLine": 22, "endColumn": 22}, {"ruleId": "407", "severity": 1, "message": "468", "line": 24, "column": 8, "nodeType": "409", "messageId": "410", "endLine": 24, "endColumn": 13}, {"ruleId": "407", "severity": 1, "message": "466", "line": 26, "column": 16, "nodeType": "409", "messageId": "410", "endLine": 26, "endColumn": 20}, {"ruleId": "407", "severity": 1, "message": "466", "line": 7, "column": 16, "nodeType": "409", "messageId": "410", "endLine": 7, "endColumn": 20}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch', 'isAuthenticated', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["497"], "@typescript-eslint/no-unused-vars", "'isMobile' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["498"], "React Hook useEffect has a missing dependency: 'fetchPermissions'. Either include it or remove the dependency array.", ["499"], ["500"], "React Hook useEffect has a missing dependency: 'fetchRoles'. Either include it or remove the dependency array.", ["501"], ["502"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["503"], "'EventDataNode' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["504"], "React Hook useEffect has missing dependencies: 'findCategoryById' and 'selectedCategory'. Either include them or remove the dependency array.", ["505"], ["506"], ["507"], "React Hook useEffect has a missing dependency: 'fetchBidRecords'. Either include it or remove the dependency array.", ["508"], "React Hook useEffect has a missing dependency: 'fetchCurrentAuction'. Either include it or remove the dependency array.", ["509"], "The ref value 'wsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'wsRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'fetchProducts' is assigned a value but never used.", "'fetchAuctions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAuctionItems'. Either include it or remove the dependency array.", ["510"], ["511"], "React Hook useEffect has a missing dependency: 'fetchAuctions'. Either include it or remove the dependency array.", ["512"], ["513"], "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["514"], "React Hook useEffect has a missing dependency: 'fetchShippings'. Either include it or remove the dependency array.", ["515"], "'handleUpdateStatus' is assigned a value but never used.", "'ColumnsType' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'confirm' is assigned a value but never used.", "'FallOutlined' is defined but never used.", "'dailyReport' is assigned a value but never used.", "'monthlyReport' is assigned a value but never used.", "'mockFinanceData' is assigned a value but never used.", "'mockRevenueAnalysis' is assigned a value but never used.", "'mockCommissionRecords' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'dateRange'. Either exclude it or remove the dependency array.", ["516"], "'Tooltip' is defined but never used.", "'UserDeleteOutlined' is defined but never used.", "'TextArea' is assigned a value but never used.", "'Option' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchConfig'. Either include it or remove the dependency array.", ["517"], "'Upload' is defined but never used.", "'Image' is defined but never used.", "'Divider' is defined but never used.", "'Text' is assigned a value but never used.", ["518"], "'dayjs' is defined but never used.", "React Hook useEffect has an unnecessary dependency: 'pagination.current'. Either exclude it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["519"], "'UploadOutlined' is defined but never used.", "'Dragger' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'config' and 'configForm'. Either include them or remove the dependency array.", ["520"], "React Hook useCallback has a missing dependency: 'loading'. Either include it or remove the dependency array.", ["521"], ["522"], ["523"], ["524"], "'SafetyOutlined' is defined but never used.", "'Link' is assigned a value but never used.", "'FilterOutlined' is defined but never used.", "'SmileOutlined' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'Title' is assigned a value but never used.", "'Paragraph' is assigned a value but never used.", "'participants' is assigned a value but never used.", "'setParticipants' is assigned a value but never used.", "'currentUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'auctionStatus'. Either include it or remove the dependency array. You can also do a functional update 'setAuctionStatus(a => ...)' if you only need 'auctionStatus' in the 'setAuctionStatus' call.", ["525"], "'handleChangePhone' is assigned a value but never used.", "'handleChangeEmail' is assigned a value but never used.", "'BellOutlined' is defined but never used.", "'EyeOutlined' is defined but never used.", "'GlobalOutlined' is defined but never used.", {"desc": "526", "fix": "527"}, {"desc": "528", "fix": "529"}, {"desc": "530", "fix": "531"}, {"kind": "532", "justification": "533"}, {"desc": "534", "fix": "535"}, {"kind": "532", "justification": "533"}, {"desc": "536", "fix": "537"}, {"desc": "538", "fix": "539"}, {"desc": "540", "fix": "541"}, {"desc": "536", "fix": "542"}, {"kind": "532", "justification": "533"}, {"desc": "543", "fix": "544"}, {"desc": "545", "fix": "546"}, {"desc": "547", "fix": "548"}, {"kind": "532", "justification": "533"}, {"desc": "549", "fix": "550"}, {"kind": "532", "justification": "533"}, {"desc": "551", "fix": "552"}, {"desc": "553", "fix": "554"}, {"desc": "555", "fix": "556"}, {"desc": "557", "fix": "558"}, {"desc": "557", "fix": "559"}, {"desc": "560", "fix": "561"}, {"desc": "562", "fix": "563"}, {"desc": "564", "fix": "565"}, {"desc": "566", "fix": "567"}, {"desc": "568", "fix": "569"}, {"desc": "570", "fix": "571"}, {"desc": "572", "fix": "573"}, "Update the dependencies array to be: [dispatch, isAuthenticated, user]", {"range": "574", "text": "575"}, "Update the dependencies array to be: [fetchUsers, queryParams]", {"range": "576", "text": "577"}, "Update the dependencies array to be: [fetchPermissions]", {"range": "578", "text": "579"}, "directive", "", "Update the dependencies array to be: [fetchRoles, queryParams]", {"range": "580", "text": "581"}, "Update the dependencies array to be: [fetchProducts, queryParams]", {"range": "582", "text": "583"}, "Update the dependencies array to be: [fetchCategories]", {"range": "584", "text": "585"}, "Update the dependencies array to be: [categories, findCategoryById, selectedCategory]", {"range": "586", "text": "587"}, {"range": "588", "text": "583"}, "Update the dependencies array to be: [fetchBidRecords, queryParams]", {"range": "589", "text": "590"}, "Update the dependencies array to be: [fetchCurrentAuction]", {"range": "591", "text": "592"}, "Update the dependencies array to be: [fetchAuctionItems, queryParams]", {"range": "593", "text": "594"}, "Update the dependencies array to be: [fetchAuctions, queryParams]", {"range": "595", "text": "596"}, "Update the dependencies array to be: [fetchOrders, queryParams]", {"range": "597", "text": "598"}, "Update the dependencies array to be: [fetchShippings, queryParams]", {"range": "599", "text": "600"}, "Update the dependencies array to be: [reportType]", {"range": "601", "text": "602"}, "Update the dependencies array to be: [fetchConfig]", {"range": "603", "text": "604"}, {"range": "605", "text": "604"}, "Update the dependencies array to be: [pagination.pageSize, searchParams]", {"range": "606", "text": "607"}, "Update the dependencies array to be: [config, configForm]", {"range": "608", "text": "609"}, "Update the dependencies array to be: [dateRange, loading, userType]", {"range": "610", "text": "611"}, "Update the dependencies array to be: [dateRange, loading, timeGranularity]", {"range": "612", "text": "613"}, "Update the dependencies array to be: [loading, dateRange, auctionType]", {"range": "614", "text": "615"}, "Update the dependencies array to be: [loading, dateRange, category]", {"range": "616", "text": "617"}, "Update the dependencies array to be: [auctionStatus]", {"range": "618", "text": "619"}, [3428, 3430], "[dispatch, isAuthenticated, user]", [2988, 3001], "[fetchUsers, queryParams]", [7154, 7156], "[fetchPermissions]", [7284, 7297], "[fetchRoles, queryParams]", [3549, 3562], "[fetchProducts, queryParams]", [4889, 4891], "[fetchCategories]", [5254, 5266], "[categories, findCategoryById, selectedCategory]", [5023, 5036], [6572, 6585], "[fetchBidRecords, queryParams]", [7577, 7579], "[fetchCurrentAuction]", [15069, 15082], "[fetchAuctionItems, queryParams]", [6078, 6091], "[fetchAuctions, queryParams]", [4374, 4387], "[fetchOrders, queryParams]", [4760, 4773], "[fetchShippings, queryParams]", [4548, 4571], "[reportType]", [3823, 3825], "[fetchConfig]", [5097, 5099], [4321, 4376], "[pagination.pageSize, searchParams]", [3877, 3879], "[config, configForm]", [3695, 3716], "[dateRange, loading, userType]", [3831, 3859], "[dateRange, loading, timeGranularity]", [3850, 3874], "[loading, dateRange, auctionType]", [3807, 3828], "[loading, dateRange, category]", [4734, 4736], "[auctionStatus]"]