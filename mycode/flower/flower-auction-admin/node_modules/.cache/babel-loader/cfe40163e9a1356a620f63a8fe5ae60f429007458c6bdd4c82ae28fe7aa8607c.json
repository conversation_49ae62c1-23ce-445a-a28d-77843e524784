{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/UserInfo/index.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Card, Avatar, Typography, Tag, Descriptions } from 'antd';\nimport { UserOutlined, TeamOutlined, MailOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst UserInfo = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n\n  // 角色映射\n  const roleMap = {\n    'admin': {\n      label: '管理员',\n      color: 'red'\n    },\n    'seller': {\n      label: '卖家',\n      color: 'blue'\n    },\n    'buyer': {\n      label: '买家',\n      color: 'green'\n    },\n    'quality_inspector': {\n      label: '质检员',\n      color: 'orange'\n    }\n  };\n\n  // 如果没有用户信息，显示加载中\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      className: \"user-info-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-info-loading\",\n        children: \"\\u52A0\\u8F7D\\u7528\\u6237\\u4FE1\\u606F\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n  const userRole = roleMap[user.role] || {\n    label: '未知',\n    color: 'default'\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"user-info-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-info-header\",\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        size: 64,\n        icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 33\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-info-title\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: user.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: userRole.color,\n          children: userRole.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Descriptions, {\n      column: 1,\n      className: \"user-info-details\",\n      children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n        label: /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 37\n          }, this), \" \\u7528\\u6237ID\"]\n        }, void 0, true),\n        children: user.id\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n        label: /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 37\n          }, this), \" \\u89D2\\u8272\"]\n        }, void 0, true),\n        children: /*#__PURE__*/_jsxDEV(Tag, {\n          color: userRole.color,\n          children: userRole.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n        label: /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 37\n          }, this), \" \\u90AE\\u7BB1\"]\n        }, void 0, true),\n        children: user.email || '未设置'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n        label: \"\\u771F\\u5B9E\\u59D3\\u540D\",\n        children: user.realName || '未设置'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(UserInfo, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = UserInfo;\nexport default UserInfo;\nvar _c;\n$RefreshReg$(_c, \"UserInfo\");", "map": {"version": 3, "names": ["React", "Card", "Avatar", "Typography", "Tag", "Descriptions", "UserOutlined", "TeamOutlined", "MailOutlined", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "UserInfo", "_s", "user", "roleMap", "label", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "userRole", "role", "size", "icon", "level", "username", "column", "<PERSON><PERSON>", "id", "email", "realName", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/UserInfo/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, Avatar, Typography, Tag, Descriptions } from 'antd';\nimport { UserOutlined, TeamOutlined, MailOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\n\nconst { Title, Text } = Typography;\n\nconst UserInfo: React.FC = () => {\n  const { user } = useAuth();\n\n  // 角色映射\n  const roleMap: Record<string, { label: string; color: string }> = {\n    'admin': { label: '管理员', color: 'red' },\n    'seller': { label: '卖家', color: 'blue' },\n    'buyer': { label: '买家', color: 'green' },\n    'quality_inspector': { label: '质检员', color: 'orange' },\n  };\n\n  // 如果没有用户信息，显示加载中\n  if (!user) {\n    return (\n      <Card className=\"user-info-card\">\n        <div className=\"user-info-loading\">加载用户信息中...</div>\n      </Card>\n    );\n  }\n\n  const userRole = roleMap[user.role] || { label: '未知', color: 'default' };\n\n  return (\n    <Card className=\"user-info-card\">\n      <div className=\"user-info-header\">\n        <Avatar size={64} icon={<UserOutlined />} />\n        <div className=\"user-info-title\">\n          <Title level={4}>{user.username}</Title>\n          <Tag color={userRole.color}>{userRole.label}</Tag>\n        </div>\n      </div>\n\n      <Descriptions column={1} className=\"user-info-details\">\n        <Descriptions.Item label={<><UserOutlined /> 用户ID</>}>\n          {user.id}\n        </Descriptions.Item>\n        <Descriptions.Item label={<><TeamOutlined /> 角色</>}>\n          <Tag color={userRole.color}>{userRole.label}</Tag>\n        </Descriptions.Item>\n        <Descriptions.Item label={<><MailOutlined /> 邮箱</>}>\n          {user.email || '未设置'}\n        </Descriptions.Item>\n        <Descriptions.Item label=\"真实姓名\">\n          {user.realName || '未设置'}\n        </Descriptions.Item>\n      </Descriptions>\n    </Card>\n  );\n};\n\nexport default UserInfo;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEC,GAAG,EAAEC,YAAY,QAAQ,MAAM;AAClE,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC5E,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGZ,UAAU;AAElC,MAAMa,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAMU,OAAyD,GAAG;IAChE,OAAO,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC;IACvC,QAAQ,EAAE;MAAED,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxC,OAAO,EAAE;MAAED,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACxC,mBAAmB,EAAE;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS;EACvD,CAAC;;EAED;EACA,IAAI,CAACH,IAAI,EAAE;IACT,oBACEP,OAAA,CAACV,IAAI;MAACqB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC9BZ,OAAA;QAAKW,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEX;EAEA,MAAMC,QAAQ,GAAGT,OAAO,CAACD,IAAI,CAACW,IAAI,CAAC,IAAI;IAAET,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC;EAExE,oBACEV,OAAA,CAACV,IAAI;IAACqB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC9BZ,OAAA;MAAKW,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BZ,OAAA,CAACT,MAAM;QAAC4B,IAAI,EAAE,EAAG;QAACC,IAAI,eAAEpB,OAAA,CAACL,YAAY;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5ChB,OAAA;QAAKW,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BZ,OAAA,CAACG,KAAK;UAACkB,KAAK,EAAE,CAAE;UAAAT,QAAA,EAAEL,IAAI,CAACe;QAAQ;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxChB,OAAA,CAACP,GAAG;UAACiB,KAAK,EAAEO,QAAQ,CAACP,KAAM;UAAAE,QAAA,EAAEK,QAAQ,CAACR;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhB,OAAA,CAACN,YAAY;MAAC6B,MAAM,EAAE,CAAE;MAACZ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBACpDZ,OAAA,CAACN,YAAY,CAAC8B,IAAI;QAACf,KAAK,eAAET,OAAA,CAAAE,SAAA;UAAAU,QAAA,gBAAEZ,OAAA,CAACL,YAAY;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAAK;QAAA,eAAE,CAAE;QAAAJ,QAAA,EAClDL,IAAI,CAACkB;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACpBhB,OAAA,CAACN,YAAY,CAAC8B,IAAI;QAACf,KAAK,eAAET,OAAA,CAAAE,SAAA;UAAAU,QAAA,gBAAEZ,OAAA,CAACJ,YAAY;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAAG;QAAA,eAAE,CAAE;QAAAJ,QAAA,eACjDZ,OAAA,CAACP,GAAG;UAACiB,KAAK,EAAEO,QAAQ,CAACP,KAAM;UAAAE,QAAA,EAAEK,QAAQ,CAACR;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACpBhB,OAAA,CAACN,YAAY,CAAC8B,IAAI;QAACf,KAAK,eAAET,OAAA,CAAAE,SAAA;UAAAU,QAAA,gBAAEZ,OAAA,CAACH,YAAY;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAAG;QAAA,eAAE,CAAE;QAAAJ,QAAA,EAChDL,IAAI,CAACmB,KAAK,IAAI;MAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACpBhB,OAAA,CAACN,YAAY,CAAC8B,IAAI;QAACf,KAAK,EAAC,0BAAM;QAAAG,QAAA,EAC5BL,IAAI,CAACoB,QAAQ,IAAI;MAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEX,CAAC;AAACV,EAAA,CAhDID,QAAkB;EAAA,QACLP,OAAO;AAAA;AAAA8B,EAAA,GADpBvB,QAAkB;AAkDxB,eAAeA,QAAQ;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}