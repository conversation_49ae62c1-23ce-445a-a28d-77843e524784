{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport AnchorContext from './context';\nconst AnchorLink = props => {\n  const {\n    href,\n    title,\n    prefixCls: customizePrefixCls,\n    children,\n    className,\n    target,\n    replace\n  } = props;\n  const context = React.useContext(AnchorContext);\n  const {\n    registerLink,\n    unregisterLink,\n    scrollTo,\n    onClick,\n    activeLink,\n    direction\n  } = context || {};\n  React.useEffect(() => {\n    registerLink === null || registerLink === void 0 ? void 0 : registerLink(href);\n    return () => {\n      unregisterLink === null || unregisterLink === void 0 ? void 0 : unregisterLink(href);\n    };\n  }, [href]);\n  const handleClick = e => {\n    onClick === null || onClick === void 0 ? void 0 : onClick(e, {\n      title,\n      href\n    });\n    scrollTo === null || scrollTo === void 0 ? void 0 : scrollTo(href);\n    // Support clicking on an anchor does not record history.\n    if (e.defaultPrevented) {\n      return;\n    }\n    const isExternalLink = href.startsWith('http://') || href.startsWith('https://');\n    // Support external link\n    if (isExternalLink) {\n      if (replace) {\n        e.preventDefault();\n        window.location.replace(href);\n      }\n      return;\n    }\n    // Handling internal anchor link\n    e.preventDefault();\n    const historyMethod = replace ? 'replaceState' : 'pushState';\n    window.history[historyMethod](null, '', href);\n  };\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Anchor.Link');\n    process.env.NODE_ENV !== \"production\" ? warning(!children || direction !== 'horizontal', 'usage', '`Anchor.Link children` is not supported when `Anchor` direction is horizontal') : void 0;\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('anchor', customizePrefixCls);\n  const active = activeLink === href;\n  const wrapperClassName = classNames(`${prefixCls}-link`, className, {\n    [`${prefixCls}-link-active`]: active\n  });\n  const titleClassName = classNames(`${prefixCls}-link-title`, {\n    [`${prefixCls}-link-title-active`]: active\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: wrapperClassName\n  }, /*#__PURE__*/React.createElement(\"a\", {\n    className: titleClassName,\n    href: href,\n    title: typeof title === 'string' ? title : '',\n    target: target,\n    onClick: handleClick\n  }, title), direction !== 'horizontal' ? children : null);\n};\nexport default AnchorLink;", "map": {"version": 3, "names": ["React", "classNames", "devUseW<PERSON>ning", "ConfigContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AnchorLink", "props", "href", "title", "prefixCls", "customizePrefixCls", "children", "className", "target", "replace", "context", "useContext", "registerLink", "unregisterLink", "scrollTo", "onClick", "activeLink", "direction", "useEffect", "handleClick", "e", "defaultPrevented", "isExternalLink", "startsWith", "preventDefault", "window", "location", "history<PERSON>ethod", "history", "process", "env", "NODE_ENV", "warning", "getPrefixCls", "active", "wrapperClassName", "titleClassName", "createElement"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/anchor/AnchorLink.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport AnchorContext from './context';\nconst AnchorLink = props => {\n  const {\n    href,\n    title,\n    prefixCls: customizePrefixCls,\n    children,\n    className,\n    target,\n    replace\n  } = props;\n  const context = React.useContext(AnchorContext);\n  const {\n    registerLink,\n    unregisterLink,\n    scrollTo,\n    onClick,\n    activeLink,\n    direction\n  } = context || {};\n  React.useEffect(() => {\n    registerLink === null || registerLink === void 0 ? void 0 : registerLink(href);\n    return () => {\n      unregisterLink === null || unregisterLink === void 0 ? void 0 : unregisterLink(href);\n    };\n  }, [href]);\n  const handleClick = e => {\n    onClick === null || onClick === void 0 ? void 0 : onClick(e, {\n      title,\n      href\n    });\n    scrollTo === null || scrollTo === void 0 ? void 0 : scrollTo(href);\n    // Support clicking on an anchor does not record history.\n    if (e.defaultPrevented) {\n      return;\n    }\n    const isExternalLink = href.startsWith('http://') || href.startsWith('https://');\n    // Support external link\n    if (isExternalLink) {\n      if (replace) {\n        e.preventDefault();\n        window.location.replace(href);\n      }\n      return;\n    }\n    // Handling internal anchor link\n    e.preventDefault();\n    const historyMethod = replace ? 'replaceState' : 'pushState';\n    window.history[historyMethod](null, '', href);\n  };\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Anchor.Link');\n    process.env.NODE_ENV !== \"production\" ? warning(!children || direction !== 'horizontal', 'usage', '`Anchor.Link children` is not supported when `Anchor` direction is horizontal') : void 0;\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('anchor', customizePrefixCls);\n  const active = activeLink === href;\n  const wrapperClassName = classNames(`${prefixCls}-link`, className, {\n    [`${prefixCls}-link-active`]: active\n  });\n  const titleClassName = classNames(`${prefixCls}-link-title`, {\n    [`${prefixCls}-link-title-active`]: active\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: wrapperClassName\n  }, /*#__PURE__*/React.createElement(\"a\", {\n    className: titleClassName,\n    href: href,\n    title: typeof title === 'string' ? title : '',\n    target: target,\n    onClick: handleClick\n  }, title), direction !== 'horizontal' ? children : null);\n};\nexport default AnchorLink;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,aAAa,MAAM,WAAW;AACrC,MAAMC,UAAU,GAAGC,KAAK,IAAI;EAC1B,MAAM;IACJC,IAAI;IACJC,KAAK;IACLC,SAAS,EAAEC,kBAAkB;IAC7BC,QAAQ;IACRC,SAAS;IACTC,MAAM;IACNC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,OAAO,GAAGf,KAAK,CAACgB,UAAU,CAACZ,aAAa,CAAC;EAC/C,MAAM;IACJa,YAAY;IACZC,cAAc;IACdC,QAAQ;IACRC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,GAAGP,OAAO,IAAI,CAAC,CAAC;EACjBf,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpBN,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACV,IAAI,CAAC;IAC9E,OAAO,MAAM;MACXW,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACX,IAAI,CAAC;IACtF,CAAC;EACH,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,MAAMiB,WAAW,GAAGC,CAAC,IAAI;IACvBL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,CAAC,EAAE;MAC3DjB,KAAK;MACLD;IACF,CAAC,CAAC;IACFY,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACZ,IAAI,CAAC;IAClE;IACA,IAAIkB,CAAC,CAACC,gBAAgB,EAAE;MACtB;IACF;IACA,MAAMC,cAAc,GAAGpB,IAAI,CAACqB,UAAU,CAAC,SAAS,CAAC,IAAIrB,IAAI,CAACqB,UAAU,CAAC,UAAU,CAAC;IAChF;IACA,IAAID,cAAc,EAAE;MAClB,IAAIb,OAAO,EAAE;QACXW,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBC,MAAM,CAACC,QAAQ,CAACjB,OAAO,CAACP,IAAI,CAAC;MAC/B;MACA;IACF;IACA;IACAkB,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMG,aAAa,GAAGlB,OAAO,GAAG,cAAc,GAAG,WAAW;IAC5DgB,MAAM,CAACG,OAAO,CAACD,aAAa,CAAC,CAAC,IAAI,EAAE,EAAE,EAAEzB,IAAI,CAAC;EAC/C,CAAC;EACD;EACA,IAAI2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGnC,aAAa,CAAC,aAAa,CAAC;IAC5CgC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,CAAC1B,QAAQ,IAAIW,SAAS,KAAK,YAAY,EAAE,OAAO,EAAE,+EAA+E,CAAC,GAAG,KAAK,CAAC;EAC7L;EACA,MAAM;IACJgB;EACF,CAAC,GAAGtC,KAAK,CAACgB,UAAU,CAACb,aAAa,CAAC;EACnC,MAAMM,SAAS,GAAG6B,YAAY,CAAC,QAAQ,EAAE5B,kBAAkB,CAAC;EAC5D,MAAM6B,MAAM,GAAGlB,UAAU,KAAKd,IAAI;EAClC,MAAMiC,gBAAgB,GAAGvC,UAAU,CAAC,GAAGQ,SAAS,OAAO,EAAEG,SAAS,EAAE;IAClE,CAAC,GAAGH,SAAS,cAAc,GAAG8B;EAChC,CAAC,CAAC;EACF,MAAME,cAAc,GAAGxC,UAAU,CAAC,GAAGQ,SAAS,aAAa,EAAE;IAC3D,CAAC,GAAGA,SAAS,oBAAoB,GAAG8B;EACtC,CAAC,CAAC;EACF,OAAO,aAAavC,KAAK,CAAC0C,aAAa,CAAC,KAAK,EAAE;IAC7C9B,SAAS,EAAE4B;EACb,CAAC,EAAE,aAAaxC,KAAK,CAAC0C,aAAa,CAAC,GAAG,EAAE;IACvC9B,SAAS,EAAE6B,cAAc;IACzBlC,IAAI,EAAEA,IAAI;IACVC,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,EAAE;IAC7CK,MAAM,EAAEA,MAAM;IACdO,OAAO,EAAEI;EACX,CAAC,EAAEhB,KAAK,CAAC,EAAEc,SAAS,KAAK,YAAY,GAAGX,QAAQ,GAAG,IAAI,CAAC;AAC1D,CAAC;AACD,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}