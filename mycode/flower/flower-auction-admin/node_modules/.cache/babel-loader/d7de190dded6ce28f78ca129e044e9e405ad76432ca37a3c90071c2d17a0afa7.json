{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/SideMenu/index.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Layout, Menu } from 'antd';\nimport { DashboardOutlined, UserOutlined, ShoppingOutlined, AuditOutlined, OrderedListOutlined, BankOutlined, SettingOutlined, ProfileOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\nconst SideMenu = ({\n  collapsed,\n  onCollapse\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const menuItems = [{\n    key: '/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: '/profile',\n    icon: /*#__PURE__*/_jsxDEV(ProfileOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this),\n    label: '个人中心',\n    children: [{\n      key: '/profile/info',\n      label: '个人信息'\n    }, {\n      key: '/profile/settings',\n      label: '个人设置'\n    }, {\n      key: '/profile/security',\n      label: '安全设置'\n    }, {\n      key: '/profile/notifications',\n      label: '消息通知'\n    }]\n  }, {\n    key: '/users',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this),\n    label: '用户管理',\n    children: [{\n      key: '/users/list',\n      label: '用户列表'\n    }, {\n      key: '/users/roles',\n      label: '角色管理'\n    }]\n  }, {\n    key: '/products',\n    icon: /*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this),\n    label: '商品管理',\n    children: [{\n      key: '/products/list',\n      label: '商品列表'\n    }, {\n      key: '/products/categories',\n      label: '分类管理'\n    }, {\n      key: '/products/audit',\n      label: '商品审核'\n    }]\n  }, {\n    key: '/auctions',\n    icon: /*#__PURE__*/_jsxDEV(AuditOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this),\n    label: '拍卖管理',\n    children: [{\n      key: '/auctions/list',\n      label: '拍卖会列表'\n    }, {\n      key: '/auctions/items',\n      label: '拍卖商品'\n    }, {\n      key: '/auctions/live',\n      label: '实时竞价'\n    }, {\n      key: '/auctions/bids',\n      label: '竞价记录'\n    }]\n  }, {\n    key: '/orders',\n    icon: /*#__PURE__*/_jsxDEV(OrderedListOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this),\n    label: '订单管理',\n    children: [{\n      key: '/orders/list',\n      label: '订单列表'\n    }, {\n      key: '/orders/shipping',\n      label: '物流管理'\n    }]\n  }, {\n    key: '/finance',\n    icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 13\n    }, this),\n    label: '财务管理',\n    children: [{\n      key: '/finance/accounts',\n      label: '账户管理'\n    }, {\n      key: '/finance/transactions',\n      label: '交易记录'\n    }, {\n      key: '/finance/reports',\n      label: '财务报表'\n    }]\n  }, {\n    key: '/settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 13\n    }, this),\n    label: '系统设置',\n    children: [{\n      key: '/settings/system',\n      label: '系统配置'\n    }, {\n      key: '/settings/security',\n      label: '安全设置'\n    }, {\n      key: '/settings/system-logs',\n      label: '系统日志'\n    }, {\n      key: '/settings/backup-restore',\n      label: '备份恢复'\n    }, {\n      key: '/settings/logs',\n      label: '操作日志'\n    }]\n  }, {\n    key: '/reports',\n    icon: /*#__PURE__*/_jsxDEV(AuditOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 13\n    }, this),\n    label: '报表中心',\n    children: [{\n      key: '/reports/sales',\n      label: '销售报表'\n    }, {\n      key: '/reports/users',\n      label: '用户报表'\n    }, {\n      key: '/reports/products',\n      label: '商品报表'\n    }, {\n      key: '/reports/auctions',\n      label: '拍卖报表'\n    }]\n  }, {\n    key: '/help',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 13\n    }, this),\n    label: '帮助中心',\n    children: [{\n      key: '/help/docs',\n      label: '使用文档'\n    }, {\n      key: '/help/faq',\n      label: '常见问题'\n    }, {\n      key: '/help/contact',\n      label: '联系我们'\n    }]\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n\n  // 获取当前选中的菜单项\n  const getSelectedKeys = () => {\n    const pathname = location.pathname;\n    return [pathname];\n  };\n\n  // 获取当前展开的子菜单\n  const getOpenKeys = () => {\n    const pathname = location.pathname;\n    const parts = pathname.split('/').filter(Boolean);\n    if (parts.length >= 1) {\n      return [`/${parts[0]}`];\n    }\n    return [];\n  };\n  return /*#__PURE__*/_jsxDEV(Sider, {\n    collapsible: true,\n    collapsed: collapsed,\n    onCollapse: onCollapse,\n    className: \"side-menu\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo\",\n      children: !collapsed ? '昆明花卉拍卖系统' : '花卉'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      theme: \"dark\",\n      mode: \"inline\",\n      selectedKeys: getSelectedKeys(),\n      defaultOpenKeys: getOpenKeys(),\n      items: menuItems,\n      onClick: handleMenuClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 248,\n    columnNumber: 5\n  }, this);\n};\n_s(SideMenu, \"VDZHUspDq9N5O9RWjniBrjgIdAA=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = SideMenu;\nexport default SideMenu;\nvar _c;\n$RefreshReg$(_c, \"SideMenu\");", "map": {"version": 3, "names": ["React", "Layout", "<PERSON><PERSON>", "DashboardOutlined", "UserOutlined", "ShoppingOutlined", "AuditOutlined", "OrderedListOutlined", "BankOutlined", "SettingOutlined", "ProfileOutlined", "useNavigate", "useLocation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "SideMenu", "collapsed", "onCollapse", "_s", "navigate", "location", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "children", "handleMenuClick", "getSelectedKeys", "pathname", "get<PERSON><PERSON><PERSON><PERSON>s", "parts", "split", "filter", "Boolean", "length", "collapsible", "className", "theme", "mode", "<PERSON><PERSON><PERSON><PERSON>", "defaultOpenKeys", "items", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/SideMenu/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { Layout, Menu } from 'antd';\nimport {\n  DashboardOutlined,\n  UserOutlined,\n  ShoppingOutlined,\n  AuditOutlined,\n  OrderedListOutlined,\n  BankOutlined,\n  SettingOutlined,\n  ProfileOutlined,\n  QuestionCircleOutlined,\n  BarChartOutlined,\n} from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport './index.css';\n\nconst { Sider } = Layout;\n\ninterface MenuItem {\n  key: string;\n  icon?: React.ReactNode;\n  label: string;\n  children?: MenuItem[];\n}\n\ninterface SideMenuProps {\n  collapsed: boolean;\n  onCollapse?: (collapsed: boolean) => void;\n}\n\nconst SideMenu: React.FC<SideMenuProps> = ({ collapsed, onCollapse }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const menuItems: MenuItem[] = [\n    {\n      key: '/dashboard',\n      icon: <DashboardOutlined />,\n      label: '仪表盘',\n    },\n    {\n      key: '/profile',\n      icon: <ProfileOutlined />,\n      label: '个人中心',\n      children: [\n        {\n          key: '/profile/info',\n          label: '个人信息',\n        },\n        {\n          key: '/profile/settings',\n          label: '个人设置',\n        },\n        {\n          key: '/profile/security',\n          label: '安全设置',\n        },\n        {\n          key: '/profile/notifications',\n          label: '消息通知',\n        },\n      ],\n    },\n    {\n      key: '/users',\n      icon: <UserOutlined />,\n      label: '用户管理',\n      children: [\n        {\n          key: '/users/list',\n          label: '用户列表',\n        },\n        {\n          key: '/users/roles',\n          label: '角色管理',\n        },\n      ],\n    },\n    {\n      key: '/products',\n      icon: <ShoppingOutlined />,\n      label: '商品管理',\n      children: [\n        {\n          key: '/products/list',\n          label: '商品列表',\n        },\n        {\n          key: '/products/categories',\n          label: '分类管理',\n        },\n        {\n          key: '/products/audit',\n          label: '商品审核',\n        },\n      ],\n    },\n    {\n      key: '/auctions',\n      icon: <AuditOutlined />,\n      label: '拍卖管理',\n      children: [\n        {\n          key: '/auctions/list',\n          label: '拍卖会列表',\n        },\n        {\n          key: '/auctions/items',\n          label: '拍卖商品',\n        },\n        {\n          key: '/auctions/live',\n          label: '实时竞价',\n        },\n        {\n          key: '/auctions/bids',\n          label: '竞价记录',\n        },\n      ],\n    },\n    {\n      key: '/orders',\n      icon: <OrderedListOutlined />,\n      label: '订单管理',\n      children: [\n        {\n          key: '/orders/list',\n          label: '订单列表',\n        },\n        {\n          key: '/orders/shipping',\n          label: '物流管理',\n        },\n      ],\n    },\n    {\n      key: '/finance',\n      icon: <BankOutlined />,\n      label: '财务管理',\n      children: [\n        {\n          key: '/finance/accounts',\n          label: '账户管理',\n        },\n        {\n          key: '/finance/transactions',\n          label: '交易记录',\n        },\n        {\n          key: '/finance/reports',\n          label: '财务报表',\n        },\n      ],\n    },\n    {\n      key: '/settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n      children: [\n        {\n          key: '/settings/system',\n          label: '系统配置',\n        },\n        {\n          key: '/settings/security',\n          label: '安全设置',\n        },\n        {\n          key: '/settings/system-logs',\n          label: '系统日志',\n        },\n        {\n          key: '/settings/backup-restore',\n          label: '备份恢复',\n        },\n        {\n          key: '/settings/logs',\n          label: '操作日志',\n        },\n      ],\n    },\n    {\n      key: '/reports',\n      icon: <AuditOutlined />,\n      label: '报表中心',\n      children: [\n        {\n          key: '/reports/sales',\n          label: '销售报表',\n        },\n        {\n          key: '/reports/users',\n          label: '用户报表',\n        },\n        {\n          key: '/reports/products',\n          label: '商品报表',\n        },\n        {\n          key: '/reports/auctions',\n          label: '拍卖报表',\n        },\n      ],\n    },\n    {\n      key: '/help',\n      icon: <SettingOutlined />,\n      label: '帮助中心',\n      children: [\n        {\n          key: '/help/docs',\n          label: '使用文档',\n        },\n        {\n          key: '/help/faq',\n          label: '常见问题',\n        },\n        {\n          key: '/help/contact',\n          label: '联系我们',\n        },\n      ],\n    },\n  ];\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    navigate(key);\n  };\n\n  // 获取当前选中的菜单项\n  const getSelectedKeys = () => {\n    const pathname = location.pathname;\n    return [pathname];\n  };\n\n  // 获取当前展开的子菜单\n  const getOpenKeys = () => {\n    const pathname = location.pathname;\n    const parts = pathname.split('/').filter(Boolean);\n    if (parts.length >= 1) {\n      return [`/${parts[0]}`];\n    }\n    return [];\n  };\n\n  return (\n    <Sider\n      collapsible\n      collapsed={collapsed}\n      onCollapse={onCollapse}\n      className=\"side-menu\"\n    >\n      <div className=\"logo\">\n        {!collapsed ? '昆明花卉拍卖系统' : '花卉'}\n      </div>\n      <Menu\n        theme=\"dark\"\n        mode=\"inline\"\n        selectedKeys={getSelectedKeys()}\n        defaultOpenKeys={getOpenKeys()}\n        items={menuItems}\n        onClick={handleMenuClick}\n      />\n    </Sider>\n  );\n};\n\nexport default SideMenu;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AACnC,SACEC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,mBAAmB,EACnBC,YAAY,EACZC,eAAe,EACfC,eAAe,QAGV,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGd,MAAM;AAcxB,MAAMe,QAAiC,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAMU,SAAqB,GAAG,CAC5B;IACEC,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAEV,OAAA,CAACX,iBAAiB;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEV,OAAA,CAACJ,eAAe;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,eAAe;MACpBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,mBAAmB;MACxBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,mBAAmB;MACxBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,wBAAwB;MAC7BM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEV,OAAA,CAACV,YAAY;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,aAAa;MAClBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,cAAc;MACnBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEV,OAAA,CAACT,gBAAgB;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,sBAAsB;MAC3BM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,iBAAiB;MACtBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEV,OAAA,CAACR,aAAa;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,iBAAiB;MACtBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEV,OAAA,CAACP,mBAAmB;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,cAAc;MACnBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,kBAAkB;MACvBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEV,OAAA,CAACN,YAAY;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,mBAAmB;MACxBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,uBAAuB;MAC5BM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,kBAAkB;MACvBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEV,OAAA,CAACL,eAAe;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,kBAAkB;MACvBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,oBAAoB;MACzBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,uBAAuB;MAC5BM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,0BAA0B;MAC/BM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEV,OAAA,CAACR,aAAa;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,gBAAgB;MACrBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,mBAAmB;MACxBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,mBAAmB;MACxBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,OAAO;IACZC,IAAI,eAAEV,OAAA,CAACL,eAAe;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,YAAY;MACjBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,WAAW;MAChBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,eAAe;MACpBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,CACF;EAED,MAAME,eAAe,GAAGA,CAAC;IAAER;EAAqB,CAAC,KAAK;IACpDH,QAAQ,CAACG,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMS,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,QAAQ,GAAGZ,QAAQ,CAACY,QAAQ;IAClC,OAAO,CAACA,QAAQ,CAAC;EACnB,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMD,QAAQ,GAAGZ,QAAQ,CAACY,QAAQ;IAClC,MAAME,KAAK,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IACjD,IAAIH,KAAK,CAACI,MAAM,IAAI,CAAC,EAAE;MACrB,OAAO,CAAC,IAAIJ,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACA,OAAO,EAAE;EACX,CAAC;EAED,oBACErB,OAAA,CAACC,KAAK;IACJyB,WAAW;IACXvB,SAAS,EAAEA,SAAU;IACrBC,UAAU,EAAEA,UAAW;IACvBuB,SAAS,EAAC,WAAW;IAAAX,QAAA,gBAErBhB,OAAA;MAAK2B,SAAS,EAAC,MAAM;MAAAX,QAAA,EAClB,CAACb,SAAS,GAAG,UAAU,GAAG;IAAI;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eACNd,OAAA,CAACZ,IAAI;MACHwC,KAAK,EAAC,MAAM;MACZC,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAEZ,eAAe,CAAC,CAAE;MAChCa,eAAe,EAAEX,WAAW,CAAC,CAAE;MAC/BY,KAAK,EAAExB,SAAU;MACjByB,OAAO,EAAEhB;IAAgB;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEZ,CAAC;AAACT,EAAA,CA3OIH,QAAiC;EAAA,QACpBL,WAAW,EACXC,WAAW;AAAA;AAAAoC,EAAA,GAFxBhC,QAAiC;AA6OvC,eAAeA,QAAQ;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}