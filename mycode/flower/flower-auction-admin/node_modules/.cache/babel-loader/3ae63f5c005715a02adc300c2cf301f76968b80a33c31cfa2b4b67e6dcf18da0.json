{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SecuritySettings/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Form, Input, InputNumber, Switch, Button, message, Row, Col, Select, Alert, Table, Tag, Space, Modal, Progress } from 'antd';\nimport { SaveOutlined, ReloadOutlined, SecurityScanOutlined, LockOutlined, EyeOutlined, DeleteOutlined, ExclamationCircleOutlined, SafetyOutlined, KeyOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  confirm\n} = Modal;\n\n// 安全配置接口\n\n// 登录记录接口\n\nconst SecuritySettings = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [config, setConfig] = useState({\n    minPasswordLength: 8,\n    requireUppercase: true,\n    requireLowercase: true,\n    requireNumbers: true,\n    requireSpecialChars: false,\n    passwordExpireDays: 90,\n    enableTwoFactor: false,\n    maxLoginAttempts: 5,\n    lockoutDuration: 30,\n    enableCaptcha: true,\n    sessionTimeout: 120,\n    maxConcurrentSessions: 3,\n    enableSessionMonitoring: true,\n    enableIpWhitelist: false,\n    allowedIps: [],\n    enableSecurityLog: true,\n    logRetentionDays: 30\n  });\n  const [loginRecords, setLoginRecords] = useState([]);\n  const [ipModalVisible, setIpModalVisible] = useState(false);\n  const [newIp, setNewIp] = useState('');\n  const [form] = Form.useForm();\n\n  // 获取安全配置\n  const fetchConfig = async () => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API获取配置\n      // const response = await securityService.getConfig();\n      // if (response.success) {\n      //   setConfig(response.data);\n      //   form.setFieldsValue(response.data);\n      // }\n\n      // 暂时使用模拟数据\n      form.setFieldsValue(config);\n      message.success('安全配置加载成功');\n    } catch (error) {\n      console.error('获取安全配置失败:', error);\n      message.error('获取安全配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取登录记录\n  const fetchLoginRecords = async () => {\n    try {\n      // 模拟登录记录数据\n      const mockRecords = [{\n        id: 1,\n        userId: 1,\n        username: 'admin',\n        ip: '*************',\n        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        loginTime: '2023-12-15 09:30:00',\n        status: 'success',\n        location: '北京市'\n      }, {\n        id: 2,\n        userId: 2,\n        username: 'user001',\n        ip: '*************',\n        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n        loginTime: '2023-12-15 08:45:00',\n        status: 'failed',\n        location: '上海市'\n      }];\n      setLoginRecords(mockRecords);\n    } catch (error) {\n      console.error('获取登录记录失败:', error);\n    }\n  };\n  useEffect(() => {\n    fetchConfig();\n    fetchLoginRecords();\n  }, []);\n\n  // 保存配置\n  const handleSave = async values => {\n    setSaving(true);\n    try {\n      const updatedConfig = {\n        ...config,\n        ...values\n      };\n\n      // 这里应该调用后端API保存配置\n      // const response = await securityService.updateConfig(updatedConfig);\n      // if (response.success) {\n      //   setConfig(updatedConfig);\n      //   message.success('安全配置保存成功');\n      // }\n\n      // 暂时使用模拟保存\n      setConfig(updatedConfig);\n      message.success('安全配置保存成功');\n    } catch (error) {\n      console.error('保存安全配置失败:', error);\n      message.error('保存安全配置失败');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 添加IP白名单\n  const handleAddIp = () => {\n    if (!newIp) {\n      message.error('请输入IP地址');\n      return;\n    }\n\n    // 简单的IP格式验证\n    const ipRegex = /^(\\d{1,3}\\.){3}\\d{1,3}$/;\n    if (!ipRegex.test(newIp)) {\n      message.error('请输入有效的IP地址');\n      return;\n    }\n    if (config.allowedIps.includes(newIp)) {\n      message.error('该IP地址已存在');\n      return;\n    }\n    const updatedConfig = {\n      ...config,\n      allowedIps: [...config.allowedIps, newIp]\n    };\n    setConfig(updatedConfig);\n    setNewIp('');\n    setIpModalVisible(false);\n    message.success('IP地址添加成功');\n  };\n\n  // 删除IP白名单\n  const handleRemoveIp = ip => {\n    confirm({\n      title: '确认删除',\n      content: `确定要删除IP地址 ${ip} 吗？`,\n      onOk() {\n        const updatedConfig = {\n          ...config,\n          allowedIps: config.allowedIps.filter(item => item !== ip)\n        };\n        setConfig(updatedConfig);\n        message.success('IP地址删除成功');\n      }\n    });\n  };\n\n  // 强制登出所有用户\n  const handleForceLogoutAll = () => {\n    confirm({\n      title: '强制登出所有用户',\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6B64\\u64CD\\u4F5C\\u5C06\\u5F3A\\u5236\\u767B\\u51FA\\u7CFB\\u7EDF\\u4E2D\\u7684\\u6240\\u6709\\u7528\\u6237\\uFF08\\u5305\\u62EC\\u60A8\\u81EA\\u5DF1\\uFF09\\uFF0C\\u786E\\u5B9A\\u8981\\u7EE7\\u7EED\\u5417\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#ff4d4f',\n            fontSize: '14px'\n          },\n          children: \"\\u26A0\\uFE0F \\u8B66\\u544A\\uFF1A\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u64A4\\u9500\\uFF0C\\u6240\\u6709\\u7528\\u6237\\u9700\\u8981\\u91CD\\u65B0\\u767B\\u5F55\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this),\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }, this),\n      okText: '确认强制登出',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          // 这里应该调用后端API强制登出所有用户\n          // await securityService.forceLogoutAll();\n\n          message.success('已强制登出所有用户');\n\n          // 延迟一下后跳转到登录页\n          setTimeout(() => {\n            window.location.href = '/login';\n          }, 2000);\n        } catch (error) {\n          console.error('强制登出失败:', error);\n          message.error('强制登出失败，请重试');\n        }\n      }\n    });\n  };\n\n  // 清除所有登录记录\n  const handleClearLoginRecords = () => {\n    confirm({\n      title: '清除登录记录',\n      content: '确定要清除所有登录记录吗？此操作不可撤销。',\n      onOk() {\n        setLoginRecords([]);\n        message.success('登录记录已清除');\n      }\n    });\n  };\n\n  // 计算密码强度\n  const calculatePasswordStrength = () => {\n    let strength = 0;\n    if (config.minPasswordLength >= 8) strength += 20;\n    if (config.requireUppercase) strength += 20;\n    if (config.requireLowercase) strength += 20;\n    if (config.requireNumbers) strength += 20;\n    if (config.requireSpecialChars) strength += 20;\n    return strength;\n  };\n\n  // 登录记录表格列\n  const loginColumns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ip',\n    key: 'ip'\n  }, {\n    title: '登录时间',\n    dataIndex: 'loginTime',\n    key: 'loginTime'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => {\n      const statusMap = {\n        success: {\n          color: 'green',\n          text: '成功'\n        },\n        failed: {\n          color: 'red',\n          text: '失败'\n        },\n        blocked: {\n          color: 'orange',\n          text: '被阻止'\n        }\n      };\n      const statusInfo = statusMap[status];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: statusInfo.color,\n        children: statusInfo.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '位置',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 19\n        }, this),\n        onClick: () => {\n          Modal.info({\n            title: '登录详情',\n            content: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u7528\\u6237\\u540D:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 24\n                }, this), \" \", record.username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"IP\\u5730\\u5740:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 24\n                }, this), \" \", record.ip]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u7528\\u6237\\u4EE3\\u7406:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 24\n                }, this), \" \", record.userAgent]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u767B\\u5F55\\u65F6\\u95F4:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 24\n                }, this), \" \", record.loginTime]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u4F4D\\u7F6E:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 24\n                }, this), \" \", record.location]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 19\n            }, this)\n          });\n        },\n        children: \"\\u8BE6\\u60C5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(SecurityScanOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), \" \\u5B89\\u5168\\u8BBE\\u7F6E\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 30\n            }, this), \" \\u5B89\\u5168\\u914D\\u7F6E\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 24\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            onFinish: handleSave,\n            initialValues: config,\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u5BC6\\u7801\\u7B56\\u7565\",\n              description: \"\\u914D\\u7F6E\\u7528\\u6237\\u5BC6\\u7801\\u7684\\u5B89\\u5168\\u8981\\u6C42\",\n              type: \"info\",\n              showIcon: true,\n              style: {\n                marginBottom: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"minPasswordLength\",\n                  label: \"\\u6700\\u5C0F\\u5BC6\\u7801\\u957F\\u5EA6\",\n                  rules: [{\n                    required: true,\n                    message: '请输入最小密码长度'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 6,\n                    max: 20,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"passwordExpireDays\",\n                  label: \"\\u5BC6\\u7801\\u8FC7\\u671F\\u5929\\u6570\",\n                  rules: [{\n                    required: true,\n                    message: '请输入密码过期天数'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 30,\n                    max: 365,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"requireUppercase\",\n                  label: \"\\u9700\\u8981\\u5927\\u5199\\u5B57\\u6BCD\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"requireLowercase\",\n                  label: \"\\u9700\\u8981\\u5C0F\\u5199\\u5B57\\u6BCD\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"requireNumbers\",\n                  label: \"\\u9700\\u8981\\u6570\\u5B57\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"requireSpecialChars\",\n                  label: \"\\u9700\\u8981\\u7279\\u6B8A\\u5B57\\u7B26\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 24\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5BC6\\u7801\\u5F3A\\u5EA6: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                percent: calculatePasswordStrength(),\n                size: \"small\",\n                status: calculatePasswordStrength() >= 80 ? 'success' : 'active',\n                style: {\n                  width: 200,\n                  display: 'inline-block',\n                  marginLeft: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u767B\\u5F55\\u5B89\\u5168\",\n              description: \"\\u914D\\u7F6E\\u767B\\u5F55\\u76F8\\u5173\\u7684\\u5B89\\u5168\\u7B56\\u7565\",\n              type: \"info\",\n              showIcon: true,\n              style: {\n                marginBottom: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"maxLoginAttempts\",\n                  label: \"\\u6700\\u5927\\u767B\\u5F55\\u5C1D\\u8BD5\\u6B21\\u6570\",\n                  rules: [{\n                    required: true,\n                    message: '请输入最大登录尝试次数'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 3,\n                    max: 10,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"lockoutDuration\",\n                  label: \"\\u9501\\u5B9A\\u65F6\\u957F\\uFF08\\u5206\\u949F\\uFF09\",\n                  rules: [{\n                    required: true,\n                    message: '请输入锁定时长'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 5,\n                    max: 1440,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"sessionTimeout\",\n                  label: \"\\u4F1A\\u8BDD\\u8D85\\u65F6\\uFF08\\u5206\\u949F\\uFF09\",\n                  rules: [{\n                    required: true,\n                    message: '请输入会话超时时间'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 30,\n                    max: 480,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"enableTwoFactor\",\n                  label: \"\\u542F\\u7528\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"enableCaptcha\",\n                  label: \"\\u542F\\u7528\\u9A8C\\u8BC1\\u7801\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"enableSessionMonitoring\",\n                  label: \"\\u542F\\u7528\\u4F1A\\u8BDD\\u76D1\\u63A7\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 27\n                  }, this),\n                  loading: saving,\n                  children: \"\\u4FDD\\u5B58\\u914D\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 27\n                  }, this),\n                  onClick: fetchConfig,\n                  loading: loading,\n                  children: \"\\u91CD\\u65B0\\u52A0\\u8F7D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 26\n            }, this), \" IP\\u767D\\u540D\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 20\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"small\",\n            onClick: () => setIpModalVisible(true),\n            children: \"\\u6DFB\\u52A0IP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this),\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u542F\\u7528IP\\u767D\\u540D\\u5355\",\n            style: {\n              marginBottom: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Switch, {\n              checked: config.enableIpWhitelist,\n              onChange: checked => setConfig({\n                ...config,\n                enableIpWhitelist: checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this), config.allowedIps.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: config.allowedIps.map((ip, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8,\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                code: true,\n                children: ip\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                danger: true,\n                icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleRemoveIp(ip)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u6682\\u65E0IP\\u767D\\u540D\\u5355\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(KeyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 30\n            }, this), \" \\u5B89\\u5168\\u72B6\\u6001\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 24\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5BC6\\u7801\\u5F3A\\u5EA6: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: calculatePasswordStrength(),\n              size: \"small\",\n              status: calculatePasswordStrength() >= 80 ? 'success' : 'active'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: config.enableTwoFactor ? 'green' : 'red',\n              children: config.enableTwoFactor ? '已启用' : '未启用'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"IP\\u767D\\u540D\\u5355: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: config.enableIpWhitelist ? 'green' : 'red',\n              children: config.enableIpWhitelist ? '已启用' : '未启用'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u4F1A\\u8BDD\\u76D1\\u63A7: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: config.enableSessionMonitoring ? 'green' : 'red',\n              children: config.enableSessionMonitoring ? '已启用' : '未启用'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6700\\u8FD1\\u767B\\u5F55\\u8BB0\\u5F55\",\n      style: {\n        marginTop: 24\n      },\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 19\n        }, this),\n        onClick: fetchLoginRecords,\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: loginColumns,\n        dataSource: loginRecords,\n        rowKey: \"id\",\n        pagination: {\n          pageSize: 10\n        },\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u6DFB\\u52A0IP\\u767D\\u540D\\u5355\",\n      open: ipModalVisible,\n      onOk: handleAddIp,\n      onCancel: () => {\n        setIpModalVisible(false);\n        setNewIp('');\n      },\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1A*************\",\n        value: newIp,\n        onChange: e => setNewIp(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 688,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 370,\n    columnNumber: 5\n  }, this);\n};\n_s(SecuritySettings, \"OBRXRMjyQvLdFEGvxBIa8EwUWIk=\", false, function () {\n  return [Form.useForm];\n});\n_c = SecuritySettings;\nexport default SecuritySettings;\nvar _c;\n$RefreshReg$(_c, \"SecuritySettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Form", "Input", "InputNumber", "Switch", "<PERSON><PERSON>", "message", "Row", "Col", "Select", "<PERSON><PERSON>", "Table", "Tag", "Space", "Modal", "Progress", "SaveOutlined", "ReloadOutlined", "SecurityScanOutlined", "LockOutlined", "EyeOutlined", "DeleteOutlined", "ExclamationCircleOutlined", "SafetyOutlined", "KeyOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "confirm", "SecuritySettings", "_s", "loading", "setLoading", "saving", "setSaving", "config", "setConfig", "min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requireUppercase", "requireLowercase", "requireNumbers", "requireSpecialChars", "passwordExpireDays", "enableTwoFactor", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutDuration", "enableCaptcha", "sessionTimeout", "maxConcurrentSessions", "enableSessionMonitoring", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedIps", "enableSecurityLog", "logRetentionDays", "loginRecords", "setLoginRecords", "ipModalVisible", "setIpModalVisible", "newIp", "setNewIp", "form", "useForm", "fetchConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "success", "error", "console", "fetchLoginRecords", "mockRecords", "id", "userId", "username", "ip", "userAgent", "loginTime", "status", "location", "handleSave", "values", "updatedConfig", "handleAddIp", "ipRegex", "test", "includes", "handleRemoveIp", "title", "content", "onOk", "filter", "item", "handleForceLogoutAll", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "fontSize", "icon", "okText", "okType", "cancelText", "setTimeout", "window", "href", "handleClearLoginRecords", "calculatePasswordStrength", "strength", "loginColumns", "dataIndex", "key", "render", "statusMap", "text", "failed", "blocked", "statusInfo", "_", "record", "type", "size", "onClick", "info", "padding", "level", "gutter", "span", "layout", "onFinish", "initialValues", "description", "showIcon", "marginBottom", "<PERSON><PERSON>", "name", "label", "rules", "required", "min", "max", "width", "valuePropName", "strong", "percent", "display", "marginLeft", "htmlType", "extra", "checked", "onChange", "length", "map", "index", "justifyContent", "alignItems", "code", "danger", "marginTop", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "open", "onCancel", "placeholder", "value", "e", "target", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SecuritySettings/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Form,\n  Input,\n  InputNumber,\n  Switch,\n  Button,\n  message,\n  Row,\n  Col,\n  Select,\n  Alert,\n  Table,\n  Tag,\n  Space,\n  Modal,\n  Tooltip,\n  Progress,\n} from 'antd';\nimport {\n  SaveOutlined,\n  ReloadOutlined,\n  SecurityScanOutlined,\n  LockOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n  SafetyOutlined,\n  KeyOutlined,\n  LogoutOutlined,\n  UserDeleteOutlined,\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { confirm } = Modal;\n\n// 安全配置接口\ninterface SecurityConfig {\n  // 密码策略\n  minPasswordLength: number;\n  requireUppercase: boolean;\n  requireLowercase: boolean;\n  requireNumbers: boolean;\n  requireSpecialChars: boolean;\n  passwordExpireDays: number;\n  \n  // 登录安全\n  enableTwoFactor: boolean;\n  maxLoginAttempts: number;\n  lockoutDuration: number; // 锁定时长（分钟）\n  enableCaptcha: boolean;\n  \n  // 会话管理\n  sessionTimeout: number; // 会话超时（分钟）\n  maxConcurrentSessions: number;\n  enableSessionMonitoring: boolean;\n  \n  // IP白名单\n  enableIpWhitelist: boolean;\n  allowedIps: string[];\n  \n  // 安全日志\n  enableSecurityLog: boolean;\n  logRetentionDays: number;\n}\n\n// 登录记录接口\ninterface LoginRecord {\n  id: number;\n  userId: number;\n  username: string;\n  ip: string;\n  userAgent: string;\n  loginTime: string;\n  status: 'success' | 'failed' | 'blocked';\n  location?: string;\n}\n\nconst SecuritySettings: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [config, setConfig] = useState<SecurityConfig>({\n    minPasswordLength: 8,\n    requireUppercase: true,\n    requireLowercase: true,\n    requireNumbers: true,\n    requireSpecialChars: false,\n    passwordExpireDays: 90,\n    \n    enableTwoFactor: false,\n    maxLoginAttempts: 5,\n    lockoutDuration: 30,\n    enableCaptcha: true,\n    \n    sessionTimeout: 120,\n    maxConcurrentSessions: 3,\n    enableSessionMonitoring: true,\n    \n    enableIpWhitelist: false,\n    allowedIps: [],\n    \n    enableSecurityLog: true,\n    logRetentionDays: 30,\n  });\n\n  const [loginRecords, setLoginRecords] = useState<LoginRecord[]>([]);\n  const [ipModalVisible, setIpModalVisible] = useState(false);\n  const [newIp, setNewIp] = useState('');\n\n  const [form] = Form.useForm();\n\n  // 获取安全配置\n  const fetchConfig = async () => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API获取配置\n      // const response = await securityService.getConfig();\n      // if (response.success) {\n      //   setConfig(response.data);\n      //   form.setFieldsValue(response.data);\n      // }\n      \n      // 暂时使用模拟数据\n      form.setFieldsValue(config);\n      message.success('安全配置加载成功');\n    } catch (error: any) {\n      console.error('获取安全配置失败:', error);\n      message.error('获取安全配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取登录记录\n  const fetchLoginRecords = async () => {\n    try {\n      // 模拟登录记录数据\n      const mockRecords: LoginRecord[] = [\n        {\n          id: 1,\n          userId: 1,\n          username: 'admin',\n          ip: '*************',\n          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          loginTime: '2023-12-15 09:30:00',\n          status: 'success',\n          location: '北京市',\n        },\n        {\n          id: 2,\n          userId: 2,\n          username: 'user001',\n          ip: '*************',\n          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n          loginTime: '2023-12-15 08:45:00',\n          status: 'failed',\n          location: '上海市',\n        },\n      ];\n      setLoginRecords(mockRecords);\n    } catch (error: any) {\n      console.error('获取登录记录失败:', error);\n    }\n  };\n\n  useEffect(() => {\n    fetchConfig();\n    fetchLoginRecords();\n  }, []);\n\n  // 保存配置\n  const handleSave = async (values: any) => {\n    setSaving(true);\n    try {\n      const updatedConfig = { ...config, ...values };\n      \n      // 这里应该调用后端API保存配置\n      // const response = await securityService.updateConfig(updatedConfig);\n      // if (response.success) {\n      //   setConfig(updatedConfig);\n      //   message.success('安全配置保存成功');\n      // }\n      \n      // 暂时使用模拟保存\n      setConfig(updatedConfig);\n      message.success('安全配置保存成功');\n    } catch (error: any) {\n      console.error('保存安全配置失败:', error);\n      message.error('保存安全配置失败');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 添加IP白名单\n  const handleAddIp = () => {\n    if (!newIp) {\n      message.error('请输入IP地址');\n      return;\n    }\n    \n    // 简单的IP格式验证\n    const ipRegex = /^(\\d{1,3}\\.){3}\\d{1,3}$/;\n    if (!ipRegex.test(newIp)) {\n      message.error('请输入有效的IP地址');\n      return;\n    }\n    \n    if (config.allowedIps.includes(newIp)) {\n      message.error('该IP地址已存在');\n      return;\n    }\n    \n    const updatedConfig = {\n      ...config,\n      allowedIps: [...config.allowedIps, newIp],\n    };\n    setConfig(updatedConfig);\n    setNewIp('');\n    setIpModalVisible(false);\n    message.success('IP地址添加成功');\n  };\n\n  // 删除IP白名单\n  const handleRemoveIp = (ip: string) => {\n    confirm({\n      title: '确认删除',\n      content: `确定要删除IP地址 ${ip} 吗？`,\n      onOk() {\n        const updatedConfig = {\n          ...config,\n          allowedIps: config.allowedIps.filter(item => item !== ip),\n        };\n        setConfig(updatedConfig);\n        message.success('IP地址删除成功');\n      },\n    });\n  };\n\n  // 强制登出所有用户\n  const handleForceLogoutAll = () => {\n    confirm({\n      title: '强制登出所有用户',\n      content: (\n        <div>\n          <p>此操作将强制登出系统中的所有用户（包括您自己），确定要继续吗？</p>\n          <p style={{ color: '#ff4d4f', fontSize: '14px' }}>\n            ⚠️ 警告：此操作不可撤销，所有用户需要重新登录\n          </p>\n        </div>\n      ),\n      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '确认强制登出',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          // 这里应该调用后端API强制登出所有用户\n          // await securityService.forceLogoutAll();\n\n          message.success('已强制登出所有用户');\n\n          // 延迟一下后跳转到登录页\n          setTimeout(() => {\n            window.location.href = '/login';\n          }, 2000);\n        } catch (error) {\n          console.error('强制登出失败:', error);\n          message.error('强制登出失败，请重试');\n        }\n      },\n    });\n  };\n\n  // 清除所有登录记录\n  const handleClearLoginRecords = () => {\n    confirm({\n      title: '清除登录记录',\n      content: '确定要清除所有登录记录吗？此操作不可撤销。',\n      onOk() {\n        setLoginRecords([]);\n        message.success('登录记录已清除');\n      },\n    });\n  };\n\n  // 计算密码强度\n  const calculatePasswordStrength = () => {\n    let strength = 0;\n    if (config.minPasswordLength >= 8) strength += 20;\n    if (config.requireUppercase) strength += 20;\n    if (config.requireLowercase) strength += 20;\n    if (config.requireNumbers) strength += 20;\n    if (config.requireSpecialChars) strength += 20;\n    return strength;\n  };\n\n  // 登录记录表格列\n  const loginColumns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ip',\n      key: 'ip',\n    },\n    {\n      title: '登录时间',\n      dataIndex: 'loginTime',\n      key: 'loginTime',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => {\n        const statusMap = {\n          success: { color: 'green', text: '成功' },\n          failed: { color: 'red', text: '失败' },\n          blocked: { color: 'orange', text: '被阻止' },\n        };\n        const statusInfo = statusMap[status as keyof typeof statusMap];\n        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\n      },\n    },\n    {\n      title: '位置',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_: any, record: LoginRecord) => (\n        <Space>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => {\n              Modal.info({\n                title: '登录详情',\n                content: (\n                  <div>\n                    <p><strong>用户名:</strong> {record.username}</p>\n                    <p><strong>IP地址:</strong> {record.ip}</p>\n                    <p><strong>用户代理:</strong> {record.userAgent}</p>\n                    <p><strong>登录时间:</strong> {record.loginTime}</p>\n                    <p><strong>位置:</strong> {record.location}</p>\n                  </div>\n                ),\n              });\n            }}\n          >\n            详情\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>\n        <SecurityScanOutlined /> 安全设置\n      </Title>\n\n      <Row gutter={24}>\n        <Col span={16}>\n          <Card title={<span><LockOutlined /> 安全配置</span>}>\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={handleSave}\n              initialValues={config}\n            >\n              {/* 密码策略 */}\n              <Alert\n                message=\"密码策略\"\n                description=\"配置用户密码的安全要求\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 24 }}\n              />\n              \n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"minPasswordLength\"\n                    label=\"最小密码长度\"\n                    rules={[{ required: true, message: '请输入最小密码长度' }]}\n                  >\n                    <InputNumber\n                      min={6}\n                      max={20}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"passwordExpireDays\"\n                    label=\"密码过期天数\"\n                    rules={[{ required: true, message: '请输入密码过期天数' }]}\n                  >\n                    <InputNumber\n                      min={30}\n                      max={365}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n              </Row>\n              \n              <Row gutter={16}>\n                <Col span={6}>\n                  <Form.Item\n                    name=\"requireUppercase\"\n                    label=\"需要大写字母\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={6}>\n                  <Form.Item\n                    name=\"requireLowercase\"\n                    label=\"需要小写字母\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={6}>\n                  <Form.Item\n                    name=\"requireNumbers\"\n                    label=\"需要数字\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={6}>\n                  <Form.Item\n                    name=\"requireSpecialChars\"\n                    label=\"需要特殊字符\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              {/* 密码强度指示器 */}\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>密码强度: </Text>\n                <Progress\n                  percent={calculatePasswordStrength()}\n                  size=\"small\"\n                  status={calculatePasswordStrength() >= 80 ? 'success' : 'active'}\n                  style={{ width: 200, display: 'inline-block', marginLeft: 8 }}\n                />\n              </div>\n\n              {/* 登录安全 */}\n              <Alert\n                message=\"登录安全\"\n                description=\"配置登录相关的安全策略\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 24 }}\n              />\n              \n              <Row gutter={16}>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"maxLoginAttempts\"\n                    label=\"最大登录尝试次数\"\n                    rules={[{ required: true, message: '请输入最大登录尝试次数' }]}\n                  >\n                    <InputNumber\n                      min={3}\n                      max={10}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"lockoutDuration\"\n                    label=\"锁定时长（分钟）\"\n                    rules={[{ required: true, message: '请输入锁定时长' }]}\n                  >\n                    <InputNumber\n                      min={5}\n                      max={1440}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"sessionTimeout\"\n                    label=\"会话超时（分钟）\"\n                    rules={[{ required: true, message: '请输入会话超时时间' }]}\n                  >\n                    <InputNumber\n                      min={30}\n                      max={480}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n              </Row>\n              \n              <Row gutter={16}>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"enableTwoFactor\"\n                    label=\"启用双因子认证\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"enableCaptcha\"\n                    label=\"启用验证码\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"enableSessionMonitoring\"\n                    label=\"启用会话监控\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item>\n                <Space>\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    icon={<SaveOutlined />}\n                    loading={saving}\n                  >\n                    保存配置\n                  </Button>\n                  <Button\n                    icon={<ReloadOutlined />}\n                    onClick={fetchConfig}\n                    loading={loading}\n                  >\n                    重新加载\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        <Col span={8}>\n          {/* IP白名单管理 */}\n          <Card \n            title={<span><SafetyOutlined /> IP白名单</span>}\n            extra={\n              <Button\n                type=\"primary\"\n                size=\"small\"\n                onClick={() => setIpModalVisible(true)}\n              >\n                添加IP\n              </Button>\n            }\n            style={{ marginBottom: 16 }}\n          >\n            <Form.Item\n              label=\"启用IP白名单\"\n              style={{ marginBottom: 16 }}\n            >\n              <Switch\n                checked={config.enableIpWhitelist}\n                onChange={(checked) => setConfig({ ...config, enableIpWhitelist: checked })}\n              />\n            </Form.Item>\n            \n            {config.allowedIps.length > 0 ? (\n              <div>\n                {config.allowedIps.map((ip, index) => (\n                  <div key={index} style={{ marginBottom: 8, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Text code>{ip}</Text>\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      danger\n                      icon={<DeleteOutlined />}\n                      onClick={() => handleRemoveIp(ip)}\n                    />\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <Text type=\"secondary\">暂无IP白名单</Text>\n            )}\n          </Card>\n\n          {/* 安全状态 */}\n          <Card title={<span><KeyOutlined /> 安全状态</span>}>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>密码强度: </Text>\n              <Progress\n                percent={calculatePasswordStrength()}\n                size=\"small\"\n                status={calculatePasswordStrength() >= 80 ? 'success' : 'active'}\n              />\n            </div>\n            \n            <div style={{ marginBottom: 8 }}>\n              <Text>双因子认证: </Text>\n              <Tag color={config.enableTwoFactor ? 'green' : 'red'}>\n                {config.enableTwoFactor ? '已启用' : '未启用'}\n              </Tag>\n            </div>\n            \n            <div style={{ marginBottom: 8 }}>\n              <Text>IP白名单: </Text>\n              <Tag color={config.enableIpWhitelist ? 'green' : 'red'}>\n                {config.enableIpWhitelist ? '已启用' : '未启用'}\n              </Tag>\n            </div>\n            \n            <div>\n              <Text>会话监控: </Text>\n              <Tag color={config.enableSessionMonitoring ? 'green' : 'red'}>\n                {config.enableSessionMonitoring ? '已启用' : '未启用'}\n              </Tag>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 登录记录 */}\n      <Card \n        title=\"最近登录记录\" \n        style={{ marginTop: 24 }}\n        extra={\n          <Button\n            icon={<ReloadOutlined />}\n            onClick={fetchLoginRecords}\n          >\n            刷新\n          </Button>\n        }\n      >\n        <Table\n          columns={loginColumns}\n          dataSource={loginRecords}\n          rowKey=\"id\"\n          pagination={{ pageSize: 10 }}\n          size=\"small\"\n        />\n      </Card>\n\n      {/* 添加IP模态框 */}\n      <Modal\n        title=\"添加IP白名单\"\n        open={ipModalVisible}\n        onOk={handleAddIp}\n        onCancel={() => {\n          setIpModalVisible(false);\n          setNewIp('');\n        }}\n      >\n        <Input\n          placeholder=\"请输入IP地址，例如：*************\"\n          value={newIp}\n          onChange={(e) => setNewIp(e.target.value)}\n        />\n      </Modal>\n    </div>\n  );\n};\n\nexport default SecuritySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,KAAK,EAELC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,oBAAoB,EACpBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,yBAAyB,EACzBC,cAAc,EACdC,WAAW,QAGN,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG5B,UAAU;AAClC,MAAM;EAAE6B;AAAS,CAAC,GAAG3B,KAAK;AAC1B,MAAM;EAAE4B;AAAO,CAAC,GAAGrB,MAAM;AACzB,MAAM;EAAEsB;AAAQ,CAAC,GAAGjB,KAAK;;AAEzB;;AA8BA;;AAYA,MAAMkB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAiB;IACnD2C,iBAAiB,EAAE,CAAC;IACpBC,gBAAgB,EAAE,IAAI;IACtBC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,mBAAmB,EAAE,KAAK;IAC1BC,kBAAkB,EAAE,EAAE;IAEtBC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE,EAAE;IACnBC,aAAa,EAAE,IAAI;IAEnBC,cAAc,EAAE,GAAG;IACnBC,qBAAqB,EAAE,CAAC;IACxBC,uBAAuB,EAAE,IAAI;IAE7BC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,EAAE;IAEdC,iBAAiB,EAAE,IAAI;IACvBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgE,KAAK,EAAEC,QAAQ,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM,CAACkE,IAAI,CAAC,GAAG9D,IAAI,CAAC+D,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B9B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA4B,IAAI,CAACG,cAAc,CAAC5B,MAAM,CAAC;MAC3BhC,OAAO,CAAC6D,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9D,OAAO,CAAC8D,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF;MACA,MAAMC,WAA0B,GAAG,CACjC;QACEC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,OAAO;QACjBC,EAAE,EAAE,eAAe;QACnBC,SAAS,EAAE,8DAA8D;QACzEC,SAAS,EAAE,qBAAqB;QAChCC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEP,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,SAAS;QACnBC,EAAE,EAAE,eAAe;QACnBC,SAAS,EAAE,oEAAoE;QAC/EC,SAAS,EAAE,qBAAqB;QAChCC,MAAM,EAAE,QAAQ;QAChBC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDrB,eAAe,CAACa,WAAW,CAAC;IAC9B,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAEDtE,SAAS,CAAC,MAAM;IACdmE,WAAW,CAAC,CAAC;IACbK,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,UAAU,GAAG,MAAOC,MAAW,IAAK;IACxC5C,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF,MAAM6C,aAAa,GAAG;QAAE,GAAG5C,MAAM;QAAE,GAAG2C;MAAO,CAAC;;MAE9C;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA1C,SAAS,CAAC2C,aAAa,CAAC;MACxB5E,OAAO,CAAC6D,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9D,OAAO,CAAC8D,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR/B,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM8C,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACtB,KAAK,EAAE;MACVvD,OAAO,CAAC8D,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;;IAEA;IACA,MAAMgB,OAAO,GAAG,yBAAyB;IACzC,IAAI,CAACA,OAAO,CAACC,IAAI,CAACxB,KAAK,CAAC,EAAE;MACxBvD,OAAO,CAAC8D,KAAK,CAAC,YAAY,CAAC;MAC3B;IACF;IAEA,IAAI9B,MAAM,CAACgB,UAAU,CAACgC,QAAQ,CAACzB,KAAK,CAAC,EAAE;MACrCvD,OAAO,CAAC8D,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,MAAMc,aAAa,GAAG;MACpB,GAAG5C,MAAM;MACTgB,UAAU,EAAE,CAAC,GAAGhB,MAAM,CAACgB,UAAU,EAAEO,KAAK;IAC1C,CAAC;IACDtB,SAAS,CAAC2C,aAAa,CAAC;IACxBpB,QAAQ,CAAC,EAAE,CAAC;IACZF,iBAAiB,CAAC,KAAK,CAAC;IACxBtD,OAAO,CAAC6D,OAAO,CAAC,UAAU,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMoB,cAAc,GAAIZ,EAAU,IAAK;IACrC5C,OAAO,CAAC;MACNyD,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,aAAad,EAAE,KAAK;MAC7Be,IAAIA,CAAA,EAAG;QACL,MAAMR,aAAa,GAAG;UACpB,GAAG5C,MAAM;UACTgB,UAAU,EAAEhB,MAAM,CAACgB,UAAU,CAACqC,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKjB,EAAE;QAC1D,CAAC;QACDpC,SAAS,CAAC2C,aAAa,CAAC;QACxB5E,OAAO,CAAC6D,OAAO,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0B,oBAAoB,GAAGA,CAAA,KAAM;IACjC9D,OAAO,CAAC;MACNyD,KAAK,EAAE,UAAU;MACjBC,OAAO,eACL/D,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAAoE,QAAA,EAAG;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtCxE,OAAA;UAAGyE,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;MACDI,IAAI,eAAE5E,OAAA,CAACJ,yBAAyB;QAAC6E,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAChEK,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBf,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF;UACA;;UAEApF,OAAO,CAAC6D,OAAO,CAAC,WAAW,CAAC;;UAE5B;UACAuC,UAAU,CAAC,MAAM;YACfC,MAAM,CAAC5B,QAAQ,CAAC6B,IAAI,GAAG,QAAQ;UACjC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,CAAC,OAAOxC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B9D,OAAO,CAAC8D,KAAK,CAAC,YAAY,CAAC;QAC7B;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyC,uBAAuB,GAAGA,CAAA,KAAM;IACpC9E,OAAO,CAAC;MACNyD,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAE,uBAAuB;MAChCC,IAAIA,CAAA,EAAG;QACLhC,eAAe,CAAC,EAAE,CAAC;QACnBpD,OAAO,CAAC6D,OAAO,CAAC,SAAS,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2C,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIzE,MAAM,CAACE,iBAAiB,IAAI,CAAC,EAAEuE,QAAQ,IAAI,EAAE;IACjD,IAAIzE,MAAM,CAACG,gBAAgB,EAAEsE,QAAQ,IAAI,EAAE;IAC3C,IAAIzE,MAAM,CAACI,gBAAgB,EAAEqE,QAAQ,IAAI,EAAE;IAC3C,IAAIzE,MAAM,CAACK,cAAc,EAAEoE,QAAQ,IAAI,EAAE;IACzC,IAAIzE,MAAM,CAACM,mBAAmB,EAAEmE,QAAQ,IAAI,EAAE;IAC9C,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IACExB,KAAK,EAAE,KAAK;IACZyB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACE1B,KAAK,EAAE,MAAM;IACbyB,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE;EACP,CAAC,EACD;IACE1B,KAAK,EAAE,MAAM;IACbyB,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACE1B,KAAK,EAAE,IAAI;IACXyB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGrC,MAAc,IAAK;MAC1B,MAAMsC,SAAS,GAAG;QAChBjD,OAAO,EAAE;UAAEiC,KAAK,EAAE,OAAO;UAAEiB,IAAI,EAAE;QAAK,CAAC;QACvCC,MAAM,EAAE;UAAElB,KAAK,EAAE,KAAK;UAAEiB,IAAI,EAAE;QAAK,CAAC;QACpCE,OAAO,EAAE;UAAEnB,KAAK,EAAE,QAAQ;UAAEiB,IAAI,EAAE;QAAM;MAC1C,CAAC;MACD,MAAMG,UAAU,GAAGJ,SAAS,CAACtC,MAAM,CAA2B;MAC9D,oBAAOpD,OAAA,CAACd,GAAG;QAACwF,KAAK,EAAEoB,UAAU,CAACpB,KAAM;QAAAN,QAAA,EAAE0B,UAAU,CAACH;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC9D;EACF,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXyB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACE1B,KAAK,EAAE,IAAI;IACX0B,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACM,CAAM,EAAEC,MAAmB,kBAClChG,OAAA,CAACb,KAAK;MAAAiF,QAAA,eACJpE,OAAA,CAACrB,MAAM;QACLsH,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZtB,IAAI,eAAE5E,OAAA,CAACN,WAAW;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtB2B,OAAO,EAAEA,CAAA,KAAM;UACb/G,KAAK,CAACgH,IAAI,CAAC;YACTtC,KAAK,EAAE,MAAM;YACbC,OAAO,eACL/D,OAAA;cAAAoE,QAAA,gBACEpE,OAAA;gBAAAoE,QAAA,gBAAGpE,OAAA;kBAAAoE,QAAA,EAAQ;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACwB,MAAM,CAAChD,QAAQ;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CxE,OAAA;gBAAAoE,QAAA,gBAAGpE,OAAA;kBAAAoE,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACwB,MAAM,CAAC/C,EAAE;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzCxE,OAAA;gBAAAoE,QAAA,gBAAGpE,OAAA;kBAAAoE,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACwB,MAAM,CAAC9C,SAAS;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDxE,OAAA;gBAAAoE,QAAA,gBAAGpE,OAAA;kBAAAoE,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACwB,MAAM,CAAC7C,SAAS;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDxE,OAAA;gBAAAoE,QAAA,gBAAGpE,OAAA;kBAAAoE,QAAA,EAAQ;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACwB,MAAM,CAAC3C,QAAQ;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAET,CAAC,CAAC;QACJ,CAAE;QAAAJ,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,oBACExE,OAAA;IAAKyE,KAAK,EAAE;MAAE4B,OAAO,EAAE;IAAG,CAAE;IAAAjC,QAAA,gBAC1BpE,OAAA,CAACC,KAAK;MAACqG,KAAK,EAAE,CAAE;MAAAlC,QAAA,gBACdpE,OAAA,CAACR,oBAAoB;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BAC1B;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERxE,OAAA,CAACnB,GAAG;MAAC0H,MAAM,EAAE,EAAG;MAAAnC,QAAA,gBACdpE,OAAA,CAAClB,GAAG;QAAC0H,IAAI,EAAE,EAAG;QAAApC,QAAA,eACZpE,OAAA,CAAC3B,IAAI;UAACyF,KAAK,eAAE9D,OAAA;YAAAoE,QAAA,gBAAMpE,OAAA,CAACP,YAAY;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAAK;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAJ,QAAA,eAC9CpE,OAAA,CAACzB,IAAI;YACH8D,IAAI,EAAEA,IAAK;YACXoE,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAEpD,UAAW;YACrBqD,aAAa,EAAE/F,MAAO;YAAAwD,QAAA,gBAGtBpE,OAAA,CAAChB,KAAK;cACJJ,OAAO,EAAC,0BAAM;cACdgI,WAAW,EAAC,oEAAa;cACzBX,IAAI,EAAC,MAAM;cACXY,QAAQ;cACRpC,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eAEFxE,OAAA,CAACnB,GAAG;cAAC0H,MAAM,EAAE,EAAG;cAAAnC,QAAA,gBACdpE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,EAAG;gBAAApC,QAAA,eACZpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,mBAAmB;kBACxBC,KAAK,EAAC,sCAAQ;kBACdC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEvI,OAAO,EAAE;kBAAY,CAAC,CAAE;kBAAAwF,QAAA,eAElDpE,OAAA,CAACvB,WAAW;oBACV2I,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,EAAG;oBACR5C,KAAK,EAAE;sBAAE6C,KAAK,EAAE;oBAAO;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,EAAG;gBAAApC,QAAA,eACZpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,oBAAoB;kBACzBC,KAAK,EAAC,sCAAQ;kBACdC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEvI,OAAO,EAAE;kBAAY,CAAC,CAAE;kBAAAwF,QAAA,eAElDpE,OAAA,CAACvB,WAAW;oBACV2I,GAAG,EAAE,EAAG;oBACRC,GAAG,EAAE,GAAI;oBACT5C,KAAK,EAAE;sBAAE6C,KAAK,EAAE;oBAAO;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxE,OAAA,CAACnB,GAAG;cAAC0H,MAAM,EAAE,EAAG;cAAAnC,QAAA,gBACdpE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAC,sCAAQ;kBACdM,aAAa,EAAC,SAAS;kBAAAnD,QAAA,eAEvBpE,OAAA,CAACtB,MAAM;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAC,sCAAQ;kBACdM,aAAa,EAAC,SAAS;kBAAAnD,QAAA,eAEvBpE,OAAA,CAACtB,MAAM;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAC,0BAAM;kBACZM,aAAa,EAAC,SAAS;kBAAAnD,QAAA,eAEvBpE,OAAA,CAACtB,MAAM;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAC,sCAAQ;kBACdM,aAAa,EAAC,SAAS;kBAAAnD,QAAA,eAEvBpE,OAAA,CAACtB,MAAM;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxE,OAAA;cAAKyE,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/BpE,OAAA,CAACE,IAAI;gBAACsH,MAAM;gBAAApD,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BxE,OAAA,CAACX,QAAQ;gBACPoI,OAAO,EAAErC,yBAAyB,CAAC,CAAE;gBACrCc,IAAI,EAAC,OAAO;gBACZ9C,MAAM,EAAEgC,yBAAyB,CAAC,CAAC,IAAI,EAAE,GAAG,SAAS,GAAG,QAAS;gBACjEX,KAAK,EAAE;kBAAE6C,KAAK,EAAE,GAAG;kBAAEI,OAAO,EAAE,cAAc;kBAAEC,UAAU,EAAE;gBAAE;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA,CAAChB,KAAK;cACJJ,OAAO,EAAC,0BAAM;cACdgI,WAAW,EAAC,oEAAa;cACzBX,IAAI,EAAC,MAAM;cACXY,QAAQ;cACRpC,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eAEFxE,OAAA,CAACnB,GAAG;cAAC0H,MAAM,EAAE,EAAG;cAAAnC,QAAA,gBACdpE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAC,kDAAU;kBAChBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEvI,OAAO,EAAE;kBAAc,CAAC,CAAE;kBAAAwF,QAAA,eAEpDpE,OAAA,CAACvB,WAAW;oBACV2I,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,EAAG;oBACR5C,KAAK,EAAE;sBAAE6C,KAAK,EAAE;oBAAO;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,iBAAiB;kBACtBC,KAAK,EAAC,kDAAU;kBAChBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEvI,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAAwF,QAAA,eAEhDpE,OAAA,CAACvB,WAAW;oBACV2I,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,IAAK;oBACV5C,KAAK,EAAE;sBAAE6C,KAAK,EAAE;oBAAO;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAC,kDAAU;kBAChBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEvI,OAAO,EAAE;kBAAY,CAAC,CAAE;kBAAAwF,QAAA,eAElDpE,OAAA,CAACvB,WAAW;oBACV2I,GAAG,EAAE,EAAG;oBACRC,GAAG,EAAE,GAAI;oBACT5C,KAAK,EAAE;sBAAE6C,KAAK,EAAE;oBAAO;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxE,OAAA,CAACnB,GAAG;cAAC0H,MAAM,EAAE,EAAG;cAAAnC,QAAA,gBACdpE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,iBAAiB;kBACtBC,KAAK,EAAC,4CAAS;kBACfM,aAAa,EAAC,SAAS;kBAAAnD,QAAA,eAEvBpE,OAAA,CAACtB,MAAM;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,eAAe;kBACpBC,KAAK,EAAC,gCAAO;kBACbM,aAAa,EAAC,SAAS;kBAAAnD,QAAA,eAEvBpE,OAAA,CAACtB,MAAM;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxE,OAAA,CAAClB,GAAG;gBAAC0H,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;kBACRC,IAAI,EAAC,yBAAyB;kBAC9BC,KAAK,EAAC,sCAAQ;kBACdM,aAAa,EAAC,SAAS;kBAAAnD,QAAA,eAEvBpE,OAAA,CAACtB,MAAM;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxE,OAAA,CAACzB,IAAI,CAACwI,IAAI;cAAA3C,QAAA,eACRpE,OAAA,CAACb,KAAK;gBAAAiF,QAAA,gBACJpE,OAAA,CAACrB,MAAM;kBACLsH,IAAI,EAAC,SAAS;kBACd2B,QAAQ,EAAC,QAAQ;kBACjBhD,IAAI,eAAE5E,OAAA,CAACV,YAAY;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBhE,OAAO,EAAEE,MAAO;kBAAA0D,QAAA,EACjB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxE,OAAA,CAACrB,MAAM;kBACLiG,IAAI,eAAE5E,OAAA,CAACT,cAAc;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzB2B,OAAO,EAAE5D,WAAY;kBACrB/B,OAAO,EAAEA,OAAQ;kBAAA4D,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxE,OAAA,CAAClB,GAAG;QAAC0H,IAAI,EAAE,CAAE;QAAApC,QAAA,gBAEXpE,OAAA,CAAC3B,IAAI;UACHyF,KAAK,eAAE9D,OAAA;YAAAoE,QAAA,gBAAMpE,OAAA,CAACH,cAAc;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAAM;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAC7CqD,KAAK,eACH7H,OAAA,CAACrB,MAAM;YACLsH,IAAI,EAAC,SAAS;YACdC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMjE,iBAAiB,CAAC,IAAI,CAAE;YAAAkC,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UACDC,KAAK,EAAE;YAAEqC,YAAY,EAAE;UAAG,CAAE;UAAA1C,QAAA,gBAE5BpE,OAAA,CAACzB,IAAI,CAACwI,IAAI;YACRE,KAAK,EAAC,kCAAS;YACfxC,KAAK,EAAE;cAAEqC,YAAY,EAAE;YAAG,CAAE;YAAA1C,QAAA,eAE5BpE,OAAA,CAACtB,MAAM;cACLoJ,OAAO,EAAElH,MAAM,CAACe,iBAAkB;cAClCoG,QAAQ,EAAGD,OAAO,IAAKjH,SAAS,CAAC;gBAAE,GAAGD,MAAM;gBAAEe,iBAAiB,EAAEmG;cAAQ,CAAC;YAAE;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,EAEX5D,MAAM,CAACgB,UAAU,CAACoG,MAAM,GAAG,CAAC,gBAC3BhI,OAAA;YAAAoE,QAAA,EACGxD,MAAM,CAACgB,UAAU,CAACqG,GAAG,CAAC,CAAChF,EAAE,EAAEiF,KAAK,kBAC/BlI,OAAA;cAAiByE,KAAK,EAAE;gBAAEqC,YAAY,EAAE,CAAC;gBAAEY,OAAO,EAAE,MAAM;gBAAES,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAhE,QAAA,gBAClHpE,OAAA,CAACE,IAAI;gBAACmI,IAAI;gBAAAjE,QAAA,EAAEnB;cAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBxE,OAAA,CAACrB,MAAM;gBACLsH,IAAI,EAAC,MAAM;gBACXC,IAAI,EAAC,OAAO;gBACZoC,MAAM;gBACN1D,IAAI,eAAE5E,OAAA,CAACL,cAAc;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzB2B,OAAO,EAAEA,CAAA,KAAMtC,cAAc,CAACZ,EAAE;cAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA,GARM0D,KAAK;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENxE,OAAA,CAACE,IAAI;YAAC+F,IAAI,EAAC,WAAW;YAAA7B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACrC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGPxE,OAAA,CAAC3B,IAAI;UAACyF,KAAK,eAAE9D,OAAA;YAAAoE,QAAA,gBAAMpE,OAAA,CAACF,WAAW;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAAK;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAJ,QAAA,gBAC7CpE,OAAA;YAAKyE,KAAK,EAAE;cAAEqC,YAAY,EAAE;YAAG,CAAE;YAAA1C,QAAA,gBAC/BpE,OAAA,CAACE,IAAI;cAACsH,MAAM;cAAApD,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BxE,OAAA,CAACX,QAAQ;cACPoI,OAAO,EAAErC,yBAAyB,CAAC,CAAE;cACrCc,IAAI,EAAC,OAAO;cACZ9C,MAAM,EAAEgC,yBAAyB,CAAC,CAAC,IAAI,EAAE,GAAG,SAAS,GAAG;YAAS;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxE,OAAA;YAAKyE,KAAK,EAAE;cAAEqC,YAAY,EAAE;YAAE,CAAE;YAAA1C,QAAA,gBAC9BpE,OAAA,CAACE,IAAI;cAAAkE,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpBxE,OAAA,CAACd,GAAG;cAACwF,KAAK,EAAE9D,MAAM,CAACQ,eAAe,GAAG,OAAO,GAAG,KAAM;cAAAgD,QAAA,EAClDxD,MAAM,CAACQ,eAAe,GAAG,KAAK,GAAG;YAAK;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxE,OAAA;YAAKyE,KAAK,EAAE;cAAEqC,YAAY,EAAE;YAAE,CAAE;YAAA1C,QAAA,gBAC9BpE,OAAA,CAACE,IAAI;cAAAkE,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpBxE,OAAA,CAACd,GAAG;cAACwF,KAAK,EAAE9D,MAAM,CAACe,iBAAiB,GAAG,OAAO,GAAG,KAAM;cAAAyC,QAAA,EACpDxD,MAAM,CAACe,iBAAiB,GAAG,KAAK,GAAG;YAAK;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxE,OAAA;YAAAoE,QAAA,gBACEpE,OAAA,CAACE,IAAI;cAAAkE,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBxE,OAAA,CAACd,GAAG;cAACwF,KAAK,EAAE9D,MAAM,CAACc,uBAAuB,GAAG,OAAO,GAAG,KAAM;cAAA0C,QAAA,EAC1DxD,MAAM,CAACc,uBAAuB,GAAG,KAAK,GAAG;YAAK;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA,CAAC3B,IAAI;MACHyF,KAAK,EAAC,sCAAQ;MACdW,KAAK,EAAE;QAAE8D,SAAS,EAAE;MAAG,CAAE;MACzBV,KAAK,eACH7H,OAAA,CAACrB,MAAM;QACLiG,IAAI,eAAE5E,OAAA,CAACT,cAAc;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzB2B,OAAO,EAAEvD,iBAAkB;QAAAwB,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAJ,QAAA,eAEDpE,OAAA,CAACf,KAAK;QACJuJ,OAAO,EAAElD,YAAa;QACtBmD,UAAU,EAAE1G,YAAa;QACzB2G,MAAM,EAAC,IAAI;QACXC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAC7B1C,IAAI,EAAC;MAAO;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPxE,OAAA,CAACZ,KAAK;MACJ0E,KAAK,EAAC,kCAAS;MACf+E,IAAI,EAAE5G,cAAe;MACrB+B,IAAI,EAAEP,WAAY;MAClBqF,QAAQ,EAAEA,CAAA,KAAM;QACd5G,iBAAiB,CAAC,KAAK,CAAC;QACxBE,QAAQ,CAAC,EAAE,CAAC;MACd,CAAE;MAAAgC,QAAA,eAEFpE,OAAA,CAACxB,KAAK;QACJuK,WAAW,EAAC,uEAA0B;QACtCC,KAAK,EAAE7G,KAAM;QACb4F,QAAQ,EAAGkB,CAAC,IAAK7G,QAAQ,CAAC6G,CAAC,CAACC,MAAM,CAACF,KAAK;MAAE;QAAA3E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACjE,EAAA,CArmBID,gBAA0B;EAAA,QA+Bf/B,IAAI,CAAC+D,OAAO;AAAA;AAAA6G,EAAA,GA/BvB7I,gBAA0B;AAumBhC,eAAeA,gBAAgB;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}