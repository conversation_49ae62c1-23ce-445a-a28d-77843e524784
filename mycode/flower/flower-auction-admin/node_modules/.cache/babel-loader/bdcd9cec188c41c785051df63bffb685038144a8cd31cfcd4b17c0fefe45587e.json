{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\n/**\n * Warning for ConfigProviderProps.\n * This will be empty function in production.\n */\nconst PropWarning = /*#__PURE__*/React.memo(({\n  dropdownMatchSelectWidth\n}) => {\n  const warning = devUseWarning('ConfigProvider');\n  warning.deprecated(dropdownMatchSelectWidth === undefined, 'dropdownMatchSelectWidth', 'popupMatchSelectWidth');\n  return null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  PropWarning.displayName = 'PropWarning';\n}\nexport default process.env.NODE_ENV !== 'production' ? PropWarning : () => null;", "map": {"version": 3, "names": ["React", "devUseW<PERSON>ning", "Prop<PERSON><PERSON>ning", "memo", "dropdownMatchSelectWidth", "warning", "deprecated", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/config-provider/PropWarning.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\n/**\n * Warning for ConfigProviderProps.\n * This will be empty function in production.\n */\nconst PropWarning = /*#__PURE__*/React.memo(({\n  dropdownMatchSelectWidth\n}) => {\n  const warning = devUseWarning('ConfigProvider');\n  warning.deprecated(dropdownMatchSelectWidth === undefined, 'dropdownMatchSelectWidth', 'popupMatchSelectWidth');\n  return null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  PropWarning.displayName = 'PropWarning';\n}\nexport default process.env.NODE_ENV !== 'production' ? PropWarning : () => null;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,kBAAkB;AAChD;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,aAAaF,KAAK,CAACG,IAAI,CAAC,CAAC;EAC3CC;AACF,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAGJ,aAAa,CAAC,gBAAgB,CAAC;EAC/CI,OAAO,CAACC,UAAU,CAACF,wBAAwB,KAAKG,SAAS,EAAE,0BAA0B,EAAE,uBAAuB,CAAC;EAC/G,OAAO,IAAI;AACb,CAAC,CAAC;AACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCR,WAAW,CAACS,WAAW,GAAG,aAAa;AACzC;AACA,eAAeH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,WAAW,GAAG,MAAM,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}