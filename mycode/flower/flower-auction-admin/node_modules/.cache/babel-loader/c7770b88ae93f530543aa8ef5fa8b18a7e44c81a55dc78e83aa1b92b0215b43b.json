{"ast": null, "code": "import React from 'react';\nimport SizeContext from '../SizeContext';\nconst useSize = customSize => {\n  const size = React.useContext(SizeContext);\n  const mergedSize = React.useMemo(() => {\n    if (!customSize) {\n      return size;\n    }\n    if (typeof customSize === 'string') {\n      return customSize !== null && customSize !== void 0 ? customSize : size;\n    }\n    if (typeof customSize === 'function') {\n      return customSize(size);\n    }\n    return size;\n  }, [customSize, size]);\n  return mergedSize;\n};\nexport default useSize;", "map": {"version": 3, "names": ["React", "SizeContext", "useSize", "customSize", "size", "useContext", "mergedSize", "useMemo"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/config-provider/hooks/useSize.js"], "sourcesContent": ["import React from 'react';\nimport SizeContext from '../SizeContext';\nconst useSize = customSize => {\n  const size = React.useContext(SizeContext);\n  const mergedSize = React.useMemo(() => {\n    if (!customSize) {\n      return size;\n    }\n    if (typeof customSize === 'string') {\n      return customSize !== null && customSize !== void 0 ? customSize : size;\n    }\n    if (typeof customSize === 'function') {\n      return customSize(size);\n    }\n    return size;\n  }, [customSize, size]);\n  return mergedSize;\n};\nexport default useSize;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,gBAAgB;AACxC,MAAMC,OAAO,GAAGC,UAAU,IAAI;EAC5B,MAAMC,IAAI,GAAGJ,KAAK,CAACK,UAAU,CAACJ,WAAW,CAAC;EAC1C,MAAMK,UAAU,GAAGN,KAAK,CAACO,OAAO,CAAC,MAAM;IACrC,IAAI,CAACJ,UAAU,EAAE;MACf,OAAOC,IAAI;IACb;IACA,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;MAClC,OAAOA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGC,IAAI;IACzE;IACA,IAAI,OAAOD,UAAU,KAAK,UAAU,EAAE;MACpC,OAAOA,UAAU,CAACC,IAAI,CAAC;IACzB;IACA,OAAOA,IAAI;EACb,CAAC,EAAE,CAACD,UAAU,EAAEC,IAAI,CAAC,CAAC;EACtB,OAAOE,UAAU;AACnB,CAAC;AACD,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}