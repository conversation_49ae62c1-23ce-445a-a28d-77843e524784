{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  user: null,\n  isAuthenticated: false,\n  loading: false\n};\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    setUser: (state, action) => {\n      state.user = action.payload;\n      state.isAuthenticated = true;\n      state.loading = false;\n    },\n    clearUser: state => {\n      state.user = null;\n      state.isAuthenticated = false;\n      state.loading = false;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    }\n  }\n});\nexport const {\n  setUser,\n  clearUser,\n  setLoading\n} = authSlice.actions;\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "user", "isAuthenticated", "loading", "authSlice", "name", "reducers", "setUser", "state", "action", "payload", "clearUser", "setLoading", "actions", "reducer"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/store/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { User } from '../../hooks/useAuth';\n\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  loading: boolean;\n}\n\nconst initialState: AuthState = {\n  user: null,\n  isAuthenticated: false,\n  loading: false,\n};\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    setUser: (state, action: PayloadAction<User>) => {\n      state.user = action.payload;\n      state.isAuthenticated = true;\n      state.loading = false;\n    },\n    clearUser: (state) => {\n      state.user = null;\n      state.isAuthenticated = false;\n      state.loading = false;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading = action.payload;\n    },\n  },\n});\n\nexport const { setUser, clearUser, setLoading } = authSlice.actions;\nexport default authSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAS7D,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,KAAK;EACtBC,OAAO,EAAE;AACX,CAAC;AAED,MAAMC,SAAS,GAAGL,WAAW,CAAC;EAC5BM,IAAI,EAAE,MAAM;EACZL,YAAY;EACZM,QAAQ,EAAE;IACRC,OAAO,EAAEA,CAACC,KAAK,EAAEC,MAA2B,KAAK;MAC/CD,KAAK,CAACP,IAAI,GAAGQ,MAAM,CAACC,OAAO;MAC3BF,KAAK,CAACN,eAAe,GAAG,IAAI;MAC5BM,KAAK,CAACL,OAAO,GAAG,KAAK;IACvB,CAAC;IACDQ,SAAS,EAAGH,KAAK,IAAK;MACpBA,KAAK,CAACP,IAAI,GAAG,IAAI;MACjBO,KAAK,CAACN,eAAe,GAAG,KAAK;MAC7BM,KAAK,CAACL,OAAO,GAAG,KAAK;IACvB,CAAC;IACDS,UAAU,EAAEA,CAACJ,KAAK,EAAEC,MAA8B,KAAK;MACrDD,KAAK,CAACL,OAAO,GAAGM,MAAM,CAACC,OAAO;IAChC;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH,OAAO;EAAEI,SAAS;EAAEC;AAAW,CAAC,GAAGR,SAAS,CAACS,OAAO;AACnE,eAAeT,SAAS,CAACU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}