{"ast": null, "code": "\"use client\";\n\nimport dayjsGenerateConfig from \"rc-picker/es/generate/dayjs\";\nimport generateCalendar from './generateCalendar';\nconst Calendar = generateCalendar(dayjsGenerateConfig);\nCalendar.generateCalendar = generateCalendar;\nexport default Calendar;", "map": {"version": 3, "names": ["dayjsGenerateConfig", "generateCalendar", "Calendar"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/calendar/index.js"], "sourcesContent": ["\"use client\";\n\nimport dayjsGenerateConfig from \"rc-picker/es/generate/dayjs\";\nimport generateCalendar from './generateCalendar';\nconst Calendar = generateCalendar(dayjsGenerateConfig);\nCalendar.generateCalendar = generateCalendar;\nexport default Calendar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,MAAMC,QAAQ,GAAGD,gBAAgB,CAACD,mBAAmB,CAAC;AACtDE,QAAQ,CAACD,gBAAgB,GAAGA,gBAAgB;AAC5C,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}