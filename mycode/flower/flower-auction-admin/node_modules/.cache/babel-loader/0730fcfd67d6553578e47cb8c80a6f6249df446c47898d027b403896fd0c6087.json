{"ast": null, "code": "\"use client\";\n\nimport { Row } from '../grid';\nexport default Row;", "map": {"version": 3, "names": ["Row"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/row/index.js"], "sourcesContent": ["\"use client\";\n\nimport { Row } from '../grid';\nexport default Row;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,GAAG,QAAQ,SAAS;AAC7B,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}