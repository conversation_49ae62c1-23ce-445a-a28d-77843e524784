{"ast": null, "code": "import { generate } from '@ant-design/colors';\nimport { FastColor } from '@ant-design/fast-color';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport warning from '../_util/warning';\nconst dynamicStyleMark = `-ant-${Date.now()}-${Math.random()}`;\nexport function getStyle(globalPrefixCls, theme) {\n  const variables = {};\n  const formatColor = (color, updater) => {\n    let clone = color.clone();\n    clone = (updater === null || updater === void 0 ? void 0 : updater(clone)) || clone;\n    return clone.toRgbString();\n  };\n  const fillColor = (colorVal, type) => {\n    const baseColor = new FastColor(colorVal);\n    const colorPalettes = generate(baseColor.toRgbString());\n    variables[`${type}-color`] = formatColor(baseColor);\n    variables[`${type}-color-disabled`] = colorPalettes[1];\n    variables[`${type}-color-hover`] = colorPalettes[4];\n    variables[`${type}-color-active`] = colorPalettes[6];\n    variables[`${type}-color-outline`] = baseColor.clone().setA(0.2).toRgbString();\n    variables[`${type}-color-deprecated-bg`] = colorPalettes[0];\n    variables[`${type}-color-deprecated-border`] = colorPalettes[2];\n  };\n  // ================ Primary Color ================\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    const primaryColor = new FastColor(theme.primaryColor);\n    const primaryColors = generate(primaryColor.toRgbString());\n    // Legacy - We should use semantic naming standard\n    primaryColors.forEach((color, index) => {\n      variables[`primary-${index + 1}`] = color;\n    });\n    // Deprecated\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, c => c.lighten(35));\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, c => c.lighten(20));\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, c => c.tint(20));\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, c => c.tint(50));\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, c => c.setA(c.a * 0.12));\n    const primaryActiveColor = new FastColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, c => c.setA(c.a * 0.3));\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, c => c.darken(2));\n  }\n  // ================ Success Color ================\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  }\n  // ================ Warning Color ================\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  }\n  // ================= Error Color =================\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  }\n  // ================= Info Color ==================\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  }\n  // Convert to css variables\n  const cssList = Object.keys(variables).map(key => `--${globalPrefixCls}-${key}: ${variables[key]};`);\n  return `\n  :root {\n    ${cssList.join('\\n')}\n  }\n  `.trim();\n}\nexport function registerTheme(globalPrefixCls, theme) {\n  const style = getStyle(globalPrefixCls, theme);\n  if (canUseDom()) {\n    updateCSS(style, `${dynamicStyleMark}-dynamic-theme`);\n  } else {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'SSR do not support dynamic theme with css variables.') : void 0;\n  }\n}", "map": {"version": 3, "names": ["generate", "FastColor", "canUseDom", "updateCSS", "warning", "dynamicStyleMark", "Date", "now", "Math", "random", "getStyle", "globalPrefixCls", "theme", "variables", "formatColor", "color", "updater", "clone", "toRgbString", "fillColor", "colorVal", "type", "baseColor", "colorPalettes", "setA", "primaryColor", "primaryColors", "for<PERSON>ach", "index", "c", "lighten", "tint", "a", "primaryActiveColor", "darken", "successColor", "warningColor", "errorColor", "infoColor", "cssList", "Object", "keys", "map", "key", "join", "trim", "registerTheme", "style", "process", "env", "NODE_ENV"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/config-provider/cssVariables.js"], "sourcesContent": ["import { generate } from '@ant-design/colors';\nimport { FastColor } from '@ant-design/fast-color';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport warning from '../_util/warning';\nconst dynamicStyleMark = `-ant-${Date.now()}-${Math.random()}`;\nexport function getStyle(globalPrefixCls, theme) {\n  const variables = {};\n  const formatColor = (color, updater) => {\n    let clone = color.clone();\n    clone = (updater === null || updater === void 0 ? void 0 : updater(clone)) || clone;\n    return clone.toRgbString();\n  };\n  const fillColor = (colorVal, type) => {\n    const baseColor = new FastColor(colorVal);\n    const colorPalettes = generate(baseColor.toRgbString());\n    variables[`${type}-color`] = formatColor(baseColor);\n    variables[`${type}-color-disabled`] = colorPalettes[1];\n    variables[`${type}-color-hover`] = colorPalettes[4];\n    variables[`${type}-color-active`] = colorPalettes[6];\n    variables[`${type}-color-outline`] = baseColor.clone().setA(0.2).toRgbString();\n    variables[`${type}-color-deprecated-bg`] = colorPalettes[0];\n    variables[`${type}-color-deprecated-border`] = colorPalettes[2];\n  };\n  // ================ Primary Color ================\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    const primaryColor = new FastColor(theme.primaryColor);\n    const primaryColors = generate(primaryColor.toRgbString());\n    // Legacy - We should use semantic naming standard\n    primaryColors.forEach((color, index) => {\n      variables[`primary-${index + 1}`] = color;\n    });\n    // Deprecated\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, c => c.lighten(35));\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, c => c.lighten(20));\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, c => c.tint(20));\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, c => c.tint(50));\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, c => c.setA(c.a * 0.12));\n    const primaryActiveColor = new FastColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, c => c.setA(c.a * 0.3));\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, c => c.darken(2));\n  }\n  // ================ Success Color ================\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  }\n  // ================ Warning Color ================\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  }\n  // ================= Error Color =================\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  }\n  // ================= Info Color ==================\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  }\n  // Convert to css variables\n  const cssList = Object.keys(variables).map(key => `--${globalPrefixCls}-${key}: ${variables[key]};`);\n  return `\n  :root {\n    ${cssList.join('\\n')}\n  }\n  `.trim();\n}\nexport function registerTheme(globalPrefixCls, theme) {\n  const style = getStyle(globalPrefixCls, theme);\n  if (canUseDom()) {\n    updateCSS(style, `${dynamicStyleMark}-dynamic-theme`);\n  } else {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'SSR do not support dynamic theme with css variables.') : void 0;\n  }\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,MAAMC,gBAAgB,GAAG,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;AAC9D,OAAO,SAASC,QAAQA,CAACC,eAAe,EAAEC,KAAK,EAAE;EAC/C,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IACtC,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAAC,CAAC;IACzBA,KAAK,GAAG,CAACD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,KAAK,CAAC,KAAKA,KAAK;IACnF,OAAOA,KAAK,CAACC,WAAW,CAAC,CAAC;EAC5B,CAAC;EACD,MAAMC,SAAS,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IACpC,MAAMC,SAAS,GAAG,IAAIrB,SAAS,CAACmB,QAAQ,CAAC;IACzC,MAAMG,aAAa,GAAGvB,QAAQ,CAACsB,SAAS,CAACJ,WAAW,CAAC,CAAC,CAAC;IACvDL,SAAS,CAAC,GAAGQ,IAAI,QAAQ,CAAC,GAAGP,WAAW,CAACQ,SAAS,CAAC;IACnDT,SAAS,CAAC,GAAGQ,IAAI,iBAAiB,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;IACtDV,SAAS,CAAC,GAAGQ,IAAI,cAAc,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;IACnDV,SAAS,CAAC,GAAGQ,IAAI,eAAe,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;IACpDV,SAAS,CAAC,GAAGQ,IAAI,gBAAgB,CAAC,GAAGC,SAAS,CAACL,KAAK,CAAC,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC,CAACN,WAAW,CAAC,CAAC;IAC9EL,SAAS,CAAC,GAAGQ,IAAI,sBAAsB,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;IAC3DV,SAAS,CAAC,GAAGQ,IAAI,0BAA0B,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;EACjE,CAAC;EACD;EACA,IAAIX,KAAK,CAACa,YAAY,EAAE;IACtBN,SAAS,CAACP,KAAK,CAACa,YAAY,EAAE,SAAS,CAAC;IACxC,MAAMA,YAAY,GAAG,IAAIxB,SAAS,CAACW,KAAK,CAACa,YAAY,CAAC;IACtD,MAAMC,aAAa,GAAG1B,QAAQ,CAACyB,YAAY,CAACP,WAAW,CAAC,CAAC,CAAC;IAC1D;IACAQ,aAAa,CAACC,OAAO,CAAC,CAACZ,KAAK,EAAEa,KAAK,KAAK;MACtCf,SAAS,CAAC,WAAWe,KAAK,GAAG,CAAC,EAAE,CAAC,GAAGb,KAAK;IAC3C,CAAC,CAAC;IACF;IACAF,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAEI,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1FjB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAEI,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1FjB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAEI,CAAC,IAAIA,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;IACvFlB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAEI,CAAC,IAAIA,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;IACvFlB,SAAS,CAAC,+BAA+B,CAAC,GAAGC,WAAW,CAACW,YAAY,EAAEI,CAAC,IAAIA,CAAC,CAACL,IAAI,CAACK,CAAC,CAACG,CAAC,GAAG,IAAI,CAAC,CAAC;IAC/F,MAAMC,kBAAkB,GAAG,IAAIhC,SAAS,CAACyB,aAAa,CAAC,CAAC,CAAC,CAAC;IAC1Db,SAAS,CAAC,sCAAsC,CAAC,GAAGC,WAAW,CAACmB,kBAAkB,EAAEJ,CAAC,IAAIA,CAAC,CAACL,IAAI,CAACK,CAAC,CAACG,CAAC,GAAG,GAAG,CAAC,CAAC;IAC3GnB,SAAS,CAAC,sCAAsC,CAAC,GAAGC,WAAW,CAACmB,kBAAkB,EAAEJ,CAAC,IAAIA,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;EACvG;EACA;EACA,IAAItB,KAAK,CAACuB,YAAY,EAAE;IACtBhB,SAAS,CAACP,KAAK,CAACuB,YAAY,EAAE,SAAS,CAAC;EAC1C;EACA;EACA,IAAIvB,KAAK,CAACwB,YAAY,EAAE;IACtBjB,SAAS,CAACP,KAAK,CAACwB,YAAY,EAAE,SAAS,CAAC;EAC1C;EACA;EACA,IAAIxB,KAAK,CAACyB,UAAU,EAAE;IACpBlB,SAAS,CAACP,KAAK,CAACyB,UAAU,EAAE,OAAO,CAAC;EACtC;EACA;EACA,IAAIzB,KAAK,CAAC0B,SAAS,EAAE;IACnBnB,SAAS,CAACP,KAAK,CAAC0B,SAAS,EAAE,MAAM,CAAC;EACpC;EACA;EACA,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC5B,SAAS,CAAC,CAAC6B,GAAG,CAACC,GAAG,IAAI,KAAKhC,eAAe,IAAIgC,GAAG,KAAK9B,SAAS,CAAC8B,GAAG,CAAC,GAAG,CAAC;EACpG,OAAO;AACT;AACA,MAAMJ,OAAO,CAACK,IAAI,CAAC,IAAI,CAAC;AACxB;AACA,GAAG,CAACC,IAAI,CAAC,CAAC;AACV;AACA,OAAO,SAASC,aAAaA,CAACnC,eAAe,EAAEC,KAAK,EAAE;EACpD,MAAMmC,KAAK,GAAGrC,QAAQ,CAACC,eAAe,EAAEC,KAAK,CAAC;EAC9C,IAAIV,SAAS,CAAC,CAAC,EAAE;IACfC,SAAS,CAAC4C,KAAK,EAAE,GAAG1C,gBAAgB,gBAAgB,CAAC;EACvD,CAAC,MAAM;IACL2C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,sDAAsD,CAAC,GAAG,KAAK,CAAC;EAC3I;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}