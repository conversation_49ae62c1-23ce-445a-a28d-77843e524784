{"ast": null, "code": "\"use client\";\n\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _callSuper from \"@babel/runtime/helpers/esm/callSuper\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport * as React from 'react';\nimport Alert from './Alert';\nlet ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _callSuper(this, ErrorBoundary, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _inherits(ErrorBoundary, _React$Component);\n  return _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error,\n        info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      const {\n        message,\n        description,\n        id,\n        children\n      } = this.props;\n      const {\n        error,\n        info\n      } = this.state;\n      const componentStack = (info === null || info === void 0 ? void 0 : info.componentStack) || null;\n      const errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      const errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          id: id,\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", {\n            style: {\n              fontSize: '0.9em',\n              overflowX: 'auto'\n            }\n          }, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n}(React.Component);\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_callSuper", "_inherits", "React", "<PERSON><PERSON>", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_this", "arguments", "state", "error", "undefined", "info", "componentStack", "key", "value", "componentDidCatch", "setState", "render", "message", "description", "id", "children", "props", "errorMessage", "toString", "errorDescription", "createElement", "type", "style", "fontSize", "overflowX", "Component"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/alert/ErrorBoundary.js"], "sourcesContent": ["\"use client\";\n\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _callSuper from \"@babel/runtime/helpers/esm/callSuper\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport * as React from 'react';\nimport Alert from './Alert';\nlet ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _callSuper(this, ErrorBoundary, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _inherits(ErrorBoundary, _React$Component);\n  return _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error,\n        info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      const {\n        message,\n        description,\n        id,\n        children\n      } = this.props;\n      const {\n        error,\n        info\n      } = this.state;\n      const componentStack = (info === null || info === void 0 ? void 0 : info.componentStack) || null;\n      const errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      const errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          id: id,\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", {\n            style: {\n              fontSize: '0.9em',\n              overflowX: 'auto'\n            }\n          }, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n}(React.Component);\nexport default ErrorBoundary;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC3D,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK;IACTR,eAAe,CAAC,IAAI,EAAEM,aAAa,CAAC;IACpCE,KAAK,GAAGN,UAAU,CAAC,IAAI,EAAEI,aAAa,EAAEG,SAAS,CAAC;IAClDD,KAAK,CAACE,KAAK,GAAG;MACZC,KAAK,EAAEC,SAAS;MAChBC,IAAI,EAAE;QACJC,cAAc,EAAE;MAClB;IACF,CAAC;IACD,OAAON,KAAK;EACd;EACAL,SAAS,CAACG,aAAa,EAAEC,gBAAgB,CAAC;EAC1C,OAAON,YAAY,CAACK,aAAa,EAAE,CAAC;IAClCS,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAACN,KAAK,EAAEE,IAAI,EAAE;MAC7C,IAAI,CAACK,QAAQ,CAAC;QACZP,KAAK;QACLE;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASG,MAAMA,CAAA,EAAG;MACvB,MAAM;QACJC,OAAO;QACPC,WAAW;QACXC,EAAE;QACFC;MACF,CAAC,GAAG,IAAI,CAACC,KAAK;MACd,MAAM;QACJb,KAAK;QACLE;MACF,CAAC,GAAG,IAAI,CAACH,KAAK;MACd,MAAMI,cAAc,GAAG,CAACD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACC,cAAc,KAAK,IAAI;MAChG,MAAMW,YAAY,GAAG,OAAOL,OAAO,KAAK,WAAW,GAAG,CAACT,KAAK,IAAI,EAAE,EAAEe,QAAQ,CAAC,CAAC,GAAGN,OAAO;MACxF,MAAMO,gBAAgB,GAAG,OAAON,WAAW,KAAK,WAAW,GAAGP,cAAc,GAAGO,WAAW;MAC1F,IAAIV,KAAK,EAAE;QACT,OAAO,aAAaP,KAAK,CAACwB,aAAa,CAACvB,KAAK,EAAE;UAC7CiB,EAAE,EAAEA,EAAE;UACNO,IAAI,EAAE,OAAO;UACbT,OAAO,EAAEK,YAAY;UACrBJ,WAAW,EAAE,aAAajB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;YACnDE,KAAK,EAAE;cACLC,QAAQ,EAAE,OAAO;cACjBC,SAAS,EAAE;YACb;UACF,CAAC,EAAEL,gBAAgB;QACrB,CAAC,CAAC;MACJ;MACA,OAAOJ,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAACnB,KAAK,CAAC6B,SAAS,CAAC;AAClB,eAAe3B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}