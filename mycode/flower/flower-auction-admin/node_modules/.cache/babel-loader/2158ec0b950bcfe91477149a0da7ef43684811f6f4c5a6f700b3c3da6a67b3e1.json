{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React, { useCallback, useContext } from 'react';\nimport classNames from 'classnames';\nimport useMultipleSelect from '../_util/hooks/useMultipleSelect';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport { groupDisabledKeysMap, groupKeysMap } from '../_util/transKeys';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport DefaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport { FormItemInputContext } from '../form/context';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nimport useData from './hooks/useData';\nimport useSelection from './hooks/useSelection';\nimport List from './list';\nimport Operation from './operation';\nimport Search from './search';\nimport useStyle from './style';\nconst Transfer = props => {\n  const {\n    dataSource,\n    targetKeys = [],\n    selectedKeys,\n    selectAllLabels = [],\n    operations = [],\n    style = {},\n    listStyle = {},\n    locale = {},\n    titles,\n    disabled,\n    showSearch = false,\n    operationStyle,\n    showSelectAll,\n    oneWay,\n    pagination,\n    status: customStatus,\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    selectionsIcon,\n    filterOption,\n    render,\n    footer,\n    children,\n    rowKey,\n    onScroll,\n    onChange,\n    onSearch,\n    onSelectChange\n  } = props;\n  const {\n    getPrefixCls,\n    renderEmpty,\n    direction: dir,\n    transfer\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('transfer', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // Fill record with `key`\n  const [mergedDataSource, leftDataSource, rightDataSource] = useData(dataSource, rowKey, targetKeys);\n  // Get direction selected keys\n  const [\n  // Keys\n  sourceSelectedKeys, targetSelectedKeys,\n  // Setters\n  setSourceSelectedKeys, setTargetSelectedKeys] = useSelection(leftDataSource, rightDataSource, selectedKeys);\n  const [leftMultipleSelect, updateLeftPrevSelectedIndex] = useMultipleSelect(item => item.key);\n  const [rightMultipleSelect, updateRightPrevSelectedIndex] = useMultipleSelect(item => item.key);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Transfer');\n    process.env.NODE_ENV !== \"production\" ? warning(!pagination || !children, 'usage', '`pagination` not support customize render list.') : void 0;\n  }\n  const setStateKeys = useCallback((direction, keys) => {\n    if (direction === 'left') {\n      const nextKeys = typeof keys === 'function' ? keys(sourceSelectedKeys || []) : keys;\n      setSourceSelectedKeys(nextKeys);\n    } else {\n      const nextKeys = typeof keys === 'function' ? keys(targetSelectedKeys || []) : keys;\n      setTargetSelectedKeys(nextKeys);\n    }\n  }, [sourceSelectedKeys, targetSelectedKeys]);\n  const setPrevSelectedIndex = (direction, value) => {\n    const isLeftDirection = direction === 'left';\n    const updatePrevSelectedIndex = isLeftDirection ? updateLeftPrevSelectedIndex : updateRightPrevSelectedIndex;\n    updatePrevSelectedIndex(value);\n  };\n  const handleSelectChange = useCallback((direction, holder) => {\n    if (direction === 'left') {\n      onSelectChange === null || onSelectChange === void 0 ? void 0 : onSelectChange(holder, targetSelectedKeys);\n    } else {\n      onSelectChange === null || onSelectChange === void 0 ? void 0 : onSelectChange(sourceSelectedKeys, holder);\n    }\n  }, [sourceSelectedKeys, targetSelectedKeys]);\n  const getTitles = transferLocale => {\n    var _a;\n    return (_a = titles !== null && titles !== void 0 ? titles : transferLocale.titles) !== null && _a !== void 0 ? _a : [];\n  };\n  const handleLeftScroll = e => {\n    onScroll === null || onScroll === void 0 ? void 0 : onScroll('left', e);\n  };\n  const handleRightScroll = e => {\n    onScroll === null || onScroll === void 0 ? void 0 : onScroll('right', e);\n  };\n  const moveTo = direction => {\n    const moveKeys = direction === 'right' ? sourceSelectedKeys : targetSelectedKeys;\n    const dataSourceDisabledKeysMap = groupDisabledKeysMap(mergedDataSource);\n    // filter the disabled options\n    const newMoveKeys = moveKeys.filter(key => !dataSourceDisabledKeysMap.has(key));\n    const newMoveKeysMap = groupKeysMap(newMoveKeys);\n    // move items to target box\n    const newTargetKeys = direction === 'right' ? newMoveKeys.concat(targetKeys) : targetKeys.filter(targetKey => !newMoveKeysMap.has(targetKey));\n    // empty checked keys\n    const oppositeDirection = direction === 'right' ? 'left' : 'right';\n    setStateKeys(oppositeDirection, []);\n    handleSelectChange(oppositeDirection, []);\n    onChange === null || onChange === void 0 ? void 0 : onChange(newTargetKeys, direction, newMoveKeys);\n  };\n  const moveToLeft = () => {\n    moveTo('left');\n    setPrevSelectedIndex('left', null);\n  };\n  const moveToRight = () => {\n    moveTo('right');\n    setPrevSelectedIndex('right', null);\n  };\n  const onItemSelectAll = (direction, keys, checkAll) => {\n    setStateKeys(direction, prevKeys => {\n      let mergedCheckedKeys = [];\n      if (checkAll === 'replace') {\n        mergedCheckedKeys = keys;\n      } else if (checkAll) {\n        // Merge current keys with origin key\n        mergedCheckedKeys = Array.from(new Set([].concat(_toConsumableArray(prevKeys), _toConsumableArray(keys))));\n      } else {\n        const selectedKeysMap = groupKeysMap(keys);\n        // Remove current keys from origin keys\n        mergedCheckedKeys = prevKeys.filter(key => !selectedKeysMap.has(key));\n      }\n      handleSelectChange(direction, mergedCheckedKeys);\n      return mergedCheckedKeys;\n    });\n    setPrevSelectedIndex(direction, null);\n  };\n  const onLeftItemSelectAll = (keys, checkAll) => {\n    onItemSelectAll('left', keys, checkAll);\n  };\n  const onRightItemSelectAll = (keys, checkAll) => {\n    onItemSelectAll('right', keys, checkAll);\n  };\n  const leftFilter = e => onSearch === null || onSearch === void 0 ? void 0 : onSearch('left', e.target.value);\n  const rightFilter = e => onSearch === null || onSearch === void 0 ? void 0 : onSearch('right', e.target.value);\n  const handleLeftClear = () => onSearch === null || onSearch === void 0 ? void 0 : onSearch('left', '');\n  const handleRightClear = () => onSearch === null || onSearch === void 0 ? void 0 : onSearch('right', '');\n  const handleSingleSelect = (direction, holder, selectedKey, checked, currentSelectedIndex) => {\n    const isSelected = holder.has(selectedKey);\n    if (isSelected) {\n      holder.delete(selectedKey);\n      setPrevSelectedIndex(direction, null);\n    }\n    if (checked) {\n      holder.add(selectedKey);\n      setPrevSelectedIndex(direction, currentSelectedIndex);\n    }\n  };\n  const handleMultipleSelect = (direction, data, holder, currentSelectedIndex) => {\n    const isLeftDirection = direction === 'left';\n    const multipleSelect = isLeftDirection ? leftMultipleSelect : rightMultipleSelect;\n    multipleSelect(currentSelectedIndex, data, holder);\n  };\n  const onItemSelect = (direction, selectedKey, checked, multiple) => {\n    const isLeftDirection = direction === 'left';\n    const holder = _toConsumableArray(isLeftDirection ? sourceSelectedKeys : targetSelectedKeys);\n    const holderSet = new Set(holder);\n    const data = _toConsumableArray(isLeftDirection ? leftDataSource : rightDataSource).filter(item => !(item === null || item === void 0 ? void 0 : item.disabled));\n    const currentSelectedIndex = data.findIndex(item => item.key === selectedKey);\n    // multiple select by hold down the shift key\n    if (multiple && holder.length > 0) {\n      handleMultipleSelect(direction, data, holderSet, currentSelectedIndex);\n    } else {\n      handleSingleSelect(direction, holderSet, selectedKey, checked, currentSelectedIndex);\n    }\n    const holderArr = Array.from(holderSet);\n    handleSelectChange(direction, holderArr);\n    if (!props.selectedKeys) {\n      setStateKeys(direction, holderArr);\n    }\n  };\n  const onLeftItemSelect = (selectedKey, checked, e) => {\n    onItemSelect('left', selectedKey, checked, e === null || e === void 0 ? void 0 : e.shiftKey);\n  };\n  const onRightItemSelect = (selectedKey, checked, e) => {\n    onItemSelect('right', selectedKey, checked, e === null || e === void 0 ? void 0 : e.shiftKey);\n  };\n  const onRightItemRemove = keys => {\n    setStateKeys('right', []);\n    onChange === null || onChange === void 0 ? void 0 : onChange(targetKeys.filter(key => !keys.includes(key)), 'left', _toConsumableArray(keys));\n  };\n  const handleListStyle = direction => {\n    if (typeof listStyle === 'function') {\n      return listStyle({\n        direction\n      });\n    }\n    return listStyle || {};\n  };\n  const formItemContext = useContext(FormItemInputContext);\n  const {\n    hasFeedback,\n    status\n  } = formItemContext;\n  const getLocale = transferLocale => Object.assign(Object.assign(Object.assign({}, transferLocale), {\n    notFoundContent: (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('Transfer')) || /*#__PURE__*/React.createElement(DefaultRenderEmpty, {\n      componentName: \"Transfer\"\n    })\n  }), locale);\n  const mergedStatus = getMergedStatus(status, customStatus);\n  const mergedPagination = !children && pagination;\n  const leftActive = rightDataSource.filter(d => targetSelectedKeys.includes(d.key) && !d.disabled).length > 0;\n  const rightActive = leftDataSource.filter(d => sourceSelectedKeys.includes(d.key) && !d.disabled).length > 0;\n  const cls = classNames(prefixCls, {\n    [`${prefixCls}-disabled`]: disabled,\n    [`${prefixCls}-customize-list`]: !!children,\n    [`${prefixCls}-rtl`]: dir === 'rtl'\n  }, getStatusClassNames(prefixCls, mergedStatus, hasFeedback), transfer === null || transfer === void 0 ? void 0 : transfer.className, className, rootClassName, hashId, cssVarCls);\n  const [contextLocale] = useLocale('Transfer', defaultLocale.Transfer);\n  const listLocale = getLocale(contextLocale);\n  const [leftTitle, rightTitle] = getTitles(listLocale);\n  const mergedSelectionsIcon = selectionsIcon !== null && selectionsIcon !== void 0 ? selectionsIcon : transfer === null || transfer === void 0 ? void 0 : transfer.selectionsIcon;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: Object.assign(Object.assign({}, transfer === null || transfer === void 0 ? void 0 : transfer.style), style)\n  }, /*#__PURE__*/React.createElement(List, Object.assign({\n    prefixCls: `${prefixCls}-list`,\n    titleText: leftTitle,\n    dataSource: leftDataSource,\n    filterOption: filterOption,\n    style: handleListStyle('left'),\n    checkedKeys: sourceSelectedKeys,\n    handleFilter: leftFilter,\n    handleClear: handleLeftClear,\n    onItemSelect: onLeftItemSelect,\n    onItemSelectAll: onLeftItemSelectAll,\n    render: render,\n    showSearch: showSearch,\n    renderList: children,\n    footer: footer,\n    onScroll: handleLeftScroll,\n    disabled: disabled,\n    direction: dir === 'rtl' ? 'right' : 'left',\n    showSelectAll: showSelectAll,\n    selectAllLabel: selectAllLabels[0],\n    pagination: mergedPagination,\n    selectionsIcon: mergedSelectionsIcon\n  }, listLocale)), /*#__PURE__*/React.createElement(Operation, {\n    className: `${prefixCls}-operation`,\n    rightActive: rightActive,\n    rightArrowText: operations[0],\n    moveToRight: moveToRight,\n    leftActive: leftActive,\n    leftArrowText: operations[1],\n    moveToLeft: moveToLeft,\n    style: operationStyle,\n    disabled: disabled,\n    direction: dir,\n    oneWay: oneWay\n  }), /*#__PURE__*/React.createElement(List, Object.assign({\n    prefixCls: `${prefixCls}-list`,\n    titleText: rightTitle,\n    dataSource: rightDataSource,\n    filterOption: filterOption,\n    style: handleListStyle('right'),\n    checkedKeys: targetSelectedKeys,\n    handleFilter: rightFilter,\n    handleClear: handleRightClear,\n    onItemSelect: onRightItemSelect,\n    onItemSelectAll: onRightItemSelectAll,\n    onItemRemove: onRightItemRemove,\n    render: render,\n    showSearch: showSearch,\n    renderList: children,\n    footer: footer,\n    onScroll: handleRightScroll,\n    disabled: disabled,\n    direction: dir === 'rtl' ? 'left' : 'right',\n    showSelectAll: showSelectAll,\n    selectAllLabel: selectAllLabels[1],\n    showRemove: oneWay,\n    pagination: mergedPagination,\n    selectionsIcon: mergedSelectionsIcon\n  }, listLocale))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Transfer.displayName = 'Transfer';\n}\nTransfer.List = List;\nTransfer.Search = Search;\nTransfer.Operation = Operation;\nexport default Transfer;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "useCallback", "useContext", "classNames", "useMultipleSelect", "getMergedStatus", "getStatusClassNames", "groupDisabledKeysMap", "groupKeysMap", "devUseW<PERSON>ning", "ConfigContext", "DefaultRenderEmpty", "FormItemInputContext", "useLocale", "defaultLocale", "useData", "useSelection", "List", "Operation", "Search", "useStyle", "Transfer", "props", "dataSource", "targetKeys", "<PERSON><PERSON><PERSON><PERSON>", "selectAllLabels", "operations", "style", "listStyle", "locale", "titles", "disabled", "showSearch", "operationStyle", "showSelectAll", "oneWay", "pagination", "status", "customStatus", "prefixCls", "customizePrefixCls", "className", "rootClassName", "selectionsIcon", "filterOption", "render", "footer", "children", "<PERSON><PERSON><PERSON>", "onScroll", "onChange", "onSearch", "onSelectChange", "getPrefixCls", "renderEmpty", "direction", "dir", "transfer", "wrapCSSVar", "hashId", "cssVarCls", "mergedDataSource", "leftDataSource", "rightDataSource", "sourceSelectedKeys", "targetSelectedKeys", "setSourceSelectedKeys", "setTargetSelectedKeys", "leftMultipleSelect", "updateLeftPrevSelectedIndex", "item", "key", "rightMultipleSelect", "updateRightPrevSelectedIndex", "process", "env", "NODE_ENV", "warning", "setStateKeys", "keys", "nextKeys", "setPrevSelectedIndex", "value", "isLeftDirection", "updatePrevSelectedIndex", "handleSelectChange", "holder", "get<PERSON>itles", "transferLocale", "_a", "handleLeftScroll", "e", "handleRightScroll", "moveTo", "move<PERSON>eys", "dataSourceDisabledKeysMap", "newMoveKeys", "filter", "has", "newMoveKeysMap", "newTargetKeys", "concat", "<PERSON><PERSON><PERSON>", "oppositeDirection", "moveToLeft", "moveToRight", "onItemSelectAll", "checkAll", "prevKeys", "mergedCheckedKeys", "Array", "from", "Set", "selectedKeysMap", "onLeftItemSelectAll", "onRightItemSelectAll", "leftFilter", "target", "rightFilter", "handleLeftClear", "handleRightClear", "handleSingleSelect", "<PERSON><PERSON><PERSON>", "checked", "currentSelectedIndex", "isSelected", "delete", "add", "handleMultipleSelect", "data", "multipleSelect", "onItemSelect", "multiple", "holderSet", "findIndex", "length", "<PERSON><PERSON><PERSON>", "onLeftItemSelect", "shift<PERSON>ey", "onRightItemSelect", "onRightItemRemove", "includes", "handleListStyle", "formItemContext", "hasFeedback", "getLocale", "Object", "assign", "notFoundContent", "createElement", "componentName", "mergedStatus", "mergedPagination", "leftActive", "d", "rightActive", "cls", "contextLocale", "listLocale", "leftTitle", "rightTitle", "mergedSelectionsIcon", "titleText", "checked<PERSON>eys", "handleFilter", "handleClear", "renderList", "selectAllLabel", "rightArrowText", "leftArrowText", "onItemRemove", "showRemove", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/transfer/index.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React, { useCallback, useContext } from 'react';\nimport classNames from 'classnames';\nimport useMultipleSelect from '../_util/hooks/useMultipleSelect';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport { groupDisabledKeysMap, groupKeysMap } from '../_util/transKeys';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport DefaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport { FormItemInputContext } from '../form/context';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nimport useData from './hooks/useData';\nimport useSelection from './hooks/useSelection';\nimport List from './list';\nimport Operation from './operation';\nimport Search from './search';\nimport useStyle from './style';\nconst Transfer = props => {\n  const {\n    dataSource,\n    targetKeys = [],\n    selectedKeys,\n    selectAllLabels = [],\n    operations = [],\n    style = {},\n    listStyle = {},\n    locale = {},\n    titles,\n    disabled,\n    showSearch = false,\n    operationStyle,\n    showSelectAll,\n    oneWay,\n    pagination,\n    status: customStatus,\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    selectionsIcon,\n    filterOption,\n    render,\n    footer,\n    children,\n    rowKey,\n    onScroll,\n    onChange,\n    onSearch,\n    onSelectChange\n  } = props;\n  const {\n    getPrefixCls,\n    renderEmpty,\n    direction: dir,\n    transfer\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('transfer', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // Fill record with `key`\n  const [mergedDataSource, leftDataSource, rightDataSource] = useData(dataSource, rowKey, targetKeys);\n  // Get direction selected keys\n  const [\n  // Keys\n  sourceSelectedKeys, targetSelectedKeys,\n  // Setters\n  setSourceSelectedKeys, setTargetSelectedKeys] = useSelection(leftDataSource, rightDataSource, selectedKeys);\n  const [leftMultipleSelect, updateLeftPrevSelectedIndex] = useMultipleSelect(item => item.key);\n  const [rightMultipleSelect, updateRightPrevSelectedIndex] = useMultipleSelect(item => item.key);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Transfer');\n    process.env.NODE_ENV !== \"production\" ? warning(!pagination || !children, 'usage', '`pagination` not support customize render list.') : void 0;\n  }\n  const setStateKeys = useCallback((direction, keys) => {\n    if (direction === 'left') {\n      const nextKeys = typeof keys === 'function' ? keys(sourceSelectedKeys || []) : keys;\n      setSourceSelectedKeys(nextKeys);\n    } else {\n      const nextKeys = typeof keys === 'function' ? keys(targetSelectedKeys || []) : keys;\n      setTargetSelectedKeys(nextKeys);\n    }\n  }, [sourceSelectedKeys, targetSelectedKeys]);\n  const setPrevSelectedIndex = (direction, value) => {\n    const isLeftDirection = direction === 'left';\n    const updatePrevSelectedIndex = isLeftDirection ? updateLeftPrevSelectedIndex : updateRightPrevSelectedIndex;\n    updatePrevSelectedIndex(value);\n  };\n  const handleSelectChange = useCallback((direction, holder) => {\n    if (direction === 'left') {\n      onSelectChange === null || onSelectChange === void 0 ? void 0 : onSelectChange(holder, targetSelectedKeys);\n    } else {\n      onSelectChange === null || onSelectChange === void 0 ? void 0 : onSelectChange(sourceSelectedKeys, holder);\n    }\n  }, [sourceSelectedKeys, targetSelectedKeys]);\n  const getTitles = transferLocale => {\n    var _a;\n    return (_a = titles !== null && titles !== void 0 ? titles : transferLocale.titles) !== null && _a !== void 0 ? _a : [];\n  };\n  const handleLeftScroll = e => {\n    onScroll === null || onScroll === void 0 ? void 0 : onScroll('left', e);\n  };\n  const handleRightScroll = e => {\n    onScroll === null || onScroll === void 0 ? void 0 : onScroll('right', e);\n  };\n  const moveTo = direction => {\n    const moveKeys = direction === 'right' ? sourceSelectedKeys : targetSelectedKeys;\n    const dataSourceDisabledKeysMap = groupDisabledKeysMap(mergedDataSource);\n    // filter the disabled options\n    const newMoveKeys = moveKeys.filter(key => !dataSourceDisabledKeysMap.has(key));\n    const newMoveKeysMap = groupKeysMap(newMoveKeys);\n    // move items to target box\n    const newTargetKeys = direction === 'right' ? newMoveKeys.concat(targetKeys) : targetKeys.filter(targetKey => !newMoveKeysMap.has(targetKey));\n    // empty checked keys\n    const oppositeDirection = direction === 'right' ? 'left' : 'right';\n    setStateKeys(oppositeDirection, []);\n    handleSelectChange(oppositeDirection, []);\n    onChange === null || onChange === void 0 ? void 0 : onChange(newTargetKeys, direction, newMoveKeys);\n  };\n  const moveToLeft = () => {\n    moveTo('left');\n    setPrevSelectedIndex('left', null);\n  };\n  const moveToRight = () => {\n    moveTo('right');\n    setPrevSelectedIndex('right', null);\n  };\n  const onItemSelectAll = (direction, keys, checkAll) => {\n    setStateKeys(direction, prevKeys => {\n      let mergedCheckedKeys = [];\n      if (checkAll === 'replace') {\n        mergedCheckedKeys = keys;\n      } else if (checkAll) {\n        // Merge current keys with origin key\n        mergedCheckedKeys = Array.from(new Set([].concat(_toConsumableArray(prevKeys), _toConsumableArray(keys))));\n      } else {\n        const selectedKeysMap = groupKeysMap(keys);\n        // Remove current keys from origin keys\n        mergedCheckedKeys = prevKeys.filter(key => !selectedKeysMap.has(key));\n      }\n      handleSelectChange(direction, mergedCheckedKeys);\n      return mergedCheckedKeys;\n    });\n    setPrevSelectedIndex(direction, null);\n  };\n  const onLeftItemSelectAll = (keys, checkAll) => {\n    onItemSelectAll('left', keys, checkAll);\n  };\n  const onRightItemSelectAll = (keys, checkAll) => {\n    onItemSelectAll('right', keys, checkAll);\n  };\n  const leftFilter = e => onSearch === null || onSearch === void 0 ? void 0 : onSearch('left', e.target.value);\n  const rightFilter = e => onSearch === null || onSearch === void 0 ? void 0 : onSearch('right', e.target.value);\n  const handleLeftClear = () => onSearch === null || onSearch === void 0 ? void 0 : onSearch('left', '');\n  const handleRightClear = () => onSearch === null || onSearch === void 0 ? void 0 : onSearch('right', '');\n  const handleSingleSelect = (direction, holder, selectedKey, checked, currentSelectedIndex) => {\n    const isSelected = holder.has(selectedKey);\n    if (isSelected) {\n      holder.delete(selectedKey);\n      setPrevSelectedIndex(direction, null);\n    }\n    if (checked) {\n      holder.add(selectedKey);\n      setPrevSelectedIndex(direction, currentSelectedIndex);\n    }\n  };\n  const handleMultipleSelect = (direction, data, holder, currentSelectedIndex) => {\n    const isLeftDirection = direction === 'left';\n    const multipleSelect = isLeftDirection ? leftMultipleSelect : rightMultipleSelect;\n    multipleSelect(currentSelectedIndex, data, holder);\n  };\n  const onItemSelect = (direction, selectedKey, checked, multiple) => {\n    const isLeftDirection = direction === 'left';\n    const holder = _toConsumableArray(isLeftDirection ? sourceSelectedKeys : targetSelectedKeys);\n    const holderSet = new Set(holder);\n    const data = _toConsumableArray(isLeftDirection ? leftDataSource : rightDataSource).filter(item => !(item === null || item === void 0 ? void 0 : item.disabled));\n    const currentSelectedIndex = data.findIndex(item => item.key === selectedKey);\n    // multiple select by hold down the shift key\n    if (multiple && holder.length > 0) {\n      handleMultipleSelect(direction, data, holderSet, currentSelectedIndex);\n    } else {\n      handleSingleSelect(direction, holderSet, selectedKey, checked, currentSelectedIndex);\n    }\n    const holderArr = Array.from(holderSet);\n    handleSelectChange(direction, holderArr);\n    if (!props.selectedKeys) {\n      setStateKeys(direction, holderArr);\n    }\n  };\n  const onLeftItemSelect = (selectedKey, checked, e) => {\n    onItemSelect('left', selectedKey, checked, e === null || e === void 0 ? void 0 : e.shiftKey);\n  };\n  const onRightItemSelect = (selectedKey, checked, e) => {\n    onItemSelect('right', selectedKey, checked, e === null || e === void 0 ? void 0 : e.shiftKey);\n  };\n  const onRightItemRemove = keys => {\n    setStateKeys('right', []);\n    onChange === null || onChange === void 0 ? void 0 : onChange(targetKeys.filter(key => !keys.includes(key)), 'left', _toConsumableArray(keys));\n  };\n  const handleListStyle = direction => {\n    if (typeof listStyle === 'function') {\n      return listStyle({\n        direction\n      });\n    }\n    return listStyle || {};\n  };\n  const formItemContext = useContext(FormItemInputContext);\n  const {\n    hasFeedback,\n    status\n  } = formItemContext;\n  const getLocale = transferLocale => Object.assign(Object.assign(Object.assign({}, transferLocale), {\n    notFoundContent: (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('Transfer')) || /*#__PURE__*/React.createElement(DefaultRenderEmpty, {\n      componentName: \"Transfer\"\n    })\n  }), locale);\n  const mergedStatus = getMergedStatus(status, customStatus);\n  const mergedPagination = !children && pagination;\n  const leftActive = rightDataSource.filter(d => targetSelectedKeys.includes(d.key) && !d.disabled).length > 0;\n  const rightActive = leftDataSource.filter(d => sourceSelectedKeys.includes(d.key) && !d.disabled).length > 0;\n  const cls = classNames(prefixCls, {\n    [`${prefixCls}-disabled`]: disabled,\n    [`${prefixCls}-customize-list`]: !!children,\n    [`${prefixCls}-rtl`]: dir === 'rtl'\n  }, getStatusClassNames(prefixCls, mergedStatus, hasFeedback), transfer === null || transfer === void 0 ? void 0 : transfer.className, className, rootClassName, hashId, cssVarCls);\n  const [contextLocale] = useLocale('Transfer', defaultLocale.Transfer);\n  const listLocale = getLocale(contextLocale);\n  const [leftTitle, rightTitle] = getTitles(listLocale);\n  const mergedSelectionsIcon = selectionsIcon !== null && selectionsIcon !== void 0 ? selectionsIcon : transfer === null || transfer === void 0 ? void 0 : transfer.selectionsIcon;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: Object.assign(Object.assign({}, transfer === null || transfer === void 0 ? void 0 : transfer.style), style)\n  }, /*#__PURE__*/React.createElement(List, Object.assign({\n    prefixCls: `${prefixCls}-list`,\n    titleText: leftTitle,\n    dataSource: leftDataSource,\n    filterOption: filterOption,\n    style: handleListStyle('left'),\n    checkedKeys: sourceSelectedKeys,\n    handleFilter: leftFilter,\n    handleClear: handleLeftClear,\n    onItemSelect: onLeftItemSelect,\n    onItemSelectAll: onLeftItemSelectAll,\n    render: render,\n    showSearch: showSearch,\n    renderList: children,\n    footer: footer,\n    onScroll: handleLeftScroll,\n    disabled: disabled,\n    direction: dir === 'rtl' ? 'right' : 'left',\n    showSelectAll: showSelectAll,\n    selectAllLabel: selectAllLabels[0],\n    pagination: mergedPagination,\n    selectionsIcon: mergedSelectionsIcon\n  }, listLocale)), /*#__PURE__*/React.createElement(Operation, {\n    className: `${prefixCls}-operation`,\n    rightActive: rightActive,\n    rightArrowText: operations[0],\n    moveToRight: moveToRight,\n    leftActive: leftActive,\n    leftArrowText: operations[1],\n    moveToLeft: moveToLeft,\n    style: operationStyle,\n    disabled: disabled,\n    direction: dir,\n    oneWay: oneWay\n  }), /*#__PURE__*/React.createElement(List, Object.assign({\n    prefixCls: `${prefixCls}-list`,\n    titleText: rightTitle,\n    dataSource: rightDataSource,\n    filterOption: filterOption,\n    style: handleListStyle('right'),\n    checkedKeys: targetSelectedKeys,\n    handleFilter: rightFilter,\n    handleClear: handleRightClear,\n    onItemSelect: onRightItemSelect,\n    onItemSelectAll: onRightItemSelectAll,\n    onItemRemove: onRightItemRemove,\n    render: render,\n    showSearch: showSearch,\n    renderList: children,\n    footer: footer,\n    onScroll: handleRightScroll,\n    disabled: disabled,\n    direction: dir === 'rtl' ? 'left' : 'right',\n    showSelectAll: showSelectAll,\n    selectAllLabel: selectAllLabels[1],\n    showRemove: oneWay,\n    pagination: mergedPagination,\n    selectionsIcon: mergedSelectionsIcon\n  }, listLocale))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Transfer.displayName = 'Transfer';\n}\nTransfer.List = List;\nTransfer.Search = Search;\nTransfer.Operation = Operation;\nexport default Transfer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,KAAK,IAAIC,WAAW,EAAEC,UAAU,QAAQ,OAAO;AACtD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,SAASC,eAAe,EAAEC,mBAAmB,QAAQ,sBAAsB;AAC3E,SAASC,oBAAoB,EAAEC,YAAY,QAAQ,oBAAoB;AACvE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,UAAU;IACVC,UAAU,GAAG,EAAE;IACfC,YAAY;IACZC,eAAe,GAAG,EAAE;IACpBC,UAAU,GAAG,EAAE;IACfC,KAAK,GAAG,CAAC,CAAC;IACVC,SAAS,GAAG,CAAC,CAAC;IACdC,MAAM,GAAG,CAAC,CAAC;IACXC,MAAM;IACNC,QAAQ;IACRC,UAAU,GAAG,KAAK;IAClBC,cAAc;IACdC,aAAa;IACbC,MAAM;IACNC,UAAU;IACVC,MAAM,EAAEC,YAAY;IACpBC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,aAAa;IACbC,cAAc;IACdC,YAAY;IACZC,MAAM;IACNC,MAAM;IACNC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,GAAG/B,KAAK;EACT,MAAM;IACJgC,YAAY;IACZC,WAAW;IACXC,SAAS,EAAEC,GAAG;IACdC;EACF,CAAC,GAAGxD,UAAU,CAACQ,aAAa,CAAC;EAC7B,MAAM8B,SAAS,GAAGc,YAAY,CAAC,UAAU,EAAEb,kBAAkB,CAAC;EAC9D,MAAM,CAACkB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAACoB,SAAS,CAAC;EAC3D;EACA,MAAM,CAACsB,gBAAgB,EAAEC,cAAc,EAAEC,eAAe,CAAC,GAAGjD,OAAO,CAACQ,UAAU,EAAE0B,MAAM,EAAEzB,UAAU,CAAC;EACnG;EACA,MAAM;EACN;EACAyC,kBAAkB,EAAEC,kBAAkB;EACtC;EACAC,qBAAqB,EAAEC,qBAAqB,CAAC,GAAGpD,YAAY,CAAC+C,cAAc,EAAEC,eAAe,EAAEvC,YAAY,CAAC;EAC3G,MAAM,CAAC4C,kBAAkB,EAAEC,2BAA2B,CAAC,GAAGlE,iBAAiB,CAACmE,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC;EAC7F,MAAM,CAACC,mBAAmB,EAAEC,4BAA4B,CAAC,GAAGtE,iBAAiB,CAACmE,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC;EAC/F,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGrE,aAAa,CAAC,UAAU,CAAC;IACzCkE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,CAACzC,UAAU,IAAI,CAACW,QAAQ,EAAE,OAAO,EAAE,iDAAiD,CAAC,GAAG,KAAK,CAAC;EAChJ;EACA,MAAM+B,YAAY,GAAG9E,WAAW,CAAC,CAACuD,SAAS,EAAEwB,IAAI,KAAK;IACpD,IAAIxB,SAAS,KAAK,MAAM,EAAE;MACxB,MAAMyB,QAAQ,GAAG,OAAOD,IAAI,KAAK,UAAU,GAAGA,IAAI,CAACf,kBAAkB,IAAI,EAAE,CAAC,GAAGe,IAAI;MACnFb,qBAAqB,CAACc,QAAQ,CAAC;IACjC,CAAC,MAAM;MACL,MAAMA,QAAQ,GAAG,OAAOD,IAAI,KAAK,UAAU,GAAGA,IAAI,CAACd,kBAAkB,IAAI,EAAE,CAAC,GAAGc,IAAI;MACnFZ,qBAAqB,CAACa,QAAQ,CAAC;IACjC;EACF,CAAC,EAAE,CAAChB,kBAAkB,EAAEC,kBAAkB,CAAC,CAAC;EAC5C,MAAMgB,oBAAoB,GAAGA,CAAC1B,SAAS,EAAE2B,KAAK,KAAK;IACjD,MAAMC,eAAe,GAAG5B,SAAS,KAAK,MAAM;IAC5C,MAAM6B,uBAAuB,GAAGD,eAAe,GAAGd,2BAA2B,GAAGI,4BAA4B;IAC5GW,uBAAuB,CAACF,KAAK,CAAC;EAChC,CAAC;EACD,MAAMG,kBAAkB,GAAGrF,WAAW,CAAC,CAACuD,SAAS,EAAE+B,MAAM,KAAK;IAC5D,IAAI/B,SAAS,KAAK,MAAM,EAAE;MACxBH,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACkC,MAAM,EAAErB,kBAAkB,CAAC;IAC5G,CAAC,MAAM;MACLb,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACY,kBAAkB,EAAEsB,MAAM,CAAC;IAC5G;EACF,CAAC,EAAE,CAACtB,kBAAkB,EAAEC,kBAAkB,CAAC,CAAC;EAC5C,MAAMsB,SAAS,GAAGC,cAAc,IAAI;IAClC,IAAIC,EAAE;IACN,OAAO,CAACA,EAAE,GAAG3D,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG0D,cAAc,CAAC1D,MAAM,MAAM,IAAI,IAAI2D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;EACzH,CAAC;EACD,MAAMC,gBAAgB,GAAGC,CAAC,IAAI;IAC5B1C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,MAAM,EAAE0C,CAAC,CAAC;EACzE,CAAC;EACD,MAAMC,iBAAiB,GAAGD,CAAC,IAAI;IAC7B1C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,OAAO,EAAE0C,CAAC,CAAC;EAC1E,CAAC;EACD,MAAME,MAAM,GAAGtC,SAAS,IAAI;IAC1B,MAAMuC,QAAQ,GAAGvC,SAAS,KAAK,OAAO,GAAGS,kBAAkB,GAAGC,kBAAkB;IAChF,MAAM8B,yBAAyB,GAAGzF,oBAAoB,CAACuD,gBAAgB,CAAC;IACxE;IACA,MAAMmC,WAAW,GAAGF,QAAQ,CAACG,MAAM,CAAC1B,GAAG,IAAI,CAACwB,yBAAyB,CAACG,GAAG,CAAC3B,GAAG,CAAC,CAAC;IAC/E,MAAM4B,cAAc,GAAG5F,YAAY,CAACyF,WAAW,CAAC;IAChD;IACA,MAAMI,aAAa,GAAG7C,SAAS,KAAK,OAAO,GAAGyC,WAAW,CAACK,MAAM,CAAC9E,UAAU,CAAC,GAAGA,UAAU,CAAC0E,MAAM,CAACK,SAAS,IAAI,CAACH,cAAc,CAACD,GAAG,CAACI,SAAS,CAAC,CAAC;IAC7I;IACA,MAAMC,iBAAiB,GAAGhD,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAClEuB,YAAY,CAACyB,iBAAiB,EAAE,EAAE,CAAC;IACnClB,kBAAkB,CAACkB,iBAAiB,EAAE,EAAE,CAAC;IACzCrD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACkD,aAAa,EAAE7C,SAAS,EAAEyC,WAAW,CAAC;EACrG,CAAC;EACD,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACvBX,MAAM,CAAC,MAAM,CAAC;IACdZ,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC;EACpC,CAAC;EACD,MAAMwB,WAAW,GAAGA,CAAA,KAAM;IACxBZ,MAAM,CAAC,OAAO,CAAC;IACfZ,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC;EACrC,CAAC;EACD,MAAMyB,eAAe,GAAGA,CAACnD,SAAS,EAAEwB,IAAI,EAAE4B,QAAQ,KAAK;IACrD7B,YAAY,CAACvB,SAAS,EAAEqD,QAAQ,IAAI;MAClC,IAAIC,iBAAiB,GAAG,EAAE;MAC1B,IAAIF,QAAQ,KAAK,SAAS,EAAE;QAC1BE,iBAAiB,GAAG9B,IAAI;MAC1B,CAAC,MAAM,IAAI4B,QAAQ,EAAE;QACnB;QACAE,iBAAiB,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,EAAE,CAACX,MAAM,CAACvG,kBAAkB,CAAC8G,QAAQ,CAAC,EAAE9G,kBAAkB,CAACiF,IAAI,CAAC,CAAC,CAAC,CAAC;MAC5G,CAAC,MAAM;QACL,MAAMkC,eAAe,GAAG1G,YAAY,CAACwE,IAAI,CAAC;QAC1C;QACA8B,iBAAiB,GAAGD,QAAQ,CAACX,MAAM,CAAC1B,GAAG,IAAI,CAAC0C,eAAe,CAACf,GAAG,CAAC3B,GAAG,CAAC,CAAC;MACvE;MACAc,kBAAkB,CAAC9B,SAAS,EAAEsD,iBAAiB,CAAC;MAChD,OAAOA,iBAAiB;IAC1B,CAAC,CAAC;IACF5B,oBAAoB,CAAC1B,SAAS,EAAE,IAAI,CAAC;EACvC,CAAC;EACD,MAAM2D,mBAAmB,GAAGA,CAACnC,IAAI,EAAE4B,QAAQ,KAAK;IAC9CD,eAAe,CAAC,MAAM,EAAE3B,IAAI,EAAE4B,QAAQ,CAAC;EACzC,CAAC;EACD,MAAMQ,oBAAoB,GAAGA,CAACpC,IAAI,EAAE4B,QAAQ,KAAK;IAC/CD,eAAe,CAAC,OAAO,EAAE3B,IAAI,EAAE4B,QAAQ,CAAC;EAC1C,CAAC;EACD,MAAMS,UAAU,GAAGzB,CAAC,IAAIxC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,MAAM,EAAEwC,CAAC,CAAC0B,MAAM,CAACnC,KAAK,CAAC;EAC5G,MAAMoC,WAAW,GAAG3B,CAAC,IAAIxC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,OAAO,EAAEwC,CAAC,CAAC0B,MAAM,CAACnC,KAAK,CAAC;EAC9G,MAAMqC,eAAe,GAAGA,CAAA,KAAMpE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;EACtG,MAAMqE,gBAAgB,GAAGA,CAAA,KAAMrE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;EACxG,MAAMsE,kBAAkB,GAAGA,CAAClE,SAAS,EAAE+B,MAAM,EAAEoC,WAAW,EAAEC,OAAO,EAAEC,oBAAoB,KAAK;IAC5F,MAAMC,UAAU,GAAGvC,MAAM,CAACY,GAAG,CAACwB,WAAW,CAAC;IAC1C,IAAIG,UAAU,EAAE;MACdvC,MAAM,CAACwC,MAAM,CAACJ,WAAW,CAAC;MAC1BzC,oBAAoB,CAAC1B,SAAS,EAAE,IAAI,CAAC;IACvC;IACA,IAAIoE,OAAO,EAAE;MACXrC,MAAM,CAACyC,GAAG,CAACL,WAAW,CAAC;MACvBzC,oBAAoB,CAAC1B,SAAS,EAAEqE,oBAAoB,CAAC;IACvD;EACF,CAAC;EACD,MAAMI,oBAAoB,GAAGA,CAACzE,SAAS,EAAE0E,IAAI,EAAE3C,MAAM,EAAEsC,oBAAoB,KAAK;IAC9E,MAAMzC,eAAe,GAAG5B,SAAS,KAAK,MAAM;IAC5C,MAAM2E,cAAc,GAAG/C,eAAe,GAAGf,kBAAkB,GAAGI,mBAAmB;IACjF0D,cAAc,CAACN,oBAAoB,EAAEK,IAAI,EAAE3C,MAAM,CAAC;EACpD,CAAC;EACD,MAAM6C,YAAY,GAAGA,CAAC5E,SAAS,EAAEmE,WAAW,EAAEC,OAAO,EAAES,QAAQ,KAAK;IAClE,MAAMjD,eAAe,GAAG5B,SAAS,KAAK,MAAM;IAC5C,MAAM+B,MAAM,GAAGxF,kBAAkB,CAACqF,eAAe,GAAGnB,kBAAkB,GAAGC,kBAAkB,CAAC;IAC5F,MAAMoE,SAAS,GAAG,IAAIrB,GAAG,CAAC1B,MAAM,CAAC;IACjC,MAAM2C,IAAI,GAAGnI,kBAAkB,CAACqF,eAAe,GAAGrB,cAAc,GAAGC,eAAe,CAAC,CAACkC,MAAM,CAAC3B,IAAI,IAAI,EAAEA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACvC,QAAQ,CAAC,CAAC;IAChK,MAAM6F,oBAAoB,GAAGK,IAAI,CAACK,SAAS,CAAChE,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAKmD,WAAW,CAAC;IAC7E;IACA,IAAIU,QAAQ,IAAI9C,MAAM,CAACiD,MAAM,GAAG,CAAC,EAAE;MACjCP,oBAAoB,CAACzE,SAAS,EAAE0E,IAAI,EAAEI,SAAS,EAAET,oBAAoB,CAAC;IACxE,CAAC,MAAM;MACLH,kBAAkB,CAAClE,SAAS,EAAE8E,SAAS,EAAEX,WAAW,EAAEC,OAAO,EAAEC,oBAAoB,CAAC;IACtF;IACA,MAAMY,SAAS,GAAG1B,KAAK,CAACC,IAAI,CAACsB,SAAS,CAAC;IACvChD,kBAAkB,CAAC9B,SAAS,EAAEiF,SAAS,CAAC;IACxC,IAAI,CAACnH,KAAK,CAACG,YAAY,EAAE;MACvBsD,YAAY,CAACvB,SAAS,EAAEiF,SAAS,CAAC;IACpC;EACF,CAAC;EACD,MAAMC,gBAAgB,GAAGA,CAACf,WAAW,EAAEC,OAAO,EAAEhC,CAAC,KAAK;IACpDwC,YAAY,CAAC,MAAM,EAAET,WAAW,EAAEC,OAAO,EAAEhC,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC+C,QAAQ,CAAC;EAC9F,CAAC;EACD,MAAMC,iBAAiB,GAAGA,CAACjB,WAAW,EAAEC,OAAO,EAAEhC,CAAC,KAAK;IACrDwC,YAAY,CAAC,OAAO,EAAET,WAAW,EAAEC,OAAO,EAAEhC,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC+C,QAAQ,CAAC;EAC/F,CAAC;EACD,MAAME,iBAAiB,GAAG7D,IAAI,IAAI;IAChCD,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;IACzB5B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC3B,UAAU,CAAC0E,MAAM,CAAC1B,GAAG,IAAI,CAACQ,IAAI,CAAC8D,QAAQ,CAACtE,GAAG,CAAC,CAAC,EAAE,MAAM,EAAEzE,kBAAkB,CAACiF,IAAI,CAAC,CAAC;EAC/I,CAAC;EACD,MAAM+D,eAAe,GAAGvF,SAAS,IAAI;IACnC,IAAI,OAAO3B,SAAS,KAAK,UAAU,EAAE;MACnC,OAAOA,SAAS,CAAC;QACf2B;MACF,CAAC,CAAC;IACJ;IACA,OAAO3B,SAAS,IAAI,CAAC,CAAC;EACxB,CAAC;EACD,MAAMmH,eAAe,GAAG9I,UAAU,CAACU,oBAAoB,CAAC;EACxD,MAAM;IACJqI,WAAW;IACX3G;EACF,CAAC,GAAG0G,eAAe;EACnB,MAAME,SAAS,GAAGzD,cAAc,IAAI0D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3D,cAAc,CAAC,EAAE;IACjG4D,eAAe,EAAE,CAAC9F,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,UAAU,CAAC,KAAK,aAAavD,KAAK,CAACsJ,aAAa,CAAC3I,kBAAkB,EAAE;MAC3J4I,aAAa,EAAE;IACjB,CAAC;EACH,CAAC,CAAC,EAAEzH,MAAM,CAAC;EACX,MAAM0H,YAAY,GAAGnJ,eAAe,CAACiC,MAAM,EAAEC,YAAY,CAAC;EAC1D,MAAMkH,gBAAgB,GAAG,CAACzG,QAAQ,IAAIX,UAAU;EAChD,MAAMqH,UAAU,GAAG1F,eAAe,CAACkC,MAAM,CAACyD,CAAC,IAAIzF,kBAAkB,CAAC4E,QAAQ,CAACa,CAAC,CAACnF,GAAG,CAAC,IAAI,CAACmF,CAAC,CAAC3H,QAAQ,CAAC,CAACwG,MAAM,GAAG,CAAC;EAC5G,MAAMoB,WAAW,GAAG7F,cAAc,CAACmC,MAAM,CAACyD,CAAC,IAAI1F,kBAAkB,CAAC6E,QAAQ,CAACa,CAAC,CAACnF,GAAG,CAAC,IAAI,CAACmF,CAAC,CAAC3H,QAAQ,CAAC,CAACwG,MAAM,GAAG,CAAC;EAC5G,MAAMqB,GAAG,GAAG1J,UAAU,CAACqC,SAAS,EAAE;IAChC,CAAC,GAAGA,SAAS,WAAW,GAAGR,QAAQ;IACnC,CAAC,GAAGQ,SAAS,iBAAiB,GAAG,CAAC,CAACQ,QAAQ;IAC3C,CAAC,GAAGR,SAAS,MAAM,GAAGiB,GAAG,KAAK;EAChC,CAAC,EAAEnD,mBAAmB,CAACkC,SAAS,EAAEgH,YAAY,EAAEP,WAAW,CAAC,EAAEvF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAChB,SAAS,EAAEA,SAAS,EAAEC,aAAa,EAAEiB,MAAM,EAAEC,SAAS,CAAC;EAClL,MAAM,CAACiG,aAAa,CAAC,GAAGjJ,SAAS,CAAC,UAAU,EAAEC,aAAa,CAACO,QAAQ,CAAC;EACrE,MAAM0I,UAAU,GAAGb,SAAS,CAACY,aAAa,CAAC;EAC3C,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,GAAGzE,SAAS,CAACuE,UAAU,CAAC;EACrD,MAAMG,oBAAoB,GAAGtH,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGc,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACd,cAAc;EAChL,OAAOe,UAAU,CAAC,aAAa3D,KAAK,CAACsJ,aAAa,CAAC,KAAK,EAAE;IACxD5G,SAAS,EAAEmH,GAAG;IACdjI,KAAK,EAAEuH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1F,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC9B,KAAK,CAAC,EAAEA,KAAK;EACnH,CAAC,EAAE,aAAa5B,KAAK,CAACsJ,aAAa,CAACrI,IAAI,EAAEkI,MAAM,CAACC,MAAM,CAAC;IACtD5G,SAAS,EAAE,GAAGA,SAAS,OAAO;IAC9B2H,SAAS,EAAEH,SAAS;IACpBzI,UAAU,EAAEwC,cAAc;IAC1BlB,YAAY,EAAEA,YAAY;IAC1BjB,KAAK,EAAEmH,eAAe,CAAC,MAAM,CAAC;IAC9BqB,WAAW,EAAEnG,kBAAkB;IAC/BoG,YAAY,EAAEhD,UAAU;IACxBiD,WAAW,EAAE9C,eAAe;IAC5BY,YAAY,EAAEM,gBAAgB;IAC9B/B,eAAe,EAAEQ,mBAAmB;IACpCrE,MAAM,EAAEA,MAAM;IACdb,UAAU,EAAEA,UAAU;IACtBsI,UAAU,EAAEvH,QAAQ;IACpBD,MAAM,EAAEA,MAAM;IACdG,QAAQ,EAAEyC,gBAAgB;IAC1B3D,QAAQ,EAAEA,QAAQ;IAClBwB,SAAS,EAAEC,GAAG,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IAC3CtB,aAAa,EAAEA,aAAa;IAC5BqI,cAAc,EAAE9I,eAAe,CAAC,CAAC,CAAC;IAClCW,UAAU,EAAEoH,gBAAgB;IAC5B7G,cAAc,EAAEsH;EAClB,CAAC,EAAEH,UAAU,CAAC,CAAC,EAAE,aAAa/J,KAAK,CAACsJ,aAAa,CAACpI,SAAS,EAAE;IAC3DwB,SAAS,EAAE,GAAGF,SAAS,YAAY;IACnCoH,WAAW,EAAEA,WAAW;IACxBa,cAAc,EAAE9I,UAAU,CAAC,CAAC,CAAC;IAC7B+E,WAAW,EAAEA,WAAW;IACxBgD,UAAU,EAAEA,UAAU;IACtBgB,aAAa,EAAE/I,UAAU,CAAC,CAAC,CAAC;IAC5B8E,UAAU,EAAEA,UAAU;IACtB7E,KAAK,EAAEM,cAAc;IACrBF,QAAQ,EAAEA,QAAQ;IAClBwB,SAAS,EAAEC,GAAG;IACdrB,MAAM,EAAEA;EACV,CAAC,CAAC,EAAE,aAAapC,KAAK,CAACsJ,aAAa,CAACrI,IAAI,EAAEkI,MAAM,CAACC,MAAM,CAAC;IACvD5G,SAAS,EAAE,GAAGA,SAAS,OAAO;IAC9B2H,SAAS,EAAEF,UAAU;IACrB1I,UAAU,EAAEyC,eAAe;IAC3BnB,YAAY,EAAEA,YAAY;IAC1BjB,KAAK,EAAEmH,eAAe,CAAC,OAAO,CAAC;IAC/BqB,WAAW,EAAElG,kBAAkB;IAC/BmG,YAAY,EAAE9C,WAAW;IACzB+C,WAAW,EAAE7C,gBAAgB;IAC7BW,YAAY,EAAEQ,iBAAiB;IAC/BjC,eAAe,EAAES,oBAAoB;IACrCuD,YAAY,EAAE9B,iBAAiB;IAC/B/F,MAAM,EAAEA,MAAM;IACdb,UAAU,EAAEA,UAAU;IACtBsI,UAAU,EAAEvH,QAAQ;IACpBD,MAAM,EAAEA,MAAM;IACdG,QAAQ,EAAE2C,iBAAiB;IAC3B7D,QAAQ,EAAEA,QAAQ;IAClBwB,SAAS,EAAEC,GAAG,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC3CtB,aAAa,EAAEA,aAAa;IAC5BqI,cAAc,EAAE9I,eAAe,CAAC,CAAC,CAAC;IAClCkJ,UAAU,EAAExI,MAAM;IAClBC,UAAU,EAAEoH,gBAAgB;IAC5B7G,cAAc,EAAEsH;EAClB,CAAC,EAAEH,UAAU,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC;AACD,IAAIpF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCxD,QAAQ,CAACwJ,WAAW,GAAG,UAAU;AACnC;AACAxJ,QAAQ,CAACJ,IAAI,GAAGA,IAAI;AACpBI,QAAQ,CAACF,MAAM,GAAGA,MAAM;AACxBE,QAAQ,CAACH,SAAS,GAAGA,SAAS;AAC9B,eAAeG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}