{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { render, unmount } from \"rc-util/es/React/render\";\nimport warning from '../_util/warning';\nconst defaultReactRender = (node, container) => {\n  // TODO: Remove in v6\n  // Warning for React 19\n  if (process.env.NODE_ENV !== 'production') {\n    const majorVersion = parseInt(React.version.split('.')[0], 10);\n    const fullKeys = Object.keys(ReactDOM);\n    process.env.NODE_ENV !== \"production\" ? warning(majorVersion < 19 || fullKeys.includes('createRoot'), 'compatible', 'antd v5 support React is 16 ~ 18. see https://u.ant.design/v5-for-19 for compatible.') : void 0;\n  }\n  render(node, container);\n  return () => {\n    return unmount(container);\n  };\n};\nlet unstableRender = defaultReactRender;\n/**\n * @deprecated Set React render function for compatible usage.\n * This is internal usage only compatible with React 19.\n * And will be removed in next major version.\n */\nexport function unstableSetRender(render) {\n  if (render) {\n    unstableRender = render;\n  }\n  return unstableRender;\n}", "map": {"version": 3, "names": ["React", "ReactDOM", "render", "unmount", "warning", "defaultReactRender", "node", "container", "process", "env", "NODE_ENV", "majorVersion", "parseInt", "version", "split", "fullKeys", "Object", "keys", "includes", "<PERSON><PERSON><PERSON>", "unstableSetRender"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/config-provider/UnstableContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { render, unmount } from \"rc-util/es/React/render\";\nimport warning from '../_util/warning';\nconst defaultReactRender = (node, container) => {\n  // TODO: Remove in v6\n  // Warning for React 19\n  if (process.env.NODE_ENV !== 'production') {\n    const majorVersion = parseInt(React.version.split('.')[0], 10);\n    const fullKeys = Object.keys(ReactDOM);\n    process.env.NODE_ENV !== \"production\" ? warning(majorVersion < 19 || fullKeys.includes('createRoot'), 'compatible', 'antd v5 support React is 16 ~ 18. see https://u.ant.design/v5-for-19 for compatible.') : void 0;\n  }\n  render(node, container);\n  return () => {\n    return unmount(container);\n  };\n};\nlet unstableRender = defaultReactRender;\n/**\n * @deprecated Set React render function for compatible usage.\n * This is internal usage only compatible with React 19.\n * And will be removed in next major version.\n */\nexport function unstableSetRender(render) {\n  if (render) {\n    unstableRender = render;\n  }\n  return unstableRender;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,MAAM,EAAEC,OAAO,QAAQ,yBAAyB;AACzD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;EAC9C;EACA;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,YAAY,GAAGC,QAAQ,CAACZ,KAAK,CAACa,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9D,MAAMC,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAAChB,QAAQ,CAAC;IACtCO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGN,OAAO,CAACO,YAAY,GAAG,EAAE,IAAII,QAAQ,CAACG,QAAQ,CAAC,YAAY,CAAC,EAAE,YAAY,EAAE,sFAAsF,CAAC,GAAG,KAAK,CAAC;EACtN;EACAhB,MAAM,CAACI,IAAI,EAAEC,SAAS,CAAC;EACvB,OAAO,MAAM;IACX,OAAOJ,OAAO,CAACI,SAAS,CAAC;EAC3B,CAAC;AACH,CAAC;AACD,IAAIY,cAAc,GAAGd,kBAAkB;AACvC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASe,iBAAiBA,CAAClB,MAAM,EAAE;EACxC,IAAIA,MAAM,EAAE;IACViB,cAAc,GAAGjB,MAAM;EACzB;EACA,OAAOiB,cAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}