{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst SizeContext = /*#__PURE__*/React.createContext(undefined);\nexport const SizeContextProvider = ({\n  children,\n  size\n}) => {\n  const originSize = React.useContext(SizeContext);\n  return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n    value: size || originSize\n  }, children);\n};\nexport default SizeContext;", "map": {"version": 3, "names": ["React", "SizeContext", "createContext", "undefined", "SizeContextProvider", "children", "size", "originSize", "useContext", "createElement", "Provider", "value"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/config-provider/SizeContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nconst SizeContext = /*#__PURE__*/React.createContext(undefined);\nexport const SizeContextProvider = ({\n  children,\n  size\n}) => {\n  const originSize = React.useContext(SizeContext);\n  return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n    value: size || originSize\n  }, children);\n};\nexport default SizeContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAC/D,OAAO,MAAMC,mBAAmB,GAAGA,CAAC;EAClCC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,UAAU,GAAGP,KAAK,CAACQ,UAAU,CAACP,WAAW,CAAC;EAChD,OAAO,aAAaD,KAAK,CAACS,aAAa,CAACR,WAAW,CAACS,QAAQ,EAAE;IAC5DC,KAAK,EAAEL,IAAI,IAAIC;EACjB,CAAC,EAAEF,QAAQ,CAAC;AACd,CAAC;AACD,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}