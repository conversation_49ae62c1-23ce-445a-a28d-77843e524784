{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { createTheme, StyleContext as CssInJsStyleContext } from '@ant-design/cssinjs';\nimport IconContext from \"@ant-design/icons/es/components/Context\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { merge } from \"rc-util/es/utils/set\";\nimport warning, { devUseWarning, WarningContext } from '../_util/warning';\nimport ValidateMessagesContext from '../form/validateMessagesContext';\nimport LocaleProvider, { ANT_MARK } from '../locale';\nimport LocaleContext from '../locale/context';\nimport defaultLocale from '../locale/en_US';\nimport { defaultTheme, DesignTokenContext } from '../theme/context';\nimport defaultSeedToken from '../theme/themes/seed';\nimport { ConfigConsumer, ConfigContext, defaultIconPrefixCls, defaultPrefixCls, Variants } from './context';\nimport { registerTheme } from './cssVariables';\nimport { DisabledContextProvider } from './DisabledContext';\nimport useConfig from './hooks/useConfig';\nimport useTheme from './hooks/useTheme';\nimport MotionWrapper from './MotionWrapper';\nimport PropWarning from './PropWarning';\nimport SizeContext, { SizeContextProvider } from './SizeContext';\nimport useStyle from './style';\nexport { Variants };\n/**\n * Since too many feedback using static method like `Modal.confirm` not getting theme, we record the\n * theme register info here to help developer get warning info.\n */\nlet existThemeConfig = false;\nexport const warnContext = process.env.NODE_ENV !== 'production' ? componentName => {\n  process.env.NODE_ENV !== \"production\" ? warning(!existThemeConfig, componentName, `Static function can not consume context like dynamic theme. Please use 'App' component instead.`) : void 0;\n} : /* istanbul ignore next */\nnull;\nexport { ConfigConsumer, ConfigContext, defaultPrefixCls, defaultIconPrefixCls };\nexport const configConsumerProps = ['getTargetContainer', 'getPopupContainer', 'rootPrefixCls', 'getPrefixCls', 'renderEmpty', 'csp', 'autoInsertSpaceInButton', 'locale'];\n// These props is used by `useContext` directly in sub component\nconst PASSED_PROPS = ['getTargetContainer', 'getPopupContainer', 'renderEmpty', 'input', 'pagination', 'form', 'select', 'button'];\nlet globalPrefixCls;\nlet globalIconPrefixCls;\nlet globalTheme;\nlet globalHolderRender;\nfunction getGlobalPrefixCls() {\n  return globalPrefixCls || defaultPrefixCls;\n}\nfunction getGlobalIconPrefixCls() {\n  return globalIconPrefixCls || defaultIconPrefixCls;\n}\nfunction isLegacyTheme(theme) {\n  return Object.keys(theme).some(key => key.endsWith('Color'));\n}\nconst setGlobalConfig = props => {\n  const {\n    prefixCls,\n    iconPrefixCls,\n    theme,\n    holderRender\n  } = props;\n  if (prefixCls !== undefined) {\n    globalPrefixCls = prefixCls;\n  }\n  if (iconPrefixCls !== undefined) {\n    globalIconPrefixCls = iconPrefixCls;\n  }\n  if ('holderRender' in props) {\n    globalHolderRender = holderRender;\n  }\n  if (theme) {\n    if (isLegacyTheme(theme)) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', '`config` of css variable theme is not work in v5. Please use new `theme` config instead.') : void 0;\n      registerTheme(getGlobalPrefixCls(), theme);\n    } else {\n      globalTheme = theme;\n    }\n  }\n};\nexport const globalConfig = () => ({\n  getPrefixCls: (suffixCls, customizePrefixCls) => {\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    return suffixCls ? `${getGlobalPrefixCls()}-${suffixCls}` : getGlobalPrefixCls();\n  },\n  getIconPrefixCls: getGlobalIconPrefixCls,\n  getRootPrefixCls: () => {\n    // If Global prefixCls provided, use this\n    if (globalPrefixCls) {\n      return globalPrefixCls;\n    }\n    // Fallback to default prefixCls\n    return getGlobalPrefixCls();\n  },\n  getTheme: () => globalTheme,\n  holderRender: globalHolderRender\n});\nconst ProviderChildren = props => {\n  const {\n    children,\n    csp: customCsp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    form,\n    locale,\n    componentSize,\n    direction,\n    space,\n    splitter,\n    virtual,\n    dropdownMatchSelectWidth,\n    popupMatchSelectWidth,\n    popupOverflow,\n    legacyLocale,\n    parentContext,\n    iconPrefixCls: customIconPrefixCls,\n    theme,\n    componentDisabled,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    input,\n    textArea,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  } = props;\n  // =================================== Context ===================================\n  const getPrefixCls = React.useCallback((suffixCls, customizePrefixCls) => {\n    const {\n      prefixCls\n    } = props;\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    const mergedPrefixCls = prefixCls || parentContext.getPrefixCls('');\n    return suffixCls ? `${mergedPrefixCls}-${suffixCls}` : mergedPrefixCls;\n  }, [parentContext.getPrefixCls, props.prefixCls]);\n  const iconPrefixCls = customIconPrefixCls || parentContext.iconPrefixCls || defaultIconPrefixCls;\n  const csp = customCsp || parentContext.csp;\n  useStyle(iconPrefixCls, csp);\n  const mergedTheme = useTheme(theme, parentContext.theme, {\n    prefixCls: getPrefixCls('')\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    existThemeConfig = existThemeConfig || !!mergedTheme;\n  }\n  const baseConfig = {\n    csp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    locale: locale || legacyLocale,\n    direction,\n    space,\n    splitter,\n    virtual,\n    popupMatchSelectWidth: popupMatchSelectWidth !== null && popupMatchSelectWidth !== void 0 ? popupMatchSelectWidth : dropdownMatchSelectWidth,\n    popupOverflow,\n    getPrefixCls,\n    iconPrefixCls,\n    theme: mergedTheme,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    input,\n    textArea,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    const warningFn = devUseWarning('ConfigProvider');\n    warningFn(!('autoInsertSpaceInButton' in props), 'deprecated', '`autoInsertSpaceInButton` is deprecated. Please use `{ button: { autoInsertSpace: boolean }}` instead.');\n  }\n  const config = Object.assign({}, parentContext);\n  Object.keys(baseConfig).forEach(key => {\n    if (baseConfig[key] !== undefined) {\n      config[key] = baseConfig[key];\n    }\n  });\n  // Pass the props used by `useContext` directly with child component.\n  // These props should merged into `config`.\n  PASSED_PROPS.forEach(propName => {\n    const propValue = props[propName];\n    if (propValue) {\n      config[propName] = propValue;\n    }\n  });\n  if (typeof autoInsertSpaceInButton !== 'undefined') {\n    // merge deprecated api\n    config.button = Object.assign({\n      autoInsertSpace: autoInsertSpaceInButton\n    }, config.button);\n  }\n  // https://github.com/ant-design/ant-design/issues/27617\n  const memoedConfig = useMemo(() => config, config, (prevConfig, currentConfig) => {\n    const prevKeys = Object.keys(prevConfig);\n    const currentKeys = Object.keys(currentConfig);\n    return prevKeys.length !== currentKeys.length || prevKeys.some(key => prevConfig[key] !== currentConfig[key]);\n  });\n  const {\n    layer\n  } = React.useContext(CssInJsStyleContext);\n  const memoIconContextValue = React.useMemo(() => ({\n    prefixCls: iconPrefixCls,\n    csp,\n    layer: layer ? 'antd' : undefined\n  }), [iconPrefixCls, csp, layer]);\n  let childNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PropWarning, {\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }), children);\n  const validateMessages = React.useMemo(() => {\n    var _a, _b, _c, _d;\n    return merge(((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.defaultValidateMessages) || {}, ((_c = (_b = memoedConfig.locale) === null || _b === void 0 ? void 0 : _b.Form) === null || _c === void 0 ? void 0 : _c.defaultValidateMessages) || {}, ((_d = memoedConfig.form) === null || _d === void 0 ? void 0 : _d.validateMessages) || {}, (form === null || form === void 0 ? void 0 : form.validateMessages) || {});\n  }, [memoedConfig, form === null || form === void 0 ? void 0 : form.validateMessages]);\n  if (Object.keys(validateMessages).length > 0) {\n    childNode = /*#__PURE__*/React.createElement(ValidateMessagesContext.Provider, {\n      value: validateMessages\n    }, childNode);\n  }\n  if (locale) {\n    childNode = /*#__PURE__*/React.createElement(LocaleProvider, {\n      locale: locale,\n      _ANT_MARK__: ANT_MARK\n    }, childNode);\n  }\n  if (iconPrefixCls || csp) {\n    childNode = /*#__PURE__*/React.createElement(IconContext.Provider, {\n      value: memoIconContextValue\n    }, childNode);\n  }\n  if (componentSize) {\n    childNode = /*#__PURE__*/React.createElement(SizeContextProvider, {\n      size: componentSize\n    }, childNode);\n  }\n  // =================================== Motion ===================================\n  childNode = /*#__PURE__*/React.createElement(MotionWrapper, null, childNode);\n  // ================================ Dynamic theme ================================\n  const memoTheme = React.useMemo(() => {\n    const _a = mergedTheme || {},\n      {\n        algorithm,\n        token,\n        components,\n        cssVar\n      } = _a,\n      rest = __rest(_a, [\"algorithm\", \"token\", \"components\", \"cssVar\"]);\n    const themeObj = algorithm && (!Array.isArray(algorithm) || algorithm.length > 0) ? createTheme(algorithm) : defaultTheme;\n    const parsedComponents = {};\n    Object.entries(components || {}).forEach(([componentName, componentToken]) => {\n      const parsedToken = Object.assign({}, componentToken);\n      if ('algorithm' in parsedToken) {\n        if (parsedToken.algorithm === true) {\n          parsedToken.theme = themeObj;\n        } else if (Array.isArray(parsedToken.algorithm) || typeof parsedToken.algorithm === 'function') {\n          parsedToken.theme = createTheme(parsedToken.algorithm);\n        }\n        delete parsedToken.algorithm;\n      }\n      parsedComponents[componentName] = parsedToken;\n    });\n    const mergedToken = Object.assign(Object.assign({}, defaultSeedToken), token);\n    return Object.assign(Object.assign({}, rest), {\n      theme: themeObj,\n      token: mergedToken,\n      components: parsedComponents,\n      override: Object.assign({\n        override: mergedToken\n      }, parsedComponents),\n      cssVar: cssVar\n    });\n  }, [mergedTheme]);\n  if (theme) {\n    childNode = /*#__PURE__*/React.createElement(DesignTokenContext.Provider, {\n      value: memoTheme\n    }, childNode);\n  }\n  // ================================== Warning ===================================\n  if (memoedConfig.warning) {\n    childNode = /*#__PURE__*/React.createElement(WarningContext.Provider, {\n      value: memoedConfig.warning\n    }, childNode);\n  }\n  // =================================== Render ===================================\n  if (componentDisabled !== undefined) {\n    childNode = /*#__PURE__*/React.createElement(DisabledContextProvider, {\n      disabled: componentDisabled\n    }, childNode);\n  }\n  return /*#__PURE__*/React.createElement(ConfigContext.Provider, {\n    value: memoedConfig\n  }, childNode);\n};\nconst ConfigProvider = props => {\n  const context = React.useContext(ConfigContext);\n  const antLocale = React.useContext(LocaleContext);\n  return /*#__PURE__*/React.createElement(ProviderChildren, Object.assign({\n    parentContext: context,\n    legacyLocale: antLocale\n  }, props));\n};\nConfigProvider.ConfigContext = ConfigContext;\nConfigProvider.SizeContext = SizeContext;\nConfigProvider.config = setGlobalConfig;\nConfigProvider.useConfig = useConfig;\nObject.defineProperty(ConfigProvider, 'SizeContext', {\n  get: () => {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'ConfigProvider.SizeContext is deprecated. Please use `ConfigProvider.useConfig().componentSize` instead.') : void 0;\n    return SizeContext;\n  }\n});\nif (process.env.NODE_ENV !== 'production') {\n  ConfigProvider.displayName = 'ConfigProvider';\n}\nexport default ConfigProvider;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "createTheme", "StyleContext", "CssInJsStyleContext", "IconContext", "useMemo", "merge", "warning", "devUseW<PERSON>ning", "WarningContext", "ValidateMessagesContext", "LocaleProvider", "ANT_MARK", "LocaleContext", "defaultLocale", "defaultTheme", "DesignTokenContext", "defaultSeedToken", "ConfigConsumer", "ConfigContext", "defaultIconPrefixCls", "defaultPrefixCls", "Variants", "registerTheme", "DisabledContextProvider", "useConfig", "useTheme", "MotionWrapper", "Prop<PERSON><PERSON>ning", "SizeContext", "SizeContextProvider", "useStyle", "existThemeConfig", "warnContext", "process", "env", "NODE_ENV", "componentName", "configConsumerProps", "PASSED_PROPS", "globalPrefixCls", "globalIconPrefixCls", "globalTheme", "globalHolderRender", "getGlobalPrefixCls", "getGlobalIconPrefixCls", "isLegacyTheme", "theme", "keys", "some", "key", "endsWith", "setGlobalConfig", "props", "prefixCls", "iconPrefixCls", "<PERSON><PERSON><PERSON>", "undefined", "globalConfig", "getPrefixCls", "suffixCls", "customizePrefixCls", "getIconPrefixCls", "getRootPrefixCls", "getTheme", "Provide<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "csp", "customCsp", "autoInsertSpaceInButton", "alert", "anchor", "form", "locale", "componentSize", "direction", "space", "splitter", "virtual", "dropdownMatchSelectWidth", "popupMatchSelectWidth", "popupOverflow", "legacyLocale", "parentContext", "customIconPrefixCls", "componentDisabled", "segmented", "statistic", "spin", "calendar", "carousel", "cascader", "collapse", "typography", "checkbox", "descriptions", "divider", "drawer", "skeleton", "steps", "image", "layout", "list", "mentions", "modal", "progress", "result", "slider", "breadcrumb", "menu", "pagination", "input", "textArea", "empty", "badge", "radio", "rate", "switch", "SWITCH", "transfer", "avatar", "message", "tag", "table", "card", "tabs", "timeline", "timePicker", "upload", "notification", "tree", "colorPicker", "datePicker", "rangePicker", "flex", "wave", "dropdown", "warningConfig", "tour", "tooltip", "popover", "popconfirm", "floatButtonGroup", "variant", "inputNumber", "treeSelect", "useCallback", "mergedPrefixCls", "mergedTheme", "baseConfig", "warningFn", "config", "assign", "for<PERSON>ach", "propName", "propValue", "button", "autoInsertSpace", "memoedConfig", "prevConfig", "currentConfig", "prevKeys", "currentKeys", "layer", "useContext", "memoIconContextValue", "childNode", "createElement", "Fragment", "validateMessages", "_a", "_b", "_c", "_d", "Form", "defaultValidateMessages", "Provider", "value", "_ANT_MARK__", "size", "memoTheme", "algorithm", "token", "components", "cssVar", "rest", "themeObj", "Array", "isArray", "parsedComponents", "entries", "componentToken", "parsedToken", "mergedToken", "override", "disabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "context", "antLocale", "defineProperty", "get", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/config-provider/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { createTheme, StyleContext as CssInJsStyleContext } from '@ant-design/cssinjs';\nimport IconContext from \"@ant-design/icons/es/components/Context\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { merge } from \"rc-util/es/utils/set\";\nimport warning, { devUseWarning, WarningContext } from '../_util/warning';\nimport ValidateMessagesContext from '../form/validateMessagesContext';\nimport LocaleProvider, { ANT_MARK } from '../locale';\nimport LocaleContext from '../locale/context';\nimport defaultLocale from '../locale/en_US';\nimport { defaultTheme, DesignTokenContext } from '../theme/context';\nimport defaultSeedToken from '../theme/themes/seed';\nimport { ConfigConsumer, ConfigContext, defaultIconPrefixCls, defaultPrefixCls, Variants } from './context';\nimport { registerTheme } from './cssVariables';\nimport { DisabledContextProvider } from './DisabledContext';\nimport useConfig from './hooks/useConfig';\nimport useTheme from './hooks/useTheme';\nimport MotionWrapper from './MotionWrapper';\nimport PropWarning from './PropWarning';\nimport SizeContext, { SizeContextProvider } from './SizeContext';\nimport useStyle from './style';\nexport { Variants };\n/**\n * Since too many feedback using static method like `Modal.confirm` not getting theme, we record the\n * theme register info here to help developer get warning info.\n */\nlet existThemeConfig = false;\nexport const warnContext = process.env.NODE_ENV !== 'production' ? componentName => {\n  process.env.NODE_ENV !== \"production\" ? warning(!existThemeConfig, componentName, `Static function can not consume context like dynamic theme. Please use 'App' component instead.`) : void 0;\n} : /* istanbul ignore next */\nnull;\nexport { ConfigConsumer, ConfigContext, defaultPrefixCls, defaultIconPrefixCls };\nexport const configConsumerProps = ['getTargetContainer', 'getPopupContainer', 'rootPrefixCls', 'getPrefixCls', 'renderEmpty', 'csp', 'autoInsertSpaceInButton', 'locale'];\n// These props is used by `useContext` directly in sub component\nconst PASSED_PROPS = ['getTargetContainer', 'getPopupContainer', 'renderEmpty', 'input', 'pagination', 'form', 'select', 'button'];\nlet globalPrefixCls;\nlet globalIconPrefixCls;\nlet globalTheme;\nlet globalHolderRender;\nfunction getGlobalPrefixCls() {\n  return globalPrefixCls || defaultPrefixCls;\n}\nfunction getGlobalIconPrefixCls() {\n  return globalIconPrefixCls || defaultIconPrefixCls;\n}\nfunction isLegacyTheme(theme) {\n  return Object.keys(theme).some(key => key.endsWith('Color'));\n}\nconst setGlobalConfig = props => {\n  const {\n    prefixCls,\n    iconPrefixCls,\n    theme,\n    holderRender\n  } = props;\n  if (prefixCls !== undefined) {\n    globalPrefixCls = prefixCls;\n  }\n  if (iconPrefixCls !== undefined) {\n    globalIconPrefixCls = iconPrefixCls;\n  }\n  if ('holderRender' in props) {\n    globalHolderRender = holderRender;\n  }\n  if (theme) {\n    if (isLegacyTheme(theme)) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', '`config` of css variable theme is not work in v5. Please use new `theme` config instead.') : void 0;\n      registerTheme(getGlobalPrefixCls(), theme);\n    } else {\n      globalTheme = theme;\n    }\n  }\n};\nexport const globalConfig = () => ({\n  getPrefixCls: (suffixCls, customizePrefixCls) => {\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    return suffixCls ? `${getGlobalPrefixCls()}-${suffixCls}` : getGlobalPrefixCls();\n  },\n  getIconPrefixCls: getGlobalIconPrefixCls,\n  getRootPrefixCls: () => {\n    // If Global prefixCls provided, use this\n    if (globalPrefixCls) {\n      return globalPrefixCls;\n    }\n    // Fallback to default prefixCls\n    return getGlobalPrefixCls();\n  },\n  getTheme: () => globalTheme,\n  holderRender: globalHolderRender\n});\nconst ProviderChildren = props => {\n  const {\n    children,\n    csp: customCsp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    form,\n    locale,\n    componentSize,\n    direction,\n    space,\n    splitter,\n    virtual,\n    dropdownMatchSelectWidth,\n    popupMatchSelectWidth,\n    popupOverflow,\n    legacyLocale,\n    parentContext,\n    iconPrefixCls: customIconPrefixCls,\n    theme,\n    componentDisabled,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    input,\n    textArea,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  } = props;\n  // =================================== Context ===================================\n  const getPrefixCls = React.useCallback((suffixCls, customizePrefixCls) => {\n    const {\n      prefixCls\n    } = props;\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    const mergedPrefixCls = prefixCls || parentContext.getPrefixCls('');\n    return suffixCls ? `${mergedPrefixCls}-${suffixCls}` : mergedPrefixCls;\n  }, [parentContext.getPrefixCls, props.prefixCls]);\n  const iconPrefixCls = customIconPrefixCls || parentContext.iconPrefixCls || defaultIconPrefixCls;\n  const csp = customCsp || parentContext.csp;\n  useStyle(iconPrefixCls, csp);\n  const mergedTheme = useTheme(theme, parentContext.theme, {\n    prefixCls: getPrefixCls('')\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    existThemeConfig = existThemeConfig || !!mergedTheme;\n  }\n  const baseConfig = {\n    csp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    locale: locale || legacyLocale,\n    direction,\n    space,\n    splitter,\n    virtual,\n    popupMatchSelectWidth: popupMatchSelectWidth !== null && popupMatchSelectWidth !== void 0 ? popupMatchSelectWidth : dropdownMatchSelectWidth,\n    popupOverflow,\n    getPrefixCls,\n    iconPrefixCls,\n    theme: mergedTheme,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    input,\n    textArea,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    const warningFn = devUseWarning('ConfigProvider');\n    warningFn(!('autoInsertSpaceInButton' in props), 'deprecated', '`autoInsertSpaceInButton` is deprecated. Please use `{ button: { autoInsertSpace: boolean }}` instead.');\n  }\n  const config = Object.assign({}, parentContext);\n  Object.keys(baseConfig).forEach(key => {\n    if (baseConfig[key] !== undefined) {\n      config[key] = baseConfig[key];\n    }\n  });\n  // Pass the props used by `useContext` directly with child component.\n  // These props should merged into `config`.\n  PASSED_PROPS.forEach(propName => {\n    const propValue = props[propName];\n    if (propValue) {\n      config[propName] = propValue;\n    }\n  });\n  if (typeof autoInsertSpaceInButton !== 'undefined') {\n    // merge deprecated api\n    config.button = Object.assign({\n      autoInsertSpace: autoInsertSpaceInButton\n    }, config.button);\n  }\n  // https://github.com/ant-design/ant-design/issues/27617\n  const memoedConfig = useMemo(() => config, config, (prevConfig, currentConfig) => {\n    const prevKeys = Object.keys(prevConfig);\n    const currentKeys = Object.keys(currentConfig);\n    return prevKeys.length !== currentKeys.length || prevKeys.some(key => prevConfig[key] !== currentConfig[key]);\n  });\n  const {\n    layer\n  } = React.useContext(CssInJsStyleContext);\n  const memoIconContextValue = React.useMemo(() => ({\n    prefixCls: iconPrefixCls,\n    csp,\n    layer: layer ? 'antd' : undefined\n  }), [iconPrefixCls, csp, layer]);\n  let childNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PropWarning, {\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }), children);\n  const validateMessages = React.useMemo(() => {\n    var _a, _b, _c, _d;\n    return merge(((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.defaultValidateMessages) || {}, ((_c = (_b = memoedConfig.locale) === null || _b === void 0 ? void 0 : _b.Form) === null || _c === void 0 ? void 0 : _c.defaultValidateMessages) || {}, ((_d = memoedConfig.form) === null || _d === void 0 ? void 0 : _d.validateMessages) || {}, (form === null || form === void 0 ? void 0 : form.validateMessages) || {});\n  }, [memoedConfig, form === null || form === void 0 ? void 0 : form.validateMessages]);\n  if (Object.keys(validateMessages).length > 0) {\n    childNode = /*#__PURE__*/React.createElement(ValidateMessagesContext.Provider, {\n      value: validateMessages\n    }, childNode);\n  }\n  if (locale) {\n    childNode = /*#__PURE__*/React.createElement(LocaleProvider, {\n      locale: locale,\n      _ANT_MARK__: ANT_MARK\n    }, childNode);\n  }\n  if (iconPrefixCls || csp) {\n    childNode = /*#__PURE__*/React.createElement(IconContext.Provider, {\n      value: memoIconContextValue\n    }, childNode);\n  }\n  if (componentSize) {\n    childNode = /*#__PURE__*/React.createElement(SizeContextProvider, {\n      size: componentSize\n    }, childNode);\n  }\n  // =================================== Motion ===================================\n  childNode = /*#__PURE__*/React.createElement(MotionWrapper, null, childNode);\n  // ================================ Dynamic theme ================================\n  const memoTheme = React.useMemo(() => {\n    const _a = mergedTheme || {},\n      {\n        algorithm,\n        token,\n        components,\n        cssVar\n      } = _a,\n      rest = __rest(_a, [\"algorithm\", \"token\", \"components\", \"cssVar\"]);\n    const themeObj = algorithm && (!Array.isArray(algorithm) || algorithm.length > 0) ? createTheme(algorithm) : defaultTheme;\n    const parsedComponents = {};\n    Object.entries(components || {}).forEach(([componentName, componentToken]) => {\n      const parsedToken = Object.assign({}, componentToken);\n      if ('algorithm' in parsedToken) {\n        if (parsedToken.algorithm === true) {\n          parsedToken.theme = themeObj;\n        } else if (Array.isArray(parsedToken.algorithm) || typeof parsedToken.algorithm === 'function') {\n          parsedToken.theme = createTheme(parsedToken.algorithm);\n        }\n        delete parsedToken.algorithm;\n      }\n      parsedComponents[componentName] = parsedToken;\n    });\n    const mergedToken = Object.assign(Object.assign({}, defaultSeedToken), token);\n    return Object.assign(Object.assign({}, rest), {\n      theme: themeObj,\n      token: mergedToken,\n      components: parsedComponents,\n      override: Object.assign({\n        override: mergedToken\n      }, parsedComponents),\n      cssVar: cssVar\n    });\n  }, [mergedTheme]);\n  if (theme) {\n    childNode = /*#__PURE__*/React.createElement(DesignTokenContext.Provider, {\n      value: memoTheme\n    }, childNode);\n  }\n  // ================================== Warning ===================================\n  if (memoedConfig.warning) {\n    childNode = /*#__PURE__*/React.createElement(WarningContext.Provider, {\n      value: memoedConfig.warning\n    }, childNode);\n  }\n  // =================================== Render ===================================\n  if (componentDisabled !== undefined) {\n    childNode = /*#__PURE__*/React.createElement(DisabledContextProvider, {\n      disabled: componentDisabled\n    }, childNode);\n  }\n  return /*#__PURE__*/React.createElement(ConfigContext.Provider, {\n    value: memoedConfig\n  }, childNode);\n};\nconst ConfigProvider = props => {\n  const context = React.useContext(ConfigContext);\n  const antLocale = React.useContext(LocaleContext);\n  return /*#__PURE__*/React.createElement(ProviderChildren, Object.assign({\n    parentContext: context,\n    legacyLocale: antLocale\n  }, props));\n};\nConfigProvider.ConfigContext = ConfigContext;\nConfigProvider.SizeContext = SizeContext;\nConfigProvider.config = setGlobalConfig;\nConfigProvider.useConfig = useConfig;\nObject.defineProperty(ConfigProvider, 'SizeContext', {\n  get: () => {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'ConfigProvider.SizeContext is deprecated. Please use `ConfigProvider.useConfig().componentSize` instead.') : void 0;\n    return SizeContext;\n  }\n});\nif (process.env.NODE_ENV !== 'production') {\n  ConfigProvider.displayName = 'ConfigProvider';\n}\nexport default ConfigProvider;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,YAAY,IAAIC,mBAAmB,QAAQ,qBAAqB;AACtF,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,OAAOC,OAAO,IAAIC,aAAa,EAAEC,cAAc,QAAQ,kBAAkB;AACzE,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,cAAc,IAAIC,QAAQ,QAAQ,WAAW;AACpD,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAkB;AACnE,OAAOC,gBAAgB,MAAM,sBAAsB;AACnD,SAASC,cAAc,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,WAAW;AAC3G,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,IAAIC,mBAAmB,QAAQ,eAAe;AAChE,OAAOC,QAAQ,MAAM,SAAS;AAC9B,SAAST,QAAQ;AACjB;AACA;AACA;AACA;AACA,IAAIU,gBAAgB,GAAG,KAAK;AAC5B,OAAO,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,aAAa,IAAI;EAClFH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,OAAO,CAAC,CAACyB,gBAAgB,EAAEK,aAAa,EAAE,iGAAiG,CAAC,GAAG,KAAK,CAAC;AAC/L,CAAC,GAAG;AACJ,IAAI;AACJ,SAASnB,cAAc,EAAEC,aAAa,EAAEE,gBAAgB,EAAED,oBAAoB;AAC9E,OAAO,MAAMkB,mBAAmB,GAAG,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,KAAK,EAAE,yBAAyB,EAAE,QAAQ,CAAC;AAC1K;AACA,MAAMC,YAAY,GAAG,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAClI,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,WAAW;AACf,IAAIC,kBAAkB;AACtB,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,OAAOJ,eAAe,IAAInB,gBAAgB;AAC5C;AACA,SAASwB,sBAAsBA,CAAA,EAAG;EAChC,OAAOJ,mBAAmB,IAAIrB,oBAAoB;AACpD;AACA,SAAS0B,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOxD,MAAM,CAACyD,IAAI,CAACD,KAAK,CAAC,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC9D;AACA,MAAMC,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,SAAS;IACTC,aAAa;IACbR,KAAK;IACLS;EACF,CAAC,GAAGH,KAAK;EACT,IAAIC,SAAS,KAAKG,SAAS,EAAE;IAC3BjB,eAAe,GAAGc,SAAS;EAC7B;EACA,IAAIC,aAAa,KAAKE,SAAS,EAAE;IAC/BhB,mBAAmB,GAAGc,aAAa;EACrC;EACA,IAAI,cAAc,IAAIF,KAAK,EAAE;IAC3BV,kBAAkB,GAAGa,YAAY;EACnC;EACA,IAAIT,KAAK,EAAE;IACT,IAAID,aAAa,CAACC,KAAK,CAAC,EAAE;MACxBb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,0FAA0F,CAAC,GAAG,KAAK,CAAC;MAC7KgB,aAAa,CAACqB,kBAAkB,CAAC,CAAC,EAAEG,KAAK,CAAC;IAC5C,CAAC,MAAM;MACLL,WAAW,GAAGK,KAAK;IACrB;EACF;AACF,CAAC;AACD,OAAO,MAAMW,YAAY,GAAGA,CAAA,MAAO;EACjCC,YAAY,EAAEA,CAACC,SAAS,EAAEC,kBAAkB,KAAK;IAC/C,IAAIA,kBAAkB,EAAE;MACtB,OAAOA,kBAAkB;IAC3B;IACA,OAAOD,SAAS,GAAG,GAAGhB,kBAAkB,CAAC,CAAC,IAAIgB,SAAS,EAAE,GAAGhB,kBAAkB,CAAC,CAAC;EAClF,CAAC;EACDkB,gBAAgB,EAAEjB,sBAAsB;EACxCkB,gBAAgB,EAAEA,CAAA,KAAM;IACtB;IACA,IAAIvB,eAAe,EAAE;MACnB,OAAOA,eAAe;IACxB;IACA;IACA,OAAOI,kBAAkB,CAAC,CAAC;EAC7B,CAAC;EACDoB,QAAQ,EAAEA,CAAA,KAAMtB,WAAW;EAC3Bc,YAAY,EAAEb;AAChB,CAAC,CAAC;AACF,MAAMsB,gBAAgB,GAAGZ,KAAK,IAAI;EAChC,MAAM;IACJa,QAAQ;IACRC,GAAG,EAAEC,SAAS;IACdC,uBAAuB;IACvBC,KAAK;IACLC,MAAM;IACNC,IAAI;IACJC,MAAM;IACNC,aAAa;IACbC,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC,OAAO;IACPC,wBAAwB;IACxBC,qBAAqB;IACrBC,aAAa;IACbC,YAAY;IACZC,aAAa;IACb5B,aAAa,EAAE6B,mBAAmB;IAClCrC,KAAK;IACLsC,iBAAiB;IACjBC,SAAS;IACTC,SAAS;IACTC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,QAAQ;IACRC,YAAY;IACZC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACRC,KAAK;IACLC,KAAK;IACLC,MAAM;IACNC,IAAI;IACJC,QAAQ;IACRC,KAAK;IACLC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC,UAAU;IACVC,IAAI;IACJC,UAAU;IACVC,KAAK;IACLC,QAAQ;IACRC,KAAK;IACLC,KAAK;IACLC,KAAK;IACLC,IAAI;IACJC,MAAM,EAAEC,MAAM;IACdC,QAAQ;IACRC,MAAM;IACNC,OAAO;IACPC,GAAG;IACHC,KAAK;IACLC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,UAAU;IACVC,MAAM;IACNC,YAAY;IACZC,IAAI;IACJC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRjI,OAAO,EAAEkI,aAAa;IACtBC,IAAI;IACJC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,gBAAgB;IAChBC,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAG5F,KAAK;EACT;EACA,MAAMM,YAAY,GAAG3D,KAAK,CAACkJ,WAAW,CAAC,CAACtF,SAAS,EAAEC,kBAAkB,KAAK;IACxE,MAAM;MACJP;IACF,CAAC,GAAGD,KAAK;IACT,IAAIQ,kBAAkB,EAAE;MACtB,OAAOA,kBAAkB;IAC3B;IACA,MAAMsF,eAAe,GAAG7F,SAAS,IAAI6B,aAAa,CAACxB,YAAY,CAAC,EAAE,CAAC;IACnE,OAAOC,SAAS,GAAG,GAAGuF,eAAe,IAAIvF,SAAS,EAAE,GAAGuF,eAAe;EACxE,CAAC,EAAE,CAAChE,aAAa,CAACxB,YAAY,EAAEN,KAAK,CAACC,SAAS,CAAC,CAAC;EACjD,MAAMC,aAAa,GAAG6B,mBAAmB,IAAID,aAAa,CAAC5B,aAAa,IAAInC,oBAAoB;EAChG,MAAM+C,GAAG,GAAGC,SAAS,IAAIe,aAAa,CAAChB,GAAG;EAC1CpC,QAAQ,CAACwB,aAAa,EAAEY,GAAG,CAAC;EAC5B,MAAMiF,WAAW,GAAG1H,QAAQ,CAACqB,KAAK,EAAEoC,aAAa,CAACpC,KAAK,EAAE;IACvDO,SAAS,EAAEK,YAAY,CAAC,EAAE;EAC5B,CAAC,CAAC;EACF,IAAIzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCJ,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC,CAACoH,WAAW;EACtD;EACA,MAAMC,UAAU,GAAG;IACjBlF,GAAG;IACHE,uBAAuB;IACvBC,KAAK;IACLC,MAAM;IACNE,MAAM,EAAEA,MAAM,IAAIS,YAAY;IAC9BP,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC,OAAO;IACPE,qBAAqB,EAAEA,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGD,wBAAwB;IAC5IE,aAAa;IACbtB,YAAY;IACZJ,aAAa;IACbR,KAAK,EAAEqG,WAAW;IAClB9D,SAAS;IACTC,SAAS;IACTC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,QAAQ;IACRC,YAAY;IACZC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACRC,KAAK;IACLC,KAAK;IACLW,KAAK;IACLC,QAAQ;IACRX,MAAM;IACNC,IAAI;IACJC,QAAQ;IACRC,KAAK;IACLC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC,UAAU;IACVC,IAAI;IACJC,UAAU;IACVG,KAAK;IACLC,KAAK;IACLC,KAAK;IACLC,IAAI;IACJC,MAAM,EAAEC,MAAM;IACdC,QAAQ;IACRC,MAAM;IACNC,OAAO;IACPC,GAAG;IACHC,KAAK;IACLC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,UAAU;IACVC,MAAM;IACNC,YAAY;IACZC,IAAI;IACJC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRjI,OAAO,EAAEkI,aAAa;IACtBC,IAAI;IACJC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,gBAAgB;IAChBC,OAAO;IACPC,WAAW;IACXC;EACF,CAAC;EACD,IAAI/G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMkH,SAAS,GAAG9I,aAAa,CAAC,gBAAgB,CAAC;IACjD8I,SAAS,CAAC,EAAE,yBAAyB,IAAIjG,KAAK,CAAC,EAAE,YAAY,EAAE,wGAAwG,CAAC;EAC1K;EACA,MAAMkG,MAAM,GAAGhK,MAAM,CAACiK,MAAM,CAAC,CAAC,CAAC,EAAErE,aAAa,CAAC;EAC/C5F,MAAM,CAACyD,IAAI,CAACqG,UAAU,CAAC,CAACI,OAAO,CAACvG,GAAG,IAAI;IACrC,IAAImG,UAAU,CAACnG,GAAG,CAAC,KAAKO,SAAS,EAAE;MACjC8F,MAAM,CAACrG,GAAG,CAAC,GAAGmG,UAAU,CAACnG,GAAG,CAAC;IAC/B;EACF,CAAC,CAAC;EACF;EACA;EACAX,YAAY,CAACkH,OAAO,CAACC,QAAQ,IAAI;IAC/B,MAAMC,SAAS,GAAGtG,KAAK,CAACqG,QAAQ,CAAC;IACjC,IAAIC,SAAS,EAAE;MACbJ,MAAM,CAACG,QAAQ,CAAC,GAAGC,SAAS;IAC9B;EACF,CAAC,CAAC;EACF,IAAI,OAAOtF,uBAAuB,KAAK,WAAW,EAAE;IAClD;IACAkF,MAAM,CAACK,MAAM,GAAGrK,MAAM,CAACiK,MAAM,CAAC;MAC5BK,eAAe,EAAExF;IACnB,CAAC,EAAEkF,MAAM,CAACK,MAAM,CAAC;EACnB;EACA;EACA,MAAME,YAAY,GAAGzJ,OAAO,CAAC,MAAMkJ,MAAM,EAAEA,MAAM,EAAE,CAACQ,UAAU,EAAEC,aAAa,KAAK;IAChF,MAAMC,QAAQ,GAAG1K,MAAM,CAACyD,IAAI,CAAC+G,UAAU,CAAC;IACxC,MAAMG,WAAW,GAAG3K,MAAM,CAACyD,IAAI,CAACgH,aAAa,CAAC;IAC9C,OAAOC,QAAQ,CAACnK,MAAM,KAAKoK,WAAW,CAACpK,MAAM,IAAImK,QAAQ,CAAChH,IAAI,CAACC,GAAG,IAAI6G,UAAU,CAAC7G,GAAG,CAAC,KAAK8G,aAAa,CAAC9G,GAAG,CAAC,CAAC;EAC/G,CAAC,CAAC;EACF,MAAM;IACJiH;EACF,CAAC,GAAGnK,KAAK,CAACoK,UAAU,CAACjK,mBAAmB,CAAC;EACzC,MAAMkK,oBAAoB,GAAGrK,KAAK,CAACK,OAAO,CAAC,OAAO;IAChDiD,SAAS,EAAEC,aAAa;IACxBY,GAAG;IACHgG,KAAK,EAAEA,KAAK,GAAG,MAAM,GAAG1G;EAC1B,CAAC,CAAC,EAAE,CAACF,aAAa,EAAEY,GAAG,EAAEgG,KAAK,CAAC,CAAC;EAChC,IAAIG,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAACvK,KAAK,CAACwK,QAAQ,EAAE,IAAI,EAAE,aAAaxK,KAAK,CAACuK,aAAa,CAAC3I,WAAW,EAAE;IACnHmD,wBAAwB,EAAEA;EAC5B,CAAC,CAAC,EAAEb,QAAQ,CAAC;EACb,MAAMuG,gBAAgB,GAAGzK,KAAK,CAACK,OAAO,CAAC,MAAM;IAC3C,IAAIqK,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClB,OAAOvK,KAAK,CAAC,CAAC,CAACoK,EAAE,GAAG5J,aAAa,CAACgK,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,uBAAuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGb,YAAY,CAACrF,MAAM,MAAM,IAAI,IAAIkG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,uBAAuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAACF,EAAE,GAAGf,YAAY,CAACtF,IAAI,MAAM,IAAI,IAAIqG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACJ,gBAAgB,KAAK,CAAC,CAAC,EAAE,CAACjG,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACiG,gBAAgB,KAAK,CAAC,CAAC,CAAC;EAC/a,CAAC,EAAE,CAACX,YAAY,EAAEtF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACiG,gBAAgB,CAAC,CAAC;EACrF,IAAIlL,MAAM,CAACyD,IAAI,CAACyH,gBAAgB,CAAC,CAAC3K,MAAM,GAAG,CAAC,EAAE;IAC5CwK,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAAC7J,uBAAuB,CAACsK,QAAQ,EAAE;MAC7EC,KAAK,EAAER;IACT,CAAC,EAAEH,SAAS,CAAC;EACf;EACA,IAAI7F,MAAM,EAAE;IACV6F,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAAC5J,cAAc,EAAE;MAC3D8D,MAAM,EAAEA,MAAM;MACdyG,WAAW,EAAEtK;IACf,CAAC,EAAE0J,SAAS,CAAC;EACf;EACA,IAAI/G,aAAa,IAAIY,GAAG,EAAE;IACxBmG,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAACnK,WAAW,CAAC4K,QAAQ,EAAE;MACjEC,KAAK,EAAEZ;IACT,CAAC,EAAEC,SAAS,CAAC;EACf;EACA,IAAI5F,aAAa,EAAE;IACjB4F,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAACzI,mBAAmB,EAAE;MAChEqJ,IAAI,EAAEzG;IACR,CAAC,EAAE4F,SAAS,CAAC;EACf;EACA;EACAA,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAAC5I,aAAa,EAAE,IAAI,EAAE2I,SAAS,CAAC;EAC5E;EACA,MAAMc,SAAS,GAAGpL,KAAK,CAACK,OAAO,CAAC,MAAM;IACpC,MAAMqK,EAAE,GAAGtB,WAAW,IAAI,CAAC,CAAC;MAC1B;QACEiC,SAAS;QACTC,KAAK;QACLC,UAAU;QACVC;MACF,CAAC,GAAGd,EAAE;MACNe,IAAI,GAAGvM,MAAM,CAACwL,EAAE,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IACnE,MAAMgB,QAAQ,GAAGL,SAAS,KAAK,CAACM,KAAK,CAACC,OAAO,CAACP,SAAS,CAAC,IAAIA,SAAS,CAACvL,MAAM,GAAG,CAAC,CAAC,GAAGG,WAAW,CAACoL,SAAS,CAAC,GAAGtK,YAAY;IACzH,MAAM8K,gBAAgB,GAAG,CAAC,CAAC;IAC3BtM,MAAM,CAACuM,OAAO,CAACP,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC9B,OAAO,CAAC,CAAC,CAACpH,aAAa,EAAE0J,cAAc,CAAC,KAAK;MAC5E,MAAMC,WAAW,GAAGzM,MAAM,CAACiK,MAAM,CAAC,CAAC,CAAC,EAAEuC,cAAc,CAAC;MACrD,IAAI,WAAW,IAAIC,WAAW,EAAE;QAC9B,IAAIA,WAAW,CAACX,SAAS,KAAK,IAAI,EAAE;UAClCW,WAAW,CAACjJ,KAAK,GAAG2I,QAAQ;QAC9B,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACI,WAAW,CAACX,SAAS,CAAC,IAAI,OAAOW,WAAW,CAACX,SAAS,KAAK,UAAU,EAAE;UAC9FW,WAAW,CAACjJ,KAAK,GAAG9C,WAAW,CAAC+L,WAAW,CAACX,SAAS,CAAC;QACxD;QACA,OAAOW,WAAW,CAACX,SAAS;MAC9B;MACAQ,gBAAgB,CAACxJ,aAAa,CAAC,GAAG2J,WAAW;IAC/C,CAAC,CAAC;IACF,MAAMC,WAAW,GAAG1M,MAAM,CAACiK,MAAM,CAACjK,MAAM,CAACiK,MAAM,CAAC,CAAC,CAAC,EAAEvI,gBAAgB,CAAC,EAAEqK,KAAK,CAAC;IAC7E,OAAO/L,MAAM,CAACiK,MAAM,CAACjK,MAAM,CAACiK,MAAM,CAAC,CAAC,CAAC,EAAEiC,IAAI,CAAC,EAAE;MAC5C1I,KAAK,EAAE2I,QAAQ;MACfJ,KAAK,EAAEW,WAAW;MAClBV,UAAU,EAAEM,gBAAgB;MAC5BK,QAAQ,EAAE3M,MAAM,CAACiK,MAAM,CAAC;QACtB0C,QAAQ,EAAED;MACZ,CAAC,EAAEJ,gBAAgB,CAAC;MACpBL,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpC,WAAW,CAAC,CAAC;EACjB,IAAIrG,KAAK,EAAE;IACTuH,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAACvJ,kBAAkB,CAACgK,QAAQ,EAAE;MACxEC,KAAK,EAAEG;IACT,CAAC,EAAEd,SAAS,CAAC;EACf;EACA;EACA,IAAIR,YAAY,CAACvJ,OAAO,EAAE;IACxB+J,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAAC9J,cAAc,CAACuK,QAAQ,EAAE;MACpEC,KAAK,EAAEnB,YAAY,CAACvJ;IACtB,CAAC,EAAE+J,SAAS,CAAC;EACf;EACA;EACA,IAAIjF,iBAAiB,KAAK5B,SAAS,EAAE;IACnC6G,SAAS,GAAG,aAAatK,KAAK,CAACuK,aAAa,CAAC/I,uBAAuB,EAAE;MACpE2K,QAAQ,EAAE9G;IACZ,CAAC,EAAEiF,SAAS,CAAC;EACf;EACA,OAAO,aAAatK,KAAK,CAACuK,aAAa,CAACpJ,aAAa,CAAC6J,QAAQ,EAAE;IAC9DC,KAAK,EAAEnB;EACT,CAAC,EAAEQ,SAAS,CAAC;AACf,CAAC;AACD,MAAM8B,cAAc,GAAG/I,KAAK,IAAI;EAC9B,MAAMgJ,OAAO,GAAGrM,KAAK,CAACoK,UAAU,CAACjJ,aAAa,CAAC;EAC/C,MAAMmL,SAAS,GAAGtM,KAAK,CAACoK,UAAU,CAACvJ,aAAa,CAAC;EACjD,OAAO,aAAab,KAAK,CAACuK,aAAa,CAACtG,gBAAgB,EAAE1E,MAAM,CAACiK,MAAM,CAAC;IACtErE,aAAa,EAAEkH,OAAO;IACtBnH,YAAY,EAAEoH;EAChB,CAAC,EAAEjJ,KAAK,CAAC,CAAC;AACZ,CAAC;AACD+I,cAAc,CAACjL,aAAa,GAAGA,aAAa;AAC5CiL,cAAc,CAACvK,WAAW,GAAGA,WAAW;AACxCuK,cAAc,CAAC7C,MAAM,GAAGnG,eAAe;AACvCgJ,cAAc,CAAC3K,SAAS,GAAGA,SAAS;AACpClC,MAAM,CAACgN,cAAc,CAACH,cAAc,EAAE,aAAa,EAAE;EACnDI,GAAG,EAAEA,CAAA,KAAM;IACTtK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,0GAA0G,CAAC,GAAG,KAAK,CAAC;IAC7L,OAAOsB,WAAW;EACpB;AACF,CAAC,CAAC;AACF,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCgK,cAAc,CAACK,WAAW,GAAG,gBAAgB;AAC/C;AACA,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}