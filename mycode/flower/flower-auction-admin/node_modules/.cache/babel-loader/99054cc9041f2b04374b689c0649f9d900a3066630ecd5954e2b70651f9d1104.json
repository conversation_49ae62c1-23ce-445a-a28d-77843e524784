{"ast": null, "code": "import * as React from 'react';\nimport { VariantContext } from '../context';\nimport { ConfigContext, Variants } from '../../config-provider';\n/**\n * Compatible for legacy `bordered` prop.\n */\nconst useVariant = (component, variant, legacyBordered = undefined) => {\n  var _a, _b;\n  const {\n    variant: configVariant,\n    [component]: componentConfig\n  } = React.useContext(ConfigContext);\n  const ctxVariant = React.useContext(VariantContext);\n  const configComponentVariant = componentConfig === null || componentConfig === void 0 ? void 0 : componentConfig.variant;\n  let mergedVariant;\n  if (typeof variant !== 'undefined') {\n    mergedVariant = variant;\n  } else if (legacyBordered === false) {\n    mergedVariant = 'borderless';\n  } else {\n    // form variant > component global variant > global variant\n    mergedVariant = (_b = (_a = ctxVariant !== null && ctxVariant !== void 0 ? ctxVariant : configComponentVariant) !== null && _a !== void 0 ? _a : configVariant) !== null && _b !== void 0 ? _b : 'outlined';\n  }\n  const enableVariantCls = Variants.includes(mergedVariant);\n  return [mergedVariant, enableVariantCls];\n};\nexport default useVariant;", "map": {"version": 3, "names": ["React", "VariantContext", "ConfigContext", "Variants", "useVariant", "component", "variant", "legacyBordered", "undefined", "_a", "_b", "config<PERSON><PERSON><PERSON>", "componentConfig", "useContext", "ctxVariant", "configComponent<PERSON><PERSON><PERSON>", "mergedVariant", "enableVariantCls", "includes"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/form/hooks/useVariants.js"], "sourcesContent": ["import * as React from 'react';\nimport { VariantContext } from '../context';\nimport { ConfigContext, Variants } from '../../config-provider';\n/**\n * Compatible for legacy `bordered` prop.\n */\nconst useVariant = (component, variant, legacyBordered = undefined) => {\n  var _a, _b;\n  const {\n    variant: configVariant,\n    [component]: componentConfig\n  } = React.useContext(ConfigContext);\n  const ctxVariant = React.useContext(VariantContext);\n  const configComponentVariant = componentConfig === null || componentConfig === void 0 ? void 0 : componentConfig.variant;\n  let mergedVariant;\n  if (typeof variant !== 'undefined') {\n    mergedVariant = variant;\n  } else if (legacyBordered === false) {\n    mergedVariant = 'borderless';\n  } else {\n    // form variant > component global variant > global variant\n    mergedVariant = (_b = (_a = ctxVariant !== null && ctxVariant !== void 0 ? ctxVariant : configComponentVariant) !== null && _a !== void 0 ? _a : configVariant) !== null && _b !== void 0 ? _b : 'outlined';\n  }\n  const enableVariantCls = Variants.includes(mergedVariant);\n  return [mergedVariant, enableVariantCls];\n};\nexport default useVariant;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,aAAa,EAAEC,QAAQ,QAAQ,uBAAuB;AAC/D;AACA;AACA;AACA,MAAMC,UAAU,GAAGA,CAACC,SAAS,EAAEC,OAAO,EAAEC,cAAc,GAAGC,SAAS,KAAK;EACrE,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IACJJ,OAAO,EAAEK,aAAa;IACtB,CAACN,SAAS,GAAGO;EACf,CAAC,GAAGZ,KAAK,CAACa,UAAU,CAACX,aAAa,CAAC;EACnC,MAAMY,UAAU,GAAGd,KAAK,CAACa,UAAU,CAACZ,cAAc,CAAC;EACnD,MAAMc,sBAAsB,GAAGH,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACN,OAAO;EACxH,IAAIU,aAAa;EACjB,IAAI,OAAOV,OAAO,KAAK,WAAW,EAAE;IAClCU,aAAa,GAAGV,OAAO;EACzB,CAAC,MAAM,IAAIC,cAAc,KAAK,KAAK,EAAE;IACnCS,aAAa,GAAG,YAAY;EAC9B,CAAC,MAAM;IACL;IACAA,aAAa,GAAG,CAACN,EAAE,GAAG,CAACD,EAAE,GAAGK,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGC,sBAAsB,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGE,aAAa,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,UAAU;EAC7M;EACA,MAAMO,gBAAgB,GAAGd,QAAQ,CAACe,QAAQ,CAACF,aAAa,CAAC;EACzD,OAAO,CAACA,aAAa,EAAEC,gBAAgB,CAAC;AAC1C,CAAC;AACD,eAAeb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}