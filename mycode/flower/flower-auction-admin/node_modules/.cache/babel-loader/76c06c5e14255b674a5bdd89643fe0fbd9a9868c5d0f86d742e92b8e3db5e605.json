{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/ProductList/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Popconfirm, Typography, Row, Col, Upload, Image, Descriptions, Switch } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExportOutlined, ReloadOutlined, UploadOutlined } from '@ant-design/icons';\nimport { productService } from '../../../services/productService';\nimport { uploadService } from '../../../services/uploadService';\n// import { handleError, handleSuccess } from '../../../utils/errorHandler';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\n\n// 商品质量等级枚举\nexport let QualityLevel = /*#__PURE__*/function (QualityLevel) {\n  QualityLevel[QualityLevel[\"EXCELLENT\"] = 1] = \"EXCELLENT\";\n  // 优\n  QualityLevel[QualityLevel[\"GOOD\"] = 2] = \"GOOD\";\n  // 良\n  QualityLevel[QualityLevel[\"MEDIUM\"] = 3] = \"MEDIUM\"; // 中\n  return QualityLevel;\n}({});\n\n// 商品状态枚举\nexport let ProductStatus = /*#__PURE__*/function (ProductStatus) {\n  ProductStatus[ProductStatus[\"OFFLINE\"] = 0] = \"OFFLINE\";\n  // 下架\n  ProductStatus[ProductStatus[\"ONLINE\"] = 1] = \"ONLINE\"; // 上架\n  return ProductStatus;\n}({});\n\n// 商品数据接口\n\n// 商品类别接口\n\n// 查询参数接口\n\nconst ProductList = () => {\n  _s();\n  var _viewingProduct$categ, _qualityLevelMap$view, _qualityLevelMap$view2;\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isDetailVisible, setIsDetailVisible] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [viewingProduct, setViewingProduct] = useState(null);\n  const [fileList, setFileList] = useState([]);\n  const [saving, setSaving] = useState(false);\n  const [formError, setFormError] = useState('');\n  const [formSuccess, setFormSuccess] = useState('');\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 质量等级映射\n  const qualityLevelMap = {\n    [QualityLevel.EXCELLENT]: {\n      label: '优',\n      color: 'green'\n    },\n    [QualityLevel.GOOD]: {\n      label: '良',\n      color: 'blue'\n    },\n    [QualityLevel.MEDIUM]: {\n      label: '中',\n      color: 'orange'\n    }\n  };\n\n  // 获取商品列表\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await productService.getProductList(queryParams);\n      if (response.success) {\n        setProducts(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取商品列表失败');\n        setProducts([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      let errorMsg = '获取商品列表失败';\n      if (error.response) {\n        const {\n          status\n        } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问商品列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setProducts([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品类别\n  const fetchCategories = async () => {\n    try {\n      const response = await productService.getCategoryList();\n      if (response.success) {\n        setCategories(response.data);\n      } else {\n        console.error('获取商品类别失败:', response.message);\n        message.warning('获取商品分类失败，部分功能可能受限');\n        setCategories([]);\n      }\n    } catch (error) {\n      console.error('获取商品类别失败:', error);\n      message.error('获取商品分类失败，请刷新页面重试');\n      setCategories([]);\n    }\n  };\n\n  // 获取供应商列表\n  const fetchSuppliers = async () => {\n    try {\n      // 获取所有用户，然后筛选出供应商类型的用户\n      const response = await fetch('/api/v1/users?page=1&pageSize=100');\n      const data = await response.json();\n      if (data.success && data.list) {\n        // 将用户转换为供应商格式\n        const supplierList = data.list.map(user => ({\n          id: user.id,\n          name: user.realName || user.username\n        }));\n        setSuppliers(supplierList);\n      }\n    } catch (error) {\n      console.error('获取供应商列表失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n    fetchSuppliers();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    // 过滤掉空值\n    const filteredValues = Object.keys(values).reduce((acc, key) => {\n      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {\n        acc[key] = values[key];\n      }\n      return acc;\n    }, {});\n    setQueryParams({\n      ...queryParams,\n      ...filteredValues,\n      page: 1\n    });\n    message.info(`正在搜索商品...`);\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    const resetParams = {\n      page: 1,\n      pageSize: queryParams.pageSize // 保持当前页面大小\n    };\n    setQueryParams(resetParams);\n    message.success('搜索条件已重置');\n  };\n\n  // 快速搜索（输入框回车）\n  const handleQuickSearch = value => {\n    if (value.trim()) {\n      searchForm.setFieldsValue({\n        name: value.trim()\n      });\n      handleSearch({\n        name: value.trim()\n      });\n    }\n  };\n\n  // 新增商品\n  const handleAdd = () => {\n    setEditingProduct(null);\n    form.resetFields();\n    setFileList([]);\n    setFormError('');\n    setFormSuccess('');\n    setIsModalVisible(true);\n  };\n\n  // 编辑商品\n  const handleEdit = product => {\n    setEditingProduct(product);\n    form.setFieldsValue({\n      ...product,\n      status: product.status === ProductStatus.ONLINE\n    });\n\n    // 设置图片列表\n    if (product.images && product.images.length > 0) {\n      const imageFiles = product.images.map((url, index) => ({\n        uid: `${index}`,\n        name: `image-${index}`,\n        status: 'done',\n        url: url\n      }));\n      setFileList(imageFiles);\n    } else {\n      setFileList([]);\n    }\n    setFormError('');\n    setFormSuccess('');\n    setIsModalVisible(true);\n  };\n\n  // 查看商品详情\n  const handleView = product => {\n    setViewingProduct(product);\n    setIsDetailVisible(true);\n  };\n\n  // 删除商品\n  const handleDelete = async id => {\n    try {\n      const response = await productService.deleteProduct(id);\n      if (response.success) {\n        message.success({\n          content: '商品删除成功！',\n          duration: 3\n        });\n        // 如果当前页没有数据了，回到上一页\n        if (products.length === 1 && queryParams.page > 1) {\n          setQueryParams({\n            ...queryParams,\n            page: queryParams.page - 1\n          });\n        } else {\n          fetchProducts();\n        }\n      } else {\n        message.error({\n          content: response.message || '删除失败，请稍后重试',\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('删除商品失败:', error);\n      message.error({\n        content: '删除失败，请检查网络连接后重试',\n        duration: 5\n      });\n    }\n  };\n\n  // 保存商品\n  const handleSave = async values => {\n    setSaving(true);\n    setFormError('');\n    setFormSuccess('');\n    try {\n      let imageUrls = [];\n\n      // 处理图片上传\n      const newFiles = fileList.filter(file => file.originFileObj && !file.url);\n      const existingUrls = fileList.filter(file => file.url).map(file => file.url);\n      if (newFiles.length > 0) {\n        // 上传新图片\n        const files = newFiles.map(file => file.originFileObj);\n        const uploadResponse = await uploadService.uploadImages(files);\n        if (uploadResponse.success && uploadResponse.data) {\n          // 如果返回的是单个URL，转换为数组\n          if (typeof uploadResponse.data === 'string') {\n            imageUrls = [uploadResponse.data];\n          } else if (Array.isArray(uploadResponse.data)) {\n            imageUrls = uploadResponse.data.map(item => item.url || item);\n          } else if (uploadResponse.data.url) {\n            imageUrls = [uploadResponse.data.url];\n          }\n        } else {\n          setFormError(uploadResponse.message || '图片上传失败');\n          return;\n        }\n      }\n\n      // 合并已有图片和新上传的图片\n      const allImageUrls = [...existingUrls, ...imageUrls];\n      const productData = {\n        ...values,\n        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,\n        images: allImageUrls\n      };\n      let response;\n      if (editingProduct) {\n        response = await productService.updateProduct(editingProduct.id, productData);\n      } else {\n        response = await productService.createProduct(productData);\n      }\n      if (response.success) {\n        const successMsg = editingProduct ? '商品信息更新成功！' : '商品创建成功！';\n        setFormSuccess(successMsg);\n\n        // 延迟关闭模态框，让用户看到成功消息\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setFileList([]);\n          setEditingProduct(null);\n          setFormSuccess('');\n          fetchProducts();\n        }, 1500);\n      } else {\n        const errorMsg = response.message || '操作失败，请稍后重试';\n        setFormError(errorMsg);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('保存商品失败:', error);\n      const errorMsg = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || '网络错误，请检查连接后重试';\n      setFormError(errorMsg);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 切换商品状态\n  const handleToggleStatus = async product => {\n    const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;\n    const statusText = newStatus === ProductStatus.ONLINE ? '上架' : '下架';\n    try {\n      const response = await productService.updateProductStatus(product.id, newStatus);\n      if (response.success) {\n        message.success({\n          content: `商品\"${product.name}\"已成功${statusText}！`,\n          duration: 3\n        });\n        fetchProducts();\n      } else {\n        message.error({\n          content: response.message || `${statusText}失败，请稍后重试`,\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('更新商品状态失败:', error);\n      message.error({\n        content: `${statusText}失败，请检查网络连接后重试`,\n        duration: 5\n      });\n    }\n  };\n\n  // 图片上传处理\n  const handleUploadChange = ({\n    fileList: newFileList\n  }) => {\n    setFileList(newFileList);\n  };\n\n  // 图片上传前的处理\n  const beforeUpload = file => {\n    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';\n    if (!isJpgOrPng) {\n      message.error('只能上传 JPG/PNG 格式的图片!');\n      return false;\n    }\n    const isLt5M = file.size / 1024 / 1024 < 5;\n    if (!isLt5M) {\n      message.error('图片大小不能超过 5MB!');\n      return false;\n    }\n    return false; // 阻止自动上传，手动处理\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '商品图片',\n    dataIndex: 'images',\n    key: 'images',\n    width: 100,\n    render: images => images && images.length > 0 ? /*#__PURE__*/_jsxDEV(Image, {\n      width: 60,\n      height: 60,\n      src: images[0],\n      style: {\n        objectFit: 'cover',\n        borderRadius: 4\n      },\n      fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: 60,\n        height: 60,\n        background: '#f5f5f5',\n        borderRadius: 4,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: \"\\u65E0\\u56FE\\u7247\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '商品名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontWeight: 500\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '分类',\n    dataIndex: 'category',\n    key: 'category',\n    width: 120,\n    render: category => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: (category === null || category === void 0 ? void 0 : category.name) || '未分类'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 34\n    }, this)\n  }, {\n    title: '质量等级',\n    dataIndex: 'qualityLevel',\n    key: 'qualityLevel',\n    width: 100,\n    render: level => {\n      const levelInfo = qualityLevelMap[level];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: (levelInfo === null || levelInfo === void 0 ? void 0 : levelInfo.color) || 'default',\n        children: (levelInfo === null || levelInfo === void 0 ? void 0 : levelInfo.label) || '未知'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '产地',\n    dataIndex: 'origin',\n    key: 'origin',\n    width: 120\n  }, {\n    title: '供应商',\n    dataIndex: 'supplierName',\n    key: 'supplierName',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"green\",\n      children: text || '未知供应商'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: (status, record) => /*#__PURE__*/_jsxDEV(Switch, {\n      checked: status === ProductStatus.ONLINE,\n      onChange: () => handleToggleStatus(record),\n      checkedChildren: \"\\u4E0A\\u67B6\",\n      unCheckedChildren: \"\\u4E0B\\u67B6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 180,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleView(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '4px'\n            },\n            children: [\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u5546\\u54C1\\\"\", record.name, \"\\\"\\u5417\\uFF1F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#999'\n            },\n            children: \"\\u5220\\u9664\\u540E\\u5C06\\u65E0\\u6CD5\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 15\n        }, this),\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\\u5220\\u9664\",\n        cancelText: \"\\u53D6\\u6D88\",\n        okButtonProps: {\n          danger: true\n        },\n        placement: \"topRight\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-list-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u5546\\u54C1\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: searchForm,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",\n                allowClear: true,\n                onPressEnter: e => handleQuickSearch(e.target.value),\n                suffix: /*#__PURE__*/_jsxDEV(SearchOutlined, {\n                  style: {\n                    color: '#1890ff',\n                    cursor: 'pointer'\n                  },\n                  onClick: () => {\n                    const value = searchForm.getFieldValue('name');\n                    if (value) handleQuickSearch(value);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"categoryId\",\n              label: \"\\u5546\\u54C1\\u5206\\u7C7B\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\\u5206\\u7C7B\",\n                allowClear: true,\n                children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"qualityLevel\",\n              label: \"\\u8D28\\u91CF\\u7B49\\u7EA7\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u8D28\\u91CF\\u7B49\\u7EA7\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.EXCELLENT,\n                  children: \"\\u4F18\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.GOOD,\n                  children: \"\\u826F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.MEDIUM,\n                  children: \"\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u5546\\u54C1\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: ProductStatus.ONLINE,\n                  children: \"\\u4E0A\\u67B6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: ProductStatus.OFFLINE,\n                  children: \"\\u4E0B\\u67B6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"origin\",\n              label: \"\\u4EA7\\u5730\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u5730\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 27\n                  }, this),\n                  loading: loading,\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 27\n                  }, this),\n                  disabled: loading,\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u641C\\u7D22\\u7ED3\\u679C\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#666',\n                  fontSize: '14px'\n                },\n                children: loading ? '搜索中...' : `共找到 ${total} 条商品`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 23\n              }, this),\n              onClick: handleAdd,\n              size: \"middle\",\n              children: \"\\u65B0\\u589E\\u5546\\u54C1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 23\n              }, this),\n              onClick: () => message.info('导出功能开发中...'),\n              disabled: loading || total === 0,\n              children: \"\\u5BFC\\u51FA\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#666',\n                fontSize: '14px'\n              },\n              children: products.length > 0 && `显示第 ${(queryParams.page - 1) * queryParams.pageSize + 1}-${Math.min(queryParams.page * queryParams.pageSize, total)} 条`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 23\n              }, this),\n              onClick: fetchProducts,\n              loading: loading,\n              title: \"\\u5237\\u65B0\\u6570\\u636E\",\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: products,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        locale: {\n          emptyText: loading ? '加载中...' : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '40px 0',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '16px',\n                color: '#999',\n                marginBottom: '8px'\n              },\n              children: \"\\u6682\\u65E0\\u5546\\u54C1\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                color: '#ccc'\n              },\n              children: Object.keys(queryParams).some(key => key !== 'page' && key !== 'pageSize' && queryParams[key]) ? '请尝试调整搜索条件' : '点击\"新增商品\"按钮添加第一个商品'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 15\n          }, this)\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => {\n            if (total === 0) return '暂无数据';\n            return `第 ${range[0]}-${range[1]} 条/共 ${total} 条`;\n          },\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          },\n          onShowSizeChange: (current, size) => {\n            setQueryParams({\n              ...queryParams,\n              page: 1,\n              // 改变页面大小时回到第一页\n              pageSize: size\n            });\n          },\n          pageSizeOptions: ['10', '20', '50', '100'],\n          size: 'default'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [editingProduct ? /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 50\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: editingProduct ? `编辑商品 - ${editingProduct.name}` : '新增商品'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 11\n      }, this),\n      open: isModalVisible,\n      onCancel: () => {\n        if (saving) {\n          message.warning('正在保存中，请稍候...');\n          return;\n        }\n        setIsModalVisible(false);\n        form.resetFields();\n        setFileList([]);\n        setEditingProduct(null);\n      },\n      footer: null,\n      width: 800,\n      destroyOnClose: true,\n      maskClosable: !saving,\n      closable: !saving,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入商品名称'\n              }, {\n                min: 2,\n                max: 50,\n                message: '商品名称长度为2-50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",\n                showCount: true,\n                maxLength: 50\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"categoryId\",\n              label: \"\\u5546\\u54C1\\u5206\\u7C7B\",\n              rules: [{\n                required: true,\n                message: '请选择商品分类'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\\u5206\\u7C7B\",\n                children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"qualityLevel\",\n              label: \"\\u8D28\\u91CF\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择质量等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u8D28\\u91CF\\u7B49\\u7EA7\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.EXCELLENT,\n                  children: \"\\u4F18\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 847,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.GOOD,\n                  children: \"\\u826F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.MEDIUM,\n                  children: \"\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"origin\",\n              label: \"\\u4EA7\\u5730\",\n              rules: [{\n                required: true,\n                message: '请输入产地'\n              }, {\n                max: 30,\n                message: '产地名称不能超过30个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u5730\",\n                showCount: true,\n                maxLength: 30\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"supplierId\",\n              label: \"\\u4F9B\\u5E94\\u5546\",\n              rules: [{\n                required: true,\n                message: '请选择供应商'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u4F9B\\u5E94\\u5546\",\n                children: suppliers.map(supplier => /*#__PURE__*/_jsxDEV(Option, {\n                  value: supplier.id,\n                  children: supplier.name\n                }, supplier.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u5546\\u54C1\\u72B6\\u6001\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checkedChildren: \"\\u4E0A\\u67B6\",\n                unCheckedChildren: \"\\u4E0B\\u67B6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u5546\\u54C1\\u63CF\\u8FF0\",\n          rules: [{\n            max: 500,\n            message: '商品描述不能超过500个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u63CF\\u8FF0\",\n            rows: 4,\n            showCount: true,\n            maxLength: 500\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 905,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"images\",\n          label: \"\\u5546\\u54C1\\u56FE\\u7247\",\n          extra: \"\\u652F\\u6301jpg\\u3001png\\u683C\\u5F0F\\uFF0C\\u5355\\u5F20\\u56FE\\u7247\\u4E0D\\u8D85\\u8FC75MB\\uFF0C\\u6700\\u591A\\u4E0A\\u4F205\\u5F20\",\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            listType: \"picture-card\",\n            fileList: fileList,\n            onChange: handleUploadChange,\n            beforeUpload: beforeUpload,\n            maxCount: 5,\n            accept: \"image/jpeg,image/png\",\n            children: fileList.length >= 5 ? null : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 8\n                },\n                children: \"\\u4E0A\\u4F20\\u56FE\\u7247\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 11\n        }, this), formError && /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '12px 16px',\n              backgroundColor: '#fff2f0',\n              border: '1px solid #ffccc7',\n              borderRadius: '6px',\n              color: '#ff4d4f',\n              fontSize: '14px',\n              lineHeight: '1.5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u274C \\u64CD\\u4F5C\\u5931\\u8D25\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 17\n            }, this), formError]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 938,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 937,\n          columnNumber: 13\n        }, this), formSuccess && /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '12px 16px',\n              backgroundColor: '#f6ffed',\n              border: '1px solid #b7eb8f',\n              borderRadius: '6px',\n              color: '#52c41a',\n              fontSize: '14px',\n              lineHeight: '1.5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u2705 \\u64CD\\u4F5C\\u6210\\u529F\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 17\n            }, this), formSuccess]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0,\n            marginTop: '24px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderTop: '1px solid #f0f0f0',\n              paddingTop: '16px',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#666',\n                fontSize: '12px'\n              },\n              children: editingProduct ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建商品'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => {\n                  if (saving) {\n                    message.warning('正在保存中，请稍候...');\n                    return;\n                  }\n                  setIsModalVisible(false);\n                  form.resetFields();\n                  setFileList([]);\n                  setEditingProduct(null);\n                  setFormError('');\n                  setFormSuccess('');\n                },\n                disabled: saving,\n                size: \"middle\",\n                children: \"\\u53D6\\u6D88\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: saving,\n                disabled: saving,\n                size: \"middle\",\n                icon: editingProduct ? /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 42\n                }, this) : /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 61\n                }, this),\n                children: saving ? '保存中...' : editingProduct ? '更新商品' : '创建商品'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 999,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 970,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 799,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 775,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u5546\\u54C1\\u8BE6\\u60C5\", viewingProduct ? ` - ${viewingProduct.name}` : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1018,\n        columnNumber: 11\n      }, this),\n      open: isDetailVisible,\n      onCancel: () => setIsDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => {\n          if (viewingProduct) {\n            setIsDetailVisible(false);\n            handleEdit(viewingProduct);\n          }\n        },\n        children: \"\\u7F16\\u8F91\\u5546\\u54C1\"\n      }, \"edit\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setIsDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 1034,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: viewingProduct && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1ID\",\n          children: viewingProduct.id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1042,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n          children: viewingProduct.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1043,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u5206\\u7C7B\",\n          children: ((_viewingProduct$categ = viewingProduct.category) === null || _viewingProduct$categ === void 0 ? void 0 : _viewingProduct$categ.name) || '未分类'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8D28\\u91CF\\u7B49\\u7EA7\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: (_qualityLevelMap$view = qualityLevelMap[viewingProduct.qualityLevel]) === null || _qualityLevelMap$view === void 0 ? void 0 : _qualityLevelMap$view.color,\n            children: (_qualityLevelMap$view2 = qualityLevelMap[viewingProduct.qualityLevel]) === null || _qualityLevelMap$view2 === void 0 ? void 0 : _qualityLevelMap$view2.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u5730\",\n          children: viewingProduct.origin\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1050,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4F9B\\u5E94\\u5546\",\n          children: viewingProduct.supplierName || '未知供应商'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1051,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: viewingProduct.status === ProductStatus.ONLINE ? 'green' : 'red',\n            children: viewingProduct.status === ProductStatus.ONLINE ? '上架' : '下架'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1052,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: new Date(viewingProduct.createdAt).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u63CF\\u8FF0\",\n          span: 2,\n          children: viewingProduct.description || '暂无描述'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 13\n        }, this), viewingProduct.images && viewingProduct.images.length > 0 && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u56FE\\u7247\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: viewingProduct.images.map((image, index) => /*#__PURE__*/_jsxDEV(Image, {\n              width: 100,\n              height: 100,\n              src: image,\n              style: {\n                objectFit: 'cover',\n                borderRadius: 4\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1064,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1041,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1016,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 584,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductList, \"sERs+fBe6gx3Ki6eZ7EdqHy+U/Q=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = ProductList;\nexport default ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Popconfirm", "Typography", "Row", "Col", "Upload", "Image", "Descriptions", "Switch", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ExportOutlined", "ReloadOutlined", "UploadOutlined", "productService", "uploadService", "jsxDEV", "_jsxDEV", "Title", "Option", "TextArea", "QualityLevel", "ProductStatus", "ProductList", "_s", "_viewingProduct$categ", "_qualityLevelMap$view", "_qualityLevelMap$view2", "products", "setProducts", "categories", "setCategories", "suppliers", "setSuppliers", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "isDetailVisible", "setIsDetailVisible", "editingProduct", "setEditingProduct", "viewingProduct", "setViewingProduct", "fileList", "setFileList", "saving", "setSaving", "formError", "setFormError", "formSuccess", "setFormSuccess", "form", "useForm", "searchForm", "qualityLevelMap", "EXCELLENT", "label", "color", "GOOD", "MEDIUM", "fetchProducts", "response", "getProductList", "success", "data", "list", "error", "console", "errorMsg", "status", "fetchCategories", "getCategoryList", "warning", "fetchSuppliers", "fetch", "json", "supplierList", "map", "user", "id", "name", "realName", "username", "handleSearch", "values", "filteredValues", "Object", "keys", "reduce", "acc", "key", "undefined", "info", "handleReset", "resetFields", "resetParams", "handleQuickSearch", "value", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleAdd", "handleEdit", "product", "ONLINE", "images", "length", "imageFiles", "url", "index", "uid", "handleView", "handleDelete", "deleteProduct", "content", "duration", "handleSave", "imageUrls", "newFiles", "filter", "file", "originFileObj", "existingUrls", "files", "uploadResponse", "uploadImages", "Array", "isArray", "item", "allImageUrls", "productData", "OFFLINE", "updateProduct", "createProduct", "successMsg", "setTimeout", "_error$response", "_error$response$data", "handleToggleStatus", "newStatus", "statusText", "updateProductStatus", "handleUploadChange", "newFileList", "beforeUpload", "isJpgOrPng", "type", "isLt5M", "size", "columns", "title", "dataIndex", "width", "render", "height", "src", "style", "objectFit", "borderRadius", "fallback", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "display", "alignItems", "justifyContent", "children", "text", "fontWeight", "category", "level", "levelInfo", "record", "checked", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "Date", "toLocaleString", "fixed", "_", "icon", "onClick", "marginBottom", "fontSize", "onConfirm", "okText", "cancelText", "okButtonProps", "danger", "placement", "className", "layout", "onFinish", "autoComplete", "gutter", "xs", "sm", "md", "<PERSON><PERSON>", "placeholder", "allowClear", "onPressEnter", "e", "target", "suffix", "cursor", "getFieldValue", "htmlType", "disabled", "justify", "align", "Math", "min", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "locale", "emptyText", "padding", "textAlign", "some", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onShowSizeChange", "pageSizeOptions", "gap", "open", "onCancel", "footer", "destroyOnClose", "maskClosable", "closable", "span", "rules", "required", "max", "showCount", "max<PERSON><PERSON><PERSON>", "supplier", "valuePropName", "rows", "extra", "listType", "maxCount", "accept", "marginTop", "backgroundColor", "border", "lineHeight", "borderTop", "paddingTop", "column", "bordered", "qualityLevel", "origin", "supplierName", "createdAt", "description", "wrap", "image", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/ProductList/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  Upload,\n  Image,\n\n  Descriptions,\n  Switch,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  UploadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { UploadFile } from 'antd/es/upload/interface';\nimport { productService } from '../../../services/productService';\nimport { uploadService } from '../../../services/uploadService';\n// import { handleError, handleSuccess } from '../../../utils/errorHandler';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// 商品质量等级枚举\nexport enum QualityLevel {\n  EXCELLENT = 1, // 优\n  GOOD = 2,      // 良\n  MEDIUM = 3,    // 中\n}\n\n// 商品状态枚举\nexport enum ProductStatus {\n  OFFLINE = 0, // 下架\n  ONLINE = 1,  // 上架\n}\n\n// 商品数据接口\nexport interface Product {\n  id: number;\n  name: string;\n  categoryId: number;\n  category?: Category;\n  description?: string;\n  qualityLevel: QualityLevel;\n  origin: string;\n  supplierId: number;\n  supplierName?: string;\n  status: ProductStatus;\n  images?: string[];\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 商品类别接口\nexport interface Category {\n  id: number;\n  name: string;\n  parentId?: number;\n  level: number;\n}\n\n// 查询参数接口\ninterface ProductQueryParams {\n  name?: string;\n  categoryId?: number;\n  qualityLevel?: QualityLevel;\n  status?: ProductStatus;\n  origin?: string;\n  page: number;\n  pageSize: number;\n}\n\nconst ProductList: React.FC = () => {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [suppliers, setSuppliers] = useState<{id: number, name: string}[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<ProductQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isDetailVisible, setIsDetailVisible] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);\n  const [fileList, setFileList] = useState<UploadFile[]>([]);\n  const [saving, setSaving] = useState(false);\n  const [formError, setFormError] = useState<string>('');\n  const [formSuccess, setFormSuccess] = useState<string>('');\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 质量等级映射\n  const qualityLevelMap = {\n    [QualityLevel.EXCELLENT]: { label: '优', color: 'green' },\n    [QualityLevel.GOOD]: { label: '良', color: 'blue' },\n    [QualityLevel.MEDIUM]: { label: '中', color: 'orange' },\n  };\n\n  // 获取商品列表\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await productService.getProductList(queryParams);\n      if (response.success) {\n        setProducts(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取商品列表失败');\n        setProducts([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取商品列表失败:', error);\n      let errorMsg = '获取商品列表失败';\n      if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问商品列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setProducts([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品类别\n  const fetchCategories = async () => {\n    try {\n      const response = await productService.getCategoryList();\n      if (response.success) {\n        setCategories(response.data);\n      } else {\n        console.error('获取商品类别失败:', response.message);\n        message.warning('获取商品分类失败，部分功能可能受限');\n        setCategories([]);\n      }\n    } catch (error: any) {\n      console.error('获取商品类别失败:', error);\n      message.error('获取商品分类失败，请刷新页面重试');\n      setCategories([]);\n    }\n  };\n\n  // 获取供应商列表\n  const fetchSuppliers = async () => {\n    try {\n      // 获取所有用户，然后筛选出供应商类型的用户\n      const response = await fetch('/api/v1/users?page=1&pageSize=100');\n      const data = await response.json();\n      if (data.success && data.list) {\n        // 将用户转换为供应商格式\n        const supplierList = data.list.map((user: any) => ({\n          id: user.id,\n          name: user.realName || user.username\n        }));\n        setSuppliers(supplierList);\n      }\n    } catch (error) {\n      console.error('获取供应商列表失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n    fetchSuppliers();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    // 过滤掉空值\n    const filteredValues = Object.keys(values).reduce((acc, key) => {\n      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {\n        acc[key] = values[key];\n      }\n      return acc;\n    }, {} as any);\n\n    setQueryParams({\n      ...queryParams,\n      ...filteredValues,\n      page: 1,\n    });\n\n    message.info(`正在搜索商品...`);\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    const resetParams = {\n      page: 1,\n      pageSize: queryParams.pageSize, // 保持当前页面大小\n    };\n    setQueryParams(resetParams);\n    message.success('搜索条件已重置');\n  };\n\n  // 快速搜索（输入框回车）\n  const handleQuickSearch = (value: string) => {\n    if (value.trim()) {\n      searchForm.setFieldsValue({ name: value.trim() });\n      handleSearch({ name: value.trim() });\n    }\n  };\n\n  // 新增商品\n  const handleAdd = () => {\n    setEditingProduct(null);\n    form.resetFields();\n    setFileList([]);\n    setFormError('');\n    setFormSuccess('');\n    setIsModalVisible(true);\n  };\n\n  // 编辑商品\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product);\n    form.setFieldsValue({\n      ...product,\n      status: product.status === ProductStatus.ONLINE,\n    });\n\n    // 设置图片列表\n    if (product.images && product.images.length > 0) {\n      const imageFiles = product.images.map((url, index) => ({\n        uid: `${index}`,\n        name: `image-${index}`,\n        status: 'done' as const,\n        url: url,\n      }));\n      setFileList(imageFiles);\n    } else {\n      setFileList([]);\n    }\n\n    setFormError('');\n    setFormSuccess('');\n    setIsModalVisible(true);\n  };\n\n  // 查看商品详情\n  const handleView = (product: Product) => {\n    setViewingProduct(product);\n    setIsDetailVisible(true);\n  };\n\n  // 删除商品\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await productService.deleteProduct(id);\n      if (response.success) {\n        message.success({\n          content: '商品删除成功！',\n          duration: 3,\n        });\n        // 如果当前页没有数据了，回到上一页\n        if (products.length === 1 && queryParams.page > 1) {\n          setQueryParams({\n            ...queryParams,\n            page: queryParams.page - 1,\n          });\n        } else {\n          fetchProducts();\n        }\n      } else {\n        message.error({\n          content: response.message || '删除失败，请稍后重试',\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('删除商品失败:', error);\n      message.error({\n        content: '删除失败，请检查网络连接后重试',\n        duration: 5,\n      });\n    }\n  };\n\n  // 保存商品\n  const handleSave = async (values: any) => {\n    setSaving(true);\n    setFormError('');\n    setFormSuccess('');\n\n    try {\n      let imageUrls: string[] = [];\n\n      // 处理图片上传\n      const newFiles = fileList.filter(file => file.originFileObj && !file.url);\n      const existingUrls = fileList.filter(file => file.url).map(file => file.url!);\n\n      if (newFiles.length > 0) {\n        // 上传新图片\n        const files = newFiles.map(file => file.originFileObj as File);\n        const uploadResponse = await uploadService.uploadImages(files);\n\n        if (uploadResponse.success && uploadResponse.data) {\n          // 如果返回的是单个URL，转换为数组\n          if (typeof uploadResponse.data === 'string') {\n            imageUrls = [uploadResponse.data];\n          } else if (Array.isArray(uploadResponse.data)) {\n            imageUrls = uploadResponse.data.map((item: any) => item.url || item);\n          } else if (uploadResponse.data.url) {\n            imageUrls = [uploadResponse.data.url];\n          }\n        } else {\n          setFormError(uploadResponse.message || '图片上传失败');\n          return;\n        }\n      }\n\n      // 合并已有图片和新上传的图片\n      const allImageUrls = [...existingUrls, ...imageUrls];\n\n      const productData = {\n        ...values,\n        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,\n        images: allImageUrls,\n      };\n\n      let response;\n      if (editingProduct) {\n        response = await productService.updateProduct(editingProduct.id, productData);\n      } else {\n        response = await productService.createProduct(productData);\n      }\n\n      if (response.success) {\n        const successMsg = editingProduct ? '商品信息更新成功！' : '商品创建成功！';\n        setFormSuccess(successMsg);\n\n        // 延迟关闭模态框，让用户看到成功消息\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setFileList([]);\n          setEditingProduct(null);\n          setFormSuccess('');\n          fetchProducts();\n        }, 1500);\n      } else {\n        const errorMsg = response.message || '操作失败，请稍后重试';\n        setFormError(errorMsg);\n      }\n    } catch (error: any) {\n      console.error('保存商品失败:', error);\n      const errorMsg = error.response?.data?.message || error.message || '网络错误，请检查连接后重试';\n      setFormError(errorMsg);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 切换商品状态\n  const handleToggleStatus = async (product: Product) => {\n    const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;\n    const statusText = newStatus === ProductStatus.ONLINE ? '上架' : '下架';\n\n    try {\n      const response = await productService.updateProductStatus(product.id, newStatus);\n      if (response.success) {\n        message.success({\n          content: `商品\"${product.name}\"已成功${statusText}！`,\n          duration: 3,\n        });\n        fetchProducts();\n      } else {\n        message.error({\n          content: response.message || `${statusText}失败，请稍后重试`,\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('更新商品状态失败:', error);\n      message.error({\n        content: `${statusText}失败，请检查网络连接后重试`,\n        duration: 5,\n      });\n    }\n  };\n\n  // 图片上传处理\n  const handleUploadChange = ({ fileList: newFileList }: any) => {\n    setFileList(newFileList);\n  };\n\n  // 图片上传前的处理\n  const beforeUpload = (file: any) => {\n    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';\n    if (!isJpgOrPng) {\n      message.error('只能上传 JPG/PNG 格式的图片!');\n      return false;\n    }\n    const isLt5M = file.size / 1024 / 1024 < 5;\n    if (!isLt5M) {\n      message.error('图片大小不能超过 5MB!');\n      return false;\n    }\n    return false; // 阻止自动上传，手动处理\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Product> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '商品图片',\n      dataIndex: 'images',\n      key: 'images',\n      width: 100,\n      render: (images: string[]) => (\n        images && images.length > 0 ? (\n          <Image\n            width={60}\n            height={60}\n            src={images[0]}\n            style={{ objectFit: 'cover', borderRadius: 4 }}\n            fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n          />\n        ) : (\n          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            无图片\n          </div>\n        )\n      ),\n    },\n    {\n      title: '商品名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontWeight: 500 }}>{text}</div>\n      ),\n    },\n    {\n      title: '分类',\n      dataIndex: 'category',\n      key: 'category',\n      width: 120,\n      render: (category: any) => <Tag color=\"blue\">{category?.name || '未分类'}</Tag>,\n    },\n    {\n      title: '质量等级',\n      dataIndex: 'qualityLevel',\n      key: 'qualityLevel',\n      width: 100,\n      render: (level: QualityLevel) => {\n        const levelInfo = qualityLevelMap[level];\n        return (\n          <Tag color={levelInfo?.color || 'default'}>\n            {levelInfo?.label || '未知'}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '产地',\n      dataIndex: 'origin',\n      key: 'origin',\n      width: 120,\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplierName',\n      key: 'supplierName',\n      width: 120,\n      render: (text: string) => <Tag color=\"green\">{text || '未知供应商'}</Tag>,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: ProductStatus, record: Product) => (\n        <Switch\n          checked={status === ProductStatus.ONLINE}\n          onChange={() => handleToggleStatus(record)}\n          checkedChildren=\"上架\"\n          unCheckedChildren=\"下架\"\n        />\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 180,\n      fixed: 'right',\n      render: (_, record: Product) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleView(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title={\n              <div>\n                <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>\n                  确定要删除商品\"{record.name}\"吗？\n                </div>\n                <div style={{ fontSize: '12px', color: '#999' }}>\n                  删除后将无法恢复，请谨慎操作\n                </div>\n              </div>\n            }\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定删除\"\n            cancelText=\"取消\"\n            okButtonProps={{ danger: true }}\n            placement=\"topRight\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"product-list-container\">\n      <Title level={2}>商品管理</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          form={searchForm}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"name\" label=\"商品名称\">\n                <Input\n                  placeholder=\"请输入商品名称\"\n                  allowClear\n                  onPressEnter={(e) => handleQuickSearch((e.target as HTMLInputElement).value)}\n                  suffix={\n                    <SearchOutlined\n                      style={{ color: '#1890ff', cursor: 'pointer' }}\n                      onClick={() => {\n                        const value = searchForm.getFieldValue('name');\n                        if (value) handleQuickSearch(value);\n                      }}\n                    />\n                  }\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"categoryId\" label=\"商品分类\">\n                <Select placeholder=\"请选择商品分类\" allowClear>\n                  {categories.map(category => (\n                    <Option key={category.id} value={category.id}>\n                      {category.name}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"qualityLevel\" label=\"质量等级\">\n                <Select placeholder=\"请选择质量等级\" allowClear>\n                  <Option value={QualityLevel.EXCELLENT}>优</Option>\n                  <Option value={QualityLevel.GOOD}>良</Option>\n                  <Option value={QualityLevel.MEDIUM}>中</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"商品状态\">\n                <Select placeholder=\"请选择商品状态\" allowClear>\n                  <Option value={ProductStatus.ONLINE}>上架</Option>\n                  <Option value={ProductStatus.OFFLINE}>下架</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"origin\" label=\"产地\">\n                <Input placeholder=\"请输入产地\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    icon={<SearchOutlined />}\n                    loading={loading}\n                  >\n                    搜索\n                  </Button>\n                  <Button\n                    onClick={handleReset}\n                    icon={<ReloadOutlined />}\n                    disabled={loading}\n                  >\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item label=\"搜索结果\">\n                <div style={{ color: '#666', fontSize: '14px' }}>\n                  {loading ? '搜索中...' : `共找到 ${total} 条商品`}\n                </div>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={handleAdd}\n                size=\"middle\"\n              >\n                新增商品\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={() => message.info('导出功能开发中...')}\n                disabled={loading || total === 0}\n              >\n                导出数据\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <div style={{ color: '#666', fontSize: '14px' }}>\n                {products.length > 0 && (\n                  `显示第 ${(queryParams.page - 1) * queryParams.pageSize + 1}-${Math.min(queryParams.page * queryParams.pageSize, total)} 条`\n                )}\n              </div>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={fetchProducts}\n                loading={loading}\n                title=\"刷新数据\"\n              >\n                刷新\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 商品列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={products}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          locale={{\n            emptyText: loading ? '加载中...' : (\n              <div style={{ padding: '40px 0', textAlign: 'center' }}>\n                <div style={{ fontSize: '16px', color: '#999', marginBottom: '8px' }}>\n                  暂无商品数据\n                </div>\n                <div style={{ fontSize: '14px', color: '#ccc' }}>\n                  {Object.keys(queryParams).some(key => key !== 'page' && key !== 'pageSize' && queryParams[key as keyof ProductQueryParams])\n                    ? '请尝试调整搜索条件'\n                    : '点击\"新增商品\"按钮添加第一个商品'\n                  }\n                </div>\n              </div>\n            )\n          }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => {\n              if (total === 0) return '暂无数据';\n              return `第 ${range[0]}-${range[1]} 条/共 ${total} 条`;\n            },\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n            onShowSizeChange: (current, size) => {\n              setQueryParams({\n                ...queryParams,\n                page: 1, // 改变页面大小时回到第一页\n                pageSize: size,\n              });\n            },\n            pageSizeOptions: ['10', '20', '50', '100'],\n            size: 'default',\n          }}\n        />\n      </Card>\n\n      {/* 商品编辑模态框 */}\n      <Modal\n        title={\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            {editingProduct ? <EditOutlined /> : <PlusOutlined />}\n            <span>{editingProduct ? `编辑商品 - ${editingProduct.name}` : '新增商品'}</span>\n          </div>\n        }\n        open={isModalVisible}\n        onCancel={() => {\n          if (saving) {\n            message.warning('正在保存中，请稍候...');\n            return;\n          }\n          setIsModalVisible(false);\n          form.resetFields();\n          setFileList([]);\n          setEditingProduct(null);\n        }}\n        footer={null}\n        width={800}\n        destroyOnClose\n        maskClosable={!saving}\n        closable={!saving}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"商品名称\"\n                rules={[\n                  { required: true, message: '请输入商品名称' },\n                  { min: 2, max: 50, message: '商品名称长度为2-50个字符' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入商品名称\"\n                  showCount\n                  maxLength={50}\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"categoryId\"\n                label=\"商品分类\"\n                rules={[{ required: true, message: '请选择商品分类' }]}\n              >\n                <Select placeholder=\"请选择商品分类\">\n                  {categories.map(category => (\n                    <Option key={category.id} value={category.id}>\n                      {category.name}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"qualityLevel\"\n                label=\"质量等级\"\n                rules={[{ required: true, message: '请选择质量等级' }]}\n              >\n                <Select placeholder=\"请选择质量等级\">\n                  <Option value={QualityLevel.EXCELLENT}>优</Option>\n                  <Option value={QualityLevel.GOOD}>良</Option>\n                  <Option value={QualityLevel.MEDIUM}>中</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"origin\"\n                label=\"产地\"\n                rules={[\n                  { required: true, message: '请输入产地' },\n                  { max: 30, message: '产地名称不能超过30个字符' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入产地\"\n                  showCount\n                  maxLength={30}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"supplierId\"\n                label=\"供应商\"\n                rules={[{ required: true, message: '请选择供应商' }]}\n              >\n                <Select placeholder=\"请选择供应商\">\n                  {suppliers.map(supplier => (\n                    <Option key={supplier.id} value={supplier.id}>\n                      {supplier.name}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"商品状态\"\n                valuePropName=\"checked\"\n              >\n                <Switch checkedChildren=\"上架\" unCheckedChildren=\"下架\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"商品描述\"\n            rules={[\n              { max: 500, message: '商品描述不能超过500个字符' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入商品描述\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"images\"\n            label=\"商品图片\"\n            extra=\"支持jpg、png格式，单张图片不超过5MB，最多上传5张\"\n          >\n            <Upload\n              listType=\"picture-card\"\n              fileList={fileList}\n              onChange={handleUploadChange}\n              beforeUpload={beforeUpload}\n              maxCount={5}\n              accept=\"image/jpeg,image/png\"\n            >\n              {fileList.length >= 5 ? null : (\n                <div>\n                  <UploadOutlined />\n                  <div style={{ marginTop: 8 }}>上传图片</div>\n                </div>\n              )}\n            </Upload>\n          </Form.Item>\n\n          {/* 错误消息显示 */}\n          {formError && (\n            <Form.Item>\n              <div style={{\n                padding: '12px 16px',\n                backgroundColor: '#fff2f0',\n                border: '1px solid #ffccc7',\n                borderRadius: '6px',\n                color: '#ff4d4f',\n                fontSize: '14px',\n                lineHeight: '1.5'\n              }}>\n                <strong>❌ 操作失败：</strong>{formError}\n              </div>\n            </Form.Item>\n          )}\n\n          {/* 成功消息显示 */}\n          {formSuccess && (\n            <Form.Item>\n              <div style={{\n                padding: '12px 16px',\n                backgroundColor: '#f6ffed',\n                border: '1px solid #b7eb8f',\n                borderRadius: '6px',\n                color: '#52c41a',\n                fontSize: '14px',\n                lineHeight: '1.5'\n              }}>\n                <strong>✅ 操作成功：</strong>{formSuccess}\n              </div>\n            </Form.Item>\n          )}\n\n          <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>\n            <div style={{\n              borderTop: '1px solid #f0f0f0',\n              paddingTop: '16px',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            }}>\n              <div style={{ color: '#666', fontSize: '12px' }}>\n                {editingProduct ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建商品'}\n              </div>\n              <Space>\n                <Button\n                  onClick={() => {\n                    if (saving) {\n                      message.warning('正在保存中，请稍候...');\n                      return;\n                    }\n                    setIsModalVisible(false);\n                    form.resetFields();\n                    setFileList([]);\n                    setEditingProduct(null);\n                    setFormError('');\n                    setFormSuccess('');\n                  }}\n                  disabled={saving}\n                  size=\"middle\"\n                >\n                  取消\n                </Button>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={saving}\n                  disabled={saving}\n                  size=\"middle\"\n                  icon={editingProduct ? <EditOutlined /> : <PlusOutlined />}\n                >\n                  {saving ? '保存中...' : (editingProduct ? '更新商品' : '创建商品')}\n                </Button>\n              </Space>\n            </div>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 商品详情模态框 */}\n      <Modal\n        title={\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            <EyeOutlined />\n            <span>商品详情{viewingProduct ? ` - ${viewingProduct.name}` : ''}</span>\n          </div>\n        }\n        open={isDetailVisible}\n        onCancel={() => setIsDetailVisible(false)}\n        footer={[\n          <Button key=\"edit\" type=\"primary\" onClick={() => {\n            if (viewingProduct) {\n              setIsDetailVisible(false);\n              handleEdit(viewingProduct);\n            }\n          }}>\n            编辑商品\n          </Button>,\n          <Button key=\"close\" onClick={() => setIsDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {viewingProduct && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"商品ID\">{viewingProduct.id}</Descriptions.Item>\n            <Descriptions.Item label=\"商品名称\">{viewingProduct.name}</Descriptions.Item>\n            <Descriptions.Item label=\"商品分类\">{viewingProduct.category?.name || '未分类'}</Descriptions.Item>\n            <Descriptions.Item label=\"质量等级\">\n              <Tag color={qualityLevelMap[viewingProduct.qualityLevel]?.color}>\n                {qualityLevelMap[viewingProduct.qualityLevel]?.label}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"产地\">{viewingProduct.origin}</Descriptions.Item>\n            <Descriptions.Item label=\"供应商\">{viewingProduct.supplierName || '未知供应商'}</Descriptions.Item>\n            <Descriptions.Item label=\"状态\">\n              <Tag color={viewingProduct.status === ProductStatus.ONLINE ? 'green' : 'red'}>\n                {viewingProduct.status === ProductStatus.ONLINE ? '上架' : '下架'}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"创建时间\">\n              {new Date(viewingProduct.createdAt).toLocaleString()}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"商品描述\" span={2}>\n              {viewingProduct.description || '暂无描述'}\n            </Descriptions.Item>\n            {viewingProduct.images && viewingProduct.images.length > 0 && (\n              <Descriptions.Item label=\"商品图片\" span={2}>\n                <Space wrap>\n                  {viewingProduct.images.map((image, index) => (\n                    <Image\n                      key={index}\n                      width={100}\n                      height={100}\n                      src={image}\n                      style={{ objectFit: 'cover', borderRadius: 4 }}\n                    />\n                  ))}\n                </Space>\n              </Descriptions.Item>\n            )}\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProductList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EAELC,YAAY,EACZC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAG1B,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,aAAa,QAAQ,iCAAiC;AAC/D;AACA,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGnB,UAAU;AAC5B,MAAM;EAAEoB;AAAO,CAAC,GAAG1B,MAAM;AACzB,MAAM;EAAE2B;AAAS,CAAC,GAAG5B,KAAK;;AAE1B;AACA,WAAY6B,YAAY,0BAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EACP;EADLA,YAAY,CAAZA,YAAY;EAEP;EAFLA,YAAY,CAAZA,YAAY,4BAGP;EAAA,OAHLA,YAAY;AAAA;;AAMxB;AACA,WAAYC,aAAa,0BAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EACV;EADHA,aAAa,CAAbA,aAAa,4BAEV;EAAA,OAFHA,aAAa;AAAA;;AAKzB;;AAiBA;;AAQA;;AAWA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAA+B,EAAE,CAAC;EAC5E,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAqB;IACjEsD,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAe,EAAE,CAAC;EAC1D,MAAM,CAACkE,MAAM,EAAEC,SAAS,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACwE,IAAI,CAAC,GAAG9D,IAAI,CAAC+D,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAGhE,IAAI,CAAC+D,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAME,eAAe,GAAG;IACtB,CAACxC,YAAY,CAACyC,SAAS,GAAG;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACxD,CAAC3C,YAAY,CAAC4C,IAAI,GAAG;MAAEF,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAO,CAAC;IAClD,CAAC3C,YAAY,CAAC6C,MAAM,GAAG;MAAEH,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAS;EACvD,CAAC;;EAED;EACA,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChChC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAMtD,cAAc,CAACuD,cAAc,CAAC/B,WAAW,CAAC;MACjE,IAAI8B,QAAQ,CAACE,OAAO,EAAE;QACpBzC,WAAW,CAACuC,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;QAC/BnC,QAAQ,CAAC+B,QAAQ,CAACG,IAAI,CAACnC,KAAK,CAAC;MAC/B,CAAC,MAAM;QACLvC,OAAO,CAAC4E,KAAK,CAACL,QAAQ,CAACvE,OAAO,IAAI,UAAU,CAAC;QAC7CgC,WAAW,CAAC,EAAE,CAAC;QACfQ,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOoC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIE,QAAQ,GAAG,UAAU;MACzB,IAAIF,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM;UAAEQ;QAAO,CAAC,GAAGH,KAAK,CAACL,QAAQ;QACjC,IAAIQ,MAAM,KAAK,GAAG,EAAE;UAClBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,YAAY;QACzB,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,eAAe;QAC5B;MACF;MACA9E,OAAO,CAAC4E,KAAK,CAACE,QAAQ,CAAC;MACvB9C,WAAW,CAAC,EAAE,CAAC;MACfQ,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMtD,cAAc,CAACgE,eAAe,CAAC,CAAC;MACvD,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpBvC,aAAa,CAACqC,QAAQ,CAACG,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLG,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEL,QAAQ,CAACvE,OAAO,CAAC;QAC5CA,OAAO,CAACkF,OAAO,CAAC,mBAAmB,CAAC;QACpChD,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC,CAAC,OAAO0C,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC5E,OAAO,CAAC4E,KAAK,CAAC,kBAAkB,CAAC;MACjC1C,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACA,MAAMZ,QAAQ,GAAG,MAAMa,KAAK,CAAC,mCAAmC,CAAC;MACjE,MAAMV,IAAI,GAAG,MAAMH,QAAQ,CAACc,IAAI,CAAC,CAAC;MAClC,IAAIX,IAAI,CAACD,OAAO,IAAIC,IAAI,CAACC,IAAI,EAAE;QAC7B;QACA,MAAMW,YAAY,GAAGZ,IAAI,CAACC,IAAI,CAACY,GAAG,CAAEC,IAAS,KAAM;UACjDC,EAAE,EAAED,IAAI,CAACC,EAAE;UACXC,IAAI,EAAEF,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACI;QAC9B,CAAC,CAAC,CAAC;QACHxD,YAAY,CAACkD,YAAY,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC;EACF,CAAC;;EAED;EACAtF,SAAS,CAAC,MAAM;IACdgF,aAAa,CAAC,CAAC;IACfU,eAAe,CAAC,CAAC;IACjBG,cAAc,CAAC,CAAC;IAClB;EACA,CAAC,EAAE,CAAC1C,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMoD,YAAY,GAAIC,MAAW,IAAK;IACpC;IACA,MAAMC,cAAc,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAC9D,IAAIN,MAAM,CAACM,GAAG,CAAC,KAAKC,SAAS,IAAIP,MAAM,CAACM,GAAG,CAAC,KAAK,IAAI,IAAIN,MAAM,CAACM,GAAG,CAAC,KAAK,EAAE,EAAE;QAC3ED,GAAG,CAACC,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;MACxB;MACA,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAQ,CAAC;IAEbzD,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAGsD,cAAc;MACjBpD,IAAI,EAAE;IACR,CAAC,CAAC;IAEF3C,OAAO,CAACsG,IAAI,CAAC,WAAW,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBxC,UAAU,CAACyC,WAAW,CAAC,CAAC;IACxB,MAAMC,WAAW,GAAG;MAClB9D,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAEH,WAAW,CAACG,QAAQ,CAAE;IAClC,CAAC;IACDF,cAAc,CAAC+D,WAAW,CAAC;IAC3BzG,OAAO,CAACyE,OAAO,CAAC,SAAS,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMiC,iBAAiB,GAAIC,KAAa,IAAK;IAC3C,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;MAChB7C,UAAU,CAAC8C,cAAc,CAAC;QAAEnB,IAAI,EAAEiB,KAAK,CAACC,IAAI,CAAC;MAAE,CAAC,CAAC;MACjDf,YAAY,CAAC;QAAEH,IAAI,EAAEiB,KAAK,CAACC,IAAI,CAAC;MAAE,CAAC,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtB5D,iBAAiB,CAAC,IAAI,CAAC;IACvBW,IAAI,CAAC2C,WAAW,CAAC,CAAC;IAClBlD,WAAW,CAAC,EAAE,CAAC;IACfI,YAAY,CAAC,EAAE,CAAC;IAChBE,cAAc,CAAC,EAAE,CAAC;IAClBd,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMiE,UAAU,GAAIC,OAAgB,IAAK;IACvC9D,iBAAiB,CAAC8D,OAAO,CAAC;IAC1BnD,IAAI,CAACgD,cAAc,CAAC;MAClB,GAAGG,OAAO;MACVjC,MAAM,EAAEiC,OAAO,CAACjC,MAAM,KAAKtD,aAAa,CAACwF;IAC3C,CAAC,CAAC;;IAEF;IACA,IAAID,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACE,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAMC,UAAU,GAAGJ,OAAO,CAACE,MAAM,CAAC3B,GAAG,CAAC,CAAC8B,GAAG,EAAEC,KAAK,MAAM;QACrDC,GAAG,EAAE,GAAGD,KAAK,EAAE;QACf5B,IAAI,EAAE,SAAS4B,KAAK,EAAE;QACtBvC,MAAM,EAAE,MAAe;QACvBsC,GAAG,EAAEA;MACP,CAAC,CAAC,CAAC;MACH/D,WAAW,CAAC8D,UAAU,CAAC;IACzB,CAAC,MAAM;MACL9D,WAAW,CAAC,EAAE,CAAC;IACjB;IAEAI,YAAY,CAAC,EAAE,CAAC;IAChBE,cAAc,CAAC,EAAE,CAAC;IAClBd,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM0E,UAAU,GAAIR,OAAgB,IAAK;IACvC5D,iBAAiB,CAAC4D,OAAO,CAAC;IAC1BhE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMyE,YAAY,GAAG,MAAOhC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMtD,cAAc,CAACyG,aAAa,CAACjC,EAAE,CAAC;MACvD,IAAIlB,QAAQ,CAACE,OAAO,EAAE;QACpBzE,OAAO,CAACyE,OAAO,CAAC;UACdkD,OAAO,EAAE,SAAS;UAClBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;QACA,IAAI7F,QAAQ,CAACoF,MAAM,KAAK,CAAC,IAAI1E,WAAW,CAACE,IAAI,GAAG,CAAC,EAAE;UACjDD,cAAc,CAAC;YACb,GAAGD,WAAW;YACdE,IAAI,EAAEF,WAAW,CAACE,IAAI,GAAG;UAC3B,CAAC,CAAC;QACJ,CAAC,MAAM;UACL2B,aAAa,CAAC,CAAC;QACjB;MACF,CAAC,MAAM;QACLtE,OAAO,CAAC4E,KAAK,CAAC;UACZ+C,OAAO,EAAEpD,QAAQ,CAACvE,OAAO,IAAI,YAAY;UACzC4H,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOhD,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B5E,OAAO,CAAC4E,KAAK,CAAC;QACZ+C,OAAO,EAAE,iBAAiB;QAC1BC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAO/B,MAAW,IAAK;IACxCtC,SAAS,CAAC,IAAI,CAAC;IACfE,YAAY,CAAC,EAAE,CAAC;IAChBE,cAAc,CAAC,EAAE,CAAC;IAElB,IAAI;MACF,IAAIkE,SAAmB,GAAG,EAAE;;MAE5B;MACA,MAAMC,QAAQ,GAAG1E,QAAQ,CAAC2E,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,aAAa,IAAI,CAACD,IAAI,CAACZ,GAAG,CAAC;MACzE,MAAMc,YAAY,GAAG9E,QAAQ,CAAC2E,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACZ,GAAG,CAAC,CAAC9B,GAAG,CAAC0C,IAAI,IAAIA,IAAI,CAACZ,GAAI,CAAC;MAE7E,IAAIU,QAAQ,CAACZ,MAAM,GAAG,CAAC,EAAE;QACvB;QACA,MAAMiB,KAAK,GAAGL,QAAQ,CAACxC,GAAG,CAAC0C,IAAI,IAAIA,IAAI,CAACC,aAAqB,CAAC;QAC9D,MAAMG,cAAc,GAAG,MAAMnH,aAAa,CAACoH,YAAY,CAACF,KAAK,CAAC;QAE9D,IAAIC,cAAc,CAAC5D,OAAO,IAAI4D,cAAc,CAAC3D,IAAI,EAAE;UACjD;UACA,IAAI,OAAO2D,cAAc,CAAC3D,IAAI,KAAK,QAAQ,EAAE;YAC3CoD,SAAS,GAAG,CAACO,cAAc,CAAC3D,IAAI,CAAC;UACnC,CAAC,MAAM,IAAI6D,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC3D,IAAI,CAAC,EAAE;YAC7CoD,SAAS,GAAGO,cAAc,CAAC3D,IAAI,CAACa,GAAG,CAAEkD,IAAS,IAAKA,IAAI,CAACpB,GAAG,IAAIoB,IAAI,CAAC;UACtE,CAAC,MAAM,IAAIJ,cAAc,CAAC3D,IAAI,CAAC2C,GAAG,EAAE;YAClCS,SAAS,GAAG,CAACO,cAAc,CAAC3D,IAAI,CAAC2C,GAAG,CAAC;UACvC;QACF,CAAC,MAAM;UACL3D,YAAY,CAAC2E,cAAc,CAACrI,OAAO,IAAI,QAAQ,CAAC;UAChD;QACF;MACF;;MAEA;MACA,MAAM0I,YAAY,GAAG,CAAC,GAAGP,YAAY,EAAE,GAAGL,SAAS,CAAC;MAEpD,MAAMa,WAAW,GAAG;QAClB,GAAG7C,MAAM;QACTf,MAAM,EAAEe,MAAM,CAACf,MAAM,GAAGtD,aAAa,CAACwF,MAAM,GAAGxF,aAAa,CAACmH,OAAO;QACpE1B,MAAM,EAAEwB;MACV,CAAC;MAED,IAAInE,QAAQ;MACZ,IAAItB,cAAc,EAAE;QAClBsB,QAAQ,GAAG,MAAMtD,cAAc,CAAC4H,aAAa,CAAC5F,cAAc,CAACwC,EAAE,EAAEkD,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLpE,QAAQ,GAAG,MAAMtD,cAAc,CAAC6H,aAAa,CAACH,WAAW,CAAC;MAC5D;MAEA,IAAIpE,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMsE,UAAU,GAAG9F,cAAc,GAAG,WAAW,GAAG,SAAS;QAC3DW,cAAc,CAACmF,UAAU,CAAC;;QAE1B;QACAC,UAAU,CAAC,MAAM;UACflG,iBAAiB,CAAC,KAAK,CAAC;UACxBe,IAAI,CAAC2C,WAAW,CAAC,CAAC;UAClBlD,WAAW,CAAC,EAAE,CAAC;UACfJ,iBAAiB,CAAC,IAAI,CAAC;UACvBU,cAAc,CAAC,EAAE,CAAC;UAClBU,aAAa,CAAC,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACL,MAAMQ,QAAQ,GAAGP,QAAQ,CAACvE,OAAO,IAAI,YAAY;QACjD0D,YAAY,CAACoB,QAAQ,CAAC;MACxB;IACF,CAAC,CAAC,OAAOF,KAAU,EAAE;MAAA,IAAAqE,eAAA,EAAAC,oBAAA;MACnBrE,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,MAAME,QAAQ,GAAG,EAAAmE,eAAA,GAAArE,KAAK,CAACL,QAAQ,cAAA0E,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBvE,IAAI,cAAAwE,oBAAA,uBAApBA,oBAAA,CAAsBlJ,OAAO,KAAI4E,KAAK,CAAC5E,OAAO,IAAI,eAAe;MAClF0D,YAAY,CAACoB,QAAQ,CAAC;IACxB,CAAC,SAAS;MACRtB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM2F,kBAAkB,GAAG,MAAOnC,OAAgB,IAAK;IACrD,MAAMoC,SAAS,GAAGpC,OAAO,CAACjC,MAAM,KAAKtD,aAAa,CAACwF,MAAM,GAAGxF,aAAa,CAACmH,OAAO,GAAGnH,aAAa,CAACwF,MAAM;IACxG,MAAMoC,UAAU,GAAGD,SAAS,KAAK3H,aAAa,CAACwF,MAAM,GAAG,IAAI,GAAG,IAAI;IAEnE,IAAI;MACF,MAAM1C,QAAQ,GAAG,MAAMtD,cAAc,CAACqI,mBAAmB,CAACtC,OAAO,CAACvB,EAAE,EAAE2D,SAAS,CAAC;MAChF,IAAI7E,QAAQ,CAACE,OAAO,EAAE;QACpBzE,OAAO,CAACyE,OAAO,CAAC;UACdkD,OAAO,EAAE,MAAMX,OAAO,CAACtB,IAAI,OAAO2D,UAAU,GAAG;UAC/CzB,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFtD,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLtE,OAAO,CAAC4E,KAAK,CAAC;UACZ+C,OAAO,EAAEpD,QAAQ,CAACvE,OAAO,IAAI,GAAGqJ,UAAU,UAAU;UACpDzB,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOhD,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC5E,OAAO,CAAC4E,KAAK,CAAC;QACZ+C,OAAO,EAAE,GAAG0B,UAAU,eAAe;QACrCzB,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM2B,kBAAkB,GAAGA,CAAC;IAAElG,QAAQ,EAAEmG;EAAiB,CAAC,KAAK;IAC7DlG,WAAW,CAACkG,WAAW,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIxB,IAAS,IAAK;IAClC,MAAMyB,UAAU,GAAGzB,IAAI,CAAC0B,IAAI,KAAK,YAAY,IAAI1B,IAAI,CAAC0B,IAAI,KAAK,WAAW;IAC1E,IAAI,CAACD,UAAU,EAAE;MACf1J,OAAO,CAAC4E,KAAK,CAAC,qBAAqB,CAAC;MACpC,OAAO,KAAK;IACd;IACA,MAAMgF,MAAM,GAAG3B,IAAI,CAAC4B,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;IAC1C,IAAI,CAACD,MAAM,EAAE;MACX5J,OAAO,CAAC4E,KAAK,CAAC,eAAe,CAAC;MAC9B,OAAO,KAAK;IACd;IACA,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMkF,OAA6B,GAAG,CACpC;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACf5D,GAAG,EAAE,IAAI;IACT6D,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnB5D,GAAG,EAAE,QAAQ;IACb6D,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGhD,MAAgB,IACvBA,MAAM,IAAIA,MAAM,CAACC,MAAM,GAAG,CAAC,gBACzB/F,OAAA,CAACd,KAAK;MACJ2J,KAAK,EAAE,EAAG;MACVE,MAAM,EAAE,EAAG;MACXC,GAAG,EAAElD,MAAM,CAAC,CAAC,CAAE;MACfmD,KAAK,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,YAAY,EAAE;MAAE,CAAE;MAC/CC,QAAQ,EAAC;IAAgoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1oB,CAAC,gBAEFxJ,OAAA;MAAKiJ,KAAK,EAAE;QAAEJ,KAAK,EAAE,EAAE;QAAEE,MAAM,EAAE,EAAE;QAAEU,UAAU,EAAE,SAAS;QAAEN,YAAY,EAAE,CAAC;QAAEO,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,EAAC;IAEhJ;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAGX,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjB5D,GAAG,EAAE,MAAM;IACX6D,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,iBACnB9J,OAAA;MAAKiJ,KAAK,EAAE;QAAEc,UAAU,EAAE;MAAI,CAAE;MAAAF,QAAA,EAAEC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEhD,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrB5D,GAAG,EAAE,UAAU;IACf6D,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGkB,QAAa,iBAAKhK,OAAA,CAACvB,GAAG;MAACsE,KAAK,EAAC,MAAM;MAAA8G,QAAA,EAAE,CAAAG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE1F,IAAI,KAAI;IAAK;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAC7E,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzB5D,GAAG,EAAE,cAAc;IACnB6D,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGmB,KAAmB,IAAK;MAC/B,MAAMC,SAAS,GAAGtH,eAAe,CAACqH,KAAK,CAAC;MACxC,oBACEjK,OAAA,CAACvB,GAAG;QAACsE,KAAK,EAAE,CAAAmH,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEnH,KAAK,KAAI,SAAU;QAAA8G,QAAA,EACvC,CAAAK,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEpH,KAAK,KAAI;MAAI;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAEV;EACF,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnB5D,GAAG,EAAE,QAAQ;IACb6D,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,cAAc;IACzB5D,GAAG,EAAE,cAAc;IACnB6D,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,iBAAK9J,OAAA,CAACvB,GAAG;MAACsE,KAAK,EAAC,OAAO;MAAA8G,QAAA,EAAEC,IAAI,IAAI;IAAO;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACrE,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnB5D,GAAG,EAAE,QAAQ;IACb6D,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACnF,MAAqB,EAAEwG,MAAe,kBAC7CnK,OAAA,CAACZ,MAAM;MACLgL,OAAO,EAAEzG,MAAM,KAAKtD,aAAa,CAACwF,MAAO;MACzCwE,QAAQ,EAAEA,CAAA,KAAMtC,kBAAkB,CAACoC,MAAM,CAAE;MAC3CG,eAAe,EAAC,cAAI;MACpBC,iBAAiB,EAAC;IAAI;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB;EAEL,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtB5D,GAAG,EAAE,WAAW;IAChB6D,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,IAAK,IAAIU,IAAI,CAACV,IAAI,CAAC,CAACW,cAAc,CAAC;EAC1D,CAAC,EACD;IACE9B,KAAK,EAAE,IAAI;IACX3D,GAAG,EAAE,QAAQ;IACb6D,KAAK,EAAE,GAAG;IACV6B,KAAK,EAAE,OAAO;IACd5B,MAAM,EAAEA,CAAC6B,CAAC,EAAER,MAAe,kBACzBnK,OAAA,CAAC1B,KAAK;MAACmK,IAAI,EAAC,OAAO;MAAAoB,QAAA,gBACjB7J,OAAA,CAAC3B,MAAM;QACLkK,IAAI,EAAC,MAAM;QACXE,IAAI,EAAC,OAAO;QACZmC,IAAI,eAAE5K,OAAA,CAACP,WAAW;UAAA4J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBqB,OAAO,EAAEA,CAAA,KAAMzE,UAAU,CAAC+D,MAAM,CAAE;QAAAN,QAAA,EACnC;MAED;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxJ,OAAA,CAAC3B,MAAM;QACLkK,IAAI,EAAC,MAAM;QACXE,IAAI,EAAC,OAAO;QACZmC,IAAI,eAAE5K,OAAA,CAACT,YAAY;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBqB,OAAO,EAAEA,CAAA,KAAMlF,UAAU,CAACwE,MAAM,CAAE;QAAAN,QAAA,EACnC;MAED;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxJ,OAAA,CAACnB,UAAU;QACT8J,KAAK,eACH3I,OAAA;UAAA6J,QAAA,gBACE7J,OAAA;YAAKiJ,KAAK,EAAE;cAAEc,UAAU,EAAE,MAAM;cAAEe,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,GAAC,8CAC/C,EAACM,MAAM,CAAC7F,IAAI,EAAC,gBACvB;UAAA;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxJ,OAAA;YAAKiJ,KAAK,EAAE;cAAE8B,QAAQ,EAAE,MAAM;cAAEhI,KAAK,EAAE;YAAO,CAAE;YAAA8G,QAAA,EAAC;UAEjD;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;QACDwB,SAAS,EAAEA,CAAA,KAAM3E,YAAY,CAAC8D,MAAM,CAAC9F,EAAE,CAAE;QACzC4G,MAAM,EAAC,0BAAM;QACbC,UAAU,EAAC,cAAI;QACfC,aAAa,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;QAChCC,SAAS,EAAC,UAAU;QAAAxB,QAAA,eAEpB7J,OAAA,CAAC3B,MAAM;UACLkK,IAAI,EAAC,MAAM;UACXE,IAAI,EAAC,OAAO;UACZ2C,MAAM;UACNR,IAAI,eAAE5K,OAAA,CAACR,cAAc;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAK,QAAA,EAC1B;QAED;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACExJ,OAAA;IAAKsL,SAAS,EAAC,wBAAwB;IAAAzB,QAAA,gBACrC7J,OAAA,CAACC,KAAK;MAACgK,KAAK,EAAE,CAAE;MAAAJ,QAAA,EAAC;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7BxJ,OAAA,CAAC7B,IAAI;MAACmN,SAAS,EAAC,aAAa;MAAC7C,IAAI,EAAC,OAAO;MAAAoB,QAAA,eACxC7J,OAAA,CAACrB,IAAI;QACH8D,IAAI,EAAEE,UAAW;QACjB4I,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAE/G,YAAa;QACvBgH,YAAY,EAAC,KAAK;QAAA5B,QAAA,eAElB7J,OAAA,CAACjB,GAAG;UAAC2M,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACzC,KAAK,EAAE;YAAEJ,KAAK,EAAE;UAAO,CAAE;UAAAgB,QAAA,gBAC9C7J,OAAA,CAAChB,GAAG;YAAC2M,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACzB7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cAACxH,IAAI,EAAC,MAAM;cAACxB,KAAK,EAAC,0BAAM;cAAA+G,QAAA,eACjC7J,OAAA,CAACzB,KAAK;gBACJwN,WAAW,EAAC,4CAAS;gBACrBC,UAAU;gBACVC,YAAY,EAAGC,CAAC,IAAK5G,iBAAiB,CAAE4G,CAAC,CAACC,MAAM,CAAsB5G,KAAK,CAAE;gBAC7E6G,MAAM,eACJpM,OAAA,CAACV,cAAc;kBACb2J,KAAK,EAAE;oBAAElG,KAAK,EAAE,SAAS;oBAAEsJ,MAAM,EAAE;kBAAU,CAAE;kBAC/CxB,OAAO,EAAEA,CAAA,KAAM;oBACb,MAAMtF,KAAK,GAAG5C,UAAU,CAAC2J,aAAa,CAAC,MAAM,CAAC;oBAC9C,IAAI/G,KAAK,EAAED,iBAAiB,CAACC,KAAK,CAAC;kBACrC;gBAAE;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAAC2M,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACzB7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cAACxH,IAAI,EAAC,YAAY;cAACxB,KAAK,EAAC,0BAAM;cAAA+G,QAAA,eACvC7J,OAAA,CAACxB,MAAM;gBAACuN,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAnC,QAAA,EACrChJ,UAAU,CAACsD,GAAG,CAAC6F,QAAQ,iBACtBhK,OAAA,CAACE,MAAM;kBAAmBqF,KAAK,EAAEyE,QAAQ,CAAC3F,EAAG;kBAAAwF,QAAA,EAC1CG,QAAQ,CAAC1F;gBAAI,GADH0F,QAAQ,CAAC3F,EAAE;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAAC2M,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACzB7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cAACxH,IAAI,EAAC,cAAc;cAACxB,KAAK,EAAC,0BAAM;cAAA+G,QAAA,eACzC7J,OAAA,CAACxB,MAAM;gBAACuN,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAnC,QAAA,gBACtC7J,OAAA,CAACE,MAAM;kBAACqF,KAAK,EAAEnF,YAAY,CAACyC,SAAU;kBAAAgH,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjDxJ,OAAA,CAACE,MAAM;kBAACqF,KAAK,EAAEnF,YAAY,CAAC4C,IAAK;kBAAA6G,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CxJ,OAAA,CAACE,MAAM;kBAACqF,KAAK,EAAEnF,YAAY,CAAC6C,MAAO;kBAAA4G,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAAC2M,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACzB7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cAACxH,IAAI,EAAC,QAAQ;cAACxB,KAAK,EAAC,0BAAM;cAAA+G,QAAA,eACnC7J,OAAA,CAACxB,MAAM;gBAACuN,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAnC,QAAA,gBACtC7J,OAAA,CAACE,MAAM;kBAACqF,KAAK,EAAElF,aAAa,CAACwF,MAAO;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDxJ,OAAA,CAACE,MAAM;kBAACqF,KAAK,EAAElF,aAAa,CAACmH,OAAQ;kBAAAqC,QAAA,EAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAAC2M,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACzB7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cAACxH,IAAI,EAAC,QAAQ;cAACxB,KAAK,EAAC,cAAI;cAAA+G,QAAA,eACjC7J,OAAA,CAACzB,KAAK;gBAACwN,WAAW,EAAC,gCAAO;gBAACC,UAAU;cAAA;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAAC2M,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACzB7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cAAAjC,QAAA,eACR7J,OAAA,CAAC1B,KAAK;gBAAAuL,QAAA,gBACJ7J,OAAA,CAAC3B,MAAM;kBACLkK,IAAI,EAAC,SAAS;kBACdgE,QAAQ,EAAC,QAAQ;kBACjB3B,IAAI,eAAE5K,OAAA,CAACV,cAAc;oBAAA+J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBvI,OAAO,EAAEA,OAAQ;kBAAA4I,QAAA,EAClB;gBAED;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxJ,OAAA,CAAC3B,MAAM;kBACLwM,OAAO,EAAE1F,WAAY;kBACrByF,IAAI,eAAE5K,OAAA,CAACL,cAAc;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBgD,QAAQ,EAAEvL,OAAQ;kBAAA4I,QAAA,EACnB;gBAED;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAAC2M,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACzB7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cAAChJ,KAAK,EAAC,0BAAM;cAAA+G,QAAA,eACrB7J,OAAA;gBAAKiJ,KAAK,EAAE;kBAAElG,KAAK,EAAE,MAAM;kBAAEgI,QAAQ,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,EAC7C5I,OAAO,GAAG,QAAQ,GAAG,OAAOE,KAAK;cAAM;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPxJ,OAAA,CAAC7B,IAAI;MAACmN,SAAS,EAAC,aAAa;MAAC7C,IAAI,EAAC,OAAO;MAAAoB,QAAA,eACxC7J,OAAA,CAACjB,GAAG;QAAC0N,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAA7C,QAAA,gBACzC7J,OAAA,CAAChB,GAAG;UAAA6K,QAAA,eACF7J,OAAA,CAAC1B,KAAK;YAAAuL,QAAA,gBACJ7J,OAAA,CAAC3B,MAAM;cACLkK,IAAI,EAAC,SAAS;cACdqC,IAAI,eAAE5K,OAAA,CAACX,YAAY;gBAAAgK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBqB,OAAO,EAAEnF,SAAU;cACnB+C,IAAI,EAAC,QAAQ;cAAAoB,QAAA,EACd;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxJ,OAAA,CAAC3B,MAAM;cACLuM,IAAI,eAAE5K,OAAA,CAACN,cAAc;gBAAA2J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBqB,OAAO,EAAEA,CAAA,KAAMjM,OAAO,CAACsG,IAAI,CAAC,YAAY,CAAE;cAC1CsH,QAAQ,EAAEvL,OAAO,IAAIE,KAAK,KAAK,CAAE;cAAA0I,QAAA,EAClC;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNxJ,OAAA,CAAChB,GAAG;UAAA6K,QAAA,eACF7J,OAAA,CAAC1B,KAAK;YAAAuL,QAAA,gBACJ7J,OAAA;cAAKiJ,KAAK,EAAE;gBAAElG,KAAK,EAAE,MAAM;gBAAEgI,QAAQ,EAAE;cAAO,CAAE;cAAAlB,QAAA,EAC7ClJ,QAAQ,CAACoF,MAAM,GAAG,CAAC,IAClB,OAAO,CAAC1E,WAAW,CAACE,IAAI,GAAG,CAAC,IAAIF,WAAW,CAACG,QAAQ,GAAG,CAAC,IAAImL,IAAI,CAACC,GAAG,CAACvL,WAAW,CAACE,IAAI,GAAGF,WAAW,CAACG,QAAQ,EAAEL,KAAK,CAAC;YACrH;cAAAkI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNxJ,OAAA,CAAC3B,MAAM;cACLuM,IAAI,eAAE5K,OAAA,CAACL,cAAc;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBqB,OAAO,EAAE3H,aAAc;cACvBjC,OAAO,EAAEA,OAAQ;cACjB0H,KAAK,EAAC,0BAAM;cAAAkB,QAAA,EACb;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPxJ,OAAA,CAAC7B,IAAI;MAAA0L,QAAA,eACH7J,OAAA,CAAC5B,KAAK;QACJsK,OAAO,EAAEA,OAAQ;QACjBmE,UAAU,EAAElM,QAAS;QACrBmM,MAAM,EAAC,IAAI;QACX7L,OAAO,EAAEA,OAAQ;QACjB8L,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,MAAM,EAAE;UACNC,SAAS,EAAEjM,OAAO,GAAG,QAAQ,gBAC3BjB,OAAA;YAAKiJ,KAAK,EAAE;cAAEkE,OAAO,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAvD,QAAA,gBACrD7J,OAAA;cAAKiJ,KAAK,EAAE;gBAAE8B,QAAQ,EAAE,MAAM;gBAAEhI,KAAK,EAAE,MAAM;gBAAE+H,YAAY,EAAE;cAAM,CAAE;cAAAjB,QAAA,EAAC;YAEtE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxJ,OAAA;cAAKiJ,KAAK,EAAE;gBAAE8B,QAAQ,EAAE,MAAM;gBAAEhI,KAAK,EAAE;cAAO,CAAE;cAAA8G,QAAA,EAC7CjF,MAAM,CAACC,IAAI,CAACxD,WAAW,CAAC,CAACgM,IAAI,CAACrI,GAAG,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,UAAU,IAAI3D,WAAW,CAAC2D,GAAG,CAA6B,CAAC,GACvH,WAAW,GACX;YAAmB;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAET,CAAE;QACF8D,UAAU,EAAE;UACVC,OAAO,EAAElM,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZqM,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACvM,KAAK,EAAEwM,KAAK,KAAK;YAC3B,IAAIxM,KAAK,KAAK,CAAC,EAAE,OAAO,MAAM;YAC9B,OAAO,KAAKwM,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQxM,KAAK,IAAI;UACnD,CAAC;UACDkJ,QAAQ,EAAEA,CAAC9I,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ,CAAC;UACDoM,gBAAgB,EAAEA,CAACL,OAAO,EAAE9E,IAAI,KAAK;YACnCnH,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI,EAAE,CAAC;cAAE;cACTC,QAAQ,EAAEiH;YACZ,CAAC,CAAC;UACJ,CAAC;UACDoF,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;UAC1CpF,IAAI,EAAE;QACR;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPxJ,OAAA,CAACtB,KAAK;MACJiK,KAAK,eACH3I,OAAA;QAAKiJ,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEmE,GAAG,EAAE;QAAM,CAAE;QAAAjE,QAAA,GAC/DhI,cAAc,gBAAG7B,OAAA,CAACT,YAAY;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGxJ,OAAA,CAACX,YAAY;UAAAgK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDxJ,OAAA;UAAA6J,QAAA,EAAOhI,cAAc,GAAG,UAAUA,cAAc,CAACyC,IAAI,EAAE,GAAG;QAAM;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CACN;MACDuE,IAAI,EAAEtM,cAAe;MACrBuM,QAAQ,EAAEA,CAAA,KAAM;QACd,IAAI7L,MAAM,EAAE;UACVvD,OAAO,CAACkF,OAAO,CAAC,cAAc,CAAC;UAC/B;QACF;QACApC,iBAAiB,CAAC,KAAK,CAAC;QACxBe,IAAI,CAAC2C,WAAW,CAAC,CAAC;QAClBlD,WAAW,CAAC,EAAE,CAAC;QACfJ,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAE;MACFmM,MAAM,EAAE,IAAK;MACbpF,KAAK,EAAE,GAAI;MACXqF,cAAc;MACdC,YAAY,EAAE,CAAChM,MAAO;MACtBiM,QAAQ,EAAE,CAACjM,MAAO;MAAA0H,QAAA,eAElB7J,OAAA,CAACrB,IAAI;QACH8D,IAAI,EAAEA,IAAK;QACX8I,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE/E,UAAW;QACrBgF,YAAY,EAAC,KAAK;QAAA5B,QAAA,gBAElB7J,OAAA,CAACjB,GAAG;UAAC2M,MAAM,EAAE,EAAG;UAAA7B,QAAA,gBACd7J,OAAA,CAAChB,GAAG;YAACqP,IAAI,EAAE,EAAG;YAAAxE,QAAA,eACZ7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cACRxH,IAAI,EAAC,MAAM;cACXxB,KAAK,EAAC,0BAAM;cACZwL,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3P,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEgO,GAAG,EAAE,CAAC;gBAAE4B,GAAG,EAAE,EAAE;gBAAE5P,OAAO,EAAE;cAAiB,CAAC,CAC9C;cAAAiL,QAAA,eAEF7J,OAAA,CAACzB,KAAK;gBACJwN,WAAW,EAAC,4CAAS;gBACrB0C,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAACqP,IAAI,EAAE,EAAG;YAAAxE,QAAA,eACZ7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cACRxH,IAAI,EAAC,YAAY;cACjBxB,KAAK,EAAC,0BAAM;cACZwL,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3P,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiL,QAAA,eAEhD7J,OAAA,CAACxB,MAAM;gBAACuN,WAAW,EAAC,4CAAS;gBAAAlC,QAAA,EAC1BhJ,UAAU,CAACsD,GAAG,CAAC6F,QAAQ,iBACtBhK,OAAA,CAACE,MAAM;kBAAmBqF,KAAK,EAAEyE,QAAQ,CAAC3F,EAAG;kBAAAwF,QAAA,EAC1CG,QAAQ,CAAC1F;gBAAI,GADH0F,QAAQ,CAAC3F,EAAE;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxJ,OAAA,CAACjB,GAAG;UAAC2M,MAAM,EAAE,EAAG;UAAA7B,QAAA,gBACd7J,OAAA,CAAChB,GAAG;YAACqP,IAAI,EAAE,EAAG;YAAAxE,QAAA,eACZ7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cACRxH,IAAI,EAAC,cAAc;cACnBxB,KAAK,EAAC,0BAAM;cACZwL,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3P,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiL,QAAA,eAEhD7J,OAAA,CAACxB,MAAM;gBAACuN,WAAW,EAAC,4CAAS;gBAAAlC,QAAA,gBAC3B7J,OAAA,CAACE,MAAM;kBAACqF,KAAK,EAAEnF,YAAY,CAACyC,SAAU;kBAAAgH,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjDxJ,OAAA,CAACE,MAAM;kBAACqF,KAAK,EAAEnF,YAAY,CAAC4C,IAAK;kBAAA6G,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CxJ,OAAA,CAACE,MAAM;kBAACqF,KAAK,EAAEnF,YAAY,CAAC6C,MAAO;kBAAA4G,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAACqP,IAAI,EAAE,EAAG;YAAAxE,QAAA,eACZ7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cACRxH,IAAI,EAAC,QAAQ;cACbxB,KAAK,EAAC,cAAI;cACVwL,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3P,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAE4P,GAAG,EAAE,EAAE;gBAAE5P,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAAiL,QAAA,eAEF7J,OAAA,CAACzB,KAAK;gBACJwN,WAAW,EAAC,gCAAO;gBACnB0C,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxJ,OAAA,CAACjB,GAAG;UAAC2M,MAAM,EAAE,EAAG;UAAA7B,QAAA,gBACd7J,OAAA,CAAChB,GAAG;YAACqP,IAAI,EAAE,EAAG;YAAAxE,QAAA,eACZ7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cACRxH,IAAI,EAAC,YAAY;cACjBxB,KAAK,EAAC,oBAAK;cACXwL,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3P,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAiL,QAAA,eAE/C7J,OAAA,CAACxB,MAAM;gBAACuN,WAAW,EAAC,sCAAQ;gBAAAlC,QAAA,EACzB9I,SAAS,CAACoD,GAAG,CAACwK,QAAQ,iBACrB3O,OAAA,CAACE,MAAM;kBAAmBqF,KAAK,EAAEoJ,QAAQ,CAACtK,EAAG;kBAAAwF,QAAA,EAC1C8E,QAAQ,CAACrK;gBAAI,GADHqK,QAAQ,CAACtK,EAAE;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxJ,OAAA,CAAChB,GAAG;YAACqP,IAAI,EAAE,EAAG;YAAAxE,QAAA,eACZ7J,OAAA,CAACrB,IAAI,CAACmN,IAAI;cACRxH,IAAI,EAAC,QAAQ;cACbxB,KAAK,EAAC,0BAAM;cACZ8L,aAAa,EAAC,SAAS;cAAA/E,QAAA,eAEvB7J,OAAA,CAACZ,MAAM;gBAACkL,eAAe,EAAC,cAAI;gBAACC,iBAAiB,EAAC;cAAI;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxJ,OAAA,CAACrB,IAAI,CAACmN,IAAI;UACRxH,IAAI,EAAC,aAAa;UAClBxB,KAAK,EAAC,0BAAM;UACZwL,KAAK,EAAE,CACL;YAAEE,GAAG,EAAE,GAAG;YAAE5P,OAAO,EAAE;UAAiB,CAAC,CACvC;UAAAiL,QAAA,eAEF7J,OAAA,CAACG,QAAQ;YACP4L,WAAW,EAAC,4CAAS;YACrB8C,IAAI,EAAE,CAAE;YACRJ,SAAS;YACTC,SAAS,EAAE;UAAI;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZxJ,OAAA,CAACrB,IAAI,CAACmN,IAAI;UACRxH,IAAI,EAAC,QAAQ;UACbxB,KAAK,EAAC,0BAAM;UACZgM,KAAK,EAAC,8HAA+B;UAAAjF,QAAA,eAErC7J,OAAA,CAACf,MAAM;YACL8P,QAAQ,EAAC,cAAc;YACvB9M,QAAQ,EAAEA,QAAS;YACnBoI,QAAQ,EAAElC,kBAAmB;YAC7BE,YAAY,EAAEA,YAAa;YAC3B2G,QAAQ,EAAE,CAAE;YACZC,MAAM,EAAC,sBAAsB;YAAApF,QAAA,EAE5B5H,QAAQ,CAAC8D,MAAM,IAAI,CAAC,GAAG,IAAI,gBAC1B/F,OAAA;cAAA6J,QAAA,gBACE7J,OAAA,CAACJ,cAAc;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClBxJ,OAAA;gBAAKiJ,KAAK,EAAE;kBAAEiG,SAAS,EAAE;gBAAE,CAAE;gBAAArF,QAAA,EAAC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGXnH,SAAS,iBACRrC,OAAA,CAACrB,IAAI,CAACmN,IAAI;UAAAjC,QAAA,eACR7J,OAAA;YAAKiJ,KAAK,EAAE;cACVkE,OAAO,EAAE,WAAW;cACpBgC,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BjG,YAAY,EAAE,KAAK;cACnBpG,KAAK,EAAE,SAAS;cAChBgI,QAAQ,EAAE,MAAM;cAChBsE,UAAU,EAAE;YACd,CAAE;YAAAxF,QAAA,gBACA7J,OAAA;cAAA6J,QAAA,EAAQ;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAACnH,SAAS;UAAA;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACZ,EAGAjH,WAAW,iBACVvC,OAAA,CAACrB,IAAI,CAACmN,IAAI;UAAAjC,QAAA,eACR7J,OAAA;YAAKiJ,KAAK,EAAE;cACVkE,OAAO,EAAE,WAAW;cACpBgC,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BjG,YAAY,EAAE,KAAK;cACnBpG,KAAK,EAAE,SAAS;cAChBgI,QAAQ,EAAE,MAAM;cAChBsE,UAAU,EAAE;YACd,CAAE;YAAAxF,QAAA,gBACA7J,OAAA;cAAA6J,QAAA,EAAQ;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAACjH,WAAW;UAAA;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACZ,eAEDxJ,OAAA,CAACrB,IAAI,CAACmN,IAAI;UAAC7C,KAAK,EAAE;YAAE6B,YAAY,EAAE,CAAC;YAAEoE,SAAS,EAAE;UAAO,CAAE;UAAArF,QAAA,eACvD7J,OAAA;YAAKiJ,KAAK,EAAE;cACVqG,SAAS,EAAE,mBAAmB;cAC9BC,UAAU,EAAE,MAAM;cAClB7F,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE;YACd,CAAE;YAAAE,QAAA,gBACA7J,OAAA;cAAKiJ,KAAK,EAAE;gBAAElG,KAAK,EAAE,MAAM;gBAAEgI,QAAQ,EAAE;cAAO,CAAE;cAAAlB,QAAA,EAC7ChI,cAAc,GAAG,gBAAgB,GAAG;YAAgB;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNxJ,OAAA,CAAC1B,KAAK;cAAAuL,QAAA,gBACJ7J,OAAA,CAAC3B,MAAM;gBACLwM,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAI1I,MAAM,EAAE;oBACVvD,OAAO,CAACkF,OAAO,CAAC,cAAc,CAAC;oBAC/B;kBACF;kBACApC,iBAAiB,CAAC,KAAK,CAAC;kBACxBe,IAAI,CAAC2C,WAAW,CAAC,CAAC;kBAClBlD,WAAW,CAAC,EAAE,CAAC;kBACfJ,iBAAiB,CAAC,IAAI,CAAC;kBACvBQ,YAAY,CAAC,EAAE,CAAC;kBAChBE,cAAc,CAAC,EAAE,CAAC;gBACpB,CAAE;gBACFgK,QAAQ,EAAErK,MAAO;gBACjBsG,IAAI,EAAC,QAAQ;gBAAAoB,QAAA,EACd;cAED;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxJ,OAAA,CAAC3B,MAAM;gBACLkK,IAAI,EAAC,SAAS;gBACdgE,QAAQ,EAAC,QAAQ;gBACjBtL,OAAO,EAAEkB,MAAO;gBAChBqK,QAAQ,EAAErK,MAAO;gBACjBsG,IAAI,EAAC,QAAQ;gBACbmC,IAAI,EAAE/I,cAAc,gBAAG7B,OAAA,CAACT,YAAY;kBAAA8J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxJ,OAAA,CAACX,YAAY;kBAAAgK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAK,QAAA,EAE1D1H,MAAM,GAAG,QAAQ,GAAIN,cAAc,GAAG,MAAM,GAAG;cAAO;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRxJ,OAAA,CAACtB,KAAK;MACJiK,KAAK,eACH3I,OAAA;QAAKiJ,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEmE,GAAG,EAAE;QAAM,CAAE;QAAAjE,QAAA,gBAChE7J,OAAA,CAACP,WAAW;UAAA4J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfxJ,OAAA;UAAA6J,QAAA,GAAM,0BAAI,EAAC9H,cAAc,GAAG,MAAMA,cAAc,CAACuC,IAAI,EAAE,GAAG,EAAE;QAAA;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACN;MACDuE,IAAI,EAAEpM,eAAgB;MACtBqM,QAAQ,EAAEA,CAAA,KAAMpM,kBAAkB,CAAC,KAAK,CAAE;MAC1CqM,MAAM,EAAE,cACNjO,OAAA,CAAC3B,MAAM;QAAYkK,IAAI,EAAC,SAAS;QAACsC,OAAO,EAAEA,CAAA,KAAM;UAC/C,IAAI9I,cAAc,EAAE;YAClBH,kBAAkB,CAAC,KAAK,CAAC;YACzB+D,UAAU,CAAC5D,cAAc,CAAC;UAC5B;QACF,CAAE;QAAA8H,QAAA,EAAC;MAEH,GAPY,MAAM;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOV,CAAC,eACTxJ,OAAA,CAAC3B,MAAM;QAAawM,OAAO,EAAEA,CAAA,KAAMjJ,kBAAkB,CAAC,KAAK,CAAE;QAAAiI,QAAA,EAAC;MAE9D,GAFY,OAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFX,KAAK,EAAE,GAAI;MAAAgB,QAAA,EAEV9H,cAAc,iBACb/B,OAAA,CAACb,YAAY;QAACqQ,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAA5F,QAAA,gBAC/B7J,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,gBAAM;UAAA+G,QAAA,EAAE9H,cAAc,CAACsC;QAAE;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACvExJ,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,0BAAM;UAAA+G,QAAA,EAAE9H,cAAc,CAACuC;QAAI;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACzExJ,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,0BAAM;UAAA+G,QAAA,EAAE,EAAArJ,qBAAA,GAAAuB,cAAc,CAACiI,QAAQ,cAAAxJ,qBAAA,uBAAvBA,qBAAA,CAAyB8D,IAAI,KAAI;QAAK;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAC5FxJ,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,0BAAM;UAAA+G,QAAA,eAC7B7J,OAAA,CAACvB,GAAG;YAACsE,KAAK,GAAAtC,qBAAA,GAAEmC,eAAe,CAACb,cAAc,CAAC2N,YAAY,CAAC,cAAAjP,qBAAA,uBAA5CA,qBAAA,CAA8CsC,KAAM;YAAA8G,QAAA,GAAAnJ,sBAAA,GAC7DkC,eAAe,CAACb,cAAc,CAAC2N,YAAY,CAAC,cAAAhP,sBAAA,uBAA5CA,sBAAA,CAA8CoC;UAAK;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpBxJ,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,cAAI;UAAA+G,QAAA,EAAE9H,cAAc,CAAC4N;QAAM;UAAAtG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACzExJ,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,oBAAK;UAAA+G,QAAA,EAAE9H,cAAc,CAAC6N,YAAY,IAAI;QAAO;UAAAvG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAC3FxJ,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,cAAI;UAAA+G,QAAA,eAC3B7J,OAAA,CAACvB,GAAG;YAACsE,KAAK,EAAEhB,cAAc,CAAC4B,MAAM,KAAKtD,aAAa,CAACwF,MAAM,GAAG,OAAO,GAAG,KAAM;YAAAgE,QAAA,EAC1E9H,cAAc,CAAC4B,MAAM,KAAKtD,aAAa,CAACwF,MAAM,GAAG,IAAI,GAAG;UAAI;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpBxJ,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,0BAAM;UAAA+G,QAAA,EAC5B,IAAIW,IAAI,CAACzI,cAAc,CAAC8N,SAAS,CAAC,CAACpF,cAAc,CAAC;QAAC;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACpBxJ,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,0BAAM;UAACuL,IAAI,EAAE,CAAE;UAAAxE,QAAA,EACrC9H,cAAc,CAAC+N,WAAW,IAAI;QAAM;UAAAzG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,EACnBzH,cAAc,CAAC+D,MAAM,IAAI/D,cAAc,CAAC+D,MAAM,CAACC,MAAM,GAAG,CAAC,iBACxD/F,OAAA,CAACb,YAAY,CAAC2M,IAAI;UAAChJ,KAAK,EAAC,0BAAM;UAACuL,IAAI,EAAE,CAAE;UAAAxE,QAAA,eACtC7J,OAAA,CAAC1B,KAAK;YAACyR,IAAI;YAAAlG,QAAA,EACR9H,cAAc,CAAC+D,MAAM,CAAC3B,GAAG,CAAC,CAAC6L,KAAK,EAAE9J,KAAK,kBACtClG,OAAA,CAACd,KAAK;cAEJ2J,KAAK,EAAE,GAAI;cACXE,MAAM,EAAE,GAAI;cACZC,GAAG,EAAEgH,KAAM;cACX/G,KAAK,EAAE;gBAAEC,SAAS,EAAE,OAAO;gBAAEC,YAAY,EAAE;cAAE;YAAE,GAJ1CjD,KAAK;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CACpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACjJ,EAAA,CA99BID,WAAqB;EAAA,QAkBV3B,IAAI,CAAC+D,OAAO,EACN/D,IAAI,CAAC+D,OAAO;AAAA;AAAAuN,EAAA,GAnB7B3P,WAAqB;AAg+B3B,eAAeA,WAAW;AAAC,IAAA2P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}