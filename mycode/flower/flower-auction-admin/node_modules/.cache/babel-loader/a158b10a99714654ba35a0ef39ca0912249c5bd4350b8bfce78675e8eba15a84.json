{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Input,Select,Tag,Modal,Form,message,Typography,Row,Col,DatePicker,Statistic,Badge}from'antd';import{PlusOutlined,SearchOutlined,EyeOutlined,EditOutlined,DeleteOutlined,PlayCircleOutlined,PauseCircleOutlined,StopOutlined,ReloadOutlined,ExclamationCircleOutlined}from'@ant-design/icons';import{auctionService}from'../../../services/auctionService';import FormMessage from'../../../components/FormMessage';import{useFormMessage,handleApiResponse,handleApiError}from'../../../hooks/useFormMessage';import dayjs from'dayjs';import'./index.css';import{jsxs as _jsxs,jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title}=Typography;const{Option}=Select;const{RangePicker}=DatePicker;// 拍卖会状态枚举\nexport let AuctionStatus=/*#__PURE__*/function(AuctionStatus){AuctionStatus[AuctionStatus[\"DRAFT\"]=1]=\"DRAFT\";// 草稿\nAuctionStatus[AuctionStatus[\"SCHEDULED\"]=2]=\"SCHEDULED\";// 已安排\nAuctionStatus[AuctionStatus[\"ONGOING\"]=3]=\"ONGOING\";// 进行中\nAuctionStatus[AuctionStatus[\"PAUSED\"]=4]=\"PAUSED\";// 已暂停\nAuctionStatus[AuctionStatus[\"COMPLETED\"]=5]=\"COMPLETED\";// 已完成\nAuctionStatus[AuctionStatus[\"CANCELLED\"]=6]=\"CANCELLED\";// 已取消\nreturn AuctionStatus;}({});// 拍卖会数据接口\n// 查询参数接口\nconst AuctionList=()=>{var _auctionStatusMap$vie,_auctionStatusMap$vie2;const[auctions,setAuctions]=useState([]);const[loading,setLoading]=useState(false);const[total,setTotal]=useState(0);const[queryParams,setQueryParams]=useState({page:1,pageSize:10});const[isModalVisible,setIsModalVisible]=useState(false);const[isViewModalVisible,setIsViewModalVisible]=useState(false);const[editingAuction,setEditingAuction]=useState(null);const[viewingAuction,setViewingAuction]=useState(null);const[saving,setSaving]=useState(false);const[statistics,setStatistics]=useState({totalAuctions:0,ongoingAuctions:0,todayAuctions:0,totalParticipants:0});const[auctioneers,setAuctioneers]=useState([]);const[form]=Form.useForm();const[searchForm]=Form.useForm();const{formError,formSuccess,setFormError,setFormSuccess,clearAllMessages}=useFormMessage();// 拍卖会状态映射\nconst auctionStatusMap={[AuctionStatus.DRAFT]:{label:'草稿',color:'default'},[AuctionStatus.SCHEDULED]:{label:'已安排',color:'blue'},[AuctionStatus.ONGOING]:{label:'进行中',color:'green'},[AuctionStatus.PAUSED]:{label:'已暂停',color:'orange'},[AuctionStatus.COMPLETED]:{label:'已完成',color:'purple'},[AuctionStatus.CANCELLED]:{label:'已取消',color:'red'}};// 获取拍卖会列表\nconst fetchAuctions=async()=>{setLoading(true);try{const response=await auctionService.getAuctionList(queryParams);if(response.success){// 将后端的name字段映射为前端的title字段\nconst mappedAuctions=response.data.list.map(auction=>_objectSpread(_objectSpread({},auction),{},{title:auction.name||auction.title,// 后端返回name，前端使用title\ntotalItems:auction.totalItems||0,totalAmount:auction.totalAmount||0,participantCount:auction.participantCount||0,soldItems:auction.soldItems||0,creatorName:auction.creatorName||'未知',// 确保auctioneerId字段被保留\nauctioneerId:auction.auctioneerId}));setAuctions(mappedAuctions);setTotal(response.data.total);console.log('获取到拍卖会列表:',mappedAuctions);}else{message.error(response.message||'获取拍卖会列表失败');setAuctions([]);setTotal(0);}}catch(error){console.error('获取拍卖会列表失败:',error);let errorMsg='获取拍卖会列表失败';if(error.response){const{status}=error.response;if(status===401){errorMsg='登录已过期，请重新登录';}else if(status===403){errorMsg='没有权限访问拍卖会列表';}else if(status===500){errorMsg='服务器内部错误，请稍后重试';}}message.error(errorMsg);setAuctions([]);setTotal(0);}finally{setLoading(false);}};// 获取拍卖会统计\nconst fetchStatistics=async()=>{try{const response=await auctionService.getAuctionStatistics();if(response.success){setStatistics(response.data);}}catch(error){console.error('获取拍卖会统计失败:',error);}};// 获取拍卖师列表\nconst fetchAuctioneers=async()=>{try{// 获取拍卖师类型的用户 (user_type=1)\nconst apiBaseUrl=process.env.REACT_APP_API_BASE_URL||'http://localhost:8081/api/v1';const response=await fetch(\"\".concat(apiBaseUrl,\"/users?user_type=1&page=1&pageSize=100\"));const data=await response.json();// 处理用户API的响应格式：{success: true, data: {list: []}}\nif(data.success&&data.data&&data.data.list){// 过滤出拍卖师用户（userType=1）\nconst auctioneerUsers=data.data.list.filter(user=>user.userType===1);const auctioneerList=auctioneerUsers.map(user=>({id:user.id,name:user.realName||user.username}));setAuctioneers(auctioneerList);console.log('获取到拍卖师列表:',auctioneerList);}else{console.warn('拍卖师数据格式异常:',data);setAuctioneers([]);}}catch(error){console.error('获取拍卖师列表失败:',error);setAuctioneers([]);}};// 初始化加载\nuseEffect(()=>{fetchAuctions();fetchStatistics();fetchAuctioneers();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[queryParams]);// 搜索处理\nconst handleSearch=values=>{setQueryParams(_objectSpread(_objectSpread(_objectSpread({},queryParams),values),{},{page:1}));};// 重置搜索\nconst handleReset=()=>{searchForm.resetFields();setQueryParams({page:1,pageSize:10});};// 新增拍卖会\nconst handleAdd=()=>{setEditingAuction(null);form.resetFields();// 设置默认值，不默认选择拍卖师\nform.setFieldsValue({// 默认时间范围为当前时间后的一天\ntimeRange:[dayjs().add(1,'hour'),dayjs().add(1,'day')]});clearAllMessages();setIsModalVisible(true);};// 查看拍卖会详情\nconst handleView=auction=>{setViewingAuction(auction);setIsViewModalVisible(true);};// 编辑拍卖会\nconst handleEdit=async auction=>{setEditingAuction(auction);try{// 获取拍卖会详情，以获取更完整的信息（包括auctioneerId）\nconst response=await auctionService.getAuctionDetail(auction.id);if(response.success){const auctionDetail=response.data;// 使用详情中的auctioneerId，如果存在的话\nconst auctioneerId=auctionDetail.auctioneerId||auctionDetail.auctioneerID||null;console.log('拍卖会详情:',auctionDetail);console.log('拍卖师ID:',auctioneerId);form.setFieldsValue({title:auction.title,// 前端表单使用title字段\ndescription:auction.description,location:auction.location||'',// 确保location有值\nauctioneerID:auctioneerId,// 设置拍卖师ID，如果没有则为null\ntimeRange:[dayjs(auction.startTime),dayjs(auction.endTime)]});}else{// 如果获取详情失败，使用现有数据\nform.setFieldsValue({title:auction.title,description:auction.description,location:auction.location||'',auctioneerID:auction.auctioneerId||null,// 尝试使用列表中的auctioneerId\ntimeRange:[dayjs(auction.startTime),dayjs(auction.endTime)]});}}catch(error){console.error('获取拍卖会详情失败:',error);// 出错时使用现有数据\nform.setFieldsValue({title:auction.title,description:auction.description,location:auction.location||'',auctioneerID:auction.auctioneerId||null,// 尝试使用列表中的auctioneerId\ntimeRange:[dayjs(auction.startTime),dayjs(auction.endTime)]});}clearAllMessages();setIsModalVisible(true);};// 删除拍卖会\nconst handleDelete=async id=>{try{const response=await auctionService.deleteAuction(id);if(response.success){message.success('删除成功');fetchAuctions();}else{message.error(response.message||'删除失败');}}catch(error){message.error(error.message||'删除失败');}};// 保存拍卖会\nconst handleSave=async values=>{setSaving(true);clearAllMessages();try{const auctionData={name:values.title,// 将title字段映射为name\ndescription:values.description,auctioneerId:values.auctioneerID,// 将前端的auctioneerID映射为后端的auctioneerId\nlocation:values.location,startTime:values.timeRange[0].toISOString(),endTime:values.timeRange[1].toISOString()};let response;if(editingAuction){response=await auctionService.updateAuction(editingAuction.id,auctionData);}else{response=await auctionService.createAuction(auctionData);}const successMsg=editingAuction?'拍卖会信息更新成功！':'拍卖会创建成功！';if(handleApiResponse(response,setFormError,setFormSuccess,successMsg)){// 成功：延迟关闭模态框\nsetTimeout(()=>{setIsModalVisible(false);form.resetFields();setEditingAuction(null);clearAllMessages();fetchAuctions();fetchStatistics();// 刷新统计数据\n},1500);}}catch(error){handleApiError(error,setFormError);}finally{setSaving(false);}};// 开始拍卖\nconst handleStart=async id=>{const auction=auctions.find(a=>a.id===id);if(!auction)return;Modal.confirm({title:'确认开始拍卖',content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u786E\\u5B9A\\u8981\\u5F00\\u59CB\\u62CD\\u5356\\u4F1A \",/*#__PURE__*/_jsxs(\"strong\",{children:[\"\\\"\",auction.title,\"\\\"\"]}),\" \\u5417\\uFF1F\"]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#999',fontSize:'12px'},children:\"\\u5F00\\u59CB\\u540E\\u5C06\\u65E0\\u6CD5\\u4FEE\\u6539\\u62CD\\u5356\\u4FE1\\u606F\\uFF0C\\u8BF7\\u786E\\u4FDD\\u6240\\u6709\\u8BBE\\u7F6E\\u6B63\\u786E\"})]}),okText:'确认开始',cancelText:'取消',okButtonProps:{type:'primary'},onOk:async()=>{try{const response=await auctionService.startAuction(id);if(response.success){message.success({content:\"\\u62CD\\u5356\\u4F1A\\\"\".concat(auction.title,\"\\\"\\u5DF2\\u6210\\u529F\\u5F00\\u59CB\\uFF01\"),duration:3});fetchAuctions();}else{message.error({content:response.message||'开始拍卖失败，请稍后重试',duration:5});}}catch(error){var _error$response,_error$response2;console.error('开始拍卖失败:',error);let errorMsg='开始拍卖失败';if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===400){errorMsg='拍卖会状态不允许开始，请检查拍卖设置';}else if(((_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status)===409){errorMsg='拍卖会已经开始或存在冲突';}else{errorMsg=error.message||'网络错误，请检查连接后重试';}message.error({content:errorMsg,duration:5});}}});};// 暂停拍卖\nconst handlePause=async id=>{const auction=auctions.find(a=>a.id===id);if(!auction)return;Modal.confirm({title:'确认暂停拍卖',content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u786E\\u5B9A\\u8981\\u6682\\u505C\\u62CD\\u5356\\u4F1A \",/*#__PURE__*/_jsxs(\"strong\",{children:[\"\\\"\",auction.title,\"\\\"\"]}),\" \\u5417\\uFF1F\"]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#999',fontSize:'12px'},children:\"\\u6682\\u505C\\u540E\\u53EF\\u4EE5\\u91CD\\u65B0\\u5F00\\u59CB\\uFF0C\\u4F46\\u4F1A\\u5F71\\u54CD\\u53C2\\u4E0E\\u8005\\u7684\\u7ADE\\u62CD\\u4F53\\u9A8C\"})]}),okText:'确认暂停',cancelText:'取消',okButtonProps:{type:'default'},onOk:async()=>{try{const response=await auctionService.pauseAuction(id);if(response.success){message.success({content:\"\\u62CD\\u5356\\u4F1A\\\"\".concat(auction.title,\"\\\"\\u5DF2\\u6682\\u505C\"),duration:3});fetchAuctions();}else{message.error({content:response.message||'暂停拍卖失败，请稍后重试',duration:5});}}catch(error){console.error('暂停拍卖失败:',error);message.error({content:error.message||'暂停拍卖失败，请检查网络连接后重试',duration:5});}}});};// 结束拍卖\nconst handleEnd=async id=>{const auction=auctions.find(a=>a.id===id);if(!auction)return;Modal.confirm({title:'确认结束拍卖',icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{style:{color:'#ff4d4f'}}),content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u786E\\u5B9A\\u8981\\u7ED3\\u675F\\u62CD\\u5356\\u4F1A \",/*#__PURE__*/_jsxs(\"strong\",{children:[\"\\\"\",auction.title,\"\\\"\"]}),\" \\u5417\\uFF1F\"]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#ff4d4f',fontSize:'12px',fontWeight:'bold'},children:\"\\u26A0\\uFE0F \\u8B66\\u544A\\uFF1A\\u7ED3\\u675F\\u540E\\u5C06\\u65E0\\u6CD5\\u6062\\u590D\\uFF0C\\u8BF7\\u786E\\u4FDD\\u6240\\u6709\\u4EA4\\u6613\\u5DF2\\u5B8C\\u6210\"})]}),okText:'确认结束',cancelText:'取消',okButtonProps:{danger:true},onOk:async()=>{try{const response=await auctionService.endAuction(id);if(response.success){message.success({content:\"\\u62CD\\u5356\\u4F1A\\\"\".concat(auction.title,\"\\\"\\u5DF2\\u6210\\u529F\\u7ED3\\u675F\\uFF01\"),duration:3});fetchAuctions();}else{message.error({content:response.message||'结束拍卖失败，请稍后重试',duration:5});}}catch(error){console.error('结束拍卖失败:',error);message.error({content:error.message||'结束拍卖失败，请检查网络连接后重试',duration:5});}}});};// 表格列定义\nconst columns=[{title:'ID',dataIndex:'id',key:'id',width:80},{title:'拍卖会标题',dataIndex:'title',key:'title',width:200,render:text=>/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:500},children:text})},{title:'拍卖状态',dataIndex:'status',key:'status',width:100,render:status=>{const statusInfo=auctionStatusMap[status];return/*#__PURE__*/_jsx(Badge,{status:status===AuctionStatus.ONGOING?'processing':status===AuctionStatus.COMPLETED?'success':status===AuctionStatus.CANCELLED?'error':'default',text:/*#__PURE__*/_jsx(Tag,{color:(statusInfo===null||statusInfo===void 0?void 0:statusInfo.color)||'default',children:(statusInfo===null||statusInfo===void 0?void 0:statusInfo.label)||'未知'})});}},{title:'商品数量',dataIndex:'totalItems',key:'totalItems',width:100,render:(total,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{children:[total,\"\\u4EF6\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:12,color:'#999'},children:[\"\\u5DF2\\u552E: \",record.soldItems]})]})},{title:'成交金额',dataIndex:'totalAmount',key:'totalAmount',width:120,render:amount=>/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:500,color:'#f50'},children:[\"\\xA5\",amount.toFixed(2)]})},{title:'参与人数',dataIndex:'participantCount',key:'participantCount',width:100},{title:'创建人',dataIndex:'creatorName',key:'creatorName',width:100},{title:'拍卖时间',dataIndex:'startTime',key:'startTime',width:160,render:(text,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:new Date(text).toLocaleString()}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:12,color:'#999'},children:[\"\\u81F3 \",new Date(record.endTime).toLocaleString()]})]})},{title:'操作',key:'action',width:200,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleView(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record),children:\"\\u7F16\\u8F91\"}),record.status===AuctionStatus.SCHEDULED&&/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),onClick:()=>handleStart(record.id),children:\"\\u5F00\\u59CB\"}),record.status===AuctionStatus.ONGOING&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(PauseCircleOutlined,{}),onClick:()=>handlePause(record.id),children:\"\\u6682\\u505C\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(StopOutlined,{}),onClick:()=>handleEnd(record.id),children:\"\\u7ED3\\u675F\"})]}),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>handleDelete(record.id),children:\"\\u5220\\u9664\"})]})}];return/*#__PURE__*/_jsxs(\"div\",{className:\"auction-list-container\",children:[/*#__PURE__*/_jsx(Title,{level:2,children:\"\\u62CD\\u5356\\u7BA1\\u7406\"}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u62CD\\u5356\\u4F1A\",value:statistics.totalAuctions,valueStyle:{color:'#3f8600'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8FDB\\u884C\\u4E2D\",value:statistics.ongoingAuctions,valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u4ECA\\u65E5\\u62CD\\u5356\",value:statistics.todayAuctions,valueStyle:{color:'#cf1322'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u53C2\\u4E0E\\u4EBA\\u6570\",value:statistics.totalParticipants,valueStyle:{color:'#722ed1'}})})})]}),/*#__PURE__*/_jsx(Card,{className:\"search-card\",size:\"small\",children:/*#__PURE__*/_jsx(Form,{form:searchForm,layout:\"inline\",onFinish:handleSearch,autoComplete:\"off\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{width:'100%'},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Form.Item,{name:\"title\",label:\"\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",allowClear:true})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Form.Item,{name:\"status\",label:\"\\u62CD\\u5356\\u72B6\\u6001\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u72B6\\u6001\",allowClear:true,children:[/*#__PURE__*/_jsx(Option,{value:AuctionStatus.DRAFT,children:\"\\u8349\\u7A3F\"}),/*#__PURE__*/_jsx(Option,{value:AuctionStatus.SCHEDULED,children:\"\\u5DF2\\u5B89\\u6392\"}),/*#__PURE__*/_jsx(Option,{value:AuctionStatus.ONGOING,children:\"\\u8FDB\\u884C\\u4E2D\"}),/*#__PURE__*/_jsx(Option,{value:AuctionStatus.PAUSED,children:\"\\u5DF2\\u6682\\u505C\"}),/*#__PURE__*/_jsx(Option,{value:AuctionStatus.COMPLETED,children:\"\\u5DF2\\u5B8C\\u6210\"}),/*#__PURE__*/_jsx(Option,{value:AuctionStatus.CANCELLED,children:\"\\u5DF2\\u53D6\\u6D88\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Form.Item,{name:\"creatorName\",label:\"\\u521B\\u5EFA\\u4EBA\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u521B\\u5EFA\\u4EBA\\u59D3\\u540D\",allowClear:true})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Form.Item,{name:\"dateRange\",label:\"\\u62CD\\u5356\\u65F6\\u95F4\",children:/*#__PURE__*/_jsx(RangePicker,{style:{width:'100%'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:4,children:/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",icon:/*#__PURE__*/_jsx(SearchOutlined,{}),children:\"\\u641C\\u7D22\"}),/*#__PURE__*/_jsx(Button,{onClick:handleReset,icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),children:\"\\u91CD\\u7F6E\"})]})})})]})})}),/*#__PURE__*/_jsx(Card,{className:\"action-card\",size:\"small\",children:/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAdd,children:\"\\u65B0\\u589E\\u62CD\\u5356\\u4F1A\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:fetchAuctions,loading:loading,children:\"\\u5237\\u65B0\"})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:auctions,rowKey:\"id\",loading:loading,scroll:{x:1400},pagination:{current:queryParams.page,pageSize:queryParams.pageSize,total:total,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\"),onChange:(page,pageSize)=>{setQueryParams(_objectSpread(_objectSpread({},queryParams),{},{page,pageSize:pageSize||10}));}}})}),/*#__PURE__*/_jsx(Modal,{title:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'8px'},children:[editingAuction?/*#__PURE__*/_jsx(EditOutlined,{}):/*#__PURE__*/_jsx(PlusOutlined,{}),/*#__PURE__*/_jsx(\"span\",{children:editingAuction?\"\\u7F16\\u8F91\\u62CD\\u5356\\u4F1A - \".concat(editingAuction.title):'新增拍卖会'})]}),open:isModalVisible,onCancel:()=>{if(saving){message.warning('正在保存中，请稍候...');return;}setIsModalVisible(false);form.resetFields();setEditingAuction(null);},footer:null,width:700,destroyOnClose:true,maskClosable:!saving,closable:!saving,children:/*#__PURE__*/_jsxs(Form,{form:form,layout:\"vertical\",onFinish:handleSave,autoComplete:\"off\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"title\",label:\"\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",rules:[{required:true,message:'请输入拍卖会标题'},{min:2,max:100,message:'标题长度为2-100个字符'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",showCount:true,maxLength:100})}),/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u62CD\\u5356\\u4F1A\\u63CF\\u8FF0\",rules:[{max:500,message:'描述不能超过500个字符'}],children:/*#__PURE__*/_jsx(Input.TextArea,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u63CF\\u8FF0\",rows:4,showCount:true,maxLength:500})}),/*#__PURE__*/_jsx(Form.Item,{name:\"timeRange\",label:\"\\u62CD\\u5356\\u65F6\\u95F4\",rules:[{required:true,message:'请选择拍卖时间'},{validator:(_,value)=>{if(!value||!value[0]||!value[1]){return Promise.resolve();}const[start,end]=value;const now=new Date();// 检查开始时间不能早于当前时间\nif(start.isBefore(now)){return Promise.reject(new Error('开始时间不能早于当前时间'));}// 检查结束时间必须晚于开始时间\nif(end.isBefore(start)){return Promise.reject(new Error('结束时间必须晚于开始时间'));}// 检查拍卖时长不能少于30分钟\nconst duration=end.diff(start,'minutes');if(duration<30){return Promise.reject(new Error('拍卖时长不能少于30分钟'));}// 检查拍卖时长不能超过24小时\nif(duration>24*60){return Promise.reject(new Error('拍卖时长不能超过24小时'));}return Promise.resolve();}}],extra:\"\\u62CD\\u5356\\u65F6\\u957F\\u5EFA\\u8BAE\\u572830\\u5206\\u949F\\u523024\\u5C0F\\u65F6\\u4E4B\\u95F4\",children:/*#__PURE__*/_jsx(RangePicker,{showTime:{format:'HH:mm',minuteStep:15// 15分钟间隔\n},format:\"YYYY-MM-DD HH:mm\",style:{width:'100%'},placeholder:['选择开始时间','选择结束时间'],disabledDate:current=>{// 禁用今天之前的日期\nreturn current&&current.isBefore(new Date(),'day');},disabledTime:(current,type)=>{const now=new Date();const isToday=current&&current.isSame(now,'day');if(type==='start'&&isToday){// 如果是今天，禁用当前时间之前的时间\nreturn{disabledHours:()=>{const hours=[];for(let i=0;i<now.getHours();i++){hours.push(i);}return hours;},disabledMinutes:selectedHour=>{if(selectedHour===now.getHours()){const minutes=[];for(let i=0;i<now.getMinutes();i++){minutes.push(i);}return minutes;}return[];}};}return{};},showNow:false,allowClear:false})}),/*#__PURE__*/_jsx(Form.Item,{name:\"location\",label:\"\\u62CD\\u5356\\u5730\\u70B9\",rules:[{required:true,message:'请输入拍卖地点'},{min:2,max:200,message:'地点长度为2-200个字符'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u5730\\u70B9\",showCount:true,maxLength:200})}),/*#__PURE__*/_jsx(Form.Item,{name:\"auctioneerID\",label:\"\\u62CD\\u5356\\u5E08\",rules:[{required:true,message:'请选择拍卖师'}],children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u5E08\",showSearch:true,optionFilterProp:\"children\",filterOption:(input,option)=>{var _option$children;return String((_option$children=option===null||option===void 0?void 0:option.children)!==null&&_option$children!==void 0?_option$children:'').toLowerCase().includes(input.toLowerCase());},children:auctioneers.map(auctioneer=>/*#__PURE__*/_jsx(Option,{value:auctioneer.id,children:auctioneer.name},auctioneer.id))})}),/*#__PURE__*/_jsx(FormMessage,{type:\"error\",message:formError,visible:!!formError}),/*#__PURE__*/_jsx(FormMessage,{type:\"success\",message:formSuccess,visible:!!formSuccess}),/*#__PURE__*/_jsx(Form.Item,{style:{marginBottom:0,marginTop:'24px'},children:/*#__PURE__*/_jsxs(\"div\",{style:{borderTop:'1px solid #f0f0f0',paddingTop:'16px',display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#666',fontSize:'12px'},children:editingAuction?'* 修改后请点击更新按钮保存':'* 请填写完整信息后创建拍卖会'}),/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>{if(saving){message.warning('正在保存中，请稍候...');return;}setIsModalVisible(false);form.resetFields();setEditingAuction(null);clearAllMessages();},disabled:saving,size:\"middle\",children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:saving,disabled:saving,size:\"middle\",icon:editingAuction?/*#__PURE__*/_jsx(EditOutlined,{}):/*#__PURE__*/_jsx(PlusOutlined,{}),children:saving?'保存中...':editingAuction?'更新拍卖会':'创建拍卖会'})]})]})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u62CD\\u5356\\u4F1A\\u8BE6\\u60C5\",open:isViewModalVisible,onCancel:()=>{setIsViewModalVisible(false);setViewingAuction(null);},footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>{setIsViewModalVisible(false);setViewingAuction(null);},children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:viewingAuction&&/*#__PURE__*/_jsx(\"div\",{style:{padding:'16px 0'},children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u62CD\\u5356\\u4F1A\\u6807\\u9898\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:viewingAuction.title})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u62CD\\u5356\\u72B6\\u6001\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:/*#__PURE__*/_jsx(Tag,{color:((_auctionStatusMap$vie=auctionStatusMap[viewingAuction.status])===null||_auctionStatusMap$vie===void 0?void 0:_auctionStatusMap$vie.color)||'default',children:((_auctionStatusMap$vie2=auctionStatusMap[viewingAuction.status])===null||_auctionStatusMap$vie2===void 0?void 0:_auctionStatusMap$vie2.label)||'未知'})})]})}),/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u62CD\\u5356\\u63CF\\u8FF0\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4,padding:'8px',backgroundColor:'#f5f5f5',borderRadius:'4px'},children:viewingAuction.description||'暂无描述'})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u62CD\\u5356\\u5730\\u70B9\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:viewingAuction.location||'暂无地点信息'})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u521B\\u5EFA\\u4EBA\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:viewingAuction.creatorName})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5F00\\u59CB\\u65F6\\u95F4\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:new Date(viewingAuction.startTime).toLocaleString()})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u7ED3\\u675F\\u65F6\\u95F4\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:4},children:new Date(viewingAuction.endTime).toLocaleString()})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5546\\u54C1\\u603B\\u6570\\uFF1A\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:4,fontSize:'18px',color:'#1890ff'},children:[viewingAuction.totalItems,\"\\u4EF6\"]})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5DF2\\u552E\\u5546\\u54C1\\uFF1A\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:4,fontSize:'18px',color:'#52c41a'},children:[viewingAuction.soldItems,\"\\u4EF6\"]})]})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u53C2\\u4E0E\\u4EBA\\u6570\\uFF1A\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:4,fontSize:'18px',color:'#722ed1'},children:[viewingAuction.participantCount,\"\\u4EBA\"]})]})}),/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6210\\u4EA4\\u91D1\\u989D\\uFF1A\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:4,fontSize:'24px',color:'#f50',fontWeight:'bold'},children:[\"\\xA5\",viewingAuction.totalAmount.toFixed(2)]})]})})]})})})]});};export default AuctionList;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Typography", "Row", "Col", "DatePicker", "Statistic", "Badge", "PlusOutlined", "SearchOutlined", "EyeOutlined", "EditOutlined", "DeleteOutlined", "PlayCircleOutlined", "PauseCircleOutlined", "StopOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "auctionService", "FormMessage", "useFormMessage", "handleApiResponse", "handleApiError", "dayjs", "jsxs", "_jsxs", "jsx", "_jsx", "Fragment", "_Fragment", "Title", "Option", "RangePicker", "AuctionStatus", "AuctionList", "_auctionStatusMap$vie", "_auctionStatusMap$vie2", "auctions", "setAuctions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "isViewModalVisible", "setIsViewModalVisible", "editingAuction", "setEditingAuction", "viewingAuction", "setViewingAuction", "saving", "setSaving", "statistics", "setStatistics", "totalAuctions", "ongoingAuctions", "todayAuctions", "totalParticipants", "auctioneers", "setAuctioneers", "form", "useForm", "searchForm", "formError", "formSuccess", "setFormError", "setFormSuccess", "clearAllMessages", "auctionStatusMap", "DRAFT", "label", "color", "SCHEDULED", "ONGOING", "PAUSED", "COMPLETED", "CANCELLED", "fetchAuctions", "response", "getAuctionList", "success", "mappedAuctions", "data", "list", "map", "auction", "_objectSpread", "title", "name", "totalItems", "totalAmount", "participantCount", "soldItems", "<PERSON><PERSON><PERSON>", "auctioneerId", "console", "log", "error", "errorMsg", "status", "fetchStatistics", "getAuctionStatistics", "fetchAuctioneers", "apiBaseUrl", "process", "env", "REACT_APP_API_BASE_URL", "fetch", "concat", "json", "auctioneerUsers", "filter", "user", "userType", "auctioneerList", "id", "realName", "username", "warn", "handleSearch", "values", "handleReset", "resetFields", "handleAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeRange", "add", "handleView", "handleEdit", "getAuctionDetail", "auctionDetail", "auctioneerID", "description", "location", "startTime", "endTime", "handleDelete", "deleteAuction", "handleSave", "auctionData", "toISOString", "updateAuction", "createAuction", "successMsg", "setTimeout", "handleStart", "find", "a", "confirm", "content", "children", "style", "fontSize", "okText", "cancelText", "okButtonProps", "type", "onOk", "startAuction", "duration", "_error$response", "_error$response2", "handlePause", "pauseAuction", "handleEnd", "icon", "fontWeight", "danger", "endAuction", "columns", "dataIndex", "key", "width", "render", "text", "statusInfo", "record", "amount", "toFixed", "Date", "toLocaleString", "fixed", "_", "size", "onClick", "className", "level", "gutter", "marginBottom", "xs", "sm", "md", "value", "valueStyle", "layout", "onFinish", "autoComplete", "<PERSON><PERSON>", "placeholder", "allowClear", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "display", "alignItems", "gap", "open", "onCancel", "warning", "footer", "destroyOnClose", "maskClosable", "closable", "rules", "required", "min", "max", "showCount", "max<PERSON><PERSON><PERSON>", "TextArea", "rows", "validator", "Promise", "resolve", "start", "end", "now", "isBefore", "reject", "Error", "diff", "extra", "showTime", "format", "minuteStep", "disabledDate", "disabledTime", "isToday", "isSame", "disabledHours", "hours", "i", "getHours", "push", "disabledMinutes", "selected<PERSON>our", "minutes", "getMinutes", "showNow", "showSearch", "optionFilterProp", "filterOption", "input", "option", "_option$children", "String", "toLowerCase", "includes", "auctioneer", "visible", "marginTop", "borderTop", "paddingTop", "justifyContent", "disabled", "padding", "span", "backgroundColor", "borderRadius"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Typography,\n  Row,\n  Col,\n  DatePicker,\n  Statistic,\n  Badge,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  ReloadOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { auctionService } from '../../../services/auctionService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';\nimport dayjs from 'dayjs';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\n// 拍卖会状态枚举\nexport enum AuctionStatus {\n  DRAFT = 1,        // 草稿\n  SCHEDULED = 2,    // 已安排\n  ONGOING = 3,      // 进行中\n  PAUSED = 4,       // 已暂停\n  COMPLETED = 5,    // 已完成\n  CANCELLED = 6,    // 已取消\n}\n\n// 拍卖会数据接口\nexport interface Auction {\n  id: number;\n  title: string;\n  description?: string;\n  startTime: string;\n  endTime: string;\n  status: AuctionStatus;\n  totalItems: number;\n  soldItems: number;\n  totalAmount: number;\n  participantCount: number;\n  creatorName: string;\n  location: string;\n  createdAt: string;\n  updatedAt: string;\n  auctioneerID?: number; // 添加可选的auctioneerID字段\n  auctioneerId?: number; // 添加后端实际返回的auctioneerId字段\n}\n\n// 查询参数接口\ninterface AuctionQueryParams {\n  title?: string;\n  status?: AuctionStatus;\n  creatorName?: string;\n  dateRange?: [string, string];\n  page: number;\n  pageSize: number;\n}\n\nconst AuctionList: React.FC = () => {\n  const [auctions, setAuctions] = useState<Auction[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<AuctionQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isViewModalVisible, setIsViewModalVisible] = useState(false);\n  const [editingAuction, setEditingAuction] = useState<Auction | null>(null);\n  const [viewingAuction, setViewingAuction] = useState<Auction | null>(null);\n  const [saving, setSaving] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalAuctions: 0,\n    ongoingAuctions: 0,\n    todayAuctions: 0,\n    totalParticipants: 0,\n  });\n  const [auctioneers, setAuctioneers] = useState<{id: number, name: string}[]>([]);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 拍卖会状态映射\n  const auctionStatusMap = {\n    [AuctionStatus.DRAFT]: { label: '草稿', color: 'default' },\n    [AuctionStatus.SCHEDULED]: { label: '已安排', color: 'blue' },\n    [AuctionStatus.ONGOING]: { label: '进行中', color: 'green' },\n    [AuctionStatus.PAUSED]: { label: '已暂停', color: 'orange' },\n    [AuctionStatus.COMPLETED]: { label: '已完成', color: 'purple' },\n    [AuctionStatus.CANCELLED]: { label: '已取消', color: 'red' },\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    setLoading(true);\n    try {\n      const response = await auctionService.getAuctionList(queryParams);\n      if (response.success) {\n        // 将后端的name字段映射为前端的title字段\n        const mappedAuctions = response.data.list.map((auction: any) => ({\n          ...auction,\n          title: auction.name || auction.title, // 后端返回name，前端使用title\n          totalItems: auction.totalItems || 0,\n          totalAmount: auction.totalAmount || 0,\n          participantCount: auction.participantCount || 0,\n          soldItems: auction.soldItems || 0,\n          creatorName: auction.creatorName || '未知',\n          // 确保auctioneerId字段被保留\n          auctioneerId: auction.auctioneerId,\n        }));\n        setAuctions(mappedAuctions);\n        setTotal(response.data.total);\n        console.log('获取到拍卖会列表:', mappedAuctions);\n      } else {\n        message.error(response.message || '获取拍卖会列表失败');\n        setAuctions([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会列表失败:', error);\n      let errorMsg = '获取拍卖会列表失败';\n      if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问拍卖会列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setAuctions([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取拍卖会统计\n  const fetchStatistics = async () => {\n    try {\n      const response = await auctionService.getAuctionStatistics();\n      if (response.success) {\n        setStatistics(response.data);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会统计失败:', error);\n    }\n  };\n\n  // 获取拍卖师列表\n  const fetchAuctioneers = async () => {\n    try {\n      // 获取拍卖师类型的用户 (user_type=1)\n      const apiBaseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1';\n      const response = await fetch(`${apiBaseUrl}/users?user_type=1&page=1&pageSize=100`);\n      const data = await response.json();\n\n      // 处理用户API的响应格式：{success: true, data: {list: []}}\n      if (data.success && data.data && data.data.list) {\n        // 过滤出拍卖师用户（userType=1）\n        const auctioneerUsers = data.data.list.filter((user: any) => user.userType === 1);\n        const auctioneerList = auctioneerUsers.map((user: any) => ({\n          id: user.id,\n          name: user.realName || user.username\n        }));\n        setAuctioneers(auctioneerList);\n        console.log('获取到拍卖师列表:', auctioneerList);\n      } else {\n        console.warn('拍卖师数据格式异常:', data);\n        setAuctioneers([]);\n      }\n    } catch (error) {\n      console.error('获取拍卖师列表失败:', error);\n      setAuctioneers([]);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchAuctions();\n    fetchStatistics();\n    fetchAuctioneers();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增拍卖会\n  const handleAdd = () => {\n    setEditingAuction(null);\n    form.resetFields();\n    \n    // 设置默认值，不默认选择拍卖师\n    form.setFieldsValue({\n      // 默认时间范围为当前时间后的一天\n      timeRange: [dayjs().add(1, 'hour'), dayjs().add(1, 'day')],\n    });\n    \n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 查看拍卖会详情\n  const handleView = (auction: Auction) => {\n    setViewingAuction(auction);\n    setIsViewModalVisible(true);\n  };\n\n  // 编辑拍卖会\n  const handleEdit = async (auction: Auction) => {\n    setEditingAuction(auction);\n    try {\n      // 获取拍卖会详情，以获取更完整的信息（包括auctioneerId）\n      const response = await auctionService.getAuctionDetail(auction.id);\n      if (response.success) {\n        const auctionDetail = response.data as Auction & { auctioneerID?: number, auctioneerId?: number };\n        // 使用详情中的auctioneerId，如果存在的话\n        const auctioneerId = auctionDetail.auctioneerId || auctionDetail.auctioneerID || null;\n        \n        console.log('拍卖会详情:', auctionDetail);\n        console.log('拍卖师ID:', auctioneerId);\n        \n    form.setFieldsValue({\n      title: auction.title, // 前端表单使用title字段\n      description: auction.description,\n          location: auction.location || '', // 确保location有值\n          auctioneerID: auctioneerId, // 设置拍卖师ID，如果没有则为null\n      timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],\n    });\n      } else {\n        // 如果获取详情失败，使用现有数据\n        form.setFieldsValue({\n          title: auction.title,\n          description: auction.description,\n          location: auction.location || '',\n          auctioneerID: auction.auctioneerId || null, // 尝试使用列表中的auctioneerId\n          timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],\n        });\n      }\n    } catch (error) {\n      console.error('获取拍卖会详情失败:', error);\n      // 出错时使用现有数据\n      form.setFieldsValue({\n        title: auction.title,\n        description: auction.description,\n        location: auction.location || '',\n        auctioneerID: auction.auctioneerId || null, // 尝试使用列表中的auctioneerId\n        timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],\n      });\n    }\n    \n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖会\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await auctionService.deleteAuction(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖会\n  const handleSave = async (values: any) => {\n    setSaving(true);\n    clearAllMessages();\n\n    try {\n      const auctionData = {\n        name: values.title, // 将title字段映射为name\n        description: values.description,\n        auctioneerId: values.auctioneerID, // 将前端的auctioneerID映射为后端的auctioneerId\n        location: values.location,\n        startTime: values.timeRange[0].toISOString(),\n        endTime: values.timeRange[1].toISOString(),\n      };\n\n      let response;\n      if (editingAuction) {\n        response = await auctionService.updateAuction(editingAuction.id, auctionData);\n      } else {\n        response = await auctionService.createAuction(auctionData);\n      }\n\n      const successMsg = editingAuction ? '拍卖会信息更新成功！' : '拍卖会创建成功！';\n\n      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingAuction(null);\n          clearAllMessages();\n          fetchAuctions();\n          fetchStatistics(); // 刷新统计数据\n        }, 1500);\n      }\n    } catch (error: any) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 开始拍卖\n  const handleStart = async (id: number) => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n\n    Modal.confirm({\n      title: '确认开始拍卖',\n      content: (\n        <div>\n          <p>确定要开始拍卖会 <strong>\"{auction.title}\"</strong> 吗？</p>\n          <p style={{ color: '#999', fontSize: '12px' }}>\n            开始后将无法修改拍卖信息，请确保所有设置正确\n          </p>\n        </div>\n      ),\n      okText: '确认开始',\n      cancelText: '取消',\n      okButtonProps: { type: 'primary' },\n      onOk: async () => {\n        try {\n          const response = await auctionService.startAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已成功开始！`,\n              duration: 3,\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '开始拍卖失败，请稍后重试',\n              duration: 5,\n            });\n          }\n        } catch (error: any) {\n          console.error('开始拍卖失败:', error);\n          let errorMsg = '开始拍卖失败';\n          if (error.response?.status === 400) {\n            errorMsg = '拍卖会状态不允许开始，请检查拍卖设置';\n          } else if (error.response?.status === 409) {\n            errorMsg = '拍卖会已经开始或存在冲突';\n          } else {\n            errorMsg = error.message || '网络错误，请检查连接后重试';\n          }\n          message.error({\n            content: errorMsg,\n            duration: 5,\n          });\n        }\n      },\n    });\n  };\n\n  // 暂停拍卖\n  const handlePause = async (id: number) => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n\n    Modal.confirm({\n      title: '确认暂停拍卖',\n      content: (\n        <div>\n          <p>确定要暂停拍卖会 <strong>\"{auction.title}\"</strong> 吗？</p>\n          <p style={{ color: '#999', fontSize: '12px' }}>\n            暂停后可以重新开始，但会影响参与者的竞拍体验\n          </p>\n        </div>\n      ),\n      okText: '确认暂停',\n      cancelText: '取消',\n      okButtonProps: { type: 'default' },\n      onOk: async () => {\n        try {\n          const response = await auctionService.pauseAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已暂停`,\n              duration: 3,\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '暂停拍卖失败，请稍后重试',\n              duration: 5,\n            });\n          }\n        } catch (error: any) {\n          console.error('暂停拍卖失败:', error);\n          message.error({\n            content: error.message || '暂停拍卖失败，请检查网络连接后重试',\n            duration: 5,\n          });\n        }\n      },\n    });\n  };\n\n  // 结束拍卖\n  const handleEnd = async (id: number) => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n\n    Modal.confirm({\n      title: '确认结束拍卖',\n      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,\n      content: (\n        <div>\n          <p>确定要结束拍卖会 <strong>\"{auction.title}\"</strong> 吗？</p>\n          <p style={{ color: '#ff4d4f', fontSize: '12px', fontWeight: 'bold' }}>\n            ⚠️ 警告：结束后将无法恢复，请确保所有交易已完成\n          </p>\n        </div>\n      ),\n      okText: '确认结束',\n      cancelText: '取消',\n      okButtonProps: { danger: true },\n      onOk: async () => {\n        try {\n          const response = await auctionService.endAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已成功结束！`,\n              duration: 3,\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '结束拍卖失败，请稍后重试',\n              duration: 5,\n            });\n          }\n        } catch (error: any) {\n          console.error('结束拍卖失败:', error);\n          message.error({\n            content: error.message || '结束拍卖失败，请检查网络连接后重试',\n            duration: 5,\n          });\n        }\n      },\n    });\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Auction> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '拍卖会标题',\n      dataIndex: 'title',\n      key: 'title',\n      width: 200,\n      render: (text: string) => (\n        <div style={{ fontWeight: 500 }}>{text}</div>\n      ),\n    },\n    {\n      title: '拍卖状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: AuctionStatus) => {\n        const statusInfo = auctionStatusMap[status];\n        return (\n          <Badge\n            status={\n              status === AuctionStatus.ONGOING ? 'processing' :\n              status === AuctionStatus.COMPLETED ? 'success' :\n              status === AuctionStatus.CANCELLED ? 'error' : 'default'\n            }\n            text={\n              <Tag color={statusInfo?.color || 'default'}>\n                {statusInfo?.label || '未知'}\n              </Tag>\n            }\n          />\n        );\n      },\n    },\n    {\n      title: '商品数量',\n      dataIndex: 'totalItems',\n      key: 'totalItems',\n      width: 100,\n      render: (total: number, record: Auction) => (\n        <div>\n          <div>{total}件</div>\n          <div style={{ fontSize: 12, color: '#999' }}>\n            已售: {record.soldItems}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '成交金额',\n      dataIndex: 'totalAmount',\n      key: 'totalAmount',\n      width: 120,\n      render: (amount: number) => (\n        <div style={{ fontWeight: 500, color: '#f50' }}>\n          ¥{amount.toFixed(2)}\n        </div>\n      ),\n    },\n    {\n      title: '参与人数',\n      dataIndex: 'participantCount',\n      key: 'participantCount',\n      width: 100,\n    },\n    {\n      title: '创建人',\n      dataIndex: 'creatorName',\n      key: 'creatorName',\n      width: 100,\n    },\n    {\n      title: '拍卖时间',\n      dataIndex: 'startTime',\n      key: 'startTime',\n      width: 160,\n      render: (text: string, record: Auction) => (\n        <div>\n          <div>{new Date(text).toLocaleString()}</div>\n          <div style={{ fontSize: 12, color: '#999' }}>\n            至 {new Date(record.endTime).toLocaleString()}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right',\n      render: (_, record: Auction) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleView(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          {record.status === AuctionStatus.SCHEDULED && (\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<PlayCircleOutlined />}\n              onClick={() => handleStart(record.id)}\n            >\n              开始\n            </Button>\n          )}\n          {record.status === AuctionStatus.ONGOING && (\n            <>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<PauseCircleOutlined />}\n                onClick={() => handlePause(record.id)}\n              >\n                暂停\n              </Button>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<StopOutlined />}\n                onClick={() => handleEnd(record.id)}\n              >\n                结束\n              </Button>\n            </>\n          )}\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n          >\n            删除\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"auction-list-container\">\n      <Title level={2}>拍卖管理</Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总拍卖会\"\n              value={statistics.totalAuctions}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"进行中\"\n              value={statistics.ongoingAuctions}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"今日拍卖\"\n              value={statistics.todayAuctions}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总参与人数\"\n              value={statistics.totalParticipants}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          form={searchForm}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"title\" label=\"拍卖会标题\">\n                <Input placeholder=\"请输入拍卖会标题\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"拍卖状态\">\n                <Select placeholder=\"请选择拍卖状态\" allowClear>\n                  <Option value={AuctionStatus.DRAFT}>草稿</Option>\n                  <Option value={AuctionStatus.SCHEDULED}>已安排</Option>\n                  <Option value={AuctionStatus.ONGOING}>进行中</Option>\n                  <Option value={AuctionStatus.PAUSED}>已暂停</Option>\n                  <Option value={AuctionStatus.COMPLETED}>已完成</Option>\n                  <Option value={AuctionStatus.CANCELLED}>已取消</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"creatorName\" label=\"创建人\">\n                <Input placeholder=\"请输入创建人姓名\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"dateRange\" label=\"拍卖时间\">\n                <RangePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={4}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              新增拍卖会\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchAuctions}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 拍卖会列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={auctions}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 拍卖会编辑模态框 */}\n      <Modal\n        title={\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            {editingAuction ? <EditOutlined /> : <PlusOutlined />}\n            <span>{editingAuction ? `编辑拍卖会 - ${editingAuction.title}` : '新增拍卖会'}</span>\n          </div>\n        }\n        open={isModalVisible}\n        onCancel={() => {\n          if (saving) {\n            message.warning('正在保存中，请稍候...');\n            return;\n          }\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingAuction(null);\n        }}\n        footer={null}\n        width={700}\n        destroyOnClose\n        maskClosable={!saving}\n        closable={!saving}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Form.Item\n            name=\"title\"\n            label=\"拍卖会标题\"\n            rules={[\n              { required: true, message: '请输入拍卖会标题' },\n              { min: 2, max: 100, message: '标题长度为2-100个字符' },\n            ]}\n          >\n            <Input\n              placeholder=\"请输入拍卖会标题\"\n              showCount\n              maxLength={100}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"拍卖会描述\"\n            rules={[\n              { max: 500, message: '描述不能超过500个字符' },\n            ]}\n          >\n            <Input.TextArea\n              placeholder=\"请输入拍卖会描述\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"timeRange\"\n            label=\"拍卖时间\"\n            rules={[\n              { required: true, message: '请选择拍卖时间' },\n              {\n                validator: (_, value) => {\n                  if (!value || !value[0] || !value[1]) {\n                    return Promise.resolve();\n                  }\n                  const [start, end] = value;\n                  const now = new Date();\n\n                  // 检查开始时间不能早于当前时间\n                  if (start.isBefore(now)) {\n                    return Promise.reject(new Error('开始时间不能早于当前时间'));\n                  }\n\n                  // 检查结束时间必须晚于开始时间\n                  if (end.isBefore(start)) {\n                    return Promise.reject(new Error('结束时间必须晚于开始时间'));\n                  }\n\n                  // 检查拍卖时长不能少于30分钟\n                  const duration = end.diff(start, 'minutes');\n                  if (duration < 30) {\n                    return Promise.reject(new Error('拍卖时长不能少于30分钟'));\n                  }\n\n                  // 检查拍卖时长不能超过24小时\n                  if (duration > 24 * 60) {\n                    return Promise.reject(new Error('拍卖时长不能超过24小时'));\n                  }\n\n                  return Promise.resolve();\n                },\n              },\n            ]}\n            extra=\"拍卖时长建议在30分钟到24小时之间\"\n          >\n            <RangePicker\n              showTime={{\n                format: 'HH:mm',\n                minuteStep: 15, // 15分钟间隔\n              }}\n              format=\"YYYY-MM-DD HH:mm\"\n              style={{ width: '100%' }}\n              placeholder={['选择开始时间', '选择结束时间']}\n              disabledDate={(current) => {\n                // 禁用今天之前的日期\n                return current && current.isBefore(new Date(), 'day');\n              }}\n              disabledTime={(current, type) => {\n                const now = new Date();\n                const isToday = current && current.isSame(now, 'day');\n\n                if (type === 'start' && isToday) {\n                  // 如果是今天，禁用当前时间之前的时间\n                  return {\n                    disabledHours: () => {\n                      const hours = [];\n                      for (let i = 0; i < now.getHours(); i++) {\n                        hours.push(i);\n                      }\n                      return hours;\n                    },\n                    disabledMinutes: (selectedHour: number) => {\n                      if (selectedHour === now.getHours()) {\n                        const minutes = [];\n                        for (let i = 0; i < now.getMinutes(); i++) {\n                          minutes.push(i);\n                        }\n                        return minutes;\n                      }\n                      return [];\n                    },\n                  };\n                }\n                return {};\n              }}\n              showNow={false}\n              allowClear={false}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"location\"\n            label=\"拍卖地点\"\n            rules={[\n              { required: true, message: '请输入拍卖地点' },\n              { min: 2, max: 200, message: '地点长度为2-200个字符' },\n            ]}\n          >\n            <Input\n              placeholder=\"请输入拍卖地点\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"auctioneerID\"\n            label=\"拍卖师\"\n            rules={[\n              { required: true, message: '请选择拍卖师' },\n            ]}\n          >\n            <Select\n              placeholder=\"请选择拍卖师\"\n              showSearch\n              optionFilterProp=\"children\"\n              filterOption={(input, option) =>\n                String(option?.children ?? '').toLowerCase().includes(input.toLowerCase())\n              }\n            >\n              {auctioneers.map(auctioneer => (\n                <Option key={auctioneer.id} value={auctioneer.id}>\n                  {auctioneer.name}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          {/* 错误和成功消息显示 */}\n          <FormMessage type=\"error\" message={formError} visible={!!formError} />\n          <FormMessage type=\"success\" message={formSuccess} visible={!!formSuccess} />\n\n          <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>\n            <div style={{\n              borderTop: '1px solid #f0f0f0',\n              paddingTop: '16px',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            }}>\n              <div style={{ color: '#666', fontSize: '12px' }}>\n                {editingAuction ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建拍卖会'}\n              </div>\n              <Space>\n                <Button\n                  onClick={() => {\n                    if (saving) {\n                      message.warning('正在保存中，请稍候...');\n                      return;\n                    }\n                    setIsModalVisible(false);\n                    form.resetFields();\n                    setEditingAuction(null);\n                    clearAllMessages();\n                  }}\n                  disabled={saving}\n                  size=\"middle\"\n                >\n                  取消\n                </Button>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={saving}\n                  disabled={saving}\n                  size=\"middle\"\n                  icon={editingAuction ? <EditOutlined /> : <PlusOutlined />}\n                >\n                  {saving ? '保存中...' : (editingAuction ? '更新拍卖会' : '创建拍卖会')}\n                </Button>\n              </Space>\n            </div>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 查看详情模态框 */}\n      <Modal\n        title=\"拍卖会详情\"\n        open={isViewModalVisible}\n        onCancel={() => {\n          setIsViewModalVisible(false);\n          setViewingAuction(null);\n        }}\n        footer={[\n          <Button key=\"close\" onClick={() => {\n            setIsViewModalVisible(false);\n            setViewingAuction(null);\n          }}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {viewingAuction && (\n          <div style={{ padding: '16px 0' }}>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>拍卖会标题：</strong>\n                  <div style={{ marginTop: 4 }}>{viewingAuction.title}</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>拍卖状态：</strong>\n                  <div style={{ marginTop: 4 }}>\n                    <Tag color={auctionStatusMap[viewingAuction.status]?.color || 'default'}>\n                      {auctionStatusMap[viewingAuction.status]?.label || '未知'}\n                    </Tag>\n                  </div>\n                </div>\n              </Col>\n              <Col span={24}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>拍卖描述：</strong>\n                  <div style={{ marginTop: 4, padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>\n                    {viewingAuction.description || '暂无描述'}\n                  </div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>拍卖地点：</strong>\n                  <div style={{ marginTop: 4 }}>{viewingAuction.location || '暂无地点信息'}</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>创建人：</strong>\n                  <div style={{ marginTop: 4 }}>{viewingAuction.creatorName}</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>开始时间：</strong>\n                  <div style={{ marginTop: 4 }}>{new Date(viewingAuction.startTime).toLocaleString()}</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>结束时间：</strong>\n                  <div style={{ marginTop: 4 }}>{new Date(viewingAuction.endTime).toLocaleString()}</div>\n                </div>\n              </Col>\n              <Col span={8}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>商品总数：</strong>\n                  <div style={{ marginTop: 4, fontSize: '18px', color: '#1890ff' }}>{viewingAuction.totalItems}件</div>\n                </div>\n              </Col>\n              <Col span={8}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>已售商品：</strong>\n                  <div style={{ marginTop: 4, fontSize: '18px', color: '#52c41a' }}>{viewingAuction.soldItems}件</div>\n                </div>\n              </Col>\n              <Col span={8}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>参与人数：</strong>\n                  <div style={{ marginTop: 4, fontSize: '18px', color: '#722ed1' }}>{viewingAuction.participantCount}人</div>\n                </div>\n              </Col>\n              <Col span={24}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>成交金额：</strong>\n                  <div style={{ marginTop: 4, fontSize: '24px', color: '#f50', fontWeight: 'bold' }}>\n                    ¥{viewingAuction.totalAmount.toFixed(2)}\n                  </div>\n                </div>\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default AuctionList;"], "mappings": "kJAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,OAAO,CACPC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,UAAU,CACVC,SAAS,CACTC,KAAK,KACA,MAAM,CACb,OACEC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,cAAc,CACdC,kBAAkB,CAClBC,mBAAmB,CACnBC,YAAY,CACZC,cAAc,CACdC,yBAAyB,KACpB,mBAAmB,CAE1B,OAASC,cAAc,KAAQ,kCAAkC,CACjE,MAAO,CAAAC,WAAW,KAAM,iCAAiC,CACzD,OAASC,cAAc,CAAEC,iBAAiB,CAAEC,cAAc,KAAQ,+BAA+B,CACjG,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,aAAa,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErB,KAAM,CAAEC,KAAM,CAAC,CAAG5B,UAAU,CAC5B,KAAM,CAAE6B,MAAO,CAAC,CAAGlC,MAAM,CACzB,KAAM,CAAEmC,WAAY,CAAC,CAAG3B,UAAU,CAElC;AACA,UAAY,CAAA4B,aAAa,uBAAbA,aAAa,EAAbA,aAAa,CAAbA,aAAa,qBACL;AADRA,aAAa,CAAbA,aAAa,6BAEL;AAFRA,aAAa,CAAbA,aAAa,yBAGL;AAHRA,aAAa,CAAbA,aAAa,uBAIL;AAJRA,aAAa,CAAbA,aAAa,6BAKL;AALRA,aAAa,CAAbA,aAAa,6BAML;AAAA,MANR,CAAAA,aAAa,OASzB;AAoBA;AAUA,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAClC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGhD,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACiD,OAAO,CAAEC,UAAU,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACmD,KAAK,CAAEC,QAAQ,CAAC,CAAGpD,QAAQ,CAAC,CAAC,CAAC,CACrC,KAAM,CAACqD,WAAW,CAAEC,cAAc,CAAC,CAAGtD,QAAQ,CAAqB,CACjEuD,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC2D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG5D,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC6D,cAAc,CAAEC,iBAAiB,CAAC,CAAG9D,QAAQ,CAAiB,IAAI,CAAC,CAC1E,KAAM,CAAC+D,cAAc,CAAEC,iBAAiB,CAAC,CAAGhE,QAAQ,CAAiB,IAAI,CAAC,CAC1E,KAAM,CAACiE,MAAM,CAAEC,SAAS,CAAC,CAAGlE,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACmE,UAAU,CAAEC,aAAa,CAAC,CAAGpE,QAAQ,CAAC,CAC3CqE,aAAa,CAAE,CAAC,CAChBC,eAAe,CAAE,CAAC,CAClBC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CAAC,CACF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG1E,QAAQ,CAA+B,EAAE,CAAC,CAChF,KAAM,CAAC2E,IAAI,CAAC,CAAGjE,IAAI,CAACkE,OAAO,CAAC,CAAC,CAC7B,KAAM,CAACC,UAAU,CAAC,CAAGnE,IAAI,CAACkE,OAAO,CAAC,CAAC,CAEnC,KAAM,CACJE,SAAS,CACTC,WAAW,CACXC,YAAY,CACZC,cAAc,CACdC,gBACF,CAAC,CAAGpD,cAAc,CAAC,CAAC,CAEpB;AACA,KAAM,CAAAqD,gBAAgB,CAAG,CACvB,CAACxC,aAAa,CAACyC,KAAK,EAAG,CAAEC,KAAK,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAU,CAAC,CACxD,CAAC3C,aAAa,CAAC4C,SAAS,EAAG,CAAEF,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC1D,CAAC3C,aAAa,CAAC6C,OAAO,EAAG,CAAEH,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,OAAQ,CAAC,CACzD,CAAC3C,aAAa,CAAC8C,MAAM,EAAG,CAAEJ,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,QAAS,CAAC,CACzD,CAAC3C,aAAa,CAAC+C,SAAS,EAAG,CAAEL,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,QAAS,CAAC,CAC5D,CAAC3C,aAAa,CAACgD,SAAS,EAAG,CAAEN,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,KAAM,CAC1D,CAAC,CAED;AACA,KAAM,CAAAM,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC1C,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAA2C,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAACkE,cAAc,CAACzC,WAAW,CAAC,CACjE,GAAIwC,QAAQ,CAACE,OAAO,CAAE,CACpB;AACA,KAAM,CAAAC,cAAc,CAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAEC,OAAY,EAAAC,aAAA,CAAAA,aAAA,IACtDD,OAAO,MACVE,KAAK,CAAEF,OAAO,CAACG,IAAI,EAAIH,OAAO,CAACE,KAAK,CAAE;AACtCE,UAAU,CAAEJ,OAAO,CAACI,UAAU,EAAI,CAAC,CACnCC,WAAW,CAAEL,OAAO,CAACK,WAAW,EAAI,CAAC,CACrCC,gBAAgB,CAAEN,OAAO,CAACM,gBAAgB,EAAI,CAAC,CAC/CC,SAAS,CAAEP,OAAO,CAACO,SAAS,EAAI,CAAC,CACjCC,WAAW,CAAER,OAAO,CAACQ,WAAW,EAAI,IAAI,CACxC;AACAC,YAAY,CAAET,OAAO,CAACS,YAAY,EAClC,CAAC,CACH7D,WAAW,CAACgD,cAAc,CAAC,CAC3B5C,QAAQ,CAACyC,QAAQ,CAACI,IAAI,CAAC9C,KAAK,CAAC,CAC7B2D,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEf,cAAc,CAAC,CAC1C,CAAC,IAAM,CACLrF,OAAO,CAACqG,KAAK,CAACnB,QAAQ,CAAClF,OAAO,EAAI,WAAW,CAAC,CAC9CqC,WAAW,CAAC,EAAE,CAAC,CACfI,QAAQ,CAAC,CAAC,CAAC,CACb,CACF,CAAE,MAAO4D,KAAU,CAAE,CACnBF,OAAO,CAACE,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,GAAI,CAAAC,QAAQ,CAAG,WAAW,CAC1B,GAAID,KAAK,CAACnB,QAAQ,CAAE,CAClB,KAAM,CAAEqB,MAAO,CAAC,CAAGF,KAAK,CAACnB,QAAQ,CACjC,GAAIqB,MAAM,GAAK,GAAG,CAAE,CAClBD,QAAQ,CAAG,aAAa,CAC1B,CAAC,IAAM,IAAIC,MAAM,GAAK,GAAG,CAAE,CACzBD,QAAQ,CAAG,aAAa,CAC1B,CAAC,IAAM,IAAIC,MAAM,GAAK,GAAG,CAAE,CACzBD,QAAQ,CAAG,eAAe,CAC5B,CACF,CACAtG,OAAO,CAACqG,KAAK,CAACC,QAAQ,CAAC,CACvBjE,WAAW,CAAC,EAAE,CAAC,CACfI,QAAQ,CAAC,CAAC,CAAC,CACb,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAiE,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF,KAAM,CAAAtB,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAACwF,oBAAoB,CAAC,CAAC,CAC5D,GAAIvB,QAAQ,CAACE,OAAO,CAAE,CACpB3B,aAAa,CAACyB,QAAQ,CAACI,IAAI,CAAC,CAC9B,CACF,CAAE,MAAOe,KAAU,CAAE,CACnBF,OAAO,CAACE,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CACpC,CACF,CAAC,CAED;AACA,KAAM,CAAAK,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAI,8BAA8B,CACvF,KAAM,CAAA5B,QAAQ,CAAG,KAAM,CAAA6B,KAAK,IAAAC,MAAA,CAAIL,UAAU,0CAAwC,CAAC,CACnF,KAAM,CAAArB,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAAC+B,IAAI,CAAC,CAAC,CAElC;AACA,GAAI3B,IAAI,CAACF,OAAO,EAAIE,IAAI,CAACA,IAAI,EAAIA,IAAI,CAACA,IAAI,CAACC,IAAI,CAAE,CAC/C;AACA,KAAM,CAAA2B,eAAe,CAAG5B,IAAI,CAACA,IAAI,CAACC,IAAI,CAAC4B,MAAM,CAAEC,IAAS,EAAKA,IAAI,CAACC,QAAQ,GAAK,CAAC,CAAC,CACjF,KAAM,CAAAC,cAAc,CAAGJ,eAAe,CAAC1B,GAAG,CAAE4B,IAAS,GAAM,CACzDG,EAAE,CAAEH,IAAI,CAACG,EAAE,CACX3B,IAAI,CAAEwB,IAAI,CAACI,QAAQ,EAAIJ,IAAI,CAACK,QAC9B,CAAC,CAAC,CAAC,CACH1D,cAAc,CAACuD,cAAc,CAAC,CAC9BnB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEkB,cAAc,CAAC,CAC1C,CAAC,IAAM,CACLnB,OAAO,CAACuB,IAAI,CAAC,YAAY,CAAEpC,IAAI,CAAC,CAChCvB,cAAc,CAAC,EAAE,CAAC,CACpB,CACF,CAAE,MAAOsC,KAAK,CAAE,CACdF,OAAO,CAACE,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClCtC,cAAc,CAAC,EAAE,CAAC,CACpB,CACF,CAAC,CAED;AACAzE,SAAS,CAAC,IAAM,CACd2F,aAAa,CAAC,CAAC,CACfuB,eAAe,CAAC,CAAC,CACjBE,gBAAgB,CAAC,CAAC,CACpB;AACA,CAAC,CAAE,CAAChE,WAAW,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAiF,YAAY,CAAIC,MAAW,EAAK,CACpCjF,cAAc,CAAA+C,aAAA,CAAAA,aAAA,CAAAA,aAAA,IACThD,WAAW,EACXkF,MAAM,MACThF,IAAI,CAAE,CAAC,EACR,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAiF,WAAW,CAAGA,CAAA,GAAM,CACxB3D,UAAU,CAAC4D,WAAW,CAAC,CAAC,CACxBnF,cAAc,CAAC,CACbC,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAkF,SAAS,CAAGA,CAAA,GAAM,CACtB5E,iBAAiB,CAAC,IAAI,CAAC,CACvBa,IAAI,CAAC8D,WAAW,CAAC,CAAC,CAElB;AACA9D,IAAI,CAACgE,cAAc,CAAC,CAClB;AACAC,SAAS,CAAE,CAAC3G,KAAK,CAAC,CAAC,CAAC4G,GAAG,CAAC,CAAC,CAAE,MAAM,CAAC,CAAE5G,KAAK,CAAC,CAAC,CAAC4G,GAAG,CAAC,CAAC,CAAE,KAAK,CAAC,CAC3D,CAAC,CAAC,CAEF3D,gBAAgB,CAAC,CAAC,CAClBxB,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAAoF,UAAU,CAAI1C,OAAgB,EAAK,CACvCpC,iBAAiB,CAACoC,OAAO,CAAC,CAC1BxC,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAAmF,UAAU,CAAG,KAAO,CAAA3C,OAAgB,EAAK,CAC7CtC,iBAAiB,CAACsC,OAAO,CAAC,CAC1B,GAAI,CACF;AACA,KAAM,CAAAP,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAACoH,gBAAgB,CAAC5C,OAAO,CAAC8B,EAAE,CAAC,CAClE,GAAIrC,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAAkD,aAAa,CAAGpD,QAAQ,CAACI,IAAkE,CACjG;AACA,KAAM,CAAAY,YAAY,CAAGoC,aAAa,CAACpC,YAAY,EAAIoC,aAAa,CAACC,YAAY,EAAI,IAAI,CAErFpC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEkC,aAAa,CAAC,CACpCnC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAEvClC,IAAI,CAACgE,cAAc,CAAC,CAClBrC,KAAK,CAAEF,OAAO,CAACE,KAAK,CAAE;AACtB6C,WAAW,CAAE/C,OAAO,CAAC+C,WAAW,CAC5BC,QAAQ,CAAEhD,OAAO,CAACgD,QAAQ,EAAI,EAAE,CAAE;AAClCF,YAAY,CAAErC,YAAY,CAAE;AAChC+B,SAAS,CAAE,CAAC3G,KAAK,CAACmE,OAAO,CAACiD,SAAS,CAAC,CAAEpH,KAAK,CAACmE,OAAO,CAACkD,OAAO,CAAC,CAC9D,CAAC,CAAC,CACA,CAAC,IAAM,CACL;AACA3E,IAAI,CAACgE,cAAc,CAAC,CAClBrC,KAAK,CAAEF,OAAO,CAACE,KAAK,CACpB6C,WAAW,CAAE/C,OAAO,CAAC+C,WAAW,CAChCC,QAAQ,CAAEhD,OAAO,CAACgD,QAAQ,EAAI,EAAE,CAChCF,YAAY,CAAE9C,OAAO,CAACS,YAAY,EAAI,IAAI,CAAE;AAC5C+B,SAAS,CAAE,CAAC3G,KAAK,CAACmE,OAAO,CAACiD,SAAS,CAAC,CAAEpH,KAAK,CAACmE,OAAO,CAACkD,OAAO,CAAC,CAC9D,CAAC,CAAC,CACJ,CACF,CAAE,MAAOtC,KAAK,CAAE,CACdF,OAAO,CAACE,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC;AACArC,IAAI,CAACgE,cAAc,CAAC,CAClBrC,KAAK,CAAEF,OAAO,CAACE,KAAK,CACpB6C,WAAW,CAAE/C,OAAO,CAAC+C,WAAW,CAChCC,QAAQ,CAAEhD,OAAO,CAACgD,QAAQ,EAAI,EAAE,CAChCF,YAAY,CAAE9C,OAAO,CAACS,YAAY,EAAI,IAAI,CAAE;AAC5C+B,SAAS,CAAE,CAAC3G,KAAK,CAACmE,OAAO,CAACiD,SAAS,CAAC,CAAEpH,KAAK,CAACmE,OAAO,CAACkD,OAAO,CAAC,CAC9D,CAAC,CAAC,CACJ,CAEApE,gBAAgB,CAAC,CAAC,CAClBxB,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAA6F,YAAY,CAAG,KAAO,CAAArB,EAAU,EAAK,CACzC,GAAI,CACF,KAAM,CAAArC,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAAC4H,aAAa,CAACtB,EAAE,CAAC,CACvD,GAAIrC,QAAQ,CAACE,OAAO,CAAE,CACpBpF,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC,CACvBH,aAAa,CAAC,CAAC,CACjB,CAAC,IAAM,CACLjF,OAAO,CAACqG,KAAK,CAACnB,QAAQ,CAAClF,OAAO,EAAI,MAAM,CAAC,CAC3C,CACF,CAAE,MAAOqG,KAAU,CAAE,CACnBrG,OAAO,CAACqG,KAAK,CAACA,KAAK,CAACrG,OAAO,EAAI,MAAM,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAA8I,UAAU,CAAG,KAAO,CAAAlB,MAAW,EAAK,CACxCrE,SAAS,CAAC,IAAI,CAAC,CACfgB,gBAAgB,CAAC,CAAC,CAElB,GAAI,CACF,KAAM,CAAAwE,WAAW,CAAG,CAClBnD,IAAI,CAAEgC,MAAM,CAACjC,KAAK,CAAE;AACpB6C,WAAW,CAAEZ,MAAM,CAACY,WAAW,CAC/BtC,YAAY,CAAE0B,MAAM,CAACW,YAAY,CAAE;AACnCE,QAAQ,CAAEb,MAAM,CAACa,QAAQ,CACzBC,SAAS,CAAEd,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAACe,WAAW,CAAC,CAAC,CAC5CL,OAAO,CAAEf,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAACe,WAAW,CAAC,CAC3C,CAAC,CAED,GAAI,CAAA9D,QAAQ,CACZ,GAAIhC,cAAc,CAAE,CAClBgC,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAACgI,aAAa,CAAC/F,cAAc,CAACqE,EAAE,CAAEwB,WAAW,CAAC,CAC/E,CAAC,IAAM,CACL7D,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAACiI,aAAa,CAACH,WAAW,CAAC,CAC5D,CAEA,KAAM,CAAAI,UAAU,CAAGjG,cAAc,CAAG,YAAY,CAAG,UAAU,CAE7D,GAAI9B,iBAAiB,CAAC8D,QAAQ,CAAEb,YAAY,CAAEC,cAAc,CAAE6E,UAAU,CAAC,CAAE,CACzE;AACAC,UAAU,CAAC,IAAM,CACfrG,iBAAiB,CAAC,KAAK,CAAC,CACxBiB,IAAI,CAAC8D,WAAW,CAAC,CAAC,CAClB3E,iBAAiB,CAAC,IAAI,CAAC,CACvBoB,gBAAgB,CAAC,CAAC,CAClBU,aAAa,CAAC,CAAC,CACfuB,eAAe,CAAC,CAAC,CAAE;AACrB,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAE,MAAOH,KAAU,CAAE,CACnBhF,cAAc,CAACgF,KAAK,CAAEhC,YAAY,CAAC,CACrC,CAAC,OAAS,CACRd,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED;AACA,KAAM,CAAA8F,WAAW,CAAG,KAAO,CAAA9B,EAAU,EAAK,CACxC,KAAM,CAAA9B,OAAO,CAAGrD,QAAQ,CAACkH,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAChC,EAAE,GAAKA,EAAE,CAAC,CAC/C,GAAI,CAAC9B,OAAO,CAAE,OAEd3F,KAAK,CAAC0J,OAAO,CAAC,CACZ7D,KAAK,CAAE,QAAQ,CACf8D,OAAO,cACLjI,KAAA,QAAAkI,QAAA,eACElI,KAAA,MAAAkI,QAAA,EAAG,mDAAS,cAAAlI,KAAA,WAAAkI,QAAA,EAAQ,IAAC,CAACjE,OAAO,CAACE,KAAK,CAAC,IAAC,EAAQ,CAAC,gBAAG,EAAG,CAAC,cACrDjE,IAAA,MAAGiI,KAAK,CAAE,CAAEhF,KAAK,CAAE,MAAM,CAAEiF,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,sIAE/C,CAAG,CAAC,EACD,CACN,CACDG,MAAM,CAAE,MAAM,CACdC,UAAU,CAAE,IAAI,CAChBC,aAAa,CAAE,CAAEC,IAAI,CAAE,SAAU,CAAC,CAClCC,IAAI,CAAE,KAAAA,CAAA,GAAY,CAChB,GAAI,CACF,KAAM,CAAA/E,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAACiJ,YAAY,CAAC3C,EAAE,CAAC,CACtD,GAAIrC,QAAQ,CAACE,OAAO,CAAE,CACpBpF,OAAO,CAACoF,OAAO,CAAC,CACdqE,OAAO,wBAAAzC,MAAA,CAASvB,OAAO,CAACE,KAAK,0CAAS,CACtCwE,QAAQ,CAAE,CACZ,CAAC,CAAC,CACFlF,aAAa,CAAC,CAAC,CACjB,CAAC,IAAM,CACLjF,OAAO,CAACqG,KAAK,CAAC,CACZoD,OAAO,CAAEvE,QAAQ,CAAClF,OAAO,EAAI,cAAc,CAC3CmK,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CACF,CAAE,MAAO9D,KAAU,CAAE,KAAA+D,eAAA,CAAAC,gBAAA,CACnBlE,OAAO,CAACE,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,GAAI,CAAAC,QAAQ,CAAG,QAAQ,CACvB,GAAI,EAAA8D,eAAA,CAAA/D,KAAK,CAACnB,QAAQ,UAAAkF,eAAA,iBAAdA,eAAA,CAAgB7D,MAAM,IAAK,GAAG,CAAE,CAClCD,QAAQ,CAAG,oBAAoB,CACjC,CAAC,IAAM,IAAI,EAAA+D,gBAAA,CAAAhE,KAAK,CAACnB,QAAQ,UAAAmF,gBAAA,iBAAdA,gBAAA,CAAgB9D,MAAM,IAAK,GAAG,CAAE,CACzCD,QAAQ,CAAG,cAAc,CAC3B,CAAC,IAAM,CACLA,QAAQ,CAAGD,KAAK,CAACrG,OAAO,EAAI,eAAe,CAC7C,CACAA,OAAO,CAACqG,KAAK,CAAC,CACZoD,OAAO,CAAEnD,QAAQ,CACjB6D,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAG,WAAW,CAAG,KAAO,CAAA/C,EAAU,EAAK,CACxC,KAAM,CAAA9B,OAAO,CAAGrD,QAAQ,CAACkH,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAChC,EAAE,GAAKA,EAAE,CAAC,CAC/C,GAAI,CAAC9B,OAAO,CAAE,OAEd3F,KAAK,CAAC0J,OAAO,CAAC,CACZ7D,KAAK,CAAE,QAAQ,CACf8D,OAAO,cACLjI,KAAA,QAAAkI,QAAA,eACElI,KAAA,MAAAkI,QAAA,EAAG,mDAAS,cAAAlI,KAAA,WAAAkI,QAAA,EAAQ,IAAC,CAACjE,OAAO,CAACE,KAAK,CAAC,IAAC,EAAQ,CAAC,gBAAG,EAAG,CAAC,cACrDjE,IAAA,MAAGiI,KAAK,CAAE,CAAEhF,KAAK,CAAE,MAAM,CAAEiF,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,sIAE/C,CAAG,CAAC,EACD,CACN,CACDG,MAAM,CAAE,MAAM,CACdC,UAAU,CAAE,IAAI,CAChBC,aAAa,CAAE,CAAEC,IAAI,CAAE,SAAU,CAAC,CAClCC,IAAI,CAAE,KAAAA,CAAA,GAAY,CAChB,GAAI,CACF,KAAM,CAAA/E,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAACsJ,YAAY,CAAChD,EAAE,CAAC,CACtD,GAAIrC,QAAQ,CAACE,OAAO,CAAE,CACpBpF,OAAO,CAACoF,OAAO,CAAC,CACdqE,OAAO,wBAAAzC,MAAA,CAASvB,OAAO,CAACE,KAAK,wBAAM,CACnCwE,QAAQ,CAAE,CACZ,CAAC,CAAC,CACFlF,aAAa,CAAC,CAAC,CACjB,CAAC,IAAM,CACLjF,OAAO,CAACqG,KAAK,CAAC,CACZoD,OAAO,CAAEvE,QAAQ,CAAClF,OAAO,EAAI,cAAc,CAC3CmK,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CACF,CAAE,MAAO9D,KAAU,CAAE,CACnBF,OAAO,CAACE,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BrG,OAAO,CAACqG,KAAK,CAAC,CACZoD,OAAO,CAAEpD,KAAK,CAACrG,OAAO,EAAI,mBAAmB,CAC7CmK,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAK,SAAS,CAAG,KAAO,CAAAjD,EAAU,EAAK,CACtC,KAAM,CAAA9B,OAAO,CAAGrD,QAAQ,CAACkH,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAChC,EAAE,GAAKA,EAAE,CAAC,CAC/C,GAAI,CAAC9B,OAAO,CAAE,OAEd3F,KAAK,CAAC0J,OAAO,CAAC,CACZ7D,KAAK,CAAE,QAAQ,CACf8E,IAAI,cAAE/I,IAAA,CAACV,yBAAyB,EAAC2I,KAAK,CAAE,CAAEhF,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAChE8E,OAAO,cACLjI,KAAA,QAAAkI,QAAA,eACElI,KAAA,MAAAkI,QAAA,EAAG,mDAAS,cAAAlI,KAAA,WAAAkI,QAAA,EAAQ,IAAC,CAACjE,OAAO,CAACE,KAAK,CAAC,IAAC,EAAQ,CAAC,gBAAG,EAAG,CAAC,cACrDjE,IAAA,MAAGiI,KAAK,CAAE,CAAEhF,KAAK,CAAE,SAAS,CAAEiF,QAAQ,CAAE,MAAM,CAAEc,UAAU,CAAE,MAAO,CAAE,CAAAhB,QAAA,CAAC,mJAEtE,CAAG,CAAC,EACD,CACN,CACDG,MAAM,CAAE,MAAM,CACdC,UAAU,CAAE,IAAI,CAChBC,aAAa,CAAE,CAAEY,MAAM,CAAE,IAAK,CAAC,CAC/BV,IAAI,CAAE,KAAAA,CAAA,GAAY,CAChB,GAAI,CACF,KAAM,CAAA/E,QAAQ,CAAG,KAAM,CAAAjE,cAAc,CAAC2J,UAAU,CAACrD,EAAE,CAAC,CACpD,GAAIrC,QAAQ,CAACE,OAAO,CAAE,CACpBpF,OAAO,CAACoF,OAAO,CAAC,CACdqE,OAAO,wBAAAzC,MAAA,CAASvB,OAAO,CAACE,KAAK,0CAAS,CACtCwE,QAAQ,CAAE,CACZ,CAAC,CAAC,CACFlF,aAAa,CAAC,CAAC,CACjB,CAAC,IAAM,CACLjF,OAAO,CAACqG,KAAK,CAAC,CACZoD,OAAO,CAAEvE,QAAQ,CAAClF,OAAO,EAAI,cAAc,CAC3CmK,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CACF,CAAE,MAAO9D,KAAU,CAAE,CACnBF,OAAO,CAACE,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BrG,OAAO,CAACqG,KAAK,CAAC,CACZoD,OAAO,CAAEpD,KAAK,CAACrG,OAAO,EAAI,mBAAmB,CAC7CmK,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAU,OAA6B,CAAG,CACpC,CACElF,KAAK,CAAE,IAAI,CACXmF,SAAS,CAAE,IAAI,CACfC,GAAG,CAAE,IAAI,CACTC,KAAK,CAAE,EACT,CAAC,CACD,CACErF,KAAK,CAAE,OAAO,CACdmF,SAAS,CAAE,OAAO,CAClBC,GAAG,CAAE,OAAO,CACZC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGC,IAAY,eACnBxJ,IAAA,QAAKiI,KAAK,CAAE,CAAEe,UAAU,CAAE,GAAI,CAAE,CAAAhB,QAAA,CAAEwB,IAAI,CAAM,CAEhD,CAAC,CACD,CACEvF,KAAK,CAAE,MAAM,CACbmF,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAG1E,MAAqB,EAAK,CACjC,KAAM,CAAA4E,UAAU,CAAG3G,gBAAgB,CAAC+B,MAAM,CAAC,CAC3C,mBACE7E,IAAA,CAACpB,KAAK,EACJiG,MAAM,CACJA,MAAM,GAAKvE,aAAa,CAAC6C,OAAO,CAAG,YAAY,CAC/C0B,MAAM,GAAKvE,aAAa,CAAC+C,SAAS,CAAG,SAAS,CAC9CwB,MAAM,GAAKvE,aAAa,CAACgD,SAAS,CAAG,OAAO,CAAG,SAChD,CACDkG,IAAI,cACFxJ,IAAA,CAAC7B,GAAG,EAAC8E,KAAK,CAAE,CAAAwG,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAExG,KAAK,GAAI,SAAU,CAAA+E,QAAA,CACxC,CAAAyB,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEzG,KAAK,GAAI,IAAI,CACvB,CACN,CACF,CAAC,CAEN,CACF,CAAC,CACD,CACEiB,KAAK,CAAE,MAAM,CACbmF,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACzI,KAAa,CAAE4I,MAAe,gBACrC5J,KAAA,QAAAkI,QAAA,eACElI,KAAA,QAAAkI,QAAA,EAAMlH,KAAK,CAAC,QAAC,EAAK,CAAC,cACnBhB,KAAA,QAAKmI,KAAK,CAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEjF,KAAK,CAAE,MAAO,CAAE,CAAA+E,QAAA,EAAC,gBACvC,CAAC0B,MAAM,CAACpF,SAAS,EAClB,CAAC,EACH,CAET,CAAC,CACD,CACEL,KAAK,CAAE,MAAM,CACbmF,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAGI,MAAc,eACrB7J,KAAA,QAAKmI,KAAK,CAAE,CAAEe,UAAU,CAAE,GAAG,CAAE/F,KAAK,CAAE,MAAO,CAAE,CAAA+E,QAAA,EAAC,MAC7C,CAAC2B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,EAChB,CAET,CAAC,CACD,CACE3F,KAAK,CAAE,MAAM,CACbmF,SAAS,CAAE,kBAAkB,CAC7BC,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,GACT,CAAC,CACD,CACErF,KAAK,CAAE,KAAK,CACZmF,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBC,KAAK,CAAE,GACT,CAAC,CACD,CACErF,KAAK,CAAE,MAAM,CACbmF,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAEA,CAACC,IAAY,CAAEE,MAAe,gBACpC5J,KAAA,QAAAkI,QAAA,eACEhI,IAAA,QAAAgI,QAAA,CAAM,GAAI,CAAA6B,IAAI,CAACL,IAAI,CAAC,CAACM,cAAc,CAAC,CAAC,CAAM,CAAC,cAC5ChK,KAAA,QAAKmI,KAAK,CAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEjF,KAAK,CAAE,MAAO,CAAE,CAAA+E,QAAA,EAAC,SACzC,CAAC,GAAI,CAAA6B,IAAI,CAACH,MAAM,CAACzC,OAAO,CAAC,CAAC6C,cAAc,CAAC,CAAC,EACzC,CAAC,EACH,CAET,CAAC,CACD,CACE7F,KAAK,CAAE,IAAI,CACXoF,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,GAAG,CACVS,KAAK,CAAE,OAAO,CACdR,MAAM,CAAEA,CAACS,CAAC,CAAEN,MAAe,gBACzB5J,KAAA,CAAC9B,KAAK,EAACiM,IAAI,CAAC,OAAO,CAAAjC,QAAA,eACjBhI,IAAA,CAACjC,MAAM,EACLuK,IAAI,CAAC,MAAM,CACX2B,IAAI,CAAC,OAAO,CACZlB,IAAI,cAAE/I,IAAA,CAACjB,WAAW,GAAE,CAAE,CACtBmL,OAAO,CAAEA,CAAA,GAAMzD,UAAU,CAACiD,MAAM,CAAE,CAAA1B,QAAA,CACnC,cAED,CAAQ,CAAC,cACThI,IAAA,CAACjC,MAAM,EACLuK,IAAI,CAAC,MAAM,CACX2B,IAAI,CAAC,OAAO,CACZlB,IAAI,cAAE/I,IAAA,CAAChB,YAAY,GAAE,CAAE,CACvBkL,OAAO,CAAEA,CAAA,GAAMxD,UAAU,CAACgD,MAAM,CAAE,CAAA1B,QAAA,CACnC,cAED,CAAQ,CAAC,CACR0B,MAAM,CAAC7E,MAAM,GAAKvE,aAAa,CAAC4C,SAAS,eACxClD,IAAA,CAACjC,MAAM,EACLuK,IAAI,CAAC,MAAM,CACX2B,IAAI,CAAC,OAAO,CACZlB,IAAI,cAAE/I,IAAA,CAACd,kBAAkB,GAAE,CAAE,CAC7BgL,OAAO,CAAEA,CAAA,GAAMvC,WAAW,CAAC+B,MAAM,CAAC7D,EAAE,CAAE,CAAAmC,QAAA,CACvC,cAED,CAAQ,CACT,CACA0B,MAAM,CAAC7E,MAAM,GAAKvE,aAAa,CAAC6C,OAAO,eACtCrD,KAAA,CAAAI,SAAA,EAAA8H,QAAA,eACEhI,IAAA,CAACjC,MAAM,EACLuK,IAAI,CAAC,MAAM,CACX2B,IAAI,CAAC,OAAO,CACZlB,IAAI,cAAE/I,IAAA,CAACb,mBAAmB,GAAE,CAAE,CAC9B+K,OAAO,CAAEA,CAAA,GAAMtB,WAAW,CAACc,MAAM,CAAC7D,EAAE,CAAE,CAAAmC,QAAA,CACvC,cAED,CAAQ,CAAC,cACThI,IAAA,CAACjC,MAAM,EACLuK,IAAI,CAAC,MAAM,CACX2B,IAAI,CAAC,OAAO,CACZlB,IAAI,cAAE/I,IAAA,CAACZ,YAAY,GAAE,CAAE,CACvB8K,OAAO,CAAEA,CAAA,GAAMpB,SAAS,CAACY,MAAM,CAAC7D,EAAE,CAAE,CAAAmC,QAAA,CACrC,cAED,CAAQ,CAAC,EACT,CACH,cACDhI,IAAA,CAACjC,MAAM,EACLuK,IAAI,CAAC,MAAM,CACX2B,IAAI,CAAC,OAAO,CACZhB,MAAM,MACNF,IAAI,cAAE/I,IAAA,CAACf,cAAc,GAAE,CAAE,CACzBiL,OAAO,CAAEA,CAAA,GAAMhD,YAAY,CAACwC,MAAM,CAAC7D,EAAE,CAAE,CAAAmC,QAAA,CACxC,cAED,CAAQ,CAAC,EACJ,CAEX,CAAC,CACF,CAED,mBACElI,KAAA,QAAKqK,SAAS,CAAC,wBAAwB,CAAAnC,QAAA,eACrChI,IAAA,CAACG,KAAK,EAACiK,KAAK,CAAE,CAAE,CAAApC,QAAA,CAAC,0BAAI,CAAO,CAAC,cAG7BlI,KAAA,CAACtB,GAAG,EAAC6L,MAAM,CAAE,EAAG,CAACpC,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC3ChI,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAACnC,IAAI,EAAAmK,QAAA,cACHhI,IAAA,CAACrB,SAAS,EACRsF,KAAK,CAAC,0BAAM,CACZyG,KAAK,CAAE5I,UAAU,CAACE,aAAc,CAChC2I,UAAU,CAAE,CAAE1H,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNjD,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAACnC,IAAI,EAAAmK,QAAA,cACHhI,IAAA,CAACrB,SAAS,EACRsF,KAAK,CAAC,oBAAK,CACXyG,KAAK,CAAE5I,UAAU,CAACG,eAAgB,CAClC0I,UAAU,CAAE,CAAE1H,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNjD,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAACnC,IAAI,EAAAmK,QAAA,cACHhI,IAAA,CAACrB,SAAS,EACRsF,KAAK,CAAC,0BAAM,CACZyG,KAAK,CAAE5I,UAAU,CAACI,aAAc,CAChCyI,UAAU,CAAE,CAAE1H,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNjD,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAACnC,IAAI,EAAAmK,QAAA,cACHhI,IAAA,CAACrB,SAAS,EACRsF,KAAK,CAAC,gCAAO,CACbyG,KAAK,CAAE5I,UAAU,CAACK,iBAAkB,CACpCwI,UAAU,CAAE,CAAE1H,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGNjD,IAAA,CAACnC,IAAI,EAACsM,SAAS,CAAC,aAAa,CAACF,IAAI,CAAC,OAAO,CAAAjC,QAAA,cACxChI,IAAA,CAAC3B,IAAI,EACHiE,IAAI,CAAEE,UAAW,CACjBoI,MAAM,CAAC,QAAQ,CACfC,QAAQ,CAAE5E,YAAa,CACvB6E,YAAY,CAAC,KAAK,CAAA9C,QAAA,cAElBlI,KAAA,CAACtB,GAAG,EAAC6L,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACpC,KAAK,CAAE,CAAEqB,KAAK,CAAE,MAAO,CAAE,CAAAtB,QAAA,eAC9ChI,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EAAC7G,IAAI,CAAC,OAAO,CAAClB,KAAK,CAAC,gCAAO,CAAAgF,QAAA,cACnChI,IAAA,CAAC/B,KAAK,EAAC+M,WAAW,CAAC,kDAAU,CAACC,UAAU,MAAE,CAAC,CAClC,CAAC,CACT,CAAC,cACNjL,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EAAC7G,IAAI,CAAC,QAAQ,CAAClB,KAAK,CAAC,0BAAM,CAAAgF,QAAA,cACnClI,KAAA,CAAC5B,MAAM,EAAC8M,WAAW,CAAC,4CAAS,CAACC,UAAU,MAAAjD,QAAA,eACtChI,IAAA,CAACI,MAAM,EAACsK,KAAK,CAAEpK,aAAa,CAACyC,KAAM,CAAAiF,QAAA,CAAC,cAAE,CAAQ,CAAC,cAC/ChI,IAAA,CAACI,MAAM,EAACsK,KAAK,CAAEpK,aAAa,CAAC4C,SAAU,CAAA8E,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACpDhI,IAAA,CAACI,MAAM,EAACsK,KAAK,CAAEpK,aAAa,CAAC6C,OAAQ,CAAA6E,QAAA,CAAC,oBAAG,CAAQ,CAAC,cAClDhI,IAAA,CAACI,MAAM,EAACsK,KAAK,CAAEpK,aAAa,CAAC8C,MAAO,CAAA4E,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACjDhI,IAAA,CAACI,MAAM,EAACsK,KAAK,CAAEpK,aAAa,CAAC+C,SAAU,CAAA2E,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACpDhI,IAAA,CAACI,MAAM,EAACsK,KAAK,CAAEpK,aAAa,CAACgD,SAAU,CAAA0E,QAAA,CAAC,oBAAG,CAAQ,CAAC,EAC9C,CAAC,CACA,CAAC,CACT,CAAC,cACNhI,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EAAC7G,IAAI,CAAC,aAAa,CAAClB,KAAK,CAAC,oBAAK,CAAAgF,QAAA,cACvChI,IAAA,CAAC/B,KAAK,EAAC+M,WAAW,CAAC,kDAAU,CAACC,UAAU,MAAE,CAAC,CAClC,CAAC,CACT,CAAC,cACNjL,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EAAC7G,IAAI,CAAC,WAAW,CAAClB,KAAK,CAAC,0BAAM,CAAAgF,QAAA,cACtChI,IAAA,CAACK,WAAW,EAAC4H,KAAK,CAAE,CAAEqB,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAChC,CAAC,CACT,CAAC,cACNtJ,IAAA,CAACvB,GAAG,EAAC8L,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAzC,QAAA,cACzBhI,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EAAA/C,QAAA,cACRlI,KAAA,CAAC9B,KAAK,EAAAgK,QAAA,eACJhI,IAAA,CAACjC,MAAM,EAACuK,IAAI,CAAC,SAAS,CAAC4C,QAAQ,CAAC,QAAQ,CAACnC,IAAI,cAAE/I,IAAA,CAAClB,cAAc,GAAE,CAAE,CAAAkJ,QAAA,CAAC,cAEnE,CAAQ,CAAC,cACThI,IAAA,CAACjC,MAAM,EAACmM,OAAO,CAAE/D,WAAY,CAAC4C,IAAI,cAAE/I,IAAA,CAACX,cAAc,GAAE,CAAE,CAAA2I,QAAA,CAAC,cAExD,CAAQ,CAAC,EACJ,CAAC,CACC,CAAC,CACT,CAAC,EACH,CAAC,CACF,CAAC,CACH,CAAC,cAGPhI,IAAA,CAACnC,IAAI,EAACsM,SAAS,CAAC,aAAa,CAACF,IAAI,CAAC,OAAO,CAAAjC,QAAA,cACxClI,KAAA,CAACtB,GAAG,EAAC2M,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAAApD,QAAA,eACzChI,IAAA,CAACvB,GAAG,EAAAuJ,QAAA,cACFhI,IAAA,CAACjC,MAAM,EACLuK,IAAI,CAAC,SAAS,CACdS,IAAI,cAAE/I,IAAA,CAACnB,YAAY,GAAE,CAAE,CACvBqL,OAAO,CAAE7D,SAAU,CAAA2B,QAAA,CACpB,gCAED,CAAQ,CAAC,CACN,CAAC,cACNhI,IAAA,CAACvB,GAAG,EAAAuJ,QAAA,cACFhI,IAAA,CAACjC,MAAM,EACLgL,IAAI,cAAE/I,IAAA,CAACX,cAAc,GAAE,CAAE,CACzB6K,OAAO,CAAE3G,aAAc,CACvB3C,OAAO,CAAEA,OAAQ,CAAAoH,QAAA,CAClB,cAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACF,CAAC,cAGPhI,IAAA,CAACnC,IAAI,EAAAmK,QAAA,cACHhI,IAAA,CAAClC,KAAK,EACJqL,OAAO,CAAEA,OAAQ,CACjBkC,UAAU,CAAE3K,QAAS,CACrB4K,MAAM,CAAC,IAAI,CACX1K,OAAO,CAAEA,OAAQ,CACjB2K,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,OAAO,CAAE1K,WAAW,CAACE,IAAI,CACzBC,QAAQ,CAAEH,WAAW,CAACG,QAAQ,CAC9BL,KAAK,CAAEA,KAAK,CACZ6K,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAAC/K,KAAK,CAAEgL,KAAK,aAAAxG,MAAA,CACjBwG,KAAK,CAAC,CAAC,CAAC,MAAAxG,MAAA,CAAIwG,KAAK,CAAC,CAAC,CAAC,oBAAAxG,MAAA,CAAQxE,KAAK,WAAI,CAC5CiL,QAAQ,CAAEA,CAAC7K,IAAI,CAAEC,QAAQ,GAAK,CAC5BF,cAAc,CAAA+C,aAAA,CAAAA,aAAA,IACThD,WAAW,MACdE,IAAI,CACJC,QAAQ,CAAEA,QAAQ,EAAI,EAAE,EACzB,CAAC,CACJ,CACF,CAAE,CACH,CAAC,CACE,CAAC,cAGPnB,IAAA,CAAC5B,KAAK,EACJ6F,KAAK,cACHnE,KAAA,QAAKmI,KAAK,CAAE,CAAE+D,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAM,CAAE,CAAAlE,QAAA,EAC/DxG,cAAc,cAAGxB,IAAA,CAAChB,YAAY,GAAE,CAAC,cAAGgB,IAAA,CAACnB,YAAY,GAAE,CAAC,cACrDmB,IAAA,SAAAgI,QAAA,CAAOxG,cAAc,qCAAA8D,MAAA,CAAc9D,cAAc,CAACyC,KAAK,EAAK,OAAO,CAAO,CAAC,EACxE,CACN,CACDkI,IAAI,CAAE/K,cAAe,CACrBgL,QAAQ,CAAEA,CAAA,GAAM,CACd,GAAIxK,MAAM,CAAE,CACVtD,OAAO,CAAC+N,OAAO,CAAC,cAAc,CAAC,CAC/B,OACF,CACAhL,iBAAiB,CAAC,KAAK,CAAC,CACxBiB,IAAI,CAAC8D,WAAW,CAAC,CAAC,CAClB3E,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CACF6K,MAAM,CAAE,IAAK,CACbhD,KAAK,CAAE,GAAI,CACXiD,cAAc,MACdC,YAAY,CAAE,CAAC5K,MAAO,CACtB6K,QAAQ,CAAE,CAAC7K,MAAO,CAAAoG,QAAA,cAElBlI,KAAA,CAACzB,IAAI,EACHiE,IAAI,CAAEA,IAAK,CACXsI,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAEzD,UAAW,CACrB0D,YAAY,CAAC,KAAK,CAAA9C,QAAA,eAElBhI,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EACR7G,IAAI,CAAC,OAAO,CACZlB,KAAK,CAAC,gCAAO,CACb0J,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAErO,OAAO,CAAE,UAAW,CAAC,CACvC,CAAEsO,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAG,CAAEvO,OAAO,CAAE,eAAgB,CAAC,CAC9C,CAAA0J,QAAA,cAEFhI,IAAA,CAAC/B,KAAK,EACJ+M,WAAW,CAAC,kDAAU,CACtB8B,SAAS,MACTC,SAAS,CAAE,GAAI,CAChB,CAAC,CACO,CAAC,cAEZ/M,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EACR7G,IAAI,CAAC,aAAa,CAClBlB,KAAK,CAAC,gCAAO,CACb0J,KAAK,CAAE,CACL,CAAEG,GAAG,CAAE,GAAG,CAAEvO,OAAO,CAAE,cAAe,CAAC,CACrC,CAAA0J,QAAA,cAEFhI,IAAA,CAAC/B,KAAK,CAAC+O,QAAQ,EACbhC,WAAW,CAAC,kDAAU,CACtBiC,IAAI,CAAE,CAAE,CACRH,SAAS,MACTC,SAAS,CAAE,GAAI,CAChB,CAAC,CACO,CAAC,cAEZ/M,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EACR7G,IAAI,CAAC,WAAW,CAChBlB,KAAK,CAAC,0BAAM,CACZ0J,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAErO,OAAO,CAAE,SAAU,CAAC,CACtC,CACE4O,SAAS,CAAEA,CAAClD,CAAC,CAAEU,KAAK,GAAK,CACvB,GAAI,CAACA,KAAK,EAAI,CAACA,KAAK,CAAC,CAAC,CAAC,EAAI,CAACA,KAAK,CAAC,CAAC,CAAC,CAAE,CACpC,MAAO,CAAAyC,OAAO,CAACC,OAAO,CAAC,CAAC,CAC1B,CACA,KAAM,CAACC,KAAK,CAAEC,GAAG,CAAC,CAAG5C,KAAK,CAC1B,KAAM,CAAA6C,GAAG,CAAG,GAAI,CAAA1D,IAAI,CAAC,CAAC,CAEtB;AACA,GAAIwD,KAAK,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAE,CACvB,MAAO,CAAAJ,OAAO,CAACM,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAClD,CAEA;AACA,GAAIJ,GAAG,CAACE,QAAQ,CAACH,KAAK,CAAC,CAAE,CACvB,MAAO,CAAAF,OAAO,CAACM,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAClD,CAEA;AACA,KAAM,CAAAjF,QAAQ,CAAG6E,GAAG,CAACK,IAAI,CAACN,KAAK,CAAE,SAAS,CAAC,CAC3C,GAAI5E,QAAQ,CAAG,EAAE,CAAE,CACjB,MAAO,CAAA0E,OAAO,CAACM,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAClD,CAEA;AACA,GAAIjF,QAAQ,CAAG,EAAE,CAAG,EAAE,CAAE,CACtB,MAAO,CAAA0E,OAAO,CAACM,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAClD,CAEA,MAAO,CAAAP,OAAO,CAACC,OAAO,CAAC,CAAC,CAC1B,CACF,CAAC,CACD,CACFQ,KAAK,CAAC,0FAAoB,CAAA5F,QAAA,cAE1BhI,IAAA,CAACK,WAAW,EACVwN,QAAQ,CAAE,CACRC,MAAM,CAAE,OAAO,CACfC,UAAU,CAAE,EAAI;AAClB,CAAE,CACFD,MAAM,CAAC,kBAAkB,CACzB7F,KAAK,CAAE,CAAEqB,KAAK,CAAE,MAAO,CAAE,CACzB0B,WAAW,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAAE,CAClCgD,YAAY,CAAGtC,OAAO,EAAK,CACzB;AACA,MAAO,CAAAA,OAAO,EAAIA,OAAO,CAAC8B,QAAQ,CAAC,GAAI,CAAA3D,IAAI,CAAC,CAAC,CAAE,KAAK,CAAC,CACvD,CAAE,CACFoE,YAAY,CAAEA,CAACvC,OAAO,CAAEpD,IAAI,GAAK,CAC/B,KAAM,CAAAiF,GAAG,CAAG,GAAI,CAAA1D,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAqE,OAAO,CAAGxC,OAAO,EAAIA,OAAO,CAACyC,MAAM,CAACZ,GAAG,CAAE,KAAK,CAAC,CAErD,GAAIjF,IAAI,GAAK,OAAO,EAAI4F,OAAO,CAAE,CAC/B;AACA,MAAO,CACLE,aAAa,CAAEA,CAAA,GAAM,CACnB,KAAM,CAAAC,KAAK,CAAG,EAAE,CAChB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,GAAG,CAACgB,QAAQ,CAAC,CAAC,CAAED,CAAC,EAAE,CAAE,CACvCD,KAAK,CAACG,IAAI,CAACF,CAAC,CAAC,CACf,CACA,MAAO,CAAAD,KAAK,CACd,CAAC,CACDI,eAAe,CAAGC,YAAoB,EAAK,CACzC,GAAIA,YAAY,GAAKnB,GAAG,CAACgB,QAAQ,CAAC,CAAC,CAAE,CACnC,KAAM,CAAAI,OAAO,CAAG,EAAE,CAClB,IAAK,GAAI,CAAAL,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,GAAG,CAACqB,UAAU,CAAC,CAAC,CAAEN,CAAC,EAAE,CAAE,CACzCK,OAAO,CAACH,IAAI,CAACF,CAAC,CAAC,CACjB,CACA,MAAO,CAAAK,OAAO,CAChB,CACA,MAAO,EAAE,CACX,CACF,CAAC,CACH,CACA,MAAO,CAAC,CAAC,CACX,CAAE,CACFE,OAAO,CAAE,KAAM,CACf5D,UAAU,CAAE,KAAM,CACnB,CAAC,CACO,CAAC,cAEZjL,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EACR7G,IAAI,CAAC,UAAU,CACflB,KAAK,CAAC,0BAAM,CACZ0J,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAErO,OAAO,CAAE,SAAU,CAAC,CACtC,CAAEsO,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAG,CAAEvO,OAAO,CAAE,eAAgB,CAAC,CAC9C,CAAA0J,QAAA,cAEFhI,IAAA,CAAC/B,KAAK,EACJ+M,WAAW,CAAC,4CAAS,CACrB8B,SAAS,MACTC,SAAS,CAAE,GAAI,CAChB,CAAC,CACO,CAAC,cAEZ/M,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EACR7G,IAAI,CAAC,cAAc,CACnBlB,KAAK,CAAC,oBAAK,CACX0J,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAErO,OAAO,CAAE,QAAS,CAAC,CACrC,CAAA0J,QAAA,cAEFhI,IAAA,CAAC9B,MAAM,EACL8M,WAAW,CAAC,sCAAQ,CACpB8D,UAAU,MACVC,gBAAgB,CAAC,UAAU,CAC3BC,YAAY,CAAEA,CAACC,KAAK,CAAEC,MAAM,QAAAC,gBAAA,OAC1B,CAAAC,MAAM,EAAAD,gBAAA,CAACD,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAElH,QAAQ,UAAAmH,gBAAA,UAAAA,gBAAA,CAAI,EAAE,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,KAAK,CAACI,WAAW,CAAC,CAAC,CAAC,EAC3E,CAAArH,QAAA,CAEA5F,WAAW,CAAC0B,GAAG,CAACyL,UAAU,eACzBvP,IAAA,CAACI,MAAM,EAAqBsK,KAAK,CAAE6E,UAAU,CAAC1J,EAAG,CAAAmC,QAAA,CAC9CuH,UAAU,CAACrL,IAAI,EADLqL,UAAU,CAAC1J,EAEhB,CACT,CAAC,CACI,CAAC,CACA,CAAC,cAGZ7F,IAAA,CAACR,WAAW,EAAC8I,IAAI,CAAC,OAAO,CAAChK,OAAO,CAAEmE,SAAU,CAAC+M,OAAO,CAAE,CAAC,CAAC/M,SAAU,CAAE,CAAC,cACtEzC,IAAA,CAACR,WAAW,EAAC8I,IAAI,CAAC,SAAS,CAAChK,OAAO,CAAEoE,WAAY,CAAC8M,OAAO,CAAE,CAAC,CAAC9M,WAAY,CAAE,CAAC,cAE5E1C,IAAA,CAAC3B,IAAI,CAAC0M,IAAI,EAAC9C,KAAK,CAAE,CAAEqC,YAAY,CAAE,CAAC,CAAEmF,SAAS,CAAE,MAAO,CAAE,CAAAzH,QAAA,cACvDlI,KAAA,QAAKmI,KAAK,CAAE,CACVyH,SAAS,CAAE,mBAAmB,CAC9BC,UAAU,CAAE,MAAM,CAClB3D,OAAO,CAAE,MAAM,CACf4D,cAAc,CAAE,eAAe,CAC/B3D,UAAU,CAAE,QACd,CAAE,CAAAjE,QAAA,eACAhI,IAAA,QAAKiI,KAAK,CAAE,CAAEhF,KAAK,CAAE,MAAM,CAAEiF,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAC7CxG,cAAc,CAAG,gBAAgB,CAAG,iBAAiB,CACnD,CAAC,cACN1B,KAAA,CAAC9B,KAAK,EAAAgK,QAAA,eACJhI,IAAA,CAACjC,MAAM,EACLmM,OAAO,CAAEA,CAAA,GAAM,CACb,GAAItI,MAAM,CAAE,CACVtD,OAAO,CAAC+N,OAAO,CAAC,cAAc,CAAC,CAC/B,OACF,CACAhL,iBAAiB,CAAC,KAAK,CAAC,CACxBiB,IAAI,CAAC8D,WAAW,CAAC,CAAC,CAClB3E,iBAAiB,CAAC,IAAI,CAAC,CACvBoB,gBAAgB,CAAC,CAAC,CACpB,CAAE,CACFgN,QAAQ,CAAEjO,MAAO,CACjBqI,IAAI,CAAC,QAAQ,CAAAjC,QAAA,CACd,cAED,CAAQ,CAAC,cACThI,IAAA,CAACjC,MAAM,EACLuK,IAAI,CAAC,SAAS,CACd4C,QAAQ,CAAC,QAAQ,CACjBtK,OAAO,CAAEgB,MAAO,CAChBiO,QAAQ,CAAEjO,MAAO,CACjBqI,IAAI,CAAC,QAAQ,CACblB,IAAI,CAAEvH,cAAc,cAAGxB,IAAA,CAAChB,YAAY,GAAE,CAAC,cAAGgB,IAAA,CAACnB,YAAY,GAAE,CAAE,CAAAmJ,QAAA,CAE1DpG,MAAM,CAAG,QAAQ,CAAIJ,cAAc,CAAG,OAAO,CAAG,OAAQ,CACnD,CAAC,EACJ,CAAC,EACL,CAAC,CACG,CAAC,EACR,CAAC,CACF,CAAC,cAGRxB,IAAA,CAAC5B,KAAK,EACJ6F,KAAK,CAAC,gCAAO,CACbkI,IAAI,CAAE7K,kBAAmB,CACzB8K,QAAQ,CAAEA,CAAA,GAAM,CACd7K,qBAAqB,CAAC,KAAK,CAAC,CAC5BI,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CACF2K,MAAM,CAAE,cACNtM,IAAA,CAACjC,MAAM,EAAamM,OAAO,CAAEA,CAAA,GAAM,CACjC3I,qBAAqB,CAAC,KAAK,CAAC,CAC5BI,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CAAAqG,QAAA,CAAC,cAEH,EALY,OAKJ,CAAC,CACT,CACFsB,KAAK,CAAE,GAAI,CAAAtB,QAAA,CAEVtG,cAAc,eACb1B,IAAA,QAAKiI,KAAK,CAAE,CAAE6H,OAAO,CAAE,QAAS,CAAE,CAAA9H,QAAA,cAChClI,KAAA,CAACtB,GAAG,EAAC6L,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAArC,QAAA,eACpBhI,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,EAAG,CAAA/H,QAAA,cACZlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,sCAAM,CAAQ,CAAC,cACvBhI,IAAA,QAAKiI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAE,CAAE,CAAAzH,QAAA,CAAEtG,cAAc,CAACuC,KAAK,CAAM,CAAC,EACvD,CAAC,CACH,CAAC,cACNjE,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,EAAG,CAAA/H,QAAA,cACZlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBhI,IAAA,QAAKiI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAE,CAAE,CAAAzH,QAAA,cAC3BhI,IAAA,CAAC7B,GAAG,EAAC8E,KAAK,CAAE,EAAAzC,qBAAA,CAAAsC,gBAAgB,CAACpB,cAAc,CAACmD,MAAM,CAAC,UAAArE,qBAAA,iBAAvCA,qBAAA,CAAyCyC,KAAK,GAAI,SAAU,CAAA+E,QAAA,CACrE,EAAAvH,sBAAA,CAAAqC,gBAAgB,CAACpB,cAAc,CAACmD,MAAM,CAAC,UAAApE,sBAAA,iBAAvCA,sBAAA,CAAyCuC,KAAK,GAAI,IAAI,CACpD,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cACNhD,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,EAAG,CAAA/H,QAAA,cACZlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBhI,IAAA,QAAKiI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAC,CAAEK,OAAO,CAAE,KAAK,CAAEE,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAjI,QAAA,CAC3FtG,cAAc,CAACoF,WAAW,EAAI,MAAM,CAClC,CAAC,EACH,CAAC,CACH,CAAC,cACN9G,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,EAAG,CAAA/H,QAAA,cACZlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBhI,IAAA,QAAKiI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAE,CAAE,CAAAzH,QAAA,CAAEtG,cAAc,CAACqF,QAAQ,EAAI,QAAQ,CAAM,CAAC,EACtE,CAAC,CACH,CAAC,cACN/G,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,EAAG,CAAA/H,QAAA,cACZlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,0BAAI,CAAQ,CAAC,cACrBhI,IAAA,QAAKiI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAE,CAAE,CAAAzH,QAAA,CAAEtG,cAAc,CAAC6C,WAAW,CAAM,CAAC,EAC7D,CAAC,CACH,CAAC,cACNvE,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,EAAG,CAAA/H,QAAA,cACZlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBhI,IAAA,QAAKiI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAE,CAAE,CAAAzH,QAAA,CAAE,GAAI,CAAA6B,IAAI,CAACnI,cAAc,CAACsF,SAAS,CAAC,CAAC8C,cAAc,CAAC,CAAC,CAAM,CAAC,EACtF,CAAC,CACH,CAAC,cACN9J,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,EAAG,CAAA/H,QAAA,cACZlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBhI,IAAA,QAAKiI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAE,CAAE,CAAAzH,QAAA,CAAE,GAAI,CAAA6B,IAAI,CAACnI,cAAc,CAACuF,OAAO,CAAC,CAAC6C,cAAc,CAAC,CAAC,CAAM,CAAC,EACpF,CAAC,CACH,CAAC,cACN9J,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,CAAE,CAAA/H,QAAA,cACXlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBlI,KAAA,QAAKmI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAC,CAAEvH,QAAQ,CAAE,MAAM,CAAEjF,KAAK,CAAE,SAAU,CAAE,CAAA+E,QAAA,EAAEtG,cAAc,CAACyC,UAAU,CAAC,QAAC,EAAK,CAAC,EACjG,CAAC,CACH,CAAC,cACNnE,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,CAAE,CAAA/H,QAAA,cACXlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBlI,KAAA,QAAKmI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAC,CAAEvH,QAAQ,CAAE,MAAM,CAAEjF,KAAK,CAAE,SAAU,CAAE,CAAA+E,QAAA,EAAEtG,cAAc,CAAC4C,SAAS,CAAC,QAAC,EAAK,CAAC,EAChG,CAAC,CACH,CAAC,cACNtE,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,CAAE,CAAA/H,QAAA,cACXlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBlI,KAAA,QAAKmI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAC,CAAEvH,QAAQ,CAAE,MAAM,CAAEjF,KAAK,CAAE,SAAU,CAAE,CAAA+E,QAAA,EAAEtG,cAAc,CAAC2C,gBAAgB,CAAC,QAAC,EAAK,CAAC,EACvG,CAAC,CACH,CAAC,cACNrE,IAAA,CAACvB,GAAG,EAACsR,IAAI,CAAE,EAAG,CAAA/H,QAAA,cACZlI,KAAA,QAAKmI,KAAK,CAAE,CAAEqC,YAAY,CAAE,EAAG,CAAE,CAAAtC,QAAA,eAC/BhI,IAAA,WAAAgI,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBlI,KAAA,QAAKmI,KAAK,CAAE,CAAEwH,SAAS,CAAE,CAAC,CAAEvH,QAAQ,CAAE,MAAM,CAAEjF,KAAK,CAAE,MAAM,CAAE+F,UAAU,CAAE,MAAO,CAAE,CAAAhB,QAAA,EAAC,MAChF,CAACtG,cAAc,CAAC0C,WAAW,CAACwF,OAAO,CAAC,CAAC,CAAC,EACpC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAArJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}