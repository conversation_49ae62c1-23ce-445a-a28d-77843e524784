{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx\";\nimport React from 'react';\nimport { Layout, Dropdown, Badge, Space } from 'antd';\nimport { BellOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Header\n} = Layout;\nconst HeaderComponent = ({\n  collapsed,\n  toggle\n}) => {\n  var _user;\n  const notificationMenuItems = [{\n    key: 'notification1',\n    label: '系统通知：新版本已发布'\n  }, {\n    key: 'notification2',\n    label: '业务通知：有新的拍卖会已创建'\n  }, {\n    key: 'notification3',\n    label: '提醒：今日有3个订单待处理'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'all',\n    label: '查看全部通知'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"site-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: /*#__PURE__*/React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n          className: 'trigger',\n          onClick: toggle\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: notificationMenuItems\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Badge, {\n              count: 5,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(BellOutlined, {\n                className: \"header-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"username\",\n                children: ((_user = user) === null || _user === void 0 ? void 0 : _user.username) || '用户'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u786E\\u8BA4\\u767B\\u51FA\",\n      open: logoutModalVisible,\n      onOk: handleLogout,\n      onCancel: () => setLogoutModalVisible(false),\n      okText: \"\\u786E\\u8BA4\\u767B\\u51FA\",\n      cancelText: \"\\u53D6\\u6D88\",\n      okType: \"danger\",\n      confirmLoading: logoutLoading,\n      centered: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u60A8\\u786E\\u5B9A\\u8981\\u9000\\u51FA\\u767B\\u5F55\\u5417\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: \"\\u9000\\u51FA\\u540E\\u9700\\u8981\\u91CD\\u65B0\\u767B\\u5F55\\u624D\\u80FD\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u529F\\u80FD\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = HeaderComponent;\nexport default HeaderComponent;\nvar _c;\n$RefreshReg$(_c, \"HeaderComponent\");", "map": {"version": 3, "names": ["React", "Layout", "Dropdown", "Badge", "Space", "BellOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "HeaderComponent", "collapsed", "toggle", "_user", "notificationMenuItems", "key", "label", "type", "children", "className", "createElement", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "menu", "items", "placement", "count", "userMenuItems", "Avatar", "icon", "UserOutlined", "user", "username", "Modal", "title", "open", "logoutModalVisible", "onOk", "handleLogout", "onCancel", "setLogoutModalVisible", "okText", "cancelText", "okType", "confirmLoading", "logoutLoading", "centered", "style", "padding", "color", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { Layout, Dropdown, Badge, Space } from 'antd';\nimport {\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n} from '@ant-design/icons';\nimport UserMenu from '../UserMenu';\nimport './index.css';\n\nconst { Header } = Layout;\n\ninterface HeaderProps {\n  collapsed: boolean;\n  toggle: () => void;\n}\n\nconst HeaderComponent: React.FC<HeaderProps> = ({ collapsed, toggle }) => {\n\n  const notificationMenuItems = [\n    {\n      key: 'notification1',\n      label: '系统通知：新版本已发布',\n    },\n    {\n      key: 'notification2',\n      label: '业务通知：有新的拍卖会已创建',\n    },\n    {\n      key: 'notification3',\n      label: '提醒：今日有3个订单待处理',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'all',\n      label: '查看全部通知',\n    },\n  ];\n\n  return (\n    <>\n      <Header className=\"site-header\">\n        <div className=\"header-left\">\n          {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n            className: 'trigger',\n            onClick: toggle,\n          })}\n        </div>\n        <div className=\"header-right\">\n          <Space size=\"large\">\n            <Dropdown menu={{ items: notificationMenuItems }} placement=\"bottomRight\">\n              <Badge count={5} size=\"small\">\n                <BellOutlined className=\"header-icon\" />\n              </Badge>\n            </Dropdown>\n            <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n              <Space>\n                <Avatar icon={<UserOutlined />} />\n                <span className=\"username\">{user?.username || '用户'}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </div>\n      </Header>\n\n      {/* 登出确认弹窗 */}\n      <Modal\n        title=\"确认登出\"\n        open={logoutModalVisible}\n        onOk={handleLogout}\n        onCancel={() => setLogoutModalVisible(false)}\n        okText=\"确认登出\"\n        cancelText=\"取消\"\n        okType=\"danger\"\n        confirmLoading={logoutLoading}\n        centered\n      >\n        <div style={{ padding: '20px 0' }}>\n          <p>您确定要退出登录吗？</p>\n          <p style={{ color: '#666', fontSize: '14px' }}>\n            退出后需要重新登录才能继续使用系统功能。\n          </p>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default HeaderComponent;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACrD,SACEC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,QACb,mBAAmB;AAE1B,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,MAAM;EAAEC;AAAO,CAAC,GAAGX,MAAM;AAOzB,MAAMY,eAAsC,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAA,IAAAC,KAAA;EAExE,MAAMC,qBAAqB,GAAG,CAC5B;IACEC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACEC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEV,OAAA,CAAAE,SAAA;IAAAU,QAAA,gBACEZ,OAAA,CAACG,MAAM;MAACU,SAAS,EAAC,aAAa;MAAAD,QAAA,gBAC7BZ,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAD,QAAA,eACzBrB,KAAK,CAACuB,aAAa,CAACT,SAAS,GAAGP,kBAAkB,GAAGD,gBAAgB,EAAE;UACtEgB,SAAS,EAAE,SAAS;UACpBE,OAAO,EAAET;QACX,CAAC;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnB,OAAA;QAAKa,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC3BZ,OAAA,CAACL,KAAK;UAACyB,IAAI,EAAC,OAAO;UAAAR,QAAA,gBACjBZ,OAAA,CAACP,QAAQ;YAAC4B,IAAI,EAAE;cAAEC,KAAK,EAAEd;YAAsB,CAAE;YAACe,SAAS,EAAC,aAAa;YAAAX,QAAA,eACvEZ,OAAA,CAACN,KAAK;cAAC8B,KAAK,EAAE,CAAE;cAACJ,IAAI,EAAC,OAAO;cAAAR,QAAA,eAC3BZ,OAAA,CAACJ,YAAY;gBAACiB,SAAS,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACXnB,OAAA,CAACP,QAAQ;YAAC4B,IAAI,EAAE;cAAEC,KAAK,EAAEG;YAAc,CAAE;YAACF,SAAS,EAAC,aAAa;YAAAX,QAAA,eAC/DZ,OAAA,CAACL,KAAK;cAAAiB,QAAA,gBACJZ,OAAA,CAAC0B,MAAM;gBAACC,IAAI,eAAE3B,OAAA,CAAC4B,YAAY;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCnB,OAAA;gBAAMa,SAAS,EAAC,UAAU;gBAAAD,QAAA,EAAE,EAAAL,KAAA,GAAAsB,IAAI,cAAAtB,KAAA,uBAAJA,KAAA,CAAMuB,QAAQ,KAAI;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTnB,OAAA,CAAC+B,KAAK;MACJC,KAAK,EAAC,0BAAM;MACZC,IAAI,EAAEC,kBAAmB;MACzBC,IAAI,EAAEC,YAAa;MACnBC,QAAQ,EAAEA,CAAA,KAAMC,qBAAqB,CAAC,KAAK,CAAE;MAC7CC,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfC,MAAM,EAAC,QAAQ;MACfC,cAAc,EAAEC,aAAc;MAC9BC,QAAQ;MAAAhC,QAAA,eAERZ,OAAA;QAAK6C,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAlC,QAAA,gBAChCZ,OAAA;UAAAY,QAAA,EAAG;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjBnB,OAAA;UAAG6C,KAAK,EAAE;YAAEE,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAApC,QAAA,EAAC;QAE/C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAAC8B,EAAA,GAvEI7C,eAAsC;AAyE5C,eAAeA,eAAe;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}