{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nexport default function useCheckable(cascaderPrefixCls, multiple) {\n  return React.useMemo(() => multiple ? /*#__PURE__*/React.createElement(\"span\", {\n    className: `${cascaderPrefixCls}-checkbox-inner`\n  }) : false, [multiple]);\n}", "map": {"version": 3, "names": ["React", "useCheckable", "cascaderPrefixCls", "multiple", "useMemo", "createElement", "className"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/cascader/hooks/useCheckable.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nexport default function useCheckable(cascaderPrefixCls, multiple) {\n  return React.useMemo(() => multiple ? /*#__PURE__*/React.createElement(\"span\", {\n    className: `${cascaderPrefixCls}-checkbox-inner`\n  }) : false, [multiple]);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,iBAAiB,EAAEC,QAAQ,EAAE;EAChE,OAAOH,KAAK,CAACI,OAAO,CAAC,MAAMD,QAAQ,GAAG,aAAaH,KAAK,CAACK,aAAa,CAAC,MAAM,EAAE;IAC7EC,SAAS,EAAE,GAAGJ,iBAAiB;EACjC,CAAC,CAAC,GAAG,KAAK,EAAE,CAACC,QAAQ,CAAC,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}