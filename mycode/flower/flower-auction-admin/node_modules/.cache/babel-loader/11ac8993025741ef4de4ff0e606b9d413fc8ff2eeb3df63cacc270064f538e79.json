{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport cls from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport Select from '../select';\nconst {\n  Option\n} = Select;\nfunction isSelectOptionOrSelectOptGroup(child) {\n  return (child === null || child === void 0 ? void 0 : child.type) && (child.type.isSelectOption || child.type.isSelectOptGroup);\n}\nconst AutoComplete = (props, ref) => {\n  var _a, _b;\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    popupClassName,\n    dropdownClassName,\n    children,\n    dataSource,\n    dropdownStyle,\n    dropdownRender,\n    popupRender,\n    onDropdownVisibleChange,\n    onOpenChange,\n    styles,\n    classNames\n  } = props;\n  const childNodes = toArray(children);\n  const mergedPopupStyle = ((_a = styles === null || styles === void 0 ? void 0 : styles.popup) === null || _a === void 0 ? void 0 : _a.root) || dropdownStyle;\n  const mergedPopupClassName = ((_b = classNames === null || classNames === void 0 ? void 0 : classNames.popup) === null || _b === void 0 ? void 0 : _b.root) || popupClassName || dropdownClassName;\n  const mergedPopupRender = popupRender || dropdownRender;\n  const mergedOnOpenChange = onOpenChange || onDropdownVisibleChange;\n  // ============================= Input =============================\n  let customizeInput;\n  if (childNodes.length === 1 && /*#__PURE__*/React.isValidElement(childNodes[0]) && !isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    [customizeInput] = childNodes;\n  }\n  const getInputElement = customizeInput ? () => customizeInput : undefined;\n  // ============================ Options ============================\n  let optionChildren;\n  // [Legacy] convert `children` or `dataSource` into option children\n  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    optionChildren = children;\n  } else {\n    optionChildren = dataSource ? dataSource.map(item => {\n      if (/*#__PURE__*/React.isValidElement(item)) {\n        return item;\n      }\n      switch (typeof item) {\n        case 'string':\n          return /*#__PURE__*/React.createElement(Option, {\n            key: item,\n            value: item\n          }, item);\n        case 'object':\n          {\n            const {\n              value: optionValue\n            } = item;\n            return /*#__PURE__*/React.createElement(Option, {\n              key: optionValue,\n              value: optionValue\n            }, item.text);\n          }\n        default:\n          return undefined;\n      }\n    }) : [];\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('AutoComplete');\n    process.env.NODE_ENV !== \"production\" ? warning(!customizeInput || !('size' in props), 'usage', 'You need to control style self instead of setting `size` when using customize input.') : void 0;\n    const deprecatedProps = {\n      dropdownMatchSelectWidth: 'popupMatchSelectWidth',\n      dropdownStyle: 'styles.popup.root',\n      dropdownClassName: 'classNames.popup.root',\n      popupClassName: 'classNames.popup.root',\n      dropdownRender: 'popupRender',\n      onDropdownVisibleChange: 'onOpenChange',\n      dataSource: 'options'\n    };\n    Object.entries(deprecatedProps).forEach(([oldProp, newProp]) => {\n      warning.deprecated(!(oldProp in props), oldProp, newProp);\n    });\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('select', customizePrefixCls);\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('SelectLike', mergedPopupStyle === null || mergedPopupStyle === void 0 ? void 0 : mergedPopupStyle.zIndex);\n  return /*#__PURE__*/React.createElement(Select, Object.assign({\n    ref: ref,\n    suffixIcon: null\n  }, omit(props, ['dataSource', 'dropdownClassName', 'popupClassName']), {\n    prefixCls: prefixCls,\n    classNames: {\n      popup: {\n        root: mergedPopupClassName\n      },\n      root: classNames === null || classNames === void 0 ? void 0 : classNames.root\n    },\n    styles: {\n      popup: {\n        root: Object.assign(Object.assign({}, mergedPopupStyle), {\n          zIndex\n        })\n      },\n      root: styles === null || styles === void 0 ? void 0 : styles.root\n    },\n    className: cls(`${prefixCls}-auto-complete`, className),\n    mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE,\n    popupRender: mergedPopupRender,\n    onOpenChange: mergedOnOpenChange,\n    // Internal api\n    getInputElement\n  }), optionChildren);\n};\nconst RefAutoComplete = /*#__PURE__*/React.forwardRef(AutoComplete);\nif (process.env.NODE_ENV !== 'production') {\n  RefAutoComplete.displayName = 'AutoComplete';\n}\nexport default RefAutoComplete;", "map": {"version": 3, "names": ["React", "cls", "toArray", "omit", "useZIndex", "devUseW<PERSON>ning", "ConfigContext", "Select", "Option", "isSelectOptionOrSelectOptGroup", "child", "type", "isSelectOption", "isSelectOptGroup", "AutoComplete", "props", "ref", "_a", "_b", "prefixCls", "customizePrefixCls", "className", "popupClassName", "dropdownClassName", "children", "dataSource", "dropdownStyle", "dropdownRender", "popupRender", "onDropdownVisibleChange", "onOpenChange", "styles", "classNames", "childNodes", "mergedPopupStyle", "popup", "root", "mergedPopupClassName", "mergedPopupRender", "mergedOnOpenChange", "customizeInput", "length", "isValidElement", "getInputElement", "undefined", "option<PERSON><PERSON><PERSON>n", "map", "item", "createElement", "key", "value", "optionValue", "text", "process", "env", "NODE_ENV", "warning", "deprecatedProps", "dropdownMatchSelectWidth", "Object", "entries", "for<PERSON>ach", "oldProp", "newProp", "deprecated", "getPrefixCls", "useContext", "zIndex", "assign", "suffixIcon", "mode", "SECRET_COMBOBOX_MODE_DO_NOT_USE", "RefAutoComplete", "forwardRef", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/auto-complete/AutoComplete.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport cls from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport Select from '../select';\nconst {\n  Option\n} = Select;\nfunction isSelectOptionOrSelectOptGroup(child) {\n  return (child === null || child === void 0 ? void 0 : child.type) && (child.type.isSelectOption || child.type.isSelectOptGroup);\n}\nconst AutoComplete = (props, ref) => {\n  var _a, _b;\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    popupClassName,\n    dropdownClassName,\n    children,\n    dataSource,\n    dropdownStyle,\n    dropdownRender,\n    popupRender,\n    onDropdownVisibleChange,\n    onOpenChange,\n    styles,\n    classNames\n  } = props;\n  const childNodes = toArray(children);\n  const mergedPopupStyle = ((_a = styles === null || styles === void 0 ? void 0 : styles.popup) === null || _a === void 0 ? void 0 : _a.root) || dropdownStyle;\n  const mergedPopupClassName = ((_b = classNames === null || classNames === void 0 ? void 0 : classNames.popup) === null || _b === void 0 ? void 0 : _b.root) || popupClassName || dropdownClassName;\n  const mergedPopupRender = popupRender || dropdownRender;\n  const mergedOnOpenChange = onOpenChange || onDropdownVisibleChange;\n  // ============================= Input =============================\n  let customizeInput;\n  if (childNodes.length === 1 && /*#__PURE__*/React.isValidElement(childNodes[0]) && !isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    [customizeInput] = childNodes;\n  }\n  const getInputElement = customizeInput ? () => customizeInput : undefined;\n  // ============================ Options ============================\n  let optionChildren;\n  // [Legacy] convert `children` or `dataSource` into option children\n  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    optionChildren = children;\n  } else {\n    optionChildren = dataSource ? dataSource.map(item => {\n      if (/*#__PURE__*/React.isValidElement(item)) {\n        return item;\n      }\n      switch (typeof item) {\n        case 'string':\n          return /*#__PURE__*/React.createElement(Option, {\n            key: item,\n            value: item\n          }, item);\n        case 'object':\n          {\n            const {\n              value: optionValue\n            } = item;\n            return /*#__PURE__*/React.createElement(Option, {\n              key: optionValue,\n              value: optionValue\n            }, item.text);\n          }\n        default:\n          return undefined;\n      }\n    }) : [];\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('AutoComplete');\n    process.env.NODE_ENV !== \"production\" ? warning(!customizeInput || !('size' in props), 'usage', 'You need to control style self instead of setting `size` when using customize input.') : void 0;\n    const deprecatedProps = {\n      dropdownMatchSelectWidth: 'popupMatchSelectWidth',\n      dropdownStyle: 'styles.popup.root',\n      dropdownClassName: 'classNames.popup.root',\n      popupClassName: 'classNames.popup.root',\n      dropdownRender: 'popupRender',\n      onDropdownVisibleChange: 'onOpenChange',\n      dataSource: 'options'\n    };\n    Object.entries(deprecatedProps).forEach(([oldProp, newProp]) => {\n      warning.deprecated(!(oldProp in props), oldProp, newProp);\n    });\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('select', customizePrefixCls);\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('SelectLike', mergedPopupStyle === null || mergedPopupStyle === void 0 ? void 0 : mergedPopupStyle.zIndex);\n  return /*#__PURE__*/React.createElement(Select, Object.assign({\n    ref: ref,\n    suffixIcon: null\n  }, omit(props, ['dataSource', 'dropdownClassName', 'popupClassName']), {\n    prefixCls: prefixCls,\n    classNames: {\n      popup: {\n        root: mergedPopupClassName\n      },\n      root: classNames === null || classNames === void 0 ? void 0 : classNames.root\n    },\n    styles: {\n      popup: {\n        root: Object.assign(Object.assign({}, mergedPopupStyle), {\n          zIndex\n        })\n      },\n      root: styles === null || styles === void 0 ? void 0 : styles.root\n    },\n    className: cls(`${prefixCls}-auto-complete`, className),\n    mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE,\n    popupRender: mergedPopupRender,\n    onOpenChange: mergedOnOpenChange,\n    // Internal api\n    getInputElement\n  }), optionChildren);\n};\nconst RefAutoComplete = /*#__PURE__*/React.forwardRef(AutoComplete);\nif (process.env.NODE_ENV !== 'production') {\n  RefAutoComplete.displayName = 'AutoComplete';\n}\nexport default RefAutoComplete;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,MAAM,MAAM,WAAW;AAC9B,MAAM;EACJC;AACF,CAAC,GAAGD,MAAM;AACV,SAASE,8BAA8BA,CAACC,KAAK,EAAE;EAC7C,OAAO,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,MAAMD,KAAK,CAACC,IAAI,CAACC,cAAc,IAAIF,KAAK,CAACC,IAAI,CAACE,gBAAgB,CAAC;AACjI;AACA,MAAMC,YAAY,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EACnC,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,cAAc;IACdC,iBAAiB;IACjBC,QAAQ;IACRC,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,WAAW;IACXC,uBAAuB;IACvBC,YAAY;IACZC,MAAM;IACNC;EACF,CAAC,GAAGjB,KAAK;EACT,MAAMkB,UAAU,GAAG/B,OAAO,CAACsB,QAAQ,CAAC;EACpC,MAAMU,gBAAgB,GAAG,CAAC,CAACjB,EAAE,GAAGc,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,KAAK,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,IAAI,KAAKV,aAAa;EAC5J,MAAMW,oBAAoB,GAAG,CAAC,CAACnB,EAAE,GAAGc,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACG,KAAK,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,IAAI,KAAKd,cAAc,IAAIC,iBAAiB;EAClM,MAAMe,iBAAiB,GAAGV,WAAW,IAAID,cAAc;EACvD,MAAMY,kBAAkB,GAAGT,YAAY,IAAID,uBAAuB;EAClE;EACA,IAAIW,cAAc;EAClB,IAAIP,UAAU,CAACQ,MAAM,KAAK,CAAC,IAAI,aAAazC,KAAK,CAAC0C,cAAc,CAACT,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAACxB,8BAA8B,CAACwB,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IACjI,CAACO,cAAc,CAAC,GAAGP,UAAU;EAC/B;EACA,MAAMU,eAAe,GAAGH,cAAc,GAAG,MAAMA,cAAc,GAAGI,SAAS;EACzE;EACA,IAAIC,cAAc;EAClB;EACA,IAAIZ,UAAU,CAACQ,MAAM,IAAIhC,8BAA8B,CAACwB,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IACtEY,cAAc,GAAGrB,QAAQ;EAC3B,CAAC,MAAM;IACLqB,cAAc,GAAGpB,UAAU,GAAGA,UAAU,CAACqB,GAAG,CAACC,IAAI,IAAI;MACnD,IAAI,aAAa/C,KAAK,CAAC0C,cAAc,CAACK,IAAI,CAAC,EAAE;QAC3C,OAAOA,IAAI;MACb;MACA,QAAQ,OAAOA,IAAI;QACjB,KAAK,QAAQ;UACX,OAAO,aAAa/C,KAAK,CAACgD,aAAa,CAACxC,MAAM,EAAE;YAC9CyC,GAAG,EAAEF,IAAI;YACTG,KAAK,EAAEH;UACT,CAAC,EAAEA,IAAI,CAAC;QACV,KAAK,QAAQ;UACX;YACE,MAAM;cACJG,KAAK,EAAEC;YACT,CAAC,GAAGJ,IAAI;YACR,OAAO,aAAa/C,KAAK,CAACgD,aAAa,CAACxC,MAAM,EAAE;cAC9CyC,GAAG,EAAEE,WAAW;cAChBD,KAAK,EAAEC;YACT,CAAC,EAAEJ,IAAI,CAACK,IAAI,CAAC;UACf;QACF;UACE,OAAOR,SAAS;MACpB;IACF,CAAC,CAAC,GAAG,EAAE;EACT;EACA,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGnD,aAAa,CAAC,cAAc,CAAC;IAC7CgD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,CAAChB,cAAc,IAAI,EAAE,MAAM,IAAIzB,KAAK,CAAC,EAAE,OAAO,EAAE,sFAAsF,CAAC,GAAG,KAAK,CAAC;IAChM,MAAM0C,eAAe,GAAG;MACtBC,wBAAwB,EAAE,uBAAuB;MACjDhC,aAAa,EAAE,mBAAmB;MAClCH,iBAAiB,EAAE,uBAAuB;MAC1CD,cAAc,EAAE,uBAAuB;MACvCK,cAAc,EAAE,aAAa;MAC7BE,uBAAuB,EAAE,cAAc;MACvCJ,UAAU,EAAE;IACd,CAAC;IACDkC,MAAM,CAACC,OAAO,CAACH,eAAe,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,OAAO,EAAEC,OAAO,CAAC,KAAK;MAC9DP,OAAO,CAACQ,UAAU,CAAC,EAAEF,OAAO,IAAI/C,KAAK,CAAC,EAAE+C,OAAO,EAAEC,OAAO,CAAC;IAC3D,CAAC,CAAC;EACJ;EACA,MAAM;IACJE;EACF,CAAC,GAAGjE,KAAK,CAACkE,UAAU,CAAC5D,aAAa,CAAC;EACnC,MAAMa,SAAS,GAAG8C,YAAY,CAAC,QAAQ,EAAE7C,kBAAkB,CAAC;EAC5D;EACA,MAAM,CAAC+C,MAAM,CAAC,GAAG/D,SAAS,CAAC,YAAY,EAAE8B,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACiC,MAAM,CAAC;EACrI,OAAO,aAAanE,KAAK,CAACgD,aAAa,CAACzC,MAAM,EAAEoD,MAAM,CAACS,MAAM,CAAC;IAC5DpD,GAAG,EAAEA,GAAG;IACRqD,UAAU,EAAE;EACd,CAAC,EAAElE,IAAI,CAACY,KAAK,EAAE,CAAC,YAAY,EAAE,mBAAmB,EAAE,gBAAgB,CAAC,CAAC,EAAE;IACrEI,SAAS,EAAEA,SAAS;IACpBa,UAAU,EAAE;MACVG,KAAK,EAAE;QACLC,IAAI,EAAEC;MACR,CAAC;MACDD,IAAI,EAAEJ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACI;IAC3E,CAAC;IACDL,MAAM,EAAE;MACNI,KAAK,EAAE;QACLC,IAAI,EAAEuB,MAAM,CAACS,MAAM,CAACT,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,EAAElC,gBAAgB,CAAC,EAAE;UACvDiC;QACF,CAAC;MACH,CAAC;MACD/B,IAAI,EAAEL,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACK;IAC/D,CAAC;IACDf,SAAS,EAAEpB,GAAG,CAAC,GAAGkB,SAAS,gBAAgB,EAAEE,SAAS,CAAC;IACvDiD,IAAI,EAAE/D,MAAM,CAACgE,+BAA+B;IAC5C3C,WAAW,EAAEU,iBAAiB;IAC9BR,YAAY,EAAES,kBAAkB;IAChC;IACAI;EACF,CAAC,CAAC,EAAEE,cAAc,CAAC;AACrB,CAAC;AACD,MAAM2B,eAAe,GAAG,aAAaxE,KAAK,CAACyE,UAAU,CAAC3D,YAAY,CAAC;AACnE,IAAIuC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCiB,eAAe,CAACE,WAAW,GAAG,cAAc;AAC9C;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}