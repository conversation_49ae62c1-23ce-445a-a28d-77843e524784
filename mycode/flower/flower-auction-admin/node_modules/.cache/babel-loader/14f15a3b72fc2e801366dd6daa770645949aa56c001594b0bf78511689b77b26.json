{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Layout, Dropdown, Badge, Space } from 'antd';\nimport { BellOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Header\n} = Layout;\nconst HeaderComponent = ({\n  collapsed,\n  toggle\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n  const handleLogout = async () => {\n    setLogoutLoading(true);\n    try {\n      await logout();\n      message.success('登出成功');\n      // 跳转到登录页\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      message.error('登出失败，请重试');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n    }\n  };\n  const showLogoutModal = () => {\n    setLogoutModalVisible(true);\n  };\n  const handleChangePassword = () => {\n    message.info('修改密码功能开发中...');\n  };\n  const handleProfile = () => {\n    message.info('个人中心功能开发中...');\n  };\n  const handleSettings = () => {\n    message.info('账号设置功能开发中...');\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    label: '个人中心',\n    onClick: handleProfile\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this),\n    label: '账号设置',\n    onClick: handleSettings\n  }, {\n    key: 'change-password',\n    icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this),\n    label: '修改密码',\n    onClick: handleChangePassword\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: showLogoutModal\n  }];\n  const notificationMenuItems = [{\n    key: 'notification1',\n    label: '系统通知：新版本已发布'\n  }, {\n    key: 'notification2',\n    label: '业务通知：有新的拍卖会已创建'\n  }, {\n    key: 'notification3',\n    label: '提醒：今日有3个订单待处理'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'all',\n    label: '查看全部通知'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"site-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: /*#__PURE__*/React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n          className: 'trigger',\n          onClick: toggle\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: notificationMenuItems\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Badge, {\n              count: 5,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(BellOutlined, {\n                className: \"header-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"username\",\n                children: (user === null || user === void 0 ? void 0 : user.username) || '用户'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u786E\\u8BA4\\u767B\\u51FA\",\n      open: logoutModalVisible,\n      onOk: handleLogout,\n      onCancel: () => setLogoutModalVisible(false),\n      okText: \"\\u786E\\u8BA4\\u767B\\u51FA\",\n      cancelText: \"\\u53D6\\u6D88\",\n      okType: \"danger\",\n      confirmLoading: logoutLoading,\n      centered: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u60A8\\u786E\\u5B9A\\u8981\\u9000\\u51FA\\u767B\\u5F55\\u5417\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: \"\\u9000\\u51FA\\u540E\\u9700\\u8981\\u91CD\\u65B0\\u767B\\u5F55\\u624D\\u80FD\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u529F\\u80FD\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(HeaderComponent, \"BxvvLj8dAOpzewwly5/7Spc8GkE=\", true);\n_c = HeaderComponent;\nexport default HeaderComponent;\nvar _c;\n$RefreshReg$(_c, \"HeaderComponent\");", "map": {"version": 3, "names": ["React", "Layout", "Dropdown", "Badge", "Space", "BellOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "HeaderComponent", "collapsed", "toggle", "_s", "user", "logout", "useAuth", "logoutModalVisible", "setLogoutModalVisible", "useState", "logoutLoading", "setLogoutLoading", "handleLogout", "message", "success", "window", "location", "href", "error", "console", "showLogoutModal", "handleChangePassword", "info", "handleProfile", "handleSettings", "userMenuItems", "key", "icon", "UserOutlined", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "onClick", "SettingOutlined", "LockOutlined", "type", "LogoutOutlined", "notificationMenuItems", "children", "className", "createElement", "size", "menu", "items", "placement", "count", "Avatar", "username", "Modal", "title", "open", "onOk", "onCancel", "okText", "cancelText", "okType", "confirmLoading", "centered", "style", "padding", "color", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { Layout, Dropdown, Badge, Space } from 'antd';\nimport {\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n} from '@ant-design/icons';\nimport UserMenu from '../UserMenu';\nimport './index.css';\n\nconst { Header } = Layout;\n\ninterface HeaderProps {\n  collapsed: boolean;\n  toggle: () => void;\n}\n\nconst HeaderComponent: React.FC<HeaderProps> = ({ collapsed, toggle }) => {\n  const { user, logout } = useAuth();\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n\n  const handleLogout = async () => {\n    setLogoutLoading(true);\n    try {\n      await logout();\n      message.success('登出成功');\n      // 跳转到登录页\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      message.error('登出失败，请重试');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n    }\n  };\n\n  const showLogoutModal = () => {\n    setLogoutModalVisible(true);\n  };\n\n  const handleChangePassword = () => {\n    message.info('修改密码功能开发中...');\n  };\n\n  const handleProfile = () => {\n    message.info('个人中心功能开发中...');\n  };\n\n  const handleSettings = () => {\n    message.info('账号设置功能开发中...');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人中心',\n      onClick: handleProfile,\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '账号设置',\n      onClick: handleSettings,\n    },\n    {\n      key: 'change-password',\n      icon: <LockOutlined />,\n      label: '修改密码',\n      onClick: handleChangePassword,\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: showLogoutModal,\n    },\n  ];\n\n  const notificationMenuItems = [\n    {\n      key: 'notification1',\n      label: '系统通知：新版本已发布',\n    },\n    {\n      key: 'notification2',\n      label: '业务通知：有新的拍卖会已创建',\n    },\n    {\n      key: 'notification3',\n      label: '提醒：今日有3个订单待处理',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'all',\n      label: '查看全部通知',\n    },\n  ];\n\n  return (\n    <>\n      <Header className=\"site-header\">\n        <div className=\"header-left\">\n          {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n            className: 'trigger',\n            onClick: toggle,\n          })}\n        </div>\n        <div className=\"header-right\">\n          <Space size=\"large\">\n            <Dropdown menu={{ items: notificationMenuItems }} placement=\"bottomRight\">\n              <Badge count={5} size=\"small\">\n                <BellOutlined className=\"header-icon\" />\n              </Badge>\n            </Dropdown>\n            <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n              <Space>\n                <Avatar icon={<UserOutlined />} />\n                <span className=\"username\">{user?.username || '用户'}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </div>\n      </Header>\n\n      {/* 登出确认弹窗 */}\n      <Modal\n        title=\"确认登出\"\n        open={logoutModalVisible}\n        onOk={handleLogout}\n        onCancel={() => setLogoutModalVisible(false)}\n        okText=\"确认登出\"\n        cancelText=\"取消\"\n        okType=\"danger\"\n        confirmLoading={logoutLoading}\n        centered\n      >\n        <div style={{ padding: '20px 0' }}>\n          <p>您确定要退出登录吗？</p>\n          <p style={{ color: '#666', fontSize: '14px' }}>\n            退出后需要重新登录才能继续使用系统功能。\n          </p>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default HeaderComponent;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACrD,SACEC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,QACb,mBAAmB;AAE1B,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,MAAM;EAAEC;AAAO,CAAC,GAAGX,MAAM;AAOzB,MAAMY,eAAsC,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGC,OAAO,CAAC,CAAC;EAClC,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGF,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAMG,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BD,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMN,MAAM,CAAC,CAAC;MACdQ,OAAO,CAACC,OAAO,CAAC,MAAM,CAAC;MACvB;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCL,OAAO,CAACK,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRP,gBAAgB,CAAC,KAAK,CAAC;MACvBH,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5BZ,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMa,oBAAoB,GAAGA,CAAA,KAAM;IACjCR,OAAO,CAACS,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BV,OAAO,CAACS,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BX,OAAO,CAACS,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMG,aAAa,GAAG,CACpB;IACEC,GAAG,EAAE,SAAS;IACdC,IAAI,eAAE/B,OAAA,CAACgC,YAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEX;EACX,CAAC,EACD;IACEG,GAAG,EAAE,UAAU;IACfC,IAAI,eAAE/B,OAAA,CAACuC,eAAe;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEV;EACX,CAAC,EACD;IACEE,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAE/B,OAAA,CAACwC,YAAY;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEb;EACX,CAAC,EACD;IACEgB,IAAI,EAAE;EACR,CAAC,EACD;IACEX,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAE/B,OAAA,CAAC0C,cAAc;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEd;EACX,CAAC,CACF;EAED,MAAMmB,qBAAqB,GAAG,CAC5B;IACEb,GAAG,EAAE,eAAe;IACpBO,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,eAAe;IACpBO,KAAK,EAAE;EACT,CAAC,EACD;IACEP,GAAG,EAAE,eAAe;IACpBO,KAAK,EAAE;EACT,CAAC,EACD;IACEI,IAAI,EAAE;EACR,CAAC,EACD;IACEX,GAAG,EAAE,KAAK;IACVO,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACErC,OAAA,CAAAE,SAAA;IAAA0C,QAAA,gBACE5C,OAAA,CAACG,MAAM;MAAC0C,SAAS,EAAC,aAAa;MAAAD,QAAA,gBAC7B5C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAD,QAAA,eACzBrD,KAAK,CAACuD,aAAa,CAACzC,SAAS,GAAGP,kBAAkB,GAAGD,gBAAgB,EAAE;UACtEgD,SAAS,EAAE,SAAS;UACpBP,OAAO,EAAEhC;QACX,CAAC;MAAC;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNpC,OAAA;QAAK6C,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC3B5C,OAAA,CAACL,KAAK;UAACoD,IAAI,EAAC,OAAO;UAAAH,QAAA,gBACjB5C,OAAA,CAACP,QAAQ;YAACuD,IAAI,EAAE;cAAEC,KAAK,EAAEN;YAAsB,CAAE;YAACO,SAAS,EAAC,aAAa;YAAAN,QAAA,eACvE5C,OAAA,CAACN,KAAK;cAACyD,KAAK,EAAE,CAAE;cAACJ,IAAI,EAAC,OAAO;cAAAH,QAAA,eAC3B5C,OAAA,CAACJ,YAAY;gBAACiD,SAAS,EAAC;cAAa;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACXpC,OAAA,CAACP,QAAQ;YAACuD,IAAI,EAAE;cAAEC,KAAK,EAAEpB;YAAc,CAAE;YAACqB,SAAS,EAAC,aAAa;YAAAN,QAAA,eAC/D5C,OAAA,CAACL,KAAK;cAAAiD,QAAA,gBACJ5C,OAAA,CAACoD,MAAM;gBAACrB,IAAI,eAAE/B,OAAA,CAACgC,YAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCpC,OAAA;gBAAM6C,SAAS,EAAC,UAAU;gBAAAD,QAAA,EAAE,CAAApC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,QAAQ,KAAI;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTpC,OAAA,CAACsD,KAAK;MACJC,KAAK,EAAC,0BAAM;MACZC,IAAI,EAAE7C,kBAAmB;MACzB8C,IAAI,EAAEzC,YAAa;MACnB0C,QAAQ,EAAEA,CAAA,KAAM9C,qBAAqB,CAAC,KAAK,CAAE;MAC7C+C,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfC,MAAM,EAAC,QAAQ;MACfC,cAAc,EAAEhD,aAAc;MAC9BiD,QAAQ;MAAAnB,QAAA,eAER5C,OAAA;QAAKgE,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAArB,QAAA,gBAChC5C,OAAA;UAAA4C,QAAA,EAAG;QAAU;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjBpC,OAAA;UAAGgE,KAAK,EAAE;YAAEE,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAvB,QAAA,EAAC;QAE/C;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAAC7B,EAAA,CAxIIH,eAAsC;AAAAgE,EAAA,GAAtChE,eAAsC;AA0I5C,eAAeA,eAAe;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}