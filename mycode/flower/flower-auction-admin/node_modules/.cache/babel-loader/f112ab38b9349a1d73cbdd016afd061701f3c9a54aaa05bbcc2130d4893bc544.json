{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/AuthProvider/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setUser, clearUser, setLoading } from '../../store/slices/authSlice';\nimport { authService } from '../../services/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// JWT解析工具函数\nconst parseJWT = token => {\n  try {\n    const base64Url = token.split('.')[1];\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));\n    return JSON.parse(jsonPayload);\n  } catch (error) {\n    console.error('Failed to parse JWT:', error);\n    return null;\n  }\n};\nconst AuthProvider = ({\n  children\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    isAuthenticated,\n    loading,\n    user\n  } = useSelector(state => state.auth);\n  const [isInitializing, setIsInitializing] = useState(true);\n  const initializationRef = useRef(false);\n  useEffect(() => {\n    // 防止重复初始化\n    if (initializationRef.current) {\n      return;\n    }\n    initializationRef.current = true;\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n\n      // 如果没有token，直接清除状态\n      if (!token) {\n        dispatch(clearUser());\n        setIsInitializing(false);\n        return;\n      }\n\n      // 如果已经有用户信息且token存在，跳过API调用\n      if (user && isAuthenticated) {\n        setIsInitializing(false);\n        return;\n      }\n      try {\n        dispatch(setLoading(true));\n\n        // 检查token是否即将过期（提前5分钟刷新）\n        const tokenPayload = parseJWT(token);\n        if (tokenPayload && tokenPayload.exp) {\n          const expirationTime = tokenPayload.exp * 1000; // 转换为毫秒\n          const currentTime = Date.now();\n          const timeUntilExpiry = expirationTime - currentTime;\n\n          // 如果token在5分钟内过期，尝试刷新\n          if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n              try {\n                const refreshResponse = await authService.refreshToken(refreshToken);\n                if (refreshResponse.success && refreshResponse.data) {\n                  localStorage.setItem('token', refreshResponse.data.token);\n                  localStorage.setItem('refreshToken', refreshResponse.data.refreshToken);\n                }\n              } catch (refreshError) {\n                console.warn('Token refresh failed during initialization:', refreshError);\n              }\n            }\n          }\n        }\n\n        // 获取用户信息\n        const response = await authService.getUserInfo();\n        if (response.success && response.data) {\n          dispatch(setUser(response.data));\n        } else {\n          // token无效，清除认证信息\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          dispatch(clearUser());\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        dispatch(clearUser());\n      } finally {\n        dispatch(setLoading(false));\n        setIsInitializing(false);\n      }\n    };\n    initializeAuth();\n  }, []); // 空依赖数组，确保只在组件挂载时执行一次\n\n  // 如果正在初始化，显示加载状态\n  if (isInitializing || loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        flexDirection: 'column',\n        gap: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u6B63\\u5728\\u521D\\u59CB\\u5316...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(AuthProvider, \"3uy+GHL4X51aKafoUMazsaUkPnk=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = AuthProvider;\nexport default AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "setUser", "clearUser", "setLoading", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "parseJWT", "token", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "toString", "slice", "join", "JSON", "parse", "error", "console", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "isAuthenticated", "loading", "user", "state", "auth", "isInitializing", "setIsInitializing", "initializationRef", "current", "initializeAuth", "localStorage", "getItem", "tokenPayload", "exp", "expirationTime", "currentTime", "Date", "now", "timeUntilExpiry", "refreshToken", "refreshResponse", "success", "data", "setItem", "refreshError", "warn", "response", "getUserInfo", "removeItem", "style", "display", "justifyContent", "alignItems", "height", "flexDirection", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/AuthProvider/index.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setUser, clearUser, setLoading } from '../../store/slices/authSlice';\nimport { authService } from '../../services/authService';\nimport { RootState } from '../../store';\n\n// JWT解析工具函数\nconst parseJWT = (token: string) => {\n  try {\n    const base64Url = token.split('.')[1];\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    const jsonPayload = decodeURIComponent(\n      atob(base64)\n        .split('')\n        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))\n        .join('')\n    );\n    return JSON.parse(jsonPayload);\n  } catch (error) {\n    console.error('Failed to parse JWT:', error);\n    return null;\n  }\n};\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nconst AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const dispatch = useDispatch();\n  const { isAuthenticated, loading, user } = useSelector((state: RootState) => state.auth);\n  const [isInitializing, setIsInitializing] = useState(true);\n  const initializationRef = useRef(false);\n\n  useEffect(() => {\n    // 防止重复初始化\n    if (initializationRef.current) {\n      return;\n    }\n    initializationRef.current = true;\n\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n\n      // 如果没有token，直接清除状态\n      if (!token) {\n        dispatch(clearUser());\n        setIsInitializing(false);\n        return;\n      }\n\n      // 如果已经有用户信息且token存在，跳过API调用\n      if (user && isAuthenticated) {\n        setIsInitializing(false);\n        return;\n      }\n\n      try {\n        dispatch(setLoading(true));\n\n        // 检查token是否即将过期（提前5分钟刷新）\n        const tokenPayload = parseJWT(token);\n        if (tokenPayload && tokenPayload.exp) {\n          const expirationTime = tokenPayload.exp * 1000; // 转换为毫秒\n          const currentTime = Date.now();\n          const timeUntilExpiry = expirationTime - currentTime;\n\n          // 如果token在5分钟内过期，尝试刷新\n          if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n              try {\n                const refreshResponse = await authService.refreshToken(refreshToken);\n                if (refreshResponse.success && refreshResponse.data) {\n                  localStorage.setItem('token', refreshResponse.data.token);\n                  localStorage.setItem('refreshToken', refreshResponse.data.refreshToken);\n                }\n              } catch (refreshError) {\n                console.warn('Token refresh failed during initialization:', refreshError);\n              }\n            }\n          }\n        }\n\n        // 获取用户信息\n        const response = await authService.getUserInfo();\n        if (response.success && response.data) {\n          dispatch(setUser(response.data));\n        } else {\n          // token无效，清除认证信息\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          dispatch(clearUser());\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        dispatch(clearUser());\n      } finally {\n        dispatch(setLoading(false));\n        setIsInitializing(false);\n      }\n    };\n\n    initializeAuth();\n  }, []); // 空依赖数组，确保只在组件挂载时执行一次\n\n  // 如果正在初始化，显示加载状态\n  if (isInitializing || loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        flexDirection: 'column',\n        gap: '16px'\n      }}>\n        <div>正在初始化...</div>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n};\n\nexport default AuthProvider;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,EAAEC,SAAS,EAAEC,UAAU,QAAQ,8BAA8B;AAC7E,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGzD;AACA,MAAMC,QAAQ,GAAIC,KAAa,IAAK;EAClC,IAAI;IACF,MAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CACpCC,IAAI,CAACJ,MAAM,CAAC,CACTD,KAAK,CAAC,EAAE,CAAC,CACTM,GAAG,CAAEC,CAAC,IAAK,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CACjEC,IAAI,CAAC,EAAE,CACZ,CAAC;IACD,OAAOC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;EAChC,CAAC,CAAC,OAAOW,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C,OAAO,IAAI;EACb;AACF,CAAC;AAMD,MAAME,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAMC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiC,eAAe;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGlC,WAAW,CAAEmC,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EACxF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM0C,iBAAiB,GAAGzC,MAAM,CAAC,KAAK,CAAC;EAEvCF,SAAS,CAAC,MAAM;IACd;IACA,IAAI2C,iBAAiB,CAACC,OAAO,EAAE;MAC7B;IACF;IACAD,iBAAiB,CAACC,OAAO,GAAG,IAAI;IAEhC,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAM/B,KAAK,GAAGgC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,IAAI,CAACjC,KAAK,EAAE;QACVqB,QAAQ,CAAC7B,SAAS,CAAC,CAAC,CAAC;QACrBoC,iBAAiB,CAAC,KAAK,CAAC;QACxB;MACF;;MAEA;MACA,IAAIJ,IAAI,IAAIF,eAAe,EAAE;QAC3BM,iBAAiB,CAAC,KAAK,CAAC;QACxB;MACF;MAEA,IAAI;QACFP,QAAQ,CAAC5B,UAAU,CAAC,IAAI,CAAC,CAAC;;QAE1B;QACA,MAAMyC,YAAY,GAAGnC,QAAQ,CAACC,KAAK,CAAC;QACpC,IAAIkC,YAAY,IAAIA,YAAY,CAACC,GAAG,EAAE;UACpC,MAAMC,cAAc,GAAGF,YAAY,CAACC,GAAG,GAAG,IAAI,CAAC,CAAC;UAChD,MAAME,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;UAC9B,MAAMC,eAAe,GAAGJ,cAAc,GAAGC,WAAW;;UAEpD;UACA,IAAIG,eAAe,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,IAAIA,eAAe,GAAG,CAAC,EAAE;YAC1D,MAAMC,YAAY,GAAGT,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;YACzD,IAAIQ,YAAY,EAAE;cAChB,IAAI;gBACF,MAAMC,eAAe,GAAG,MAAMhD,WAAW,CAAC+C,YAAY,CAACA,YAAY,CAAC;gBACpE,IAAIC,eAAe,CAACC,OAAO,IAAID,eAAe,CAACE,IAAI,EAAE;kBACnDZ,YAAY,CAACa,OAAO,CAAC,OAAO,EAAEH,eAAe,CAACE,IAAI,CAAC5C,KAAK,CAAC;kBACzDgC,YAAY,CAACa,OAAO,CAAC,cAAc,EAAEH,eAAe,CAACE,IAAI,CAACH,YAAY,CAAC;gBACzE;cACF,CAAC,CAAC,OAAOK,YAAY,EAAE;gBACrB7B,OAAO,CAAC8B,IAAI,CAAC,6CAA6C,EAAED,YAAY,CAAC;cAC3E;YACF;UACF;QACF;;QAEA;QACA,MAAME,QAAQ,GAAG,MAAMtD,WAAW,CAACuD,WAAW,CAAC,CAAC;QAChD,IAAID,QAAQ,CAACL,OAAO,IAAIK,QAAQ,CAACJ,IAAI,EAAE;UACrCvB,QAAQ,CAAC9B,OAAO,CAACyD,QAAQ,CAACJ,IAAI,CAAC,CAAC;QAClC,CAAC,MAAM;UACL;UACAZ,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC;UAChClB,YAAY,CAACkB,UAAU,CAAC,cAAc,CAAC;UACvC7B,QAAQ,CAAC7B,SAAS,CAAC,CAAC,CAAC;QACvB;MACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDgB,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC;QAChClB,YAAY,CAACkB,UAAU,CAAC,cAAc,CAAC;QACvC7B,QAAQ,CAAC7B,SAAS,CAAC,CAAC,CAAC;MACvB,CAAC,SAAS;QACR6B,QAAQ,CAAC5B,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3BmC,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IAEDG,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,IAAIJ,cAAc,IAAIJ,OAAO,EAAE;IAC7B,oBACE3B,OAAA;MAAKuD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE;MACP,CAAE;MAAAtC,QAAA,eACAvB,OAAA;QAAAuB,QAAA,EAAK;MAAQ;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAEV;EAEA,oBAAOjE,OAAA,CAAAE,SAAA;IAAAqB,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CAjGIF,YAAyC;EAAA,QAC5B7B,WAAW,EACeC,WAAW;AAAA;AAAAwE,EAAA,GAFlD5C,YAAyC;AAmG/C,eAAeA,YAAY;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}