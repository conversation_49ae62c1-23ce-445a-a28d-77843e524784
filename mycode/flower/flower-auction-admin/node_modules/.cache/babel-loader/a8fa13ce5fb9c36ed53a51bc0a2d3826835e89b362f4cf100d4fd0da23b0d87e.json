{"ast": null, "code": "import axios from 'axios';\nimport { message } from 'antd';\n\n// 防止重复刷新token的标志\nlet isRefreshing = false;\nlet failedQueue = [];\n\n// 创建axios实例\nexport const apiClient = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1',\n  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napiClient.interceptors.request.use(config => {\n  // 添加认证token\n  const token = localStorage.getItem('token');\n  if (token && config.headers) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 处理失败队列\nconst processQueue = (error, token = null) => {\n  failedQueue.forEach(({\n    resolve,\n    reject\n  }) => {\n    if (error) {\n      reject(error);\n    } else {\n      resolve(token);\n    }\n  });\n  failedQueue = [];\n};\n\n// 响应拦截器\napiClient.interceptors.response.use(response => {\n  return response;\n}, async error => {\n  var _response$data;\n  const originalRequest = error.config;\n  const {\n    response\n  } = error;\n  if (response) {\n    switch (response.status) {\n      case 401:\n        // token过期或无效\n        if (originalRequest._retry) {\n          // 已经重试过，直接跳转登录页\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          if (window.location.pathname !== '/login') {\n            window.location.href = '/login';\n            message.error('登录已过期，请重新登录');\n          }\n          return Promise.reject(error);\n        }\n        if (isRefreshing) {\n          // 正在刷新token，将请求加入队列\n          return new Promise((resolve, reject) => {\n            failedQueue.push({\n              resolve,\n              reject\n            });\n          }).then(token => {\n            originalRequest.headers.Authorization = `Bearer ${token}`;\n            return apiClient(originalRequest);\n          }).catch(err => {\n            return Promise.reject(err);\n          });\n        }\n        originalRequest._retry = true;\n        isRefreshing = true;\n        const refreshToken = localStorage.getItem('refreshToken');\n        if (refreshToken) {\n          try {\n            // 尝试刷新token\n            const refreshResponse = await axios.post(`${process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1'}/auth/refresh`, {\n              refreshToken\n            });\n            if (refreshResponse.data.success) {\n              const newToken = refreshResponse.data.data.token;\n              const newRefreshToken = refreshResponse.data.data.refreshToken;\n\n              // 更新token\n              localStorage.setItem('token', newToken);\n              localStorage.setItem('refreshToken', newRefreshToken);\n\n              // 处理队列中的请求\n              processQueue(null, newToken);\n              isRefreshing = false;\n\n              // 重新发送原请求\n              originalRequest.headers.Authorization = `Bearer ${newToken}`;\n              return apiClient(originalRequest);\n            }\n          } catch (refreshError) {\n            console.error('Token refresh failed:', refreshError);\n            processQueue(refreshError, null);\n          }\n        }\n\n        // 刷新失败，清除认证信息并跳转到登录页\n        isRefreshing = false;\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n\n        // 只有当前不在登录页时才跳转，避免无限重定向\n        if (window.location.pathname !== '/login') {\n          window.location.href = '/login';\n          message.error('登录已过期，请重新登录');\n        }\n        break;\n      case 403:\n        // 权限不足，提供重新登录选项\n        message.error({\n          content: '权限不足，请重新登录',\n          duration: 5,\n          key: 'permission-error',\n          onClick: () => {\n            // 清除认证信息\n            localStorage.removeItem('token');\n            localStorage.removeItem('refreshToken');\n            localStorage.removeItem('user');\n            // 跳转到登录页\n            window.location.href = '/login';\n          }\n        });\n        break;\n      case 404:\n        message.error('请求的资源不存在');\n        break;\n      case 500:\n        message.error('服务器内部错误');\n        break;\n      default:\n        message.error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '请求失败');\n    }\n  } else {\n    // 网络错误\n    message.error('网络连接失败，请检查网络设置');\n  }\n  return Promise.reject(error);\n});\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "message", "isRefreshing", "failedQueue", "apiClient", "create", "baseURL", "process", "env", "REACT_APP_API_BASE_URL", "timeout", "parseInt", "REACT_APP_REQUEST_TIMEOUT", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "processQueue", "for<PERSON>ach", "resolve", "response", "_response$data", "originalRequest", "status", "_retry", "removeItem", "window", "location", "pathname", "href", "push", "then", "catch", "err", "refreshToken", "refreshResponse", "post", "data", "success", "newToken", "newRefreshToken", "setItem", "refreshError", "console", "content", "duration", "key", "onClick"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/apiClient.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\nimport { message } from 'antd';\n\n// 防止重复刷新token的标志\nlet isRefreshing = false;\nlet failedQueue: Array<{\n  resolve: (value?: any) => void;\n  reject: (reason?: any) => void;\n}> = [];\n\n// 创建axios实例\nexport const apiClient: AxiosInstance = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1',\n  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napiClient.interceptors.request.use(\n  (config: InternalAxiosRequestConfig) => {\n    // 添加认证token\n    const token = localStorage.getItem('token');\n    if (token && config.headers) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 处理失败队列\nconst processQueue = (error: any, token: string | null = null) => {\n  failedQueue.forEach(({ resolve, reject }) => {\n    if (error) {\n      reject(error);\n    } else {\n      resolve(token);\n    }\n  });\n\n  failedQueue = [];\n};\n\n// 响应拦截器\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response;\n  },\n  async (error) => {\n    const originalRequest = error.config;\n    const { response } = error;\n\n    if (response) {\n      switch (response.status) {\n        case 401:\n          // token过期或无效\n          if (originalRequest._retry) {\n            // 已经重试过，直接跳转登录页\n            localStorage.removeItem('token');\n            localStorage.removeItem('refreshToken');\n            if (window.location.pathname !== '/login') {\n              window.location.href = '/login';\n              message.error('登录已过期，请重新登录');\n            }\n            return Promise.reject(error);\n          }\n\n          if (isRefreshing) {\n            // 正在刷新token，将请求加入队列\n            return new Promise((resolve, reject) => {\n              failedQueue.push({ resolve, reject });\n            }).then(token => {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return apiClient(originalRequest);\n            }).catch(err => {\n              return Promise.reject(err);\n            });\n          }\n\n          originalRequest._retry = true;\n          isRefreshing = true;\n\n          const refreshToken = localStorage.getItem('refreshToken');\n          if (refreshToken) {\n            try {\n              // 尝试刷新token\n              const refreshResponse = await axios.post(\n                `${process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1'}/auth/refresh`,\n                { refreshToken }\n              );\n\n              if (refreshResponse.data.success) {\n                const newToken = refreshResponse.data.data.token;\n                const newRefreshToken = refreshResponse.data.data.refreshToken;\n\n                // 更新token\n                localStorage.setItem('token', newToken);\n                localStorage.setItem('refreshToken', newRefreshToken);\n\n                // 处理队列中的请求\n                processQueue(null, newToken);\n                isRefreshing = false;\n\n                // 重新发送原请求\n                originalRequest.headers.Authorization = `Bearer ${newToken}`;\n                return apiClient(originalRequest);\n              }\n            } catch (refreshError) {\n              console.error('Token refresh failed:', refreshError);\n              processQueue(refreshError, null);\n            }\n          }\n\n          // 刷新失败，清除认证信息并跳转到登录页\n          isRefreshing = false;\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n\n          // 只有当前不在登录页时才跳转，避免无限重定向\n          if (window.location.pathname !== '/login') {\n            window.location.href = '/login';\n            message.error('登录已过期，请重新登录');\n          }\n          break;\n\n        case 403:\n          // 权限不足，提供重新登录选项\n          message.error({\n            content: '权限不足，请重新登录',\n            duration: 5,\n            key: 'permission-error',\n            onClick: () => {\n              // 清除认证信息\n              localStorage.removeItem('token');\n              localStorage.removeItem('refreshToken');\n              localStorage.removeItem('user');\n              // 跳转到登录页\n              window.location.href = '/login';\n            }\n          });\n          break;\n\n        case 404:\n          message.error('请求的资源不存在');\n          break;\n\n        case 500:\n          message.error('服务器内部错误');\n          break;\n\n        default:\n          message.error(response.data?.message || '请求失败');\n      }\n    } else {\n      // 网络错误\n      message.error('网络连接失败，请检查网络设置');\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport default apiClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAoE,OAAO;AACvF,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AACA,IAAIC,YAAY,GAAG,KAAK;AACxB,IAAIC,WAGF,GAAG,EAAE;;AAEP;AACA,OAAO,MAAMC,SAAwB,GAAGJ,KAAK,CAACK,MAAM,CAAC;EACnDC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,8BAA8B;EAC7EC,OAAO,EAAEC,QAAQ,CAACJ,OAAO,CAACC,GAAG,CAACI,yBAAyB,IAAI,OAAO,CAAC;EACnEC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAT,SAAS,CAACU,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAkC,IAAK;EACtC;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,IAAID,MAAM,CAACJ,OAAO,EAAE;IAC3BI,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGA,CAACH,KAAU,EAAEJ,KAAoB,GAAG,IAAI,KAAK;EAChEf,WAAW,CAACuB,OAAO,CAAC,CAAC;IAAEC,OAAO;IAAEH;EAAO,CAAC,KAAK;IAC3C,IAAIF,KAAK,EAAE;MACTE,MAAM,CAACF,KAAK,CAAC;IACf,CAAC,MAAM;MACLK,OAAO,CAACT,KAAK,CAAC;IAChB;EACF,CAAC,CAAC;EAEFf,WAAW,GAAG,EAAE;AAClB,CAAC;;AAED;AACAC,SAAS,CAACU,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAChCY,QAAuB,IAAK;EAC3B,OAAOA,QAAQ;AACjB,CAAC,EACD,MAAON,KAAK,IAAK;EAAA,IAAAO,cAAA;EACf,MAAMC,eAAe,GAAGR,KAAK,CAACL,MAAM;EACpC,MAAM;IAAEW;EAAS,CAAC,GAAGN,KAAK;EAE1B,IAAIM,QAAQ,EAAE;IACZ,QAAQA,QAAQ,CAACG,MAAM;MACrB,KAAK,GAAG;QACN;QACA,IAAID,eAAe,CAACE,MAAM,EAAE;UAC1B;UACAb,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;UAChCd,YAAY,CAACc,UAAU,CAAC,cAAc,CAAC;UACvC,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;YACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;YAC/BpC,OAAO,CAACqB,KAAK,CAAC,aAAa,CAAC;UAC9B;UACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;QAC9B;QAEA,IAAIpB,YAAY,EAAE;UAChB;UACA,OAAO,IAAIqB,OAAO,CAAC,CAACI,OAAO,EAAEH,MAAM,KAAK;YACtCrB,WAAW,CAACmC,IAAI,CAAC;cAAEX,OAAO;cAAEH;YAAO,CAAC,CAAC;UACvC,CAAC,CAAC,CAACe,IAAI,CAACrB,KAAK,IAAI;YACfY,eAAe,CAACjB,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;YACzD,OAAOd,SAAS,CAAC0B,eAAe,CAAC;UACnC,CAAC,CAAC,CAACU,KAAK,CAACC,GAAG,IAAI;YACd,OAAOlB,OAAO,CAACC,MAAM,CAACiB,GAAG,CAAC;UAC5B,CAAC,CAAC;QACJ;QAEAX,eAAe,CAACE,MAAM,GAAG,IAAI;QAC7B9B,YAAY,GAAG,IAAI;QAEnB,MAAMwC,YAAY,GAAGvB,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QACzD,IAAIsB,YAAY,EAAE;UAChB,IAAI;YACF;YACA,MAAMC,eAAe,GAAG,MAAM3C,KAAK,CAAC4C,IAAI,CACtC,GAAGrC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,8BAA8B,eAAe,EACtF;cAAEiC;YAAa,CACjB,CAAC;YAED,IAAIC,eAAe,CAACE,IAAI,CAACC,OAAO,EAAE;cAChC,MAAMC,QAAQ,GAAGJ,eAAe,CAACE,IAAI,CAACA,IAAI,CAAC3B,KAAK;cAChD,MAAM8B,eAAe,GAAGL,eAAe,CAACE,IAAI,CAACA,IAAI,CAACH,YAAY;;cAE9D;cACAvB,YAAY,CAAC8B,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;cACvC5B,YAAY,CAAC8B,OAAO,CAAC,cAAc,EAAED,eAAe,CAAC;;cAErD;cACAvB,YAAY,CAAC,IAAI,EAAEsB,QAAQ,CAAC;cAC5B7C,YAAY,GAAG,KAAK;;cAEpB;cACA4B,eAAe,CAACjB,OAAO,CAACQ,aAAa,GAAG,UAAU0B,QAAQ,EAAE;cAC5D,OAAO3C,SAAS,CAAC0B,eAAe,CAAC;YACnC;UACF,CAAC,CAAC,OAAOoB,YAAY,EAAE;YACrBC,OAAO,CAAC7B,KAAK,CAAC,uBAAuB,EAAE4B,YAAY,CAAC;YACpDzB,YAAY,CAACyB,YAAY,EAAE,IAAI,CAAC;UAClC;QACF;;QAEA;QACAhD,YAAY,GAAG,KAAK;QACpBiB,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;QAChCd,YAAY,CAACc,UAAU,CAAC,cAAc,CAAC;;QAEvC;QACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;UACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;UAC/BpC,OAAO,CAACqB,KAAK,CAAC,aAAa,CAAC;QAC9B;QACA;MAEF,KAAK,GAAG;QACN;QACArB,OAAO,CAACqB,KAAK,CAAC;UACZ8B,OAAO,EAAE,YAAY;UACrBC,QAAQ,EAAE,CAAC;UACXC,GAAG,EAAE,kBAAkB;UACvBC,OAAO,EAAEA,CAAA,KAAM;YACb;YACApC,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;YAChCd,YAAY,CAACc,UAAU,CAAC,cAAc,CAAC;YACvCd,YAAY,CAACc,UAAU,CAAC,MAAM,CAAC;YAC/B;YACAC,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;UACjC;QACF,CAAC,CAAC;QACF;MAEF,KAAK,GAAG;QACNpC,OAAO,CAACqB,KAAK,CAAC,UAAU,CAAC;QACzB;MAEF,KAAK,GAAG;QACNrB,OAAO,CAACqB,KAAK,CAAC,SAAS,CAAC;QACxB;MAEF;QACErB,OAAO,CAACqB,KAAK,CAAC,EAAAO,cAAA,GAAAD,QAAQ,CAACiB,IAAI,cAAAhB,cAAA,uBAAbA,cAAA,CAAe5B,OAAO,KAAI,MAAM,CAAC;IACnD;EACF,CAAC,MAAM;IACL;IACAA,OAAO,CAACqB,KAAK,CAAC,gBAAgB,CAAC;EACjC;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAelB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}