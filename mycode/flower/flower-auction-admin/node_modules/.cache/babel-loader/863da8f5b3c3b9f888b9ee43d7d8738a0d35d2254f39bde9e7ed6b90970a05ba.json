{"ast": null, "code": "\"use client\";\n\nimport Group from './Group';\nimport InternalInput from './Input';\nimport OTP from './OTP';\nimport Password from './Password';\nimport Search from './Search';\nimport TextArea from './TextArea';\nconst Input = InternalInput;\nInput.Group = Group;\nInput.Search = Search;\nInput.TextArea = TextArea;\nInput.Password = Password;\nInput.OTP = OTP;\nexport default Input;", "map": {"version": 3, "names": ["Group", "InternalInput", "OTP", "Password", "Search", "TextArea", "Input"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/input/index.js"], "sourcesContent": ["\"use client\";\n\nimport Group from './Group';\nimport InternalInput from './Input';\nimport OTP from './OTP';\nimport Password from './Password';\nimport Search from './Search';\nimport TextArea from './TextArea';\nconst Input = InternalInput;\nInput.Group = Group;\nInput.Search = Search;\nInput.TextArea = TextArea;\nInput.Password = Password;\nInput.OTP = OTP;\nexport default Input;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AACjC,MAAMC,KAAK,GAAGL,aAAa;AAC3BK,KAAK,CAACN,KAAK,GAAGA,KAAK;AACnBM,KAAK,CAACF,MAAM,GAAGA,MAAM;AACrBE,KAAK,CAACD,QAAQ,GAAGA,QAAQ;AACzBC,KAAK,CAACH,QAAQ,GAAGA,QAAQ;AACzBG,KAAK,CAACJ,GAAG,GAAGA,GAAG;AACf,eAAeI,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}