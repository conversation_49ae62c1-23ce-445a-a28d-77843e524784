{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setUser, clearUser, setLoading } from '../store/slices/authSlice';\nimport { authService } from '../services/authService';\nexport const useAuth = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    user,\n    isAuthenticated,\n    loading\n  } = useSelector(state => state.auth);\n\n  // 登录\n  const login = async credentials => {\n    try {\n      dispatch(setLoading(true));\n      console.log('开始登录:', credentials.username);\n      const response = await authService.login(credentials);\n      console.log('登录响应:', response);\n      if (response.success && response.data) {\n        // 保存token到localStorage\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n\n        // 保存token过期时间\n        if (response.data.expiresAt) {\n          localStorage.setItem('tokenExpiresAt', response.data.expiresAt.toString());\n        }\n\n        // 直接使用登录响应中的用户信息\n        console.log('设置用户信息:', response.data.user);\n        dispatch(setUser(response.data.user));\n        console.log('登录成功，用户已认证');\n        return {\n          success: true\n        };\n      }\n      console.log('登录失败:', response.message);\n      return {\n        success: false,\n        message: response.message || '登录失败'\n      };\n    } catch (error) {\n      console.error('登录异常:', error);\n      return {\n        success: false,\n        message: error.message || '登录失败'\n      };\n    } finally {\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      dispatch(setLoading(true));\n      // 调用后端登出API\n      await authService.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // 即使后端调用失败，也要清除本地数据\n    } finally {\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('tokenExpiresAt');\n      localStorage.removeItem('user');\n\n      // 清除会话存储\n      sessionStorage.removeItem('token');\n      sessionStorage.removeItem('refreshToken');\n      sessionStorage.removeItem('user');\n\n      // 清除管理端相关的缓存数据\n      localStorage.removeItem('admin_preferences');\n      localStorage.removeItem('admin_dashboard_config');\n      localStorage.removeItem('admin_recent_actions');\n\n      // 清除Redux状态\n      dispatch(clearUser());\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 刷新token\n  const refreshToken = async () => {\n    const refreshTokenValue = localStorage.getItem('refreshToken');\n    if (!refreshTokenValue) {\n      return false;\n    }\n    try {\n      const response = await authService.refreshToken(refreshTokenValue);\n      if (response.success && response.data) {\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Refresh token error:', error);\n      return false;\n    }\n  };\n  return {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    refreshToken\n  };\n};\n_s(useAuth, \"ROuHSsBLhlKfgyQ1CkIOqd3wYHk=\", false, function () {\n  return [useDispatch, useSelector];\n});", "map": {"version": 3, "names": ["useDispatch", "useSelector", "setUser", "clearUser", "setLoading", "authService", "useAuth", "_s", "dispatch", "user", "isAuthenticated", "loading", "state", "auth", "login", "credentials", "console", "log", "username", "response", "success", "data", "localStorage", "setItem", "token", "refreshToken", "expiresAt", "toString", "message", "error", "logout", "removeItem", "sessionStorage", "refreshTokenValue", "getItem"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/hooks/useAuth.ts"], "sourcesContent": ["import { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../store';\nimport { setUser, clearUser, setLoading } from '../store/slices/authSlice';\nimport { authService } from '../services/authService';\n\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  role: string;\n  avatar?: string;\n  realName?: string;\n  userType?: number;\n  phone?: string;\n  companyName?: string;\n  balance?: number;\n  frozenAmount?: number;\n  creditLevel?: number;\n  roles?: Array<{\n    id: number;\n    name: string;\n    code?: string;\n  }>;\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport const useAuth = () => {\n  const dispatch = useDispatch();\n  const { user, isAuthenticated, loading } = useSelector((state: RootState) => state.auth);\n\n  // 登录\n  const login = async (credentials: LoginCredentials) => {\n    try {\n      dispatch(setLoading(true));\n      console.log('开始登录:', credentials.username);\n      const response = await authService.login(credentials);\n      console.log('登录响应:', response);\n\n      if (response.success && response.data) {\n        // 保存token到localStorage\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n\n        // 保存token过期时间\n        if (response.data.expiresAt) {\n          localStorage.setItem('tokenExpiresAt', response.data.expiresAt.toString());\n        }\n\n        // 直接使用登录响应中的用户信息\n        console.log('设置用户信息:', response.data.user);\n        dispatch(setUser(response.data.user));\n\n        console.log('登录成功，用户已认证');\n        return { success: true };\n      }\n\n      console.log('登录失败:', response.message);\n      return { success: false, message: response.message || '登录失败' };\n    } catch (error: any) {\n      console.error('登录异常:', error);\n      return { success: false, message: error.message || '登录失败' };\n    } finally {\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      dispatch(setLoading(true));\n      // 调用后端登出API\n      await authService.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // 即使后端调用失败，也要清除本地数据\n    } finally {\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('tokenExpiresAt');\n      localStorage.removeItem('user');\n\n      // 清除会话存储\n      sessionStorage.removeItem('token');\n      sessionStorage.removeItem('refreshToken');\n      sessionStorage.removeItem('user');\n\n      // 清除管理端相关的缓存数据\n      localStorage.removeItem('admin_preferences');\n      localStorage.removeItem('admin_dashboard_config');\n      localStorage.removeItem('admin_recent_actions');\n\n      // 清除Redux状态\n      dispatch(clearUser());\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 刷新token\n  const refreshToken = async () => {\n    const refreshTokenValue = localStorage.getItem('refreshToken');\n    if (!refreshTokenValue) {\n      return false;\n    }\n\n    try {\n      const response = await authService.refreshToken(refreshTokenValue);\n      if (response.success && response.data) {\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Refresh token error:', error);\n      return false;\n    }\n  };\n\n  return {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    refreshToken,\n  };\n};\n"], "mappings": ";AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,OAAO,EAAEC,SAAS,EAAEC,UAAU,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,QAAQ,yBAAyB;AA2BrD,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,IAAI;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGV,WAAW,CAAEW,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;;EAExF;EACA,MAAMC,KAAK,GAAG,MAAOC,WAA6B,IAAK;IACrD,IAAI;MACFP,QAAQ,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;MAC1BY,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,WAAW,CAACG,QAAQ,CAAC;MAC1C,MAAMC,QAAQ,GAAG,MAAMd,WAAW,CAACS,KAAK,CAACC,WAAW,CAAC;MACrDC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEE,QAAQ,CAAC;MAE9B,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrC;QACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;QAClDF,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACE,IAAI,CAACI,YAAY,CAAC;;QAEhE;QACA,IAAIN,QAAQ,CAACE,IAAI,CAACK,SAAS,EAAE;UAC3BJ,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,QAAQ,CAACE,IAAI,CAACK,SAAS,CAACC,QAAQ,CAAC,CAAC,CAAC;QAC5E;;QAEA;QACAX,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEE,QAAQ,CAACE,IAAI,CAACZ,IAAI,CAAC;QAC1CD,QAAQ,CAACN,OAAO,CAACiB,QAAQ,CAACE,IAAI,CAACZ,IAAI,CAAC,CAAC;QAErCO,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QACzB,OAAO;UAAEG,OAAO,EAAE;QAAK,CAAC;MAC1B;MAEAJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEE,QAAQ,CAACS,OAAO,CAAC;MACtC,OAAO;QAAER,OAAO,EAAE,KAAK;QAAEQ,OAAO,EAAET,QAAQ,CAACS,OAAO,IAAI;MAAO,CAAC;IAChE,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B,OAAO;QAAET,OAAO,EAAE,KAAK;QAAEQ,OAAO,EAAEC,KAAK,CAACD,OAAO,IAAI;MAAO,CAAC;IAC7D,CAAC,SAAS;MACRpB,QAAQ,CAACJ,UAAU,CAAC,KAAK,CAAC,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAM0B,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACFtB,QAAQ,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;MAC1B;MACA,MAAMC,WAAW,CAACyB,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC,SAAS;MACR;MACAP,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;MAChCT,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;MACvCT,YAAY,CAACS,UAAU,CAAC,gBAAgB,CAAC;MACzCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;;MAE/B;MACAC,cAAc,CAACD,UAAU,CAAC,OAAO,CAAC;MAClCC,cAAc,CAACD,UAAU,CAAC,cAAc,CAAC;MACzCC,cAAc,CAACD,UAAU,CAAC,MAAM,CAAC;;MAEjC;MACAT,YAAY,CAACS,UAAU,CAAC,mBAAmB,CAAC;MAC5CT,YAAY,CAACS,UAAU,CAAC,wBAAwB,CAAC;MACjDT,YAAY,CAACS,UAAU,CAAC,sBAAsB,CAAC;;MAE/C;MACAvB,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrBK,QAAQ,CAACJ,UAAU,CAAC,KAAK,CAAC,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMqB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMQ,iBAAiB,GAAGX,YAAY,CAACY,OAAO,CAAC,cAAc,CAAC;IAC9D,IAAI,CAACD,iBAAiB,EAAE;MACtB,OAAO,KAAK;IACd;IAEA,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMd,WAAW,CAACoB,YAAY,CAACQ,iBAAiB,CAAC;MAClE,IAAId,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrCC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;QAClDF,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACE,IAAI,CAACI,YAAY,CAAC;QAChE,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;EAED,OAAO;IACLpB,IAAI;IACJC,eAAe;IACfC,OAAO;IACPG,KAAK;IACLgB,MAAM;IACNL;EACF,CAAC;AACH,CAAC;AAAClB,EAAA,CArGWD,OAAO;EAAA,QACDN,WAAW,EACeC,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}