{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx\";\nimport React from 'react';\nimport { Layout, Dropdown, Badge, Space } from 'antd';\nimport { BellOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\nimport UserMenu from '../UserMenu';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header\n} = Layout;\nconst HeaderComponent = ({\n  collapsed,\n  toggle\n}) => {\n  const notificationMenuItems = [{\n    key: 'notification1',\n    label: '系统通知：新版本已发布'\n  }, {\n    key: 'notification2',\n    label: '业务通知：有新的拍卖会已创建'\n  }, {\n    key: 'notification3',\n    label: '提醒：今日有3个订单待处理'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'all',\n    label: '查看全部通知'\n  }];\n  return /*#__PURE__*/_jsxDEV(Header, {\n    className: \"site-header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-left\",\n      children: /*#__PURE__*/React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n        className: 'trigger',\n        onClick: toggle\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-right\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items: notificationMenuItems\n          },\n          placement: \"bottomRight\",\n          children: /*#__PURE__*/_jsxDEV(Badge, {\n            count: 5,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(BellOutlined, {\n              className: \"header-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserMenu, {\n          showUsername: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_c = HeaderComponent;\nexport default HeaderComponent;\nvar _c;\n$RefreshReg$(_c, \"HeaderComponent\");", "map": {"version": 3, "names": ["React", "Layout", "Dropdown", "Badge", "Space", "BellOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "UserMenu", "jsxDEV", "_jsxDEV", "Header", "HeaderComponent", "collapsed", "toggle", "notificationMenuItems", "key", "label", "type", "className", "children", "createElement", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "menu", "items", "placement", "count", "showUsername", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx"], "sourcesContent": ["import React from 'react';\nimport { Layout, Dropdown, Badge, Space } from 'antd';\nimport {\n  BellOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n} from '@ant-design/icons';\nimport UserMenu from '../UserMenu';\nimport './index.css';\n\nconst { Header } = Layout;\n\ninterface HeaderProps {\n  collapsed: boolean;\n  toggle: () => void;\n}\n\nconst HeaderComponent: React.FC<HeaderProps> = ({ collapsed, toggle }) => {\n\n  const notificationMenuItems = [\n    {\n      key: 'notification1',\n      label: '系统通知：新版本已发布',\n    },\n    {\n      key: 'notification2',\n      label: '业务通知：有新的拍卖会已创建',\n    },\n    {\n      key: 'notification3',\n      label: '提醒：今日有3个订单待处理',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'all',\n      label: '查看全部通知',\n    },\n  ];\n\n  return (\n    <Header className=\"site-header\">\n      <div className=\"header-left\">\n        {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n          className: 'trigger',\n          onClick: toggle,\n        })}\n      </div>\n      <div className=\"header-right\">\n        <Space size=\"large\">\n          <Dropdown menu={{ items: notificationMenuItems }} placement=\"bottomRight\">\n            <Badge count={5} size=\"small\">\n              <BellOutlined className=\"header-icon\" />\n            </Badge>\n          </Dropdown>\n          <UserMenu showUsername={true} />\n        </Space>\n      </div>\n    </Header>\n  );\n};\n\nexport default HeaderComponent;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACrD,SACEC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,QACb,mBAAmB;AAC1B,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAO,CAAC,GAAGV,MAAM;AAOzB,MAAMW,eAAsC,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAExE,MAAMC,qBAAqB,GAAG,CAC5B;IACEC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACEC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEP,OAAA,CAACC,MAAM;IAACQ,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC7BV,OAAA;MAAKS,SAAS,EAAC,aAAa;MAAAC,QAAA,eACzBpB,KAAK,CAACqB,aAAa,CAACR,SAAS,GAAGN,kBAAkB,GAAGD,gBAAgB,EAAE;QACtEa,SAAS,EAAE,SAAS;QACpBG,OAAO,EAAER;MACX,CAAC;IAAC;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNhB,OAAA;MAAKS,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BV,OAAA,CAACN,KAAK;QAACuB,IAAI,EAAC,OAAO;QAAAP,QAAA,gBACjBV,OAAA,CAACR,QAAQ;UAAC0B,IAAI,EAAE;YAAEC,KAAK,EAAEd;UAAsB,CAAE;UAACe,SAAS,EAAC,aAAa;UAAAV,QAAA,eACvEV,OAAA,CAACP,KAAK;YAAC4B,KAAK,EAAE,CAAE;YAACJ,IAAI,EAAC,OAAO;YAAAP,QAAA,eAC3BV,OAAA,CAACL,YAAY;cAACc,SAAS,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACXhB,OAAA,CAACF,QAAQ;UAACwB,YAAY,EAAE;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACO,EAAA,GA5CIrB,eAAsC;AA8C5C,eAAeA,eAAe;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}