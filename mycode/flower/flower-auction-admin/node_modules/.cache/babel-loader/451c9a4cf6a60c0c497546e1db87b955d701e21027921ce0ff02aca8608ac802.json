{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport omit from \"rc-util/es/omit\";\nimport getScroll from '../_util/getScroll';\nimport { cloneElement } from '../_util/reactNode';\nimport scrollTo from '../_util/scrollTo';\nimport throttleByAnimationFrame from '../_util/throttleByAnimationFrame';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst BackTop = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    visibilityHeight = 400,\n    target,\n    onClick,\n    duration = 450\n  } = props;\n  const [visible, setVisible] = React.useState(visibilityHeight === 0);\n  const ref = React.useRef(null);\n  const getDefaultTarget = () => {\n    var _a;\n    return ((_a = ref.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || window;\n  };\n  const handleScroll = throttleByAnimationFrame(e => {\n    const scrollTop = getScroll(e.target);\n    setVisible(scrollTop >= visibilityHeight);\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('BackTop');\n    warning.deprecated(false, 'BackTop', 'FloatButton.BackTop');\n  }\n  React.useEffect(() => {\n    const getTarget = target || getDefaultTarget;\n    const container = getTarget();\n    handleScroll({\n      target: container\n    });\n    container === null || container === void 0 ? void 0 : container.addEventListener('scroll', handleScroll);\n    return () => {\n      handleScroll.cancel();\n      container === null || container === void 0 ? void 0 : container.removeEventListener('scroll', handleScroll);\n    };\n  }, [target]);\n  const scrollToTop = e => {\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration\n    });\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('back-top', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const classString = classNames(hashId, cssVarCls, prefixCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName);\n  // fix https://fb.me/react-unknown-prop\n  const divProps = omit(props, ['prefixCls', 'className', 'rootClassName', 'children', 'visibilityHeight', 'target']);\n  const defaultElement = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-content`\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-icon`\n  }, /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null)));\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({}, divProps, {\n    className: classString,\n    onClick: scrollToTop,\n    ref: ref\n  }), /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    motionName: `${rootPrefixCls}-fade`\n  }, ({\n    className: motionClassName\n  }) => cloneElement(props.children || defaultElement, ({\n    className: cloneCls\n  }) => ({\n    className: classNames(motionClassName, cloneCls)\n  })))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  BackTop.displayName = 'BackTop';\n}\nexport default BackTop;", "map": {"version": 3, "names": ["React", "VerticalAlignTopOutlined", "classNames", "CSSMotion", "omit", "getScroll", "cloneElement", "scrollTo", "throttleByAnimationFrame", "devUseW<PERSON>ning", "ConfigContext", "useStyle", "BackTop", "props", "prefixCls", "customizePrefixCls", "className", "rootClassName", "visibilityHeight", "target", "onClick", "duration", "visible", "setVisible", "useState", "ref", "useRef", "getDefaultTarget", "_a", "current", "ownerDocument", "window", "handleScroll", "e", "scrollTop", "process", "env", "NODE_ENV", "warning", "deprecated", "useEffect", "get<PERSON><PERSON><PERSON>", "container", "addEventListener", "cancel", "removeEventListener", "scrollToTop", "getContainer", "getPrefixCls", "direction", "useContext", "rootPrefixCls", "wrapCSSVar", "hashId", "cssVarCls", "classString", "divProps", "defaultElement", "createElement", "Object", "assign", "motionName", "motionClassName", "children", "cloneCls", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/back-top/index.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport omit from \"rc-util/es/omit\";\nimport getScroll from '../_util/getScroll';\nimport { cloneElement } from '../_util/reactNode';\nimport scrollTo from '../_util/scrollTo';\nimport throttleByAnimationFrame from '../_util/throttleByAnimationFrame';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst BackTop = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    visibilityHeight = 400,\n    target,\n    onClick,\n    duration = 450\n  } = props;\n  const [visible, setVisible] = React.useState(visibilityHeight === 0);\n  const ref = React.useRef(null);\n  const getDefaultTarget = () => {\n    var _a;\n    return ((_a = ref.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || window;\n  };\n  const handleScroll = throttleByAnimationFrame(e => {\n    const scrollTop = getScroll(e.target);\n    setVisible(scrollTop >= visibilityHeight);\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('BackTop');\n    warning.deprecated(false, 'BackTop', 'FloatButton.BackTop');\n  }\n  React.useEffect(() => {\n    const getTarget = target || getDefaultTarget;\n    const container = getTarget();\n    handleScroll({\n      target: container\n    });\n    container === null || container === void 0 ? void 0 : container.addEventListener('scroll', handleScroll);\n    return () => {\n      handleScroll.cancel();\n      container === null || container === void 0 ? void 0 : container.removeEventListener('scroll', handleScroll);\n    };\n  }, [target]);\n  const scrollToTop = e => {\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration\n    });\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('back-top', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const classString = classNames(hashId, cssVarCls, prefixCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName);\n  // fix https://fb.me/react-unknown-prop\n  const divProps = omit(props, ['prefixCls', 'className', 'rootClassName', 'children', 'visibilityHeight', 'target']);\n  const defaultElement = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-content`\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-icon`\n  }, /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null)));\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({}, divProps, {\n    className: classString,\n    onClick: scrollToTop,\n    ref: ref\n  }), /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    motionName: `${rootPrefixCls}-fade`\n  }, ({\n    className: motionClassName\n  }) => cloneElement(props.children || defaultElement, ({\n    className: cloneCls\n  }) => ({\n    className: classNames(motionClassName, cloneCls)\n  })))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  BackTop.displayName = 'BackTop';\n}\nexport default BackTop;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,wBAAwB,MAAM,qDAAqD;AAC1F,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,wBAAwB,MAAM,mCAAmC;AACxE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,OAAO,GAAGC,KAAK,IAAI;EACvB,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,aAAa;IACbC,gBAAgB,GAAG,GAAG;IACtBC,MAAM;IACNC,OAAO;IACPC,QAAQ,GAAG;EACb,CAAC,GAAGR,KAAK;EACT,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGvB,KAAK,CAACwB,QAAQ,CAACN,gBAAgB,KAAK,CAAC,CAAC;EACpE,MAAMO,GAAG,GAAGzB,KAAK,CAAC0B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAGH,GAAG,CAACI,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,aAAa,KAAKC,MAAM;EAC7F,CAAC;EACD,MAAMC,YAAY,GAAGxB,wBAAwB,CAACyB,CAAC,IAAI;IACjD,MAAMC,SAAS,GAAG7B,SAAS,CAAC4B,CAAC,CAACd,MAAM,CAAC;IACrCI,UAAU,CAACW,SAAS,IAAIhB,gBAAgB,CAAC;EAC3C,CAAC,CAAC;EACF,IAAIiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAG7B,aAAa,CAAC,SAAS,CAAC;IACxC6B,OAAO,CAACC,UAAU,CAAC,KAAK,EAAE,SAAS,EAAE,qBAAqB,CAAC;EAC7D;EACAvC,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB,MAAMC,SAAS,GAAGtB,MAAM,IAAIQ,gBAAgB;IAC5C,MAAMe,SAAS,GAAGD,SAAS,CAAC,CAAC;IAC7BT,YAAY,CAAC;MACXb,MAAM,EAAEuB;IACV,CAAC,CAAC;IACFA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,gBAAgB,CAAC,QAAQ,EAAEX,YAAY,CAAC;IACxG,OAAO,MAAM;MACXA,YAAY,CAACY,MAAM,CAAC,CAAC;MACrBF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,mBAAmB,CAAC,QAAQ,EAAEb,YAAY,CAAC;IAC7G,CAAC;EACH,CAAC,EAAE,CAACb,MAAM,CAAC,CAAC;EACZ,MAAM2B,WAAW,GAAGb,CAAC,IAAI;IACvB1B,QAAQ,CAAC,CAAC,EAAE;MACVwC,YAAY,EAAE5B,MAAM,IAAIQ,gBAAgB;MACxCN;IACF,CAAC,CAAC;IACFD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,CAAC,CAAC;EAC9D,CAAC;EACD,MAAM;IACJe,YAAY;IACZC;EACF,CAAC,GAAGjD,KAAK,CAACkD,UAAU,CAACxC,aAAa,CAAC;EACnC,MAAMI,SAAS,GAAGkC,YAAY,CAAC,UAAU,EAAEjC,kBAAkB,CAAC;EAC9D,MAAMoC,aAAa,GAAGH,YAAY,CAAC,CAAC;EACpC,MAAM,CAACI,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAACG,SAAS,CAAC;EAC3D,MAAMyC,WAAW,GAAGrD,UAAU,CAACmD,MAAM,EAAEC,SAAS,EAAExC,SAAS,EAAE;IAC3D,CAAC,GAAGA,SAAS,MAAM,GAAGmC,SAAS,KAAK;EACtC,CAAC,EAAEjC,SAAS,EAAEC,aAAa,CAAC;EAC5B;EACA,MAAMuC,QAAQ,GAAGpD,IAAI,CAACS,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;EACnH,MAAM4C,cAAc,GAAG,aAAazD,KAAK,CAAC0D,aAAa,CAAC,KAAK,EAAE;IAC7D1C,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,EAAE,aAAad,KAAK,CAAC0D,aAAa,CAAC,KAAK,EAAE;IACzC1C,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,EAAE,aAAad,KAAK,CAAC0D,aAAa,CAACzD,wBAAwB,EAAE,IAAI,CAAC,CAAC,CAAC;EACrE,OAAOmD,UAAU,CAAC,aAAapD,KAAK,CAAC0D,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,QAAQ,EAAE;IACpFxC,SAAS,EAAEuC,WAAW;IACtBnC,OAAO,EAAE0B,WAAW;IACpBrB,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAazB,KAAK,CAAC0D,aAAa,CAACvD,SAAS,EAAE;IAC9CmB,OAAO,EAAEA,OAAO;IAChBuC,UAAU,EAAE,GAAGV,aAAa;EAC9B,CAAC,EAAE,CAAC;IACFnC,SAAS,EAAE8C;EACb,CAAC,KAAKxD,YAAY,CAACO,KAAK,CAACkD,QAAQ,IAAIN,cAAc,EAAE,CAAC;IACpDzC,SAAS,EAAEgD;EACb,CAAC,MAAM;IACLhD,SAAS,EAAEd,UAAU,CAAC4D,eAAe,EAAEE,QAAQ;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AACD,IAAI7B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCzB,OAAO,CAACqD,WAAW,GAAG,SAAS;AACjC;AACA,eAAerD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}