{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalSecurity/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Form, Input, Button, message, Typography, Space, Alert, Table, Tag, Modal, Steps, QRCode } from 'antd';\nimport { LockOutlined, SafetyOutlined, DeleteOutlined, ShieldOutlined } from '@ant-design/icons';\nimport { LogoutButton } from '../../../components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Step\n} = Steps;\nconst PersonalSecurity = () => {\n  _s();\n  const [passwordForm] = Form.useForm();\n  const [phoneForm] = Form.useForm();\n  const [emailForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [twoFactorModalVisible, setTwoFactorModalVisible] = useState(false);\n  const [twoFactorStep, setTwoFactorStep] = useState(0);\n  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);\n  const [loginDevices] = useState([{\n    id: '1',\n    deviceName: 'MacBook Pro',\n    deviceType: 'desktop',\n    browser: 'Chrome 120.0',\n    os: 'macOS 14.0',\n    ip: '*************',\n    location: '昆明市',\n    lastActive: '2024-01-15 10:30:00',\n    isCurrent: true\n  }, {\n    id: '2',\n    deviceName: 'iPhone 15',\n    deviceType: 'mobile',\n    browser: 'Safari 17.0',\n    os: 'iOS 17.0',\n    ip: '*************',\n    location: '昆明市',\n    lastActive: '2024-01-14 18:45:00',\n    isCurrent: false\n  }]);\n  const handleChangePassword = async values => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改密码\n      // await authService.changePassword(values);\n\n      message.success('密码修改成功，请重新登录');\n      passwordForm.resetFields();\n\n      // 延迟后跳转到登录页\n      setTimeout(() => {\n        window.location.href = '/login';\n      }, 2000);\n    } catch (error) {\n      console.error('修改密码失败:', error);\n      message.error('修改密码失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChangePhone = async values => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改手机号\n      // await userService.changePhone(values);\n\n      message.success('手机号修改成功');\n      phoneForm.resetFields();\n    } catch (error) {\n      console.error('修改手机号失败:', error);\n      message.error('修改手机号失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChangeEmail = async values => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改邮箱\n      // await userService.changeEmail(values);\n\n      message.success('邮箱修改成功');\n      emailForm.resetFields();\n    } catch (error) {\n      console.error('修改邮箱失败:', error);\n      message.error('修改邮箱失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRemoveDevice = deviceId => {\n    Modal.confirm({\n      title: '移除设备',\n      content: '确定要移除此设备的登录状态吗？该设备将被强制登出。',\n      onOk: async () => {\n        try {\n          // 这里应该调用后端API移除设备\n          // await authService.removeDevice(deviceId);\n\n          message.success('设备已移除');\n        } catch (error) {\n          console.error('移除设备失败:', error);\n          message.error('移除设备失败');\n        }\n      }\n    });\n  };\n  const handleEnableTwoFactor = () => {\n    setTwoFactorModalVisible(true);\n    setTwoFactorStep(0);\n  };\n  const deviceColumns = [{\n    title: '设备信息',\n    key: 'device',\n    render: record => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 500\n        },\n        children: [record.deviceName, record.isCurrent && /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          style: {\n            marginLeft: 8\n          },\n          children: \"\\u5F53\\u524D\\u8BBE\\u5907\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 34\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#666',\n          fontSize: '12px'\n        },\n        children: [record.browser, \" \\xB7 \", record.os]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ip',\n    key: 'ip'\n  }, {\n    title: '位置',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: '最后活跃',\n    dataIndex: 'lastActive',\n    key: 'lastActive'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: record => /*#__PURE__*/_jsxDEV(Space, {\n      children: !record.isCurrent && /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 21\n        }, this),\n        onClick: () => handleRemoveDevice(record.id),\n        children: \"\\u79FB\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), \" \\u5B89\\u5168\\u8BBE\\u7F6E\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4FEE\\u6539\\u5BC6\\u7801\",\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u5BC6\\u7801\\u5B89\\u5168\\u63D0\\u793A\",\n        description: \"\\u4E3A\\u4E86\\u60A8\\u7684\\u8D26\\u6237\\u5B89\\u5168\\uFF0C\\u5EFA\\u8BAE\\u5B9A\\u671F\\u66F4\\u6362\\u5BC6\\u7801\\uFF0C\\u5BC6\\u7801\\u5E94\\u5305\\u542B\\u5927\\u5C0F\\u5199\\u5B57\\u6BCD\\u3001\\u6570\\u5B57\\u548C\\u7279\\u6B8A\\u5B57\\u7B26\\u3002\",\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: passwordForm,\n        layout: \"vertical\",\n        onFinish: handleChangePassword,\n        style: {\n          maxWidth: 400\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"currentPassword\",\n          label: \"\\u5F53\\u524D\\u5BC6\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入当前密码'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"newPassword\",\n          label: \"\\u65B0\\u5BC6\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入新密码'\n          }, {\n            min: 8,\n            message: '密码至少8个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"confirmPassword\",\n          label: \"\\u786E\\u8BA4\\u65B0\\u5BC6\\u7801\",\n          dependencies: ['newPassword'],\n          rules: [{\n            required: true,\n            message: '请确认新密码'\n          }, ({\n            getFieldValue\n          }) => ({\n            validator(_, value) {\n              if (!value || getFieldValue('newPassword') === value) {\n                return Promise.resolve();\n              }\n              return Promise.reject(new Error('两次输入的密码不一致'));\n            }\n          })],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: loading,\n            children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1\",\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 500,\n              marginBottom: 4\n            },\n            children: [\"\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1 \", twoFactorEnabled ? /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"green\",\n              children: \"\\u5DF2\\u542F\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 41\n            }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"red\",\n              children: \"\\u672A\\u542F\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 72\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u542F\\u7528\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1\\u53EF\\u4EE5\\u5927\\u5927\\u63D0\\u9AD8\\u60A8\\u8D26\\u6237\\u7684\\u5B89\\u5168\\u6027\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: twoFactorEnabled ? 'default' : 'primary',\n          icon: /*#__PURE__*/_jsxDEV(ShieldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 19\n          }, this),\n          onClick: handleEnableTwoFactor,\n          children: twoFactorEnabled ? '管理' : '启用'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u767B\\u5F55\\u8BBE\\u5907\\u7BA1\\u7406\",\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u8BBE\\u5907\\u5B89\\u5168\",\n        description: \"\\u5B9A\\u671F\\u68C0\\u67E5\\u767B\\u5F55\\u8BBE\\u5907\\uFF0C\\u5982\\u53D1\\u73B0\\u5F02\\u5E38\\u8BBE\\u5907\\u8BF7\\u53CA\\u65F6\\u79FB\\u9664\\u3002\",\n        type: \"warning\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: deviceColumns,\n        dataSource: loginDevices,\n        rowKey: \"id\",\n        pagination: false,\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5B89\\u5168\\u64CD\\u4F5C\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5371\\u9669\\u64CD\\u4F5C\",\n          description: \"\\u4EE5\\u4E0B\\u64CD\\u4F5C\\u53EF\\u80FD\\u4F1A\\u5F71\\u54CD\\u60A8\\u7684\\u8D26\\u6237\\u5B89\\u5168\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\u3002\",\n          type: \"error\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px 0'\n          },\n          children: /*#__PURE__*/_jsxDEV(LogoutButton, {\n            type: \"primary\",\n            danger: true,\n            children: \"\\u7ACB\\u5373\\u767B\\u51FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BBE\\u7F6E\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1\",\n      open: twoFactorModalVisible,\n      onCancel: () => setTwoFactorModalVisible(false),\n      footer: null,\n      width: 600,\n      children: [/*#__PURE__*/_jsxDEV(Steps, {\n        current: twoFactorStep,\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Step, {\n          title: \"\\u4E0B\\u8F7D\\u5E94\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Step, {\n          title: \"\\u626B\\u63CF\\u4E8C\\u7EF4\\u7801\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Step, {\n          title: \"\\u9A8C\\u8BC1\\u7ED1\\u5B9A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), twoFactorStep === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16\n          },\n          children: \"\\u8BF7\\u5148\\u4E0B\\u8F7D\\u5E76\\u5B89\\u88C5\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5668\\u5E94\\u7528\\uFF0C\\u63A8\\u8350\\u4F7F\\u7528\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            children: \"Google Authenticator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            children: \"Microsoft Authenticator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            children: \"Authy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: () => setTwoFactorStep(1),\n            children: \"\\u4E0B\\u4E00\\u6B65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 11\n      }, this), twoFactorStep === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16\n          },\n          children: \"\\u8BF7\\u4F7F\\u7528\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5668\\u5E94\\u7528\\u626B\\u63CF\\u4E0B\\u65B9\\u4E8C\\u7EF4\\u7801\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(QRCode, {\n          value: \"otpauth://totp/FlowerAuction:admin?secret=JBSWY3DPEHPK3PXP&issuer=FlowerAuction\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setTwoFactorStep(0),\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u4E0A\\u4E00\\u6B65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: () => setTwoFactorStep(2),\n            children: \"\\u4E0B\\u4E00\\u6B65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this), twoFactorStep === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16\n          },\n          children: \"\\u8BF7\\u8F93\\u5165\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5668\\u5E94\\u7528\\u4E2D\\u663E\\u793A\\u76846\\u4F4D\\u6570\\u5B57\\u9A8C\\u8BC1\\u7801\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          style: {\n            width: 200,\n            textAlign: 'center',\n            fontSize: '18px'\n          },\n          maxLength: 6,\n          placeholder: \"000000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setTwoFactorStep(1),\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u4E0A\\u4E00\\u6B65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: () => {\n              setTwoFactorEnabled(true);\n              setTwoFactorModalVisible(false);\n              message.success('双因子认证设置成功');\n            },\n            children: \"\\u5B8C\\u6210\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this);\n};\n_s(PersonalSecurity, \"3PqP7AFLEMKPDJHEdTlYL47wQOg=\", false, function () {\n  return [Form.useForm, Form.useForm, Form.useForm];\n});\n_c = PersonalSecurity;\nexport default PersonalSecurity;\nvar _c;\n$RefreshReg$(_c, \"PersonalSecurity\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Form", "Input", "<PERSON><PERSON>", "message", "Typography", "Space", "<PERSON><PERSON>", "Table", "Tag", "Modal", "Steps", "QRCode", "LockOutlined", "SafetyOutlined", "DeleteOutlined", "ShieldOutlined", "LogoutButton", "jsxDEV", "_jsxDEV", "Title", "Text", "Step", "PersonalSecurity", "_s", "passwordForm", "useForm", "phoneForm", "emailForm", "loading", "setLoading", "twoFactorModalVisible", "setTwoFactorModalVisible", "twoFactorStep", "setTwoFactorStep", "twoFactorEnabled", "setTwoFactorEnabled", "loginDevices", "id", "deviceName", "deviceType", "browser", "os", "ip", "location", "lastActive", "isCurrent", "handleChangePassword", "values", "success", "resetFields", "setTimeout", "window", "href", "error", "console", "handleChangePhone", "handleChangeEmail", "handleRemoveDevice", "deviceId", "confirm", "title", "content", "onOk", "handleEnableTwoFactor", "deviceColumns", "key", "render", "record", "children", "style", "fontWeight", "color", "marginLeft", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "dataIndex", "type", "size", "danger", "icon", "onClick", "padding", "level", "marginBottom", "description", "showIcon", "form", "layout", "onFinish", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "name", "label", "rules", "required", "Password", "prefix", "min", "dependencies", "getFieldValue", "validator", "_", "value", "Promise", "resolve", "reject", "Error", "htmlType", "display", "justifyContent", "alignItems", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "direction", "width", "open", "onCancel", "footer", "current", "textAlign", "marginTop", "marginRight", "max<PERSON><PERSON><PERSON>", "placeholder", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalSecurity/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  message,\n  Typography,\n  Space,\n  Alert,\n  Table,\n  Tag,\n  Modal,\n  Steps,\n  QRCode,\n} from 'antd';\nimport {\n  LockOutlined,\n  SafetyOutlined,\n  MobileOutlined,\n  MailOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  PlusOutlined,\n  ShieldOutlined,\n} from '@ant-design/icons';\nimport { LogoutButton } from '../../../components';\n\nconst { Title, Text } = Typography;\nconst { Step } = Steps;\n\ninterface LoginDevice {\n  id: string;\n  deviceName: string;\n  deviceType: 'desktop' | 'mobile' | 'tablet';\n  browser: string;\n  os: string;\n  ip: string;\n  location: string;\n  lastActive: string;\n  isCurrent: boolean;\n}\n\nconst PersonalSecurity: React.FC = () => {\n  const [passwordForm] = Form.useForm();\n  const [phoneForm] = Form.useForm();\n  const [emailForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [twoFactorModalVisible, setTwoFactorModalVisible] = useState(false);\n  const [twoFactorStep, setTwoFactorStep] = useState(0);\n  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);\n\n  const [loginDevices] = useState<LoginDevice[]>([\n    {\n      id: '1',\n      deviceName: 'MacBook Pro',\n      deviceType: 'desktop',\n      browser: 'Chrome 120.0',\n      os: 'macOS 14.0',\n      ip: '*************',\n      location: '昆明市',\n      lastActive: '2024-01-15 10:30:00',\n      isCurrent: true,\n    },\n    {\n      id: '2',\n      deviceName: 'iPhone 15',\n      deviceType: 'mobile',\n      browser: 'Safari 17.0',\n      os: 'iOS 17.0',\n      ip: '*************',\n      location: '昆明市',\n      lastActive: '2024-01-14 18:45:00',\n      isCurrent: false,\n    },\n  ]);\n\n  const handleChangePassword = async (values: any) => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改密码\n      // await authService.changePassword(values);\n      \n      message.success('密码修改成功，请重新登录');\n      passwordForm.resetFields();\n      \n      // 延迟后跳转到登录页\n      setTimeout(() => {\n        window.location.href = '/login';\n      }, 2000);\n    } catch (error) {\n      console.error('修改密码失败:', error);\n      message.error('修改密码失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChangePhone = async (values: any) => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改手机号\n      // await userService.changePhone(values);\n      \n      message.success('手机号修改成功');\n      phoneForm.resetFields();\n    } catch (error) {\n      console.error('修改手机号失败:', error);\n      message.error('修改手机号失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChangeEmail = async (values: any) => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改邮箱\n      // await userService.changeEmail(values);\n      \n      message.success('邮箱修改成功');\n      emailForm.resetFields();\n    } catch (error) {\n      console.error('修改邮箱失败:', error);\n      message.error('修改邮箱失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveDevice = (deviceId: string) => {\n    Modal.confirm({\n      title: '移除设备',\n      content: '确定要移除此设备的登录状态吗？该设备将被强制登出。',\n      onOk: async () => {\n        try {\n          // 这里应该调用后端API移除设备\n          // await authService.removeDevice(deviceId);\n          \n          message.success('设备已移除');\n        } catch (error) {\n          console.error('移除设备失败:', error);\n          message.error('移除设备失败');\n        }\n      },\n    });\n  };\n\n  const handleEnableTwoFactor = () => {\n    setTwoFactorModalVisible(true);\n    setTwoFactorStep(0);\n  };\n\n  const deviceColumns = [\n    {\n      title: '设备信息',\n      key: 'device',\n      render: (record: LoginDevice) => (\n        <div>\n          <div style={{ fontWeight: 500 }}>\n            {record.deviceName}\n            {record.isCurrent && <Tag color=\"green\" style={{ marginLeft: 8 }}>当前设备</Tag>}\n          </div>\n          <div style={{ color: '#666', fontSize: '12px' }}>\n            {record.browser} · {record.os}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ip',\n      key: 'ip',\n    },\n    {\n      title: '位置',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: '最后活跃',\n      dataIndex: 'lastActive',\n      key: 'lastActive',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (record: LoginDevice) => (\n        <Space>\n          {!record.isCurrent && (\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => handleRemoveDevice(record.id)}\n            >\n              移除\n            </Button>\n          )}\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>\n        <SafetyOutlined /> 安全设置\n      </Title>\n\n      {/* 密码修改 */}\n      <Card title=\"修改密码\" style={{ marginBottom: 24 }}>\n        <Alert\n          message=\"密码安全提示\"\n          description=\"为了您的账户安全，建议定期更换密码，密码应包含大小写字母、数字和特殊字符。\"\n          type=\"info\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        \n        <Form\n          form={passwordForm}\n          layout=\"vertical\"\n          onFinish={handleChangePassword}\n          style={{ maxWidth: 400 }}\n        >\n          <Form.Item\n            name=\"currentPassword\"\n            label=\"当前密码\"\n            rules={[{ required: true, message: '请输入当前密码' }]}\n          >\n            <Input.Password prefix={<LockOutlined />} />\n          </Form.Item>\n\n          <Form.Item\n            name=\"newPassword\"\n            label=\"新密码\"\n            rules={[\n              { required: true, message: '请输入新密码' },\n              { min: 8, message: '密码至少8个字符' },\n            ]}\n          >\n            <Input.Password prefix={<LockOutlined />} />\n          </Form.Item>\n\n          <Form.Item\n            name=\"confirmPassword\"\n            label=\"确认新密码\"\n            dependencies={['newPassword']}\n            rules={[\n              { required: true, message: '请确认新密码' },\n              ({ getFieldValue }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('newPassword') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                },\n              }),\n            ]}\n          >\n            <Input.Password prefix={<LockOutlined />} />\n          </Form.Item>\n\n          <Form.Item>\n            <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n              修改密码\n            </Button>\n          </Form.Item>\n        </Form>\n      </Card>\n\n      {/* 双因子认证 */}\n      <Card title=\"双因子认证\" style={{ marginBottom: 24 }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: 500, marginBottom: 4 }}>\n              双因子认证 {twoFactorEnabled ? <Tag color=\"green\">已启用</Tag> : <Tag color=\"red\">未启用</Tag>}\n            </div>\n            <div style={{ color: '#666' }}>\n              启用双因子认证可以大大提高您账户的安全性\n            </div>\n          </div>\n          <Button\n            type={twoFactorEnabled ? 'default' : 'primary'}\n            icon={<ShieldOutlined />}\n            onClick={handleEnableTwoFactor}\n          >\n            {twoFactorEnabled ? '管理' : '启用'}\n          </Button>\n        </div>\n      </Card>\n\n      {/* 登录设备管理 */}\n      <Card title=\"登录设备管理\" style={{ marginBottom: 24 }}>\n        <Alert\n          message=\"设备安全\"\n          description=\"定期检查登录设备，如发现异常设备请及时移除。\"\n          type=\"warning\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        \n        <Table\n          columns={deviceColumns}\n          dataSource={loginDevices}\n          rowKey=\"id\"\n          pagination={false}\n          size=\"small\"\n        />\n      </Card>\n\n      {/* 安全操作 */}\n      <Card title=\"安全操作\">\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Alert\n            message=\"危险操作\"\n            description=\"以下操作可能会影响您的账户安全，请谨慎操作。\"\n            type=\"error\"\n            showIcon\n          />\n          \n          <div style={{ padding: '16px 0' }}>\n            <LogoutButton type=\"primary\" danger>\n              立即登出\n            </LogoutButton>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 双因子认证设置弹窗 */}\n      <Modal\n        title=\"设置双因子认证\"\n        open={twoFactorModalVisible}\n        onCancel={() => setTwoFactorModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Steps current={twoFactorStep} style={{ marginBottom: 24 }}>\n          <Step title=\"下载应用\" />\n          <Step title=\"扫描二维码\" />\n          <Step title=\"验证绑定\" />\n        </Steps>\n\n        {twoFactorStep === 0 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ marginBottom: 16 }}>\n              请先下载并安装身份验证器应用，推荐使用：\n            </div>\n            <Space>\n              <Button>Google Authenticator</Button>\n              <Button>Microsoft Authenticator</Button>\n              <Button>Authy</Button>\n            </Space>\n            <div style={{ marginTop: 16 }}>\n              <Button type=\"primary\" onClick={() => setTwoFactorStep(1)}>\n                下一步\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {twoFactorStep === 1 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ marginBottom: 16 }}>\n              请使用身份验证器应用扫描下方二维码：\n            </div>\n            <QRCode value=\"otpauth://totp/FlowerAuction:admin?secret=JBSWY3DPEHPK3PXP&issuer=FlowerAuction\" />\n            <div style={{ marginTop: 16 }}>\n              <Button onClick={() => setTwoFactorStep(0)} style={{ marginRight: 8 }}>\n                上一步\n              </Button>\n              <Button type=\"primary\" onClick={() => setTwoFactorStep(2)}>\n                下一步\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {twoFactorStep === 2 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ marginBottom: 16 }}>\n              请输入身份验证器应用中显示的6位数字验证码：\n            </div>\n            <Input\n              style={{ width: 200, textAlign: 'center', fontSize: '18px' }}\n              maxLength={6}\n              placeholder=\"000000\"\n            />\n            <div style={{ marginTop: 16 }}>\n              <Button onClick={() => setTwoFactorStep(1)} style={{ marginRight: 8 }}>\n                上一步\n              </Button>\n              <Button\n                type=\"primary\"\n                onClick={() => {\n                  setTwoFactorEnabled(true);\n                  setTwoFactorModalVisible(false);\n                  message.success('双因子认证设置成功');\n                }}\n              >\n                完成设置\n              </Button>\n            </div>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default PersonalSecurity;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EAIdC,cAAc,EAEdC,cAAc,QACT,mBAAmB;AAC1B,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGhB,UAAU;AAClC,MAAM;EAAEiB;AAAK,CAAC,GAAGX,KAAK;AActB,MAAMY,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,YAAY,CAAC,GAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC;EACrC,MAAM,CAACC,SAAS,CAAC,GAAG1B,IAAI,CAACyB,OAAO,CAAC,CAAC;EAClC,MAAM,CAACE,SAAS,CAAC,GAAG3B,IAAI,CAACyB,OAAO,CAAC,CAAC;EAClC,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAM,CAACsC,YAAY,CAAC,GAAGtC,QAAQ,CAAgB,CAC7C;IACEuC,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,cAAc;IACvBC,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,eAAe;IACnBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,qBAAqB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACER,EAAE,EAAE,GAAG;IACPC,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,aAAa;IACtBC,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,eAAe;IACnBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,qBAAqB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAMC,oBAAoB,GAAG,MAAOC,MAAW,IAAK;IAClDlB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;;MAEA1B,OAAO,CAAC6C,OAAO,CAAC,cAAc,CAAC;MAC/BxB,YAAY,CAACyB,WAAW,CAAC,CAAC;;MAE1B;MACAC,UAAU,CAAC,MAAM;QACfC,MAAM,CAACR,QAAQ,CAACS,IAAI,GAAG,QAAQ;MACjC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlD,OAAO,CAACkD,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,iBAAiB,GAAG,MAAOR,MAAW,IAAK;IAC/ClB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;;MAEA1B,OAAO,CAAC6C,OAAO,CAAC,SAAS,CAAC;MAC1BtB,SAAS,CAACuB,WAAW,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChClD,OAAO,CAACkD,KAAK,CAAC,SAAS,CAAC;IAC1B,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,iBAAiB,GAAG,MAAOT,MAAW,IAAK;IAC/ClB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;;MAEA1B,OAAO,CAAC6C,OAAO,CAAC,QAAQ,CAAC;MACzBrB,SAAS,CAACsB,WAAW,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlD,OAAO,CAACkD,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,kBAAkB,GAAIC,QAAgB,IAAK;IAC/CjD,KAAK,CAACkD,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF;UACA;;UAEA3D,OAAO,CAAC6C,OAAO,CAAC,OAAO,CAAC;QAC1B,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BlD,OAAO,CAACkD,KAAK,CAAC,QAAQ,CAAC;QACzB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMU,qBAAqB,GAAGA,CAAA,KAAM;IAClChC,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM+B,aAAa,GAAG,CACpB;IACEJ,KAAK,EAAE,MAAM;IACbK,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGC,MAAmB,iBAC1BjD,OAAA;MAAAkD,QAAA,gBACElD,OAAA;QAAKmD,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAI,CAAE;QAAAF,QAAA,GAC7BD,MAAM,CAAC7B,UAAU,EACjB6B,MAAM,CAACtB,SAAS,iBAAI3B,OAAA,CAACV,GAAG;UAAC+D,KAAK,EAAC,OAAO;UAACF,KAAK,EAAE;YAAEG,UAAU,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eACN1D,OAAA;QAAKmD,KAAK,EAAE;UAAEE,KAAK,EAAE,MAAM;UAAEM,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,GAC7CD,MAAM,CAAC3B,OAAO,EAAC,QAAG,EAAC2B,MAAM,CAAC1B,EAAE;MAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEhB,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE,IAAI;IACfb,GAAG,EAAE;EACP,CAAC,EACD;IACEL,KAAK,EAAE,IAAI;IACXkB,SAAS,EAAE,UAAU;IACrBb,GAAG,EAAE;EACP,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE,YAAY;IACvBb,GAAG,EAAE;EACP,CAAC,EACD;IACEL,KAAK,EAAE,IAAI;IACXK,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGC,MAAmB,iBAC1BjD,OAAA,CAACb,KAAK;MAAA+D,QAAA,EACH,CAACD,MAAM,CAACtB,SAAS,iBAChB3B,OAAA,CAAChB,MAAM;QACL6E,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,MAAM;QACNC,IAAI,eAAEhE,OAAA,CAACJ,cAAc;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBO,OAAO,EAAEA,CAAA,KAAM1B,kBAAkB,CAACU,MAAM,CAAC9B,EAAE,CAAE;QAAA+B,QAAA,EAC9C;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,CACF;EAED,oBACE1D,OAAA;IAAKmD,KAAK,EAAE;MAAEe,OAAO,EAAE;IAAG,CAAE;IAAAhB,QAAA,gBAC1BlD,OAAA,CAACC,KAAK;MAACkE,KAAK,EAAE,CAAE;MAAAjB,QAAA,gBACdlD,OAAA,CAACL,cAAc;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAGR1D,OAAA,CAACnB,IAAI;MAAC6D,KAAK,EAAC,0BAAM;MAACS,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAlB,QAAA,gBAC7ClD,OAAA,CAACZ,KAAK;QACJH,OAAO,EAAC,sCAAQ;QAChBoF,WAAW,EAAC,gOAAuC;QACnDR,IAAI,EAAC,MAAM;QACXS,QAAQ;QACRnB,KAAK,EAAE;UAAEiB,YAAY,EAAE;QAAG;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEF1D,OAAA,CAAClB,IAAI;QACHyF,IAAI,EAAEjE,YAAa;QACnBkE,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE7C,oBAAqB;QAC/BuB,KAAK,EAAE;UAAEuB,QAAQ,EAAE;QAAI,CAAE;QAAAxB,QAAA,gBAEzBlD,OAAA,CAAClB,IAAI,CAAC6F,IAAI;UACRC,IAAI,EAAC,iBAAiB;UACtBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9F,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAiE,QAAA,eAEhDlD,OAAA,CAACjB,KAAK,CAACiG,QAAQ;YAACC,MAAM,eAAEjF,OAAA,CAACN,YAAY;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAEZ1D,OAAA,CAAClB,IAAI,CAAC6F,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE9F,OAAO,EAAE;UAAS,CAAC,EACrC;YAAEiG,GAAG,EAAE,CAAC;YAAEjG,OAAO,EAAE;UAAW,CAAC,CAC/B;UAAAiE,QAAA,eAEFlD,OAAA,CAACjB,KAAK,CAACiG,QAAQ;YAACC,MAAM,eAAEjF,OAAA,CAACN,YAAY;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAEZ1D,OAAA,CAAClB,IAAI,CAAC6F,IAAI;UACRC,IAAI,EAAC,iBAAiB;UACtBC,KAAK,EAAC,gCAAO;UACbM,YAAY,EAAE,CAAC,aAAa,CAAE;UAC9BL,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE9F,OAAO,EAAE;UAAS,CAAC,EACrC,CAAC;YAAEmG;UAAc,CAAC,MAAM;YACtBC,SAASA,CAACC,CAAC,EAAEC,KAAK,EAAE;cAClB,IAAI,CAACA,KAAK,IAAIH,aAAa,CAAC,aAAa,CAAC,KAAKG,KAAK,EAAE;gBACpD,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;cAC1B;cACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;YAChD;UACF,CAAC,CAAC,CACF;UAAAzC,QAAA,eAEFlD,OAAA,CAACjB,KAAK,CAACiG,QAAQ;YAACC,MAAM,eAAEjF,OAAA,CAACN,YAAY;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAEZ1D,OAAA,CAAClB,IAAI,CAAC6F,IAAI;UAAAzB,QAAA,eACRlD,OAAA,CAAChB,MAAM;YAAC6E,IAAI,EAAC,SAAS;YAAC+B,QAAQ,EAAC,QAAQ;YAAClF,OAAO,EAAEA,OAAQ;YAAAwC,QAAA,EAAC;UAE3D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1D,OAAA,CAACnB,IAAI;MAAC6D,KAAK,EAAC,gCAAO;MAACS,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAlB,QAAA,eAC9ClD,OAAA;QAAKmD,KAAK,EAAE;UAAE0C,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA7C,QAAA,gBACrFlD,OAAA;UAAAkD,QAAA,gBACElD,OAAA;YAAKmD,KAAK,EAAE;cAAEC,UAAU,EAAE,GAAG;cAAEgB,YAAY,EAAE;YAAE,CAAE;YAAAlB,QAAA,GAAC,iCAC1C,EAAClC,gBAAgB,gBAAGhB,OAAA,CAACV,GAAG;cAAC+D,KAAK,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAAG1D,OAAA,CAACV,GAAG;cAAC+D,KAAK,EAAC,KAAK;cAAAH,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACN1D,OAAA;YAAKmD,KAAK,EAAE;cAAEE,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,EAAC;UAE/B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1D,OAAA,CAAChB,MAAM;UACL6E,IAAI,EAAE7C,gBAAgB,GAAG,SAAS,GAAG,SAAU;UAC/CgD,IAAI,eAAEhE,OAAA,CAACH,cAAc;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBO,OAAO,EAAEpB,qBAAsB;UAAAK,QAAA,EAE9BlC,gBAAgB,GAAG,IAAI,GAAG;QAAI;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP1D,OAAA,CAACnB,IAAI;MAAC6D,KAAK,EAAC,sCAAQ;MAACS,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAlB,QAAA,gBAC/ClD,OAAA,CAACZ,KAAK;QACJH,OAAO,EAAC,0BAAM;QACdoF,WAAW,EAAC,sIAAwB;QACpCR,IAAI,EAAC,SAAS;QACdS,QAAQ;QACRnB,KAAK,EAAE;UAAEiB,YAAY,EAAE;QAAG;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEF1D,OAAA,CAACX,KAAK;QACJ2G,OAAO,EAAElD,aAAc;QACvBmD,UAAU,EAAE/E,YAAa;QACzBgF,MAAM,EAAC,IAAI;QACXC,UAAU,EAAE,KAAM;QAClBrC,IAAI,EAAC;MAAO;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP1D,OAAA,CAACnB,IAAI;MAAC6D,KAAK,EAAC,0BAAM;MAAAQ,QAAA,eAChBlD,OAAA,CAACb,KAAK;QAACiH,SAAS,EAAC,UAAU;QAACjD,KAAK,EAAE;UAAEkD,KAAK,EAAE;QAAO,CAAE;QAAAnD,QAAA,gBACnDlD,OAAA,CAACZ,KAAK;UACJH,OAAO,EAAC,0BAAM;UACdoF,WAAW,EAAC,sIAAwB;UACpCR,IAAI,EAAC,OAAO;UACZS,QAAQ;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEF1D,OAAA;UAAKmD,KAAK,EAAE;YAAEe,OAAO,EAAE;UAAS,CAAE;UAAAhB,QAAA,eAChClD,OAAA,CAACF,YAAY;YAAC+D,IAAI,EAAC,SAAS;YAACE,MAAM;YAAAb,QAAA,EAAC;UAEpC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGP1D,OAAA,CAACT,KAAK;MACJmD,KAAK,EAAC,4CAAS;MACf4D,IAAI,EAAE1F,qBAAsB;MAC5B2F,QAAQ,EAAEA,CAAA,KAAM1F,wBAAwB,CAAC,KAAK,CAAE;MAChD2F,MAAM,EAAE,IAAK;MACbH,KAAK,EAAE,GAAI;MAAAnD,QAAA,gBAEXlD,OAAA,CAACR,KAAK;QAACiH,OAAO,EAAE3F,aAAc;QAACqC,KAAK,EAAE;UAAEiB,YAAY,EAAE;QAAG,CAAE;QAAAlB,QAAA,gBACzDlD,OAAA,CAACG,IAAI;UAACuC,KAAK,EAAC;QAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrB1D,OAAA,CAACG,IAAI;UAACuC,KAAK,EAAC;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtB1D,OAAA,CAACG,IAAI;UAACuC,KAAK,EAAC;QAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,EAEP5C,aAAa,KAAK,CAAC,iBAClBd,OAAA;QAAKmD,KAAK,EAAE;UAAEuD,SAAS,EAAE;QAAS,CAAE;QAAAxD,QAAA,gBAClClD,OAAA;UAAKmD,KAAK,EAAE;YAAEiB,YAAY,EAAE;UAAG,CAAE;UAAAlB,QAAA,EAAC;QAElC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN1D,OAAA,CAACb,KAAK;UAAA+D,QAAA,gBACJlD,OAAA,CAAChB,MAAM;YAAAkE,QAAA,EAAC;UAAoB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC1D,OAAA,CAAChB,MAAM;YAAAkE,QAAA,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC1D,OAAA,CAAChB,MAAM;YAAAkE,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACR1D,OAAA;UAAKmD,KAAK,EAAE;YAAEwD,SAAS,EAAE;UAAG,CAAE;UAAAzD,QAAA,eAC5BlD,OAAA,CAAChB,MAAM;YAAC6E,IAAI,EAAC,SAAS;YAACI,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC,CAAC,CAAE;YAAAmC,QAAA,EAAC;UAE3D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA5C,aAAa,KAAK,CAAC,iBAClBd,OAAA;QAAKmD,KAAK,EAAE;UAAEuD,SAAS,EAAE;QAAS,CAAE;QAAAxD,QAAA,gBAClClD,OAAA;UAAKmD,KAAK,EAAE;YAAEiB,YAAY,EAAE;UAAG,CAAE;UAAAlB,QAAA,EAAC;QAElC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN1D,OAAA,CAACP,MAAM;UAAC8F,KAAK,EAAC;QAAiF;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClG1D,OAAA;UAAKmD,KAAK,EAAE;YAAEwD,SAAS,EAAE;UAAG,CAAE;UAAAzD,QAAA,gBAC5BlD,OAAA,CAAChB,MAAM;YAACiF,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC,CAAC,CAAE;YAACoC,KAAK,EAAE;cAAEyD,WAAW,EAAE;YAAE,CAAE;YAAA1D,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1D,OAAA,CAAChB,MAAM;YAAC6E,IAAI,EAAC,SAAS;YAACI,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC,CAAC,CAAE;YAAAmC,QAAA,EAAC;UAE3D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA5C,aAAa,KAAK,CAAC,iBAClBd,OAAA;QAAKmD,KAAK,EAAE;UAAEuD,SAAS,EAAE;QAAS,CAAE;QAAAxD,QAAA,gBAClClD,OAAA;UAAKmD,KAAK,EAAE;YAAEiB,YAAY,EAAE;UAAG,CAAE;UAAAlB,QAAA,EAAC;QAElC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN1D,OAAA,CAACjB,KAAK;UACJoE,KAAK,EAAE;YAAEkD,KAAK,EAAE,GAAG;YAAEK,SAAS,EAAE,QAAQ;YAAE/C,QAAQ,EAAE;UAAO,CAAE;UAC7DkD,SAAS,EAAE,CAAE;UACbC,WAAW,EAAC;QAAQ;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACF1D,OAAA;UAAKmD,KAAK,EAAE;YAAEwD,SAAS,EAAE;UAAG,CAAE;UAAAzD,QAAA,gBAC5BlD,OAAA,CAAChB,MAAM;YAACiF,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC,CAAC,CAAE;YAACoC,KAAK,EAAE;cAAEyD,WAAW,EAAE;YAAE,CAAE;YAAA1D,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1D,OAAA,CAAChB,MAAM;YACL6E,IAAI,EAAC,SAAS;YACdI,OAAO,EAAEA,CAAA,KAAM;cACbhD,mBAAmB,CAAC,IAAI,CAAC;cACzBJ,wBAAwB,CAAC,KAAK,CAAC;cAC/B5B,OAAO,CAAC6C,OAAO,CAAC,WAAW,CAAC;YAC9B,CAAE;YAAAoB,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrD,EAAA,CA/WID,gBAA0B;EAAA,QACPtB,IAAI,CAACyB,OAAO,EACfzB,IAAI,CAACyB,OAAO,EACZzB,IAAI,CAACyB,OAAO;AAAA;AAAAwG,EAAA,GAH5B3G,gBAA0B;AAiXhC,eAAeA,gBAAgB;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}