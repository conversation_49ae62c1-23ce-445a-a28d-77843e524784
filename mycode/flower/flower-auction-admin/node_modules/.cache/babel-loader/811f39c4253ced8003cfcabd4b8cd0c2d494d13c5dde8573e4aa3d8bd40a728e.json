{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Layout, Dropdown, Avatar, Badge, Space, message } from 'antd';\nimport { UserOutlined, BellOutlined, LogoutOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header\n} = Layout;\nconst HeaderComponent = ({\n  collapsed,\n  toggle\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n  const handleLogout = async () => {\n    setLogoutLoading(true);\n    try {\n      await logout();\n      message.success('登出成功');\n      // 跳转到登录页\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      message.error('登出失败，请重试');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n    }\n  };\n  const showLogoutModal = () => {\n    setLogoutModalVisible(true);\n  };\n  const handleChangePassword = () => {\n    message.info('修改密码功能开发中...');\n  };\n  const handleProfile = () => {\n    message.info('个人中心功能开发中...');\n  };\n  const handleSettings = () => {\n    message.info('账号设置功能开发中...');\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this),\n    label: '个人中心'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this),\n    label: '账号设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: handleLogout\n  }];\n  const notificationMenuItems = [{\n    key: 'notification1',\n    label: '系统通知：新版本已发布'\n  }, {\n    key: 'notification2',\n    label: '业务通知：有新的拍卖会已创建'\n  }, {\n    key: 'notification3',\n    label: '提醒：今日有3个订单待处理'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'all',\n    label: '查看全部通知'\n  }];\n  return /*#__PURE__*/_jsxDEV(Header, {\n    className: \"site-header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-left\",\n      children: /*#__PURE__*/React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n        className: 'trigger',\n        onClick: toggle\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-right\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items: notificationMenuItems\n          },\n          placement: \"bottomRight\",\n          children: /*#__PURE__*/_jsxDEV(Badge, {\n            count: 5,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(BellOutlined, {\n              className: \"header-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items: userMenuItems\n          },\n          placement: \"bottomRight\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"username\",\n              children: (user === null || user === void 0 ? void 0 : user.username) || '用户'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(HeaderComponent, \"BxvvLj8dAOpzewwly5/7Spc8GkE=\", false, function () {\n  return [useAuth];\n});\n_c = HeaderComponent;\nexport default HeaderComponent;\nvar _c;\n$RefreshReg$(_c, \"HeaderComponent\");", "map": {"version": 3, "names": ["React", "useState", "Layout", "Dropdown", "Avatar", "Badge", "Space", "message", "UserOutlined", "BellOutlined", "LogoutOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "useAuth", "jsxDEV", "_jsxDEV", "Header", "HeaderComponent", "collapsed", "toggle", "_s", "user", "logout", "logoutModalVisible", "setLogoutModalVisible", "logoutLoading", "setLogoutLoading", "handleLogout", "success", "window", "location", "href", "error", "console", "showLogoutModal", "handleChangePassword", "info", "handleProfile", "handleSettings", "userMenuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "type", "onClick", "notificationMenuItems", "className", "children", "createElement", "size", "menu", "items", "placement", "count", "username", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Layout, Dropdown, Avatar, Badge, Space, Modal, message } from 'antd';\nimport {\n  UserOutlined,\n  BellOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  LockOutlined,\n} from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\n\nconst { Header } = Layout;\n\ninterface HeaderProps {\n  collapsed: boolean;\n  toggle: () => void;\n}\n\nconst HeaderComponent: React.FC<HeaderProps> = ({ collapsed, toggle }) => {\n  const { user, logout } = useAuth();\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n\n  const handleLogout = async () => {\n    setLogoutLoading(true);\n    try {\n      await logout();\n      message.success('登出成功');\n      // 跳转到登录页\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      message.error('登出失败，请重试');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n    }\n  };\n\n  const showLogoutModal = () => {\n    setLogoutModalVisible(true);\n  };\n\n  const handleChangePassword = () => {\n    message.info('修改密码功能开发中...');\n  };\n\n  const handleProfile = () => {\n    message.info('个人中心功能开发中...');\n  };\n\n  const handleSettings = () => {\n    message.info('账号设置功能开发中...');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人中心',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '账号设置',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  const notificationMenuItems = [\n    {\n      key: 'notification1',\n      label: '系统通知：新版本已发布',\n    },\n    {\n      key: 'notification2',\n      label: '业务通知：有新的拍卖会已创建',\n    },\n    {\n      key: 'notification3',\n      label: '提醒：今日有3个订单待处理',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'all',\n      label: '查看全部通知',\n    },\n  ];\n\n  return (\n    <Header className=\"site-header\">\n      <div className=\"header-left\">\n        {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n          className: 'trigger',\n          onClick: toggle,\n        })}\n      </div>\n      <div className=\"header-right\">\n        <Space size=\"large\">\n          <Dropdown menu={{ items: notificationMenuItems }} placement=\"bottomRight\">\n            <Badge count={5} size=\"small\">\n              <BellOutlined className=\"header-icon\" />\n            </Badge>\n          </Dropdown>\n          <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n            <Space>\n              <Avatar icon={<UserOutlined />} />\n              <span className=\"username\">{user?.username || '用户'}</span>\n            </Space>\n          </Dropdown>\n        </Space>\n      </div>\n    </Header>\n  );\n};\n\nexport default HeaderComponent;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAASC,OAAO,QAAQ,MAAM;AAC7E,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,QAEb,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAO,CAAC,GAAGf,MAAM;AAOzB,MAAMgB,eAAsC,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EAClC,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BD,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMJ,MAAM,CAAC,CAAC;MACdhB,OAAO,CAACsB,OAAO,CAAC,MAAM,CAAC;MACvB;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRN,gBAAgB,CAAC,KAAK,CAAC;MACvBF,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BV,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMW,oBAAoB,GAAGA,CAAA,KAAM;IACjC7B,OAAO,CAAC8B,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B/B,OAAO,CAAC8B,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BhC,OAAO,CAAC8B,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMG,aAAa,GAAG,CACpB;IACEC,GAAG,EAAE,SAAS;IACdC,IAAI,eAAE1B,OAAA,CAACR,YAAY;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAE1B,OAAA,CAACL,eAAe;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAE1B,OAAA,CAACN,cAAc;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbE,OAAO,EAAErB;EACX,CAAC,CACF;EAED,MAAMsB,qBAAqB,GAAG,CAC5B;IACET,GAAG,EAAE,eAAe;IACpBM,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,eAAe;IACpBM,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,eAAe;IACpBM,KAAK,EAAE;EACT,CAAC,EACD;IACEC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,KAAK;IACVM,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACE/B,OAAA,CAACC,MAAM;IAACkC,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC7BpC,OAAA;MAAKmC,SAAS,EAAC,aAAa;MAAAC,QAAA,eACzBpD,KAAK,CAACqD,aAAa,CAAClC,SAAS,GAAGN,kBAAkB,GAAGD,gBAAgB,EAAE;QACtEuC,SAAS,EAAE,SAAS;QACpBF,OAAO,EAAE7B;MACX,CAAC;IAAC;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACN9B,OAAA;MAAKmC,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BpC,OAAA,CAACV,KAAK;QAACgD,IAAI,EAAC,OAAO;QAAAF,QAAA,gBACjBpC,OAAA,CAACb,QAAQ;UAACoD,IAAI,EAAE;YAAEC,KAAK,EAAEN;UAAsB,CAAE;UAACO,SAAS,EAAC,aAAa;UAAAL,QAAA,eACvEpC,OAAA,CAACX,KAAK;YAACqD,KAAK,EAAE,CAAE;YAACJ,IAAI,EAAC,OAAO;YAAAF,QAAA,eAC3BpC,OAAA,CAACP,YAAY;cAAC0C,SAAS,EAAC;YAAa;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACX9B,OAAA,CAACb,QAAQ;UAACoD,IAAI,EAAE;YAAEC,KAAK,EAAEhB;UAAc,CAAE;UAACiB,SAAS,EAAC,aAAa;UAAAL,QAAA,eAC/DpC,OAAA,CAACV,KAAK;YAAA8C,QAAA,gBACJpC,OAAA,CAACZ,MAAM;cAACsC,IAAI,eAAE1B,OAAA,CAACR,YAAY;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClC9B,OAAA;cAAMmC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAE,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,QAAQ,KAAI;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACzB,EAAA,CA1GIH,eAAsC;EAAA,QACjBJ,OAAO;AAAA;AAAA8C,EAAA,GAD5B1C,eAAsC;AA4G5C,eAAeA,eAAe;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}