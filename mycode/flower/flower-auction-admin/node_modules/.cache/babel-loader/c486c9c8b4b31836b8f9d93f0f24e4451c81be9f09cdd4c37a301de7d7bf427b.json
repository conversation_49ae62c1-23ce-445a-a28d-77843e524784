{"ast": null, "code": "import React from 'react';\nimport defaultSeedToken from './themes/seed';\nexport { default as defaultTheme } from './themes/default/theme';\n// ================================ Context =================================\n// To ensure snapshot stable. We disable hashed in test env.\nexport const defaultConfig = {\n  token: defaultSeedToken,\n  override: {\n    override: defaultSeedToken\n  },\n  hashed: true\n};\nexport const DesignTokenContext = /*#__PURE__*/React.createContext(defaultConfig);", "map": {"version": 3, "names": ["React", "defaultSeedToken", "default", "defaultTheme", "defaultConfig", "token", "override", "hashed", "DesignTokenContext", "createContext"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/theme/context.js"], "sourcesContent": ["import React from 'react';\nimport defaultSeedToken from './themes/seed';\nexport { default as defaultTheme } from './themes/default/theme';\n// ================================ Context =================================\n// To ensure snapshot stable. We disable hashed in test env.\nexport const defaultConfig = {\n  token: defaultSeedToken,\n  override: {\n    override: defaultSeedToken\n  },\n  hashed: true\n};\nexport const DesignTokenContext = /*#__PURE__*/React.createContext(defaultConfig);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,eAAe;AAC5C,SAASC,OAAO,IAAIC,YAAY,QAAQ,wBAAwB;AAChE;AACA;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,KAAK,EAAEJ,gBAAgB;EACvBK,QAAQ,EAAE;IACRA,QAAQ,EAAEL;EACZ,CAAC;EACDM,MAAM,EAAE;AACV,CAAC;AACD,OAAO,MAAMC,kBAAkB,GAAG,aAAaR,KAAK,CAACS,aAAa,CAACL,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}