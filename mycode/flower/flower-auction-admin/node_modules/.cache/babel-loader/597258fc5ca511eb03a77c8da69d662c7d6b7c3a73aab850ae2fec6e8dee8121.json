{"ast": null, "code": "// WebSocket服务类\nexport class WebSocketService {\n  constructor(url) {\n    this.ws = null;\n    this.url = void 0;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectInterval = 3000;\n    this.heartbeatInterval = null;\n    this.listeners = new Map();\n    this.isConnecting = false;\n    this.url = url;\n  }\n\n  // 连接WebSocket\n  connect() {\n    return new Promise((resolve, reject) => {\n      if (this.isConnecting || this.ws && this.ws.readyState === WebSocket.OPEN) {\n        resolve();\n        return;\n      }\n      this.isConnecting = true;\n      try {\n        this.ws = new WebSocket(this.url);\n        this.ws.onopen = () => {\n          console.log('WebSocket连接已建立');\n          this.isConnecting = false;\n          this.reconnectAttempts = 0;\n          this.startHeartbeat();\n          this.emit('connected');\n          resolve();\n        };\n        this.ws.onmessage = event => {\n          try {\n            const data = JSON.parse(event.data);\n            this.handleMessage(data);\n          } catch (error) {\n            console.error('解析WebSocket消息失败:', error);\n          }\n        };\n        this.ws.onclose = event => {\n          console.log('WebSocket连接已关闭:', event.code, event.reason);\n          this.isConnecting = false;\n          this.stopHeartbeat();\n          this.emit('disconnected', {\n            code: event.code,\n            reason: event.reason\n          });\n\n          // 自动重连\n          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.scheduleReconnect();\n          }\n        };\n        this.ws.onerror = error => {\n          console.error('WebSocket错误:', error);\n          this.isConnecting = false;\n          this.emit('error', error);\n          reject(error);\n        };\n      } catch (error) {\n        this.isConnecting = false;\n        reject(error);\n      }\n    });\n  }\n\n  // 断开连接\n  disconnect() {\n    if (this.ws) {\n      this.stopHeartbeat();\n      this.ws.close(1000, '主动断开连接');\n      this.ws = null;\n    }\n  }\n\n  // 发送消息\n  send(type, data) {\n    if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n      const message = {\n        type,\n        data,\n        timestamp: Date.now()\n      };\n      this.ws.send(JSON.stringify(message));\n    } else {\n      console.warn('WebSocket未连接，无法发送消息');\n    }\n  }\n\n  // 处理接收到的消息\n  handleMessage(message) {\n    const {\n      type,\n      data\n    } = message;\n    this.emit(type, data);\n  }\n\n  // 添加事件监听器\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  // 移除事件监听器\n  off(event, callback) {\n    const eventListeners = this.listeners.get(event);\n    if (eventListeners) {\n      eventListeners.delete(callback);\n    }\n  }\n\n  // 触发事件\n  emit(event, data) {\n    const eventListeners = this.listeners.get(event);\n    if (eventListeners) {\n      eventListeners.forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error('事件回调执行失败:', error);\n        }\n      });\n    }\n  }\n\n  // 开始心跳\n  startHeartbeat() {\n    this.heartbeatInterval = setInterval(() => {\n      this.send('ping', {});\n    }, 30000); // 30秒心跳\n  }\n\n  // 停止心跳\n  stopHeartbeat() {\n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n      this.heartbeatInterval = null;\n    }\n  }\n\n  // 计划重连\n  scheduleReconnect() {\n    this.reconnectAttempts++;\n    console.log(`计划第${this.reconnectAttempts}次重连...`);\n    setTimeout(() => {\n      this.connect().catch(error => {\n        console.error('重连失败:', error);\n      });\n    }, this.reconnectInterval);\n  }\n\n  // 获取连接状态\n  get isConnected() {\n    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;\n  }\n}\n\n// 拍卖WebSocket服务\nexport class AuctionWebSocketService extends WebSocketService {\n  constructor() {\n    // 根据环境变量或配置决定WebSocket地址\n    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8081/ws/auction';\n    super(wsUrl);\n  }\n\n  // 加入拍卖房间\n  joinAuction(auctionId) {\n    this.send('join_auction', {\n      auctionId\n    });\n  }\n\n  // 离开拍卖房间\n  leaveAuction(auctionId) {\n    this.send('leave_auction', {\n      auctionId\n    });\n  }\n\n  // 提交出价\n  placeBid(auctionId, bidAmount) {\n    this.send('place_bid', {\n      auctionId,\n      bidAmount\n    });\n  }\n\n  // 开始拍卖\n  startAuction(auctionId) {\n    this.send('start_auction', {\n      auctionId\n    });\n  }\n\n  // 结束拍卖\n  endAuction(auctionId) {\n    this.send('end_auction', {\n      auctionId\n    });\n  }\n\n  // 暂停拍卖\n  pauseAuction(auctionId) {\n    this.send('pause_auction', {\n      auctionId\n    });\n  }\n\n  // 恢复拍卖\n  resumeAuction(auctionId) {\n    this.send('resume_auction', {\n      auctionId\n    });\n  }\n}\n\n// 创建全局实例\nexport const auctionWebSocket = new AuctionWebSocketService();\n\n// 拍卖事件类型\nexport let AuctionEventType = /*#__PURE__*/function (AuctionEventType) {\n  AuctionEventType[\"AUCTION_STARTED\"] = \"auction_started\";\n  AuctionEventType[\"AUCTION_ENDED\"] = \"auction_ended\";\n  AuctionEventType[\"AUCTION_PAUSED\"] = \"auction_paused\";\n  AuctionEventType[\"AUCTION_RESUMED\"] = \"auction_resumed\";\n  AuctionEventType[\"BID_PLACED\"] = \"bid_placed\";\n  AuctionEventType[\"BID_REJECTED\"] = \"bid_rejected\";\n  AuctionEventType[\"PRICE_UPDATED\"] = \"price_updated\";\n  AuctionEventType[\"TIME_UPDATED\"] = \"time_updated\";\n  AuctionEventType[\"USER_JOINED\"] = \"user_joined\";\n  AuctionEventType[\"USER_LEFT\"] = \"user_left\";\n  AuctionEventType[\"ERROR\"] = \"error\";\n  return AuctionEventType;\n}({});\n\n// 拍卖状态接口\n\n// 出价信息接口\n\nexport default WebSocketService;", "map": {"version": 3, "names": ["WebSocketService", "constructor", "url", "ws", "reconnectAttempts", "maxReconnectAttempts", "reconnectInterval", "heartbeatInterval", "listeners", "Map", "isConnecting", "connect", "Promise", "resolve", "reject", "readyState", "WebSocket", "OPEN", "onopen", "console", "log", "startHeartbeat", "emit", "onmessage", "event", "data", "JSON", "parse", "handleMessage", "error", "onclose", "code", "reason", "stopHeartbeat", "scheduleReconnect", "onerror", "disconnect", "close", "send", "type", "message", "timestamp", "Date", "now", "stringify", "warn", "on", "callback", "has", "set", "Set", "get", "add", "off", "eventListeners", "delete", "for<PERSON>ach", "setInterval", "clearInterval", "setTimeout", "catch", "isConnected", "AuctionWebSocketService", "wsUrl", "process", "env", "REACT_APP_WS_URL", "joinAuction", "auctionId", "leaveAuction", "placeBid", "bidAmount", "startAuction", "endAuction", "pauseAuction", "resumeAuction", "auctionWebSocket", "AuctionEventType"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/websocketService.ts"], "sourcesContent": ["// WebSocket服务类\nexport class WebSocketService {\n  private ws: WebSocket | null = null;\n  private url: string;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectInterval = 3000;\n  private heartbeatInterval: NodeJS.Timeout | null = null;\n  private listeners: Map<string, Set<Function>> = new Map();\n  private isConnecting = false;\n\n  constructor(url: string) {\n    this.url = url;\n  }\n\n  // 连接WebSocket\n  connect(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {\n        resolve();\n        return;\n      }\n\n      this.isConnecting = true;\n\n      try {\n        this.ws = new WebSocket(this.url);\n\n        this.ws.onopen = () => {\n          console.log('WebSocket连接已建立');\n          this.isConnecting = false;\n          this.reconnectAttempts = 0;\n          this.startHeartbeat();\n          this.emit('connected');\n          resolve();\n        };\n\n        this.ws.onmessage = (event) => {\n          try {\n            const data = JSON.parse(event.data);\n            this.handleMessage(data);\n          } catch (error) {\n            console.error('解析WebSocket消息失败:', error);\n          }\n        };\n\n        this.ws.onclose = (event) => {\n          console.log('WebSocket连接已关闭:', event.code, event.reason);\n          this.isConnecting = false;\n          this.stopHeartbeat();\n          this.emit('disconnected', { code: event.code, reason: event.reason });\n          \n          // 自动重连\n          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.scheduleReconnect();\n          }\n        };\n\n        this.ws.onerror = (error) => {\n          console.error('WebSocket错误:', error);\n          this.isConnecting = false;\n          this.emit('error', error);\n          reject(error);\n        };\n      } catch (error) {\n        this.isConnecting = false;\n        reject(error);\n      }\n    });\n  }\n\n  // 断开连接\n  disconnect() {\n    if (this.ws) {\n      this.stopHeartbeat();\n      this.ws.close(1000, '主动断开连接');\n      this.ws = null;\n    }\n  }\n\n  // 发送消息\n  send(type: string, data: any) {\n    if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n      const message = {\n        type,\n        data,\n        timestamp: Date.now(),\n      };\n      this.ws.send(JSON.stringify(message));\n    } else {\n      console.warn('WebSocket未连接，无法发送消息');\n    }\n  }\n\n  // 处理接收到的消息\n  private handleMessage(message: any) {\n    const { type, data } = message;\n    this.emit(type, data);\n  }\n\n  // 添加事件监听器\n  on(event: string, callback: Function) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event)!.add(callback);\n  }\n\n  // 移除事件监听器\n  off(event: string, callback: Function) {\n    const eventListeners = this.listeners.get(event);\n    if (eventListeners) {\n      eventListeners.delete(callback);\n    }\n  }\n\n  // 触发事件\n  private emit(event: string, data?: any) {\n    const eventListeners = this.listeners.get(event);\n    if (eventListeners) {\n      eventListeners.forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error('事件回调执行失败:', error);\n        }\n      });\n    }\n  }\n\n  // 开始心跳\n  private startHeartbeat() {\n    this.heartbeatInterval = setInterval(() => {\n      this.send('ping', {});\n    }, 30000); // 30秒心跳\n  }\n\n  // 停止心跳\n  private stopHeartbeat() {\n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n      this.heartbeatInterval = null;\n    }\n  }\n\n  // 计划重连\n  private scheduleReconnect() {\n    this.reconnectAttempts++;\n    console.log(`计划第${this.reconnectAttempts}次重连...`);\n    \n    setTimeout(() => {\n      this.connect().catch(error => {\n        console.error('重连失败:', error);\n      });\n    }, this.reconnectInterval);\n  }\n\n  // 获取连接状态\n  get isConnected(): boolean {\n    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;\n  }\n}\n\n// 拍卖WebSocket服务\nexport class AuctionWebSocketService extends WebSocketService {\n  constructor() {\n    // 根据环境变量或配置决定WebSocket地址\n    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8081/ws/auction';\n    super(wsUrl);\n  }\n\n  // 加入拍卖房间\n  joinAuction(auctionId: string) {\n    this.send('join_auction', { auctionId });\n  }\n\n  // 离开拍卖房间\n  leaveAuction(auctionId: string) {\n    this.send('leave_auction', { auctionId });\n  }\n\n  // 提交出价\n  placeBid(auctionId: string, bidAmount: number) {\n    this.send('place_bid', { auctionId, bidAmount });\n  }\n\n  // 开始拍卖\n  startAuction(auctionId: string) {\n    this.send('start_auction', { auctionId });\n  }\n\n  // 结束拍卖\n  endAuction(auctionId: string) {\n    this.send('end_auction', { auctionId });\n  }\n\n  // 暂停拍卖\n  pauseAuction(auctionId: string) {\n    this.send('pause_auction', { auctionId });\n  }\n\n  // 恢复拍卖\n  resumeAuction(auctionId: string) {\n    this.send('resume_auction', { auctionId });\n  }\n}\n\n// 创建全局实例\nexport const auctionWebSocket = new AuctionWebSocketService();\n\n// 拍卖事件类型\nexport enum AuctionEventType {\n  AUCTION_STARTED = 'auction_started',\n  AUCTION_ENDED = 'auction_ended',\n  AUCTION_PAUSED = 'auction_paused',\n  AUCTION_RESUMED = 'auction_resumed',\n  BID_PLACED = 'bid_placed',\n  BID_REJECTED = 'bid_rejected',\n  PRICE_UPDATED = 'price_updated',\n  TIME_UPDATED = 'time_updated',\n  USER_JOINED = 'user_joined',\n  USER_LEFT = 'user_left',\n  ERROR = 'error',\n}\n\n// 拍卖状态接口\nexport interface AuctionStatus {\n  id: string;\n  status: 'waiting' | 'active' | 'paused' | 'ended';\n  currentPrice: number;\n  startPrice: number;\n  reservePrice?: number;\n  timeRemaining: number;\n  bidCount: number;\n  participantCount: number;\n  lastBidder?: {\n    id: string;\n    name: string;\n    bidAmount: number;\n    bidTime: string;\n  };\n}\n\n// 出价信息接口\nexport interface BidInfo {\n  id: string;\n  auctionId: string;\n  bidderId: string;\n  bidderName: string;\n  bidAmount: number;\n  bidTime: string;\n  isWinning: boolean;\n}\n\nexport default WebSocketService;\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,gBAAgB,CAAC;EAU5BC,WAAWA,CAACC,GAAW,EAAE;IAAA,KATjBC,EAAE,GAAqB,IAAI;IAAA,KAC3BD,GAAG;IAAA,KACHE,iBAAiB,GAAG,CAAC;IAAA,KACrBC,oBAAoB,GAAG,CAAC;IAAA,KACxBC,iBAAiB,GAAG,IAAI;IAAA,KACxBC,iBAAiB,GAA0B,IAAI;IAAA,KAC/CC,SAAS,GAA+B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACjDC,YAAY,GAAG,KAAK;IAG1B,IAAI,CAACR,GAAG,GAAGA,GAAG;EAChB;;EAEA;EACAS,OAAOA,CAAA,EAAkB;IACvB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI,IAAI,CAACJ,YAAY,IAAK,IAAI,CAACP,EAAE,IAAI,IAAI,CAACA,EAAE,CAACY,UAAU,KAAKC,SAAS,CAACC,IAAK,EAAE;QAC3EJ,OAAO,CAAC,CAAC;QACT;MACF;MAEA,IAAI,CAACH,YAAY,GAAG,IAAI;MAExB,IAAI;QACF,IAAI,CAACP,EAAE,GAAG,IAAIa,SAAS,CAAC,IAAI,CAACd,GAAG,CAAC;QAEjC,IAAI,CAACC,EAAE,CAACe,MAAM,GAAG,MAAM;UACrBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,IAAI,CAACV,YAAY,GAAG,KAAK;UACzB,IAAI,CAACN,iBAAiB,GAAG,CAAC;UAC1B,IAAI,CAACiB,cAAc,CAAC,CAAC;UACrB,IAAI,CAACC,IAAI,CAAC,WAAW,CAAC;UACtBT,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAACV,EAAE,CAACoB,SAAS,GAAIC,KAAK,IAAK;UAC7B,IAAI;YACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;YACnC,IAAI,CAACG,aAAa,CAACH,IAAI,CAAC;UAC1B,CAAC,CAAC,OAAOI,KAAK,EAAE;YACdV,OAAO,CAACU,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;UAC1C;QACF,CAAC;QAED,IAAI,CAAC1B,EAAE,CAAC2B,OAAO,GAAIN,KAAK,IAAK;UAC3BL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,KAAK,CAACO,IAAI,EAAEP,KAAK,CAACQ,MAAM,CAAC;UACxD,IAAI,CAACtB,YAAY,GAAG,KAAK;UACzB,IAAI,CAACuB,aAAa,CAAC,CAAC;UACpB,IAAI,CAACX,IAAI,CAAC,cAAc,EAAE;YAAES,IAAI,EAAEP,KAAK,CAACO,IAAI;YAAEC,MAAM,EAAER,KAAK,CAACQ;UAAO,CAAC,CAAC;;UAErE;UACA,IAAIR,KAAK,CAACO,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC3B,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;YAC7E,IAAI,CAAC6B,iBAAiB,CAAC,CAAC;UAC1B;QACF,CAAC;QAED,IAAI,CAAC/B,EAAE,CAACgC,OAAO,GAAIN,KAAK,IAAK;UAC3BV,OAAO,CAACU,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UACpC,IAAI,CAACnB,YAAY,GAAG,KAAK;UACzB,IAAI,CAACY,IAAI,CAAC,OAAO,EAAEO,KAAK,CAAC;UACzBf,MAAM,CAACe,KAAK,CAAC;QACf,CAAC;MACH,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd,IAAI,CAACnB,YAAY,GAAG,KAAK;QACzBI,MAAM,CAACe,KAAK,CAAC;MACf;IACF,CAAC,CAAC;EACJ;;EAEA;EACAO,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACjC,EAAE,EAAE;MACX,IAAI,CAAC8B,aAAa,CAAC,CAAC;MACpB,IAAI,CAAC9B,EAAE,CAACkC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC;MAC7B,IAAI,CAAClC,EAAE,GAAG,IAAI;IAChB;EACF;;EAEA;EACAmC,IAAIA,CAACC,IAAY,EAAEd,IAAS,EAAE;IAC5B,IAAI,IAAI,CAACtB,EAAE,IAAI,IAAI,CAACA,EAAE,CAACY,UAAU,KAAKC,SAAS,CAACC,IAAI,EAAE;MACpD,MAAMuB,OAAO,GAAG;QACdD,IAAI;QACJd,IAAI;QACJgB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC;MACD,IAAI,CAACxC,EAAE,CAACmC,IAAI,CAACZ,IAAI,CAACkB,SAAS,CAACJ,OAAO,CAAC,CAAC;IACvC,CAAC,MAAM;MACLrB,OAAO,CAAC0B,IAAI,CAAC,qBAAqB,CAAC;IACrC;EACF;;EAEA;EACQjB,aAAaA,CAACY,OAAY,EAAE;IAClC,MAAM;MAAED,IAAI;MAAEd;IAAK,CAAC,GAAGe,OAAO;IAC9B,IAAI,CAAClB,IAAI,CAACiB,IAAI,EAAEd,IAAI,CAAC;EACvB;;EAEA;EACAqB,EAAEA,CAACtB,KAAa,EAAEuB,QAAkB,EAAE;IACpC,IAAI,CAAC,IAAI,CAACvC,SAAS,CAACwC,GAAG,CAACxB,KAAK,CAAC,EAAE;MAC9B,IAAI,CAAChB,SAAS,CAACyC,GAAG,CAACzB,KAAK,EAAE,IAAI0B,GAAG,CAAC,CAAC,CAAC;IACtC;IACA,IAAI,CAAC1C,SAAS,CAAC2C,GAAG,CAAC3B,KAAK,CAAC,CAAE4B,GAAG,CAACL,QAAQ,CAAC;EAC1C;;EAEA;EACAM,GAAGA,CAAC7B,KAAa,EAAEuB,QAAkB,EAAE;IACrC,MAAMO,cAAc,GAAG,IAAI,CAAC9C,SAAS,CAAC2C,GAAG,CAAC3B,KAAK,CAAC;IAChD,IAAI8B,cAAc,EAAE;MAClBA,cAAc,CAACC,MAAM,CAACR,QAAQ,CAAC;IACjC;EACF;;EAEA;EACQzB,IAAIA,CAACE,KAAa,EAAEC,IAAU,EAAE;IACtC,MAAM6B,cAAc,GAAG,IAAI,CAAC9C,SAAS,CAAC2C,GAAG,CAAC3B,KAAK,CAAC;IAChD,IAAI8B,cAAc,EAAE;MAClBA,cAAc,CAACE,OAAO,CAACT,QAAQ,IAAI;QACjC,IAAI;UACFA,QAAQ,CAACtB,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdV,OAAO,CAACU,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;EACQR,cAAcA,CAAA,EAAG;IACvB,IAAI,CAACd,iBAAiB,GAAGkD,WAAW,CAAC,MAAM;MACzC,IAAI,CAACnB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACb;;EAEA;EACQL,aAAaA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC1B,iBAAiB,EAAE;MAC1BmD,aAAa,CAAC,IAAI,CAACnD,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;IAC/B;EACF;;EAEA;EACQ2B,iBAAiBA,CAAA,EAAG;IAC1B,IAAI,CAAC9B,iBAAiB,EAAE;IACxBe,OAAO,CAACC,GAAG,CAAC,MAAM,IAAI,CAAChB,iBAAiB,QAAQ,CAAC;IAEjDuD,UAAU,CAAC,MAAM;MACf,IAAI,CAAChD,OAAO,CAAC,CAAC,CAACiD,KAAK,CAAC/B,KAAK,IAAI;QAC5BV,OAAO,CAACU,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAACvB,iBAAiB,CAAC;EAC5B;;EAEA;EACA,IAAIuD,WAAWA,CAAA,EAAY;IACzB,OAAO,IAAI,CAAC1D,EAAE,KAAK,IAAI,IAAI,IAAI,CAACA,EAAE,CAACY,UAAU,KAAKC,SAAS,CAACC,IAAI;EAClE;AACF;;AAEA;AACA,OAAO,MAAM6C,uBAAuB,SAAS9D,gBAAgB,CAAC;EAC5DC,WAAWA,CAAA,EAAG;IACZ;IACA,MAAM8D,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,IAAI,gCAAgC;IAC9E,KAAK,CAACH,KAAK,CAAC;EACd;;EAEA;EACAI,WAAWA,CAACC,SAAiB,EAAE;IAC7B,IAAI,CAAC9B,IAAI,CAAC,cAAc,EAAE;MAAE8B;IAAU,CAAC,CAAC;EAC1C;;EAEA;EACAC,YAAYA,CAACD,SAAiB,EAAE;IAC9B,IAAI,CAAC9B,IAAI,CAAC,eAAe,EAAE;MAAE8B;IAAU,CAAC,CAAC;EAC3C;;EAEA;EACAE,QAAQA,CAACF,SAAiB,EAAEG,SAAiB,EAAE;IAC7C,IAAI,CAACjC,IAAI,CAAC,WAAW,EAAE;MAAE8B,SAAS;MAAEG;IAAU,CAAC,CAAC;EAClD;;EAEA;EACAC,YAAYA,CAACJ,SAAiB,EAAE;IAC9B,IAAI,CAAC9B,IAAI,CAAC,eAAe,EAAE;MAAE8B;IAAU,CAAC,CAAC;EAC3C;;EAEA;EACAK,UAAUA,CAACL,SAAiB,EAAE;IAC5B,IAAI,CAAC9B,IAAI,CAAC,aAAa,EAAE;MAAE8B;IAAU,CAAC,CAAC;EACzC;;EAEA;EACAM,YAAYA,CAACN,SAAiB,EAAE;IAC9B,IAAI,CAAC9B,IAAI,CAAC,eAAe,EAAE;MAAE8B;IAAU,CAAC,CAAC;EAC3C;;EAEA;EACAO,aAAaA,CAACP,SAAiB,EAAE;IAC/B,IAAI,CAAC9B,IAAI,CAAC,gBAAgB,EAAE;MAAE8B;IAAU,CAAC,CAAC;EAC5C;AACF;;AAEA;AACA,OAAO,MAAMQ,gBAAgB,GAAG,IAAId,uBAAuB,CAAC,CAAC;;AAE7D;AACA,WAAYe,gBAAgB,0BAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAA,OAAhBA,gBAAgB;AAAA;;AAc5B;;AAkBA;;AAWA,eAAe7E,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}