{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Card, Row, Col, Button, message, Typography, Space, Tag, Avatar, List, Statistic, Progress, Badge, Modal, Form, InputNumber, Alert } from 'antd';\nimport { PlayCircleOutlined, PauseCircleOutlined, StopOutlined, AuditOutlined, UserOutlined, ClockCircleOutlined, TrophyOutlined } from '@ant-design/icons';\nimport { auctionService } from '../../../services/auctionService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\n\n// 竞价记录接口\n\n// 拍卖商品接口\n\nconst LiveBidding = () => {\n  _s();\n  const [currentAuctionId, setCurrentAuctionId] = useState(null);\n  const [currentItem, setCurrentItem] = useState(null);\n  const [bidRecords, setBidRecords] = useState([]);\n  const [isAuctionActive, setIsAuctionActive] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState(0);\n  const [bidAmount, setBidAmount] = useState(0);\n  const [isBidModalVisible, setIsBidModalVisible] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState(0);\n  const [form] = Form.useForm();\n  const timerRef = useRef(null);\n  const wsRef = useRef(null);\n\n  // 模拟拍卖商品数据\n  const mockAuctionItem = {\n    id: 1,\n    productName: '荷兰郁金香 - 红色经典',\n    productCode: 'TLP-001',\n    images: ['https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=400', 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400'],\n    startingPrice: 100.00,\n    currentPrice: 350.00,\n    reservePrice: 500.00,\n    bidIncrement: 10.00,\n    bidCount: 15,\n    timeRemaining: 300,\n    // 5分钟\n    status: 'ongoing',\n    highestBidder: 'user123',\n    description: '来自荷兰的优质郁金香，颜色鲜艳，品质上乘。适合园艺爱好者和花卉收藏家。'\n  };\n\n  // 模拟竞价记录\n  const mockBidRecords = [{\n    id: 1,\n    userId: 1,\n    username: 'user123',\n    bidAmount: 350.00,\n    bidTime: new Date().toISOString(),\n    isWinning: true\n  }, {\n    id: 2,\n    userId: 2,\n    username: 'flower_lover',\n    bidAmount: 340.00,\n    bidTime: new Date(Date.now() - 30000).toISOString(),\n    isWinning: false\n  }, {\n    id: 3,\n    userId: 3,\n    username: 'garden_master',\n    bidAmount: 330.00,\n    bidTime: new Date(Date.now() - 60000).toISOString(),\n    isWinning: false\n  }];\n\n  // 获取当前进行中的拍卖\n  const fetchCurrentAuction = async () => {\n    try {\n      // 获取拍卖会列表\n      const auctionResponse = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 10\n      });\n      if (auctionResponse.success && auctionResponse.data.list.length > 0) {\n        const auction = auctionResponse.data.list[0];\n        console.log('获取到拍卖会:', auction);\n\n        // 设置当前拍卖会ID\n        setCurrentAuctionId(auction.id);\n\n        // 获取拍卖商品列表\n        const itemsResponse = await auctionService.getAuctionItemsByAuction(auction.id, {\n          page: 1,\n          pageSize: 1\n        });\n        if (itemsResponse.success && itemsResponse.data.list.length > 0) {\n          const auctionItem = itemsResponse.data.list[0];\n          console.log('获取到拍卖商品:', auctionItem);\n\n          // 使用真实的拍卖商品数据\n          const liveItem = {\n            id: auctionItem.id,\n            productName: auctionItem.productName || '未知商品',\n            productCode: `ITEM-${auctionItem.id}`,\n            images: auctionItem.images || ['https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=400'],\n            startingPrice: auctionItem.startingPrice || 0,\n            currentPrice: auctionItem.currentPrice || auctionItem.startingPrice || 0,\n            reservePrice: 0,\n            // 保留价，如果有的话\n            bidIncrement: 10,\n            // 默认加价幅度\n            bidCount: auctionItem.bidCount || 0,\n            timeRemaining: 300,\n            // 5分钟倒计时\n            status: auctionItem.status === '1' || auctionItem.status === 'ongoing' ? 'ongoing' : 'pending',\n            highestBidder: '暂无',\n            description: `拍卖商品：${auctionItem.productName}`\n          };\n          setCurrentItem(liveItem);\n          setTimeRemaining(liveItem.timeRemaining);\n          setBidAmount(liveItem.currentPrice + liveItem.bidIncrement);\n\n          // 如果商品正在拍卖中，设置拍卖状态\n          if (auctionItem.status === '1' || auctionItem.status === 'ongoing') {\n            setIsAuctionActive(true);\n          }\n        } else {\n          // 如果没有拍卖商品，使用拍卖会信息创建默认商品\n          const auctionName = auction.name || auction.title;\n          const liveItem = {\n            ...mockAuctionItem,\n            id: auction.id,\n            productName: `${auctionName} - 拍卖商品`,\n            description: auction.description || `拍卖会：${auctionName}`\n          };\n          setCurrentItem(liveItem);\n          setTimeRemaining(liveItem.timeRemaining);\n          setBidAmount(liveItem.currentPrice + liveItem.bidIncrement);\n        }\n\n        // 获取竞价记录\n        fetchBidRecords(auction.id);\n      } else {\n        // 如果没有拍卖会，使用模拟数据\n        console.log('没有拍卖会数据，使用模拟数据');\n        setCurrentItem(mockAuctionItem);\n        setBidRecords(mockBidRecords);\n        setTimeRemaining(mockAuctionItem.timeRemaining);\n        setBidAmount(mockAuctionItem.currentPrice + mockAuctionItem.bidIncrement);\n      }\n      setOnlineUsers(Math.floor(Math.random() * 50) + 10);\n    } catch (error) {\n      console.error('获取拍卖数据失败:', error);\n      // 出错时使用模拟数据\n      setCurrentItem(mockAuctionItem);\n      setBidRecords(mockBidRecords);\n      setTimeRemaining(mockAuctionItem.timeRemaining);\n      setBidAmount(mockAuctionItem.currentPrice + mockAuctionItem.bidIncrement);\n      setOnlineUsers(Math.floor(Math.random() * 50) + 10);\n    }\n  };\n\n  // 获取竞价记录\n  const fetchBidRecords = async auctionId => {\n    try {\n      console.log('获取拍卖会竞价记录:', auctionId);\n\n      // 尝试获取真实的竞价记录\n      const response = await auctionService.getBidRecords(auctionId, undefined, {\n        page: 1,\n        pageSize: 50\n      });\n      if (response.success && response.data && response.data.list) {\n        // 转换后端数据格式为前端格式\n        const bidRecords = response.data.list.map(record => ({\n          id: record.id,\n          userId: record.userId || record.user_id,\n          username: record.bidderName || record.bidder_name || record.username || `用户${record.userId || record.user_id}`,\n          bidAmount: record.price || record.bidAmount || record.amount,\n          bidTime: record.createdAt || record.created_at || record.bidTime,\n          isWinning: record.status === 1 || record.isWinning || false\n        }));\n        setBidRecords(bidRecords);\n        console.log('获取到真实竞价记录:', bidRecords);\n      } else {\n        // 如果没有真实数据，使用模拟数据\n        console.log('没有竞价记录，使用模拟数据');\n        setBidRecords(mockBidRecords);\n      }\n    } catch (error) {\n      console.error('获取竞价记录失败:', error);\n      // 出错时使用模拟数据\n      setBidRecords(mockBidRecords);\n    }\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    fetchCurrentAuction();\n  }, []);\n\n  // 倒计时器\n  useEffect(() => {\n    if (isAuctionActive && timeRemaining > 0) {\n      timerRef.current = setInterval(() => {\n        setTimeRemaining(prev => {\n          if (prev <= 1) {\n            setIsAuctionActive(false);\n            message.info('拍卖时间结束');\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n    } else {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    }\n    return () => {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    };\n  }, [isAuctionActive, timeRemaining]);\n\n  // WebSocket连接（模拟实时更新）\n  useEffect(() => {\n    // 模拟实时数据更新\n    const interval = setInterval(() => {\n      if (isAuctionActive && currentAuctionId) {\n        // 模拟随机出价\n        const shouldAddBid = Math.random() < 0.3; // 30%概率有新出价\n        if (shouldAddBid && currentItem) {\n          const randomUsers = ['张三', '李四', '王五', '赵六', '钱七'];\n          const randomUser = randomUsers[Math.floor(Math.random() * randomUsers.length)];\n          const newPrice = currentItem.currentPrice + currentItem.bidIncrement + Math.random() * 50;\n          const newBid = {\n            id: Date.now(),\n            userId: Math.floor(Math.random() * 1000),\n            username: randomUser,\n            bidAmount: newPrice,\n            bidTime: new Date().toISOString(),\n            isWinning: true\n          };\n\n          // 更新竞价记录\n          setBidRecords(prev => {\n            const updated = prev.map(record => ({\n              ...record,\n              isWinning: false\n            }));\n            return [newBid, ...updated.slice(0, 19)]; // 只保留最新20条\n          });\n\n          // 更新当前价格\n          setCurrentItem(prev => prev ? {\n            ...prev,\n            currentPrice: newPrice,\n            bidCount: prev.bidCount + 1,\n            highestBidder: randomUser\n          } : null);\n\n          // 更新建议出价\n          setBidAmount(newPrice + currentItem.bidIncrement);\n        }\n\n        // 模拟在线用户数变化\n        setOnlineUsers(prev => {\n          const change = Math.floor(Math.random() * 6) - 3; // -3 到 +3\n          return Math.max(5, Math.min(100, prev + change));\n        });\n      }\n    }, 5000); // 每5秒检查一次\n\n    return () => {\n      clearInterval(interval);\n      if (wsRef.current) {\n        wsRef.current.close();\n      }\n    };\n  }, [isAuctionActive, currentAuctionId, currentItem]);\n\n  // 格式化时间\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 开始拍卖\n  const handleStartAuction = async () => {\n    if (!currentAuctionId) {\n      message.error('没有选中的拍卖会');\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await auctionService.startAuction(currentAuctionId);\n      if (response.success) {\n        setIsAuctionActive(true);\n        message.success('拍卖已开始');\n      } else {\n        throw new Error(response.message || '开始拍卖失败');\n      }\n    } catch (error) {\n      console.error('开始拍卖失败:', error);\n      message.error(error.message || '开始拍卖失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 暂停拍卖\n  const handlePauseAuction = async () => {\n    if (!currentAuctionId) {\n      message.error('没有选中的拍卖会');\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await auctionService.pauseAuction(currentAuctionId);\n      if (response.success) {\n        setIsAuctionActive(false);\n        message.info('拍卖已暂停');\n      } else {\n        throw new Error(response.message || '暂停拍卖失败');\n      }\n    } catch (error) {\n      console.error('暂停拍卖失败:', error);\n      message.error(error.message || '暂停拍卖失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 结束拍卖\n  const handleEndAuction = async () => {\n    if (!currentAuctionId) {\n      message.error('没有选中的拍卖会');\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await auctionService.endAuction(currentAuctionId);\n      if (response.success) {\n        setIsAuctionActive(false);\n        setTimeRemaining(0);\n        message.success('拍卖已结束');\n      } else {\n        throw new Error(response.message || '结束拍卖失败');\n      }\n    } catch (error) {\n      console.error('结束拍卖失败:', error);\n      message.error(error.message || '结束拍卖失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 出价\n  const handleBid = () => {\n    setIsBidModalVisible(true);\n    form.setFieldsValue({\n      bidAmount\n    });\n  };\n\n  // 确认出价\n  const handleConfirmBid = async values => {\n    setLoading(true);\n    try {\n      if (!currentItem) {\n        throw new Error('当前没有选中的拍卖商品');\n      }\n\n      // 调用后端API提交出价\n      const response = await auctionService.placeBid({\n        itemId: currentItem.id,\n        userId: 1,\n        // 这里应该从用户上下文获取真实的用户ID\n        price: values.bidAmount\n      });\n      if (response.success) {\n        const newBid = {\n          id: Date.now(),\n          userId: 1,\n          username: 'admin',\n          // 这里应该从用户上下文获取真实的用户名\n          bidAmount: values.bidAmount,\n          bidTime: new Date().toISOString(),\n          isWinning: true\n        };\n\n        // 更新竞价记录\n        setBidRecords(prev => {\n          const updated = prev.map(record => ({\n            ...record,\n            isWinning: false\n          }));\n          return [newBid, ...updated];\n        });\n\n        // 更新当前价格\n        setCurrentItem(prev => prev ? {\n          ...prev,\n          currentPrice: values.bidAmount,\n          bidCount: prev.bidCount + 1,\n          highestBidder: 'admin'\n        } : null);\n\n        // 设置下一次出价金额\n        setBidAmount(values.bidAmount + ((currentItem === null || currentItem === void 0 ? void 0 : currentItem.bidIncrement) || 10));\n        message.success('出价成功');\n        setIsBidModalVisible(false);\n      } else {\n        throw new Error(response.message || '出价失败');\n      }\n    } catch (error) {\n      console.error('出价失败:', error);\n      message.error(error.message || '出价失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!currentItem) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: 24,\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 3,\n        children: \"\\u6682\\u65E0\\u8FDB\\u884C\\u4E2D\\u7684\\u62CD\\u5356\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u5B9E\\u65F6\\u7ADE\\u4EF7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: currentItem.images[0],\n                alt: currentItem.productName,\n                style: {\n                  width: '100%',\n                  height: 200,\n                  objectFit: 'cover',\n                  borderRadius: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              md: 16,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                size: \"small\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Title, {\n                  level: 3,\n                  style: {\n                    margin: 0\n                  },\n                  children: currentItem.productName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u5546\\u54C1\\u7F16\\u53F7: \", currentItem.productCode]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: currentItem.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Statistic, {\n                      title: \"\\u8D77\\u62CD\\u4EF7\",\n                      value: currentItem.startingPrice,\n                      precision: 2,\n                      prefix: \"\\xA5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Statistic, {\n                      title: \"\\u5F53\\u524D\\u4EF7\\u683C\",\n                      value: currentItem.currentPrice,\n                      precision: 2,\n                      prefix: \"\\xA5\",\n                      valueStyle: {\n                        color: '#f50',\n                        fontSize: 24,\n                        fontWeight: 'bold'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Statistic, {\n                      title: \"\\u51FA\\u4EF7\\u6B21\\u6570\",\n                      value: currentItem.bidCount,\n                      suffix: \"\\u6B21\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u62CD\\u5356\\u63A7\\u5236\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            align: \"middle\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  status: isAuctionActive ? 'processing' : 'default',\n                  text: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: [\"\\u72B6\\u6001: \", isAuctionActive ? '进行中' : '已暂停']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5269\\u4F59\\u65F6\\u95F4\",\n                value: formatTime(timeRemaining),\n                prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 27\n                }, this),\n                valueStyle: {\n                  color: timeRemaining < 60 ? '#f50' : '#1890ff',\n                  fontSize: 20,\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                percent: Math.max(0, timeRemaining / 300 * 100),\n                showInfo: false,\n                strokeColor: timeRemaining < 60 ? '#f50' : '#1890ff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [!isAuctionActive ? /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 29\n                  }, this),\n                  onClick: handleStartAuction,\n                  disabled: timeRemaining === 0,\n                  loading: loading,\n                  children: \"\\u5F00\\u59CB\\u62CD\\u5356\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(PauseCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 29\n                  }, this),\n                  onClick: handlePauseAuction,\n                  loading: loading,\n                  children: \"\\u6682\\u505C\\u62CD\\u5356\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  danger: true,\n                  icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 27\n                  }, this),\n                  onClick: handleEndAuction,\n                  loading: loading,\n                  children: \"\\u7ED3\\u675F\\u62CD\\u5356\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5FEB\\u901F\\u51FA\\u4EF7\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            align: \"middle\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: [\"\\u5EFA\\u8BAE\\u51FA\\u4EF7: \\xA5\", bidAmount.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u52A0\\u4EF7\\u5E45\\u5EA6: \\xA5\", currentItem.bidIncrement.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 19\n                }, this), currentItem.reservePrice && /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"warning\",\n                  children: [\"\\u4FDD\\u7559\\u4EF7: \\xA5\", currentItem.reservePrice.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  size: \"large\",\n                  icon: /*#__PURE__*/_jsxDEV(AuditOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 27\n                  }, this),\n                  onClick: handleBid,\n                  disabled: !isAuctionActive || timeRemaining === 0,\n                  children: [\"\\u51FA\\u4EF7 \\xA5\", bidAmount.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5728\\u7EBF\\u7528\\u6237\",\n                value: onlineUsers,\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 27\n                }, this),\n                valueStyle: {\n                  color: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u6700\\u9AD8\\u51FA\\u4EF7\\u4EBA\",\n                value: currentItem.highestBidder || '暂无',\n                prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7ADE\\u4EF7\\u8BB0\\u5F55\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: bidRecords,\n            renderItem: record => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                padding: '8px 0',\n                backgroundColor: record.isWinning ? '#f6ffed' : 'transparent',\n                borderRadius: record.isWinning ? 4 : 0,\n                paddingLeft: record.isWinning ? 8 : 0\n              },\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                  src: record.avatar,\n                  icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 687,\n                    columnNumber: 31\n                  }, this),\n                  style: {\n                    backgroundColor: record.isWinning ? '#52c41a' : '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 23\n                }, this),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: record.isWinning,\n                    children: record.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 25\n                  }, this), record.isWinning && /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"green\",\n                    children: \"\\u6700\\u9AD8\\u4EF7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  direction: \"vertical\",\n                  size: 0,\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    style: {\n                      color: '#f50'\n                    },\n                    children: [\"\\xA5\", record.bidAmount.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 12\n                    },\n                    children: new Date(record.bidTime).toLocaleTimeString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 17\n            }, this),\n            style: {\n              maxHeight: 400,\n              overflowY: 'auto'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u786E\\u8BA4\\u51FA\\u4EF7\",\n      open: isBidModalVisible,\n      onCancel: () => setIsBidModalVisible(false),\n      footer: null,\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleConfirmBid,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u51FA\\u4EF7\\u63D0\\u9192\",\n          description: `当前最高价: ¥${currentItem.currentPrice.toFixed(2)}，最小加价幅度: ¥${currentItem.bidIncrement.toFixed(2)}`,\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"bidAmount\",\n          label: \"\\u51FA\\u4EF7\\u91D1\\u989D\",\n          rules: [{\n            required: true,\n            message: '请输入出价金额'\n          }, {\n            type: 'number',\n            min: currentItem.currentPrice + currentItem.bidIncrement,\n            message: `出价必须高于当前价格 ¥${(currentItem.currentPrice + currentItem.bidIncrement).toFixed(2)}`\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            style: {\n              width: '100%'\n            },\n            precision: 2,\n            min: currentItem.currentPrice + currentItem.bidIncrement,\n            step: currentItem.bidIncrement,\n            addonBefore: \"\\xA5\",\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u51FA\\u4EF7\\u91D1\\u989D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsBidModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              children: \"\\u786E\\u8BA4\\u51FA\\u4EF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 725,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 488,\n    columnNumber: 5\n  }, this);\n};\n_s(LiveBidding, \"rbS8CJKhNJbJpoLPWdNXbr2t+eQ=\", false, function () {\n  return [Form.useForm];\n});\n_c = LiveBidding;\nexport default LiveBidding;\nvar _c;\n$RefreshReg$(_c, \"LiveBidding\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Card", "Row", "Col", "<PERSON><PERSON>", "message", "Typography", "Space", "Tag", "Avatar", "List", "Statistic", "Progress", "Badge", "Modal", "Form", "InputNumber", "<PERSON><PERSON>", "PlayCircleOutlined", "PauseCircleOutlined", "StopOutlined", "AuditOutlined", "UserOutlined", "ClockCircleOutlined", "TrophyOutlined", "auctionService", "jsxDEV", "_jsxDEV", "Title", "Text", "LiveBidding", "_s", "currentAuctionId", "setCurrentAuctionId", "currentItem", "setCurrentItem", "bidRecords", "setBidRecords", "isAuctionActive", "setIsAuctionActive", "timeRemaining", "setTimeRemaining", "bidAmount", "setBidAmount", "isBidModalVisible", "setIsBidModalVisible", "loading", "setLoading", "onlineUsers", "setOnlineUsers", "form", "useForm", "timerRef", "wsRef", "mockAuctionItem", "id", "productName", "productCode", "images", "startingPrice", "currentPrice", "reservePrice", "bidIncrement", "bidCount", "status", "highestBidder", "description", "mockBidRecords", "userId", "username", "bidTime", "Date", "toISOString", "isWinning", "now", "fetchCurrentAuction", "auctionResponse", "getAuctionList", "page", "pageSize", "success", "data", "list", "length", "auction", "console", "log", "itemsResponse", "getAuctionItemsByAuction", "auctionItem", "liveItem", "auctionName", "name", "title", "fetchBidRecords", "Math", "floor", "random", "error", "auctionId", "response", "getBidRecords", "undefined", "map", "record", "user_id", "bidderName", "bidder_name", "price", "amount", "createdAt", "created_at", "current", "setInterval", "prev", "info", "clearInterval", "interval", "shouldAddBid", "randomUsers", "randomUser", "newPrice", "newBid", "updated", "slice", "change", "max", "min", "close", "formatTime", "seconds", "mins", "secs", "toString", "padStart", "handleStartAuction", "startAuction", "Error", "handlePauseAuction", "pauseAuction", "handleEndAuction", "endAuction", "handleBid", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleConfirmBid", "values", "placeBid", "itemId", "style", "padding", "textAlign", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "xs", "lg", "marginBottom", "md", "src", "alt", "width", "height", "objectFit", "borderRadius", "direction", "size", "margin", "type", "span", "value", "precision", "prefix", "valueStyle", "color", "fontSize", "fontWeight", "suffix", "align", "sm", "text", "strong", "percent", "showInfo", "strokeColor", "icon", "onClick", "disabled", "danger", "toFixed", "dataSource", "renderItem", "<PERSON><PERSON>", "backgroundColor", "paddingLeft", "Meta", "avatar", "toLocaleTimeString", "maxHeight", "overflowY", "open", "onCancel", "footer", "layout", "onFinish", "autoComplete", "showIcon", "label", "rules", "required", "step", "addonBefore", "placeholder", "justifyContent", "htmlType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  <PERSON>,\n  Row,\n  Col,\n  Button,\n\n  message,\n  Typography,\n  Space,\n  Tag,\n  Avatar,\n  List,\n  Statistic,\n  Progress,\n  Badge,\n  Modal,\n  Form,\n  InputNumber,\n  Alert,\n} from 'antd';\nimport {\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  AuditOutlined,\n  UserOutlined,\n  ClockCircleOutlined,\n  TrophyOutlined,\n} from '@ant-design/icons';\nimport { auctionService } from '../../../services/auctionService';\n\nconst { Title, Text } = Typography;\n\n// 竞价记录接口\ninterface BidRecord {\n  id: number;\n  userId: number;\n  username: string;\n  avatar?: string;\n  bidAmount: number;\n  bidTime: string;\n  isWinning: boolean;\n}\n\n// 拍卖商品接口\ninterface LiveAuctionItem {\n  id: number;\n  productName: string;\n  productCode: string;\n  images: string[];\n  startingPrice: number;\n  currentPrice: number;\n  reservePrice?: number;\n  bidIncrement: number;\n  bidCount: number;\n  timeRemaining: number; // 剩余时间（秒）\n  status: 'pending' | 'ongoing' | 'ended';\n  highestBidder?: string;\n  description: string;\n}\n\nconst LiveBidding: React.FC = () => {\n  const [currentAuctionId, setCurrentAuctionId] = useState<number | null>(null);\n  const [currentItem, setCurrentItem] = useState<LiveAuctionItem | null>(null);\n  const [bidRecords, setBidRecords] = useState<BidRecord[]>([]);\n  const [isAuctionActive, setIsAuctionActive] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState(0);\n  const [bidAmount, setBidAmount] = useState<number>(0);\n  const [isBidModalVisible, setIsBidModalVisible] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState(0);\n  const [form] = Form.useForm();\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\n  const wsRef = useRef<WebSocket | null>(null);\n\n  // 模拟拍卖商品数据\n  const mockAuctionItem: LiveAuctionItem = {\n    id: 1,\n    productName: '荷兰郁金香 - 红色经典',\n    productCode: 'TLP-001',\n    images: [\n      'https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=400',\n      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400',\n    ],\n    startingPrice: 100.00,\n    currentPrice: 350.00,\n    reservePrice: 500.00,\n    bidIncrement: 10.00,\n    bidCount: 15,\n    timeRemaining: 300, // 5分钟\n    status: 'ongoing',\n    highestBidder: 'user123',\n    description: '来自荷兰的优质郁金香，颜色鲜艳，品质上乘。适合园艺爱好者和花卉收藏家。',\n  };\n\n  // 模拟竞价记录\n  const mockBidRecords: BidRecord[] = [\n    {\n      id: 1,\n      userId: 1,\n      username: 'user123',\n      bidAmount: 350.00,\n      bidTime: new Date().toISOString(),\n      isWinning: true,\n    },\n    {\n      id: 2,\n      userId: 2,\n      username: 'flower_lover',\n      bidAmount: 340.00,\n      bidTime: new Date(Date.now() - 30000).toISOString(),\n      isWinning: false,\n    },\n    {\n      id: 3,\n      userId: 3,\n      username: 'garden_master',\n      bidAmount: 330.00,\n      bidTime: new Date(Date.now() - 60000).toISOString(),\n      isWinning: false,\n    },\n  ];\n\n  // 获取当前进行中的拍卖\n  const fetchCurrentAuction = async () => {\n    try {\n      // 获取拍卖会列表\n      const auctionResponse = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 10,\n      });\n\n      if (auctionResponse.success && auctionResponse.data.list.length > 0) {\n        const auction = auctionResponse.data.list[0];\n        console.log('获取到拍卖会:', auction);\n\n        // 设置当前拍卖会ID\n        setCurrentAuctionId(auction.id);\n\n        // 获取拍卖商品列表\n        const itemsResponse = await auctionService.getAuctionItemsByAuction(auction.id, {\n          page: 1,\n          pageSize: 1,\n        });\n\n        if (itemsResponse.success && itemsResponse.data.list.length > 0) {\n          const auctionItem = itemsResponse.data.list[0];\n          console.log('获取到拍卖商品:', auctionItem);\n\n          // 使用真实的拍卖商品数据\n          const liveItem: LiveAuctionItem = {\n            id: auctionItem.id,\n            productName: auctionItem.productName || '未知商品',\n            productCode: `ITEM-${auctionItem.id}`,\n            images: auctionItem.images || [\n              'https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=400',\n            ],\n            startingPrice: auctionItem.startingPrice || 0,\n            currentPrice: auctionItem.currentPrice || auctionItem.startingPrice || 0,\n            reservePrice: 0, // 保留价，如果有的话\n            bidIncrement: 10, // 默认加价幅度\n            bidCount: auctionItem.bidCount || 0,\n            timeRemaining: 300, // 5分钟倒计时\n            status: auctionItem.status === '1' || auctionItem.status === 'ongoing' ? 'ongoing' : 'pending',\n            highestBidder: '暂无',\n            description: `拍卖商品：${auctionItem.productName}`,\n          };\n\n          setCurrentItem(liveItem);\n          setTimeRemaining(liveItem.timeRemaining);\n          setBidAmount(liveItem.currentPrice + liveItem.bidIncrement);\n\n          // 如果商品正在拍卖中，设置拍卖状态\n          if (auctionItem.status === '1' || auctionItem.status === 'ongoing') {\n            setIsAuctionActive(true);\n          }\n        } else {\n          // 如果没有拍卖商品，使用拍卖会信息创建默认商品\n          const auctionName = (auction as any).name || auction.title;\n          const liveItem: LiveAuctionItem = {\n            ...mockAuctionItem,\n            id: auction.id,\n            productName: `${auctionName} - 拍卖商品`,\n            description: auction.description || `拍卖会：${auctionName}`,\n          };\n\n          setCurrentItem(liveItem);\n          setTimeRemaining(liveItem.timeRemaining);\n          setBidAmount(liveItem.currentPrice + liveItem.bidIncrement);\n        }\n\n        // 获取竞价记录\n        fetchBidRecords(auction.id);\n      } else {\n        // 如果没有拍卖会，使用模拟数据\n        console.log('没有拍卖会数据，使用模拟数据');\n        setCurrentItem(mockAuctionItem);\n        setBidRecords(mockBidRecords);\n        setTimeRemaining(mockAuctionItem.timeRemaining);\n        setBidAmount(mockAuctionItem.currentPrice + mockAuctionItem.bidIncrement);\n      }\n\n      setOnlineUsers(Math.floor(Math.random() * 50) + 10);\n    } catch (error) {\n      console.error('获取拍卖数据失败:', error);\n      // 出错时使用模拟数据\n      setCurrentItem(mockAuctionItem);\n      setBidRecords(mockBidRecords);\n      setTimeRemaining(mockAuctionItem.timeRemaining);\n      setBidAmount(mockAuctionItem.currentPrice + mockAuctionItem.bidIncrement);\n      setOnlineUsers(Math.floor(Math.random() * 50) + 10);\n    }\n  };\n\n  // 获取竞价记录\n  const fetchBidRecords = async (auctionId: number) => {\n    try {\n      console.log('获取拍卖会竞价记录:', auctionId);\n\n      // 尝试获取真实的竞价记录\n      const response = await auctionService.getBidRecords(auctionId, undefined, {\n        page: 1,\n        pageSize: 50\n      });\n\n      if (response.success && response.data && response.data.list) {\n        // 转换后端数据格式为前端格式\n        const bidRecords: BidRecord[] = response.data.list.map((record: any) => ({\n          id: record.id,\n          userId: record.userId || record.user_id,\n          username: record.bidderName || record.bidder_name || record.username || `用户${record.userId || record.user_id}`,\n          bidAmount: record.price || record.bidAmount || record.amount,\n          bidTime: record.createdAt || record.created_at || record.bidTime,\n          isWinning: record.status === 1 || record.isWinning || false,\n        }));\n\n        setBidRecords(bidRecords);\n        console.log('获取到真实竞价记录:', bidRecords);\n      } else {\n        // 如果没有真实数据，使用模拟数据\n        console.log('没有竞价记录，使用模拟数据');\n        setBidRecords(mockBidRecords);\n      }\n    } catch (error) {\n      console.error('获取竞价记录失败:', error);\n      // 出错时使用模拟数据\n      setBidRecords(mockBidRecords);\n    }\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    fetchCurrentAuction();\n  }, []);\n\n  // 倒计时器\n  useEffect(() => {\n    if (isAuctionActive && timeRemaining > 0) {\n      timerRef.current = setInterval(() => {\n        setTimeRemaining(prev => {\n          if (prev <= 1) {\n            setIsAuctionActive(false);\n            message.info('拍卖时间结束');\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n    } else {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    }\n\n    return () => {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    };\n  }, [isAuctionActive, timeRemaining]);\n\n  // WebSocket连接（模拟实时更新）\n  useEffect(() => {\n    // 模拟实时数据更新\n    const interval = setInterval(() => {\n      if (isAuctionActive && currentAuctionId) {\n        // 模拟随机出价\n        const shouldAddBid = Math.random() < 0.3; // 30%概率有新出价\n        if (shouldAddBid && currentItem) {\n          const randomUsers = ['张三', '李四', '王五', '赵六', '钱七'];\n          const randomUser = randomUsers[Math.floor(Math.random() * randomUsers.length)];\n          const newPrice = currentItem.currentPrice + currentItem.bidIncrement + Math.random() * 50;\n\n          const newBid: BidRecord = {\n            id: Date.now(),\n            userId: Math.floor(Math.random() * 1000),\n            username: randomUser,\n            bidAmount: newPrice,\n            bidTime: new Date().toISOString(),\n            isWinning: true,\n          };\n\n          // 更新竞价记录\n          setBidRecords(prev => {\n            const updated = prev.map(record => ({ ...record, isWinning: false }));\n            return [newBid, ...updated.slice(0, 19)]; // 只保留最新20条\n          });\n\n          // 更新当前价格\n          setCurrentItem(prev => prev ? {\n            ...prev,\n            currentPrice: newPrice,\n            bidCount: prev.bidCount + 1,\n            highestBidder: randomUser,\n          } : null);\n\n          // 更新建议出价\n          setBidAmount(newPrice + currentItem.bidIncrement);\n        }\n\n        // 模拟在线用户数变化\n        setOnlineUsers(prev => {\n          const change = Math.floor(Math.random() * 6) - 3; // -3 到 +3\n          return Math.max(5, Math.min(100, prev + change));\n        });\n      }\n    }, 5000); // 每5秒检查一次\n\n    return () => {\n      clearInterval(interval);\n      if (wsRef.current) {\n        wsRef.current.close();\n      }\n    };\n  }, [isAuctionActive, currentAuctionId, currentItem]);\n\n  // 格式化时间\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 开始拍卖\n  const handleStartAuction = async () => {\n    if (!currentAuctionId) {\n      message.error('没有选中的拍卖会');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const response = await auctionService.startAuction(currentAuctionId);\n      if (response.success) {\n        setIsAuctionActive(true);\n        message.success('拍卖已开始');\n      } else {\n        throw new Error(response.message || '开始拍卖失败');\n      }\n    } catch (error: any) {\n      console.error('开始拍卖失败:', error);\n      message.error(error.message || '开始拍卖失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 暂停拍卖\n  const handlePauseAuction = async () => {\n    if (!currentAuctionId) {\n      message.error('没有选中的拍卖会');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const response = await auctionService.pauseAuction(currentAuctionId);\n      if (response.success) {\n        setIsAuctionActive(false);\n        message.info('拍卖已暂停');\n      } else {\n        throw new Error(response.message || '暂停拍卖失败');\n      }\n    } catch (error: any) {\n      console.error('暂停拍卖失败:', error);\n      message.error(error.message || '暂停拍卖失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 结束拍卖\n  const handleEndAuction = async () => {\n    if (!currentAuctionId) {\n      message.error('没有选中的拍卖会');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const response = await auctionService.endAuction(currentAuctionId);\n      if (response.success) {\n        setIsAuctionActive(false);\n        setTimeRemaining(0);\n        message.success('拍卖已结束');\n      } else {\n        throw new Error(response.message || '结束拍卖失败');\n      }\n    } catch (error: any) {\n      console.error('结束拍卖失败:', error);\n      message.error(error.message || '结束拍卖失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 出价\n  const handleBid = () => {\n    setIsBidModalVisible(true);\n    form.setFieldsValue({ bidAmount });\n  };\n\n  // 确认出价\n  const handleConfirmBid = async (values: any) => {\n    setLoading(true);\n    try {\n      if (!currentItem) {\n        throw new Error('当前没有选中的拍卖商品');\n      }\n\n      // 调用后端API提交出价\n      const response = await auctionService.placeBid({\n        itemId: currentItem.id,\n        userId: 1, // 这里应该从用户上下文获取真实的用户ID\n        price: values.bidAmount,\n      });\n\n      if (response.success) {\n        const newBid: BidRecord = {\n          id: Date.now(),\n          userId: 1,\n          username: 'admin', // 这里应该从用户上下文获取真实的用户名\n          bidAmount: values.bidAmount,\n          bidTime: new Date().toISOString(),\n          isWinning: true,\n        };\n\n        // 更新竞价记录\n        setBidRecords(prev => {\n          const updated = prev.map(record => ({ ...record, isWinning: false }));\n          return [newBid, ...updated];\n        });\n\n        // 更新当前价格\n        setCurrentItem(prev => prev ? {\n          ...prev,\n          currentPrice: values.bidAmount,\n          bidCount: prev.bidCount + 1,\n          highestBidder: 'admin',\n        } : null);\n\n        // 设置下一次出价金额\n        setBidAmount(values.bidAmount + (currentItem?.bidIncrement || 10));\n\n        message.success('出价成功');\n        setIsBidModalVisible(false);\n      } else {\n        throw new Error(response.message || '出价失败');\n      }\n    } catch (error: any) {\n      console.error('出价失败:', error);\n      message.error(error.message || '出价失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!currentItem) {\n    return (\n      <div style={{ padding: 24, textAlign: 'center' }}>\n        <Title level={3}>暂无进行中的拍卖</Title>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>实时竞价</Title>\n\n      <Row gutter={24}>\n        {/* 左侧：商品信息和控制面板 */}\n        <Col xs={24} lg={16}>\n          {/* 商品信息卡片 */}\n          <Card style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col xs={24} md={8}>\n                <img\n                  src={currentItem.images[0]}\n                  alt={currentItem.productName}\n                  style={{\n                    width: '100%',\n                    height: 200,\n                    objectFit: 'cover',\n                    borderRadius: 8,\n                  }}\n                />\n              </Col>\n              <Col xs={24} md={16}>\n                <Space direction=\"vertical\" size=\"small\" style={{ width: '100%' }}>\n                  <Title level={3} style={{ margin: 0 }}>\n                    {currentItem.productName}\n                  </Title>\n                  <Text type=\"secondary\">商品编号: {currentItem.productCode}</Text>\n                  <Text>{currentItem.description}</Text>\n\n                  <Row gutter={16}>\n                    <Col span={8}>\n                      <Statistic\n                        title=\"起拍价\"\n                        value={currentItem.startingPrice}\n                        precision={2}\n                        prefix=\"¥\"\n                      />\n                    </Col>\n                    <Col span={8}>\n                      <Statistic\n                        title=\"当前价格\"\n                        value={currentItem.currentPrice}\n                        precision={2}\n                        prefix=\"¥\"\n                        valueStyle={{ color: '#f50', fontSize: 24, fontWeight: 'bold' }}\n                      />\n                    </Col>\n                    <Col span={8}>\n                      <Statistic\n                        title=\"出价次数\"\n                        value={currentItem.bidCount}\n                        suffix=\"次\"\n                      />\n                    </Col>\n                  </Row>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 拍卖控制面板 */}\n          <Card title=\"拍卖控制\" style={{ marginBottom: 16 }}>\n            <Row gutter={16} align=\"middle\">\n              <Col xs={24} sm={8}>\n                <Space>\n                  <Badge\n                    status={isAuctionActive ? 'processing' : 'default'}\n                    text={\n                      <Text strong>\n                        状态: {isAuctionActive ? '进行中' : '已暂停'}\n                      </Text>\n                    }\n                  />\n                </Space>\n              </Col>\n              <Col xs={24} sm={8}>\n                <Statistic\n                  title=\"剩余时间\"\n                  value={formatTime(timeRemaining)}\n                  prefix={<ClockCircleOutlined />}\n                  valueStyle={{\n                    color: timeRemaining < 60 ? '#f50' : '#1890ff',\n                    fontSize: 20,\n                    fontWeight: 'bold',\n                  }}\n                />\n                <Progress\n                  percent={Math.max(0, (timeRemaining / 300) * 100)}\n                  showInfo={false}\n                  strokeColor={timeRemaining < 60 ? '#f50' : '#1890ff'}\n                />\n              </Col>\n              <Col xs={24} sm={8}>\n                <Space>\n                  {!isAuctionActive ? (\n                    <Button\n                      type=\"primary\"\n                      icon={<PlayCircleOutlined />}\n                      onClick={handleStartAuction}\n                      disabled={timeRemaining === 0}\n                      loading={loading}\n                    >\n                      开始拍卖\n                    </Button>\n                  ) : (\n                    <Button\n                      icon={<PauseCircleOutlined />}\n                      onClick={handlePauseAuction}\n                      loading={loading}\n                    >\n                      暂停拍卖\n                    </Button>\n                  )}\n                  <Button\n                    danger\n                    icon={<StopOutlined />}\n                    onClick={handleEndAuction}\n                    loading={loading}\n                  >\n                    结束拍卖\n                  </Button>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 出价面板 */}\n          <Card title=\"快速出价\">\n            <Row gutter={16} align=\"middle\">\n              <Col xs={24} sm={12}>\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Text>建议出价: ¥{bidAmount.toFixed(2)}</Text>\n                  <Text type=\"secondary\">\n                    加价幅度: ¥{currentItem.bidIncrement.toFixed(2)}\n                  </Text>\n                  {currentItem.reservePrice && (\n                    <Text type=\"warning\">\n                      保留价: ¥{currentItem.reservePrice.toFixed(2)}\n                    </Text>\n                  )}\n                </Space>\n              </Col>\n              <Col xs={24} sm={12}>\n                <Space>\n                  <Button\n                    type=\"primary\"\n                    size=\"large\"\n                    icon={<AuditOutlined />}\n                    onClick={handleBid}\n                    disabled={!isAuctionActive || timeRemaining === 0}\n                  >\n                    出价 ¥{bidAmount.toFixed(2)}\n                  </Button>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 右侧：竞价记录和在线用户 */}\n        <Col xs={24} lg={8}>\n          {/* 在线统计 */}\n          <Card size=\"small\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Statistic\n                  title=\"在线用户\"\n                  value={onlineUsers}\n                  prefix={<UserOutlined />}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"最高出价人\"\n                  value={currentItem.highestBidder || '暂无'}\n                  prefix={<TrophyOutlined />}\n                />\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 竞价记录 */}\n          <Card title=\"竞价记录\" size=\"small\">\n            <List\n              dataSource={bidRecords}\n              renderItem={(record) => (\n                <List.Item\n                  style={{\n                    padding: '8px 0',\n                    backgroundColor: record.isWinning ? '#f6ffed' : 'transparent',\n                    borderRadius: record.isWinning ? 4 : 0,\n                    paddingLeft: record.isWinning ? 8 : 0,\n                  }}\n                >\n                  <List.Item.Meta\n                    avatar={\n                      <Avatar\n                        src={record.avatar}\n                        icon={<UserOutlined />}\n                        style={{\n                          backgroundColor: record.isWinning ? '#52c41a' : '#1890ff',\n                        }}\n                      />\n                    }\n                    title={\n                      <Space>\n                        <Text strong={record.isWinning}>\n                          {record.username}\n                        </Text>\n                        {record.isWinning && (\n                          <Tag color=\"green\">\n                            最高价\n                          </Tag>\n                        )}\n                      </Space>\n                    }\n                    description={\n                      <Space direction=\"vertical\" size={0}>\n                        <Text strong style={{ color: '#f50' }}>\n                          ¥{record.bidAmount.toFixed(2)}\n                        </Text>\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          {new Date(record.bidTime).toLocaleTimeString()}\n                        </Text>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n              style={{ maxHeight: 400, overflowY: 'auto' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 出价确认模态框 */}\n      <Modal\n        title=\"确认出价\"\n        open={isBidModalVisible}\n        onCancel={() => setIsBidModalVisible(false)}\n        footer={null}\n        width={400}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleConfirmBid}\n          autoComplete=\"off\"\n        >\n          <Alert\n            message=\"出价提醒\"\n            description={`当前最高价: ¥${currentItem.currentPrice.toFixed(2)}，最小加价幅度: ¥${currentItem.bidIncrement.toFixed(2)}`}\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n\n          <Form.Item\n            name=\"bidAmount\"\n            label=\"出价金额\"\n            rules={[\n              { required: true, message: '请输入出价金额' },\n              {\n                type: 'number',\n                min: currentItem.currentPrice + currentItem.bidIncrement,\n                message: `出价必须高于当前价格 ¥${(currentItem.currentPrice + currentItem.bidIncrement).toFixed(2)}`,\n              },\n            ]}\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              precision={2}\n              min={currentItem.currentPrice + currentItem.bidIncrement}\n              step={currentItem.bidIncrement}\n              addonBefore=\"¥\"\n              placeholder=\"请输入出价金额\"\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setIsBidModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n                确认出价\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default LiveBidding;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,MAAM,EAENC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,KAAK,QACA,MAAM;AACb,SACEC,kBAAkB,EAClBC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,mBAAmB,EACnBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,cAAc,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGvB,UAAU;;AAElC;;AAWA;;AAiBA,MAAMwB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAgB,IAAI,CAAC;EAC7E,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAyB,IAAI,CAAC;EAC5E,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAS,CAAC,CAAC;EACrD,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoD,IAAI,CAAC,GAAGnC,IAAI,CAACoC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGpD,MAAM,CAAwB,IAAI,CAAC;EACpD,MAAMqD,KAAK,GAAGrD,MAAM,CAAmB,IAAI,CAAC;;EAE5C;EACA,MAAMsD,eAAgC,GAAG;IACvCC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,CACN,oEAAoE,EACpE,oEAAoE,CACrE;IACDC,aAAa,EAAE,MAAM;IACrBC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,EAAE;IACZvB,aAAa,EAAE,GAAG;IAAE;IACpBwB,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE;EACf,CAAC;;EAED;EACA,MAAMC,cAA2B,GAAG,CAClC;IACEZ,EAAE,EAAE,CAAC;IACLa,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,SAAS;IACnB3B,SAAS,EAAE,MAAM;IACjB4B,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLa,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,cAAc;IACxB3B,SAAS,EAAE,MAAM;IACjB4B,OAAO,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAACF,WAAW,CAAC,CAAC;IACnDC,SAAS,EAAE;EACb,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLa,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,eAAe;IACzB3B,SAAS,EAAE,MAAM;IACjB4B,OAAO,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAACF,WAAW,CAAC,CAAC;IACnDC,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF;MACA,MAAMC,eAAe,GAAG,MAAMnD,cAAc,CAACoD,cAAc,CAAC;QAC1DC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAIH,eAAe,CAACI,OAAO,IAAIJ,eAAe,CAACK,IAAI,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACnE,MAAMC,OAAO,GAAGR,eAAe,CAACK,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;QAC5CG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,OAAO,CAAC;;QAE/B;QACAnD,mBAAmB,CAACmD,OAAO,CAAC7B,EAAE,CAAC;;QAE/B;QACA,MAAMgC,aAAa,GAAG,MAAM9D,cAAc,CAAC+D,wBAAwB,CAACJ,OAAO,CAAC7B,EAAE,EAAE;UAC9EuB,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,IAAIQ,aAAa,CAACP,OAAO,IAAIO,aAAa,CAACN,IAAI,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UAC/D,MAAMM,WAAW,GAAGF,aAAa,CAACN,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;UAC9CG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEG,WAAW,CAAC;;UAEpC;UACA,MAAMC,QAAyB,GAAG;YAChCnC,EAAE,EAAEkC,WAAW,CAAClC,EAAE;YAClBC,WAAW,EAAEiC,WAAW,CAACjC,WAAW,IAAI,MAAM;YAC9CC,WAAW,EAAE,QAAQgC,WAAW,CAAClC,EAAE,EAAE;YACrCG,MAAM,EAAE+B,WAAW,CAAC/B,MAAM,IAAI,CAC5B,oEAAoE,CACrE;YACDC,aAAa,EAAE8B,WAAW,CAAC9B,aAAa,IAAI,CAAC;YAC7CC,YAAY,EAAE6B,WAAW,CAAC7B,YAAY,IAAI6B,WAAW,CAAC9B,aAAa,IAAI,CAAC;YACxEE,YAAY,EAAE,CAAC;YAAE;YACjBC,YAAY,EAAE,EAAE;YAAE;YAClBC,QAAQ,EAAE0B,WAAW,CAAC1B,QAAQ,IAAI,CAAC;YACnCvB,aAAa,EAAE,GAAG;YAAE;YACpBwB,MAAM,EAAEyB,WAAW,CAACzB,MAAM,KAAK,GAAG,IAAIyB,WAAW,CAACzB,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;YAC9FC,aAAa,EAAE,IAAI;YACnBC,WAAW,EAAE,QAAQuB,WAAW,CAACjC,WAAW;UAC9C,CAAC;UAEDrB,cAAc,CAACuD,QAAQ,CAAC;UACxBjD,gBAAgB,CAACiD,QAAQ,CAAClD,aAAa,CAAC;UACxCG,YAAY,CAAC+C,QAAQ,CAAC9B,YAAY,GAAG8B,QAAQ,CAAC5B,YAAY,CAAC;;UAE3D;UACA,IAAI2B,WAAW,CAACzB,MAAM,KAAK,GAAG,IAAIyB,WAAW,CAACzB,MAAM,KAAK,SAAS,EAAE;YAClEzB,kBAAkB,CAAC,IAAI,CAAC;UAC1B;QACF,CAAC,MAAM;UACL;UACA,MAAMoD,WAAW,GAAIP,OAAO,CAASQ,IAAI,IAAIR,OAAO,CAACS,KAAK;UAC1D,MAAMH,QAAyB,GAAG;YAChC,GAAGpC,eAAe;YAClBC,EAAE,EAAE6B,OAAO,CAAC7B,EAAE;YACdC,WAAW,EAAE,GAAGmC,WAAW,SAAS;YACpCzB,WAAW,EAAEkB,OAAO,CAAClB,WAAW,IAAI,OAAOyB,WAAW;UACxD,CAAC;UAEDxD,cAAc,CAACuD,QAAQ,CAAC;UACxBjD,gBAAgB,CAACiD,QAAQ,CAAClD,aAAa,CAAC;UACxCG,YAAY,CAAC+C,QAAQ,CAAC9B,YAAY,GAAG8B,QAAQ,CAAC5B,YAAY,CAAC;QAC7D;;QAEA;QACAgC,eAAe,CAACV,OAAO,CAAC7B,EAAE,CAAC;MAC7B,CAAC,MAAM;QACL;QACA8B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;QAC7BnD,cAAc,CAACmB,eAAe,CAAC;QAC/BjB,aAAa,CAAC8B,cAAc,CAAC;QAC7B1B,gBAAgB,CAACa,eAAe,CAACd,aAAa,CAAC;QAC/CG,YAAY,CAACW,eAAe,CAACM,YAAY,GAAGN,eAAe,CAACQ,YAAY,CAAC;MAC3E;MAEAb,cAAc,CAAC8C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACrD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;MACA/D,cAAc,CAACmB,eAAe,CAAC;MAC/BjB,aAAa,CAAC8B,cAAc,CAAC;MAC7B1B,gBAAgB,CAACa,eAAe,CAACd,aAAa,CAAC;MAC/CG,YAAY,CAACW,eAAe,CAACM,YAAY,GAAGN,eAAe,CAACQ,YAAY,CAAC;MACzEb,cAAc,CAAC8C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMH,eAAe,GAAG,MAAOK,SAAiB,IAAK;IACnD,IAAI;MACFd,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEa,SAAS,CAAC;;MAEpC;MACA,MAAMC,QAAQ,GAAG,MAAM3E,cAAc,CAAC4E,aAAa,CAACF,SAAS,EAAEG,SAAS,EAAE;QACxExB,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAIqB,QAAQ,CAACpB,OAAO,IAAIoB,QAAQ,CAACnB,IAAI,IAAImB,QAAQ,CAACnB,IAAI,CAACC,IAAI,EAAE;QAC3D;QACA,MAAM9C,UAAuB,GAAGgE,QAAQ,CAACnB,IAAI,CAACC,IAAI,CAACqB,GAAG,CAAEC,MAAW,KAAM;UACvEjD,EAAE,EAAEiD,MAAM,CAACjD,EAAE;UACba,MAAM,EAAEoC,MAAM,CAACpC,MAAM,IAAIoC,MAAM,CAACC,OAAO;UACvCpC,QAAQ,EAAEmC,MAAM,CAACE,UAAU,IAAIF,MAAM,CAACG,WAAW,IAAIH,MAAM,CAACnC,QAAQ,IAAI,KAAKmC,MAAM,CAACpC,MAAM,IAAIoC,MAAM,CAACC,OAAO,EAAE;UAC9G/D,SAAS,EAAE8D,MAAM,CAACI,KAAK,IAAIJ,MAAM,CAAC9D,SAAS,IAAI8D,MAAM,CAACK,MAAM;UAC5DvC,OAAO,EAAEkC,MAAM,CAACM,SAAS,IAAIN,MAAM,CAACO,UAAU,IAAIP,MAAM,CAAClC,OAAO;UAChEG,SAAS,EAAE+B,MAAM,CAACxC,MAAM,KAAK,CAAC,IAAIwC,MAAM,CAAC/B,SAAS,IAAI;QACxD,CAAC,CAAC,CAAC;QAEHpC,aAAa,CAACD,UAAU,CAAC;QACzBiD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAElD,UAAU,CAAC;MACvC,CAAC,MAAM;QACL;QACAiD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC5BjD,aAAa,CAAC8B,cAAc,CAAC;MAC/B;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;MACA7D,aAAa,CAAC8B,cAAc,CAAC;IAC/B;EACF,CAAC;;EAED;EACApE,SAAS,CAAC,MAAM;IACd4E,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5E,SAAS,CAAC,MAAM;IACd,IAAIuC,eAAe,IAAIE,aAAa,GAAG,CAAC,EAAE;MACxCY,QAAQ,CAAC4D,OAAO,GAAGC,WAAW,CAAC,MAAM;QACnCxE,gBAAgB,CAACyE,IAAI,IAAI;UACvB,IAAIA,IAAI,IAAI,CAAC,EAAE;YACb3E,kBAAkB,CAAC,KAAK,CAAC;YACzBlC,OAAO,CAAC8G,IAAI,CAAC,QAAQ,CAAC;YACtB,OAAO,CAAC;UACV;UACA,OAAOD,IAAI,GAAG,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAI9D,QAAQ,CAAC4D,OAAO,EAAE;QACpBI,aAAa,CAAChE,QAAQ,CAAC4D,OAAO,CAAC;MACjC;IACF;IAEA,OAAO,MAAM;MACX,IAAI5D,QAAQ,CAAC4D,OAAO,EAAE;QACpBI,aAAa,CAAChE,QAAQ,CAAC4D,OAAO,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAAC1E,eAAe,EAAEE,aAAa,CAAC,CAAC;;EAEpC;EACAzC,SAAS,CAAC,MAAM;IACd;IACA,MAAMsH,QAAQ,GAAGJ,WAAW,CAAC,MAAM;MACjC,IAAI3E,eAAe,IAAIN,gBAAgB,EAAE;QACvC;QACA,MAAMsF,YAAY,GAAGvB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAC1C,IAAIqB,YAAY,IAAIpF,WAAW,EAAE;UAC/B,MAAMqF,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UAClD,MAAMC,UAAU,GAAGD,WAAW,CAACxB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGsB,WAAW,CAACpC,MAAM,CAAC,CAAC;UAC9E,MAAMsC,QAAQ,GAAGvF,WAAW,CAAC0B,YAAY,GAAG1B,WAAW,CAAC4B,YAAY,GAAGiC,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE;UAEzF,MAAMyB,MAAiB,GAAG;YACxBnE,EAAE,EAAEgB,IAAI,CAACG,GAAG,CAAC,CAAC;YACdN,MAAM,EAAE2B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;YACxC5B,QAAQ,EAAEmD,UAAU;YACpB9E,SAAS,EAAE+E,QAAQ;YACnBnD,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACjCC,SAAS,EAAE;UACb,CAAC;;UAED;UACApC,aAAa,CAAC6E,IAAI,IAAI;YACpB,MAAMS,OAAO,GAAGT,IAAI,CAACX,GAAG,CAACC,MAAM,KAAK;cAAE,GAAGA,MAAM;cAAE/B,SAAS,EAAE;YAAM,CAAC,CAAC,CAAC;YACrE,OAAO,CAACiD,MAAM,EAAE,GAAGC,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC;;UAEF;UACAzF,cAAc,CAAC+E,IAAI,IAAIA,IAAI,GAAG;YAC5B,GAAGA,IAAI;YACPtD,YAAY,EAAE6D,QAAQ;YACtB1D,QAAQ,EAAEmD,IAAI,CAACnD,QAAQ,GAAG,CAAC;YAC3BE,aAAa,EAAEuD;UACjB,CAAC,GAAG,IAAI,CAAC;;UAET;UACA7E,YAAY,CAAC8E,QAAQ,GAAGvF,WAAW,CAAC4B,YAAY,CAAC;QACnD;;QAEA;QACAb,cAAc,CAACiE,IAAI,IAAI;UACrB,MAAMW,MAAM,GAAG9B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClD,OAAOF,IAAI,CAAC+B,GAAG,CAAC,CAAC,EAAE/B,IAAI,CAACgC,GAAG,CAAC,GAAG,EAAEb,IAAI,GAAGW,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAM;MACXT,aAAa,CAACC,QAAQ,CAAC;MACvB,IAAIhE,KAAK,CAAC2D,OAAO,EAAE;QACjB3D,KAAK,CAAC2D,OAAO,CAACgB,KAAK,CAAC,CAAC;MACvB;IACF,CAAC;EACH,CAAC,EAAE,CAAC1F,eAAe,EAAEN,gBAAgB,EAAEE,WAAW,CAAC,CAAC;;EAEpD;EACA,MAAM+F,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAGpC,IAAI,CAACC,KAAK,CAACkC,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACvG,gBAAgB,EAAE;MACrB3B,OAAO,CAAC6F,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAI;MACFnD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqD,QAAQ,GAAG,MAAM3E,cAAc,CAAC+G,YAAY,CAACxG,gBAAgB,CAAC;MACpE,IAAIoE,QAAQ,CAACpB,OAAO,EAAE;QACpBzC,kBAAkB,CAAC,IAAI,CAAC;QACxBlC,OAAO,CAAC2E,OAAO,CAAC,OAAO,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAIyD,KAAK,CAACrC,QAAQ,CAAC/F,OAAO,IAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAO6F,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7F,OAAO,CAAC6F,KAAK,CAACA,KAAK,CAAC7F,OAAO,IAAI,QAAQ,CAAC;IAC1C,CAAC,SAAS;MACR0C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2F,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAC1G,gBAAgB,EAAE;MACrB3B,OAAO,CAAC6F,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAI;MACFnD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqD,QAAQ,GAAG,MAAM3E,cAAc,CAACkH,YAAY,CAAC3G,gBAAgB,CAAC;MACpE,IAAIoE,QAAQ,CAACpB,OAAO,EAAE;QACpBzC,kBAAkB,CAAC,KAAK,CAAC;QACzBlC,OAAO,CAAC8G,IAAI,CAAC,OAAO,CAAC;MACvB,CAAC,MAAM;QACL,MAAM,IAAIsB,KAAK,CAACrC,QAAQ,CAAC/F,OAAO,IAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAO6F,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7F,OAAO,CAAC6F,KAAK,CAACA,KAAK,CAAC7F,OAAO,IAAI,QAAQ,CAAC;IAC1C,CAAC,SAAS;MACR0C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6F,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC5G,gBAAgB,EAAE;MACrB3B,OAAO,CAAC6F,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAI;MACFnD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqD,QAAQ,GAAG,MAAM3E,cAAc,CAACoH,UAAU,CAAC7G,gBAAgB,CAAC;MAClE,IAAIoE,QAAQ,CAACpB,OAAO,EAAE;QACpBzC,kBAAkB,CAAC,KAAK,CAAC;QACzBE,gBAAgB,CAAC,CAAC,CAAC;QACnBpC,OAAO,CAAC2E,OAAO,CAAC,OAAO,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAIyD,KAAK,CAACrC,QAAQ,CAAC/F,OAAO,IAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAO6F,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7F,OAAO,CAAC6F,KAAK,CAACA,KAAK,CAAC7F,OAAO,IAAI,QAAQ,CAAC;IAC1C,CAAC,SAAS;MACR0C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+F,SAAS,GAAGA,CAAA,KAAM;IACtBjG,oBAAoB,CAAC,IAAI,CAAC;IAC1BK,IAAI,CAAC6F,cAAc,CAAC;MAAErG;IAAU,CAAC,CAAC;EACpC,CAAC;;EAED;EACA,MAAMsG,gBAAgB,GAAG,MAAOC,MAAW,IAAK;IAC9ClG,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAI,CAACb,WAAW,EAAE;QAChB,MAAM,IAAIuG,KAAK,CAAC,aAAa,CAAC;MAChC;;MAEA;MACA,MAAMrC,QAAQ,GAAG,MAAM3E,cAAc,CAACyH,QAAQ,CAAC;QAC7CC,MAAM,EAAEjH,WAAW,CAACqB,EAAE;QACtBa,MAAM,EAAE,CAAC;QAAE;QACXwC,KAAK,EAAEqC,MAAM,CAACvG;MAChB,CAAC,CAAC;MAEF,IAAI0D,QAAQ,CAACpB,OAAO,EAAE;QACpB,MAAM0C,MAAiB,GAAG;UACxBnE,EAAE,EAAEgB,IAAI,CAACG,GAAG,CAAC,CAAC;UACdN,MAAM,EAAE,CAAC;UACTC,QAAQ,EAAE,OAAO;UAAE;UACnB3B,SAAS,EAAEuG,MAAM,CAACvG,SAAS;UAC3B4B,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACjCC,SAAS,EAAE;QACb,CAAC;;QAED;QACApC,aAAa,CAAC6E,IAAI,IAAI;UACpB,MAAMS,OAAO,GAAGT,IAAI,CAACX,GAAG,CAACC,MAAM,KAAK;YAAE,GAAGA,MAAM;YAAE/B,SAAS,EAAE;UAAM,CAAC,CAAC,CAAC;UACrE,OAAO,CAACiD,MAAM,EAAE,GAAGC,OAAO,CAAC;QAC7B,CAAC,CAAC;;QAEF;QACAxF,cAAc,CAAC+E,IAAI,IAAIA,IAAI,GAAG;UAC5B,GAAGA,IAAI;UACPtD,YAAY,EAAEqF,MAAM,CAACvG,SAAS;UAC9BqB,QAAQ,EAAEmD,IAAI,CAACnD,QAAQ,GAAG,CAAC;UAC3BE,aAAa,EAAE;QACjB,CAAC,GAAG,IAAI,CAAC;;QAET;QACAtB,YAAY,CAACsG,MAAM,CAACvG,SAAS,IAAI,CAAAR,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4B,YAAY,KAAI,EAAE,CAAC,CAAC;QAElEzD,OAAO,CAAC2E,OAAO,CAAC,MAAM,CAAC;QACvBnC,oBAAoB,CAAC,KAAK,CAAC;MAC7B,CAAC,MAAM;QACL,MAAM,IAAI4F,KAAK,CAACrC,QAAQ,CAAC/F,OAAO,IAAI,MAAM,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO6F,KAAU,EAAE;MACnBb,OAAO,CAACa,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B7F,OAAO,CAAC6F,KAAK,CAACA,KAAK,CAAC7F,OAAO,IAAI,MAAM,CAAC;IACxC,CAAC,SAAS;MACR0C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACb,WAAW,EAAE;IAChB,oBACEP,OAAA;MAAKyH,KAAK,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eAC/C5H,OAAA,CAACC,KAAK;QAAC4H,KAAK,EAAE,CAAE;QAAAD,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEV;EAEA,oBACEjI,OAAA;IAAKyH,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAE,QAAA,gBAC1B5H,OAAA,CAACC,KAAK;MAAC4H,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAE7BjI,OAAA,CAACzB,GAAG;MAAC2J,MAAM,EAAE,EAAG;MAAAN,QAAA,gBAEd5H,OAAA,CAACxB,GAAG;QAAC2J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAR,QAAA,gBAElB5H,OAAA,CAAC1B,IAAI;UAACmJ,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,eAChC5H,OAAA,CAACzB,GAAG;YAAC2J,MAAM,EAAE,EAAG;YAAAN,QAAA,gBACd5H,OAAA,CAACxB,GAAG;cAAC2J,EAAE,EAAE,EAAG;cAACG,EAAE,EAAE,CAAE;cAAAV,QAAA,eACjB5H,OAAA;gBACEuI,GAAG,EAAEhI,WAAW,CAACwB,MAAM,CAAC,CAAC,CAAE;gBAC3ByG,GAAG,EAAEjI,WAAW,CAACsB,WAAY;gBAC7B4F,KAAK,EAAE;kBACLgB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,GAAG;kBACXC,SAAS,EAAE,OAAO;kBAClBC,YAAY,EAAE;gBAChB;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjI,OAAA,CAACxB,GAAG;cAAC2J,EAAE,EAAE,EAAG;cAACG,EAAE,EAAE,EAAG;cAAAV,QAAA,eAClB5H,OAAA,CAACpB,KAAK;gBAACiK,SAAS,EAAC,UAAU;gBAACC,IAAI,EAAC,OAAO;gBAACrB,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBAAAb,QAAA,gBAChE5H,OAAA,CAACC,KAAK;kBAAC4H,KAAK,EAAE,CAAE;kBAACJ,KAAK,EAAE;oBAAEsB,MAAM,EAAE;kBAAE,CAAE;kBAAAnB,QAAA,EACnCrH,WAAW,CAACsB;gBAAW;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACRjI,OAAA,CAACE,IAAI;kBAAC8I,IAAI,EAAC,WAAW;kBAAApB,QAAA,GAAC,4BAAM,EAACrH,WAAW,CAACuB,WAAW;gBAAA;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7DjI,OAAA,CAACE,IAAI;kBAAA0H,QAAA,EAAErH,WAAW,CAACgC;gBAAW;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAEtCjI,OAAA,CAACzB,GAAG;kBAAC2J,MAAM,EAAE,EAAG;kBAAAN,QAAA,gBACd5H,OAAA,CAACxB,GAAG;oBAACyK,IAAI,EAAE,CAAE;oBAAArB,QAAA,eACX5H,OAAA,CAAChB,SAAS;sBACRkF,KAAK,EAAC,oBAAK;sBACXgF,KAAK,EAAE3I,WAAW,CAACyB,aAAc;sBACjCmH,SAAS,EAAE,CAAE;sBACbC,MAAM,EAAC;oBAAG;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNjI,OAAA,CAACxB,GAAG;oBAACyK,IAAI,EAAE,CAAE;oBAAArB,QAAA,eACX5H,OAAA,CAAChB,SAAS;sBACRkF,KAAK,EAAC,0BAAM;sBACZgF,KAAK,EAAE3I,WAAW,CAAC0B,YAAa;sBAChCkH,SAAS,EAAE,CAAE;sBACbC,MAAM,EAAC,MAAG;sBACVC,UAAU,EAAE;wBAAEC,KAAK,EAAE,MAAM;wBAAEC,QAAQ,EAAE,EAAE;wBAAEC,UAAU,EAAE;sBAAO;oBAAE;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNjI,OAAA,CAACxB,GAAG;oBAACyK,IAAI,EAAE,CAAE;oBAAArB,QAAA,eACX5H,OAAA,CAAChB,SAAS;sBACRkF,KAAK,EAAC,0BAAM;sBACZgF,KAAK,EAAE3I,WAAW,CAAC6B,QAAS;sBAC5BqH,MAAM,EAAC;oBAAG;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPjI,OAAA,CAAC1B,IAAI;UAAC4F,KAAK,EAAC,0BAAM;UAACuD,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,eAC7C5H,OAAA,CAACzB,GAAG;YAAC2J,MAAM,EAAE,EAAG;YAACwB,KAAK,EAAC,QAAQ;YAAA9B,QAAA,gBAC7B5H,OAAA,CAACxB,GAAG;cAAC2J,EAAE,EAAE,EAAG;cAACwB,EAAE,EAAE,CAAE;cAAA/B,QAAA,eACjB5H,OAAA,CAACpB,KAAK;gBAAAgJ,QAAA,eACJ5H,OAAA,CAACd,KAAK;kBACJmD,MAAM,EAAE1B,eAAe,GAAG,YAAY,GAAG,SAAU;kBACnDiJ,IAAI,eACF5J,OAAA,CAACE,IAAI;oBAAC2J,MAAM;oBAAAjC,QAAA,GAAC,gBACP,EAACjH,eAAe,GAAG,KAAK,GAAG,KAAK;kBAAA;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBACP;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjI,OAAA,CAACxB,GAAG;cAAC2J,EAAE,EAAE,EAAG;cAACwB,EAAE,EAAE,CAAE;cAAA/B,QAAA,gBACjB5H,OAAA,CAAChB,SAAS;gBACRkF,KAAK,EAAC,0BAAM;gBACZgF,KAAK,EAAE5C,UAAU,CAACzF,aAAa,CAAE;gBACjCuI,MAAM,eAAEpJ,OAAA,CAACJ,mBAAmB;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAChCoB,UAAU,EAAE;kBACVC,KAAK,EAAEzI,aAAa,GAAG,EAAE,GAAG,MAAM,GAAG,SAAS;kBAC9C0I,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE;gBACd;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFjI,OAAA,CAACf,QAAQ;gBACP6K,OAAO,EAAE1F,IAAI,CAAC+B,GAAG,CAAC,CAAC,EAAGtF,aAAa,GAAG,GAAG,GAAI,GAAG,CAAE;gBAClDkJ,QAAQ,EAAE,KAAM;gBAChBC,WAAW,EAAEnJ,aAAa,GAAG,EAAE,GAAG,MAAM,GAAG;cAAU;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjI,OAAA,CAACxB,GAAG;cAAC2J,EAAE,EAAE,EAAG;cAACwB,EAAE,EAAE,CAAE;cAAA/B,QAAA,eACjB5H,OAAA,CAACpB,KAAK;gBAAAgJ,QAAA,GACH,CAACjH,eAAe,gBACfX,OAAA,CAACvB,MAAM;kBACLuK,IAAI,EAAC,SAAS;kBACdiB,IAAI,eAAEjK,OAAA,CAACT,kBAAkB;oBAAAuI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC7BiC,OAAO,EAAEtD,kBAAmB;kBAC5BuD,QAAQ,EAAEtJ,aAAa,KAAK,CAAE;kBAC9BM,OAAO,EAAEA,OAAQ;kBAAAyG,QAAA,EAClB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gBAETjI,OAAA,CAACvB,MAAM;kBACLwL,IAAI,eAAEjK,OAAA,CAACR,mBAAmB;oBAAAsI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC9BiC,OAAO,EAAEnD,kBAAmB;kBAC5B5F,OAAO,EAAEA,OAAQ;kBAAAyG,QAAA,EAClB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACDjI,OAAA,CAACvB,MAAM;kBACL2L,MAAM;kBACNH,IAAI,eAAEjK,OAAA,CAACP,YAAY;oBAAAqI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBiC,OAAO,EAAEjD,gBAAiB;kBAC1B9F,OAAO,EAAEA,OAAQ;kBAAAyG,QAAA,EAClB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPjI,OAAA,CAAC1B,IAAI;UAAC4F,KAAK,EAAC,0BAAM;UAAA0D,QAAA,eAChB5H,OAAA,CAACzB,GAAG;YAAC2J,MAAM,EAAE,EAAG;YAACwB,KAAK,EAAC,QAAQ;YAAA9B,QAAA,gBAC7B5H,OAAA,CAACxB,GAAG;cAAC2J,EAAE,EAAE,EAAG;cAACwB,EAAE,EAAE,EAAG;cAAA/B,QAAA,eAClB5H,OAAA,CAACpB,KAAK;gBAACiK,SAAS,EAAC,UAAU;gBAACpB,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBAAAb,QAAA,gBACnD5H,OAAA,CAACE,IAAI;kBAAA0H,QAAA,GAAC,gCAAO,EAAC7G,SAAS,CAACsJ,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CjI,OAAA,CAACE,IAAI;kBAAC8I,IAAI,EAAC,WAAW;kBAAApB,QAAA,GAAC,gCACd,EAACrH,WAAW,CAAC4B,YAAY,CAACkI,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,EACN1H,WAAW,CAAC2B,YAAY,iBACvBlC,OAAA,CAACE,IAAI;kBAAC8I,IAAI,EAAC,SAAS;kBAAApB,QAAA,GAAC,0BACb,EAACrH,WAAW,CAAC2B,YAAY,CAACmI,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjI,OAAA,CAACxB,GAAG;cAAC2J,EAAE,EAAE,EAAG;cAACwB,EAAE,EAAE,EAAG;cAAA/B,QAAA,eAClB5H,OAAA,CAACpB,KAAK;gBAAAgJ,QAAA,eACJ5H,OAAA,CAACvB,MAAM;kBACLuK,IAAI,EAAC,SAAS;kBACdF,IAAI,EAAC,OAAO;kBACZmB,IAAI,eAAEjK,OAAA,CAACN,aAAa;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBiC,OAAO,EAAE/C,SAAU;kBACnBgD,QAAQ,EAAE,CAACxJ,eAAe,IAAIE,aAAa,KAAK,CAAE;kBAAA+G,QAAA,GACnD,mBACK,EAAC7G,SAAS,CAACsJ,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNjI,OAAA,CAACxB,GAAG;QAAC2J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAR,QAAA,gBAEjB5H,OAAA,CAAC1B,IAAI;UAACwK,IAAI,EAAC,OAAO;UAACrB,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,eAC7C5H,OAAA,CAACzB,GAAG;YAAC2J,MAAM,EAAE,EAAG;YAAAN,QAAA,gBACd5H,OAAA,CAACxB,GAAG;cAACyK,IAAI,EAAE,EAAG;cAAArB,QAAA,eACZ5H,OAAA,CAAChB,SAAS;gBACRkF,KAAK,EAAC,0BAAM;gBACZgF,KAAK,EAAE7H,WAAY;gBACnB+H,MAAM,eAAEpJ,OAAA,CAACL,YAAY;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBoB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjI,OAAA,CAACxB,GAAG;cAACyK,IAAI,EAAE,EAAG;cAAArB,QAAA,eACZ5H,OAAA,CAAChB,SAAS;gBACRkF,KAAK,EAAC,gCAAO;gBACbgF,KAAK,EAAE3I,WAAW,CAAC+B,aAAa,IAAI,IAAK;gBACzC8G,MAAM,eAAEpJ,OAAA,CAACH,cAAc;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPjI,OAAA,CAAC1B,IAAI;UAAC4F,KAAK,EAAC,0BAAM;UAAC4E,IAAI,EAAC,OAAO;UAAAlB,QAAA,eAC7B5H,OAAA,CAACjB,IAAI;YACHuL,UAAU,EAAE7J,UAAW;YACvB8J,UAAU,EAAG1F,MAAM,iBACjB7E,OAAA,CAACjB,IAAI,CAACyL,IAAI;cACR/C,KAAK,EAAE;gBACLC,OAAO,EAAE,OAAO;gBAChB+C,eAAe,EAAE5F,MAAM,CAAC/B,SAAS,GAAG,SAAS,GAAG,aAAa;gBAC7D8F,YAAY,EAAE/D,MAAM,CAAC/B,SAAS,GAAG,CAAC,GAAG,CAAC;gBACtC4H,WAAW,EAAE7F,MAAM,CAAC/B,SAAS,GAAG,CAAC,GAAG;cACtC,CAAE;cAAA8E,QAAA,eAEF5H,OAAA,CAACjB,IAAI,CAACyL,IAAI,CAACG,IAAI;gBACbC,MAAM,eACJ5K,OAAA,CAAClB,MAAM;kBACLyJ,GAAG,EAAE1D,MAAM,CAAC+F,MAAO;kBACnBX,IAAI,eAAEjK,OAAA,CAACL,YAAY;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBR,KAAK,EAAE;oBACLgD,eAAe,EAAE5F,MAAM,CAAC/B,SAAS,GAAG,SAAS,GAAG;kBAClD;gBAAE;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF;gBACD/D,KAAK,eACHlE,OAAA,CAACpB,KAAK;kBAAAgJ,QAAA,gBACJ5H,OAAA,CAACE,IAAI;oBAAC2J,MAAM,EAAEhF,MAAM,CAAC/B,SAAU;oBAAA8E,QAAA,EAC5B/C,MAAM,CAACnC;kBAAQ;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,EACNpD,MAAM,CAAC/B,SAAS,iBACf9C,OAAA,CAACnB,GAAG;oBAACyK,KAAK,EAAC,OAAO;oBAAA1B,QAAA,EAAC;kBAEnB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACR;gBACD1F,WAAW,eACTvC,OAAA,CAACpB,KAAK;kBAACiK,SAAS,EAAC,UAAU;kBAACC,IAAI,EAAE,CAAE;kBAAAlB,QAAA,gBAClC5H,OAAA,CAACE,IAAI;oBAAC2J,MAAM;oBAACpC,KAAK,EAAE;sBAAE6B,KAAK,EAAE;oBAAO,CAAE;oBAAA1B,QAAA,GAAC,MACpC,EAAC/C,MAAM,CAAC9D,SAAS,CAACsJ,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACPjI,OAAA,CAACE,IAAI;oBAAC8I,IAAI,EAAC,WAAW;oBAACvB,KAAK,EAAE;sBAAE8B,QAAQ,EAAE;oBAAG,CAAE;oBAAA3B,QAAA,EAC5C,IAAIhF,IAAI,CAACiC,MAAM,CAAClC,OAAO,CAAC,CAACkI,kBAAkB,CAAC;kBAAC;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CACX;YACFR,KAAK,EAAE;cAAEqD,SAAS,EAAE,GAAG;cAAEC,SAAS,EAAE;YAAO;UAAE;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjI,OAAA,CAACb,KAAK;MACJ+E,KAAK,EAAC,0BAAM;MACZ8G,IAAI,EAAE/J,iBAAkB;MACxBgK,QAAQ,EAAEA,CAAA,KAAM/J,oBAAoB,CAAC,KAAK,CAAE;MAC5CgK,MAAM,EAAE,IAAK;MACbzC,KAAK,EAAE,GAAI;MAAAb,QAAA,eAEX5H,OAAA,CAACZ,IAAI;QACHmC,IAAI,EAAEA,IAAK;QACX4J,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE/D,gBAAiB;QAC3BgE,YAAY,EAAC,KAAK;QAAAzD,QAAA,gBAElB5H,OAAA,CAACV,KAAK;UACJZ,OAAO,EAAC,0BAAM;UACd6D,WAAW,EAAE,WAAWhC,WAAW,CAAC0B,YAAY,CAACoI,OAAO,CAAC,CAAC,CAAC,aAAa9J,WAAW,CAAC4B,YAAY,CAACkI,OAAO,CAAC,CAAC,CAAC,EAAG;UAC9GrB,IAAI,EAAC,MAAM;UACXsC,QAAQ;UACR7D,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAG;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFjI,OAAA,CAACZ,IAAI,CAACoL,IAAI;UACRvG,IAAI,EAAC,WAAW;UAChBsH,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE/M,OAAO,EAAE;UAAU,CAAC,EACtC;YACEsK,IAAI,EAAE,QAAQ;YACd5C,GAAG,EAAE7F,WAAW,CAAC0B,YAAY,GAAG1B,WAAW,CAAC4B,YAAY;YACxDzD,OAAO,EAAE,eAAe,CAAC6B,WAAW,CAAC0B,YAAY,GAAG1B,WAAW,CAAC4B,YAAY,EAAEkI,OAAO,CAAC,CAAC,CAAC;UAC1F,CAAC,CACD;UAAAzC,QAAA,eAEF5H,OAAA,CAACX,WAAW;YACVoI,KAAK,EAAE;cAAEgB,KAAK,EAAE;YAAO,CAAE;YACzBU,SAAS,EAAE,CAAE;YACb/C,GAAG,EAAE7F,WAAW,CAAC0B,YAAY,GAAG1B,WAAW,CAAC4B,YAAa;YACzDuJ,IAAI,EAAEnL,WAAW,CAAC4B,YAAa;YAC/BwJ,WAAW,EAAC,MAAG;YACfC,WAAW,EAAC;UAAS;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZjI,OAAA,CAACZ,IAAI,CAACoL,IAAI;UAAA5C,QAAA,eACR5H,OAAA,CAACpB,KAAK;YAAC6I,KAAK,EAAE;cAAEgB,KAAK,EAAE,MAAM;cAAEoD,cAAc,EAAE;YAAW,CAAE;YAAAjE,QAAA,gBAC1D5H,OAAA,CAACvB,MAAM;cAACyL,OAAO,EAAEA,CAAA,KAAMhJ,oBAAoB,CAAC,KAAK,CAAE;cAAA0G,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjI,OAAA,CAACvB,MAAM;cAACuK,IAAI,EAAC,SAAS;cAAC8C,QAAQ,EAAC,QAAQ;cAAC3K,OAAO,EAAEA,OAAQ;cAAAyG,QAAA,EAAC;YAE3D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC7H,EAAA,CA/sBID,WAAqB;EAAA,QAUVf,IAAI,CAACoC,OAAO;AAAA;AAAAuK,EAAA,GAVvB5L,WAAqB;AAitB3B,eAAeA,WAAW;AAAC,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}