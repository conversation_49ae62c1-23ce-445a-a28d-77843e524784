{"ast": null, "code": "import React from 'react';\nimport AppContext from './context';\nconst useApp = () => React.useContext(AppContext);\nexport default useApp;", "map": {"version": 3, "names": ["React", "AppContext", "useApp", "useContext"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/app/useApp.js"], "sourcesContent": ["import React from 'react';\nimport AppContext from './context';\nconst useApp = () => React.useContext(AppContext);\nexport default useApp;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,WAAW;AAClC,MAAMC,MAAM,GAAGA,CAAA,KAAMF,KAAK,CAACG,UAAU,CAACF,UAAU,CAAC;AACjD,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}