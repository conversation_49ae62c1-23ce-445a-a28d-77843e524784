{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext } from 'react';\nimport RCTour from '@rc-component/tour';\nimport classNames from 'classnames';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport getPlacements from '../_util/placements';\nimport zIndexContext from '../_util/zindexContext';\nimport { ConfigContext } from '../config-provider';\nimport { useToken } from '../theme/internal';\nimport TourPanel from './panelRender';\nimport PurePanel from './PurePanel';\nimport useStyle from './style';\nconst Tour = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      type,\n      rootClassName,\n      indicatorsRender,\n      actionsRender,\n      steps,\n      closeIcon\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"rootClassName\", \"indicatorsRender\", \"actionsRender\", \"steps\", \"closeIcon\"]);\n  const {\n    getPrefixCls,\n    direction,\n    tour\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('tour', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const [, token] = useToken();\n  const mergedSteps = React.useMemo(() => steps === null || steps === void 0 ? void 0 : steps.map(step => {\n    var _a;\n    return Object.assign(Object.assign({}, step), {\n      className: classNames(step.className, {\n        [`${prefixCls}-primary`]: ((_a = step.type) !== null && _a !== void 0 ? _a : type) === 'primary'\n      })\n    });\n  }), [steps, type]);\n  const builtinPlacements = config => {\n    var _a;\n    return getPlacements({\n      arrowPointAtCenter: (_a = config === null || config === void 0 ? void 0 : config.arrowPointAtCenter) !== null && _a !== void 0 ? _a : true,\n      autoAdjustOverflow: true,\n      offset: token.marginXXS,\n      arrowWidth: token.sizePopupArrow,\n      borderRadius: token.borderRadius\n    });\n  };\n  const customClassName = classNames({\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, hashId, cssVarCls, rootClassName);\n  const mergedRenderPanel = (stepProps, stepCurrent) => (/*#__PURE__*/React.createElement(TourPanel, {\n    type: type,\n    stepProps: stepProps,\n    current: stepCurrent,\n    indicatorsRender: indicatorsRender,\n    actionsRender: actionsRender\n  }));\n  // ============================ zIndex ============================\n  const [zIndex, contextZIndex] = useZIndex('Tour', restProps.zIndex);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(zIndexContext.Provider, {\n    value: contextZIndex\n  }, /*#__PURE__*/React.createElement(RCTour, Object.assign({}, restProps, {\n    closeIcon: closeIcon !== null && closeIcon !== void 0 ? closeIcon : tour === null || tour === void 0 ? void 0 : tour.closeIcon,\n    zIndex: zIndex,\n    rootClassName: customClassName,\n    prefixCls: prefixCls,\n    animated: true,\n    renderPanel: mergedRenderPanel,\n    builtinPlacements: builtinPlacements,\n    steps: mergedSteps\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Tour.displayName = 'Tour';\n}\nTour._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default Tour;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useContext", "RCTour", "classNames", "useZIndex", "getPlacements", "zIndexContext", "ConfigContext", "useToken", "TourPanel", "PurePanel", "useStyle", "Tour", "props", "prefixCls", "customizePrefixCls", "type", "rootClassName", "<PERSON><PERSON><PERSON>", "actionsRender", "steps", "closeIcon", "restProps", "getPrefixCls", "direction", "tour", "wrapCSSVar", "hashId", "cssVarCls", "token", "mergedSteps", "useMemo", "map", "step", "_a", "assign", "className", "builtinPlacements", "config", "arrowPointAtCenter", "autoAdjustOverflow", "offset", "marginXXS", "arrow<PERSON>idth", "sizePopupArrow", "borderRadius", "customClassName", "mergedRenderPanel", "stepProps", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "current", "zIndex", "contextZIndex", "Provider", "value", "animated", "renderPanel", "process", "env", "NODE_ENV", "displayName", "_InternalPanelDoNotUseOrYouWillBeFired"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/tour/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext } from 'react';\nimport RCTour from '@rc-component/tour';\nimport classNames from 'classnames';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport getPlacements from '../_util/placements';\nimport zIndexContext from '../_util/zindexContext';\nimport { ConfigContext } from '../config-provider';\nimport { useToken } from '../theme/internal';\nimport TourPanel from './panelRender';\nimport PurePanel from './PurePanel';\nimport useStyle from './style';\nconst Tour = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      type,\n      rootClassName,\n      indicatorsRender,\n      actionsRender,\n      steps,\n      closeIcon\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"rootClassName\", \"indicatorsRender\", \"actionsRender\", \"steps\", \"closeIcon\"]);\n  const {\n    getPrefixCls,\n    direction,\n    tour\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('tour', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const [, token] = useToken();\n  const mergedSteps = React.useMemo(() => steps === null || steps === void 0 ? void 0 : steps.map(step => {\n    var _a;\n    return Object.assign(Object.assign({}, step), {\n      className: classNames(step.className, {\n        [`${prefixCls}-primary`]: ((_a = step.type) !== null && _a !== void 0 ? _a : type) === 'primary'\n      })\n    });\n  }), [steps, type]);\n  const builtinPlacements = config => {\n    var _a;\n    return getPlacements({\n      arrowPointAtCenter: (_a = config === null || config === void 0 ? void 0 : config.arrowPointAtCenter) !== null && _a !== void 0 ? _a : true,\n      autoAdjustOverflow: true,\n      offset: token.marginXXS,\n      arrowWidth: token.sizePopupArrow,\n      borderRadius: token.borderRadius\n    });\n  };\n  const customClassName = classNames({\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, hashId, cssVarCls, rootClassName);\n  const mergedRenderPanel = (stepProps, stepCurrent) => (/*#__PURE__*/React.createElement(TourPanel, {\n    type: type,\n    stepProps: stepProps,\n    current: stepCurrent,\n    indicatorsRender: indicatorsRender,\n    actionsRender: actionsRender\n  }));\n  // ============================ zIndex ============================\n  const [zIndex, contextZIndex] = useZIndex('Tour', restProps.zIndex);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(zIndexContext.Provider, {\n    value: contextZIndex\n  }, /*#__PURE__*/React.createElement(RCTour, Object.assign({}, restProps, {\n    closeIcon: closeIcon !== null && closeIcon !== void 0 ? closeIcon : tour === null || tour === void 0 ? void 0 : tour.closeIcon,\n    zIndex: zIndex,\n    rootClassName: customClassName,\n    prefixCls: prefixCls,\n    animated: true,\n    renderPanel: mergedRenderPanel,\n    builtinPlacements: builtinPlacements,\n    steps: mergedSteps\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Tour.displayName = 'Tour';\n}\nTour._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default Tour;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,SAAS,QAAQ,0BAA0B;AACpD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,OAAOC,SAAS,MAAM,eAAe;AACrC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,IAAI,GAAGC,KAAK,IAAI;EACpB,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,IAAI;MACJC,aAAa;MACbC,gBAAgB;MAChBC,aAAa;MACbC,KAAK;MACLC;IACF,CAAC,GAAGR,KAAK;IACTS,SAAS,GAAGpC,MAAM,CAAC2B,KAAK,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;EAC9H,MAAM;IACJU,YAAY;IACZC,SAAS;IACTC;EACF,CAAC,GAAGxB,UAAU,CAACM,aAAa,CAAC;EAC7B,MAAMO,SAAS,GAAGS,YAAY,CAAC,MAAM,EAAER,kBAAkB,CAAC;EAC1D,MAAM,CAACW,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAACG,SAAS,CAAC;EAC3D,MAAM,GAAGe,KAAK,CAAC,GAAGrB,QAAQ,CAAC,CAAC;EAC5B,MAAMsB,WAAW,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,MAAMX,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACY,GAAG,CAACC,IAAI,IAAI;IACtG,IAAIC,EAAE;IACN,OAAO3C,MAAM,CAAC4C,MAAM,CAAC5C,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAEF,IAAI,CAAC,EAAE;MAC5CG,SAAS,EAAEjC,UAAU,CAAC8B,IAAI,CAACG,SAAS,EAAE;QACpC,CAAC,GAAGtB,SAAS,UAAU,GAAG,CAAC,CAACoB,EAAE,GAAGD,IAAI,CAACjB,IAAI,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGlB,IAAI,MAAM;MACzF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC,EAAE,CAACI,KAAK,EAAEJ,IAAI,CAAC,CAAC;EAClB,MAAMqB,iBAAiB,GAAGC,MAAM,IAAI;IAClC,IAAIJ,EAAE;IACN,OAAO7B,aAAa,CAAC;MACnBkC,kBAAkB,EAAE,CAACL,EAAE,GAAGI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,kBAAkB,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;MAC1IM,kBAAkB,EAAE,IAAI;MACxBC,MAAM,EAAEZ,KAAK,CAACa,SAAS;MACvBC,UAAU,EAAEd,KAAK,CAACe,cAAc;MAChCC,YAAY,EAAEhB,KAAK,CAACgB;IACtB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,eAAe,GAAG3C,UAAU,CAAC;IACjC,CAAC,GAAGW,SAAS,MAAM,GAAGU,SAAS,KAAK;EACtC,CAAC,EAAEG,MAAM,EAAEC,SAAS,EAAEX,aAAa,CAAC;EACpC,MAAM8B,iBAAiB,GAAGA,CAACC,SAAS,EAAEC,WAAW,MAAM,aAAajD,KAAK,CAACkD,aAAa,CAACzC,SAAS,EAAE;IACjGO,IAAI,EAAEA,IAAI;IACVgC,SAAS,EAAEA,SAAS;IACpBG,OAAO,EAAEF,WAAW;IACpB/B,gBAAgB,EAAEA,gBAAgB;IAClCC,aAAa,EAAEA;EACjB,CAAC,CAAC,CAAC;EACH;EACA,MAAM,CAACiC,MAAM,EAAEC,aAAa,CAAC,GAAGjD,SAAS,CAAC,MAAM,EAAEkB,SAAS,CAAC8B,MAAM,CAAC;EACnE,OAAO1B,UAAU,CAAC,aAAa1B,KAAK,CAACkD,aAAa,CAAC5C,aAAa,CAACgD,QAAQ,EAAE;IACzEC,KAAK,EAAEF;EACT,CAAC,EAAE,aAAarD,KAAK,CAACkD,aAAa,CAAChD,MAAM,EAAEX,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAEb,SAAS,EAAE;IACvED,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGI,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACJ,SAAS;IAC9H+B,MAAM,EAAEA,MAAM;IACdnC,aAAa,EAAE6B,eAAe;IAC9BhC,SAAS,EAAEA,SAAS;IACpB0C,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAEV,iBAAiB;IAC9BV,iBAAiB,EAAEA,iBAAiB;IACpCjB,KAAK,EAAEU;EACT,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,IAAI4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChD,IAAI,CAACiD,WAAW,GAAG,MAAM;AAC3B;AACAjD,IAAI,CAACkD,sCAAsC,GAAGpD,SAAS;AACvD,eAAeE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}