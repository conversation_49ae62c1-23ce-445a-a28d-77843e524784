{"ast": null, "code": "\"use client\";\n\nimport InternalCheckbox from './Checkbox';\nimport Group from './Group';\nconst Checkbox = InternalCheckbox;\nCheckbox.Group = Group;\nCheckbox.__ANT_CHECKBOX = true;\nif (process.env.NODE_ENV !== 'production') {\n  Checkbox.displayName = 'Checkbox';\n}\nexport default Checkbox;", "map": {"version": 3, "names": ["InternalCheckbox", "Group", "Checkbox", "__ANT_CHECKBOX", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/checkbox/index.js"], "sourcesContent": ["\"use client\";\n\nimport InternalCheckbox from './Checkbox';\nimport Group from './Group';\nconst Checkbox = InternalCheckbox;\nCheckbox.Group = Group;\nCheckbox.__ANT_CHECKBOX = true;\nif (process.env.NODE_ENV !== 'production') {\n  Checkbox.displayName = 'Checkbox';\n}\nexport default Checkbox;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,gBAAgB,MAAM,YAAY;AACzC,OAAOC,KAAK,MAAM,SAAS;AAC3B,MAAMC,QAAQ,GAAGF,gBAAgB;AACjCE,QAAQ,CAACD,KAAK,GAAGA,KAAK;AACtBC,QAAQ,CAACC,cAAc,GAAG,IAAI;AAC9B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,QAAQ,CAACK,WAAW,GAAG,UAAU;AACnC;AACA,eAAeL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}