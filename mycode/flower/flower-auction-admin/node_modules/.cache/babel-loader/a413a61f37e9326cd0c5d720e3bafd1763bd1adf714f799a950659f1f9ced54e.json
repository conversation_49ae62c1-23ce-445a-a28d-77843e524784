{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalInfo/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Form, Input, Button, Avatar, Upload, message, Row, Col, Typography, Space, Tag, Descriptions } from 'antd';\nimport { UserOutlined, EditOutlined, SaveOutlined, CameraOutlined, MailOutlined, PhoneOutlined, EnvironmentOutlined, CalendarOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst PersonalInfo = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [form] = Form.useForm();\n  const [editing, setEditing] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [uploading, setUploading] = useState(false);\n  const [userProfile, setUserProfile] = useState({\n    id: 1,\n    username: (user === null || user === void 0 ? void 0 : user.username) || 'admin',\n    realName: (user === null || user === void 0 ? void 0 : user.realName) || '系统管理员',\n    email: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>',\n    phone: '138****8888',\n    avatar: user === null || user === void 0 ? void 0 : user.avatar,\n    department: '技术部',\n    position: '系统管理员',\n    address: '云南省昆明市',\n    bio: '负责昆明花卉拍卖系统的运维和管理工作',\n    joinDate: '2023-01-01',\n    lastLoginTime: '2024-01-15 10:30:00',\n    status: 'active'\n  });\n  useEffect(() => {\n    form.setFieldsValue(userProfile);\n  }, [userProfile, form]);\n  const handleEdit = () => {\n    setEditing(true);\n  };\n  const handleCancel = () => {\n    setEditing(false);\n    form.setFieldsValue(userProfile);\n  };\n  const handleSave = async values => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API更新用户信息\n      // await userService.updateProfile(values);\n\n      const updatedProfile = {\n        ...userProfile,\n        ...values\n      };\n      setUserProfile(updatedProfile);\n      setEditing(false);\n      message.success('个人信息更新成功');\n    } catch (error) {\n      console.error('更新个人信息失败:', error);\n      message.error('更新个人信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const uploadProps = {\n    name: 'avatar',\n    action: '/api/upload/avatar',\n    headers: {\n      authorization: `Bearer ${localStorage.getItem('token')}`\n    },\n    beforeUpload: file => {\n      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';\n      if (!isJpgOrPng) {\n        message.error('只能上传 JPG/PNG 格式的图片!');\n        return false;\n      }\n      const isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isLt2M) {\n        message.error('图片大小不能超过 2MB!');\n        return false;\n      }\n      return true;\n    },\n    onChange: info => {\n      if (info.file.status === 'uploading') {\n        setUploading(true);\n        return;\n      }\n      if (info.file.status === 'done') {\n        var _info$file$response;\n        setUploading(false);\n        if ((_info$file$response = info.file.response) !== null && _info$file$response !== void 0 && _info$file$response.success) {\n          const newAvatar = info.file.response.data.url;\n          setUserProfile(prev => ({\n            ...prev,\n            avatar: newAvatar\n          }));\n          message.success('头像上传成功');\n        } else {\n          message.error('头像上传失败');\n        }\n      }\n      if (info.file.status === 'error') {\n        setUploading(false);\n        message.error('头像上传失败');\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), \" \\u4E2A\\u4EBA\\u4FE1\\u606F\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5934\\u50CF\\u4FE1\\u606F\",\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              size: 120,\n              src: userProfile.avatar,\n              icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Upload, {\n              ...uploadProps,\n              showUploadList: false,\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(CameraOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 25\n                }, this),\n                loading: uploading,\n                type: \"primary\",\n                ghost: true,\n                children: uploading ? '上传中...' : '更换头像'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0\n                },\n                children: userProfile.realName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: [\"@\", userProfile.username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: userProfile.status === 'active' ? 'green' : 'red',\n              children: userProfile.status === 'active' ? '在线' : '离线'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u57FA\\u672C\\u7EDF\\u8BA1\",\n          style: {\n            marginTop: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Descriptions, {\n            column: 1,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u52A0\\u5165\\u65F6\\u95F4\",\n              children: [/*#__PURE__*/_jsxDEV(CalendarOutlined, {\n                style: {\n                  marginRight: 4\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), userProfile.joinDate]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6700\\u540E\\u767B\\u5F55\",\n              children: [/*#__PURE__*/_jsxDEV(CalendarOutlined, {\n                style: {\n                  marginRight: 4\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), userProfile.lastLoginTime]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          extra: !editing ? /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 25\n            }, this),\n            onClick: handleEdit,\n            children: \"\\u7F16\\u8F91\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleCancel,\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 27\n              }, this),\n              onClick: () => form.submit(),\n              loading: loading,\n              children: \"\\u4FDD\\u5B58\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            onFinish: handleSave,\n            disabled: !editing,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"realName\",\n                  label: \"\\u771F\\u5B9E\\u59D3\\u540D\",\n                  rules: [{\n                    required: true,\n                    message: '请输入真实姓名'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 36\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"username\",\n                  label: \"\\u7528\\u6237\\u540D\",\n                  rules: [{\n                    required: true,\n                    message: '请输入用户名'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 36\n                    }, this),\n                    disabled: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"email\",\n                  label: \"\\u90AE\\u7BB1\\u5730\\u5740\",\n                  rules: [{\n                    required: true,\n                    message: '请输入邮箱地址'\n                  }, {\n                    type: 'email',\n                    message: '请输入有效的邮箱地址'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 36\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"phone\",\n                  label: \"\\u624B\\u673A\\u53F7\\u7801\",\n                  rules: [{\n                    required: true,\n                    message: '请输入手机号码'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(PhoneOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 36\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"department\",\n                  label: \"\\u6240\\u5C5E\\u90E8\\u95E8\",\n                  rules: [{\n                    required: true,\n                    message: '请输入所属部门'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"position\",\n                  label: \"\\u804C\\u4F4D\",\n                  rules: [{\n                    required: true,\n                    message: '请输入职位'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"address\",\n              label: \"\\u5730\\u5740\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 32\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"bio\",\n              label: \"\\u4E2A\\u4EBA\\u7B80\\u4ECB\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4E2A\\u4EBA\\u7B80\\u4ECB...\",\n                maxLength: 200,\n                showCount: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(PersonalInfo, \"xOrw/EEBPSR9+uVF1/PqtWPmkMY=\", false, function () {\n  return [useAuth, Form.useForm];\n});\n_c = PersonalInfo;\nexport default PersonalInfo;\nvar _c;\n$RefreshReg$(_c, \"PersonalInfo\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Form", "Input", "<PERSON><PERSON>", "Avatar", "Upload", "message", "Row", "Col", "Typography", "Space", "Tag", "Descriptions", "UserOutlined", "EditOutlined", "SaveOutlined", "CameraOutlined", "MailOutlined", "PhoneOutlined", "EnvironmentOutlined", "CalendarOutlined", "useAuth", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "PersonalInfo", "_s", "user", "form", "useForm", "editing", "setEditing", "loading", "setLoading", "uploading", "setUploading", "userProfile", "setUserProfile", "id", "username", "realName", "email", "phone", "avatar", "department", "position", "address", "bio", "joinDate", "lastLoginTime", "status", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleEdit", "handleCancel", "handleSave", "values", "updatedProfile", "success", "error", "console", "uploadProps", "name", "action", "headers", "authorization", "localStorage", "getItem", "beforeUpload", "file", "isJpgOrPng", "type", "isLt2M", "size", "onChange", "info", "_info$file$response", "response", "newAvat<PERSON>", "data", "url", "prev", "style", "padding", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "span", "title", "textAlign", "direction", "src", "icon", "showUploadList", "ghost", "margin", "color", "marginTop", "column", "<PERSON><PERSON>", "label", "marginRight", "extra", "onClick", "submit", "layout", "onFinish", "disabled", "rules", "required", "prefix", "rows", "placeholder", "max<PERSON><PERSON><PERSON>", "showCount", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalInfo/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  Avatar,\n  Upload,\n  message,\n  Row,\n  Col,\n  Typography,\n  Divider,\n  Space,\n  Tag,\n  Descriptions,\n} from 'antd';\nimport {\n  UserOutlined,\n  EditOutlined,\n  SaveOutlined,\n  CameraOutlined,\n  MailOutlined,\n  PhoneOutlined,\n  EnvironmentOutlined,\n  CalendarOutlined,\n} from '@ant-design/icons';\nimport { useAuth } from '../../../hooks/useAuth';\nimport type { UploadProps } from 'antd';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface UserProfile {\n  id: number;\n  username: string;\n  realName: string;\n  email: string;\n  phone: string;\n  avatar?: string;\n  department: string;\n  position: string;\n  address: string;\n  bio: string;\n  joinDate: string;\n  lastLoginTime: string;\n  status: 'active' | 'inactive';\n}\n\nconst PersonalInfo: React.FC = () => {\n  const { user } = useAuth();\n  const [form] = Form.useForm();\n  const [editing, setEditing] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [uploading, setUploading] = useState(false);\n  const [userProfile, setUserProfile] = useState<UserProfile>({\n    id: 1,\n    username: user?.username || 'admin',\n    realName: user?.realName || '系统管理员',\n    email: user?.email || '<EMAIL>',\n    phone: '138****8888',\n    avatar: user?.avatar,\n    department: '技术部',\n    position: '系统管理员',\n    address: '云南省昆明市',\n    bio: '负责昆明花卉拍卖系统的运维和管理工作',\n    joinDate: '2023-01-01',\n    lastLoginTime: '2024-01-15 10:30:00',\n    status: 'active',\n  });\n\n  useEffect(() => {\n    form.setFieldsValue(userProfile);\n  }, [userProfile, form]);\n\n  const handleEdit = () => {\n    setEditing(true);\n  };\n\n  const handleCancel = () => {\n    setEditing(false);\n    form.setFieldsValue(userProfile);\n  };\n\n  const handleSave = async (values: any) => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API更新用户信息\n      // await userService.updateProfile(values);\n      \n      const updatedProfile = { ...userProfile, ...values };\n      setUserProfile(updatedProfile);\n      setEditing(false);\n      message.success('个人信息更新成功');\n    } catch (error) {\n      console.error('更新个人信息失败:', error);\n      message.error('更新个人信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const uploadProps: UploadProps = {\n    name: 'avatar',\n    action: '/api/upload/avatar',\n    headers: {\n      authorization: `Bearer ${localStorage.getItem('token')}`,\n    },\n    beforeUpload: (file) => {\n      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';\n      if (!isJpgOrPng) {\n        message.error('只能上传 JPG/PNG 格式的图片!');\n        return false;\n      }\n      const isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isLt2M) {\n        message.error('图片大小不能超过 2MB!');\n        return false;\n      }\n      return true;\n    },\n    onChange: (info) => {\n      if (info.file.status === 'uploading') {\n        setUploading(true);\n        return;\n      }\n      if (info.file.status === 'done') {\n        setUploading(false);\n        if (info.file.response?.success) {\n          const newAvatar = info.file.response.data.url;\n          setUserProfile(prev => ({ ...prev, avatar: newAvatar }));\n          message.success('头像上传成功');\n        } else {\n          message.error('头像上传失败');\n        }\n      }\n      if (info.file.status === 'error') {\n        setUploading(false);\n        message.error('头像上传失败');\n      }\n    },\n  };\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>\n        <UserOutlined /> 个人信息\n      </Title>\n\n      <Row gutter={24}>\n        <Col span={8}>\n          <Card title=\"头像信息\" style={{ textAlign: 'center' }}>\n            <Space direction=\"vertical\" size=\"large\">\n              <Avatar\n                size={120}\n                src={userProfile.avatar}\n                icon={<UserOutlined />}\n              />\n              \n              <Upload {...uploadProps} showUploadList={false}>\n                <Button\n                  icon={<CameraOutlined />}\n                  loading={uploading}\n                  type=\"primary\"\n                  ghost\n                >\n                  {uploading ? '上传中...' : '更换头像'}\n                </Button>\n              </Upload>\n              \n              <div>\n                <Title level={4} style={{ margin: 0 }}>\n                  {userProfile.realName}\n                </Title>\n                <Text type=\"secondary\">@{userProfile.username}</Text>\n              </div>\n              \n              <Tag color={userProfile.status === 'active' ? 'green' : 'red'}>\n                {userProfile.status === 'active' ? '在线' : '离线'}\n              </Tag>\n            </Space>\n          </Card>\n\n          <Card title=\"基本统计\" style={{ marginTop: 16 }}>\n            <Descriptions column={1} size=\"small\">\n              <Descriptions.Item label=\"加入时间\">\n                <CalendarOutlined style={{ marginRight: 4 }} />\n                {userProfile.joinDate}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"最后登录\">\n                <CalendarOutlined style={{ marginRight: 4 }} />\n                {userProfile.lastLoginTime}\n              </Descriptions.Item>\n            </Descriptions>\n          </Card>\n        </Col>\n\n        <Col span={16}>\n          <Card\n            title=\"详细信息\"\n            extra={\n              !editing ? (\n                <Button\n                  type=\"primary\"\n                  icon={<EditOutlined />}\n                  onClick={handleEdit}\n                >\n                  编辑信息\n                </Button>\n              ) : (\n                <Space>\n                  <Button onClick={handleCancel}>取消</Button>\n                  <Button\n                    type=\"primary\"\n                    icon={<SaveOutlined />}\n                    onClick={() => form.submit()}\n                    loading={loading}\n                  >\n                    保存\n                  </Button>\n                </Space>\n              )\n            }\n          >\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={handleSave}\n              disabled={!editing}\n            >\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"realName\"\n                    label=\"真实姓名\"\n                    rules={[{ required: true, message: '请输入真实姓名' }]}\n                  >\n                    <Input prefix={<UserOutlined />} />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"username\"\n                    label=\"用户名\"\n                    rules={[{ required: true, message: '请输入用户名' }]}\n                  >\n                    <Input prefix={<UserOutlined />} disabled />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"email\"\n                    label=\"邮箱地址\"\n                    rules={[\n                      { required: true, message: '请输入邮箱地址' },\n                      { type: 'email', message: '请输入有效的邮箱地址' },\n                    ]}\n                  >\n                    <Input prefix={<MailOutlined />} />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"phone\"\n                    label=\"手机号码\"\n                    rules={[{ required: true, message: '请输入手机号码' }]}\n                  >\n                    <Input prefix={<PhoneOutlined />} />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"department\"\n                    label=\"所属部门\"\n                    rules={[{ required: true, message: '请输入所属部门' }]}\n                  >\n                    <Input />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"position\"\n                    label=\"职位\"\n                    rules={[{ required: true, message: '请输入职位' }]}\n                  >\n                    <Input />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item\n                name=\"address\"\n                label=\"地址\"\n              >\n                <Input prefix={<EnvironmentOutlined />} />\n              </Form.Item>\n\n              <Form.Item\n                name=\"bio\"\n                label=\"个人简介\"\n              >\n                <TextArea\n                  rows={4}\n                  placeholder=\"请输入个人简介...\"\n                  maxLength={200}\n                  showCount\n                />\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default PersonalInfo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,UAAU,EAEVC,KAAK,EACLC,GAAG,EACHC,YAAY,QACP,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,aAAa,EACbC,mBAAmB,EACnBC,gBAAgB,QACX,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGjD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGhB,UAAU;AAClC,MAAM;EAAEiB;AAAS,CAAC,GAAGxB,KAAK;AAkB1B,MAAMyB,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAc;IAC1D0C,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,CAAAZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,QAAQ,KAAI,OAAO;IACnCC,QAAQ,EAAE,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,QAAQ,KAAI,OAAO;IACnCC,KAAK,EAAE,CAAAd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,KAAK,KAAI,mBAAmB;IACzCC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAEhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,MAAM;IACpBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,OAAO;IACjBC,OAAO,EAAE,QAAQ;IACjBC,GAAG,EAAE,oBAAoB;IACzBC,QAAQ,EAAE,YAAY;IACtBC,aAAa,EAAE,qBAAqB;IACpCC,MAAM,EAAE;EACV,CAAC,CAAC;EAEFrD,SAAS,CAAC,MAAM;IACd+B,IAAI,CAACuB,cAAc,CAACf,WAAW,CAAC;EAClC,CAAC,EAAE,CAACA,WAAW,EAAER,IAAI,CAAC,CAAC;EAEvB,MAAMwB,UAAU,GAAGA,CAAA,KAAM;IACvBrB,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzBtB,UAAU,CAAC,KAAK,CAAC;IACjBH,IAAI,CAACuB,cAAc,CAACf,WAAW,CAAC;EAClC,CAAC;EAED,MAAMkB,UAAU,GAAG,MAAOC,MAAW,IAAK;IACxCtB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;;MAEA,MAAMuB,cAAc,GAAG;QAAE,GAAGpB,WAAW;QAAE,GAAGmB;MAAO,CAAC;MACpDlB,cAAc,CAACmB,cAAc,CAAC;MAC9BzB,UAAU,CAAC,KAAK,CAAC;MACjB3B,OAAO,CAACqD,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCtD,OAAO,CAACsD,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,WAAwB,GAAG;IAC/BC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,oBAAoB;IAC5BC,OAAO,EAAE;MACPC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IACxD,CAAC;IACDC,YAAY,EAAGC,IAAI,IAAK;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,KAAK,YAAY,IAAIF,IAAI,CAACE,IAAI,KAAK,WAAW;MAC1E,IAAI,CAACD,UAAU,EAAE;QACfjE,OAAO,CAACsD,KAAK,CAAC,qBAAqB,CAAC;QACpC,OAAO,KAAK;MACd;MACA,MAAMa,MAAM,GAAGH,IAAI,CAACI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;MAC1C,IAAI,CAACD,MAAM,EAAE;QACXnE,OAAO,CAACsD,KAAK,CAAC,eAAe,CAAC;QAC9B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IACDe,QAAQ,EAAGC,IAAI,IAAK;MAClB,IAAIA,IAAI,CAACN,IAAI,CAAClB,MAAM,KAAK,WAAW,EAAE;QACpCf,YAAY,CAAC,IAAI,CAAC;QAClB;MACF;MACA,IAAIuC,IAAI,CAACN,IAAI,CAAClB,MAAM,KAAK,MAAM,EAAE;QAAA,IAAAyB,mBAAA;QAC/BxC,YAAY,CAAC,KAAK,CAAC;QACnB,KAAAwC,mBAAA,GAAID,IAAI,CAACN,IAAI,CAACQ,QAAQ,cAAAD,mBAAA,eAAlBA,mBAAA,CAAoBlB,OAAO,EAAE;UAC/B,MAAMoB,SAAS,GAAGH,IAAI,CAACN,IAAI,CAACQ,QAAQ,CAACE,IAAI,CAACC,GAAG;UAC7C1C,cAAc,CAAC2C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAErC,MAAM,EAAEkC;UAAU,CAAC,CAAC,CAAC;UACxDzE,OAAO,CAACqD,OAAO,CAAC,QAAQ,CAAC;QAC3B,CAAC,MAAM;UACLrD,OAAO,CAACsD,KAAK,CAAC,QAAQ,CAAC;QACzB;MACF;MACA,IAAIgB,IAAI,CAACN,IAAI,CAAClB,MAAM,KAAK,OAAO,EAAE;QAChCf,YAAY,CAAC,KAAK,CAAC;QACnB/B,OAAO,CAACsD,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF;EACF,CAAC;EAED,oBACErC,OAAA;IAAK4D,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAC,QAAA,gBAC1B9D,OAAA,CAACC,KAAK;MAAC8D,KAAK,EAAE,CAAE;MAAAD,QAAA,gBACd9D,OAAA,CAACV,YAAY;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BAClB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERnE,OAAA,CAAChB,GAAG;MAACoF,MAAM,EAAE,EAAG;MAAAN,QAAA,gBACd9D,OAAA,CAACf,GAAG;QAACoF,IAAI,EAAE,CAAE;QAAAP,QAAA,gBACX9D,OAAA,CAACvB,IAAI;UAAC6F,KAAK,EAAC,0BAAM;UAACV,KAAK,EAAE;YAAEW,SAAS,EAAE;UAAS,CAAE;UAAAT,QAAA,eAChD9D,OAAA,CAACb,KAAK;YAACqF,SAAS,EAAC,UAAU;YAACrB,IAAI,EAAC,OAAO;YAAAW,QAAA,gBACtC9D,OAAA,CAACnB,MAAM;cACLsE,IAAI,EAAE,GAAI;cACVsB,GAAG,EAAE1D,WAAW,CAACO,MAAO;cACxBoD,IAAI,eAAE1E,OAAA,CAACV,YAAY;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eAEFnE,OAAA,CAAClB,MAAM;cAAA,GAAKyD,WAAW;cAAEoC,cAAc,EAAE,KAAM;cAAAb,QAAA,eAC7C9D,OAAA,CAACpB,MAAM;gBACL8F,IAAI,eAAE1E,OAAA,CAACP,cAAc;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBxD,OAAO,EAAEE,SAAU;gBACnBoC,IAAI,EAAC,SAAS;gBACd2B,KAAK;gBAAAd,QAAA,EAEJjD,SAAS,GAAG,QAAQ,GAAG;cAAM;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAETnE,OAAA;cAAA8D,QAAA,gBACE9D,OAAA,CAACC,KAAK;gBAAC8D,KAAK,EAAE,CAAE;gBAACH,KAAK,EAAE;kBAAEiB,MAAM,EAAE;gBAAE,CAAE;gBAAAf,QAAA,EACnC/C,WAAW,CAACI;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACRnE,OAAA,CAACE,IAAI;gBAAC+C,IAAI,EAAC,WAAW;gBAAAa,QAAA,GAAC,GAAC,EAAC/C,WAAW,CAACG,QAAQ;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eAENnE,OAAA,CAACZ,GAAG;cAAC0F,KAAK,EAAE/D,WAAW,CAACc,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAM;cAAAiC,QAAA,EAC3D/C,WAAW,CAACc,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;YAAI;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPnE,OAAA,CAACvB,IAAI;UAAC6F,KAAK,EAAC,0BAAM;UAACV,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAG,CAAE;UAAAjB,QAAA,eAC1C9D,OAAA,CAACX,YAAY;YAAC2F,MAAM,EAAE,CAAE;YAAC7B,IAAI,EAAC,OAAO;YAAAW,QAAA,gBACnC9D,OAAA,CAACX,YAAY,CAAC4F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAApB,QAAA,gBAC7B9D,OAAA,CAACH,gBAAgB;gBAAC+D,KAAK,EAAE;kBAAEuB,WAAW,EAAE;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC9CpD,WAAW,CAACY,QAAQ;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACpBnE,OAAA,CAACX,YAAY,CAAC4F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAApB,QAAA,gBAC7B9D,OAAA,CAACH,gBAAgB;gBAAC+D,KAAK,EAAE;kBAAEuB,WAAW,EAAE;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC9CpD,WAAW,CAACa,aAAa;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENnE,OAAA,CAACf,GAAG;QAACoF,IAAI,EAAE,EAAG;QAAAP,QAAA,eACZ9D,OAAA,CAACvB,IAAI;UACH6F,KAAK,EAAC,0BAAM;UACZc,KAAK,EACH,CAAC3E,OAAO,gBACNT,OAAA,CAACpB,MAAM;YACLqE,IAAI,EAAC,SAAS;YACdyB,IAAI,eAAE1E,OAAA,CAACT,YAAY;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBkB,OAAO,EAAEtD,UAAW;YAAA+B,QAAA,EACrB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAETnE,OAAA,CAACb,KAAK;YAAA2E,QAAA,gBACJ9D,OAAA,CAACpB,MAAM;cAACyG,OAAO,EAAErD,YAAa;cAAA8B,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1CnE,OAAA,CAACpB,MAAM;cACLqE,IAAI,EAAC,SAAS;cACdyB,IAAI,eAAE1E,OAAA,CAACR,YAAY;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBkB,OAAO,EAAEA,CAAA,KAAM9E,IAAI,CAAC+E,MAAM,CAAC,CAAE;cAC7B3E,OAAO,EAAEA,OAAQ;cAAAmD,QAAA,EAClB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAEV;UAAAL,QAAA,eAED9D,OAAA,CAACtB,IAAI;YACH6B,IAAI,EAAEA,IAAK;YACXgF,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAEvD,UAAW;YACrBwD,QAAQ,EAAE,CAAChF,OAAQ;YAAAqD,QAAA,gBAEnB9D,OAAA,CAAChB,GAAG;cAACoF,MAAM,EAAE,EAAG;cAAAN,QAAA,gBACd9D,OAAA,CAACf,GAAG;gBAACoF,IAAI,EAAE,EAAG;gBAAAP,QAAA,eACZ9D,OAAA,CAACtB,IAAI,CAACuG,IAAI;kBACRzC,IAAI,EAAC,UAAU;kBACf0C,KAAK,EAAC,0BAAM;kBACZQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5G,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA+E,QAAA,eAEhD9D,OAAA,CAACrB,KAAK;oBAACiH,MAAM,eAAE5F,OAAA,CAACV,YAAY;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnE,OAAA,CAACf,GAAG;gBAACoF,IAAI,EAAE,EAAG;gBAAAP,QAAA,eACZ9D,OAAA,CAACtB,IAAI,CAACuG,IAAI;kBACRzC,IAAI,EAAC,UAAU;kBACf0C,KAAK,EAAC,oBAAK;kBACXQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5G,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAA+E,QAAA,eAE/C9D,OAAA,CAACrB,KAAK;oBAACiH,MAAM,eAAE5F,OAAA,CAACV,YAAY;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAACsB,QAAQ;kBAAA;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnE,OAAA,CAAChB,GAAG;cAACoF,MAAM,EAAE,EAAG;cAAAN,QAAA,gBACd9D,OAAA,CAACf,GAAG;gBAACoF,IAAI,EAAE,EAAG;gBAAAP,QAAA,eACZ9D,OAAA,CAACtB,IAAI,CAACuG,IAAI;kBACRzC,IAAI,EAAC,OAAO;kBACZ0C,KAAK,EAAC,0BAAM;kBACZQ,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5G,OAAO,EAAE;kBAAU,CAAC,EACtC;oBAAEkE,IAAI,EAAE,OAAO;oBAAElE,OAAO,EAAE;kBAAa,CAAC,CACxC;kBAAA+E,QAAA,eAEF9D,OAAA,CAACrB,KAAK;oBAACiH,MAAM,eAAE5F,OAAA,CAACN,YAAY;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnE,OAAA,CAACf,GAAG;gBAACoF,IAAI,EAAE,EAAG;gBAAAP,QAAA,eACZ9D,OAAA,CAACtB,IAAI,CAACuG,IAAI;kBACRzC,IAAI,EAAC,OAAO;kBACZ0C,KAAK,EAAC,0BAAM;kBACZQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5G,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA+E,QAAA,eAEhD9D,OAAA,CAACrB,KAAK;oBAACiH,MAAM,eAAE5F,OAAA,CAACL,aAAa;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnE,OAAA,CAAChB,GAAG;cAACoF,MAAM,EAAE,EAAG;cAAAN,QAAA,gBACd9D,OAAA,CAACf,GAAG;gBAACoF,IAAI,EAAE,EAAG;gBAAAP,QAAA,eACZ9D,OAAA,CAACtB,IAAI,CAACuG,IAAI;kBACRzC,IAAI,EAAC,YAAY;kBACjB0C,KAAK,EAAC,0BAAM;kBACZQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5G,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAA+E,QAAA,eAEhD9D,OAAA,CAACrB,KAAK;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnE,OAAA,CAACf,GAAG;gBAACoF,IAAI,EAAE,EAAG;gBAAAP,QAAA,eACZ9D,OAAA,CAACtB,IAAI,CAACuG,IAAI;kBACRzC,IAAI,EAAC,UAAU;kBACf0C,KAAK,EAAC,cAAI;kBACVQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE5G,OAAO,EAAE;kBAAQ,CAAC,CAAE;kBAAA+E,QAAA,eAE9C9D,OAAA,CAACrB,KAAK;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnE,OAAA,CAACtB,IAAI,CAACuG,IAAI;cACRzC,IAAI,EAAC,SAAS;cACd0C,KAAK,EAAC,cAAI;cAAApB,QAAA,eAEV9D,OAAA,CAACrB,KAAK;gBAACiH,MAAM,eAAE5F,OAAA,CAACJ,mBAAmB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEZnE,OAAA,CAACtB,IAAI,CAACuG,IAAI;cACRzC,IAAI,EAAC,KAAK;cACV0C,KAAK,EAAC,0BAAM;cAAApB,QAAA,eAEZ9D,OAAA,CAACG,QAAQ;gBACP0F,IAAI,EAAE,CAAE;gBACRC,WAAW,EAAC,+CAAY;gBACxBC,SAAS,EAAE,GAAI;gBACfC,SAAS;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CA/QID,YAAsB;EAAA,QACTN,OAAO,EACTpB,IAAI,CAAC8B,OAAO;AAAA;AAAAyF,EAAA,GAFvB7F,YAAsB;AAiR5B,eAAeA,YAAY;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}