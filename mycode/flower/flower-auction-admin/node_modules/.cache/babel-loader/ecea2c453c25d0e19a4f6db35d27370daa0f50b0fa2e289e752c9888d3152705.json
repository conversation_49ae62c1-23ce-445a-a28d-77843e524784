{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport throttleByAnimationFrame from '../_util/throttleByAnimationFrame';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport { getFixedBottom, getFixedTop, getTargetRect } from './utils';\nconst TRIGGER_EVENTS = ['resize', 'scroll', 'touchstart', 'touchmove', 'touchend', 'pageshow', 'load'];\nfunction getDefaultTarget() {\n  return typeof window !== 'undefined' ? window : null;\n}\nconst AFFIX_STATUS_NONE = 0;\nconst AFFIX_STATUS_PREPARE = 1;\nconst Affix = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n      style,\n      offsetTop,\n      offsetBottom,\n      prefixCls,\n      className,\n      rootClassName,\n      children,\n      target,\n      onChange,\n      onTestUpdatePosition\n    } = props,\n    restProps = __rest(props, [\"style\", \"offsetTop\", \"offsetBottom\", \"prefixCls\", \"className\", \"rootClassName\", \"children\", \"target\", \"onChange\", \"onTestUpdatePosition\"]);\n  const {\n    getPrefixCls,\n    getTargetContainer\n  } = React.useContext(ConfigContext);\n  const affixPrefixCls = getPrefixCls('affix', prefixCls);\n  const [lastAffix, setLastAffix] = React.useState(false);\n  const [affixStyle, setAffixStyle] = React.useState();\n  const [placeholderStyle, setPlaceholderStyle] = React.useState();\n  const status = React.useRef(AFFIX_STATUS_NONE);\n  const prevTarget = React.useRef(null);\n  const prevListener = React.useRef(null);\n  const placeholderNodeRef = React.useRef(null);\n  const fixedNodeRef = React.useRef(null);\n  const timer = React.useRef(null);\n  const targetFunc = (_a = target !== null && target !== void 0 ? target : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultTarget;\n  const internalOffsetTop = offsetBottom === undefined && offsetTop === undefined ? 0 : offsetTop;\n  // =================== Measure ===================\n  const measure = () => {\n    if (status.current !== AFFIX_STATUS_PREPARE || !fixedNodeRef.current || !placeholderNodeRef.current || !targetFunc) {\n      return;\n    }\n    const targetNode = targetFunc();\n    if (targetNode) {\n      const newState = {\n        status: AFFIX_STATUS_NONE\n      };\n      const placeholderRect = getTargetRect(placeholderNodeRef.current);\n      if (placeholderRect.top === 0 && placeholderRect.left === 0 && placeholderRect.width === 0 && placeholderRect.height === 0) {\n        return;\n      }\n      const targetRect = getTargetRect(targetNode);\n      const fixedTop = getFixedTop(placeholderRect, targetRect, internalOffsetTop);\n      const fixedBottom = getFixedBottom(placeholderRect, targetRect, offsetBottom);\n      if (fixedTop !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          top: fixedTop,\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n      } else if (fixedBottom !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          bottom: fixedBottom,\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n      }\n      newState.lastAffix = !!newState.affixStyle;\n      if (lastAffix !== newState.lastAffix) {\n        onChange === null || onChange === void 0 ? void 0 : onChange(newState.lastAffix);\n      }\n      status.current = newState.status;\n      setAffixStyle(newState.affixStyle);\n      setPlaceholderStyle(newState.placeholderStyle);\n      setLastAffix(newState.lastAffix);\n    }\n  };\n  const prepareMeasure = () => {\n    status.current = AFFIX_STATUS_PREPARE;\n    measure();\n    if (process.env.NODE_ENV === 'test') {\n      onTestUpdatePosition === null || onTestUpdatePosition === void 0 ? void 0 : onTestUpdatePosition();\n    }\n  };\n  const updatePosition = throttleByAnimationFrame(() => {\n    prepareMeasure();\n  });\n  const lazyUpdatePosition = throttleByAnimationFrame(() => {\n    // Check position change before measure to make Safari smooth\n    if (targetFunc && affixStyle) {\n      const targetNode = targetFunc();\n      if (targetNode && placeholderNodeRef.current) {\n        const targetRect = getTargetRect(targetNode);\n        const placeholderRect = getTargetRect(placeholderNodeRef.current);\n        const fixedTop = getFixedTop(placeholderRect, targetRect, internalOffsetTop);\n        const fixedBottom = getFixedBottom(placeholderRect, targetRect, offsetBottom);\n        if (fixedTop !== undefined && affixStyle.top === fixedTop || fixedBottom !== undefined && affixStyle.bottom === fixedBottom) {\n          return;\n        }\n      }\n    }\n    // Directly call prepare measure since it's already throttled.\n    prepareMeasure();\n  });\n  const addListeners = () => {\n    const listenerTarget = targetFunc === null || targetFunc === void 0 ? void 0 : targetFunc();\n    if (!listenerTarget) {\n      return;\n    }\n    TRIGGER_EVENTS.forEach(eventName => {\n      var _a;\n      if (prevListener.current) {\n        (_a = prevTarget.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(eventName, prevListener.current);\n      }\n      listenerTarget === null || listenerTarget === void 0 ? void 0 : listenerTarget.addEventListener(eventName, lazyUpdatePosition);\n    });\n    prevTarget.current = listenerTarget;\n    prevListener.current = lazyUpdatePosition;\n  };\n  const removeListeners = () => {\n    if (timer.current) {\n      clearTimeout(timer.current);\n      timer.current = null;\n    }\n    const newTarget = targetFunc === null || targetFunc === void 0 ? void 0 : targetFunc();\n    TRIGGER_EVENTS.forEach(eventName => {\n      var _a;\n      newTarget === null || newTarget === void 0 ? void 0 : newTarget.removeEventListener(eventName, lazyUpdatePosition);\n      if (prevListener.current) {\n        (_a = prevTarget.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(eventName, prevListener.current);\n      }\n    });\n    updatePosition.cancel();\n    lazyUpdatePosition.cancel();\n  };\n  React.useImperativeHandle(ref, () => ({\n    updatePosition\n  }));\n  // mount & unmount\n  React.useEffect(() => {\n    // [Legacy] Wait for parent component ref has its value.\n    // We should use target as directly element instead of function which makes element check hard.\n    timer.current = setTimeout(addListeners);\n    return () => removeListeners();\n  }, []);\n  React.useEffect(() => {\n    addListeners();\n    return () => removeListeners();\n  }, [target, affixStyle, lastAffix, offsetTop, offsetBottom]);\n  React.useEffect(() => {\n    updatePosition();\n  }, [target, offsetTop, offsetBottom]);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(affixPrefixCls);\n  const rootCls = classNames(rootClassName, hashId, affixPrefixCls, cssVarCls);\n  const mergedCls = classNames({\n    [rootCls]: affixStyle\n  });\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: updatePosition\n  }, /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    style: style,\n    className: className,\n    ref: placeholderNodeRef\n  }, restProps), affixStyle && /*#__PURE__*/React.createElement(\"div\", {\n    style: placeholderStyle,\n    \"aria-hidden\": \"true\"\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: mergedCls,\n    ref: fixedNodeRef,\n    style: affixStyle\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: updatePosition\n  }, children)))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Affix.displayName = 'Affix';\n}\nexport default Affix;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ResizeObserver", "throttleByAnimationFrame", "ConfigContext", "useStyle", "getFixedBottom", "getFixedTop", "getTargetRect", "TRIGGER_EVENTS", "getDefaultTarget", "window", "AFFIX_STATUS_NONE", "AFFIX_STATUS_PREPARE", "Affix", "forwardRef", "props", "ref", "_a", "style", "offsetTop", "offsetBottom", "prefixCls", "className", "rootClassName", "children", "target", "onChange", "onTestUpdatePosition", "restProps", "getPrefixCls", "getTargetContainer", "useContext", "affixPrefixCls", "lastAffix", "setLastAffix", "useState", "affixStyle", "setAffixStyle", "placeholder<PERSON><PERSON><PERSON>", "setPlaceholderStyle", "status", "useRef", "prevTarget", "prevListener", "placeholder<PERSON><PERSON><PERSON><PERSON>", "fixedNodeRef", "timer", "targetFunc", "internalOffsetTop", "undefined", "measure", "current", "targetNode", "newState", "placeholder<PERSON><PERSON><PERSON>", "top", "left", "width", "height", "targetRect", "fixedTop", "fixedBottom", "position", "bottom", "prepareMeasure", "process", "env", "NODE_ENV", "updatePosition", "lazyUpdatePosition", "addListeners", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "eventName", "removeEventListener", "addEventListener", "removeListeners", "clearTimeout", "newTarget", "cancel", "useImperativeHandle", "useEffect", "setTimeout", "wrapCSSVar", "hashId", "cssVarCls", "rootCls", "mergedCls", "createElement", "onResize", "assign", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/affix/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport throttleByAnimationFrame from '../_util/throttleByAnimationFrame';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport { getFixedBottom, getFixedTop, getTargetRect } from './utils';\nconst TRIGGER_EVENTS = ['resize', 'scroll', 'touchstart', 'touchmove', 'touchend', 'pageshow', 'load'];\nfunction getDefaultTarget() {\n  return typeof window !== 'undefined' ? window : null;\n}\nconst AFFIX_STATUS_NONE = 0;\nconst AFFIX_STATUS_PREPARE = 1;\nconst Affix = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n      style,\n      offsetTop,\n      offsetBottom,\n      prefixCls,\n      className,\n      rootClassName,\n      children,\n      target,\n      onChange,\n      onTestUpdatePosition\n    } = props,\n    restProps = __rest(props, [\"style\", \"offsetTop\", \"offsetBottom\", \"prefixCls\", \"className\", \"rootClassName\", \"children\", \"target\", \"onChange\", \"onTestUpdatePosition\"]);\n  const {\n    getPrefixCls,\n    getTargetContainer\n  } = React.useContext(ConfigContext);\n  const affixPrefixCls = getPrefixCls('affix', prefixCls);\n  const [lastAffix, setLastAffix] = React.useState(false);\n  const [affixStyle, setAffixStyle] = React.useState();\n  const [placeholderStyle, setPlaceholderStyle] = React.useState();\n  const status = React.useRef(AFFIX_STATUS_NONE);\n  const prevTarget = React.useRef(null);\n  const prevListener = React.useRef(null);\n  const placeholderNodeRef = React.useRef(null);\n  const fixedNodeRef = React.useRef(null);\n  const timer = React.useRef(null);\n  const targetFunc = (_a = target !== null && target !== void 0 ? target : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultTarget;\n  const internalOffsetTop = offsetBottom === undefined && offsetTop === undefined ? 0 : offsetTop;\n  // =================== Measure ===================\n  const measure = () => {\n    if (status.current !== AFFIX_STATUS_PREPARE || !fixedNodeRef.current || !placeholderNodeRef.current || !targetFunc) {\n      return;\n    }\n    const targetNode = targetFunc();\n    if (targetNode) {\n      const newState = {\n        status: AFFIX_STATUS_NONE\n      };\n      const placeholderRect = getTargetRect(placeholderNodeRef.current);\n      if (placeholderRect.top === 0 && placeholderRect.left === 0 && placeholderRect.width === 0 && placeholderRect.height === 0) {\n        return;\n      }\n      const targetRect = getTargetRect(targetNode);\n      const fixedTop = getFixedTop(placeholderRect, targetRect, internalOffsetTop);\n      const fixedBottom = getFixedBottom(placeholderRect, targetRect, offsetBottom);\n      if (fixedTop !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          top: fixedTop,\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n      } else if (fixedBottom !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          bottom: fixedBottom,\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n      }\n      newState.lastAffix = !!newState.affixStyle;\n      if (lastAffix !== newState.lastAffix) {\n        onChange === null || onChange === void 0 ? void 0 : onChange(newState.lastAffix);\n      }\n      status.current = newState.status;\n      setAffixStyle(newState.affixStyle);\n      setPlaceholderStyle(newState.placeholderStyle);\n      setLastAffix(newState.lastAffix);\n    }\n  };\n  const prepareMeasure = () => {\n    status.current = AFFIX_STATUS_PREPARE;\n    measure();\n    if (process.env.NODE_ENV === 'test') {\n      onTestUpdatePosition === null || onTestUpdatePosition === void 0 ? void 0 : onTestUpdatePosition();\n    }\n  };\n  const updatePosition = throttleByAnimationFrame(() => {\n    prepareMeasure();\n  });\n  const lazyUpdatePosition = throttleByAnimationFrame(() => {\n    // Check position change before measure to make Safari smooth\n    if (targetFunc && affixStyle) {\n      const targetNode = targetFunc();\n      if (targetNode && placeholderNodeRef.current) {\n        const targetRect = getTargetRect(targetNode);\n        const placeholderRect = getTargetRect(placeholderNodeRef.current);\n        const fixedTop = getFixedTop(placeholderRect, targetRect, internalOffsetTop);\n        const fixedBottom = getFixedBottom(placeholderRect, targetRect, offsetBottom);\n        if (fixedTop !== undefined && affixStyle.top === fixedTop || fixedBottom !== undefined && affixStyle.bottom === fixedBottom) {\n          return;\n        }\n      }\n    }\n    // Directly call prepare measure since it's already throttled.\n    prepareMeasure();\n  });\n  const addListeners = () => {\n    const listenerTarget = targetFunc === null || targetFunc === void 0 ? void 0 : targetFunc();\n    if (!listenerTarget) {\n      return;\n    }\n    TRIGGER_EVENTS.forEach(eventName => {\n      var _a;\n      if (prevListener.current) {\n        (_a = prevTarget.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(eventName, prevListener.current);\n      }\n      listenerTarget === null || listenerTarget === void 0 ? void 0 : listenerTarget.addEventListener(eventName, lazyUpdatePosition);\n    });\n    prevTarget.current = listenerTarget;\n    prevListener.current = lazyUpdatePosition;\n  };\n  const removeListeners = () => {\n    if (timer.current) {\n      clearTimeout(timer.current);\n      timer.current = null;\n    }\n    const newTarget = targetFunc === null || targetFunc === void 0 ? void 0 : targetFunc();\n    TRIGGER_EVENTS.forEach(eventName => {\n      var _a;\n      newTarget === null || newTarget === void 0 ? void 0 : newTarget.removeEventListener(eventName, lazyUpdatePosition);\n      if (prevListener.current) {\n        (_a = prevTarget.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(eventName, prevListener.current);\n      }\n    });\n    updatePosition.cancel();\n    lazyUpdatePosition.cancel();\n  };\n  React.useImperativeHandle(ref, () => ({\n    updatePosition\n  }));\n  // mount & unmount\n  React.useEffect(() => {\n    // [Legacy] Wait for parent component ref has its value.\n    // We should use target as directly element instead of function which makes element check hard.\n    timer.current = setTimeout(addListeners);\n    return () => removeListeners();\n  }, []);\n  React.useEffect(() => {\n    addListeners();\n    return () => removeListeners();\n  }, [target, affixStyle, lastAffix, offsetTop, offsetBottom]);\n  React.useEffect(() => {\n    updatePosition();\n  }, [target, offsetTop, offsetBottom]);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(affixPrefixCls);\n  const rootCls = classNames(rootClassName, hashId, affixPrefixCls, cssVarCls);\n  const mergedCls = classNames({\n    [rootCls]: affixStyle\n  });\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: updatePosition\n  }, /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    style: style,\n    className: className,\n    ref: placeholderNodeRef\n  }, restProps), affixStyle && /*#__PURE__*/React.createElement(\"div\", {\n    style: placeholderStyle,\n    \"aria-hidden\": \"true\"\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: mergedCls,\n    ref: fixedNodeRef,\n    style: affixStyle\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: updatePosition\n  }, children)))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Affix.displayName = 'Affix';\n}\nexport default Affix;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,wBAAwB,MAAM,mCAAmC;AACxE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,SAASC,cAAc,EAAEC,WAAW,EAAEC,aAAa,QAAQ,SAAS;AACpE,MAAMC,cAAc,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC;AACtG,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;AACtD;AACA,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,KAAK,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC1D,IAAIC,EAAE;EACN,MAAM;MACFC,KAAK;MACLC,SAAS;MACTC,YAAY;MACZC,SAAS;MACTC,SAAS;MACTC,aAAa;MACbC,QAAQ;MACRC,MAAM;MACNC,QAAQ;MACRC;IACF,CAAC,GAAGZ,KAAK;IACTa,SAAS,GAAG3C,MAAM,CAAC8B,KAAK,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;EACxK,MAAM;IACJc,YAAY;IACZC;EACF,CAAC,GAAG/B,KAAK,CAACgC,UAAU,CAAC5B,aAAa,CAAC;EACnC,MAAM6B,cAAc,GAAGH,YAAY,CAAC,OAAO,EAAER,SAAS,CAAC;EACvD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,KAAK,CAACoC,QAAQ,CAAC,CAAC;EACpD,MAAM,CAACG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,KAAK,CAACoC,QAAQ,CAAC,CAAC;EAChE,MAAMK,MAAM,GAAGzC,KAAK,CAAC0C,MAAM,CAAC9B,iBAAiB,CAAC;EAC9C,MAAM+B,UAAU,GAAG3C,KAAK,CAAC0C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,YAAY,GAAG5C,KAAK,CAAC0C,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMG,kBAAkB,GAAG7C,KAAK,CAAC0C,MAAM,CAAC,IAAI,CAAC;EAC7C,MAAMI,YAAY,GAAG9C,KAAK,CAAC0C,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMK,KAAK,GAAG/C,KAAK,CAAC0C,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMM,UAAU,GAAG,CAAC9B,EAAE,GAAGQ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGK,kBAAkB,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGR,gBAAgB;EAC9I,MAAMuC,iBAAiB,GAAG5B,YAAY,KAAK6B,SAAS,IAAI9B,SAAS,KAAK8B,SAAS,GAAG,CAAC,GAAG9B,SAAS;EAC/F;EACA,MAAM+B,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIV,MAAM,CAACW,OAAO,KAAKvC,oBAAoB,IAAI,CAACiC,YAAY,CAACM,OAAO,IAAI,CAACP,kBAAkB,CAACO,OAAO,IAAI,CAACJ,UAAU,EAAE;MAClH;IACF;IACA,MAAMK,UAAU,GAAGL,UAAU,CAAC,CAAC;IAC/B,IAAIK,UAAU,EAAE;MACd,MAAMC,QAAQ,GAAG;QACfb,MAAM,EAAE7B;MACV,CAAC;MACD,MAAM2C,eAAe,GAAG/C,aAAa,CAACqC,kBAAkB,CAACO,OAAO,CAAC;MACjE,IAAIG,eAAe,CAACC,GAAG,KAAK,CAAC,IAAID,eAAe,CAACE,IAAI,KAAK,CAAC,IAAIF,eAAe,CAACG,KAAK,KAAK,CAAC,IAAIH,eAAe,CAACI,MAAM,KAAK,CAAC,EAAE;QAC1H;MACF;MACA,MAAMC,UAAU,GAAGpD,aAAa,CAAC6C,UAAU,CAAC;MAC5C,MAAMQ,QAAQ,GAAGtD,WAAW,CAACgD,eAAe,EAAEK,UAAU,EAAEX,iBAAiB,CAAC;MAC5E,MAAMa,WAAW,GAAGxD,cAAc,CAACiD,eAAe,EAAEK,UAAU,EAAEvC,YAAY,CAAC;MAC7E,IAAIwC,QAAQ,KAAKX,SAAS,EAAE;QAC1BI,QAAQ,CAACjB,UAAU,GAAG;UACpB0B,QAAQ,EAAE,OAAO;UACjBP,GAAG,EAAEK,QAAQ;UACbH,KAAK,EAAEH,eAAe,CAACG,KAAK;UAC5BC,MAAM,EAAEJ,eAAe,CAACI;QAC1B,CAAC;QACDL,QAAQ,CAACf,gBAAgB,GAAG;UAC1BmB,KAAK,EAAEH,eAAe,CAACG,KAAK;UAC5BC,MAAM,EAAEJ,eAAe,CAACI;QAC1B,CAAC;MACH,CAAC,MAAM,IAAIG,WAAW,KAAKZ,SAAS,EAAE;QACpCI,QAAQ,CAACjB,UAAU,GAAG;UACpB0B,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAEF,WAAW;UACnBJ,KAAK,EAAEH,eAAe,CAACG,KAAK;UAC5BC,MAAM,EAAEJ,eAAe,CAACI;QAC1B,CAAC;QACDL,QAAQ,CAACf,gBAAgB,GAAG;UAC1BmB,KAAK,EAAEH,eAAe,CAACG,KAAK;UAC5BC,MAAM,EAAEJ,eAAe,CAACI;QAC1B,CAAC;MACH;MACAL,QAAQ,CAACpB,SAAS,GAAG,CAAC,CAACoB,QAAQ,CAACjB,UAAU;MAC1C,IAAIH,SAAS,KAAKoB,QAAQ,CAACpB,SAAS,EAAE;QACpCP,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC2B,QAAQ,CAACpB,SAAS,CAAC;MAClF;MACAO,MAAM,CAACW,OAAO,GAAGE,QAAQ,CAACb,MAAM;MAChCH,aAAa,CAACgB,QAAQ,CAACjB,UAAU,CAAC;MAClCG,mBAAmB,CAACc,QAAQ,CAACf,gBAAgB,CAAC;MAC9CJ,YAAY,CAACmB,QAAQ,CAACpB,SAAS,CAAC;IAClC;EACF,CAAC;EACD,MAAM+B,cAAc,GAAGA,CAAA,KAAM;IAC3BxB,MAAM,CAACW,OAAO,GAAGvC,oBAAoB;IACrCsC,OAAO,CAAC,CAAC;IACT,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnCxC,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAAC,CAAC;IACpG;EACF,CAAC;EACD,MAAMyC,cAAc,GAAGlE,wBAAwB,CAAC,MAAM;IACpD8D,cAAc,CAAC,CAAC;EAClB,CAAC,CAAC;EACF,MAAMK,kBAAkB,GAAGnE,wBAAwB,CAAC,MAAM;IACxD;IACA,IAAI6C,UAAU,IAAIX,UAAU,EAAE;MAC5B,MAAMgB,UAAU,GAAGL,UAAU,CAAC,CAAC;MAC/B,IAAIK,UAAU,IAAIR,kBAAkB,CAACO,OAAO,EAAE;QAC5C,MAAMQ,UAAU,GAAGpD,aAAa,CAAC6C,UAAU,CAAC;QAC5C,MAAME,eAAe,GAAG/C,aAAa,CAACqC,kBAAkB,CAACO,OAAO,CAAC;QACjE,MAAMS,QAAQ,GAAGtD,WAAW,CAACgD,eAAe,EAAEK,UAAU,EAAEX,iBAAiB,CAAC;QAC5E,MAAMa,WAAW,GAAGxD,cAAc,CAACiD,eAAe,EAAEK,UAAU,EAAEvC,YAAY,CAAC;QAC7E,IAAIwC,QAAQ,KAAKX,SAAS,IAAIb,UAAU,CAACmB,GAAG,KAAKK,QAAQ,IAAIC,WAAW,KAAKZ,SAAS,IAAIb,UAAU,CAAC2B,MAAM,KAAKF,WAAW,EAAE;UAC3H;QACF;MACF;IACF;IACA;IACAG,cAAc,CAAC,CAAC;EAClB,CAAC,CAAC;EACF,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,cAAc,GAAGxB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC;IAC3F,IAAI,CAACwB,cAAc,EAAE;MACnB;IACF;IACA/D,cAAc,CAACgE,OAAO,CAACC,SAAS,IAAI;MAClC,IAAIxD,EAAE;MACN,IAAI0B,YAAY,CAACQ,OAAO,EAAE;QACxB,CAAClC,EAAE,GAAGyB,UAAU,CAACS,OAAO,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyD,mBAAmB,CAACD,SAAS,EAAE9B,YAAY,CAACQ,OAAO,CAAC;MACxH;MACAoB,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,gBAAgB,CAACF,SAAS,EAAEJ,kBAAkB,CAAC;IAChI,CAAC,CAAC;IACF3B,UAAU,CAACS,OAAO,GAAGoB,cAAc;IACnC5B,YAAY,CAACQ,OAAO,GAAGkB,kBAAkB;EAC3C,CAAC;EACD,MAAMO,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI9B,KAAK,CAACK,OAAO,EAAE;MACjB0B,YAAY,CAAC/B,KAAK,CAACK,OAAO,CAAC;MAC3BL,KAAK,CAACK,OAAO,GAAG,IAAI;IACtB;IACA,MAAM2B,SAAS,GAAG/B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC;IACtFvC,cAAc,CAACgE,OAAO,CAACC,SAAS,IAAI;MAClC,IAAIxD,EAAE;MACN6D,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACJ,mBAAmB,CAACD,SAAS,EAAEJ,kBAAkB,CAAC;MAClH,IAAI1B,YAAY,CAACQ,OAAO,EAAE;QACxB,CAAClC,EAAE,GAAGyB,UAAU,CAACS,OAAO,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyD,mBAAmB,CAACD,SAAS,EAAE9B,YAAY,CAACQ,OAAO,CAAC;MACxH;IACF,CAAC,CAAC;IACFiB,cAAc,CAACW,MAAM,CAAC,CAAC;IACvBV,kBAAkB,CAACU,MAAM,CAAC,CAAC;EAC7B,CAAC;EACDhF,KAAK,CAACiF,mBAAmB,CAAChE,GAAG,EAAE,OAAO;IACpCoD;EACF,CAAC,CAAC,CAAC;EACH;EACArE,KAAK,CAACkF,SAAS,CAAC,MAAM;IACpB;IACA;IACAnC,KAAK,CAACK,OAAO,GAAG+B,UAAU,CAACZ,YAAY,CAAC;IACxC,OAAO,MAAMM,eAAe,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EACN7E,KAAK,CAACkF,SAAS,CAAC,MAAM;IACpBX,YAAY,CAAC,CAAC;IACd,OAAO,MAAMM,eAAe,CAAC,CAAC;EAChC,CAAC,EAAE,CAACnD,MAAM,EAAEW,UAAU,EAAEH,SAAS,EAAEd,SAAS,EAAEC,YAAY,CAAC,CAAC;EAC5DrB,KAAK,CAACkF,SAAS,CAAC,MAAM;IACpBb,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC3C,MAAM,EAAEN,SAAS,EAAEC,YAAY,CAAC,CAAC;EACrC,MAAM,CAAC+D,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGjF,QAAQ,CAAC4B,cAAc,CAAC;EAChE,MAAMsD,OAAO,GAAGtF,UAAU,CAACuB,aAAa,EAAE6D,MAAM,EAAEpD,cAAc,EAAEqD,SAAS,CAAC;EAC5E,MAAME,SAAS,GAAGvF,UAAU,CAAC;IAC3B,CAACsF,OAAO,GAAGlD;EACb,CAAC,CAAC;EACF,OAAO+C,UAAU,CAAC,aAAapF,KAAK,CAACyF,aAAa,CAACvF,cAAc,EAAE;IACjEwF,QAAQ,EAAErB;EACZ,CAAC,EAAE,aAAarE,KAAK,CAACyF,aAAa,CAAC,KAAK,EAAElG,MAAM,CAACoG,MAAM,CAAC;IACvDxE,KAAK,EAAEA,KAAK;IACZI,SAAS,EAAEA,SAAS;IACpBN,GAAG,EAAE4B;EACP,CAAC,EAAEhB,SAAS,CAAC,EAAEQ,UAAU,IAAI,aAAarC,KAAK,CAACyF,aAAa,CAAC,KAAK,EAAE;IACnEtE,KAAK,EAAEoB,gBAAgB;IACvB,aAAa,EAAE;EACjB,CAAC,CAAC,EAAE,aAAavC,KAAK,CAACyF,aAAa,CAAC,KAAK,EAAE;IAC1ClE,SAAS,EAAEiE,SAAS;IACpBvE,GAAG,EAAE6B,YAAY;IACjB3B,KAAK,EAAEkB;EACT,CAAC,EAAE,aAAarC,KAAK,CAACyF,aAAa,CAACvF,cAAc,EAAE;IAClDwF,QAAQ,EAAErB;EACZ,CAAC,EAAE5C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF,IAAIyC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCtD,KAAK,CAAC8E,WAAW,GAAG,OAAO;AAC7B;AACA,eAAe9E,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}