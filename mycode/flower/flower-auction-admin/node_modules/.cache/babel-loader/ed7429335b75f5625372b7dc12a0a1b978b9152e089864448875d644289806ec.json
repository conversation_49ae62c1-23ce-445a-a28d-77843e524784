{"ast": null, "code": "import * as React from 'react';\nimport rcWarning, { resetWarned as rcResetWarned } from \"rc-util/es/warning\";\nexport function noop() {}\nlet deprecatedWarnList = null;\nexport function resetWarned() {\n  deprecatedWarnList = null;\n  rcResetWarned();\n}\n// eslint-disable-next-line import/no-mutable-exports\nlet warning = noop;\nif (process.env.NODE_ENV !== 'production') {\n  warning = (valid, component, message) => {\n    rcWarning(valid, `[antd: ${component}] ${message}`);\n    // StrictMode will inject console which will not throw warning in React 17.\n    if (process.env.NODE_ENV === 'test') {\n      resetWarned();\n    }\n  };\n}\nexport const WarningContext = /*#__PURE__*/React.createContext({});\n/**\n * This is a hook but we not named as `useWarning`\n * since this is only used in development.\n * We should always wrap this in `if (process.env.NODE_ENV !== 'production')` condition\n */\nexport const devUseWarning = process.env.NODE_ENV !== 'production' ? component => {\n  const {\n    strict\n  } = React.useContext(WarningContext);\n  const typeWarning = (valid, type, message) => {\n    if (!valid) {\n      if (strict === false && type === 'deprecated') {\n        const existWarning = deprecatedWarnList;\n        if (!deprecatedWarnList) {\n          deprecatedWarnList = {};\n        }\n        deprecatedWarnList[component] = deprecatedWarnList[component] || [];\n        if (!deprecatedWarnList[component].includes(message || '')) {\n          deprecatedWarnList[component].push(message || '');\n        }\n        // Warning for the first time\n        if (!existWarning) {\n          console.warn('[antd] There exists deprecated usage in your code:', deprecatedWarnList);\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(valid, component, message) : void 0;\n      }\n    }\n  };\n  typeWarning.deprecated = (valid, oldProp, newProp, message) => {\n    typeWarning(valid, 'deprecated', `\\`${oldProp}\\` is deprecated. Please use \\`${newProp}\\` instead.${message ? ` ${message}` : ''}`);\n  };\n  return typeWarning;\n} : () => {\n  const noopWarning = () => {};\n  noopWarning.deprecated = noop;\n  return noopWarning;\n};\nexport default warning;", "map": {"version": 3, "names": ["React", "rc<PERSON><PERSON>ning", "resetWarned", "rcResetWarned", "noop", "deprecatedWarnList", "warning", "process", "env", "NODE_ENV", "valid", "component", "message", "WarningContext", "createContext", "devUseW<PERSON>ning", "strict", "useContext", "typeWarning", "type", "existWarning", "includes", "push", "console", "warn", "deprecated", "oldProp", "newProp", "noopWarning"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/_util/warning.js"], "sourcesContent": ["import * as React from 'react';\nimport rcWarning, { resetWarned as rcResetWarned } from \"rc-util/es/warning\";\nexport function noop() {}\nlet deprecatedWarnList = null;\nexport function resetWarned() {\n  deprecatedWarnList = null;\n  rcResetWarned();\n}\n// eslint-disable-next-line import/no-mutable-exports\nlet warning = noop;\nif (process.env.NODE_ENV !== 'production') {\n  warning = (valid, component, message) => {\n    rcWarning(valid, `[antd: ${component}] ${message}`);\n    // StrictMode will inject console which will not throw warning in React 17.\n    if (process.env.NODE_ENV === 'test') {\n      resetWarned();\n    }\n  };\n}\nexport const WarningContext = /*#__PURE__*/React.createContext({});\n/**\n * This is a hook but we not named as `useWarning`\n * since this is only used in development.\n * We should always wrap this in `if (process.env.NODE_ENV !== 'production')` condition\n */\nexport const devUseWarning = process.env.NODE_ENV !== 'production' ? component => {\n  const {\n    strict\n  } = React.useContext(WarningContext);\n  const typeWarning = (valid, type, message) => {\n    if (!valid) {\n      if (strict === false && type === 'deprecated') {\n        const existWarning = deprecatedWarnList;\n        if (!deprecatedWarnList) {\n          deprecatedWarnList = {};\n        }\n        deprecatedWarnList[component] = deprecatedWarnList[component] || [];\n        if (!deprecatedWarnList[component].includes(message || '')) {\n          deprecatedWarnList[component].push(message || '');\n        }\n        // Warning for the first time\n        if (!existWarning) {\n          console.warn('[antd] There exists deprecated usage in your code:', deprecatedWarnList);\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(valid, component, message) : void 0;\n      }\n    }\n  };\n  typeWarning.deprecated = (valid, oldProp, newProp, message) => {\n    typeWarning(valid, 'deprecated', `\\`${oldProp}\\` is deprecated. Please use \\`${newProp}\\` instead.${message ? ` ${message}` : ''}`);\n  };\n  return typeWarning;\n} : () => {\n  const noopWarning = () => {};\n  noopWarning.deprecated = noop;\n  return noopWarning;\n};\nexport default warning;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,IAAIC,WAAW,IAAIC,aAAa,QAAQ,oBAAoB;AAC5E,OAAO,SAASC,IAAIA,CAAA,EAAG,CAAC;AACxB,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,OAAO,SAASH,WAAWA,CAAA,EAAG;EAC5BG,kBAAkB,GAAG,IAAI;EACzBF,aAAa,CAAC,CAAC;AACjB;AACA;AACA,IAAIG,OAAO,GAAGF,IAAI;AAClB,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,OAAO,GAAGA,CAACI,KAAK,EAAEC,SAAS,EAAEC,OAAO,KAAK;IACvCX,SAAS,CAACS,KAAK,EAAE,UAAUC,SAAS,KAAKC,OAAO,EAAE,CAAC;IACnD;IACA,IAAIL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnCP,WAAW,CAAC,CAAC;IACf;EACF,CAAC;AACH;AACA,OAAO,MAAMW,cAAc,GAAG,aAAab,KAAK,CAACc,aAAa,CAAC,CAAC,CAAC,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGE,SAAS,IAAI;EAChF,MAAM;IACJK;EACF,CAAC,GAAGhB,KAAK,CAACiB,UAAU,CAACJ,cAAc,CAAC;EACpC,MAAMK,WAAW,GAAGA,CAACR,KAAK,EAAES,IAAI,EAAEP,OAAO,KAAK;IAC5C,IAAI,CAACF,KAAK,EAAE;MACV,IAAIM,MAAM,KAAK,KAAK,IAAIG,IAAI,KAAK,YAAY,EAAE;QAC7C,MAAMC,YAAY,GAAGf,kBAAkB;QACvC,IAAI,CAACA,kBAAkB,EAAE;UACvBA,kBAAkB,GAAG,CAAC,CAAC;QACzB;QACAA,kBAAkB,CAACM,SAAS,CAAC,GAAGN,kBAAkB,CAACM,SAAS,CAAC,IAAI,EAAE;QACnE,IAAI,CAACN,kBAAkB,CAACM,SAAS,CAAC,CAACU,QAAQ,CAACT,OAAO,IAAI,EAAE,CAAC,EAAE;UAC1DP,kBAAkB,CAACM,SAAS,CAAC,CAACW,IAAI,CAACV,OAAO,IAAI,EAAE,CAAC;QACnD;QACA;QACA,IAAI,CAACQ,YAAY,EAAE;UACjBG,OAAO,CAACC,IAAI,CAAC,oDAAoD,EAAEnB,kBAAkB,CAAC;QACxF;MACF,CAAC,MAAM;QACLE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGH,OAAO,CAACI,KAAK,EAAEC,SAAS,EAAEC,OAAO,CAAC,GAAG,KAAK,CAAC;MACrF;IACF;EACF,CAAC;EACDM,WAAW,CAACO,UAAU,GAAG,CAACf,KAAK,EAAEgB,OAAO,EAAEC,OAAO,EAAEf,OAAO,KAAK;IAC7DM,WAAW,CAACR,KAAK,EAAE,YAAY,EAAE,KAAKgB,OAAO,kCAAkCC,OAAO,cAAcf,OAAO,GAAG,IAAIA,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC;EACrI,CAAC;EACD,OAAOM,WAAW;AACpB,CAAC,GAAG,MAAM;EACR,MAAMU,WAAW,GAAGA,CAAA,KAAM,CAAC,CAAC;EAC5BA,WAAW,CAACH,UAAU,GAAGrB,IAAI;EAC7B,OAAOwB,WAAW;AACpB,CAAC;AACD,eAAetB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}