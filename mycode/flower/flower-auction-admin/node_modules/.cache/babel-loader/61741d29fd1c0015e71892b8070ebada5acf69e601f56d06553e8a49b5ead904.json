{"ast": null, "code": "import Pagination from \"rc-pagination/es/locale/en_US\";\nimport Calendar from '../calendar/locale/en_US';\nimport DatePicker from '../date-picker/locale/en_US';\nimport TimePicker from '../time-picker/locale/en_US';\nconst typeTemplate = '${label} is not a valid ${type}';\nconst localeValues = {\n  locale: 'en',\n  Pagination,\n  DatePicker,\n  TimePicker,\n  Calendar,\n  global: {\n    placeholder: 'Please select',\n    close: 'Close'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'No filters',\n    filterCheckAll: 'Select all items',\n    filterSearchPlaceholder: 'Search in filters',\n    emptyText: 'No data',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectNone: 'Clear all data',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting'\n  },\n  Tour: {\n    Next: 'Next',\n    Previous: 'Previous',\n    Finish: 'Finish'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    deselectAll: 'Deselect all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand',\n    collapse: 'Collapse'\n  },\n  Form: {\n    optional: '(optional)',\n    defaultValidateMessages: {\n      default: 'Field validation error for ${label}',\n      required: 'Please enter ${label}',\n      enum: '${label} must be one of [${enum}]',\n      whitespace: '${label} cannot be a blank character',\n      date: {\n        format: '${label} date format is invalid',\n        parse: '${label} cannot be converted to a date',\n        invalid: '${label} is an invalid date'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label} must be ${len} characters',\n        min: '${label} must be at least ${min} characters',\n        max: '${label} must be up to ${max} characters',\n        range: '${label} must be between ${min}-${max} characters'\n      },\n      number: {\n        len: '${label} must be equal to ${len}',\n        min: '${label} must be minimum ${min}',\n        max: '${label} must be maximum ${max}',\n        range: '${label} must be between ${min}-${max}'\n      },\n      array: {\n        len: 'Must be ${len} ${label}',\n        min: 'At least ${min} ${label}',\n        max: 'At most ${max} ${label}',\n        range: 'The amount of ${label} must be between ${min}-${max}'\n      },\n      pattern: {\n        mismatch: '${label} does not match the pattern ${pattern}'\n      }\n    }\n  },\n  Image: {\n    preview: 'Preview'\n  },\n  QRCode: {\n    expired: 'QR code expired',\n    refresh: 'Refresh',\n    scanned: 'Scanned'\n  },\n  ColorPicker: {\n    presetEmpty: 'Empty',\n    transparent: 'Transparent',\n    singleColor: 'Single',\n    gradientColor: 'Gradient'\n  }\n};\nexport default localeValues;", "map": {"version": 3, "names": ["Pagination", "Calendar", "DatePicker", "TimePicker", "typeTemplate", "localeValues", "locale", "global", "placeholder", "close", "Table", "filterTitle", "filterConfirm", "filterReset", "filterEmptyText", "filterCheckAll", "filterSearchPlaceholder", "emptyText", "selectAll", "selectInvert", "selectNone", "selectionAll", "sortTitle", "expand", "collapse", "triggerDesc", "triggerAsc", "cancelSort", "Tour", "Next", "Previous", "Finish", "Modal", "okText", "cancelText", "justOkText", "Popconfirm", "Transfer", "titles", "searchPlaceholder", "itemUnit", "itemsUnit", "remove", "selectCurrent", "removeCurrent", "deselectAll", "removeAll", "Upload", "uploading", "removeFile", "uploadError", "previewFile", "downloadFile", "Empty", "description", "Icon", "icon", "Text", "edit", "copy", "copied", "Form", "optional", "defaultValidateMessages", "default", "required", "enum", "whitespace", "date", "format", "parse", "invalid", "types", "string", "method", "array", "object", "number", "boolean", "integer", "float", "regexp", "email", "url", "hex", "len", "min", "max", "range", "pattern", "mismatch", "Image", "preview", "QRCode", "expired", "refresh", "scanned", "ColorPicker", "presetEmpty", "transparent", "singleColor", "gradientColor"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/locale/en_US.js"], "sourcesContent": ["import Pagination from \"rc-pagination/es/locale/en_US\";\nimport Calendar from '../calendar/locale/en_US';\nimport DatePicker from '../date-picker/locale/en_US';\nimport TimePicker from '../time-picker/locale/en_US';\nconst typeTemplate = '${label} is not a valid ${type}';\nconst localeValues = {\n  locale: 'en',\n  Pagination,\n  DatePicker,\n  TimePicker,\n  Calendar,\n  global: {\n    placeholder: 'Please select',\n    close: 'Close'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'No filters',\n    filterCheckAll: 'Select all items',\n    filterSearchPlaceholder: 'Search in filters',\n    emptyText: 'No data',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectNone: 'Clear all data',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting'\n  },\n  Tour: {\n    Next: 'Next',\n    Previous: 'Previous',\n    Finish: 'Finish'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    deselectAll: 'Deselect all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand',\n    collapse: 'Collapse'\n  },\n  Form: {\n    optional: '(optional)',\n    defaultValidateMessages: {\n      default: 'Field validation error for ${label}',\n      required: 'Please enter ${label}',\n      enum: '${label} must be one of [${enum}]',\n      whitespace: '${label} cannot be a blank character',\n      date: {\n        format: '${label} date format is invalid',\n        parse: '${label} cannot be converted to a date',\n        invalid: '${label} is an invalid date'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label} must be ${len} characters',\n        min: '${label} must be at least ${min} characters',\n        max: '${label} must be up to ${max} characters',\n        range: '${label} must be between ${min}-${max} characters'\n      },\n      number: {\n        len: '${label} must be equal to ${len}',\n        min: '${label} must be minimum ${min}',\n        max: '${label} must be maximum ${max}',\n        range: '${label} must be between ${min}-${max}'\n      },\n      array: {\n        len: 'Must be ${len} ${label}',\n        min: 'At least ${min} ${label}',\n        max: 'At most ${max} ${label}',\n        range: 'The amount of ${label} must be between ${min}-${max}'\n      },\n      pattern: {\n        mismatch: '${label} does not match the pattern ${pattern}'\n      }\n    }\n  },\n  Image: {\n    preview: 'Preview'\n  },\n  QRCode: {\n    expired: 'QR code expired',\n    refresh: 'Refresh',\n    scanned: 'Scanned'\n  },\n  ColorPicker: {\n    presetEmpty: 'Empty',\n    transparent: 'Transparent',\n    singleColor: 'Single',\n    gradientColor: 'Gradient'\n  }\n};\nexport default localeValues;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,+BAA+B;AACtD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,MAAMC,YAAY,GAAG,iCAAiC;AACtD,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE,IAAI;EACZN,UAAU;EACVE,UAAU;EACVC,UAAU;EACVF,QAAQ;EACRM,MAAM,EAAE;IACNC,WAAW,EAAE,eAAe;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAE;IACLC,WAAW,EAAE,aAAa;IAC1BC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,OAAO;IACpBC,eAAe,EAAE,YAAY;IAC7BC,cAAc,EAAE,kBAAkB;IAClCC,uBAAuB,EAAE,mBAAmB;IAC5CC,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE,qBAAqB;IAChCC,YAAY,EAAE,qBAAqB;IACnCC,UAAU,EAAE,gBAAgB;IAC5BC,YAAY,EAAE,iBAAiB;IAC/BC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,cAAc;IACxBC,WAAW,EAAE,0BAA0B;IACvCC,UAAU,EAAE,yBAAyB;IACrCC,UAAU,EAAE;EACd,CAAC;EACDC,IAAI,EAAE;IACJC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE;EACV,CAAC;EACDC,KAAK,EAAE;IACLC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACVH,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE;EACd,CAAC;EACDG,QAAQ,EAAE;IACRC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IAChBC,iBAAiB,EAAE,aAAa;IAChCC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,qBAAqB;IACpCC,aAAa,EAAE,qBAAqB;IACpC1B,SAAS,EAAE,iBAAiB;IAC5B2B,WAAW,EAAE,mBAAmB;IAChCC,SAAS,EAAE,iBAAiB;IAC5B3B,YAAY,EAAE;EAChB,CAAC;EACD4B,MAAM,EAAE;IACNC,SAAS,EAAE,cAAc;IACzBC,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE;EAChB,CAAC;EACDC,KAAK,EAAE;IACLC,WAAW,EAAE;EACf,CAAC;EACDC,IAAI,EAAE;IACJC,IAAI,EAAE;EACR,CAAC;EACDC,IAAI,EAAE;IACJC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBrC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACZ,CAAC;EACDqC,IAAI,EAAE;IACJC,QAAQ,EAAE,YAAY;IACtBC,uBAAuB,EAAE;MACvBC,OAAO,EAAE,qCAAqC;MAC9CC,QAAQ,EAAE,uBAAuB;MACjCC,IAAI,EAAE,mCAAmC;MACzCC,UAAU,EAAE,sCAAsC;MAClDC,IAAI,EAAE;QACJC,MAAM,EAAE,iCAAiC;QACzCC,KAAK,EAAE,wCAAwC;QAC/CC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAErE,YAAY;QACpBsE,MAAM,EAAEtE,YAAY;QACpBuE,KAAK,EAAEvE,YAAY;QACnBwE,MAAM,EAAExE,YAAY;QACpByE,MAAM,EAAEzE,YAAY;QACpBgE,IAAI,EAAEhE,YAAY;QAClB0E,OAAO,EAAE1E,YAAY;QACrB2E,OAAO,EAAE3E,YAAY;QACrB4E,KAAK,EAAE5E,YAAY;QACnB6E,MAAM,EAAE7E,YAAY;QACpB8E,KAAK,EAAE9E,YAAY;QACnB+E,GAAG,EAAE/E,YAAY;QACjBgF,GAAG,EAAEhF;MACP,CAAC;MACDqE,MAAM,EAAE;QACNY,GAAG,EAAE,oCAAoC;QACzCC,GAAG,EAAE,6CAA6C;QAClDC,GAAG,EAAE,0CAA0C;QAC/CC,KAAK,EAAE;MACT,CAAC;MACDX,MAAM,EAAE;QACNQ,GAAG,EAAE,kCAAkC;QACvCC,GAAG,EAAE,iCAAiC;QACtCC,GAAG,EAAE,iCAAiC;QACtCC,KAAK,EAAE;MACT,CAAC;MACDb,KAAK,EAAE;QACLU,GAAG,EAAE,yBAAyB;QAC9BC,GAAG,EAAE,0BAA0B;QAC/BC,GAAG,EAAE,yBAAyB;QAC9BC,KAAK,EAAE;MACT,CAAC;MACDC,OAAO,EAAE;QACPC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACDC,KAAK,EAAE;IACLC,OAAO,EAAE;EACX,CAAC;EACDC,MAAM,EAAE;IACNC,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE;EACX,CAAC;EACDC,WAAW,EAAE;IACXC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,QAAQ;IACrBC,aAAa,EAAE;EACjB;AACF,CAAC;AACD,eAAehG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}