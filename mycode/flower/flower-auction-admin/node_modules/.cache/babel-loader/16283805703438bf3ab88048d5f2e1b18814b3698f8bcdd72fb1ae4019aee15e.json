{"ast": null, "code": "import presetColors from \"./presetColors\";\nconst round = Math.round;\n\n/**\n * Support format, alpha unit will check the % mark:\n * - rgba(102, 204, 255, .5)      -> [102, 204, 255, 0.5]\n * - rgb(102 204 255 / .5)        -> [102, 204, 255, 0.5]\n * - rgb(100%, 50%, 0% / 50%)     -> [255, 128, 0, 0.5]\n * - hsl(270, 60, 40, .5)         -> [270, 60, 40, 0.5]\n * - hsl(270deg 60% 40% / 50%)   -> [270, 60, 40, 0.5]\n *\n * When `base` is provided, the percentage value will be divided by `base`.\n */\nfunction splitColorStr(str, parseNum) {\n  const match = str\n  // Remove str before `(`\n  .replace(/^[^(]*\\((.*)/, '$1')\n  // Remove str after `)`\n  .replace(/\\).*/, '').match(/\\d*\\.?\\d+%?/g) || [];\n  const numList = match.map(item => parseFloat(item));\n  for (let i = 0; i < 3; i += 1) {\n    numList[i] = parseNum(numList[i] || 0, match[i] || '', i);\n  }\n\n  // For alpha. 50% should be 0.5\n  if (match[3]) {\n    numList[3] = match[3].includes('%') ? numList[3] / 100 : numList[3];\n  } else {\n    // By default, alpha is 1\n    numList[3] = 1;\n  }\n  return numList;\n}\nconst parseHSVorHSL = (num, _, index) => index === 0 ? num : num / 100;\n\n/** round and limit number to integer between 0-255 */\nfunction limitRange(value, max) {\n  const mergedMax = max || 255;\n  if (value > mergedMax) {\n    return mergedMax;\n  }\n  if (value < 0) {\n    return 0;\n  }\n  return value;\n}\nexport class FastColor {\n  /**\n   * All FastColor objects are valid. So isValid is always true. This property is kept to be compatible with TinyColor.\n   */\n  isValid = true;\n\n  /**\n   * Red, R in RGB\n   */\n  r = 0;\n\n  /**\n   * Green, G in RGB\n   */\n  g = 0;\n\n  /**\n   * Blue, B in RGB\n   */\n  b = 0;\n\n  /**\n   * Alpha/Opacity, A in RGBA/HSLA\n   */\n  a = 1;\n\n  // HSV privates\n  _h;\n  _s;\n  _l;\n  _v;\n\n  // intermediate variables to calculate HSL/HSV\n  _max;\n  _min;\n  _brightness;\n  constructor(input) {\n    /**\n     * Always check 3 char in the object to determine the format.\n     * We not use function in check to save bundle size.\n     * e.g. 'rgb' -> { r: 0, g: 0, b: 0 }.\n     */\n    function matchFormat(str) {\n      return str[0] in input && str[1] in input && str[2] in input;\n    }\n    if (!input) {\n      // Do nothing since already initialized\n    } else if (typeof input === 'string') {\n      const trimStr = input.trim();\n      function matchPrefix(prefix) {\n        return trimStr.startsWith(prefix);\n      }\n      if (/^#?[A-F\\d]{3,8}$/i.test(trimStr)) {\n        this.fromHexString(trimStr);\n      } else if (matchPrefix('rgb')) {\n        this.fromRgbString(trimStr);\n      } else if (matchPrefix('hsl')) {\n        this.fromHslString(trimStr);\n      } else if (matchPrefix('hsv') || matchPrefix('hsb')) {\n        this.fromHsvString(trimStr);\n      } else {\n        // From preset color\n        const presetColor = presetColors[trimStr.toLowerCase()];\n        if (presetColor) {\n          this.fromHexString(\n          // Convert 36 hex to 16 hex\n          parseInt(presetColor, 36).toString(16).padStart(6, '0'));\n        }\n      }\n    } else if (input instanceof FastColor) {\n      this.r = input.r;\n      this.g = input.g;\n      this.b = input.b;\n      this.a = input.a;\n      this._h = input._h;\n      this._s = input._s;\n      this._l = input._l;\n      this._v = input._v;\n    } else if (matchFormat('rgb')) {\n      this.r = limitRange(input.r);\n      this.g = limitRange(input.g);\n      this.b = limitRange(input.b);\n      this.a = typeof input.a === 'number' ? limitRange(input.a, 1) : 1;\n    } else if (matchFormat('hsl')) {\n      this.fromHsl(input);\n    } else if (matchFormat('hsv')) {\n      this.fromHsv(input);\n    } else {\n      throw new Error('@ant-design/fast-color: unsupported input ' + JSON.stringify(input));\n    }\n  }\n\n  // ======================= Setter =======================\n\n  setR(value) {\n    return this._sc('r', value);\n  }\n  setG(value) {\n    return this._sc('g', value);\n  }\n  setB(value) {\n    return this._sc('b', value);\n  }\n  setA(value) {\n    return this._sc('a', value, 1);\n  }\n  setHue(value) {\n    const hsv = this.toHsv();\n    hsv.h = value;\n    return this._c(hsv);\n  }\n\n  // ======================= Getter =======================\n  /**\n   * Returns the perceived luminance of a color, from 0-1.\n   * @see http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n   */\n  getLuminance() {\n    function adjustGamma(raw) {\n      const val = raw / 255;\n      return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);\n    }\n    const R = adjustGamma(this.r);\n    const G = adjustGamma(this.g);\n    const B = adjustGamma(this.b);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  }\n  getHue() {\n    if (typeof this._h === 'undefined') {\n      const delta = this.getMax() - this.getMin();\n      if (delta === 0) {\n        this._h = 0;\n      } else {\n        this._h = round(60 * (this.r === this.getMax() ? (this.g - this.b) / delta + (this.g < this.b ? 6 : 0) : this.g === this.getMax() ? (this.b - this.r) / delta + 2 : (this.r - this.g) / delta + 4));\n      }\n    }\n    return this._h;\n  }\n  getSaturation() {\n    if (typeof this._s === 'undefined') {\n      const delta = this.getMax() - this.getMin();\n      if (delta === 0) {\n        this._s = 0;\n      } else {\n        this._s = delta / this.getMax();\n      }\n    }\n    return this._s;\n  }\n  getLightness() {\n    if (typeof this._l === 'undefined') {\n      this._l = (this.getMax() + this.getMin()) / 510;\n    }\n    return this._l;\n  }\n  getValue() {\n    if (typeof this._v === 'undefined') {\n      this._v = this.getMax() / 255;\n    }\n    return this._v;\n  }\n\n  /**\n   * Returns the perceived brightness of the color, from 0-255.\n   * Note: this is not the b of HSB\n   * @see http://www.w3.org/TR/AERT#color-contrast\n   */\n  getBrightness() {\n    if (typeof this._brightness === 'undefined') {\n      this._brightness = (this.r * 299 + this.g * 587 + this.b * 114) / 1000;\n    }\n    return this._brightness;\n  }\n\n  // ======================== Func ========================\n\n  darken(amount = 10) {\n    const h = this.getHue();\n    const s = this.getSaturation();\n    let l = this.getLightness() - amount / 100;\n    if (l < 0) {\n      l = 0;\n    }\n    return this._c({\n      h,\n      s,\n      l,\n      a: this.a\n    });\n  }\n  lighten(amount = 10) {\n    const h = this.getHue();\n    const s = this.getSaturation();\n    let l = this.getLightness() + amount / 100;\n    if (l > 1) {\n      l = 1;\n    }\n    return this._c({\n      h,\n      s,\n      l,\n      a: this.a\n    });\n  }\n\n  /**\n   * Mix the current color a given amount with another color, from 0 to 100.\n   * 0 means no mixing (return current color).\n   */\n  mix(input, amount = 50) {\n    const color = this._c(input);\n    const p = amount / 100;\n    const calc = key => (color[key] - this[key]) * p + this[key];\n    const rgba = {\n      r: round(calc('r')),\n      g: round(calc('g')),\n      b: round(calc('b')),\n      a: round(calc('a') * 100) / 100\n    };\n    return this._c(rgba);\n  }\n\n  /**\n   * Mix the color with pure white, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return white.\n   */\n  tint(amount = 10) {\n    return this.mix({\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 1\n    }, amount);\n  }\n\n  /**\n   * Mix the color with pure black, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return black.\n   */\n  shade(amount = 10) {\n    return this.mix({\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    }, amount);\n  }\n  onBackground(background) {\n    const bg = this._c(background);\n    const alpha = this.a + bg.a * (1 - this.a);\n    const calc = key => {\n      return round((this[key] * this.a + bg[key] * bg.a * (1 - this.a)) / alpha);\n    };\n    return this._c({\n      r: calc('r'),\n      g: calc('g'),\n      b: calc('b'),\n      a: alpha\n    });\n  }\n\n  // ======================= Status =======================\n  isDark() {\n    return this.getBrightness() < 128;\n  }\n  isLight() {\n    return this.getBrightness() >= 128;\n  }\n\n  // ======================== MISC ========================\n  equals(other) {\n    return this.r === other.r && this.g === other.g && this.b === other.b && this.a === other.a;\n  }\n  clone() {\n    return this._c(this);\n  }\n\n  // ======================= Format =======================\n  toHexString() {\n    let hex = '#';\n    const rHex = (this.r || 0).toString(16);\n    hex += rHex.length === 2 ? rHex : '0' + rHex;\n    const gHex = (this.g || 0).toString(16);\n    hex += gHex.length === 2 ? gHex : '0' + gHex;\n    const bHex = (this.b || 0).toString(16);\n    hex += bHex.length === 2 ? bHex : '0' + bHex;\n    if (typeof this.a === 'number' && this.a >= 0 && this.a < 1) {\n      const aHex = round(this.a * 255).toString(16);\n      hex += aHex.length === 2 ? aHex : '0' + aHex;\n    }\n    return hex;\n  }\n\n  /** CSS support color pattern */\n  toHsl() {\n    return {\n      h: this.getHue(),\n      s: this.getSaturation(),\n      l: this.getLightness(),\n      a: this.a\n    };\n  }\n\n  /** CSS support color pattern */\n  toHslString() {\n    const h = this.getHue();\n    const s = round(this.getSaturation() * 100);\n    const l = round(this.getLightness() * 100);\n    return this.a !== 1 ? `hsla(${h},${s}%,${l}%,${this.a})` : `hsl(${h},${s}%,${l}%)`;\n  }\n\n  /** Same as toHsb */\n  toHsv() {\n    return {\n      h: this.getHue(),\n      s: this.getSaturation(),\n      v: this.getValue(),\n      a: this.a\n    };\n  }\n  toRgb() {\n    return {\n      r: this.r,\n      g: this.g,\n      b: this.b,\n      a: this.a\n    };\n  }\n  toRgbString() {\n    return this.a !== 1 ? `rgba(${this.r},${this.g},${this.b},${this.a})` : `rgb(${this.r},${this.g},${this.b})`;\n  }\n  toString() {\n    return this.toRgbString();\n  }\n\n  // ====================== Privates ======================\n  /** Return a new FastColor object with one channel changed */\n  _sc(rgb, value, max) {\n    const clone = this.clone();\n    clone[rgb] = limitRange(value, max);\n    return clone;\n  }\n  _c(input) {\n    return new this.constructor(input);\n  }\n  getMax() {\n    if (typeof this._max === 'undefined') {\n      this._max = Math.max(this.r, this.g, this.b);\n    }\n    return this._max;\n  }\n  getMin() {\n    if (typeof this._min === 'undefined') {\n      this._min = Math.min(this.r, this.g, this.b);\n    }\n    return this._min;\n  }\n  fromHexString(trimStr) {\n    const withoutPrefix = trimStr.replace('#', '');\n    function connectNum(index1, index2) {\n      return parseInt(withoutPrefix[index1] + withoutPrefix[index2 || index1], 16);\n    }\n    if (withoutPrefix.length < 6) {\n      // #rgb or #rgba\n      this.r = connectNum(0);\n      this.g = connectNum(1);\n      this.b = connectNum(2);\n      this.a = withoutPrefix[3] ? connectNum(3) / 255 : 1;\n    } else {\n      // #rrggbb or #rrggbbaa\n      this.r = connectNum(0, 1);\n      this.g = connectNum(2, 3);\n      this.b = connectNum(4, 5);\n      this.a = withoutPrefix[6] ? connectNum(6, 7) / 255 : 1;\n    }\n  }\n  fromHsl({\n    h,\n    s,\n    l,\n    a\n  }) {\n    this._h = h % 360;\n    this._s = s;\n    this._l = l;\n    this.a = typeof a === 'number' ? a : 1;\n    if (s <= 0) {\n      const rgb = round(l * 255);\n      this.r = rgb;\n      this.g = rgb;\n      this.b = rgb;\n    }\n    let r = 0,\n      g = 0,\n      b = 0;\n    const huePrime = h / 60;\n    const chroma = (1 - Math.abs(2 * l - 1)) * s;\n    const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));\n    if (huePrime >= 0 && huePrime < 1) {\n      r = chroma;\n      g = secondComponent;\n    } else if (huePrime >= 1 && huePrime < 2) {\n      r = secondComponent;\n      g = chroma;\n    } else if (huePrime >= 2 && huePrime < 3) {\n      g = chroma;\n      b = secondComponent;\n    } else if (huePrime >= 3 && huePrime < 4) {\n      g = secondComponent;\n      b = chroma;\n    } else if (huePrime >= 4 && huePrime < 5) {\n      r = secondComponent;\n      b = chroma;\n    } else if (huePrime >= 5 && huePrime < 6) {\n      r = chroma;\n      b = secondComponent;\n    }\n    const lightnessModification = l - chroma / 2;\n    this.r = round((r + lightnessModification) * 255);\n    this.g = round((g + lightnessModification) * 255);\n    this.b = round((b + lightnessModification) * 255);\n  }\n  fromHsv({\n    h,\n    s,\n    v,\n    a\n  }) {\n    this._h = h % 360;\n    this._s = s;\n    this._v = v;\n    this.a = typeof a === 'number' ? a : 1;\n    const vv = round(v * 255);\n    this.r = vv;\n    this.g = vv;\n    this.b = vv;\n    if (s <= 0) {\n      return;\n    }\n    const hh = h / 60;\n    const i = Math.floor(hh);\n    const ff = hh - i;\n    const p = round(v * (1.0 - s) * 255);\n    const q = round(v * (1.0 - s * ff) * 255);\n    const t = round(v * (1.0 - s * (1.0 - ff)) * 255);\n    switch (i) {\n      case 0:\n        this.g = t;\n        this.b = p;\n        break;\n      case 1:\n        this.r = q;\n        this.b = p;\n        break;\n      case 2:\n        this.r = p;\n        this.b = t;\n        break;\n      case 3:\n        this.r = p;\n        this.g = q;\n        break;\n      case 4:\n        this.r = t;\n        this.g = p;\n        break;\n      case 5:\n      default:\n        this.g = p;\n        this.b = q;\n        break;\n    }\n  }\n  fromHsvString(trimStr) {\n    const cells = splitColorStr(trimStr, parseHSVorHSL);\n    this.fromHsv({\n      h: cells[0],\n      s: cells[1],\n      v: cells[2],\n      a: cells[3]\n    });\n  }\n  fromHslString(trimStr) {\n    const cells = splitColorStr(trimStr, parseHSVorHSL);\n    this.fromHsl({\n      h: cells[0],\n      s: cells[1],\n      l: cells[2],\n      a: cells[3]\n    });\n  }\n  fromRgbString(trimStr) {\n    const cells = splitColorStr(trimStr, (num, txt) =>\n    // Convert percentage to number. e.g. 50% -> 128\n    txt.includes('%') ? round(num / 100 * 255) : num);\n    this.r = cells[0];\n    this.g = cells[1];\n    this.b = cells[2];\n    this.a = cells[3];\n  }\n}", "map": {"version": 3, "names": ["presetColors", "round", "Math", "splitColorStr", "str", "parseNum", "match", "replace", "numList", "map", "item", "parseFloat", "i", "includes", "parseHSVorHSL", "num", "_", "index", "limitRange", "value", "max", "mergedMax", "FastColor", "<PERSON><PERSON><PERSON><PERSON>", "r", "g", "b", "a", "_h", "_s", "_l", "_v", "_max", "_min", "_brightness", "constructor", "input", "matchFormat", "trimStr", "trim", "matchPrefix", "prefix", "startsWith", "test", "fromHexString", "fromRgbString", "fromHslString", "fromHsvString", "presetColor", "toLowerCase", "parseInt", "toString", "padStart", "fromHsl", "fromHsv", "Error", "JSON", "stringify", "setR", "_sc", "setG", "setB", "setA", "setHue", "hsv", "toHsv", "h", "_c", "getLuminance", "<PERSON><PERSON><PERSON><PERSON>", "raw", "val", "pow", "R", "G", "B", "getHue", "delta", "getMax", "getMin", "getSaturation", "getLightness", "getValue", "getBrightness", "darken", "amount", "s", "l", "lighten", "mix", "color", "p", "calc", "key", "rgba", "tint", "shade", "onBackground", "background", "bg", "alpha", "isDark", "isLight", "equals", "other", "clone", "toHexString", "hex", "rHex", "length", "gHex", "bHex", "aHex", "toHsl", "toHslString", "v", "toRgb", "toRgbString", "rgb", "min", "withoutPrefix", "connectNum", "index1", "index2", "huePrime", "chroma", "abs", "secondComponent", "lightnessModification", "vv", "hh", "floor", "ff", "q", "t", "cells", "txt"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/@ant-design/fast-color/es/FastColor.js"], "sourcesContent": ["import presetColors from \"./presetColors\";\nconst round = Math.round;\n\n/**\n * Support format, alpha unit will check the % mark:\n * - rgba(102, 204, 255, .5)      -> [102, 204, 255, 0.5]\n * - rgb(102 204 255 / .5)        -> [102, 204, 255, 0.5]\n * - rgb(100%, 50%, 0% / 50%)     -> [255, 128, 0, 0.5]\n * - hsl(270, 60, 40, .5)         -> [270, 60, 40, 0.5]\n * - hsl(270deg 60% 40% / 50%)   -> [270, 60, 40, 0.5]\n *\n * When `base` is provided, the percentage value will be divided by `base`.\n */\nfunction splitColorStr(str, parseNum) {\n  const match = str\n  // Remove str before `(`\n  .replace(/^[^(]*\\((.*)/, '$1')\n  // Remove str after `)`\n  .replace(/\\).*/, '').match(/\\d*\\.?\\d+%?/g) || [];\n  const numList = match.map(item => parseFloat(item));\n  for (let i = 0; i < 3; i += 1) {\n    numList[i] = parseNum(numList[i] || 0, match[i] || '', i);\n  }\n\n  // For alpha. 50% should be 0.5\n  if (match[3]) {\n    numList[3] = match[3].includes('%') ? numList[3] / 100 : numList[3];\n  } else {\n    // By default, alpha is 1\n    numList[3] = 1;\n  }\n  return numList;\n}\nconst parseHSVorHSL = (num, _, index) => index === 0 ? num : num / 100;\n\n/** round and limit number to integer between 0-255 */\nfunction limitRange(value, max) {\n  const mergedMax = max || 255;\n  if (value > mergedMax) {\n    return mergedMax;\n  }\n  if (value < 0) {\n    return 0;\n  }\n  return value;\n}\nexport class FastColor {\n  /**\n   * All FastColor objects are valid. So isValid is always true. This property is kept to be compatible with TinyColor.\n   */\n  isValid = true;\n\n  /**\n   * Red, R in RGB\n   */\n  r = 0;\n\n  /**\n   * Green, G in RGB\n   */\n  g = 0;\n\n  /**\n   * Blue, B in RGB\n   */\n  b = 0;\n\n  /**\n   * Alpha/Opacity, A in RGBA/HSLA\n   */\n  a = 1;\n\n  // HSV privates\n  _h;\n  _s;\n  _l;\n  _v;\n\n  // intermediate variables to calculate HSL/HSV\n  _max;\n  _min;\n  _brightness;\n  constructor(input) {\n    /**\n     * Always check 3 char in the object to determine the format.\n     * We not use function in check to save bundle size.\n     * e.g. 'rgb' -> { r: 0, g: 0, b: 0 }.\n     */\n    function matchFormat(str) {\n      return str[0] in input && str[1] in input && str[2] in input;\n    }\n    if (!input) {\n      // Do nothing since already initialized\n    } else if (typeof input === 'string') {\n      const trimStr = input.trim();\n      function matchPrefix(prefix) {\n        return trimStr.startsWith(prefix);\n      }\n      if (/^#?[A-F\\d]{3,8}$/i.test(trimStr)) {\n        this.fromHexString(trimStr);\n      } else if (matchPrefix('rgb')) {\n        this.fromRgbString(trimStr);\n      } else if (matchPrefix('hsl')) {\n        this.fromHslString(trimStr);\n      } else if (matchPrefix('hsv') || matchPrefix('hsb')) {\n        this.fromHsvString(trimStr);\n      } else {\n        // From preset color\n        const presetColor = presetColors[trimStr.toLowerCase()];\n        if (presetColor) {\n          this.fromHexString(\n          // Convert 36 hex to 16 hex\n          parseInt(presetColor, 36).toString(16).padStart(6, '0'));\n        }\n      }\n    } else if (input instanceof FastColor) {\n      this.r = input.r;\n      this.g = input.g;\n      this.b = input.b;\n      this.a = input.a;\n      this._h = input._h;\n      this._s = input._s;\n      this._l = input._l;\n      this._v = input._v;\n    } else if (matchFormat('rgb')) {\n      this.r = limitRange(input.r);\n      this.g = limitRange(input.g);\n      this.b = limitRange(input.b);\n      this.a = typeof input.a === 'number' ? limitRange(input.a, 1) : 1;\n    } else if (matchFormat('hsl')) {\n      this.fromHsl(input);\n    } else if (matchFormat('hsv')) {\n      this.fromHsv(input);\n    } else {\n      throw new Error('@ant-design/fast-color: unsupported input ' + JSON.stringify(input));\n    }\n  }\n\n  // ======================= Setter =======================\n\n  setR(value) {\n    return this._sc('r', value);\n  }\n  setG(value) {\n    return this._sc('g', value);\n  }\n  setB(value) {\n    return this._sc('b', value);\n  }\n  setA(value) {\n    return this._sc('a', value, 1);\n  }\n  setHue(value) {\n    const hsv = this.toHsv();\n    hsv.h = value;\n    return this._c(hsv);\n  }\n\n  // ======================= Getter =======================\n  /**\n   * Returns the perceived luminance of a color, from 0-1.\n   * @see http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n   */\n  getLuminance() {\n    function adjustGamma(raw) {\n      const val = raw / 255;\n      return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);\n    }\n    const R = adjustGamma(this.r);\n    const G = adjustGamma(this.g);\n    const B = adjustGamma(this.b);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  }\n  getHue() {\n    if (typeof this._h === 'undefined') {\n      const delta = this.getMax() - this.getMin();\n      if (delta === 0) {\n        this._h = 0;\n      } else {\n        this._h = round(60 * (this.r === this.getMax() ? (this.g - this.b) / delta + (this.g < this.b ? 6 : 0) : this.g === this.getMax() ? (this.b - this.r) / delta + 2 : (this.r - this.g) / delta + 4));\n      }\n    }\n    return this._h;\n  }\n  getSaturation() {\n    if (typeof this._s === 'undefined') {\n      const delta = this.getMax() - this.getMin();\n      if (delta === 0) {\n        this._s = 0;\n      } else {\n        this._s = delta / this.getMax();\n      }\n    }\n    return this._s;\n  }\n  getLightness() {\n    if (typeof this._l === 'undefined') {\n      this._l = (this.getMax() + this.getMin()) / 510;\n    }\n    return this._l;\n  }\n  getValue() {\n    if (typeof this._v === 'undefined') {\n      this._v = this.getMax() / 255;\n    }\n    return this._v;\n  }\n\n  /**\n   * Returns the perceived brightness of the color, from 0-255.\n   * Note: this is not the b of HSB\n   * @see http://www.w3.org/TR/AERT#color-contrast\n   */\n  getBrightness() {\n    if (typeof this._brightness === 'undefined') {\n      this._brightness = (this.r * 299 + this.g * 587 + this.b * 114) / 1000;\n    }\n    return this._brightness;\n  }\n\n  // ======================== Func ========================\n\n  darken(amount = 10) {\n    const h = this.getHue();\n    const s = this.getSaturation();\n    let l = this.getLightness() - amount / 100;\n    if (l < 0) {\n      l = 0;\n    }\n    return this._c({\n      h,\n      s,\n      l,\n      a: this.a\n    });\n  }\n  lighten(amount = 10) {\n    const h = this.getHue();\n    const s = this.getSaturation();\n    let l = this.getLightness() + amount / 100;\n    if (l > 1) {\n      l = 1;\n    }\n    return this._c({\n      h,\n      s,\n      l,\n      a: this.a\n    });\n  }\n\n  /**\n   * Mix the current color a given amount with another color, from 0 to 100.\n   * 0 means no mixing (return current color).\n   */\n  mix(input, amount = 50) {\n    const color = this._c(input);\n    const p = amount / 100;\n    const calc = key => (color[key] - this[key]) * p + this[key];\n    const rgba = {\n      r: round(calc('r')),\n      g: round(calc('g')),\n      b: round(calc('b')),\n      a: round(calc('a') * 100) / 100\n    };\n    return this._c(rgba);\n  }\n\n  /**\n   * Mix the color with pure white, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return white.\n   */\n  tint(amount = 10) {\n    return this.mix({\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 1\n    }, amount);\n  }\n\n  /**\n   * Mix the color with pure black, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return black.\n   */\n  shade(amount = 10) {\n    return this.mix({\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    }, amount);\n  }\n  onBackground(background) {\n    const bg = this._c(background);\n    const alpha = this.a + bg.a * (1 - this.a);\n    const calc = key => {\n      return round((this[key] * this.a + bg[key] * bg.a * (1 - this.a)) / alpha);\n    };\n    return this._c({\n      r: calc('r'),\n      g: calc('g'),\n      b: calc('b'),\n      a: alpha\n    });\n  }\n\n  // ======================= Status =======================\n  isDark() {\n    return this.getBrightness() < 128;\n  }\n  isLight() {\n    return this.getBrightness() >= 128;\n  }\n\n  // ======================== MISC ========================\n  equals(other) {\n    return this.r === other.r && this.g === other.g && this.b === other.b && this.a === other.a;\n  }\n  clone() {\n    return this._c(this);\n  }\n\n  // ======================= Format =======================\n  toHexString() {\n    let hex = '#';\n    const rHex = (this.r || 0).toString(16);\n    hex += rHex.length === 2 ? rHex : '0' + rHex;\n    const gHex = (this.g || 0).toString(16);\n    hex += gHex.length === 2 ? gHex : '0' + gHex;\n    const bHex = (this.b || 0).toString(16);\n    hex += bHex.length === 2 ? bHex : '0' + bHex;\n    if (typeof this.a === 'number' && this.a >= 0 && this.a < 1) {\n      const aHex = round(this.a * 255).toString(16);\n      hex += aHex.length === 2 ? aHex : '0' + aHex;\n    }\n    return hex;\n  }\n\n  /** CSS support color pattern */\n  toHsl() {\n    return {\n      h: this.getHue(),\n      s: this.getSaturation(),\n      l: this.getLightness(),\n      a: this.a\n    };\n  }\n\n  /** CSS support color pattern */\n  toHslString() {\n    const h = this.getHue();\n    const s = round(this.getSaturation() * 100);\n    const l = round(this.getLightness() * 100);\n    return this.a !== 1 ? `hsla(${h},${s}%,${l}%,${this.a})` : `hsl(${h},${s}%,${l}%)`;\n  }\n\n  /** Same as toHsb */\n  toHsv() {\n    return {\n      h: this.getHue(),\n      s: this.getSaturation(),\n      v: this.getValue(),\n      a: this.a\n    };\n  }\n  toRgb() {\n    return {\n      r: this.r,\n      g: this.g,\n      b: this.b,\n      a: this.a\n    };\n  }\n  toRgbString() {\n    return this.a !== 1 ? `rgba(${this.r},${this.g},${this.b},${this.a})` : `rgb(${this.r},${this.g},${this.b})`;\n  }\n  toString() {\n    return this.toRgbString();\n  }\n\n  // ====================== Privates ======================\n  /** Return a new FastColor object with one channel changed */\n  _sc(rgb, value, max) {\n    const clone = this.clone();\n    clone[rgb] = limitRange(value, max);\n    return clone;\n  }\n  _c(input) {\n    return new this.constructor(input);\n  }\n  getMax() {\n    if (typeof this._max === 'undefined') {\n      this._max = Math.max(this.r, this.g, this.b);\n    }\n    return this._max;\n  }\n  getMin() {\n    if (typeof this._min === 'undefined') {\n      this._min = Math.min(this.r, this.g, this.b);\n    }\n    return this._min;\n  }\n  fromHexString(trimStr) {\n    const withoutPrefix = trimStr.replace('#', '');\n    function connectNum(index1, index2) {\n      return parseInt(withoutPrefix[index1] + withoutPrefix[index2 || index1], 16);\n    }\n    if (withoutPrefix.length < 6) {\n      // #rgb or #rgba\n      this.r = connectNum(0);\n      this.g = connectNum(1);\n      this.b = connectNum(2);\n      this.a = withoutPrefix[3] ? connectNum(3) / 255 : 1;\n    } else {\n      // #rrggbb or #rrggbbaa\n      this.r = connectNum(0, 1);\n      this.g = connectNum(2, 3);\n      this.b = connectNum(4, 5);\n      this.a = withoutPrefix[6] ? connectNum(6, 7) / 255 : 1;\n    }\n  }\n  fromHsl({\n    h,\n    s,\n    l,\n    a\n  }) {\n    this._h = h % 360;\n    this._s = s;\n    this._l = l;\n    this.a = typeof a === 'number' ? a : 1;\n    if (s <= 0) {\n      const rgb = round(l * 255);\n      this.r = rgb;\n      this.g = rgb;\n      this.b = rgb;\n    }\n    let r = 0,\n      g = 0,\n      b = 0;\n    const huePrime = h / 60;\n    const chroma = (1 - Math.abs(2 * l - 1)) * s;\n    const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));\n    if (huePrime >= 0 && huePrime < 1) {\n      r = chroma;\n      g = secondComponent;\n    } else if (huePrime >= 1 && huePrime < 2) {\n      r = secondComponent;\n      g = chroma;\n    } else if (huePrime >= 2 && huePrime < 3) {\n      g = chroma;\n      b = secondComponent;\n    } else if (huePrime >= 3 && huePrime < 4) {\n      g = secondComponent;\n      b = chroma;\n    } else if (huePrime >= 4 && huePrime < 5) {\n      r = secondComponent;\n      b = chroma;\n    } else if (huePrime >= 5 && huePrime < 6) {\n      r = chroma;\n      b = secondComponent;\n    }\n    const lightnessModification = l - chroma / 2;\n    this.r = round((r + lightnessModification) * 255);\n    this.g = round((g + lightnessModification) * 255);\n    this.b = round((b + lightnessModification) * 255);\n  }\n  fromHsv({\n    h,\n    s,\n    v,\n    a\n  }) {\n    this._h = h % 360;\n    this._s = s;\n    this._v = v;\n    this.a = typeof a === 'number' ? a : 1;\n    const vv = round(v * 255);\n    this.r = vv;\n    this.g = vv;\n    this.b = vv;\n    if (s <= 0) {\n      return;\n    }\n    const hh = h / 60;\n    const i = Math.floor(hh);\n    const ff = hh - i;\n    const p = round(v * (1.0 - s) * 255);\n    const q = round(v * (1.0 - s * ff) * 255);\n    const t = round(v * (1.0 - s * (1.0 - ff)) * 255);\n    switch (i) {\n      case 0:\n        this.g = t;\n        this.b = p;\n        break;\n      case 1:\n        this.r = q;\n        this.b = p;\n        break;\n      case 2:\n        this.r = p;\n        this.b = t;\n        break;\n      case 3:\n        this.r = p;\n        this.g = q;\n        break;\n      case 4:\n        this.r = t;\n        this.g = p;\n        break;\n      case 5:\n      default:\n        this.g = p;\n        this.b = q;\n        break;\n    }\n  }\n  fromHsvString(trimStr) {\n    const cells = splitColorStr(trimStr, parseHSVorHSL);\n    this.fromHsv({\n      h: cells[0],\n      s: cells[1],\n      v: cells[2],\n      a: cells[3]\n    });\n  }\n  fromHslString(trimStr) {\n    const cells = splitColorStr(trimStr, parseHSVorHSL);\n    this.fromHsl({\n      h: cells[0],\n      s: cells[1],\n      l: cells[2],\n      a: cells[3]\n    });\n  }\n  fromRgbString(trimStr) {\n    const cells = splitColorStr(trimStr, (num, txt) =>\n    // Convert percentage to number. e.g. 50% -> 128\n    txt.includes('%') ? round(num / 100 * 255) : num);\n    this.r = cells[0];\n    this.g = cells[1];\n    this.b = cells[2];\n    this.a = cells[3];\n  }\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,gBAAgB;AACzC,MAAMC,KAAK,GAAGC,IAAI,CAACD,KAAK;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACpC,MAAMC,KAAK,GAAGF;EACd;EAAA,CACCG,OAAO,CAAC,cAAc,EAAE,IAAI;EAC7B;EAAA,CACCA,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACD,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE;EAChD,MAAME,OAAO,GAAGF,KAAK,CAACG,GAAG,CAACC,IAAI,IAAIC,UAAU,CAACD,IAAI,CAAC,CAAC;EACnD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC7BJ,OAAO,CAACI,CAAC,CAAC,GAAGP,QAAQ,CAACG,OAAO,CAACI,CAAC,CAAC,IAAI,CAAC,EAAEN,KAAK,CAACM,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC;EAC3D;;EAEA;EACA,IAAIN,KAAK,CAAC,CAAC,CAAC,EAAE;IACZE,OAAO,CAAC,CAAC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACO,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,OAAO,CAAC,CAAC,CAAC;EACrE,CAAC,MAAM;IACL;IACAA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAChB;EACA,OAAOA,OAAO;AAChB;AACA,MAAMM,aAAa,GAAGA,CAACC,GAAG,EAAEC,CAAC,EAAEC,KAAK,KAAKA,KAAK,KAAK,CAAC,GAAGF,GAAG,GAAGA,GAAG,GAAG,GAAG;;AAEtE;AACA,SAASG,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC9B,MAAMC,SAAS,GAAGD,GAAG,IAAI,GAAG;EAC5B,IAAID,KAAK,GAAGE,SAAS,EAAE;IACrB,OAAOA,SAAS;EAClB;EACA,IAAIF,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,CAAC;EACV;EACA,OAAOA,KAAK;AACd;AACA,OAAO,MAAMG,SAAS,CAAC;EACrB;AACF;AACA;EACEC,OAAO,GAAG,IAAI;;EAEd;AACF;AACA;EACEC,CAAC,GAAG,CAAC;;EAEL;AACF;AACA;EACEC,CAAC,GAAG,CAAC;;EAEL;AACF;AACA;EACEC,CAAC,GAAG,CAAC;;EAEL;AACF;AACA;EACEC,CAAC,GAAG,CAAC;;EAEL;EACAC,EAAE;EACFC,EAAE;EACFC,EAAE;EACFC,EAAE;;EAEF;EACAC,IAAI;EACJC,IAAI;EACJC,WAAW;EACXC,WAAWA,CAACC,KAAK,EAAE;IACjB;AACJ;AACA;AACA;AACA;IACI,SAASC,WAAWA,CAACjC,GAAG,EAAE;MACxB,OAAOA,GAAG,CAAC,CAAC,CAAC,IAAIgC,KAAK,IAAIhC,GAAG,CAAC,CAAC,CAAC,IAAIgC,KAAK,IAAIhC,GAAG,CAAC,CAAC,CAAC,IAAIgC,KAAK;IAC9D;IACA,IAAI,CAACA,KAAK,EAAE;MACV;IAAA,CACD,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACpC,MAAME,OAAO,GAAGF,KAAK,CAACG,IAAI,CAAC,CAAC;MAC5B,SAASC,WAAWA,CAACC,MAAM,EAAE;QAC3B,OAAOH,OAAO,CAACI,UAAU,CAACD,MAAM,CAAC;MACnC;MACA,IAAI,mBAAmB,CAACE,IAAI,CAACL,OAAO,CAAC,EAAE;QACrC,IAAI,CAACM,aAAa,CAACN,OAAO,CAAC;MAC7B,CAAC,MAAM,IAAIE,WAAW,CAAC,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACK,aAAa,CAACP,OAAO,CAAC;MAC7B,CAAC,MAAM,IAAIE,WAAW,CAAC,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACM,aAAa,CAACR,OAAO,CAAC;MAC7B,CAAC,MAAM,IAAIE,WAAW,CAAC,KAAK,CAAC,IAAIA,WAAW,CAAC,KAAK,CAAC,EAAE;QACnD,IAAI,CAACO,aAAa,CAACT,OAAO,CAAC;MAC7B,CAAC,MAAM;QACL;QACA,MAAMU,WAAW,GAAGhD,YAAY,CAACsC,OAAO,CAACW,WAAW,CAAC,CAAC,CAAC;QACvD,IAAID,WAAW,EAAE;UACf,IAAI,CAACJ,aAAa;UAClB;UACAM,QAAQ,CAACF,WAAW,EAAE,EAAE,CAAC,CAACG,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1D;MACF;IACF,CAAC,MAAM,IAAIhB,KAAK,YAAYd,SAAS,EAAE;MACrC,IAAI,CAACE,CAAC,GAAGY,KAAK,CAACZ,CAAC;MAChB,IAAI,CAACC,CAAC,GAAGW,KAAK,CAACX,CAAC;MAChB,IAAI,CAACC,CAAC,GAAGU,KAAK,CAACV,CAAC;MAChB,IAAI,CAACC,CAAC,GAAGS,KAAK,CAACT,CAAC;MAChB,IAAI,CAACC,EAAE,GAAGQ,KAAK,CAACR,EAAE;MAClB,IAAI,CAACC,EAAE,GAAGO,KAAK,CAACP,EAAE;MAClB,IAAI,CAACC,EAAE,GAAGM,KAAK,CAACN,EAAE;MAClB,IAAI,CAACC,EAAE,GAAGK,KAAK,CAACL,EAAE;IACpB,CAAC,MAAM,IAAIM,WAAW,CAAC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACb,CAAC,GAAGN,UAAU,CAACkB,KAAK,CAACZ,CAAC,CAAC;MAC5B,IAAI,CAACC,CAAC,GAAGP,UAAU,CAACkB,KAAK,CAACX,CAAC,CAAC;MAC5B,IAAI,CAACC,CAAC,GAAGR,UAAU,CAACkB,KAAK,CAACV,CAAC,CAAC;MAC5B,IAAI,CAACC,CAAC,GAAG,OAAOS,KAAK,CAACT,CAAC,KAAK,QAAQ,GAAGT,UAAU,CAACkB,KAAK,CAACT,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;IACnE,CAAC,MAAM,IAAIU,WAAW,CAAC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACgB,OAAO,CAACjB,KAAK,CAAC;IACrB,CAAC,MAAM,IAAIC,WAAW,CAAC,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACiB,OAAO,CAAClB,KAAK,CAAC;IACrB,CAAC,MAAM;MACL,MAAM,IAAImB,KAAK,CAAC,4CAA4C,GAAGC,IAAI,CAACC,SAAS,CAACrB,KAAK,CAAC,CAAC;IACvF;EACF;;EAEA;;EAEAsB,IAAIA,CAACvC,KAAK,EAAE;IACV,OAAO,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAExC,KAAK,CAAC;EAC7B;EACAyC,IAAIA,CAACzC,KAAK,EAAE;IACV,OAAO,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAExC,KAAK,CAAC;EAC7B;EACA0C,IAAIA,CAAC1C,KAAK,EAAE;IACV,OAAO,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAExC,KAAK,CAAC;EAC7B;EACA2C,IAAIA,CAAC3C,KAAK,EAAE;IACV,OAAO,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAExC,KAAK,EAAE,CAAC,CAAC;EAChC;EACA4C,MAAMA,CAAC5C,KAAK,EAAE;IACZ,MAAM6C,GAAG,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;IACxBD,GAAG,CAACE,CAAC,GAAG/C,KAAK;IACb,OAAO,IAAI,CAACgD,EAAE,CAACH,GAAG,CAAC;EACrB;;EAEA;EACA;AACF;AACA;AACA;EACEI,YAAYA,CAAA,EAAG;IACb,SAASC,WAAWA,CAACC,GAAG,EAAE;MACxB,MAAMC,GAAG,GAAGD,GAAG,GAAG,GAAG;MACrB,OAAOC,GAAG,IAAI,OAAO,GAAGA,GAAG,GAAG,KAAK,GAAGrE,IAAI,CAACsE,GAAG,CAAC,CAACD,GAAG,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IAC5E;IACA,MAAME,CAAC,GAAGJ,WAAW,CAAC,IAAI,CAAC7C,CAAC,CAAC;IAC7B,MAAMkD,CAAC,GAAGL,WAAW,CAAC,IAAI,CAAC5C,CAAC,CAAC;IAC7B,MAAMkD,CAAC,GAAGN,WAAW,CAAC,IAAI,CAAC3C,CAAC,CAAC;IAC7B,OAAO,MAAM,GAAG+C,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,MAAM,GAAGC,CAAC;EAC7C;EACAC,MAAMA,CAAA,EAAG;IACP,IAAI,OAAO,IAAI,CAAChD,EAAE,KAAK,WAAW,EAAE;MAClC,MAAMiD,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;MAC3C,IAAIF,KAAK,KAAK,CAAC,EAAE;QACf,IAAI,CAACjD,EAAE,GAAG,CAAC;MACb,CAAC,MAAM;QACL,IAAI,CAACA,EAAE,GAAG3B,KAAK,CAAC,EAAE,IAAI,IAAI,CAACuB,CAAC,KAAK,IAAI,CAACsD,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAACrD,CAAC,GAAG,IAAI,CAACC,CAAC,IAAImD,KAAK,IAAI,IAAI,CAACpD,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACD,CAAC,KAAK,IAAI,CAACqD,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAACpD,CAAC,GAAG,IAAI,CAACF,CAAC,IAAIqD,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAACrD,CAAC,GAAG,IAAI,CAACC,CAAC,IAAIoD,KAAK,GAAG,CAAC,CAAC,CAAC;MACrM;IACF;IACA,OAAO,IAAI,CAACjD,EAAE;EAChB;EACAoD,aAAaA,CAAA,EAAG;IACd,IAAI,OAAO,IAAI,CAACnD,EAAE,KAAK,WAAW,EAAE;MAClC,MAAMgD,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;MAC3C,IAAIF,KAAK,KAAK,CAAC,EAAE;QACf,IAAI,CAAChD,EAAE,GAAG,CAAC;MACb,CAAC,MAAM;QACL,IAAI,CAACA,EAAE,GAAGgD,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;MACjC;IACF;IACA,OAAO,IAAI,CAACjD,EAAE;EAChB;EACAoD,YAAYA,CAAA,EAAG;IACb,IAAI,OAAO,IAAI,CAACnD,EAAE,KAAK,WAAW,EAAE;MAClC,IAAI,CAACA,EAAE,GAAG,CAAC,IAAI,CAACgD,MAAM,CAAC,CAAC,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,IAAI,GAAG;IACjD;IACA,OAAO,IAAI,CAACjD,EAAE;EAChB;EACAoD,QAAQA,CAAA,EAAG;IACT,IAAI,OAAO,IAAI,CAACnD,EAAE,KAAK,WAAW,EAAE;MAClC,IAAI,CAACA,EAAE,GAAG,IAAI,CAAC+C,MAAM,CAAC,CAAC,GAAG,GAAG;IAC/B;IACA,OAAO,IAAI,CAAC/C,EAAE;EAChB;;EAEA;AACF;AACA;AACA;AACA;EACEoD,aAAaA,CAAA,EAAG;IACd,IAAI,OAAO,IAAI,CAACjD,WAAW,KAAK,WAAW,EAAE;MAC3C,IAAI,CAACA,WAAW,GAAG,CAAC,IAAI,CAACV,CAAC,GAAG,GAAG,GAAG,IAAI,CAACC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACC,CAAC,GAAG,GAAG,IAAI,IAAI;IACxE;IACA,OAAO,IAAI,CAACQ,WAAW;EACzB;;EAEA;;EAEAkD,MAAMA,CAACC,MAAM,GAAG,EAAE,EAAE;IAClB,MAAMnB,CAAC,GAAG,IAAI,CAACU,MAAM,CAAC,CAAC;IACvB,MAAMU,CAAC,GAAG,IAAI,CAACN,aAAa,CAAC,CAAC;IAC9B,IAAIO,CAAC,GAAG,IAAI,CAACN,YAAY,CAAC,CAAC,GAAGI,MAAM,GAAG,GAAG;IAC1C,IAAIE,CAAC,GAAG,CAAC,EAAE;MACTA,CAAC,GAAG,CAAC;IACP;IACA,OAAO,IAAI,CAACpB,EAAE,CAAC;MACbD,CAAC;MACDoB,CAAC;MACDC,CAAC;MACD5D,CAAC,EAAE,IAAI,CAACA;IACV,CAAC,CAAC;EACJ;EACA6D,OAAOA,CAACH,MAAM,GAAG,EAAE,EAAE;IACnB,MAAMnB,CAAC,GAAG,IAAI,CAACU,MAAM,CAAC,CAAC;IACvB,MAAMU,CAAC,GAAG,IAAI,CAACN,aAAa,CAAC,CAAC;IAC9B,IAAIO,CAAC,GAAG,IAAI,CAACN,YAAY,CAAC,CAAC,GAAGI,MAAM,GAAG,GAAG;IAC1C,IAAIE,CAAC,GAAG,CAAC,EAAE;MACTA,CAAC,GAAG,CAAC;IACP;IACA,OAAO,IAAI,CAACpB,EAAE,CAAC;MACbD,CAAC;MACDoB,CAAC;MACDC,CAAC;MACD5D,CAAC,EAAE,IAAI,CAACA;IACV,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACE8D,GAAGA,CAACrD,KAAK,EAAEiD,MAAM,GAAG,EAAE,EAAE;IACtB,MAAMK,KAAK,GAAG,IAAI,CAACvB,EAAE,CAAC/B,KAAK,CAAC;IAC5B,MAAMuD,CAAC,GAAGN,MAAM,GAAG,GAAG;IACtB,MAAMO,IAAI,GAAGC,GAAG,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC,GAAG,IAAI,CAACA,GAAG,CAAC,IAAIF,CAAC,GAAG,IAAI,CAACE,GAAG,CAAC;IAC5D,MAAMC,IAAI,GAAG;MACXtE,CAAC,EAAEvB,KAAK,CAAC2F,IAAI,CAAC,GAAG,CAAC,CAAC;MACnBnE,CAAC,EAAExB,KAAK,CAAC2F,IAAI,CAAC,GAAG,CAAC,CAAC;MACnBlE,CAAC,EAAEzB,KAAK,CAAC2F,IAAI,CAAC,GAAG,CAAC,CAAC;MACnBjE,CAAC,EAAE1B,KAAK,CAAC2F,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG;IAC9B,CAAC;IACD,OAAO,IAAI,CAACzB,EAAE,CAAC2B,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;EACEC,IAAIA,CAACV,MAAM,GAAG,EAAE,EAAE;IAChB,OAAO,IAAI,CAACI,GAAG,CAAC;MACdjE,CAAC,EAAE,GAAG;MACNC,CAAC,EAAE,GAAG;MACNC,CAAC,EAAE,GAAG;MACNC,CAAC,EAAE;IACL,CAAC,EAAE0D,MAAM,CAAC;EACZ;;EAEA;AACF;AACA;AACA;EACEW,KAAKA,CAACX,MAAM,GAAG,EAAE,EAAE;IACjB,OAAO,IAAI,CAACI,GAAG,CAAC;MACdjE,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC,EAAE0D,MAAM,CAAC;EACZ;EACAY,YAAYA,CAACC,UAAU,EAAE;IACvB,MAAMC,EAAE,GAAG,IAAI,CAAChC,EAAE,CAAC+B,UAAU,CAAC;IAC9B,MAAME,KAAK,GAAG,IAAI,CAACzE,CAAC,GAAGwE,EAAE,CAACxE,CAAC,IAAI,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;IAC1C,MAAMiE,IAAI,GAAGC,GAAG,IAAI;MAClB,OAAO5F,KAAK,CAAC,CAAC,IAAI,CAAC4F,GAAG,CAAC,GAAG,IAAI,CAAClE,CAAC,GAAGwE,EAAE,CAACN,GAAG,CAAC,GAAGM,EAAE,CAACxE,CAAC,IAAI,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC,IAAIyE,KAAK,CAAC;IAC5E,CAAC;IACD,OAAO,IAAI,CAACjC,EAAE,CAAC;MACb3C,CAAC,EAAEoE,IAAI,CAAC,GAAG,CAAC;MACZnE,CAAC,EAAEmE,IAAI,CAAC,GAAG,CAAC;MACZlE,CAAC,EAAEkE,IAAI,CAAC,GAAG,CAAC;MACZjE,CAAC,EAAEyE;IACL,CAAC,CAAC;EACJ;;EAEA;EACAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClB,aAAa,CAAC,CAAC,GAAG,GAAG;EACnC;EACAmB,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACnB,aAAa,CAAC,CAAC,IAAI,GAAG;EACpC;;EAEA;EACAoB,MAAMA,CAACC,KAAK,EAAE;IACZ,OAAO,IAAI,CAAChF,CAAC,KAAKgF,KAAK,CAAChF,CAAC,IAAI,IAAI,CAACC,CAAC,KAAK+E,KAAK,CAAC/E,CAAC,IAAI,IAAI,CAACC,CAAC,KAAK8E,KAAK,CAAC9E,CAAC,IAAI,IAAI,CAACC,CAAC,KAAK6E,KAAK,CAAC7E,CAAC;EAC7F;EACA8E,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAACtC,EAAE,CAAC,IAAI,CAAC;EACtB;;EAEA;EACAuC,WAAWA,CAAA,EAAG;IACZ,IAAIC,GAAG,GAAG,GAAG;IACb,MAAMC,IAAI,GAAG,CAAC,IAAI,CAACpF,CAAC,IAAI,CAAC,EAAE2B,QAAQ,CAAC,EAAE,CAAC;IACvCwD,GAAG,IAAIC,IAAI,CAACC,MAAM,KAAK,CAAC,GAAGD,IAAI,GAAG,GAAG,GAAGA,IAAI;IAC5C,MAAME,IAAI,GAAG,CAAC,IAAI,CAACrF,CAAC,IAAI,CAAC,EAAE0B,QAAQ,CAAC,EAAE,CAAC;IACvCwD,GAAG,IAAIG,IAAI,CAACD,MAAM,KAAK,CAAC,GAAGC,IAAI,GAAG,GAAG,GAAGA,IAAI;IAC5C,MAAMC,IAAI,GAAG,CAAC,IAAI,CAACrF,CAAC,IAAI,CAAC,EAAEyB,QAAQ,CAAC,EAAE,CAAC;IACvCwD,GAAG,IAAII,IAAI,CAACF,MAAM,KAAK,CAAC,GAAGE,IAAI,GAAG,GAAG,GAAGA,IAAI;IAC5C,IAAI,OAAO,IAAI,CAACpF,CAAC,KAAK,QAAQ,IAAI,IAAI,CAACA,CAAC,IAAI,CAAC,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,EAAE;MAC3D,MAAMqF,IAAI,GAAG/G,KAAK,CAAC,IAAI,CAAC0B,CAAC,GAAG,GAAG,CAAC,CAACwB,QAAQ,CAAC,EAAE,CAAC;MAC7CwD,GAAG,IAAIK,IAAI,CAACH,MAAM,KAAK,CAAC,GAAGG,IAAI,GAAG,GAAG,GAAGA,IAAI;IAC9C;IACA,OAAOL,GAAG;EACZ;;EAEA;EACAM,KAAKA,CAAA,EAAG;IACN,OAAO;MACL/C,CAAC,EAAE,IAAI,CAACU,MAAM,CAAC,CAAC;MAChBU,CAAC,EAAE,IAAI,CAACN,aAAa,CAAC,CAAC;MACvBO,CAAC,EAAE,IAAI,CAACN,YAAY,CAAC,CAAC;MACtBtD,CAAC,EAAE,IAAI,CAACA;IACV,CAAC;EACH;;EAEA;EACAuF,WAAWA,CAAA,EAAG;IACZ,MAAMhD,CAAC,GAAG,IAAI,CAACU,MAAM,CAAC,CAAC;IACvB,MAAMU,CAAC,GAAGrF,KAAK,CAAC,IAAI,CAAC+E,aAAa,CAAC,CAAC,GAAG,GAAG,CAAC;IAC3C,MAAMO,CAAC,GAAGtF,KAAK,CAAC,IAAI,CAACgF,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC;IAC1C,OAAO,IAAI,CAACtD,CAAC,KAAK,CAAC,GAAG,QAAQuC,CAAC,IAAIoB,CAAC,KAAKC,CAAC,KAAK,IAAI,CAAC5D,CAAC,GAAG,GAAG,OAAOuC,CAAC,IAAIoB,CAAC,KAAKC,CAAC,IAAI;EACpF;;EAEA;EACAtB,KAAKA,CAAA,EAAG;IACN,OAAO;MACLC,CAAC,EAAE,IAAI,CAACU,MAAM,CAAC,CAAC;MAChBU,CAAC,EAAE,IAAI,CAACN,aAAa,CAAC,CAAC;MACvBmC,CAAC,EAAE,IAAI,CAACjC,QAAQ,CAAC,CAAC;MAClBvD,CAAC,EAAE,IAAI,CAACA;IACV,CAAC;EACH;EACAyF,KAAKA,CAAA,EAAG;IACN,OAAO;MACL5F,CAAC,EAAE,IAAI,CAACA,CAAC;MACTC,CAAC,EAAE,IAAI,CAACA,CAAC;MACTC,CAAC,EAAE,IAAI,CAACA,CAAC;MACTC,CAAC,EAAE,IAAI,CAACA;IACV,CAAC;EACH;EACA0F,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC1F,CAAC,KAAK,CAAC,GAAG,QAAQ,IAAI,CAACH,CAAC,IAAI,IAAI,CAACC,CAAC,IAAI,IAAI,CAACC,CAAC,IAAI,IAAI,CAACC,CAAC,GAAG,GAAG,OAAO,IAAI,CAACH,CAAC,IAAI,IAAI,CAACC,CAAC,IAAI,IAAI,CAACC,CAAC,GAAG;EAC9G;EACAyB,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACkE,WAAW,CAAC,CAAC;EAC3B;;EAEA;EACA;EACA1D,GAAGA,CAAC2D,GAAG,EAAEnG,KAAK,EAAEC,GAAG,EAAE;IACnB,MAAMqF,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IAC1BA,KAAK,CAACa,GAAG,CAAC,GAAGpG,UAAU,CAACC,KAAK,EAAEC,GAAG,CAAC;IACnC,OAAOqF,KAAK;EACd;EACAtC,EAAEA,CAAC/B,KAAK,EAAE;IACR,OAAO,IAAI,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC;EACpC;EACA0C,MAAMA,CAAA,EAAG;IACP,IAAI,OAAO,IAAI,CAAC9C,IAAI,KAAK,WAAW,EAAE;MACpC,IAAI,CAACA,IAAI,GAAG9B,IAAI,CAACkB,GAAG,CAAC,IAAI,CAACI,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC9C;IACA,OAAO,IAAI,CAACM,IAAI;EAClB;EACA+C,MAAMA,CAAA,EAAG;IACP,IAAI,OAAO,IAAI,CAAC9C,IAAI,KAAK,WAAW,EAAE;MACpC,IAAI,CAACA,IAAI,GAAG/B,IAAI,CAACqH,GAAG,CAAC,IAAI,CAAC/F,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC9C;IACA,OAAO,IAAI,CAACO,IAAI;EAClB;EACAW,aAAaA,CAACN,OAAO,EAAE;IACrB,MAAMkF,aAAa,GAAGlF,OAAO,CAAC/B,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC9C,SAASkH,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;MAClC,OAAOzE,QAAQ,CAACsE,aAAa,CAACE,MAAM,CAAC,GAAGF,aAAa,CAACG,MAAM,IAAID,MAAM,CAAC,EAAE,EAAE,CAAC;IAC9E;IACA,IAAIF,aAAa,CAACX,MAAM,GAAG,CAAC,EAAE;MAC5B;MACA,IAAI,CAACrF,CAAC,GAAGiG,UAAU,CAAC,CAAC,CAAC;MACtB,IAAI,CAAChG,CAAC,GAAGgG,UAAU,CAAC,CAAC,CAAC;MACtB,IAAI,CAAC/F,CAAC,GAAG+F,UAAU,CAAC,CAAC,CAAC;MACtB,IAAI,CAAC9F,CAAC,GAAG6F,aAAa,CAAC,CAAC,CAAC,GAAGC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;IACrD,CAAC,MAAM;MACL;MACA,IAAI,CAACjG,CAAC,GAAGiG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;MACzB,IAAI,CAAChG,CAAC,GAAGgG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;MACzB,IAAI,CAAC/F,CAAC,GAAG+F,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;MACzB,IAAI,CAAC9F,CAAC,GAAG6F,aAAa,CAAC,CAAC,CAAC,GAAGC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;IACxD;EACF;EACApE,OAAOA,CAAC;IACNa,CAAC;IACDoB,CAAC;IACDC,CAAC;IACD5D;EACF,CAAC,EAAE;IACD,IAAI,CAACC,EAAE,GAAGsC,CAAC,GAAG,GAAG;IACjB,IAAI,CAACrC,EAAE,GAAGyD,CAAC;IACX,IAAI,CAACxD,EAAE,GAAGyD,CAAC;IACX,IAAI,CAAC5D,CAAC,GAAG,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAG,CAAC;IACtC,IAAI2D,CAAC,IAAI,CAAC,EAAE;MACV,MAAMgC,GAAG,GAAGrH,KAAK,CAACsF,CAAC,GAAG,GAAG,CAAC;MAC1B,IAAI,CAAC/D,CAAC,GAAG8F,GAAG;MACZ,IAAI,CAAC7F,CAAC,GAAG6F,GAAG;MACZ,IAAI,CAAC5F,CAAC,GAAG4F,GAAG;IACd;IACA,IAAI9F,CAAC,GAAG,CAAC;MACPC,CAAC,GAAG,CAAC;MACLC,CAAC,GAAG,CAAC;IACP,MAAMkG,QAAQ,GAAG1D,CAAC,GAAG,EAAE;IACvB,MAAM2D,MAAM,GAAG,CAAC,CAAC,GAAG3H,IAAI,CAAC4H,GAAG,CAAC,CAAC,GAAGvC,CAAC,GAAG,CAAC,CAAC,IAAID,CAAC;IAC5C,MAAMyC,eAAe,GAAGF,MAAM,IAAI,CAAC,GAAG3H,IAAI,CAAC4H,GAAG,CAACF,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACjE,IAAIA,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACjCpG,CAAC,GAAGqG,MAAM;MACVpG,CAAC,GAAGsG,eAAe;IACrB,CAAC,MAAM,IAAIH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACxCpG,CAAC,GAAGuG,eAAe;MACnBtG,CAAC,GAAGoG,MAAM;IACZ,CAAC,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACxCnG,CAAC,GAAGoG,MAAM;MACVnG,CAAC,GAAGqG,eAAe;IACrB,CAAC,MAAM,IAAIH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACxCnG,CAAC,GAAGsG,eAAe;MACnBrG,CAAC,GAAGmG,MAAM;IACZ,CAAC,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACxCpG,CAAC,GAAGuG,eAAe;MACnBrG,CAAC,GAAGmG,MAAM;IACZ,CAAC,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACxCpG,CAAC,GAAGqG,MAAM;MACVnG,CAAC,GAAGqG,eAAe;IACrB;IACA,MAAMC,qBAAqB,GAAGzC,CAAC,GAAGsC,MAAM,GAAG,CAAC;IAC5C,IAAI,CAACrG,CAAC,GAAGvB,KAAK,CAAC,CAACuB,CAAC,GAAGwG,qBAAqB,IAAI,GAAG,CAAC;IACjD,IAAI,CAACvG,CAAC,GAAGxB,KAAK,CAAC,CAACwB,CAAC,GAAGuG,qBAAqB,IAAI,GAAG,CAAC;IACjD,IAAI,CAACtG,CAAC,GAAGzB,KAAK,CAAC,CAACyB,CAAC,GAAGsG,qBAAqB,IAAI,GAAG,CAAC;EACnD;EACA1E,OAAOA,CAAC;IACNY,CAAC;IACDoB,CAAC;IACD6B,CAAC;IACDxF;EACF,CAAC,EAAE;IACD,IAAI,CAACC,EAAE,GAAGsC,CAAC,GAAG,GAAG;IACjB,IAAI,CAACrC,EAAE,GAAGyD,CAAC;IACX,IAAI,CAACvD,EAAE,GAAGoF,CAAC;IACX,IAAI,CAACxF,CAAC,GAAG,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAG,CAAC;IACtC,MAAMsG,EAAE,GAAGhI,KAAK,CAACkH,CAAC,GAAG,GAAG,CAAC;IACzB,IAAI,CAAC3F,CAAC,GAAGyG,EAAE;IACX,IAAI,CAACxG,CAAC,GAAGwG,EAAE;IACX,IAAI,CAACvG,CAAC,GAAGuG,EAAE;IACX,IAAI3C,CAAC,IAAI,CAAC,EAAE;MACV;IACF;IACA,MAAM4C,EAAE,GAAGhE,CAAC,GAAG,EAAE;IACjB,MAAMtD,CAAC,GAAGV,IAAI,CAACiI,KAAK,CAACD,EAAE,CAAC;IACxB,MAAME,EAAE,GAAGF,EAAE,GAAGtH,CAAC;IACjB,MAAM+E,CAAC,GAAG1F,KAAK,CAACkH,CAAC,IAAI,GAAG,GAAG7B,CAAC,CAAC,GAAG,GAAG,CAAC;IACpC,MAAM+C,CAAC,GAAGpI,KAAK,CAACkH,CAAC,IAAI,GAAG,GAAG7B,CAAC,GAAG8C,EAAE,CAAC,GAAG,GAAG,CAAC;IACzC,MAAME,CAAC,GAAGrI,KAAK,CAACkH,CAAC,IAAI,GAAG,GAAG7B,CAAC,IAAI,GAAG,GAAG8C,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;IACjD,QAAQxH,CAAC;MACP,KAAK,CAAC;QACJ,IAAI,CAACa,CAAC,GAAG6G,CAAC;QACV,IAAI,CAAC5G,CAAC,GAAGiE,CAAC;QACV;MACF,KAAK,CAAC;QACJ,IAAI,CAACnE,CAAC,GAAG6G,CAAC;QACV,IAAI,CAAC3G,CAAC,GAAGiE,CAAC;QACV;MACF,KAAK,CAAC;QACJ,IAAI,CAACnE,CAAC,GAAGmE,CAAC;QACV,IAAI,CAACjE,CAAC,GAAG4G,CAAC;QACV;MACF,KAAK,CAAC;QACJ,IAAI,CAAC9G,CAAC,GAAGmE,CAAC;QACV,IAAI,CAAClE,CAAC,GAAG4G,CAAC;QACV;MACF,KAAK,CAAC;QACJ,IAAI,CAAC7G,CAAC,GAAG8G,CAAC;QACV,IAAI,CAAC7G,CAAC,GAAGkE,CAAC;QACV;MACF,KAAK,CAAC;MACN;QACE,IAAI,CAAClE,CAAC,GAAGkE,CAAC;QACV,IAAI,CAACjE,CAAC,GAAG2G,CAAC;QACV;IACJ;EACF;EACAtF,aAAaA,CAACT,OAAO,EAAE;IACrB,MAAMiG,KAAK,GAAGpI,aAAa,CAACmC,OAAO,EAAExB,aAAa,CAAC;IACnD,IAAI,CAACwC,OAAO,CAAC;MACXY,CAAC,EAAEqE,KAAK,CAAC,CAAC,CAAC;MACXjD,CAAC,EAAEiD,KAAK,CAAC,CAAC,CAAC;MACXpB,CAAC,EAAEoB,KAAK,CAAC,CAAC,CAAC;MACX5G,CAAC,EAAE4G,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ;EACAzF,aAAaA,CAACR,OAAO,EAAE;IACrB,MAAMiG,KAAK,GAAGpI,aAAa,CAACmC,OAAO,EAAExB,aAAa,CAAC;IACnD,IAAI,CAACuC,OAAO,CAAC;MACXa,CAAC,EAAEqE,KAAK,CAAC,CAAC,CAAC;MACXjD,CAAC,EAAEiD,KAAK,CAAC,CAAC,CAAC;MACXhD,CAAC,EAAEgD,KAAK,CAAC,CAAC,CAAC;MACX5G,CAAC,EAAE4G,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ;EACA1F,aAAaA,CAACP,OAAO,EAAE;IACrB,MAAMiG,KAAK,GAAGpI,aAAa,CAACmC,OAAO,EAAE,CAACvB,GAAG,EAAEyH,GAAG;IAC9C;IACAA,GAAG,CAAC3H,QAAQ,CAAC,GAAG,CAAC,GAAGZ,KAAK,CAACc,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGA,GAAG,CAAC;IACjD,IAAI,CAACS,CAAC,GAAG+G,KAAK,CAAC,CAAC,CAAC;IACjB,IAAI,CAAC9G,CAAC,GAAG8G,KAAK,CAAC,CAAC,CAAC;IACjB,IAAI,CAAC7G,CAAC,GAAG6G,KAAK,CAAC,CAAC,CAAC;IACjB,IAAI,CAAC5G,CAAC,GAAG4G,KAAK,CAAC,CAAC,CAAC;EACnB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}