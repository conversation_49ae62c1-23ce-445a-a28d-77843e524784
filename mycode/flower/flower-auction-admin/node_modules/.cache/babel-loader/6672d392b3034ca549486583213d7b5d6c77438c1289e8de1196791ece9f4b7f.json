{"ast": null, "code": "import transpose from \"./transpose.js\";\nexport default function zip() {\n  return transpose(arguments);\n}", "map": {"version": 3, "names": ["transpose", "zip", "arguments"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/d3-array/src/zip.js"], "sourcesContent": ["import transpose from \"./transpose.js\";\n\nexport default function zip() {\n  return transpose(arguments);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B,OAAOD,SAAS,CAACE,SAAS,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}