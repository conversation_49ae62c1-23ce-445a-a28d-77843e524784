{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  console.error('API Error:', error);\n  return Promise.reject(error);\n});\n\n// 销售报表相关接口\nexport const salesReportAPI = {\n  // 获取销售报表\n  getSalesReport: params => api.get('/reports/sales', {\n    params\n  }),\n  // 获取销售趋势\n  getSalesTrend: params => api.get('/reports/sales/trend', {\n    params\n  }),\n  // 获取商品销售排行\n  getProductSalesRank: params => api.get('/reports/sales/products', {\n    params\n  }),\n  // 获取销售渠道分布\n  getSalesChannelDistribution: params => api.get('/reports/sales/channels', {\n    params\n  })\n};\n\n// 用户报表相关接口\nexport const userReportAPI = {\n  // 获取用户报表\n  getUserReport: params => api.get('/reports/users', {\n    params\n  }),\n  // 获取用户增长趋势\n  getUserGrowthTrend: params => api.get('/reports/users/growth', {\n    params\n  }),\n  // 获取用户分布\n  getUserDistribution: () => api.get('/reports/users/distribution'),\n  // 获取用户活跃度排行\n  getUserActivityRank: params => api.get('/reports/users/activity', {\n    params\n  })\n};\n\n// 商品报表相关接口\nexport const productReportAPI = {\n  // 获取商品报表\n  getProductReport: params => api.get('/reports/products', {\n    params\n  }),\n  // 获取分类销售数据\n  getCategorySales: params => api.get('/reports/products/categories', {\n    params\n  }),\n  // 获取商品性能数据\n  getProductPerformance: params => api.get('/reports/products/performance', {\n    params\n  }),\n  // 获取价格分布\n  getPriceDistribution: () => api.get('/reports/products/price-distribution')\n};\n\n// 拍卖报表相关接口\nexport const auctionReportAPI = {\n  // 获取拍卖报表\n  getAuctionReport: params => api.get('/reports/auctions', {\n    params\n  }),\n  // 获取拍卖趋势\n  getAuctionTrend: params => api.get('/reports/auctions/trend', {\n    params\n  }),\n  // 获取拍卖性能数据\n  getAuctionPerformance: params => api.get('/reports/auctions/performance', {\n    params\n  }),\n  // 获取拍卖状态分布\n  getAuctionStatusDistribution: () => api.get('/reports/auctions/status')\n};\n\n// 导出报表\nexport const exportReportAPI = {\n  exportReport: (type, params) => {\n    const url = `/reports/export/${type}`;\n    return api.get(url, {\n      params,\n      responseType: 'blob' // 用于文件下载\n    });\n  }\n};\nconst reportService = {\n  salesReportAPI,\n  userReportAPI,\n  productReportAPI,\n  auctionReportAPI,\n  exportReportAPI\n};\nexport default reportService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "api", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "data", "console", "salesReportAPI", "getSalesReport", "params", "get", "getSalesTrend", "getProductSalesRank", "getSalesChannelDistribution", "userReportAPI", "getUserReport", "getUserGrowthTrend", "getUserDistribution", "getUserActivityRank", "productReportAPI", "getProductReport", "getCategorySales", "getProductPerformance", "getPriceDistribution", "auctionReportAPI", "getAuctionReport", "getAuctionTrend", "getAuctionPerformance", "getAuctionStatusDistribution", "exportReportAPI", "exportReport", "type", "url", "responseType", "reportService"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/reportService.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    console.error('API Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// 销售报表相关接口\nexport const salesReportAPI = {\n  // 获取销售报表\n  getSalesReport: (params: {\n    start_date: string;\n    end_date: string;\n    granularity?: string;\n  }) => api.get('/reports/sales', { params }),\n\n  // 获取销售趋势\n  getSalesTrend: (params: {\n    start_date: string;\n    end_date: string;\n    granularity?: string;\n  }) => api.get('/reports/sales/trend', { params }),\n\n  // 获取商品销售排行\n  getProductSalesRank: (params: {\n    start_date: string;\n    end_date: string;\n    limit?: number;\n  }) => api.get('/reports/sales/products', { params }),\n\n  // 获取销售渠道分布\n  getSalesChannelDistribution: (params: {\n    start_date: string;\n    end_date: string;\n  }) => api.get('/reports/sales/channels', { params }),\n};\n\n// 用户报表相关接口\nexport const userReportAPI = {\n  // 获取用户报表\n  getUserReport: (params: {\n    start_date: string;\n    end_date: string;\n    user_type?: string;\n  }) => api.get('/reports/users', { params }),\n\n  // 获取用户增长趋势\n  getUserGrowthTrend: (params: {\n    start_date: string;\n    end_date: string;\n  }) => api.get('/reports/users/growth', { params }),\n\n  // 获取用户分布\n  getUserDistribution: () => api.get('/reports/users/distribution'),\n\n  // 获取用户活跃度排行\n  getUserActivityRank: (params: {\n    user_type?: string;\n    limit?: number;\n  }) => api.get('/reports/users/activity', { params }),\n};\n\n// 商品报表相关接口\nexport const productReportAPI = {\n  // 获取商品报表\n  getProductReport: (params: {\n    start_date: string;\n    end_date: string;\n    category?: string;\n  }) => api.get('/reports/products', { params }),\n\n  // 获取分类销售数据\n  getCategorySales: (params: {\n    start_date: string;\n    end_date: string;\n  }) => api.get('/reports/products/categories', { params }),\n\n  // 获取商品性能数据\n  getProductPerformance: (params: {\n    start_date: string;\n    end_date: string;\n    limit?: number;\n  }) => api.get('/reports/products/performance', { params }),\n\n  // 获取价格分布\n  getPriceDistribution: () => api.get('/reports/products/price-distribution'),\n};\n\n// 拍卖报表相关接口\nexport const auctionReportAPI = {\n  // 获取拍卖报表\n  getAuctionReport: (params: {\n    start_date: string;\n    end_date: string;\n    auction_type?: string;\n  }) => api.get('/reports/auctions', { params }),\n\n  // 获取拍卖趋势\n  getAuctionTrend: (params: {\n    start_date: string;\n    end_date: string;\n  }) => api.get('/reports/auctions/trend', { params }),\n\n  // 获取拍卖性能数据\n  getAuctionPerformance: (params: {\n    start_date: string;\n    end_date: string;\n    limit?: number;\n  }) => api.get('/reports/auctions/performance', { params }),\n\n  // 获取拍卖状态分布\n  getAuctionStatusDistribution: () => api.get('/reports/auctions/status'),\n};\n\n// 导出报表\nexport const exportReportAPI = {\n  exportReport: (type: string, params: {\n    start_date: string;\n    end_date: string;\n    format?: string;\n  }) => {\n    const url = `/reports/export/${type}`;\n    return api.get(url, { \n      params,\n      responseType: 'blob' // 用于文件下载\n    });\n  },\n};\n\nconst reportService = {\n  salesReportAPI,\n  userReportAPI,\n  productReportAPI,\n  auctionReportAPI,\n  exportReportAPI,\n};\n\nexport default reportService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,8BAA8B;;AAEzF;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACI,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EACTK,OAAO,CAACL,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EAClC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMM,cAAc,GAAG;EAC5B;EACAC,cAAc,EAAGC,MAIhB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,gBAAgB,EAAE;IAAED;EAAO,CAAC,CAAC;EAE3C;EACAE,aAAa,EAAGF,MAIf,IAAKrB,GAAG,CAACsB,GAAG,CAAC,sBAAsB,EAAE;IAAED;EAAO,CAAC,CAAC;EAEjD;EACAG,mBAAmB,EAAGH,MAIrB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,yBAAyB,EAAE;IAAED;EAAO,CAAC,CAAC;EAEpD;EACAI,2BAA2B,EAAGJ,MAG7B,IAAKrB,GAAG,CAACsB,GAAG,CAAC,yBAAyB,EAAE;IAAED;EAAO,CAAC;AACrD,CAAC;;AAED;AACA,OAAO,MAAMK,aAAa,GAAG;EAC3B;EACAC,aAAa,EAAGN,MAIf,IAAKrB,GAAG,CAACsB,GAAG,CAAC,gBAAgB,EAAE;IAAED;EAAO,CAAC,CAAC;EAE3C;EACAO,kBAAkB,EAAGP,MAGpB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,uBAAuB,EAAE;IAAED;EAAO,CAAC,CAAC;EAElD;EACAQ,mBAAmB,EAAEA,CAAA,KAAM7B,GAAG,CAACsB,GAAG,CAAC,6BAA6B,CAAC;EAEjE;EACAQ,mBAAmB,EAAGT,MAGrB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,yBAAyB,EAAE;IAAED;EAAO,CAAC;AACrD,CAAC;;AAED;AACA,OAAO,MAAMU,gBAAgB,GAAG;EAC9B;EACAC,gBAAgB,EAAGX,MAIlB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,mBAAmB,EAAE;IAAED;EAAO,CAAC,CAAC;EAE9C;EACAY,gBAAgB,EAAGZ,MAGlB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,8BAA8B,EAAE;IAAED;EAAO,CAAC,CAAC;EAEzD;EACAa,qBAAqB,EAAGb,MAIvB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,+BAA+B,EAAE;IAAED;EAAO,CAAC,CAAC;EAE1D;EACAc,oBAAoB,EAAEA,CAAA,KAAMnC,GAAG,CAACsB,GAAG,CAAC,sCAAsC;AAC5E,CAAC;;AAED;AACA,OAAO,MAAMc,gBAAgB,GAAG;EAC9B;EACAC,gBAAgB,EAAGhB,MAIlB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,mBAAmB,EAAE;IAAED;EAAO,CAAC,CAAC;EAE9C;EACAiB,eAAe,EAAGjB,MAGjB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,yBAAyB,EAAE;IAAED;EAAO,CAAC,CAAC;EAEpD;EACAkB,qBAAqB,EAAGlB,MAIvB,IAAKrB,GAAG,CAACsB,GAAG,CAAC,+BAA+B,EAAE;IAAED;EAAO,CAAC,CAAC;EAE1D;EACAmB,4BAA4B,EAAEA,CAAA,KAAMxC,GAAG,CAACsB,GAAG,CAAC,0BAA0B;AACxE,CAAC;;AAED;AACA,OAAO,MAAMmB,eAAe,GAAG;EAC7BC,YAAY,EAAEA,CAACC,IAAY,EAAEtB,MAI5B,KAAK;IACJ,MAAMuB,GAAG,GAAG,mBAAmBD,IAAI,EAAE;IACrC,OAAO3C,GAAG,CAACsB,GAAG,CAACsB,GAAG,EAAE;MAClBvB,MAAM;MACNwB,YAAY,EAAE,MAAM,CAAC;IACvB,CAAC,CAAC;EACJ;AACF,CAAC;AAED,MAAMC,aAAa,GAAG;EACpB3B,cAAc;EACdO,aAAa;EACbK,gBAAgB;EAChBK,gBAAgB;EAChBK;AACF,CAAC;AAED,eAAeK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}