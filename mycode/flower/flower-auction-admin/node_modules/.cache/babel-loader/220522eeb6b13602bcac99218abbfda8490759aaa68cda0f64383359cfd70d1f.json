{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Input,Select,Tag,Modal,Form,message,Typography,Row,Col,InputNumber,Image,Popconfirm,Statistic,Badge,Tooltip,DatePicker}from'antd';import{PlusOutlined,SearchOutlined,EditOutlined,DeleteOutlined,EyeOutlined,ReloadOutlined,AuditOutlined}from'@ant-design/icons';// import type { ColumnsType } from 'antd/es/table';\nimport{auctionService}from'../../../services/auctionService';import{productService}from'../../../services/productService';import FormMessage from'../../../components/FormMessage';import{useFormMessage,handleApiResponse,handleApiError}from'../../../hooks/useFormMessage';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const{Option}=Select;// 拍卖商品状态枚举\nexport let AuctionItemStatus=/*#__PURE__*/function(AuctionItemStatus){AuctionItemStatus[AuctionItemStatus[\"PENDING\"]=0]=\"PENDING\";// 待拍卖\nAuctionItemStatus[AuctionItemStatus[\"ONGOING\"]=1]=\"ONGOING\";// 拍卖中\nAuctionItemStatus[AuctionItemStatus[\"SOLD\"]=2]=\"SOLD\";// 已成交\nAuctionItemStatus[AuctionItemStatus[\"UNSOLD\"]=3]=\"UNSOLD\";// 流拍\nAuctionItemStatus[AuctionItemStatus[\"WITHDRAWN\"]=4]=\"WITHDRAWN\";// 撤回\nreturn AuctionItemStatus;}({});// 拍卖商品接口\n// 查询参数接口\nconst AuctionItems=()=>{const[auctionItems,setAuctionItems]=useState([]);const[products,setProducts]=useState([]);const[auctions,setAuctions]=useState([]);const[loading,setLoading]=useState(false);const[total,setTotal]=useState(0);const[queryParams,setQueryParams]=useState({page:1,pageSize:10});const[isModalVisible,setIsModalVisible]=useState(false);const[editingItem,setEditingItem]=useState(null);const[saving,setSaving]=useState(false);const[statistics,setStatistics]=useState({totalItems:0,pendingItems:0,ongoingItems:0,soldItems:0,totalValue:0});const[form]=Form.useForm();const{formError,formSuccess,setFormError,setFormSuccess,clearAllMessages}=useFormMessage();// 拍卖商品状态映射\nconst itemStatusMap={[AuctionItemStatus.PENDING]:{label:'待拍卖',color:'default'},[AuctionItemStatus.ONGOING]:{label:'拍卖中',color:'blue'},[AuctionItemStatus.SOLD]:{label:'已成交',color:'green'},[AuctionItemStatus.UNSOLD]:{label:'流拍',color:'orange'},[AuctionItemStatus.WITHDRAWN]:{label:'撤回',color:'red'}};// 获取拍卖商品列表\nconst fetchAuctionItems=async()=>{setLoading(true);try{// 调用后端API获取拍卖商品列表\nconst apiParams=_objectSpread(_objectSpread({},queryParams),{},{// 确保状态参数是数字类型\nstatus:typeof queryParams.status==='number'?queryParams.status:undefined});console.log('请求参数:',apiParams);console.log('请求URL:',\"/auction-items\");const response=await auctionService.getAuctionItemList(apiParams);console.log('API响应完整数据:',JSON.stringify(response));if(response.success&&response.data){// 检查数据结构\nconsole.log('响应数据结构:',Object.keys(response.data));// 确保list字段存在且是数组\nconst itemsList=response.data.list||[];if(!Array.isArray(itemsList)){console.error('列表数据不是数组:',itemsList);setAuctionItems([]);setTotal(0);return;}console.log('原始拍卖商品数据:',itemsList);// 处理拍卖商品数据，映射字段\nconst mappedItems=itemsList.map(item=>{console.log('处理项目:',item);// 从product中获取商品信息\nconst product=item.product||{};console.log('商品信息:',product);// 创建映射后的对象\nconst mappedItem={id:item.id||0,auctionId:item.auctionId||0,productId:item.productId||0,productName:product.name||item.productName||'未知商品',productCode:product.code||item.productCode||\"\\u5546\\u54C1\".concat(item.productId||0),quantity:product.quantity||item.quantity||1,unit:product.unit||item.unit||'件',images:product.images||item.images||[],startingPrice:item.startPrice||item.startingPrice||0,currentPrice:item.currentPrice||item.startPrice||item.startingPrice||0,reservePrice:item.reservePrice||0,bidIncrement:item.stepPrice||item.bidIncrement||10,bidCount:item.totalBids||item.bidCount||0,highestBidder:item.winnerName||item.highestBidder||'',status:typeof item.status==='number'?item.status:item.status==='PENDING'?AuctionItemStatus.PENDING:item.status==='ONGOING'?AuctionItemStatus.ONGOING:item.status==='SOLD'?AuctionItemStatus.SOLD:item.status==='UNSOLD'?AuctionItemStatus.UNSOLD:item.status==='WITHDRAWN'?AuctionItemStatus.WITHDRAWN:AuctionItemStatus.PENDING,createdAt:item.createdAt||'',updatedAt:item.updatedAt||'',category:product.category||item.category||'',quality:product.qualityLevel||item.quality||''};console.log('映射后的项目:',mappedItem);return mappedItem;});console.log('最终映射后的数据:',mappedItems);setAuctionItems(mappedItems);setTotal(response.data.total||0);// 更新统计信息\nconst pendingItems=mappedItems.filter(item=>item.status===AuctionItemStatus.PENDING).length;const ongoingItems=mappedItems.filter(item=>item.status===AuctionItemStatus.ONGOING).length;const soldItems=mappedItems.filter(item=>item.status===AuctionItemStatus.SOLD).length;const totalValue=mappedItems.reduce((sum,item)=>sum+item.currentPrice,0);setStatistics({totalItems:mappedItems.length,pendingItems,ongoingItems,soldItems,totalValue});}else{console.warn('拍卖商品数据格式异常:',response);setAuctionItems([]);setTotal(0);}}catch(error){console.error('获取拍卖商品列表失败:',error);message.error(error.message||'获取拍卖商品列表失败');setAuctionItems([]);setTotal(0);}finally{setLoading(false);}};// 获取商品列表（用于添加拍卖商品）\nconst fetchProducts=async()=>{try{const response=await productService.getProductList({page:1,pageSize:100,auditStatus:'approved'// 只获取已审核的商品\n});if(response.success&&response.data&&response.data.list){setProducts(response.data.list);console.log('获取到商品列表:',response.data.list);}else{console.warn('商品数据格式异常:',response);setProducts([]);}}catch(error){console.error('获取商品列表失败:',error);setProducts([]);}};// 获取拍卖会列表\nconst fetchAuctions=async()=>{try{const response=await auctionService.getAuctionList({page:1,pageSize:100});if(response.success&&response.data&&response.data.list){// 将后端的name字段映射为前端的title字段\nconst mappedAuctions=response.data.list.map(auction=>_objectSpread(_objectSpread({},auction),{},{title:auction.name||auction.title// 后端返回name，前端使用title\n}));setAuctions(mappedAuctions);console.log('获取到拍卖会列表:',mappedAuctions);}else{console.warn('拍卖会数据格式异常:',response);setAuctions([]);}}catch(error){console.error('获取拍卖会列表失败:',error);setAuctions([]);}};// 获取统计信息\nconst fetchStatistics=async()=>{try{// 这里需要调用后端API获取统计信息\n// 暂时使用模拟数据\nsetStatistics({totalItems:0,pendingItems:0,ongoingItems:0,soldItems:0,totalValue:0});}catch(error){console.error('获取统计信息失败:',error);}};// 检查API连接\nconst checkApiConnection=async()=>{try{message.loading('正在检查API连接...');// 尝试请求API基础URL\nconst baseUrl=process.env.REACT_APP_API_BASE_URL;// 使用fetch直接请求，避免axios拦截器的影响\nconst response=await fetch(\"\".concat(baseUrl,\"/auction-items?page=1&pageSize=1\"));const data=await response.json();console.log('API连接测试结果:',data);if(response.ok){message.success(\"API\\u8FDE\\u63A5\\u6B63\\u5E38\\uFF0C\\u54CD\\u5E94\\u72B6\\u6001: \".concat(response.status));}else{message.error(\"API\\u8FDE\\u63A5\\u5F02\\u5E38\\uFF0C\\u54CD\\u5E94\\u72B6\\u6001: \".concat(response.status));}// 显示响应数据结构\nif(data){console.log('响应数据结构:',Object.keys(data));message.info(\"\\u54CD\\u5E94\\u6570\\u636E\\u7ED3\\u6784: \".concat(JSON.stringify(Object.keys(data))));}}catch(error){console.error('API连接测试失败:',error);message.error(\"API\\u8FDE\\u63A5\\u6D4B\\u8BD5\\u5931\\u8D25: \".concat(error.message));}};// 初始化加载\nuseEffect(()=>{// 修改初始化请求，减少重复请求\nconst initData=async()=>{setLoading(true);try{console.log('开始初始化数据加载');// 并行请求数据，减少请求次数\nconst[itemsResponse,auctionsResponse,productsResponse]=await Promise.all([auctionService.getAuctionItemList(_objectSpread(_objectSpread({},queryParams),{},{// 枚举现在已经是数字类型，可以直接使用\nstatus:queryParams.status,pageSize:50// 使用更大的页面大小，减少请求次数\n})),auctionService.getAuctionList({page:1,pageSize:50// 使用更大的页面大小，减少请求次数\n}),productService.getProductList({page:1,pageSize:50,// 使用更大的页面大小，减少请求次数\nauditStatus:'approved'})]);console.log('初始化数据请求完成');console.log('拍卖商品响应:',JSON.stringify(itemsResponse));console.log('拍卖会响应:',JSON.stringify(auctionsResponse));console.log('商品响应:',JSON.stringify(productsResponse));// 处理拍卖会数据\nif(auctionsResponse.success&&auctionsResponse.data&&auctionsResponse.data.list){const mappedAuctions=auctionsResponse.data.list.map(auction=>_objectSpread(_objectSpread({},auction),{},{title:auction.name||auction.title}));setAuctions(mappedAuctions);console.log('处理后的拍卖会数据:',mappedAuctions);}// 处理商品数据\nif(productsResponse.success&&productsResponse.data&&productsResponse.data.list){setProducts(productsResponse.data.list);console.log('处理后的商品数据:',productsResponse.data.list);}// 处理拍卖商品数据\nif(itemsResponse.success&&itemsResponse.data){console.log('拍卖商品数据结构:',Object.keys(itemsResponse.data));// 确保list字段存在且是数组\nconst itemsList=itemsResponse.data.list||[];if(!Array.isArray(itemsList)){console.error('列表数据不是数组:',itemsList);setAuctionItems([]);setTotal(0);return;}console.log('拍卖商品原始数据:',itemsList);const mappedItems=itemsList.map(item=>{// 从product中获取商品信息\nconst product=item.product||{};console.log('处理商品项:',item.id,'商品信息:',product);// 状态字段现在是数字，可以直接使用\nconst status=typeof item.status==='number'?item.status:item.status==='PENDING'?AuctionItemStatus.PENDING:item.status==='ONGOING'?AuctionItemStatus.ONGOING:item.status==='SOLD'?AuctionItemStatus.SOLD:item.status==='UNSOLD'?AuctionItemStatus.UNSOLD:item.status==='WITHDRAWN'?AuctionItemStatus.WITHDRAWN:AuctionItemStatus.PENDING;return{id:item.id,auctionId:item.auctionId,productId:item.productId,productName:product.name||item.productName||'未知商品',productCode:product.code||item.productCode||\"\\u5546\\u54C1\".concat(item.productId),quantity:product.quantity||item.quantity||1,unit:product.unit||item.unit||'件',images:product.images||item.images||[],// 价格字段映射\nstartingPrice:item.startPrice||item.startingPrice||0,currentPrice:item.currentPrice||item.startPrice||item.startingPrice||0,reservePrice:item.reservePrice||0,bidIncrement:item.stepPrice||item.bidIncrement||10,bidCount:item.totalBids||item.bidCount||0,highestBidder:item.winnerName||item.highestBidder||'',status:status,createdAt:item.createdAt,updatedAt:item.updatedAt,category:product.category||item.category||'',quality:product.qualityLevel||item.quality||''};});console.log('映射后的拍卖商品数据:',mappedItems);setAuctionItems(mappedItems);setTotal(itemsResponse.data.total||0);// 更新统计信息\nconst pendingItems=mappedItems.filter(item=>item.status===AuctionItemStatus.PENDING).length;const ongoingItems=mappedItems.filter(item=>item.status===AuctionItemStatus.ONGOING).length;const soldItems=mappedItems.filter(item=>item.status===AuctionItemStatus.SOLD).length;const totalValue=mappedItems.reduce((sum,item)=>sum+item.currentPrice,0);setStatistics({totalItems:mappedItems.length,pendingItems,ongoingItems,soldItems,totalValue});}else{console.warn('拍卖商品数据格式异常:',itemsResponse);setAuctionItems([]);setTotal(0);}}catch(error){console.error('初始化数据失败:',error);message.error('获取数据失败，请刷新页面重试');}finally{setLoading(false);}};// 首次加载或重置查询参数时，使用并行请求\nif(queryParams.page===1&&!queryParams.auctionId&&!queryParams.productName&&!queryParams.status){initData();}else{// 搜索或翻页时，只请求拍卖商品列表\nfetchAuctionItems();}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[queryParams]);// 搜索处理\nconst handleSearch=values=>{setQueryParams(_objectSpread(_objectSpread(_objectSpread({},queryParams),values),{},{page:1}));};// 重置搜索\nconst handleReset=()=>{setQueryParams({page:1,pageSize:10});};// 新增拍卖商品\nconst handleAdd=()=>{setEditingItem(null);form.resetFields();clearAllMessages();setIsModalVisible(true);};// 编辑拍卖商品\nconst handleEdit=item=>{setEditingItem(item);form.setFieldsValue(item);clearAllMessages();setIsModalVisible(true);};// 删除拍卖商品\nconst handleDelete=async id=>{try{// 调用删除API\nmessage.success('删除成功');fetchAuctionItems();}catch(error){message.error(error.message||'删除失败');}};// 保存拍卖商品\nconst handleSave=async values=>{setSaving(true);clearAllMessages();try{let response;if(editingItem){// 更新拍卖商品 - 目前暂不支持更新，显示提示信息\nsetFormError('暂不支持更新拍卖商品，请删除后重新添加');setSaving(false);return;}else{// 添加拍卖商品\nresponse=await auctionService.addAuctionItem(values.auctionId,{productId:values.productId,startingPrice:values.startingPrice,reservePrice:values.reservePrice,bidIncrement:values.bidIncrement,startTime:values.startTime?values.startTime.toISOString():undefined});}const successMsg='拍卖商品添加成功！';if(handleApiResponse(response,setFormError,setFormSuccess,successMsg)){// 成功：延迟关闭模态框\nsetTimeout(()=>{setIsModalVisible(false);form.resetFields();setEditingItem(null);clearAllMessages();fetchAuctionItems();fetchStatistics();},1500);}}catch(error){handleApiError(error,setFormError);}finally{setSaving(false);}};return/*#__PURE__*/_jsxs(\"div\",{style:{padding:24},children:[/*#__PURE__*/_jsx(Title,{level:2,children:\"\\u62CD\\u5356\\u5546\\u54C1\\u7BA1\\u7406\"}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u5546\\u54C1\\u6570\",value:statistics.totalItems,prefix:/*#__PURE__*/_jsx(AuditOutlined,{}),valueStyle:{color:'#3f8600'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F85\\u62CD\\u5356\",value:statistics.pendingItems,valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u62CD\\u5356\\u4E2D\",value:statistics.ongoingItems,valueStyle:{color:'#722ed1'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u6210\\u4EA4\",value:statistics.soldItems,valueStyle:{color:'#52c41a'}})})})]}),/*#__PURE__*/_jsx(Card,{className:\"search-card\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsx(Form,{layout:\"inline\",onFinish:handleSearch,autoComplete:\"off\",children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{width:'100%'},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Form.Item,{name:\"productName\",label:\"\\u5546\\u54C1\\u540D\\u79F0\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",allowClear:true})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Form.Item,{name:\"status\",label:\"\\u62CD\\u5356\\u72B6\\u6001\",children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u72B6\\u6001\",allowClear:true,children:[/*#__PURE__*/_jsx(Option,{value:AuctionItemStatus.PENDING,children:\"\\u5F85\\u62CD\\u5356\"}),/*#__PURE__*/_jsx(Option,{value:AuctionItemStatus.ONGOING,children:\"\\u62CD\\u5356\\u4E2D\"}),/*#__PURE__*/_jsx(Option,{value:AuctionItemStatus.SOLD,children:\"\\u5DF2\\u6210\\u4EA4\"}),/*#__PURE__*/_jsx(Option,{value:AuctionItemStatus.UNSOLD,children:\"\\u6D41\\u62CD\"}),/*#__PURE__*/_jsx(Option,{value:AuctionItemStatus.WITHDRAWN,children:\"\\u64A4\\u56DE\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Form.Item,{name:\"auctionId\",label:\"\\u62CD\\u5356\\u4F1A\",children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u4F1A\",allowClear:true,children:auctions.map(auction=>/*#__PURE__*/_jsx(Option,{value:auction.id,children:auction.title},auction.id))})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",icon:/*#__PURE__*/_jsx(SearchOutlined,{}),children:\"\\u641C\\u7D22\"}),/*#__PURE__*/_jsx(Button,{onClick:handleReset,icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),children:\"\\u91CD\\u7F6E\"})]})})})]})})}),/*#__PURE__*/_jsx(Card,{className:\"action-card\",size:\"small\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Row,{justify:\"space-between\",align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleAdd,children:\"\\u6DFB\\u52A0\\u62CD\\u5356\\u5546\\u54C1\"})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:fetchAuctionItems,loading:loading,children:\"\\u5237\\u65B0\"})})]})}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u5F53\\u524D\\u6570\\u636E\\u72B6\\u6001: \",loading?'加载中...':\"\\u5171 \".concat(auctionItems.length,\" \\u6761\\u8BB0\\u5F55\")]}),auctionItems.length===0&&!loading&&/*#__PURE__*/_jsx(\"p\",{style:{color:'red'},children:\"\\u672A\\u627E\\u5230\\u62CD\\u5356\\u5546\\u54C1\\u6570\\u636E\\uFF0C\\u8BF7\\u68C0\\u67E5\\u6570\\u636E\\u6620\\u5C04\\u6216\\u7F51\\u7EDC\\u8BF7\\u6C42\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>{console.log('当前拍卖商品数据:',auctionItems);message.info(\"\\u5F53\\u524D\\u6570\\u636E\\u6761\\u6570: \".concat(auctionItems.length));},style:{marginRight:8},children:\"\\u8C03\\u8BD5\\u6570\\u636E\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:checkApiConnection,style:{marginRight:8},children:\"\\u6D4B\\u8BD5API\\u8FDE\\u63A5\"}),/*#__PURE__*/_jsx(Button,{onClick:fetchAuctionItems,style:{marginRight:8},children:\"\\u91CD\\u65B0\\u83B7\\u53D6\\u6570\\u636E\"})]}),/*#__PURE__*/_jsx(Table,{columns:[{title:'ID',dataIndex:'id',key:'id',width:80},{title:'商品信息',key:'product',width:250,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[record.images&&record.images.length>0&&/*#__PURE__*/_jsx(Image,{width:60,height:60,src:record.images[0],style:{marginRight:12,borderRadius:4},fallback:\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:500},children:record.productName}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:12,color:'#999'},children:[\"\\u7F16\\u53F7: \",record.productCode]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:12,color:'#999'},children:[record.quantity,\" \",record.unit]})]})]})},{title:'拍卖状态',dataIndex:'status',key:'status',width:100,render:status=>{console.log('渲染状态:',status,typeof status);// 确保状态是数字类型\nconst numStatus=typeof status==='number'?status:status==='PENDING'?AuctionItemStatus.PENDING:status==='ONGOING'?AuctionItemStatus.ONGOING:status==='SOLD'?AuctionItemStatus.SOLD:status==='UNSOLD'?AuctionItemStatus.UNSOLD:status==='WITHDRAWN'?AuctionItemStatus.WITHDRAWN:AuctionItemStatus.PENDING;const statusInfo=itemStatusMap[numStatus];console.log('状态信息:',statusInfo);return/*#__PURE__*/_jsx(Badge,{status:numStatus===AuctionItemStatus.ONGOING?'processing':numStatus===AuctionItemStatus.SOLD?'success':numStatus===AuctionItemStatus.UNSOLD?'warning':numStatus===AuctionItemStatus.WITHDRAWN?'error':'default',text:/*#__PURE__*/_jsx(Tag,{color:(statusInfo===null||statusInfo===void 0?void 0:statusInfo.color)||'default',children:(statusInfo===null||statusInfo===void 0?void 0:statusInfo.label)||\"\\u672A\\u77E5(\".concat(numStatus,\")\")})});}},{title:'价格信息',key:'price',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u8D77\\u62CD: \\xA5\",record.startingPrice.toFixed(2)]}),/*#__PURE__*/_jsxs(\"div\",{style:{color:'#f50',fontWeight:500},children:[\"\\u5F53\\u524D: \\xA5\",record.currentPrice.toFixed(2)]}),record.reservePrice&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:12,color:'#999'},children:[\"\\u4FDD\\u7559: \\xA5\",record.reservePrice.toFixed(2)]})]})},{title:'竞价信息',key:'bid',width:120,render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u51FA\\u4EF7\\u6B21\\u6570: \",record.bidCount]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u52A0\\u4EF7\\u5E45\\u5EA6: \\xA5\",record.bidIncrement.toFixed(2)]}),record.highestBidder&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:12,color:'#999'},children:[\"\\u6700\\u9AD8\\u51FA\\u4EF7\\u4EBA: \",record.highestBidder]})]})},{title:'创建时间',dataIndex:'createdAt',key:'createdAt',width:160,render:text=>new Date(text).toLocaleString()},{title:'操作',key:'action',width:200,fixed:'right',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u67E5\\u770B\\u8BE6\\u60C5\",children:/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>{/* 查看详情 */}})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u7F16\\u8F91\",children:/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record),disabled:record.status===AuctionItemStatus.ONGOING})}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u62CD\\u5356\\u5546\\u54C1\\u5417\\uFF1F\",onConfirm:()=>handleDelete(record.id),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u5220\\u9664\",children:/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),disabled:record.status===AuctionItemStatus.ONGOING})})})]})}],dataSource:auctionItems,rowKey:\"id\",loading:loading,scroll:{x:1200},pagination:{current:queryParams.page,pageSize:queryParams.pageSize,total:total,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\\u7B2C \".concat(range[0],\"-\").concat(range[1],\" \\u6761/\\u5171 \").concat(total,\" \\u6761\"),onChange:(page,pageSize)=>{setQueryParams(_objectSpread(_objectSpread({},queryParams),{},{page,pageSize:pageSize||10}));}}})]}),/*#__PURE__*/_jsx(Modal,{title:editingItem?'编辑拍卖商品':'添加拍卖商品',open:isModalVisible,onCancel:()=>setIsModalVisible(false),footer:null,width:600,children:/*#__PURE__*/_jsxs(Form,{form:form,layout:\"vertical\",onFinish:handleSave,autoComplete:\"off\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"auctionId\",label:\"\\u62CD\\u5356\\u4F1A\",rules:[{required:true,message:'请选择拍卖会'}],children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u4F1A\",children:auctions.map(auction=>/*#__PURE__*/_jsx(Option,{value:auction.id,children:auction.title},auction.id))})}),/*#__PURE__*/_jsx(Form.Item,{name:\"productId\",label:\"\\u5546\\u54C1\",rules:[{required:true,message:'请选择商品'}],children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\",showSearch:true,optionFilterProp:\"children\",children:products.map(product=>/*#__PURE__*/_jsxs(Option,{value:product.id,children:[product.name,\" - \",product.code]},product.id))})}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"startingPrice\",label:\"\\u8D77\\u62CD\\u4EF7\",rules:[{required:true,message:'请输入起拍价'},{type:'number',min:0.01,message:'起拍价必须大于0'}],children:/*#__PURE__*/_jsx(InputNumber,{style:{width:'100%'},placeholder:\"\\u8BF7\\u8F93\\u5165\\u8D77\\u62CD\\u4EF7\",precision:2,min:0.01,addonBefore:\"\\xA5\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"reservePrice\",label:\"\\u4FDD\\u7559\\u4EF7\\uFF08\\u53EF\\u9009\\uFF09\",rules:[{type:'number',min:0.01,message:'保留价必须大于0'}],children:/*#__PURE__*/_jsx(InputNumber,{style:{width:'100%'},placeholder:\"\\u8BF7\\u8F93\\u5165\\u4FDD\\u7559\\u4EF7\",precision:2,min:0.01,addonBefore:\"\\xA5\"})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"bidIncrement\",label:\"\\u52A0\\u4EF7\\u5E45\\u5EA6\",rules:[{required:true,message:'请输入加价幅度'},{type:'number',min:0.01,message:'加价幅度必须大于0'}],children:/*#__PURE__*/_jsx(InputNumber,{style:{width:'100%'},placeholder:\"\\u8BF7\\u8F93\\u5165\\u52A0\\u4EF7\\u5E45\\u5EA6\",precision:2,min:0.01,addonBefore:\"\\xA5\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"startTime\",label:\"\\u5F00\\u59CB\\u65F6\\u95F4\",rules:[{required:true,message:'请选择开始时间'}],children:/*#__PURE__*/_jsx(DatePicker,{style:{width:'100%'},showTime:true,placeholder:\"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65F6\\u95F4\",format:\"YYYY-MM-DD HH:mm:ss\"})}),/*#__PURE__*/_jsx(FormMessage,{type:\"error\",message:formError,visible:!!formError}),/*#__PURE__*/_jsx(FormMessage,{type:\"success\",message:formSuccess,visible:!!formSuccess}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsxs(Space,{style:{width:'100%',justifyContent:'flex-end'},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>{setIsModalVisible(false);form.resetFields();setEditingItem(null);clearAllMessages();},disabled:saving,children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:saving,disabled:saving,children:saving?'保存中...':editingItem?'更新':'添加'})]})})]})})]});};export default AuctionItems;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Typography", "Row", "Col", "InputNumber", "Image", "Popconfirm", "Statistic", "Badge", "<PERSON><PERSON><PERSON>", "DatePicker", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ReloadOutlined", "AuditOutlined", "auctionService", "productService", "FormMessage", "useFormMessage", "handleApiResponse", "handleApiError", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Option", "AuctionItemStatus", "AuctionItems", "auctionItems", "setAuctionItems", "products", "setProducts", "auctions", "setAuctions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "editingItem", "setEditingItem", "saving", "setSaving", "statistics", "setStatistics", "totalItems", "pendingItems", "ongoingItems", "soldItems", "totalValue", "form", "useForm", "formError", "formSuccess", "setFormError", "setFormSuccess", "clearAllMessages", "itemStatusMap", "PENDING", "label", "color", "ONGOING", "SOLD", "UNSOLD", "WITHDRAWN", "fetchAuctionItems", "apiParams", "_objectSpread", "status", "undefined", "console", "log", "response", "getAuctionItemList", "JSON", "stringify", "success", "data", "Object", "keys", "itemsList", "list", "Array", "isArray", "error", "mappedItems", "map", "item", "product", "mappedItem", "id", "auctionId", "productId", "productName", "name", "productCode", "code", "concat", "quantity", "unit", "images", "startingPrice", "startPrice", "currentPrice", "reservePrice", "bidIncrement", "step<PERSON><PERSON>", "bidCount", "totalBids", "highestBidder", "winner<PERSON><PERSON>", "createdAt", "updatedAt", "category", "quality", "qualityLevel", "filter", "length", "reduce", "sum", "warn", "fetchProducts", "getProductList", "auditStatus", "fetchAuctions", "getAuctionList", "mappedAuctions", "auction", "title", "fetchStatistics", "checkApiConnection", "baseUrl", "process", "env", "REACT_APP_API_BASE_URL", "fetch", "json", "ok", "info", "initData", "itemsResponse", "auctionsResponse", "productsResponse", "Promise", "all", "handleSearch", "values", "handleReset", "handleAdd", "resetFields", "handleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "handleSave", "addAuctionItem", "startTime", "toISOString", "successMsg", "setTimeout", "style", "padding", "children", "level", "gutter", "marginBottom", "xs", "sm", "md", "value", "prefix", "valueStyle", "className", "size", "layout", "onFinish", "autoComplete", "width", "<PERSON><PERSON>", "placeholder", "allowClear", "type", "htmlType", "icon", "onClick", "justify", "align", "marginRight", "columns", "dataIndex", "key", "render", "_", "record", "display", "alignItems", "height", "src", "borderRadius", "fallback", "fontWeight", "fontSize", "numStatus", "statusInfo", "text", "toFixed", "Date", "toLocaleString", "fixed", "disabled", "onConfirm", "okText", "cancelText", "danger", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "open", "onCancel", "footer", "rules", "required", "showSearch", "optionFilterProp", "span", "min", "precision", "addonBefore", "showTime", "format", "visible", "justifyContent"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Typography,\n  Row,\n  Col,\n  InputNumber,\n  Image,\n  Popconfirm,\n  Statistic,\n  Badge,\n  Tooltip,\n  DatePicker,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  AuditOutlined,\n} from '@ant-design/icons';\n// import type { ColumnsType } from 'antd/es/table';\nimport { auctionService } from '../../../services/auctionService';\nimport { productService } from '../../../services/productService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';\n\nconst { Title } = Typography;\nconst { Option } = Select;\n\n// 拍卖商品状态枚举\nexport enum AuctionItemStatus {\n  PENDING = 0,     // 待拍卖\n  ONGOING = 1,     // 拍卖中\n  SOLD = 2,        // 已成交\n  UNSOLD = 3,      // 流拍\n  WITHDRAWN = 4,   // 撤回\n}\n\n// 拍卖商品接口\nexport interface AuctionItem {\n  id: number;\n  auctionId: number;\n  productId: number;\n  productName: string;\n  productCode: string;\n  startingPrice: number;\n  currentPrice: number;\n  reservePrice?: number;\n  bidIncrement: number;\n  bidCount: number;\n  status: AuctionItemStatus;\n  images?: string[];\n  category: string;\n  quality: string;\n  quantity: number;\n  unit: string;\n  highestBidder?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数接口\ninterface AuctionItemQueryParams {\n  auctionId?: number;\n  productName?: string;\n  status?: AuctionItemStatus;\n  category?: string;\n  page: number;\n  pageSize: number;\n}\n\nconst AuctionItems: React.FC = () => {\n  const [auctionItems, setAuctionItems] = useState<AuctionItem[]>([]);\n  const [products, setProducts] = useState<any[]>([]);\n  const [auctions, setAuctions] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<AuctionItemQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingItem, setEditingItem] = useState<AuctionItem | null>(null);\n  const [saving, setSaving] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalItems: 0,\n    pendingItems: 0,\n    ongoingItems: 0,\n    soldItems: 0,\n    totalValue: 0,\n  });\n  const [form] = Form.useForm();\n\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 拍卖商品状态映射\n  const itemStatusMap = {\n    [AuctionItemStatus.PENDING]: { label: '待拍卖', color: 'default' },\n    [AuctionItemStatus.ONGOING]: { label: '拍卖中', color: 'blue' },\n    [AuctionItemStatus.SOLD]: { label: '已成交', color: 'green' },\n    [AuctionItemStatus.UNSOLD]: { label: '流拍', color: 'orange' },\n    [AuctionItemStatus.WITHDRAWN]: { label: '撤回', color: 'red' },\n  };\n\n  // 获取拍卖商品列表\n  const fetchAuctionItems = async () => {\n    setLoading(true);\n    try {\n      // 调用后端API获取拍卖商品列表\n      const apiParams = {\n        ...queryParams,\n        // 确保状态参数是数字类型\n        status: typeof queryParams.status === 'number' ? queryParams.status : undefined,\n      };\n      \n      console.log('请求参数:', apiParams);\n      console.log('请求URL:', `/auction-items`);\n      \n      const response = await auctionService.getAuctionItemList(apiParams);\n      console.log('API响应完整数据:', JSON.stringify(response));\n\n      if (response.success && response.data) {\n        // 检查数据结构\n        console.log('响应数据结构:', Object.keys(response.data));\n        \n        // 确保list字段存在且是数组\n        const itemsList = response.data.list || [];\n        if (!Array.isArray(itemsList)) {\n          console.error('列表数据不是数组:', itemsList);\n          setAuctionItems([]);\n          setTotal(0);\n          return;\n        }\n        \n        console.log('原始拍卖商品数据:', itemsList);\n        \n        // 处理拍卖商品数据，映射字段\n        const mappedItems = itemsList.map((item: any) => {\n          console.log('处理项目:', item);\n          \n          // 从product中获取商品信息\n          const product = item.product || {};\n          console.log('商品信息:', product);\n          \n          // 创建映射后的对象\n          const mappedItem: AuctionItem = {\n            id: item.id || 0,\n            auctionId: item.auctionId || 0,\n            productId: item.productId || 0,\n            productName: product.name || item.productName || '未知商品',\n            productCode: product.code || item.productCode || `商品${item.productId || 0}`,\n            quantity: product.quantity || item.quantity || 1,\n            unit: product.unit || item.unit || '件',\n            images: product.images || item.images || [],\n            startingPrice: item.startPrice || item.startingPrice || 0,\n            currentPrice: item.currentPrice || item.startPrice || item.startingPrice || 0,\n            reservePrice: item.reservePrice || 0,\n            bidIncrement: item.stepPrice || item.bidIncrement || 10,\n            bidCount: item.totalBids || item.bidCount || 0,\n            highestBidder: item.winnerName || item.highestBidder || '',\n            status: typeof item.status === 'number' ? item.status : \n                   item.status === 'PENDING' ? AuctionItemStatus.PENDING :\n                   item.status === 'ONGOING' ? AuctionItemStatus.ONGOING :\n                   item.status === 'SOLD' ? AuctionItemStatus.SOLD :\n                   item.status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :\n                   item.status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : \n                   AuctionItemStatus.PENDING,\n            createdAt: item.createdAt || '',\n            updatedAt: item.updatedAt || '',\n            category: product.category || item.category || '',\n            quality: product.qualityLevel || item.quality || '',\n          };\n          \n          console.log('映射后的项目:', mappedItem);\n          return mappedItem;\n        });\n\n        console.log('最终映射后的数据:', mappedItems);\n        setAuctionItems(mappedItems);\n        setTotal(response.data.total || 0);\n        \n        // 更新统计信息\n        const pendingItems = mappedItems.filter(item => item.status === AuctionItemStatus.PENDING).length;\n        const ongoingItems = mappedItems.filter(item => item.status === AuctionItemStatus.ONGOING).length;\n        const soldItems = mappedItems.filter(item => item.status === AuctionItemStatus.SOLD).length;\n        const totalValue = mappedItems.reduce((sum, item) => sum + item.currentPrice, 0);\n        \n        setStatistics({\n          totalItems: mappedItems.length,\n          pendingItems,\n          ongoingItems,\n          soldItems,\n          totalValue,\n        });\n      } else {\n        console.warn('拍卖商品数据格式异常:', response);\n        setAuctionItems([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖商品列表失败:', error);\n      message.error(error.message || '获取拍卖商品列表失败');\n      setAuctionItems([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品列表（用于添加拍卖商品）\n  const fetchProducts = async () => {\n    try {\n      const response = await productService.getProductList({\n        page: 1,\n        pageSize: 100,\n        auditStatus: 'approved', // 只获取已审核的商品\n      });\n\n      if (response.success && response.data && response.data.list) {\n        setProducts(response.data.list);\n        console.log('获取到商品列表:', response.data.list);\n      } else {\n        console.warn('商品数据格式异常:', response);\n        setProducts([]);\n      }\n    } catch (error: any) {\n      console.error('获取商品列表失败:', error);\n      setProducts([]);\n    }\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    try {\n      const response = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 100,\n      });\n\n      if (response.success && response.data && response.data.list) {\n        // 将后端的name字段映射为前端的title字段\n        const mappedAuctions = response.data.list.map((auction: any) => ({\n          ...auction,\n          title: auction.name || auction.title, // 后端返回name，前端使用title\n        }));\n        setAuctions(mappedAuctions);\n        console.log('获取到拍卖会列表:', mappedAuctions);\n      } else {\n        console.warn('拍卖会数据格式异常:', response);\n        setAuctions([]);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会列表失败:', error);\n      setAuctions([]);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      // 这里需要调用后端API获取统计信息\n      // 暂时使用模拟数据\n      setStatistics({\n        totalItems: 0,\n        pendingItems: 0,\n        ongoingItems: 0,\n        soldItems: 0,\n        totalValue: 0,\n      });\n    } catch (error: any) {\n      console.error('获取统计信息失败:', error);\n    }\n  };\n\n  // 检查API连接\n  const checkApiConnection = async () => {\n    try {\n      message.loading('正在检查API连接...');\n      \n      // 尝试请求API基础URL\n      const baseUrl = process.env.REACT_APP_API_BASE_URL;\n      \n      // 使用fetch直接请求，避免axios拦截器的影响\n      const response = await fetch(`${baseUrl}/auction-items?page=1&pageSize=1`);\n      const data = await response.json();\n      \n      console.log('API连接测试结果:', data);\n      \n      if (response.ok) {\n        message.success(`API连接正常，响应状态: ${response.status}`);\n      } else {\n        message.error(`API连接异常，响应状态: ${response.status}`);\n      }\n      \n      // 显示响应数据结构\n      if (data) {\n        console.log('响应数据结构:', Object.keys(data));\n        message.info(`响应数据结构: ${JSON.stringify(Object.keys(data))}`);\n      }\n    } catch (error: any) {\n      console.error('API连接测试失败:', error);\n      message.error(`API连接测试失败: ${error.message}`);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    // 修改初始化请求，减少重复请求\n    const initData = async () => {\n      setLoading(true);\n      try {\n        console.log('开始初始化数据加载');\n        // 并行请求数据，减少请求次数\n        const [itemsResponse, auctionsResponse, productsResponse] = await Promise.all([\n          auctionService.getAuctionItemList({\n            ...queryParams,\n            // 枚举现在已经是数字类型，可以直接使用\n            status: queryParams.status,\n            pageSize: 50, // 使用更大的页面大小，减少请求次数\n          }),\n          auctionService.getAuctionList({\n            page: 1,\n            pageSize: 50, // 使用更大的页面大小，减少请求次数\n          }),\n          productService.getProductList({\n            page: 1,\n            pageSize: 50, // 使用更大的页面大小，减少请求次数\n            auditStatus: 'approved',\n          }),\n        ]);\n\n        console.log('初始化数据请求完成');\n        console.log('拍卖商品响应:', JSON.stringify(itemsResponse));\n        console.log('拍卖会响应:', JSON.stringify(auctionsResponse));\n        console.log('商品响应:', JSON.stringify(productsResponse));\n\n        // 处理拍卖会数据\n        if (auctionsResponse.success && auctionsResponse.data && auctionsResponse.data.list) {\n          const mappedAuctions = auctionsResponse.data.list.map((auction: any) => ({\n            ...auction,\n            title: auction.name || auction.title,\n          }));\n          setAuctions(mappedAuctions);\n          console.log('处理后的拍卖会数据:', mappedAuctions);\n        }\n\n        // 处理商品数据\n        if (productsResponse.success && productsResponse.data && productsResponse.data.list) {\n          setProducts(productsResponse.data.list);\n          console.log('处理后的商品数据:', productsResponse.data.list);\n        }\n\n        // 处理拍卖商品数据\n        if (itemsResponse.success && itemsResponse.data) {\n          console.log('拍卖商品数据结构:', Object.keys(itemsResponse.data));\n          \n          // 确保list字段存在且是数组\n          const itemsList = itemsResponse.data.list || [];\n          if (!Array.isArray(itemsList)) {\n            console.error('列表数据不是数组:', itemsList);\n            setAuctionItems([]);\n            setTotal(0);\n            return;\n          }\n          \n          console.log('拍卖商品原始数据:', itemsList);\n          \n          const mappedItems = itemsList.map((item: any) => {\n            // 从product中获取商品信息\n            const product = item.product || {};\n            console.log('处理商品项:', item.id, '商品信息:', product);\n            \n            // 状态字段现在是数字，可以直接使用\n            const status = typeof item.status === 'number' ? item.status : \n                          item.status === 'PENDING' ? AuctionItemStatus.PENDING :\n                          item.status === 'ONGOING' ? AuctionItemStatus.ONGOING :\n                          item.status === 'SOLD' ? AuctionItemStatus.SOLD :\n                          item.status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :\n                          item.status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : \n                          AuctionItemStatus.PENDING;\n            \n            return {\n              id: item.id,\n              auctionId: item.auctionId,\n              productId: item.productId,\n              productName: product.name || item.productName || '未知商品',\n              productCode: product.code || item.productCode || `商品${item.productId}`,\n              quantity: product.quantity || item.quantity || 1,\n              unit: product.unit || item.unit || '件',\n              images: product.images || item.images || [],\n              // 价格字段映射\n              startingPrice: item.startPrice || item.startingPrice || 0,\n              currentPrice: item.currentPrice || item.startPrice || item.startingPrice || 0,\n              reservePrice: item.reservePrice || 0,\n              bidIncrement: item.stepPrice || item.bidIncrement || 10,\n              bidCount: item.totalBids || item.bidCount || 0,\n              highestBidder: item.winnerName || item.highestBidder || '',\n              status: status,\n              createdAt: item.createdAt,\n              updatedAt: item.updatedAt,\n              category: product.category || item.category || '',\n              quality: product.qualityLevel || item.quality || '',\n            };\n          });\n\n          console.log('映射后的拍卖商品数据:', mappedItems);\n          setAuctionItems(mappedItems);\n          setTotal(itemsResponse.data.total || 0);\n          \n          // 更新统计信息\n          const pendingItems = mappedItems.filter(item => item.status === AuctionItemStatus.PENDING).length;\n          const ongoingItems = mappedItems.filter(item => item.status === AuctionItemStatus.ONGOING).length;\n          const soldItems = mappedItems.filter(item => item.status === AuctionItemStatus.SOLD).length;\n          const totalValue = mappedItems.reduce((sum, item) => sum + item.currentPrice, 0);\n          \n          setStatistics({\n            totalItems: mappedItems.length,\n            pendingItems,\n            ongoingItems,\n            soldItems,\n            totalValue,\n          });\n        } else {\n          console.warn('拍卖商品数据格式异常:', itemsResponse);\n          setAuctionItems([]);\n          setTotal(0);\n        }\n      } catch (error) {\n        console.error('初始化数据失败:', error);\n        message.error('获取数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // 首次加载或重置查询参数时，使用并行请求\n    if (\n      queryParams.page === 1 && \n      !queryParams.auctionId && \n      !queryParams.productName && \n      !queryParams.status\n    ) {\n      initData();\n    } else {\n      // 搜索或翻页时，只请求拍卖商品列表\n      fetchAuctionItems();\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增拍卖商品\n  const handleAdd = () => {\n    setEditingItem(null);\n    form.resetFields();\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 编辑拍卖商品\n  const handleEdit = (item: AuctionItem) => {\n    setEditingItem(item);\n    form.setFieldsValue(item);\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖商品\n  const handleDelete = async (id: number) => {\n    try {\n      // 调用删除API\n      message.success('删除成功');\n      fetchAuctionItems();\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖商品\n  const handleSave = async (values: any) => {\n    setSaving(true);\n    clearAllMessages();\n\n    try {\n      let response;\n      if (editingItem) {\n        // 更新拍卖商品 - 目前暂不支持更新，显示提示信息\n        setFormError('暂不支持更新拍卖商品，请删除后重新添加');\n        setSaving(false);\n        return;\n      } else {\n        // 添加拍卖商品\n        response = await auctionService.addAuctionItem(values.auctionId, {\n          productId: values.productId,\n          startingPrice: values.startingPrice,\n          reservePrice: values.reservePrice,\n          bidIncrement: values.bidIncrement,\n          startTime: values.startTime ? values.startTime.toISOString() : undefined,\n        });\n      }\n\n      const successMsg = '拍卖商品添加成功！';\n\n      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingItem(null);\n          clearAllMessages();\n          fetchAuctionItems();\n          fetchStatistics();\n        }, 1500);\n      }\n    } catch (error: any) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>拍卖商品管理</Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总商品数\"\n              value={statistics.totalItems}\n              prefix={<AuditOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"待拍卖\"\n              value={statistics.pendingItems}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"拍卖中\"\n              value={statistics.ongoingItems}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"已成交\"\n              value={statistics.soldItems}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\" style={{ marginBottom: 16 }}>\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"productName\" label=\"商品名称\">\n                <Input placeholder=\"请输入商品名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"拍卖状态\">\n                <Select placeholder=\"请选择拍卖状态\" allowClear>\n                  <Option value={AuctionItemStatus.PENDING}>待拍卖</Option>\n                  <Option value={AuctionItemStatus.ONGOING}>拍卖中</Option>\n                  <Option value={AuctionItemStatus.SOLD}>已成交</Option>\n                  <Option value={AuctionItemStatus.UNSOLD}>流拍</Option>\n                  <Option value={AuctionItemStatus.WITHDRAWN}>撤回</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"auctionId\" label=\"拍卖会\">\n                <Select placeholder=\"请选择拍卖会\" allowClear>\n                  {auctions.map(auction => (\n                    <Option key={auction.id} value={auction.id}>\n                      {auction.title}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\" style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              添加拍卖商品\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchAuctionItems}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 拍卖商品列表表格 */}\n      <Card>\n        {/* 添加调试信息，显示当前数据状态 */}\n        <div style={{ marginBottom: 16 }}>\n          <p>当前数据状态: {loading ? '加载中...' : `共 ${auctionItems.length} 条记录`}</p>\n          {auctionItems.length === 0 && !loading && (\n            <p style={{ color: 'red' }}>未找到拍卖商品数据，请检查数据映射或网络请求</p>\n          )}\n          {/* 添加调试按钮 */}\n          <Button \n            type=\"primary\" \n            onClick={() => {\n              console.log('当前拍卖商品数据:', auctionItems);\n              message.info(`当前数据条数: ${auctionItems.length}`);\n            }}\n            style={{ marginRight: 8 }}\n          >\n            调试数据\n          </Button>\n          <Button \n            type=\"primary\" \n            onClick={checkApiConnection}\n            style={{ marginRight: 8 }}\n          >\n            测试API连接\n          </Button>\n          <Button \n            onClick={fetchAuctionItems}\n            style={{ marginRight: 8 }}\n          >\n            重新获取数据\n          </Button>\n        </div>\n        \n        <Table\n          columns={[\n            {\n              title: 'ID',\n              dataIndex: 'id',\n              key: 'id',\n              width: 80,\n            },\n            {\n              title: '商品信息',\n              key: 'product',\n              width: 250,\n              render: (_, record: AuctionItem) => (\n                <div style={{ display: 'flex', alignItems: 'center' }}>\n                  {record.images && record.images.length > 0 && (\n                    <Image\n                      width={60}\n                      height={60}\n                      src={record.images[0]}\n                      style={{ marginRight: 12, borderRadius: 4 }}\n                      fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n                    />\n                  )}\n                  <div>\n                    <div style={{ fontWeight: 500 }}>{record.productName}</div>\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      编号: {record.productCode}\n                    </div>\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      {record.quantity} {record.unit}\n                    </div>\n                  </div>\n                </div>\n              ),\n            },\n            {\n              title: '拍卖状态',\n              dataIndex: 'status',\n              key: 'status',\n              width: 100,\n              render: (status: AuctionItemStatus) => {\n                console.log('渲染状态:', status, typeof status);\n                // 确保状态是数字类型\n                const numStatus = typeof status === 'number' ? status : \n                                 status === 'PENDING' ? AuctionItemStatus.PENDING :\n                                 status === 'ONGOING' ? AuctionItemStatus.ONGOING :\n                                 status === 'SOLD' ? AuctionItemStatus.SOLD :\n                                 status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :\n                                 status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : \n                                 AuctionItemStatus.PENDING;\n                \n                const statusInfo = itemStatusMap[numStatus];\n                console.log('状态信息:', statusInfo);\n                \n                return (\n                  <Badge\n                    status={\n                      numStatus === AuctionItemStatus.ONGOING ? 'processing' :\n                      numStatus === AuctionItemStatus.SOLD ? 'success' :\n                      numStatus === AuctionItemStatus.UNSOLD ? 'warning' :\n                      numStatus === AuctionItemStatus.WITHDRAWN ? 'error' : 'default'\n                    }\n                    text={\n                      <Tag color={statusInfo?.color || 'default'}>\n                        {statusInfo?.label || `未知(${numStatus})`}\n                      </Tag>\n                    }\n                  />\n                );\n              },\n            },\n            {\n              title: '价格信息',\n              key: 'price',\n              width: 150,\n              render: (_, record: AuctionItem) => (\n                <div>\n                  <div>起拍: ¥{record.startingPrice.toFixed(2)}</div>\n                  <div style={{ color: '#f50', fontWeight: 500 }}>\n                    当前: ¥{record.currentPrice.toFixed(2)}\n                  </div>\n                  {record.reservePrice && (\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      保留: ¥{record.reservePrice.toFixed(2)}\n                    </div>\n                  )}\n                </div>\n              ),\n            },\n            {\n              title: '竞价信息',\n              key: 'bid',\n              width: 120,\n              render: (_, record: AuctionItem) => (\n                <div>\n                  <div>出价次数: {record.bidCount}</div>\n                  <div>加价幅度: ¥{record.bidIncrement.toFixed(2)}</div>\n                  {record.highestBidder && (\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      最高出价人: {record.highestBidder}\n                    </div>\n                  )}\n                </div>\n              ),\n            },\n            {\n              title: '创建时间',\n              dataIndex: 'createdAt',\n              key: 'createdAt',\n              width: 160,\n              render: (text: string) => new Date(text).toLocaleString(),\n            },\n            {\n              title: '操作',\n              key: 'action',\n              width: 200,\n              fixed: 'right',\n              render: (_, record: AuctionItem) => (\n                <Space size=\"small\">\n                  <Tooltip title=\"查看详情\">\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      icon={<EyeOutlined />}\n                      onClick={() => {/* 查看详情 */}}\n                    />\n                  </Tooltip>\n                  <Tooltip title=\"编辑\">\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      icon={<EditOutlined />}\n                      onClick={() => handleEdit(record)}\n                      disabled={record.status === AuctionItemStatus.ONGOING}\n                    />\n                  </Tooltip>\n                  <Popconfirm\n                    title=\"确定要删除这个拍卖商品吗？\"\n                    onConfirm={() => handleDelete(record.id)}\n                    okText=\"确定\"\n                    cancelText=\"取消\"\n                  >\n                    <Tooltip title=\"删除\">\n                      <Button\n                        type=\"link\"\n                        size=\"small\"\n                        danger\n                        icon={<DeleteOutlined />}\n                        disabled={record.status === AuctionItemStatus.ONGOING}\n                      />\n                    </Tooltip>\n                  </Popconfirm>\n                </Space>\n              ),\n            },\n          ]}\n          dataSource={auctionItems}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 拍卖商品编辑模态框 */}\n      <Modal\n        title={editingItem ? '编辑拍卖商品' : '添加拍卖商品'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Form.Item\n            name=\"auctionId\"\n            label=\"拍卖会\"\n            rules={[{ required: true, message: '请选择拍卖会' }]}\n          >\n            <Select placeholder=\"请选择拍卖会\">\n              {auctions.map(auction => (\n                <Option key={auction.id} value={auction.id}>\n                  {auction.title}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"productId\"\n            label=\"商品\"\n            rules={[{ required: true, message: '请选择商品' }]}\n          >\n            <Select placeholder=\"请选择商品\" showSearch optionFilterProp=\"children\">\n              {products.map(product => (\n                <Option key={product.id} value={product.id}>\n                  {product.name} - {product.code}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"startingPrice\"\n                label=\"起拍价\"\n                rules={[\n                  { required: true, message: '请输入起拍价' },\n                  { type: 'number', min: 0.01, message: '起拍价必须大于0' },\n                ]}\n              >\n                <InputNumber\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入起拍价\"\n                  precision={2}\n                  min={0.01}\n                  addonBefore=\"¥\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"reservePrice\"\n                label=\"保留价（可选）\"\n                rules={[\n                  { type: 'number', min: 0.01, message: '保留价必须大于0' },\n                ]}\n              >\n                <InputNumber\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入保留价\"\n                  precision={2}\n                  min={0.01}\n                  addonBefore=\"¥\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"bidIncrement\"\n            label=\"加价幅度\"\n            rules={[\n              { required: true, message: '请输入加价幅度' },\n              { type: 'number', min: 0.01, message: '加价幅度必须大于0' },\n            ]}\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              placeholder=\"请输入加价幅度\"\n              precision={2}\n              min={0.01}\n              addonBefore=\"¥\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"startTime\"\n            label=\"开始时间\"\n            rules={[\n              { required: true, message: '请选择开始时间' },\n            ]}\n          >\n            <DatePicker\n              style={{ width: '100%' }}\n              showTime\n              placeholder=\"请选择开始时间\"\n              format=\"YYYY-MM-DD HH:mm:ss\"\n            />\n          </Form.Item>\n\n          {/* 错误和成功消息显示 */}\n          <FormMessage type=\"error\" message={formError} visible={!!formError} />\n          <FormMessage type=\"success\" message={formSuccess} visible={!!formSuccess} />\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button\n                onClick={() => {\n                  setIsModalVisible(false);\n                  form.resetFields();\n                  setEditingItem(null);\n                  clearAllMessages();\n                }}\n                disabled={saving}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={saving}\n                disabled={saving}\n              >\n                {saving ? '保存中...' : (editingItem ? '更新' : '添加')}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AuctionItems;\n"], "mappings": "kJAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,OAAO,CACPC,UAAU,CACVC,GAAG,CACHC,GAAG,CACHC,WAAW,CACXC,KAAK,CACLC,UAAU,CACVC,SAAS,CACTC,KAAK,CACLC,OAAO,CACPC,UAAU,KACL,MAAM,CACb,OACEC,YAAY,CACZC,cAAc,CACdC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,aAAa,KACR,mBAAmB,CAC1B;AACA,OAASC,cAAc,KAAQ,kCAAkC,CACjE,OAASC,cAAc,KAAQ,kCAAkC,CACjE,MAAO,CAAAC,WAAW,KAAM,iCAAiC,CACzD,OAASC,cAAc,CAAEC,iBAAiB,CAAEC,cAAc,KAAQ,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElG,KAAM,CAAEC,KAAM,CAAC,CAAG3B,UAAU,CAC5B,KAAM,CAAE4B,MAAO,CAAC,CAAGjC,MAAM,CAEzB;AACA,UAAY,CAAAkC,iBAAiB,uBAAjBA,iBAAiB,EAAjBA,iBAAiB,CAAjBA,iBAAiB,yBACV;AADPA,iBAAiB,CAAjBA,iBAAiB,yBAEV;AAFPA,iBAAiB,CAAjBA,iBAAiB,mBAGV;AAHPA,iBAAiB,CAAjBA,iBAAiB,uBAIV;AAJPA,iBAAiB,CAAjBA,iBAAiB,6BAKV;AAAA,MALP,CAAAA,iBAAiB,OAQ7B;AAuBA;AAUA,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG5C,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAAC6C,QAAQ,CAAEC,WAAW,CAAC,CAAG9C,QAAQ,CAAQ,EAAE,CAAC,CACnD,KAAM,CAAC+C,QAAQ,CAAEC,WAAW,CAAC,CAAGhD,QAAQ,CAAQ,EAAE,CAAC,CACnD,KAAM,CAACiD,OAAO,CAAEC,UAAU,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACmD,KAAK,CAAEC,QAAQ,CAAC,CAAGpD,QAAQ,CAAC,CAAC,CAAC,CACrC,KAAM,CAACqD,WAAW,CAAEC,cAAc,CAAC,CAAGtD,QAAQ,CAAyB,CACrEuD,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC2D,WAAW,CAAEC,cAAc,CAAC,CAAG5D,QAAQ,CAAqB,IAAI,CAAC,CACxE,KAAM,CAAC6D,MAAM,CAAEC,SAAS,CAAC,CAAG9D,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAC+D,UAAU,CAAEC,aAAa,CAAC,CAAGhE,QAAQ,CAAC,CAC3CiE,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,CAAC,CACfC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,CACd,CAAC,CAAC,CACF,KAAM,CAACC,IAAI,CAAC,CAAG5D,IAAI,CAAC6D,OAAO,CAAC,CAAC,CAE7B,KAAM,CACJC,SAAS,CACTC,WAAW,CACXC,YAAY,CACZC,cAAc,CACdC,gBACF,CAAC,CAAG5C,cAAc,CAAC,CAAC,CAEpB;AACA,KAAM,CAAA6C,aAAa,CAAG,CACpB,CAACpC,iBAAiB,CAACqC,OAAO,EAAG,CAAEC,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/D,CAACvC,iBAAiB,CAACwC,OAAO,EAAG,CAAEF,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC5D,CAACvC,iBAAiB,CAACyC,IAAI,EAAG,CAAEH,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAC1D,CAACvC,iBAAiB,CAAC0C,MAAM,EAAG,CAAEJ,KAAK,CAAE,IAAI,CAAEC,KAAK,CAAE,QAAS,CAAC,CAC5D,CAACvC,iBAAiB,CAAC2C,SAAS,EAAG,CAAEL,KAAK,CAAE,IAAI,CAAEC,KAAK,CAAE,KAAM,CAC7D,CAAC,CAED;AACA,KAAM,CAAAK,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpCnC,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,CAAAoC,SAAS,CAAAC,aAAA,CAAAA,aAAA,IACVlC,WAAW,MACd;AACAmC,MAAM,CAAE,MAAO,CAAAnC,WAAW,CAACmC,MAAM,GAAK,QAAQ,CAAGnC,WAAW,CAACmC,MAAM,CAAGC,SAAS,EAChF,CAEDC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEL,SAAS,CAAC,CAC/BI,OAAO,CAACC,GAAG,CAAC,QAAQ,iBAAkB,CAAC,CAEvC,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/D,cAAc,CAACgE,kBAAkB,CAACP,SAAS,CAAC,CACnEI,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEG,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAAC,CAEnD,GAAIA,QAAQ,CAACI,OAAO,EAAIJ,QAAQ,CAACK,IAAI,CAAE,CACrC;AACAP,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEO,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,IAAI,CAAC,CAAC,CAElD;AACA,KAAM,CAAAG,SAAS,CAAGR,QAAQ,CAACK,IAAI,CAACI,IAAI,EAAI,EAAE,CAC1C,GAAI,CAACC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,CAAE,CAC7BV,OAAO,CAACc,KAAK,CAAC,WAAW,CAAEJ,SAAS,CAAC,CACrCxD,eAAe,CAAC,EAAE,CAAC,CACnBQ,QAAQ,CAAC,CAAC,CAAC,CACX,OACF,CAEAsC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAES,SAAS,CAAC,CAEnC;AACA,KAAM,CAAAK,WAAW,CAAGL,SAAS,CAACM,GAAG,CAAEC,IAAS,EAAK,CAC/CjB,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEgB,IAAI,CAAC,CAE1B;AACA,KAAM,CAAAC,OAAO,CAAGD,IAAI,CAACC,OAAO,EAAI,CAAC,CAAC,CAClClB,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEiB,OAAO,CAAC,CAE7B;AACA,KAAM,CAAAC,UAAuB,CAAG,CAC9BC,EAAE,CAAEH,IAAI,CAACG,EAAE,EAAI,CAAC,CAChBC,SAAS,CAAEJ,IAAI,CAACI,SAAS,EAAI,CAAC,CAC9BC,SAAS,CAAEL,IAAI,CAACK,SAAS,EAAI,CAAC,CAC9BC,WAAW,CAAEL,OAAO,CAACM,IAAI,EAAIP,IAAI,CAACM,WAAW,EAAI,MAAM,CACvDE,WAAW,CAAEP,OAAO,CAACQ,IAAI,EAAIT,IAAI,CAACQ,WAAW,iBAAAE,MAAA,CAASV,IAAI,CAACK,SAAS,EAAI,CAAC,CAAE,CAC3EM,QAAQ,CAAEV,OAAO,CAACU,QAAQ,EAAIX,IAAI,CAACW,QAAQ,EAAI,CAAC,CAChDC,IAAI,CAAEX,OAAO,CAACW,IAAI,EAAIZ,IAAI,CAACY,IAAI,EAAI,GAAG,CACtCC,MAAM,CAAEZ,OAAO,CAACY,MAAM,EAAIb,IAAI,CAACa,MAAM,EAAI,EAAE,CAC3CC,aAAa,CAAEd,IAAI,CAACe,UAAU,EAAIf,IAAI,CAACc,aAAa,EAAI,CAAC,CACzDE,YAAY,CAAEhB,IAAI,CAACgB,YAAY,EAAIhB,IAAI,CAACe,UAAU,EAAIf,IAAI,CAACc,aAAa,EAAI,CAAC,CAC7EG,YAAY,CAAEjB,IAAI,CAACiB,YAAY,EAAI,CAAC,CACpCC,YAAY,CAAElB,IAAI,CAACmB,SAAS,EAAInB,IAAI,CAACkB,YAAY,EAAI,EAAE,CACvDE,QAAQ,CAAEpB,IAAI,CAACqB,SAAS,EAAIrB,IAAI,CAACoB,QAAQ,EAAI,CAAC,CAC9CE,aAAa,CAAEtB,IAAI,CAACuB,UAAU,EAAIvB,IAAI,CAACsB,aAAa,EAAI,EAAE,CAC1DzC,MAAM,CAAE,MAAO,CAAAmB,IAAI,CAACnB,MAAM,GAAK,QAAQ,CAAGmB,IAAI,CAACnB,MAAM,CAC9CmB,IAAI,CAACnB,MAAM,GAAK,SAAS,CAAG/C,iBAAiB,CAACqC,OAAO,CACrD6B,IAAI,CAACnB,MAAM,GAAK,SAAS,CAAG/C,iBAAiB,CAACwC,OAAO,CACrD0B,IAAI,CAACnB,MAAM,GAAK,MAAM,CAAG/C,iBAAiB,CAACyC,IAAI,CAC/CyB,IAAI,CAACnB,MAAM,GAAK,QAAQ,CAAG/C,iBAAiB,CAAC0C,MAAM,CACnDwB,IAAI,CAACnB,MAAM,GAAK,WAAW,CAAG/C,iBAAiB,CAAC2C,SAAS,CACzD3C,iBAAiB,CAACqC,OAAO,CAChCqD,SAAS,CAAExB,IAAI,CAACwB,SAAS,EAAI,EAAE,CAC/BC,SAAS,CAAEzB,IAAI,CAACyB,SAAS,EAAI,EAAE,CAC/BC,QAAQ,CAAEzB,OAAO,CAACyB,QAAQ,EAAI1B,IAAI,CAAC0B,QAAQ,EAAI,EAAE,CACjDC,OAAO,CAAE1B,OAAO,CAAC2B,YAAY,EAAI5B,IAAI,CAAC2B,OAAO,EAAI,EACnD,CAAC,CAED5C,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEkB,UAAU,CAAC,CAClC,MAAO,CAAAA,UAAU,CACnB,CAAC,CAAC,CAEFnB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEc,WAAW,CAAC,CACrC7D,eAAe,CAAC6D,WAAW,CAAC,CAC5BrD,QAAQ,CAACwC,QAAQ,CAACK,IAAI,CAAC9C,KAAK,EAAI,CAAC,CAAC,CAElC;AACA,KAAM,CAAAe,YAAY,CAAGuC,WAAW,CAAC+B,MAAM,CAAC7B,IAAI,EAAIA,IAAI,CAACnB,MAAM,GAAK/C,iBAAiB,CAACqC,OAAO,CAAC,CAAC2D,MAAM,CACjG,KAAM,CAAAtE,YAAY,CAAGsC,WAAW,CAAC+B,MAAM,CAAC7B,IAAI,EAAIA,IAAI,CAACnB,MAAM,GAAK/C,iBAAiB,CAACwC,OAAO,CAAC,CAACwD,MAAM,CACjG,KAAM,CAAArE,SAAS,CAAGqC,WAAW,CAAC+B,MAAM,CAAC7B,IAAI,EAAIA,IAAI,CAACnB,MAAM,GAAK/C,iBAAiB,CAACyC,IAAI,CAAC,CAACuD,MAAM,CAC3F,KAAM,CAAApE,UAAU,CAAGoC,WAAW,CAACiC,MAAM,CAAC,CAACC,GAAG,CAAEhC,IAAI,GAAKgC,GAAG,CAAGhC,IAAI,CAACgB,YAAY,CAAE,CAAC,CAAC,CAEhF3D,aAAa,CAAC,CACZC,UAAU,CAAEwC,WAAW,CAACgC,MAAM,CAC9BvE,YAAY,CACZC,YAAY,CACZC,SAAS,CACTC,UACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLqB,OAAO,CAACkD,IAAI,CAAC,aAAa,CAAEhD,QAAQ,CAAC,CACrChD,eAAe,CAAC,EAAE,CAAC,CACnBQ,QAAQ,CAAC,CAAC,CAAC,CACb,CACF,CAAE,MAAOoD,KAAU,CAAE,CACnBd,OAAO,CAACc,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC7F,OAAO,CAAC6F,KAAK,CAACA,KAAK,CAAC7F,OAAO,EAAI,YAAY,CAAC,CAC5CiC,eAAe,CAAC,EAAE,CAAC,CACnBQ,QAAQ,CAAC,CAAC,CAAC,CACb,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA2F,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAjD,QAAQ,CAAG,KAAM,CAAA9D,cAAc,CAACgH,cAAc,CAAC,CACnDvF,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,GAAG,CACbuF,WAAW,CAAE,UAAY;AAC3B,CAAC,CAAC,CAEF,GAAInD,QAAQ,CAACI,OAAO,EAAIJ,QAAQ,CAACK,IAAI,EAAIL,QAAQ,CAACK,IAAI,CAACI,IAAI,CAAE,CAC3DvD,WAAW,CAAC8C,QAAQ,CAACK,IAAI,CAACI,IAAI,CAAC,CAC/BX,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEC,QAAQ,CAACK,IAAI,CAACI,IAAI,CAAC,CAC7C,CAAC,IAAM,CACLX,OAAO,CAACkD,IAAI,CAAC,WAAW,CAAEhD,QAAQ,CAAC,CACnC9C,WAAW,CAAC,EAAE,CAAC,CACjB,CACF,CAAE,MAAO0D,KAAU,CAAE,CACnBd,OAAO,CAACc,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC1D,WAAW,CAAC,EAAE,CAAC,CACjB,CACF,CAAC,CAED;AACA,KAAM,CAAAkG,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAApD,QAAQ,CAAG,KAAM,CAAA/D,cAAc,CAACoH,cAAc,CAAC,CACnD1F,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,GACZ,CAAC,CAAC,CAEF,GAAIoC,QAAQ,CAACI,OAAO,EAAIJ,QAAQ,CAACK,IAAI,EAAIL,QAAQ,CAACK,IAAI,CAACI,IAAI,CAAE,CAC3D;AACA,KAAM,CAAA6C,cAAc,CAAGtD,QAAQ,CAACK,IAAI,CAACI,IAAI,CAACK,GAAG,CAAEyC,OAAY,EAAA5D,aAAA,CAAAA,aAAA,IACtD4D,OAAO,MACVC,KAAK,CAAED,OAAO,CAACjC,IAAI,EAAIiC,OAAO,CAACC,KAAO;AAAA,EACtC,CAAC,CACHpG,WAAW,CAACkG,cAAc,CAAC,CAC3BxD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEuD,cAAc,CAAC,CAC1C,CAAC,IAAM,CACLxD,OAAO,CAACkD,IAAI,CAAC,YAAY,CAAEhD,QAAQ,CAAC,CACpC5C,WAAW,CAAC,EAAE,CAAC,CACjB,CACF,CAAE,MAAOwD,KAAU,CAAE,CACnBd,OAAO,CAACc,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClCxD,WAAW,CAAC,EAAE,CAAC,CACjB,CACF,CAAC,CAED;AACA,KAAM,CAAAqG,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF;AACA;AACArF,aAAa,CAAC,CACZC,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,CAAC,CACfC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,CACd,CAAC,CAAC,CACJ,CAAE,MAAOmC,KAAU,CAAE,CACnBd,OAAO,CAACc,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAA8C,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF3I,OAAO,CAACsC,OAAO,CAAC,cAAc,CAAC,CAE/B;AACA,KAAM,CAAAsG,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,CAElD;AACA,KAAM,CAAA9D,QAAQ,CAAG,KAAM,CAAA+D,KAAK,IAAAtC,MAAA,CAAIkC,OAAO,oCAAkC,CAAC,CAC1E,KAAM,CAAAtD,IAAI,CAAG,KAAM,CAAAL,QAAQ,CAACgE,IAAI,CAAC,CAAC,CAElClE,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEM,IAAI,CAAC,CAE/B,GAAIL,QAAQ,CAACiE,EAAE,CAAE,CACflJ,OAAO,CAACqF,OAAO,+DAAAqB,MAAA,CAAkBzB,QAAQ,CAACJ,MAAM,CAAE,CAAC,CACrD,CAAC,IAAM,CACL7E,OAAO,CAAC6F,KAAK,+DAAAa,MAAA,CAAkBzB,QAAQ,CAACJ,MAAM,CAAE,CAAC,CACnD,CAEA;AACA,GAAIS,IAAI,CAAE,CACRP,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEO,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAAC,CACzCtF,OAAO,CAACmJ,IAAI,0CAAAzC,MAAA,CAAYvB,IAAI,CAACC,SAAS,CAACG,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAAC,CAAE,CAAC,CAC9D,CACF,CAAE,MAAOO,KAAU,CAAE,CACnBd,OAAO,CAACc,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC7F,OAAO,CAAC6F,KAAK,6CAAAa,MAAA,CAAeb,KAAK,CAAC7F,OAAO,CAAE,CAAC,CAC9C,CACF,CAAC,CAED;AACAV,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA8J,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B7G,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACFwC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB;AACA,KAAM,CAACqE,aAAa,CAAEC,gBAAgB,CAAEC,gBAAgB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAC5EvI,cAAc,CAACgE,kBAAkB,CAAAN,aAAA,CAAAA,aAAA,IAC5BlC,WAAW,MACd;AACAmC,MAAM,CAAEnC,WAAW,CAACmC,MAAM,CAC1BhC,QAAQ,CAAE,EAAI;AAAA,EACf,CAAC,CACF3B,cAAc,CAACoH,cAAc,CAAC,CAC5B1F,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EAAI;AAChB,CAAC,CAAC,CACF1B,cAAc,CAACgH,cAAc,CAAC,CAC5BvF,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EAAE,CAAE;AACduF,WAAW,CAAE,UACf,CAAC,CAAC,CACH,CAAC,CAEFrD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxBD,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEG,IAAI,CAACC,SAAS,CAACiE,aAAa,CAAC,CAAC,CACrDtE,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEG,IAAI,CAACC,SAAS,CAACkE,gBAAgB,CAAC,CAAC,CACvDvE,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEG,IAAI,CAACC,SAAS,CAACmE,gBAAgB,CAAC,CAAC,CAEtD;AACA,GAAID,gBAAgB,CAACjE,OAAO,EAAIiE,gBAAgB,CAAChE,IAAI,EAAIgE,gBAAgB,CAAChE,IAAI,CAACI,IAAI,CAAE,CACnF,KAAM,CAAA6C,cAAc,CAAGe,gBAAgB,CAAChE,IAAI,CAACI,IAAI,CAACK,GAAG,CAAEyC,OAAY,EAAA5D,aAAA,CAAAA,aAAA,IAC9D4D,OAAO,MACVC,KAAK,CAAED,OAAO,CAACjC,IAAI,EAAIiC,OAAO,CAACC,KAAK,EACpC,CAAC,CACHpG,WAAW,CAACkG,cAAc,CAAC,CAC3BxD,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEuD,cAAc,CAAC,CAC3C,CAEA;AACA,GAAIgB,gBAAgB,CAAClE,OAAO,EAAIkE,gBAAgB,CAACjE,IAAI,EAAIiE,gBAAgB,CAACjE,IAAI,CAACI,IAAI,CAAE,CACnFvD,WAAW,CAACoH,gBAAgB,CAACjE,IAAI,CAACI,IAAI,CAAC,CACvCX,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEuE,gBAAgB,CAACjE,IAAI,CAACI,IAAI,CAAC,CACtD,CAEA;AACA,GAAI2D,aAAa,CAAChE,OAAO,EAAIgE,aAAa,CAAC/D,IAAI,CAAE,CAC/CP,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEO,MAAM,CAACC,IAAI,CAAC6D,aAAa,CAAC/D,IAAI,CAAC,CAAC,CAEzD;AACA,KAAM,CAAAG,SAAS,CAAG4D,aAAa,CAAC/D,IAAI,CAACI,IAAI,EAAI,EAAE,CAC/C,GAAI,CAACC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,CAAE,CAC7BV,OAAO,CAACc,KAAK,CAAC,WAAW,CAAEJ,SAAS,CAAC,CACrCxD,eAAe,CAAC,EAAE,CAAC,CACnBQ,QAAQ,CAAC,CAAC,CAAC,CACX,OACF,CAEAsC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAES,SAAS,CAAC,CAEnC,KAAM,CAAAK,WAAW,CAAGL,SAAS,CAACM,GAAG,CAAEC,IAAS,EAAK,CAC/C;AACA,KAAM,CAAAC,OAAO,CAAGD,IAAI,CAACC,OAAO,EAAI,CAAC,CAAC,CAClClB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEgB,IAAI,CAACG,EAAE,CAAE,OAAO,CAAEF,OAAO,CAAC,CAEhD;AACA,KAAM,CAAApB,MAAM,CAAG,MAAO,CAAAmB,IAAI,CAACnB,MAAM,GAAK,QAAQ,CAAGmB,IAAI,CAACnB,MAAM,CAC9CmB,IAAI,CAACnB,MAAM,GAAK,SAAS,CAAG/C,iBAAiB,CAACqC,OAAO,CACrD6B,IAAI,CAACnB,MAAM,GAAK,SAAS,CAAG/C,iBAAiB,CAACwC,OAAO,CACrD0B,IAAI,CAACnB,MAAM,GAAK,MAAM,CAAG/C,iBAAiB,CAACyC,IAAI,CAC/CyB,IAAI,CAACnB,MAAM,GAAK,QAAQ,CAAG/C,iBAAiB,CAAC0C,MAAM,CACnDwB,IAAI,CAACnB,MAAM,GAAK,WAAW,CAAG/C,iBAAiB,CAAC2C,SAAS,CACzD3C,iBAAiB,CAACqC,OAAO,CAEvC,MAAO,CACLgC,EAAE,CAAEH,IAAI,CAACG,EAAE,CACXC,SAAS,CAAEJ,IAAI,CAACI,SAAS,CACzBC,SAAS,CAAEL,IAAI,CAACK,SAAS,CACzBC,WAAW,CAAEL,OAAO,CAACM,IAAI,EAAIP,IAAI,CAACM,WAAW,EAAI,MAAM,CACvDE,WAAW,CAAEP,OAAO,CAACQ,IAAI,EAAIT,IAAI,CAACQ,WAAW,iBAAAE,MAAA,CAASV,IAAI,CAACK,SAAS,CAAE,CACtEM,QAAQ,CAAEV,OAAO,CAACU,QAAQ,EAAIX,IAAI,CAACW,QAAQ,EAAI,CAAC,CAChDC,IAAI,CAAEX,OAAO,CAACW,IAAI,EAAIZ,IAAI,CAACY,IAAI,EAAI,GAAG,CACtCC,MAAM,CAAEZ,OAAO,CAACY,MAAM,EAAIb,IAAI,CAACa,MAAM,EAAI,EAAE,CAC3C;AACAC,aAAa,CAAEd,IAAI,CAACe,UAAU,EAAIf,IAAI,CAACc,aAAa,EAAI,CAAC,CACzDE,YAAY,CAAEhB,IAAI,CAACgB,YAAY,EAAIhB,IAAI,CAACe,UAAU,EAAIf,IAAI,CAACc,aAAa,EAAI,CAAC,CAC7EG,YAAY,CAAEjB,IAAI,CAACiB,YAAY,EAAI,CAAC,CACpCC,YAAY,CAAElB,IAAI,CAACmB,SAAS,EAAInB,IAAI,CAACkB,YAAY,EAAI,EAAE,CACvDE,QAAQ,CAAEpB,IAAI,CAACqB,SAAS,EAAIrB,IAAI,CAACoB,QAAQ,EAAI,CAAC,CAC9CE,aAAa,CAAEtB,IAAI,CAACuB,UAAU,EAAIvB,IAAI,CAACsB,aAAa,EAAI,EAAE,CAC1DzC,MAAM,CAAEA,MAAM,CACd2C,SAAS,CAAExB,IAAI,CAACwB,SAAS,CACzBC,SAAS,CAAEzB,IAAI,CAACyB,SAAS,CACzBC,QAAQ,CAAEzB,OAAO,CAACyB,QAAQ,EAAI1B,IAAI,CAAC0B,QAAQ,EAAI,EAAE,CACjDC,OAAO,CAAE1B,OAAO,CAAC2B,YAAY,EAAI5B,IAAI,CAAC2B,OAAO,EAAI,EACnD,CAAC,CACH,CAAC,CAAC,CAEF5C,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEc,WAAW,CAAC,CACvC7D,eAAe,CAAC6D,WAAW,CAAC,CAC5BrD,QAAQ,CAAC4G,aAAa,CAAC/D,IAAI,CAAC9C,KAAK,EAAI,CAAC,CAAC,CAEvC;AACA,KAAM,CAAAe,YAAY,CAAGuC,WAAW,CAAC+B,MAAM,CAAC7B,IAAI,EAAIA,IAAI,CAACnB,MAAM,GAAK/C,iBAAiB,CAACqC,OAAO,CAAC,CAAC2D,MAAM,CACjG,KAAM,CAAAtE,YAAY,CAAGsC,WAAW,CAAC+B,MAAM,CAAC7B,IAAI,EAAIA,IAAI,CAACnB,MAAM,GAAK/C,iBAAiB,CAACwC,OAAO,CAAC,CAACwD,MAAM,CACjG,KAAM,CAAArE,SAAS,CAAGqC,WAAW,CAAC+B,MAAM,CAAC7B,IAAI,EAAIA,IAAI,CAACnB,MAAM,GAAK/C,iBAAiB,CAACyC,IAAI,CAAC,CAACuD,MAAM,CAC3F,KAAM,CAAApE,UAAU,CAAGoC,WAAW,CAACiC,MAAM,CAAC,CAACC,GAAG,CAAEhC,IAAI,GAAKgC,GAAG,CAAGhC,IAAI,CAACgB,YAAY,CAAE,CAAC,CAAC,CAEhF3D,aAAa,CAAC,CACZC,UAAU,CAAEwC,WAAW,CAACgC,MAAM,CAC9BvE,YAAY,CACZC,YAAY,CACZC,SAAS,CACTC,UACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLqB,OAAO,CAACkD,IAAI,CAAC,aAAa,CAAEoB,aAAa,CAAC,CAC1CpH,eAAe,CAAC,EAAE,CAAC,CACnBQ,QAAQ,CAAC,CAAC,CAAC,CACb,CACF,CAAE,MAAOoD,KAAK,CAAE,CACdd,OAAO,CAACc,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChC7F,OAAO,CAAC6F,KAAK,CAAC,gBAAgB,CAAC,CACjC,CAAC,OAAS,CACRtD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,GACEG,WAAW,CAACE,IAAI,GAAK,CAAC,EACtB,CAACF,WAAW,CAAC0D,SAAS,EACtB,CAAC1D,WAAW,CAAC4D,WAAW,EACxB,CAAC5D,WAAW,CAACmC,MAAM,CACnB,CACAuE,QAAQ,CAAC,CAAC,CACZ,CAAC,IAAM,CACL;AACA1E,iBAAiB,CAAC,CAAC,CACrB,CACF;AACA,CAAC,CAAE,CAAChC,WAAW,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAgH,YAAY,CAAIC,MAAW,EAAK,CACpChH,cAAc,CAAAiC,aAAA,CAAAA,aAAA,CAAAA,aAAA,IACTlC,WAAW,EACXiH,MAAM,MACT/G,IAAI,CAAE,CAAC,EACR,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAgH,WAAW,CAAGA,CAAA,GAAM,CACxBjH,cAAc,CAAC,CACbC,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAgH,SAAS,CAAGA,CAAA,GAAM,CACtB5G,cAAc,CAAC,IAAI,CAAC,CACpBU,IAAI,CAACmG,WAAW,CAAC,CAAC,CAClB7F,gBAAgB,CAAC,CAAC,CAClBlB,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAAgH,UAAU,CAAI/D,IAAiB,EAAK,CACxC/C,cAAc,CAAC+C,IAAI,CAAC,CACpBrC,IAAI,CAACqG,cAAc,CAAChE,IAAI,CAAC,CACzB/B,gBAAgB,CAAC,CAAC,CAClBlB,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAAkH,YAAY,CAAG,KAAO,CAAA9D,EAAU,EAAK,CACzC,GAAI,CACF;AACAnG,OAAO,CAACqF,OAAO,CAAC,MAAM,CAAC,CACvBX,iBAAiB,CAAC,CAAC,CACrB,CAAE,MAAOmB,KAAU,CAAE,CACnB7F,OAAO,CAAC6F,KAAK,CAACA,KAAK,CAAC7F,OAAO,EAAI,MAAM,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAAkK,UAAU,CAAG,KAAO,CAAAP,MAAW,EAAK,CACxCxG,SAAS,CAAC,IAAI,CAAC,CACfc,gBAAgB,CAAC,CAAC,CAElB,GAAI,CACF,GAAI,CAAAgB,QAAQ,CACZ,GAAIjC,WAAW,CAAE,CACf;AACAe,YAAY,CAAC,qBAAqB,CAAC,CACnCZ,SAAS,CAAC,KAAK,CAAC,CAChB,OACF,CAAC,IAAM,CACL;AACA8B,QAAQ,CAAG,KAAM,CAAA/D,cAAc,CAACiJ,cAAc,CAACR,MAAM,CAACvD,SAAS,CAAE,CAC/DC,SAAS,CAAEsD,MAAM,CAACtD,SAAS,CAC3BS,aAAa,CAAE6C,MAAM,CAAC7C,aAAa,CACnCG,YAAY,CAAE0C,MAAM,CAAC1C,YAAY,CACjCC,YAAY,CAAEyC,MAAM,CAACzC,YAAY,CACjCkD,SAAS,CAAET,MAAM,CAACS,SAAS,CAAGT,MAAM,CAACS,SAAS,CAACC,WAAW,CAAC,CAAC,CAAGvF,SACjE,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAwF,UAAU,CAAG,WAAW,CAE9B,GAAIhJ,iBAAiB,CAAC2D,QAAQ,CAAElB,YAAY,CAAEC,cAAc,CAAEsG,UAAU,CAAC,CAAE,CACzE;AACAC,UAAU,CAAC,IAAM,CACfxH,iBAAiB,CAAC,KAAK,CAAC,CACxBY,IAAI,CAACmG,WAAW,CAAC,CAAC,CAClB7G,cAAc,CAAC,IAAI,CAAC,CACpBgB,gBAAgB,CAAC,CAAC,CAClBS,iBAAiB,CAAC,CAAC,CACnBgE,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAE,MAAO7C,KAAU,CAAE,CACnBtE,cAAc,CAACsE,KAAK,CAAE9B,YAAY,CAAC,CACrC,CAAC,OAAS,CACRZ,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED,mBACExB,KAAA,QAAK6I,KAAK,CAAE,CAAEC,OAAO,CAAE,EAAG,CAAE,CAAAC,QAAA,eAC1BjJ,IAAA,CAACG,KAAK,EAAC+I,KAAK,CAAE,CAAE,CAAAD,QAAA,CAAC,sCAAM,CAAO,CAAC,cAG/B/I,KAAA,CAACzB,GAAG,EAAC0K,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eAC3CjJ,IAAA,CAACtB,GAAG,EAAC2K,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACzBjJ,IAAA,CAAClC,IAAI,EAAAmL,QAAA,cACHjJ,IAAA,CAAClB,SAAS,EACRkI,KAAK,CAAC,0BAAM,CACZwC,KAAK,CAAE7H,UAAU,CAACE,UAAW,CAC7B4H,MAAM,cAAEzJ,IAAA,CAACR,aAAa,GAAE,CAAE,CAC1BkK,UAAU,CAAE,CAAE9G,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN5C,IAAA,CAACtB,GAAG,EAAC2K,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACzBjJ,IAAA,CAAClC,IAAI,EAAAmL,QAAA,cACHjJ,IAAA,CAAClB,SAAS,EACRkI,KAAK,CAAC,oBAAK,CACXwC,KAAK,CAAE7H,UAAU,CAACG,YAAa,CAC/B4H,UAAU,CAAE,CAAE9G,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN5C,IAAA,CAACtB,GAAG,EAAC2K,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACzBjJ,IAAA,CAAClC,IAAI,EAAAmL,QAAA,cACHjJ,IAAA,CAAClB,SAAS,EACRkI,KAAK,CAAC,oBAAK,CACXwC,KAAK,CAAE7H,UAAU,CAACI,YAAa,CAC/B2H,UAAU,CAAE,CAAE9G,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN5C,IAAA,CAACtB,GAAG,EAAC2K,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACzBjJ,IAAA,CAAClC,IAAI,EAAAmL,QAAA,cACHjJ,IAAA,CAAClB,SAAS,EACRkI,KAAK,CAAC,oBAAK,CACXwC,KAAK,CAAE7H,UAAU,CAACK,SAAU,CAC5B0H,UAAU,CAAE,CAAE9G,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGN5C,IAAA,CAAClC,IAAI,EAAC6L,SAAS,CAAC,aAAa,CAACC,IAAI,CAAC,OAAO,CAACb,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,cACrEjJ,IAAA,CAAC1B,IAAI,EACHuL,MAAM,CAAC,QAAQ,CACfC,QAAQ,CAAE7B,YAAa,CACvB8B,YAAY,CAAC,KAAK,CAAAd,QAAA,cAElB/I,KAAA,CAACzB,GAAG,EAAC0K,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACJ,KAAK,CAAE,CAAEiB,KAAK,CAAE,MAAO,CAAE,CAAAf,QAAA,eAC9CjJ,IAAA,CAACtB,GAAG,EAAC2K,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACzBjJ,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EAACnF,IAAI,CAAC,aAAa,CAACnC,KAAK,CAAC,0BAAM,CAAAsG,QAAA,cACxCjJ,IAAA,CAAC9B,KAAK,EAACgM,WAAW,CAAC,4CAAS,CAACC,UAAU,MAAE,CAAC,CACjC,CAAC,CACT,CAAC,cACNnK,IAAA,CAACtB,GAAG,EAAC2K,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACzBjJ,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EAACnF,IAAI,CAAC,QAAQ,CAACnC,KAAK,CAAC,0BAAM,CAAAsG,QAAA,cACnC/I,KAAA,CAAC/B,MAAM,EAAC+L,WAAW,CAAC,4CAAS,CAACC,UAAU,MAAAlB,QAAA,eACtCjJ,IAAA,CAACI,MAAM,EAACoJ,KAAK,CAAEnJ,iBAAiB,CAACqC,OAAQ,CAAAuG,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACtDjJ,IAAA,CAACI,MAAM,EAACoJ,KAAK,CAAEnJ,iBAAiB,CAACwC,OAAQ,CAAAoG,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACtDjJ,IAAA,CAACI,MAAM,EAACoJ,KAAK,CAAEnJ,iBAAiB,CAACyC,IAAK,CAAAmG,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACnDjJ,IAAA,CAACI,MAAM,EAACoJ,KAAK,CAAEnJ,iBAAiB,CAAC0C,MAAO,CAAAkG,QAAA,CAAC,cAAE,CAAQ,CAAC,cACpDjJ,IAAA,CAACI,MAAM,EAACoJ,KAAK,CAAEnJ,iBAAiB,CAAC2C,SAAU,CAAAiG,QAAA,CAAC,cAAE,CAAQ,CAAC,EACjD,CAAC,CACA,CAAC,CACT,CAAC,cACNjJ,IAAA,CAACtB,GAAG,EAAC2K,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACzBjJ,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EAACnF,IAAI,CAAC,WAAW,CAACnC,KAAK,CAAC,oBAAK,CAAAsG,QAAA,cACrCjJ,IAAA,CAAC7B,MAAM,EAAC+L,WAAW,CAAC,sCAAQ,CAACC,UAAU,MAAAlB,QAAA,CACpCtI,QAAQ,CAAC2D,GAAG,CAACyC,OAAO,eACnB/G,IAAA,CAACI,MAAM,EAAkBoJ,KAAK,CAAEzC,OAAO,CAACrC,EAAG,CAAAuE,QAAA,CACxClC,OAAO,CAACC,KAAK,EADHD,OAAO,CAACrC,EAEb,CACT,CAAC,CACI,CAAC,CACA,CAAC,CACT,CAAC,cACN1E,IAAA,CAACtB,GAAG,EAAC2K,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACzBjJ,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EAAAhB,QAAA,cACR/I,KAAA,CAACjC,KAAK,EAAAgL,QAAA,eACJjJ,IAAA,CAAChC,MAAM,EAACoM,IAAI,CAAC,SAAS,CAACC,QAAQ,CAAC,QAAQ,CAACC,IAAI,cAAEtK,IAAA,CAACb,cAAc,GAAE,CAAE,CAAA8J,QAAA,CAAC,cAEnE,CAAQ,CAAC,cACTjJ,IAAA,CAAChC,MAAM,EAACuM,OAAO,CAAEpC,WAAY,CAACmC,IAAI,cAAEtK,IAAA,CAACT,cAAc,GAAE,CAAE,CAAA0J,QAAA,CAAC,cAExD,CAAQ,CAAC,EACJ,CAAC,CACC,CAAC,CACT,CAAC,EACH,CAAC,CACF,CAAC,CACH,CAAC,cAGPjJ,IAAA,CAAClC,IAAI,EAAC6L,SAAS,CAAC,aAAa,CAACC,IAAI,CAAC,OAAO,CAACb,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,cACrE/I,KAAA,CAACzB,GAAG,EAAC+L,OAAO,CAAC,eAAe,CAACC,KAAK,CAAC,QAAQ,CAAAxB,QAAA,eACzCjJ,IAAA,CAACtB,GAAG,EAAAuK,QAAA,cACFjJ,IAAA,CAAChC,MAAM,EACLoM,IAAI,CAAC,SAAS,CACdE,IAAI,cAAEtK,IAAA,CAACd,YAAY,GAAE,CAAE,CACvBqL,OAAO,CAAEnC,SAAU,CAAAa,QAAA,CACpB,sCAED,CAAQ,CAAC,CACN,CAAC,cACNjJ,IAAA,CAACtB,GAAG,EAAAuK,QAAA,cACFjJ,IAAA,CAAChC,MAAM,EACLsM,IAAI,cAAEtK,IAAA,CAACT,cAAc,GAAE,CAAE,CACzBgL,OAAO,CAAEtH,iBAAkB,CAC3BpC,OAAO,CAAEA,OAAQ,CAAAoI,QAAA,CAClB,cAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACF,CAAC,cAGP/I,KAAA,CAACpC,IAAI,EAAAmL,QAAA,eAEH/I,KAAA,QAAK6I,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eAC/B/I,KAAA,MAAA+I,QAAA,EAAG,wCAAQ,CAACpI,OAAO,CAAG,QAAQ,WAAAoE,MAAA,CAAQ1E,YAAY,CAAC8F,MAAM,uBAAM,EAAI,CAAC,CACnE9F,YAAY,CAAC8F,MAAM,GAAK,CAAC,EAAI,CAACxF,OAAO,eACpCb,IAAA,MAAG+I,KAAK,CAAE,CAAEnG,KAAK,CAAE,KAAM,CAAE,CAAAqG,QAAA,CAAC,sIAAsB,CAAG,CACtD,cAEDjJ,IAAA,CAAChC,MAAM,EACLoM,IAAI,CAAC,SAAS,CACdG,OAAO,CAAEA,CAAA,GAAM,CACbjH,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEhD,YAAY,CAAC,CACtChC,OAAO,CAACmJ,IAAI,0CAAAzC,MAAA,CAAY1E,YAAY,CAAC8F,MAAM,CAAE,CAAC,CAChD,CAAE,CACF0C,KAAK,CAAE,CAAE2B,WAAW,CAAE,CAAE,CAAE,CAAAzB,QAAA,CAC3B,0BAED,CAAQ,CAAC,cACTjJ,IAAA,CAAChC,MAAM,EACLoM,IAAI,CAAC,SAAS,CACdG,OAAO,CAAErD,kBAAmB,CAC5B6B,KAAK,CAAE,CAAE2B,WAAW,CAAE,CAAE,CAAE,CAAAzB,QAAA,CAC3B,6BAED,CAAQ,CAAC,cACTjJ,IAAA,CAAChC,MAAM,EACLuM,OAAO,CAAEtH,iBAAkB,CAC3B8F,KAAK,CAAE,CAAE2B,WAAW,CAAE,CAAE,CAAE,CAAAzB,QAAA,CAC3B,sCAED,CAAQ,CAAC,EACN,CAAC,cAENjJ,IAAA,CAACjC,KAAK,EACJ4M,OAAO,CAAE,CACP,CACE3D,KAAK,CAAE,IAAI,CACX4D,SAAS,CAAE,IAAI,CACfC,GAAG,CAAE,IAAI,CACTb,KAAK,CAAE,EACT,CAAC,CACD,CACEhD,KAAK,CAAE,MAAM,CACb6D,GAAG,CAAE,SAAS,CACdb,KAAK,CAAE,GAAG,CACVc,MAAM,CAAEA,CAACC,CAAC,CAAEC,MAAmB,gBAC7B9K,KAAA,QAAK6I,KAAK,CAAE,CAAEkC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAjC,QAAA,EACnD+B,MAAM,CAAC5F,MAAM,EAAI4F,MAAM,CAAC5F,MAAM,CAACiB,MAAM,CAAG,CAAC,eACxCrG,IAAA,CAACpB,KAAK,EACJoL,KAAK,CAAE,EAAG,CACVmB,MAAM,CAAE,EAAG,CACXC,GAAG,CAAEJ,MAAM,CAAC5F,MAAM,CAAC,CAAC,CAAE,CACtB2D,KAAK,CAAE,CAAE2B,WAAW,CAAE,EAAE,CAAEW,YAAY,CAAE,CAAE,CAAE,CAC5CC,QAAQ,CAAC,goBAAgoB,CAC1oB,CACF,cACDpL,KAAA,QAAA+I,QAAA,eACEjJ,IAAA,QAAK+I,KAAK,CAAE,CAAEwC,UAAU,CAAE,GAAI,CAAE,CAAAtC,QAAA,CAAE+B,MAAM,CAACnG,WAAW,CAAM,CAAC,cAC3D3E,KAAA,QAAK6I,KAAK,CAAE,CAAEyC,QAAQ,CAAE,EAAE,CAAE5I,KAAK,CAAE,MAAO,CAAE,CAAAqG,QAAA,EAAC,gBACvC,CAAC+B,MAAM,CAACjG,WAAW,EACpB,CAAC,cACN7E,KAAA,QAAK6I,KAAK,CAAE,CAAEyC,QAAQ,CAAE,EAAE,CAAE5I,KAAK,CAAE,MAAO,CAAE,CAAAqG,QAAA,EACzC+B,MAAM,CAAC9F,QAAQ,CAAC,GAAC,CAAC8F,MAAM,CAAC7F,IAAI,EAC3B,CAAC,EACH,CAAC,EACH,CAET,CAAC,CACD,CACE6B,KAAK,CAAE,MAAM,CACb4D,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbb,KAAK,CAAE,GAAG,CACVc,MAAM,CAAG1H,MAAyB,EAAK,CACrCE,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEH,MAAM,CAAE,MAAO,CAAAA,MAAM,CAAC,CAC3C;AACA,KAAM,CAAAqI,SAAS,CAAG,MAAO,CAAArI,MAAM,GAAK,QAAQ,CAAGA,MAAM,CACpCA,MAAM,GAAK,SAAS,CAAG/C,iBAAiB,CAACqC,OAAO,CAChDU,MAAM,GAAK,SAAS,CAAG/C,iBAAiB,CAACwC,OAAO,CAChDO,MAAM,GAAK,MAAM,CAAG/C,iBAAiB,CAACyC,IAAI,CAC1CM,MAAM,GAAK,QAAQ,CAAG/C,iBAAiB,CAAC0C,MAAM,CAC9CK,MAAM,GAAK,WAAW,CAAG/C,iBAAiB,CAAC2C,SAAS,CACpD3C,iBAAiB,CAACqC,OAAO,CAE1C,KAAM,CAAAgJ,UAAU,CAAGjJ,aAAa,CAACgJ,SAAS,CAAC,CAC3CnI,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEmI,UAAU,CAAC,CAEhC,mBACE1L,IAAA,CAACjB,KAAK,EACJqE,MAAM,CACJqI,SAAS,GAAKpL,iBAAiB,CAACwC,OAAO,CAAG,YAAY,CACtD4I,SAAS,GAAKpL,iBAAiB,CAACyC,IAAI,CAAG,SAAS,CAChD2I,SAAS,GAAKpL,iBAAiB,CAAC0C,MAAM,CAAG,SAAS,CAClD0I,SAAS,GAAKpL,iBAAiB,CAAC2C,SAAS,CAAG,OAAO,CAAG,SACvD,CACD2I,IAAI,cACF3L,IAAA,CAAC5B,GAAG,EAACwE,KAAK,CAAE,CAAA8I,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE9I,KAAK,GAAI,SAAU,CAAAqG,QAAA,CACxC,CAAAyC,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE/I,KAAK,mBAAAsC,MAAA,CAAUwG,SAAS,KAAG,CACrC,CACN,CACF,CAAC,CAEN,CACF,CAAC,CACD,CACEzE,KAAK,CAAE,MAAM,CACb6D,GAAG,CAAE,OAAO,CACZb,KAAK,CAAE,GAAG,CACVc,MAAM,CAAEA,CAACC,CAAC,CAAEC,MAAmB,gBAC7B9K,KAAA,QAAA+I,QAAA,eACE/I,KAAA,QAAA+I,QAAA,EAAK,oBAAK,CAAC+B,MAAM,CAAC3F,aAAa,CAACuG,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,cACjD1L,KAAA,QAAK6I,KAAK,CAAE,CAAEnG,KAAK,CAAE,MAAM,CAAE2I,UAAU,CAAE,GAAI,CAAE,CAAAtC,QAAA,EAAC,oBACzC,CAAC+B,MAAM,CAACzF,YAAY,CAACqG,OAAO,CAAC,CAAC,CAAC,EACjC,CAAC,CACLZ,MAAM,CAACxF,YAAY,eAClBtF,KAAA,QAAK6I,KAAK,CAAE,CAAEyC,QAAQ,CAAE,EAAE,CAAE5I,KAAK,CAAE,MAAO,CAAE,CAAAqG,QAAA,EAAC,oBACtC,CAAC+B,MAAM,CAACxF,YAAY,CAACoG,OAAO,CAAC,CAAC,CAAC,EACjC,CACN,EACE,CAET,CAAC,CACD,CACE5E,KAAK,CAAE,MAAM,CACb6D,GAAG,CAAE,KAAK,CACVb,KAAK,CAAE,GAAG,CACVc,MAAM,CAAEA,CAACC,CAAC,CAAEC,MAAmB,gBAC7B9K,KAAA,QAAA+I,QAAA,eACE/I,KAAA,QAAA+I,QAAA,EAAK,4BAAM,CAAC+B,MAAM,CAACrF,QAAQ,EAAM,CAAC,cAClCzF,KAAA,QAAA+I,QAAA,EAAK,gCAAO,CAAC+B,MAAM,CAACvF,YAAY,CAACmG,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,CACjDZ,MAAM,CAACnF,aAAa,eACnB3F,KAAA,QAAK6I,KAAK,CAAE,CAAEyC,QAAQ,CAAE,EAAE,CAAE5I,KAAK,CAAE,MAAO,CAAE,CAAAqG,QAAA,EAAC,kCACpC,CAAC+B,MAAM,CAACnF,aAAa,EACzB,CACN,EACE,CAET,CAAC,CACD,CACEmB,KAAK,CAAE,MAAM,CACb4D,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBb,KAAK,CAAE,GAAG,CACVc,MAAM,CAAGa,IAAY,EAAK,GAAI,CAAAE,IAAI,CAACF,IAAI,CAAC,CAACG,cAAc,CAAC,CAC1D,CAAC,CACD,CACE9E,KAAK,CAAE,IAAI,CACX6D,GAAG,CAAE,QAAQ,CACbb,KAAK,CAAE,GAAG,CACV+B,KAAK,CAAE,OAAO,CACdjB,MAAM,CAAEA,CAACC,CAAC,CAAEC,MAAmB,gBAC7B9K,KAAA,CAACjC,KAAK,EAAC2L,IAAI,CAAC,OAAO,CAAAX,QAAA,eACjBjJ,IAAA,CAAChB,OAAO,EAACgI,KAAK,CAAC,0BAAM,CAAAiC,QAAA,cACnBjJ,IAAA,CAAChC,MAAM,EACLoM,IAAI,CAAC,MAAM,CACXR,IAAI,CAAC,OAAO,CACZU,IAAI,cAAEtK,IAAA,CAACV,WAAW,GAAE,CAAE,CACtBiL,OAAO,CAAEA,CAAA,GAAM,CAAC,WAAY,CAC7B,CAAC,CACK,CAAC,cACVvK,IAAA,CAAChB,OAAO,EAACgI,KAAK,CAAC,cAAI,CAAAiC,QAAA,cACjBjJ,IAAA,CAAChC,MAAM,EACLoM,IAAI,CAAC,MAAM,CACXR,IAAI,CAAC,OAAO,CACZU,IAAI,cAAEtK,IAAA,CAACZ,YAAY,GAAE,CAAE,CACvBmL,OAAO,CAAEA,CAAA,GAAMjC,UAAU,CAAC0C,MAAM,CAAE,CAClCgB,QAAQ,CAAEhB,MAAM,CAAC5H,MAAM,GAAK/C,iBAAiB,CAACwC,OAAQ,CACvD,CAAC,CACK,CAAC,cACV7C,IAAA,CAACnB,UAAU,EACTmI,KAAK,CAAC,gFAAe,CACrBiF,SAAS,CAAEA,CAAA,GAAMzD,YAAY,CAACwC,MAAM,CAACtG,EAAE,CAAE,CACzCwH,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAlD,QAAA,cAEfjJ,IAAA,CAAChB,OAAO,EAACgI,KAAK,CAAC,cAAI,CAAAiC,QAAA,cACjBjJ,IAAA,CAAChC,MAAM,EACLoM,IAAI,CAAC,MAAM,CACXR,IAAI,CAAC,OAAO,CACZwC,MAAM,MACN9B,IAAI,cAAEtK,IAAA,CAACX,cAAc,GAAE,CAAE,CACzB2M,QAAQ,CAAEhB,MAAM,CAAC5H,MAAM,GAAK/C,iBAAiB,CAACwC,OAAQ,CACvD,CAAC,CACK,CAAC,CACA,CAAC,EACR,CAEX,CAAC,CACD,CACFwJ,UAAU,CAAE9L,YAAa,CACzB+L,MAAM,CAAC,IAAI,CACXzL,OAAO,CAAEA,OAAQ,CACjB0L,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBC,UAAU,CAAE,CACVC,OAAO,CAAEzL,WAAW,CAACE,IAAI,CACzBC,QAAQ,CAAEH,WAAW,CAACG,QAAQ,CAC9BL,KAAK,CAAEA,KAAK,CACZ4L,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAAC9L,KAAK,CAAE+L,KAAK,aAAA7H,MAAA,CACjB6H,KAAK,CAAC,CAAC,CAAC,MAAA7H,MAAA,CAAI6H,KAAK,CAAC,CAAC,CAAC,oBAAA7H,MAAA,CAAQlE,KAAK,WAAI,CAC5CgM,QAAQ,CAAEA,CAAC5L,IAAI,CAAEC,QAAQ,GAAK,CAC5BF,cAAc,CAAAiC,aAAA,CAAAA,aAAA,IACTlC,WAAW,MACdE,IAAI,CACJC,QAAQ,CAAEA,QAAQ,EAAI,EAAE,EACzB,CAAC,CACJ,CACF,CAAE,CACH,CAAC,EACE,CAAC,cAGPpB,IAAA,CAAC3B,KAAK,EACJ2I,KAAK,CAAEzF,WAAW,CAAG,QAAQ,CAAG,QAAS,CACzCyL,IAAI,CAAE3L,cAAe,CACrB4L,QAAQ,CAAEA,CAAA,GAAM3L,iBAAiB,CAAC,KAAK,CAAE,CACzC4L,MAAM,CAAE,IAAK,CACblD,KAAK,CAAE,GAAI,CAAAf,QAAA,cAEX/I,KAAA,CAAC5B,IAAI,EACH4D,IAAI,CAAEA,IAAK,CACX2H,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAErB,UAAW,CACrBsB,YAAY,CAAC,KAAK,CAAAd,QAAA,eAElBjJ,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EACRnF,IAAI,CAAC,WAAW,CAChBnC,KAAK,CAAC,oBAAK,CACXwK,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE7O,OAAO,CAAE,QAAS,CAAC,CAAE,CAAA0K,QAAA,cAE/CjJ,IAAA,CAAC7B,MAAM,EAAC+L,WAAW,CAAC,sCAAQ,CAAAjB,QAAA,CACzBtI,QAAQ,CAAC2D,GAAG,CAACyC,OAAO,eACnB/G,IAAA,CAACI,MAAM,EAAkBoJ,KAAK,CAAEzC,OAAO,CAACrC,EAAG,CAAAuE,QAAA,CACxClC,OAAO,CAACC,KAAK,EADHD,OAAO,CAACrC,EAEb,CACT,CAAC,CACI,CAAC,CACA,CAAC,cAEZ1E,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EACRnF,IAAI,CAAC,WAAW,CAChBnC,KAAK,CAAC,cAAI,CACVwK,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE7O,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAA0K,QAAA,cAE9CjJ,IAAA,CAAC7B,MAAM,EAAC+L,WAAW,CAAC,gCAAO,CAACmD,UAAU,MAACC,gBAAgB,CAAC,UAAU,CAAArE,QAAA,CAC/DxI,QAAQ,CAAC6D,GAAG,CAACE,OAAO,eACnBtE,KAAA,CAACE,MAAM,EAAkBoJ,KAAK,CAAEhF,OAAO,CAACE,EAAG,CAAAuE,QAAA,EACxCzE,OAAO,CAACM,IAAI,CAAC,KAAG,CAACN,OAAO,CAACQ,IAAI,GADnBR,OAAO,CAACE,EAEb,CACT,CAAC,CACI,CAAC,CACA,CAAC,cAEZxE,KAAA,CAACzB,GAAG,EAAC0K,MAAM,CAAE,EAAG,CAAAF,QAAA,eACdjJ,IAAA,CAACtB,GAAG,EAAC6O,IAAI,CAAE,EAAG,CAAAtE,QAAA,cACZjJ,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EACRnF,IAAI,CAAC,eAAe,CACpBnC,KAAK,CAAC,oBAAK,CACXwK,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAE7O,OAAO,CAAE,QAAS,CAAC,CACrC,CAAE6L,IAAI,CAAE,QAAQ,CAAEoD,GAAG,CAAE,IAAI,CAAEjP,OAAO,CAAE,UAAW,CAAC,CAClD,CAAA0K,QAAA,cAEFjJ,IAAA,CAACrB,WAAW,EACVoK,KAAK,CAAE,CAAEiB,KAAK,CAAE,MAAO,CAAE,CACzBE,WAAW,CAAC,sCAAQ,CACpBuD,SAAS,CAAE,CAAE,CACbD,GAAG,CAAE,IAAK,CACVE,WAAW,CAAC,MAAG,CAChB,CAAC,CACO,CAAC,CACT,CAAC,cACN1N,IAAA,CAACtB,GAAG,EAAC6O,IAAI,CAAE,EAAG,CAAAtE,QAAA,cACZjJ,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EACRnF,IAAI,CAAC,cAAc,CACnBnC,KAAK,CAAC,4CAAS,CACfwK,KAAK,CAAE,CACL,CAAE/C,IAAI,CAAE,QAAQ,CAAEoD,GAAG,CAAE,IAAI,CAAEjP,OAAO,CAAE,UAAW,CAAC,CAClD,CAAA0K,QAAA,cAEFjJ,IAAA,CAACrB,WAAW,EACVoK,KAAK,CAAE,CAAEiB,KAAK,CAAE,MAAO,CAAE,CACzBE,WAAW,CAAC,sCAAQ,CACpBuD,SAAS,CAAE,CAAE,CACbD,GAAG,CAAE,IAAK,CACVE,WAAW,CAAC,MAAG,CAChB,CAAC,CACO,CAAC,CACT,CAAC,EACH,CAAC,cAEN1N,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EACRnF,IAAI,CAAC,cAAc,CACnBnC,KAAK,CAAC,0BAAM,CACZwK,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAE7O,OAAO,CAAE,SAAU,CAAC,CACtC,CAAE6L,IAAI,CAAE,QAAQ,CAAEoD,GAAG,CAAE,IAAI,CAAEjP,OAAO,CAAE,WAAY,CAAC,CACnD,CAAA0K,QAAA,cAEFjJ,IAAA,CAACrB,WAAW,EACVoK,KAAK,CAAE,CAAEiB,KAAK,CAAE,MAAO,CAAE,CACzBE,WAAW,CAAC,4CAAS,CACrBuD,SAAS,CAAE,CAAE,CACbD,GAAG,CAAE,IAAK,CACVE,WAAW,CAAC,MAAG,CAChB,CAAC,CACO,CAAC,cAEZ1N,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EACRnF,IAAI,CAAC,WAAW,CAChBnC,KAAK,CAAC,0BAAM,CACZwK,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAE7O,OAAO,CAAE,SAAU,CAAC,CACtC,CAAA0K,QAAA,cAEFjJ,IAAA,CAACf,UAAU,EACT8J,KAAK,CAAE,CAAEiB,KAAK,CAAE,MAAO,CAAE,CACzB2D,QAAQ,MACRzD,WAAW,CAAC,4CAAS,CACrB0D,MAAM,CAAC,qBAAqB,CAC7B,CAAC,CACO,CAAC,cAGZ5N,IAAA,CAACL,WAAW,EAACyK,IAAI,CAAC,OAAO,CAAC7L,OAAO,CAAE6D,SAAU,CAACyL,OAAO,CAAE,CAAC,CAACzL,SAAU,CAAE,CAAC,cACtEpC,IAAA,CAACL,WAAW,EAACyK,IAAI,CAAC,SAAS,CAAC7L,OAAO,CAAE8D,WAAY,CAACwL,OAAO,CAAE,CAAC,CAACxL,WAAY,CAAE,CAAC,cAE5ErC,IAAA,CAAC1B,IAAI,CAAC2L,IAAI,EAAAhB,QAAA,cACR/I,KAAA,CAACjC,KAAK,EAAC8K,KAAK,CAAE,CAAEiB,KAAK,CAAE,MAAM,CAAE8D,cAAc,CAAE,UAAW,CAAE,CAAA7E,QAAA,eAC1DjJ,IAAA,CAAChC,MAAM,EACLuM,OAAO,CAAEA,CAAA,GAAM,CACbjJ,iBAAiB,CAAC,KAAK,CAAC,CACxBY,IAAI,CAACmG,WAAW,CAAC,CAAC,CAClB7G,cAAc,CAAC,IAAI,CAAC,CACpBgB,gBAAgB,CAAC,CAAC,CACpB,CAAE,CACFwJ,QAAQ,CAAEvK,MAAO,CAAAwH,QAAA,CAClB,cAED,CAAQ,CAAC,cACTjJ,IAAA,CAAChC,MAAM,EACLoM,IAAI,CAAC,SAAS,CACdC,QAAQ,CAAC,QAAQ,CACjBxJ,OAAO,CAAEY,MAAO,CAChBuK,QAAQ,CAAEvK,MAAO,CAAAwH,QAAA,CAEhBxH,MAAM,CAAG,QAAQ,CAAIF,WAAW,CAAG,IAAI,CAAG,IAAK,CAC1C,CAAC,EACJ,CAAC,CACC,CAAC,EACR,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}