{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport authSlice from './slices/authSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice\n  },\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: {\n      ignoredActions: ['persist/PERSIST']\n    }\n  })\n});", "map": {"version": 3, "names": ["configureStore", "authSlice", "store", "reducer", "auth", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/store/index.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport authSlice from './slices/authSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST'],\n      },\n    }),\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,SAAS,MAAM,oBAAoB;AAE1C,OAAO,MAAMC,KAAK,GAAGF,cAAc,CAAC;EAClCG,OAAO,EAAE;IACPC,IAAI,EAAEH;EACR,CAAC;EACDI,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAAC;IACnBC,iBAAiB,EAAE;MACjBC,cAAc,EAAE,CAAC,iBAAiB;IACpC;EACF,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}