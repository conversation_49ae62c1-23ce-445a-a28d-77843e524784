{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Typography, Row, Col, DatePicker, Statistic, Badge } from 'antd';\nimport { PlusOutlined, SearchOutlined, EyeOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, PauseCircleOutlined, StopOutlined, ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { auctionService } from '../../../services/auctionService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';\nimport dayjs from 'dayjs';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\n\n// 拍卖会状态枚举\nexport let AuctionStatus = /*#__PURE__*/function (AuctionStatus) {\n  AuctionStatus[AuctionStatus[\"DRAFT\"] = 1] = \"DRAFT\";\n  // 草稿\n  AuctionStatus[AuctionStatus[\"SCHEDULED\"] = 2] = \"SCHEDULED\";\n  // 已安排\n  AuctionStatus[AuctionStatus[\"ONGOING\"] = 3] = \"ONGOING\";\n  // 进行中\n  AuctionStatus[AuctionStatus[\"PAUSED\"] = 4] = \"PAUSED\";\n  // 已暂停\n  AuctionStatus[AuctionStatus[\"COMPLETED\"] = 5] = \"COMPLETED\";\n  // 已完成\n  AuctionStatus[AuctionStatus[\"CANCELLED\"] = 6] = \"CANCELLED\"; // 已取消\n  return AuctionStatus;\n}({});\n\n// 拍卖会数据接口\n\n// 查询参数接口\n\nconst AuctionList = () => {\n  _s();\n  var _auctionStatusMap$vie, _auctionStatusMap$vie2;\n  const [auctions, setAuctions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isViewModalVisible, setIsViewModalVisible] = useState(false);\n  const [editingAuction, setEditingAuction] = useState(null);\n  const [viewingAuction, setViewingAuction] = useState(null);\n  const [saving, setSaving] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalAuctions: 0,\n    ongoingAuctions: 0,\n    todayAuctions: 0,\n    totalParticipants: 0\n  });\n  const [auctioneers, setAuctioneers] = useState([]);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 拍卖会状态映射\n  const auctionStatusMap = {\n    [AuctionStatus.DRAFT]: {\n      label: '草稿',\n      color: 'default'\n    },\n    [AuctionStatus.SCHEDULED]: {\n      label: '已安排',\n      color: 'blue'\n    },\n    [AuctionStatus.ONGOING]: {\n      label: '进行中',\n      color: 'green'\n    },\n    [AuctionStatus.PAUSED]: {\n      label: '已暂停',\n      color: 'orange'\n    },\n    [AuctionStatus.COMPLETED]: {\n      label: '已完成',\n      color: 'purple'\n    },\n    [AuctionStatus.CANCELLED]: {\n      label: '已取消',\n      color: 'red'\n    }\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    setLoading(true);\n    try {\n      const response = await auctionService.getAuctionList(queryParams);\n      if (response.success) {\n        // 将后端的name字段映射为前端的title字段\n        const mappedAuctions = response.data.list.map(auction => ({\n          ...auction,\n          title: auction.name || auction.title,\n          // 后端返回name，前端使用title\n          totalItems: auction.totalItems || 0,\n          totalAmount: auction.totalAmount || 0,\n          participantCount: auction.participantCount || 0,\n          soldItems: auction.soldItems || 0,\n          creatorName: auction.creatorName || '未知',\n          // 确保auctioneerId字段被保留\n          auctioneerId: auction.auctioneerId\n        }));\n        setAuctions(mappedAuctions);\n        setTotal(response.data.total);\n        console.log('获取到拍卖会列表:', mappedAuctions);\n      } else {\n        message.error(response.message || '获取拍卖会列表失败');\n        setAuctions([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      console.error('获取拍卖会列表失败:', error);\n      let errorMsg = '获取拍卖会列表失败';\n      if (error.response) {\n        const {\n          status\n        } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问拍卖会列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setAuctions([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取拍卖会统计\n  const fetchStatistics = async () => {\n    try {\n      const response = await auctionService.getAuctionStatistics();\n      if (response.success) {\n        setStatistics(response.data);\n      }\n    } catch (error) {\n      console.error('获取拍卖会统计失败:', error);\n    }\n  };\n\n  // 获取拍卖师列表\n  const fetchAuctioneers = async () => {\n    try {\n      // 获取拍卖师类型的用户 (user_type=1)\n      const response = await fetch('http://localhost:8081/api/v1/users?user_type=1&page=1&pageSize=100');\n      const data = await response.json();\n\n      // 处理用户API的响应格式：{success: true, data: {list: []}}\n      if (data.success && data.data && data.data.list) {\n        // 过滤出拍卖师用户（userType=1）\n        const auctioneerUsers = data.data.list.filter(user => user.userType === 1);\n        const auctioneerList = auctioneerUsers.map(user => ({\n          id: user.id,\n          name: user.realName || user.username\n        }));\n        setAuctioneers(auctioneerList);\n        console.log('获取到拍卖师列表:', auctioneerList);\n      } else {\n        console.warn('拍卖师数据格式异常:', data);\n        setAuctioneers([]);\n      }\n    } catch (error) {\n      console.error('获取拍卖师列表失败:', error);\n      setAuctioneers([]);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchAuctions();\n    fetchStatistics();\n    fetchAuctioneers();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增拍卖会\n  const handleAdd = () => {\n    setEditingAuction(null);\n    form.resetFields();\n\n    // 设置默认值，不默认选择拍卖师\n    form.setFieldsValue({\n      // 默认时间范围为当前时间后的一天\n      timeRange: [dayjs().add(1, 'hour'), dayjs().add(1, 'day')]\n    });\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 查看拍卖会详情\n  const handleView = auction => {\n    setViewingAuction(auction);\n    setIsViewModalVisible(true);\n  };\n\n  // 编辑拍卖会\n  const handleEdit = async auction => {\n    setEditingAuction(auction);\n    try {\n      // 获取拍卖会详情，以获取更完整的信息（包括auctioneerId）\n      const response = await auctionService.getAuctionDetail(auction.id);\n      if (response.success) {\n        const auctionDetail = response.data;\n        // 使用详情中的auctioneerId，如果存在的话\n        const auctioneerId = auctionDetail.auctioneerId || auctionDetail.auctioneerID || null;\n        console.log('拍卖会详情:', auctionDetail);\n        console.log('拍卖师ID:', auctioneerId);\n        form.setFieldsValue({\n          title: auction.title,\n          // 前端表单使用title字段\n          description: auction.description,\n          location: auction.location || '',\n          // 确保location有值\n          auctioneerID: auctioneerId,\n          // 设置拍卖师ID，如果没有则为null\n          timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)]\n        });\n      } else {\n        // 如果获取详情失败，使用现有数据\n        form.setFieldsValue({\n          title: auction.title,\n          description: auction.description,\n          location: auction.location || '',\n          auctioneerID: auction.auctioneerId || null,\n          // 尝试使用列表中的auctioneerId\n          timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)]\n        });\n      }\n    } catch (error) {\n      console.error('获取拍卖会详情失败:', error);\n      // 出错时使用现有数据\n      form.setFieldsValue({\n        title: auction.title,\n        description: auction.description,\n        location: auction.location || '',\n        auctioneerID: auction.auctioneerId || null,\n        // 尝试使用列表中的auctioneerId\n        timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)]\n      });\n    }\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖会\n  const handleDelete = async id => {\n    try {\n      const response = await auctionService.deleteAuction(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖会\n  const handleSave = async values => {\n    setSaving(true);\n    clearAllMessages();\n    try {\n      const auctionData = {\n        name: values.title,\n        // 将title字段映射为name\n        description: values.description,\n        auctioneerId: values.auctioneerID,\n        // 将前端的auctioneerID映射为后端的auctioneerId\n        location: values.location,\n        startTime: values.timeRange[0].toISOString(),\n        endTime: values.timeRange[1].toISOString()\n      };\n      let response;\n      if (editingAuction) {\n        response = await auctionService.updateAuction(editingAuction.id, auctionData);\n      } else {\n        response = await auctionService.createAuction(auctionData);\n      }\n      const successMsg = editingAuction ? '拍卖会信息更新成功！' : '拍卖会创建成功！';\n      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingAuction(null);\n          clearAllMessages();\n          fetchAuctions();\n          fetchStatistics(); // 刷新统计数据\n        }, 1500);\n      }\n    } catch (error) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 开始拍卖\n  const handleStart = async id => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n    Modal.confirm({\n      title: '确认开始拍卖',\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u786E\\u5B9A\\u8981\\u5F00\\u59CB\\u62CD\\u5356\\u4F1A \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"\\\"\", auction.title, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 23\n          }, this), \" \\u5417\\uFF1F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#999',\n            fontSize: '12px'\n          },\n          children: \"\\u5F00\\u59CB\\u540E\\u5C06\\u65E0\\u6CD5\\u4FEE\\u6539\\u62CD\\u5356\\u4FE1\\u606F\\uFF0C\\u8BF7\\u786E\\u4FDD\\u6240\\u6709\\u8BBE\\u7F6E\\u6B63\\u786E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this),\n      okText: '确认开始',\n      cancelText: '取消',\n      okButtonProps: {\n        type: 'primary'\n      },\n      onOk: async () => {\n        try {\n          const response = await auctionService.startAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已成功开始！`,\n              duration: 3\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '开始拍卖失败，请稍后重试',\n              duration: 5\n            });\n          }\n        } catch (error) {\n          var _error$response, _error$response2;\n          console.error('开始拍卖失败:', error);\n          let errorMsg = '开始拍卖失败';\n          if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 400) {\n            errorMsg = '拍卖会状态不允许开始，请检查拍卖设置';\n          } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 409) {\n            errorMsg = '拍卖会已经开始或存在冲突';\n          } else {\n            errorMsg = error.message || '网络错误，请检查连接后重试';\n          }\n          message.error({\n            content: errorMsg,\n            duration: 5\n          });\n        }\n      }\n    });\n  };\n\n  // 暂停拍卖\n  const handlePause = async id => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n    Modal.confirm({\n      title: '确认暂停拍卖',\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u786E\\u5B9A\\u8981\\u6682\\u505C\\u62CD\\u5356\\u4F1A \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"\\\"\", auction.title, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 23\n          }, this), \" \\u5417\\uFF1F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#999',\n            fontSize: '12px'\n          },\n          children: \"\\u6682\\u505C\\u540E\\u53EF\\u4EE5\\u91CD\\u65B0\\u5F00\\u59CB\\uFF0C\\u4F46\\u4F1A\\u5F71\\u54CD\\u53C2\\u4E0E\\u8005\\u7684\\u7ADE\\u62CD\\u4F53\\u9A8C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this),\n      okText: '确认暂停',\n      cancelText: '取消',\n      okButtonProps: {\n        type: 'default'\n      },\n      onOk: async () => {\n        try {\n          const response = await auctionService.pauseAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已暂停`,\n              duration: 3\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '暂停拍卖失败，请稍后重试',\n              duration: 5\n            });\n          }\n        } catch (error) {\n          console.error('暂停拍卖失败:', error);\n          message.error({\n            content: error.message || '暂停拍卖失败，请检查网络连接后重试',\n            duration: 5\n          });\n        }\n      }\n    });\n  };\n\n  // 结束拍卖\n  const handleEnd = async id => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n    Modal.confirm({\n      title: '确认结束拍卖',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 13\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u786E\\u5B9A\\u8981\\u7ED3\\u675F\\u62CD\\u5356\\u4F1A \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"\\\"\", auction.title, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 23\n          }, this), \" \\u5417\\uFF1F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#ff4d4f',\n            fontSize: '12px',\n            fontWeight: 'bold'\n          },\n          children: \"\\u26A0\\uFE0F \\u8B66\\u544A\\uFF1A\\u7ED3\\u675F\\u540E\\u5C06\\u65E0\\u6CD5\\u6062\\u590D\\uFF0C\\u8BF7\\u786E\\u4FDD\\u6240\\u6709\\u4EA4\\u6613\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this),\n      okText: '确认结束',\n      cancelText: '取消',\n      okButtonProps: {\n        danger: true\n      },\n      onOk: async () => {\n        try {\n          const response = await auctionService.endAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已成功结束！`,\n              duration: 3\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '结束拍卖失败，请稍后重试',\n              duration: 5\n            });\n          }\n        } catch (error) {\n          console.error('结束拍卖失败:', error);\n          message.error({\n            content: error.message || '结束拍卖失败，请检查网络连接后重试',\n            duration: 5\n          });\n        }\n      }\n    });\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '拍卖会标题',\n    dataIndex: 'title',\n    key: 'title',\n    width: 200,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontWeight: 500\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '拍卖状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => {\n      const statusInfo = auctionStatusMap[status];\n      return /*#__PURE__*/_jsxDEV(Badge, {\n        status: status === AuctionStatus.ONGOING ? 'processing' : status === AuctionStatus.COMPLETED ? 'success' : status === AuctionStatus.CANCELLED ? 'error' : 'default',\n        text: /*#__PURE__*/_jsxDEV(Tag, {\n          color: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || 'default',\n          children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.label) || '未知'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '商品数量',\n    dataIndex: 'totalItems',\n    key: 'totalItems',\n    width: 100,\n    render: (total, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [total, \"\\u4EF6\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 12,\n          color: '#999'\n        },\n        children: [\"\\u5DF2\\u552E: \", record.soldItems]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '成交金额',\n    dataIndex: 'totalAmount',\n    key: 'totalAmount',\n    width: 120,\n    render: amount => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontWeight: 500,\n        color: '#f50'\n      },\n      children: [\"\\xA5\", amount.toFixed(2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '参与人数',\n    dataIndex: 'participantCount',\n    key: 'participantCount',\n    width: 100\n  }, {\n    title: '创建人',\n    dataIndex: 'creatorName',\n    key: 'creatorName',\n    width: 100\n  }, {\n    title: '拍卖时间',\n    dataIndex: 'startTime',\n    key: 'startTime',\n    width: 160,\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: new Date(text).toLocaleString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 12,\n          color: '#999'\n        },\n        children: [\"\\u81F3 \", new Date(record.endTime).toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleView(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 11\n      }, this), record.status === AuctionStatus.SCHEDULED && /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 21\n        }, this),\n        onClick: () => handleStart(record.id),\n        children: \"\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 13\n      }, this), record.status === AuctionStatus.ONGOING && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(PauseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 23\n          }, this),\n          onClick: () => handlePause(record.id),\n          children: \"\\u6682\\u505C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 23\n          }, this),\n          onClick: () => handleEnd(record.id),\n          children: \"\\u7ED3\\u675F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auction-list-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u62CD\\u5356\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u62CD\\u5356\\u4F1A\",\n            value: statistics.totalAuctions,\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FDB\\u884C\\u4E2D\",\n            value: statistics.ongoingAuctions,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4ECA\\u65E5\\u62CD\\u5356\",\n            value: statistics.todayAuctions,\n            valueStyle: {\n              color: '#cf1322'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u53C2\\u4E0E\\u4EBA\\u6570\",\n            value: statistics.totalParticipants,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 693,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 665,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: searchForm,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u62CD\\u5356\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.DRAFT,\n                  children: \"\\u8349\\u7A3F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.SCHEDULED,\n                  children: \"\\u5DF2\\u5B89\\u6392\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.ONGOING,\n                  children: \"\\u8FDB\\u884C\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.PAUSED,\n                  children: \"\\u5DF2\\u6682\\u505C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.COMPLETED,\n                  children: \"\\u5DF2\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.CANCELLED,\n                  children: \"\\u5DF2\\u53D6\\u6D88\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"creatorName\",\n              label: \"\\u521B\\u5EFA\\u4EBA\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u521B\\u5EFA\\u4EBA\\u59D3\\u540D\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dateRange\",\n              label: \"\\u62CD\\u5356\\u65F6\\u95F4\",\n              children: /*#__PURE__*/_jsxDEV(RangePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 705,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u65B0\\u589E\\u62CD\\u5356\\u4F1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 21\n            }, this),\n            onClick: fetchAuctions,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: auctions,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [editingAuction ? /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 50\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: editingAuction ? `编辑拍卖会 - ${editingAuction.title}` : '新增拍卖会'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 11\n      }, this),\n      open: isModalVisible,\n      onCancel: () => {\n        if (saving) {\n          message.warning('正在保存中，请稍候...');\n          return;\n        }\n        setIsModalVisible(false);\n        form.resetFields();\n        setEditingAuction(null);\n      },\n      footer: null,\n      width: 700,\n      destroyOnClose: true,\n      maskClosable: !saving,\n      closable: !saving,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",\n          rules: [{\n            required: true,\n            message: '请输入拍卖会标题'\n          }, {\n            min: 2,\n            max: 100,\n            message: '标题长度为2-100个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",\n            showCount: true,\n            maxLength: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u62CD\\u5356\\u4F1A\\u63CF\\u8FF0\",\n          rules: [{\n            max: 500,\n            message: '描述不能超过500个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u63CF\\u8FF0\",\n            rows: 4,\n            showCount: true,\n            maxLength: 500\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"timeRange\",\n          label: \"\\u62CD\\u5356\\u65F6\\u95F4\",\n          rules: [{\n            required: true,\n            message: '请选择拍卖时间'\n          }, {\n            validator: (_, value) => {\n              if (!value || !value[0] || !value[1]) {\n                return Promise.resolve();\n              }\n              const [start, end] = value;\n              const now = new Date();\n\n              // 检查开始时间不能早于当前时间\n              if (start.isBefore(now)) {\n                return Promise.reject(new Error('开始时间不能早于当前时间'));\n              }\n\n              // 检查结束时间必须晚于开始时间\n              if (end.isBefore(start)) {\n                return Promise.reject(new Error('结束时间必须晚于开始时间'));\n              }\n\n              // 检查拍卖时长不能少于30分钟\n              const duration = end.diff(start, 'minutes');\n              if (duration < 30) {\n                return Promise.reject(new Error('拍卖时长不能少于30分钟'));\n              }\n\n              // 检查拍卖时长不能超过24小时\n              if (duration > 24 * 60) {\n                return Promise.reject(new Error('拍卖时长不能超过24小时'));\n              }\n              return Promise.resolve();\n            }\n          }],\n          extra: \"\\u62CD\\u5356\\u65F6\\u957F\\u5EFA\\u8BAE\\u572830\\u5206\\u949F\\u523024\\u5C0F\\u65F6\\u4E4B\\u95F4\",\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            showTime: {\n              format: 'HH:mm',\n              minuteStep: 15 // 15分钟间隔\n            },\n            format: \"YYYY-MM-DD HH:mm\",\n            style: {\n              width: '100%'\n            },\n            placeholder: ['选择开始时间', '选择结束时间'],\n            disabledDate: current => {\n              // 禁用今天之前的日期\n              return current && current.isBefore(new Date(), 'day');\n            },\n            disabledTime: (current, type) => {\n              const now = new Date();\n              const isToday = current && current.isSame(now, 'day');\n              if (type === 'start' && isToday) {\n                // 如果是今天，禁用当前时间之前的时间\n                return {\n                  disabledHours: () => {\n                    const hours = [];\n                    for (let i = 0; i < now.getHours(); i++) {\n                      hours.push(i);\n                    }\n                    return hours;\n                  },\n                  disabledMinutes: selectedHour => {\n                    if (selectedHour === now.getHours()) {\n                      const minutes = [];\n                      for (let i = 0; i < now.getMinutes(); i++) {\n                        minutes.push(i);\n                      }\n                      return minutes;\n                    }\n                    return [];\n                  }\n                };\n              }\n              return {};\n            },\n            showNow: false,\n            allowClear: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"location\",\n          label: \"\\u62CD\\u5356\\u5730\\u70B9\",\n          rules: [{\n            required: true,\n            message: '请输入拍卖地点'\n          }, {\n            min: 2,\n            max: 200,\n            message: '地点长度为2-200个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u5730\\u70B9\",\n            showCount: true,\n            maxLength: 200\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 960,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 952,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"auctioneerID\",\n          label: \"\\u62CD\\u5356\\u5E08\",\n          rules: [{\n            required: true,\n            message: '请选择拍卖师'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u5E08\",\n            showSearch: true,\n            optionFilterProp: \"children\",\n            filterOption: (input, option) => {\n              var _option$children;\n              return String((_option$children = option === null || option === void 0 ? void 0 : option.children) !== null && _option$children !== void 0 ? _option$children : '').toLowerCase().includes(input.toLowerCase());\n            },\n            children: auctioneers.map(auctioneer => /*#__PURE__*/_jsxDEV(Option, {\n              value: auctioneer.id,\n              children: auctioneer.name\n            }, auctioneer.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 967,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormMessage, {\n          type: \"error\",\n          message: formError,\n          visible: !!formError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormMessage, {\n          type: \"success\",\n          message: formSuccess,\n          visible: !!formSuccess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0,\n            marginTop: '24px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderTop: '1px solid #f0f0f0',\n              paddingTop: '16px',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#666',\n                fontSize: '12px'\n              },\n              children: editingAuction ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建拍卖会'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => {\n                  if (saving) {\n                    message.warning('正在保存中，请稍候...');\n                    return;\n                  }\n                  setIsModalVisible(false);\n                  form.resetFields();\n                  setEditingAuction(null);\n                  clearAllMessages();\n                },\n                disabled: saving,\n                size: \"middle\",\n                children: \"\\u53D6\\u6D88\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: saving,\n                disabled: saving,\n                size: \"middle\",\n                icon: editingAuction ? /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 42\n                }, this) : /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 61\n                }, this),\n                children: saving ? '保存中...' : editingAuction ? '更新拍卖会' : '创建拍卖会'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1022,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 808,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u62CD\\u5356\\u4F1A\\u8BE6\\u60C5\",\n      open: isViewModalVisible,\n      onCancel: () => {\n        setIsViewModalVisible(false);\n        setViewingAuction(null);\n      },\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          setIsViewModalVisible(false);\n          setViewingAuction(null);\n        },\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 1047,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: viewingAuction && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u62CD\\u5356\\u4F1A\\u6807\\u9898\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: viewingAuction.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1059,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u62CD\\u5356\\u72B6\\u6001\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1067,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: /*#__PURE__*/_jsxDEV(Tag, {\n                  color: ((_auctionStatusMap$vie = auctionStatusMap[viewingAuction.status]) === null || _auctionStatusMap$vie === void 0 ? void 0 : _auctionStatusMap$vie.color) || 'default',\n                  children: ((_auctionStatusMap$vie2 = auctionStatusMap[viewingAuction.status]) === null || _auctionStatusMap$vie2 === void 0 ? void 0 : _auctionStatusMap$vie2.label) || '未知'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u62CD\\u5356\\u63CF\\u8FF0\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4,\n                  padding: '8px',\n                  backgroundColor: '#f5f5f5',\n                  borderRadius: '4px'\n                },\n                children: viewingAuction.description || '暂无描述'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1076,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1075,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u62CD\\u5356\\u5730\\u70B9\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1085,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: viewingAuction.location || '暂无地点信息'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1086,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1083,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u521B\\u5EFA\\u4EBA\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1091,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: viewingAuction.creatorName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1090,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1089,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5F00\\u59CB\\u65F6\\u95F4\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1097,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: new Date(viewingAuction.startTime).toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1096,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1095,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u7ED3\\u675F\\u65F6\\u95F4\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: new Date(viewingAuction.endTime).toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5546\\u54C1\\u603B\\u6570\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4,\n                  fontSize: '18px',\n                  color: '#1890ff'\n                },\n                children: [viewingAuction.totalItems, \"\\u4EF6\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1107,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5DF2\\u552E\\u5546\\u54C1\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4,\n                  fontSize: '18px',\n                  color: '#52c41a'\n                },\n                children: [viewingAuction.soldItems, \"\\u4EF6\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1116,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1114,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1113,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u53C2\\u4E0E\\u4EBA\\u6570\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1121,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4,\n                  fontSize: '18px',\n                  color: '#722ed1'\n                },\n                children: [viewingAuction.participantCount, \"\\u4EBA\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u6210\\u4EA4\\u91D1\\u989D\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1127,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4,\n                  fontSize: '24px',\n                  color: '#f50',\n                  fontWeight: 'bold'\n                },\n                children: [\"\\xA5\", viewingAuction.totalAmount.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1128,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1125,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1058,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1057,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1039,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 661,\n    columnNumber: 5\n  }, this);\n};\n_s(AuctionList, \"O8Pph13aUWdLNK3mW9oi17Q/jjQ=\", false, function () {\n  return [Form.useForm, Form.useForm, useFormMessage];\n});\n_c = AuctionList;\nexport default AuctionList;\nvar _c;\n$RefreshReg$(_c, \"AuctionList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Typography", "Row", "Col", "DatePicker", "Statistic", "Badge", "PlusOutlined", "SearchOutlined", "EyeOutlined", "EditOutlined", "DeleteOutlined", "PlayCircleOutlined", "PauseCircleOutlined", "StopOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "auctionService", "FormMessage", "useFormMessage", "handleApiResponse", "handleApiError", "dayjs", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Option", "RangePicker", "AuctionStatus", "AuctionList", "_s", "_auctionStatusMap$vie", "_auctionStatusMap$vie2", "auctions", "setAuctions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "isViewModalVisible", "setIsViewModalVisible", "editingAuction", "setEditingAuction", "viewingAuction", "setViewingAuction", "saving", "setSaving", "statistics", "setStatistics", "totalAuctions", "ongoingAuctions", "todayAuctions", "totalParticipants", "auctioneers", "setAuctioneers", "form", "useForm", "searchForm", "formError", "formSuccess", "setFormError", "setFormSuccess", "clearAllMessages", "auctionStatusMap", "DRAFT", "label", "color", "SCHEDULED", "ONGOING", "PAUSED", "COMPLETED", "CANCELLED", "fetchAuctions", "response", "getAuctionList", "success", "mappedAuctions", "data", "list", "map", "auction", "title", "name", "totalItems", "totalAmount", "participantCount", "soldItems", "<PERSON><PERSON><PERSON>", "auctioneerId", "console", "log", "error", "errorMsg", "status", "fetchStatistics", "getAuctionStatistics", "fetchAuctioneers", "fetch", "json", "auctioneerUsers", "filter", "user", "userType", "auctioneerList", "id", "realName", "username", "warn", "handleSearch", "values", "handleReset", "resetFields", "handleAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeRange", "add", "handleView", "handleEdit", "getAuctionDetail", "auctionDetail", "auctioneerID", "description", "location", "startTime", "endTime", "handleDelete", "deleteAuction", "handleSave", "auctionData", "toISOString", "updateAuction", "createAuction", "successMsg", "setTimeout", "handleStart", "find", "a", "confirm", "content", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "okText", "cancelText", "okButtonProps", "type", "onOk", "startAuction", "duration", "_error$response", "_error$response2", "handlePause", "pauseAuction", "handleEnd", "icon", "fontWeight", "danger", "endAuction", "columns", "dataIndex", "key", "width", "render", "text", "statusInfo", "record", "amount", "toFixed", "Date", "toLocaleString", "fixed", "_", "size", "onClick", "className", "level", "gutter", "marginBottom", "xs", "sm", "md", "value", "valueStyle", "layout", "onFinish", "autoComplete", "<PERSON><PERSON>", "placeholder", "allowClear", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "display", "alignItems", "gap", "open", "onCancel", "warning", "footer", "destroyOnClose", "maskClosable", "closable", "rules", "required", "min", "max", "showCount", "max<PERSON><PERSON><PERSON>", "TextArea", "rows", "validator", "Promise", "resolve", "start", "end", "now", "isBefore", "reject", "Error", "diff", "extra", "showTime", "format", "minuteStep", "disabledDate", "disabledTime", "isToday", "isSame", "disabledHours", "hours", "i", "getHours", "push", "disabledMinutes", "selected<PERSON>our", "minutes", "getMinutes", "showNow", "showSearch", "optionFilterProp", "filterOption", "input", "option", "_option$children", "String", "toLowerCase", "includes", "auctioneer", "visible", "marginTop", "borderTop", "paddingTop", "justifyContent", "disabled", "padding", "span", "backgroundColor", "borderRadius", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Typography,\n  Row,\n  Col,\n  DatePicker,\n  Statistic,\n  Badge,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  ReloadOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { auctionService } from '../../../services/auctionService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';\nimport dayjs from 'dayjs';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\n// 拍卖会状态枚举\nexport enum AuctionStatus {\n  DRAFT = 1,        // 草稿\n  SCHEDULED = 2,    // 已安排\n  ONGOING = 3,      // 进行中\n  PAUSED = 4,       // 已暂停\n  COMPLETED = 5,    // 已完成\n  CANCELLED = 6,    // 已取消\n}\n\n// 拍卖会数据接口\nexport interface Auction {\n  id: number;\n  title: string;\n  description?: string;\n  startTime: string;\n  endTime: string;\n  status: AuctionStatus;\n  totalItems: number;\n  soldItems: number;\n  totalAmount: number;\n  participantCount: number;\n  creatorName: string;\n  location: string;\n  createdAt: string;\n  updatedAt: string;\n  auctioneerID?: number; // 添加可选的auctioneerID字段\n  auctioneerId?: number; // 添加后端实际返回的auctioneerId字段\n}\n\n// 查询参数接口\ninterface AuctionQueryParams {\n  title?: string;\n  status?: AuctionStatus;\n  creatorName?: string;\n  dateRange?: [string, string];\n  page: number;\n  pageSize: number;\n}\n\nconst AuctionList: React.FC = () => {\n  const [auctions, setAuctions] = useState<Auction[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<AuctionQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isViewModalVisible, setIsViewModalVisible] = useState(false);\n  const [editingAuction, setEditingAuction] = useState<Auction | null>(null);\n  const [viewingAuction, setViewingAuction] = useState<Auction | null>(null);\n  const [saving, setSaving] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalAuctions: 0,\n    ongoingAuctions: 0,\n    todayAuctions: 0,\n    totalParticipants: 0,\n  });\n  const [auctioneers, setAuctioneers] = useState<{id: number, name: string}[]>([]);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 拍卖会状态映射\n  const auctionStatusMap = {\n    [AuctionStatus.DRAFT]: { label: '草稿', color: 'default' },\n    [AuctionStatus.SCHEDULED]: { label: '已安排', color: 'blue' },\n    [AuctionStatus.ONGOING]: { label: '进行中', color: 'green' },\n    [AuctionStatus.PAUSED]: { label: '已暂停', color: 'orange' },\n    [AuctionStatus.COMPLETED]: { label: '已完成', color: 'purple' },\n    [AuctionStatus.CANCELLED]: { label: '已取消', color: 'red' },\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    setLoading(true);\n    try {\n      const response = await auctionService.getAuctionList(queryParams);\n      if (response.success) {\n        // 将后端的name字段映射为前端的title字段\n        const mappedAuctions = response.data.list.map((auction: any) => ({\n          ...auction,\n          title: auction.name || auction.title, // 后端返回name，前端使用title\n          totalItems: auction.totalItems || 0,\n          totalAmount: auction.totalAmount || 0,\n          participantCount: auction.participantCount || 0,\n          soldItems: auction.soldItems || 0,\n          creatorName: auction.creatorName || '未知',\n          // 确保auctioneerId字段被保留\n          auctioneerId: auction.auctioneerId,\n        }));\n        setAuctions(mappedAuctions);\n        setTotal(response.data.total);\n        console.log('获取到拍卖会列表:', mappedAuctions);\n      } else {\n        message.error(response.message || '获取拍卖会列表失败');\n        setAuctions([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会列表失败:', error);\n      let errorMsg = '获取拍卖会列表失败';\n      if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问拍卖会列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setAuctions([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取拍卖会统计\n  const fetchStatistics = async () => {\n    try {\n      const response = await auctionService.getAuctionStatistics();\n      if (response.success) {\n        setStatistics(response.data);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会统计失败:', error);\n    }\n  };\n\n  // 获取拍卖师列表\n  const fetchAuctioneers = async () => {\n    try {\n      // 获取拍卖师类型的用户 (user_type=1)\n      const response = await fetch('http://localhost:8081/api/v1/users?user_type=1&page=1&pageSize=100');\n      const data = await response.json();\n\n      // 处理用户API的响应格式：{success: true, data: {list: []}}\n      if (data.success && data.data && data.data.list) {\n        // 过滤出拍卖师用户（userType=1）\n        const auctioneerUsers = data.data.list.filter((user: any) => user.userType === 1);\n        const auctioneerList = auctioneerUsers.map((user: any) => ({\n          id: user.id,\n          name: user.realName || user.username\n        }));\n        setAuctioneers(auctioneerList);\n        console.log('获取到拍卖师列表:', auctioneerList);\n      } else {\n        console.warn('拍卖师数据格式异常:', data);\n        setAuctioneers([]);\n      }\n    } catch (error) {\n      console.error('获取拍卖师列表失败:', error);\n      setAuctioneers([]);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchAuctions();\n    fetchStatistics();\n    fetchAuctioneers();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增拍卖会\n  const handleAdd = () => {\n    setEditingAuction(null);\n    form.resetFields();\n    \n    // 设置默认值，不默认选择拍卖师\n    form.setFieldsValue({\n      // 默认时间范围为当前时间后的一天\n      timeRange: [dayjs().add(1, 'hour'), dayjs().add(1, 'day')],\n    });\n    \n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 查看拍卖会详情\n  const handleView = (auction: Auction) => {\n    setViewingAuction(auction);\n    setIsViewModalVisible(true);\n  };\n\n  // 编辑拍卖会\n  const handleEdit = async (auction: Auction) => {\n    setEditingAuction(auction);\n    try {\n      // 获取拍卖会详情，以获取更完整的信息（包括auctioneerId）\n      const response = await auctionService.getAuctionDetail(auction.id);\n      if (response.success) {\n        const auctionDetail = response.data as Auction & { auctioneerID?: number, auctioneerId?: number };\n        // 使用详情中的auctioneerId，如果存在的话\n        const auctioneerId = auctionDetail.auctioneerId || auctionDetail.auctioneerID || null;\n        \n        console.log('拍卖会详情:', auctionDetail);\n        console.log('拍卖师ID:', auctioneerId);\n        \n    form.setFieldsValue({\n      title: auction.title, // 前端表单使用title字段\n      description: auction.description,\n          location: auction.location || '', // 确保location有值\n          auctioneerID: auctioneerId, // 设置拍卖师ID，如果没有则为null\n      timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],\n    });\n      } else {\n        // 如果获取详情失败，使用现有数据\n        form.setFieldsValue({\n          title: auction.title,\n          description: auction.description,\n          location: auction.location || '',\n          auctioneerID: auction.auctioneerId || null, // 尝试使用列表中的auctioneerId\n          timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],\n        });\n      }\n    } catch (error) {\n      console.error('获取拍卖会详情失败:', error);\n      // 出错时使用现有数据\n      form.setFieldsValue({\n        title: auction.title,\n        description: auction.description,\n        location: auction.location || '',\n        auctioneerID: auction.auctioneerId || null, // 尝试使用列表中的auctioneerId\n        timeRange: [dayjs(auction.startTime), dayjs(auction.endTime)],\n      });\n    }\n    \n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖会\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await auctionService.deleteAuction(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖会\n  const handleSave = async (values: any) => {\n    setSaving(true);\n    clearAllMessages();\n\n    try {\n      const auctionData = {\n        name: values.title, // 将title字段映射为name\n        description: values.description,\n        auctioneerId: values.auctioneerID, // 将前端的auctioneerID映射为后端的auctioneerId\n        location: values.location,\n        startTime: values.timeRange[0].toISOString(),\n        endTime: values.timeRange[1].toISOString(),\n      };\n\n      let response;\n      if (editingAuction) {\n        response = await auctionService.updateAuction(editingAuction.id, auctionData);\n      } else {\n        response = await auctionService.createAuction(auctionData);\n      }\n\n      const successMsg = editingAuction ? '拍卖会信息更新成功！' : '拍卖会创建成功！';\n\n      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingAuction(null);\n          clearAllMessages();\n          fetchAuctions();\n          fetchStatistics(); // 刷新统计数据\n        }, 1500);\n      }\n    } catch (error: any) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 开始拍卖\n  const handleStart = async (id: number) => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n\n    Modal.confirm({\n      title: '确认开始拍卖',\n      content: (\n        <div>\n          <p>确定要开始拍卖会 <strong>\"{auction.title}\"</strong> 吗？</p>\n          <p style={{ color: '#999', fontSize: '12px' }}>\n            开始后将无法修改拍卖信息，请确保所有设置正确\n          </p>\n        </div>\n      ),\n      okText: '确认开始',\n      cancelText: '取消',\n      okButtonProps: { type: 'primary' },\n      onOk: async () => {\n        try {\n          const response = await auctionService.startAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已成功开始！`,\n              duration: 3,\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '开始拍卖失败，请稍后重试',\n              duration: 5,\n            });\n          }\n        } catch (error: any) {\n          console.error('开始拍卖失败:', error);\n          let errorMsg = '开始拍卖失败';\n          if (error.response?.status === 400) {\n            errorMsg = '拍卖会状态不允许开始，请检查拍卖设置';\n          } else if (error.response?.status === 409) {\n            errorMsg = '拍卖会已经开始或存在冲突';\n          } else {\n            errorMsg = error.message || '网络错误，请检查连接后重试';\n          }\n          message.error({\n            content: errorMsg,\n            duration: 5,\n          });\n        }\n      },\n    });\n  };\n\n  // 暂停拍卖\n  const handlePause = async (id: number) => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n\n    Modal.confirm({\n      title: '确认暂停拍卖',\n      content: (\n        <div>\n          <p>确定要暂停拍卖会 <strong>\"{auction.title}\"</strong> 吗？</p>\n          <p style={{ color: '#999', fontSize: '12px' }}>\n            暂停后可以重新开始，但会影响参与者的竞拍体验\n          </p>\n        </div>\n      ),\n      okText: '确认暂停',\n      cancelText: '取消',\n      okButtonProps: { type: 'default' },\n      onOk: async () => {\n        try {\n          const response = await auctionService.pauseAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已暂停`,\n              duration: 3,\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '暂停拍卖失败，请稍后重试',\n              duration: 5,\n            });\n          }\n        } catch (error: any) {\n          console.error('暂停拍卖失败:', error);\n          message.error({\n            content: error.message || '暂停拍卖失败，请检查网络连接后重试',\n            duration: 5,\n          });\n        }\n      },\n    });\n  };\n\n  // 结束拍卖\n  const handleEnd = async (id: number) => {\n    const auction = auctions.find(a => a.id === id);\n    if (!auction) return;\n\n    Modal.confirm({\n      title: '确认结束拍卖',\n      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,\n      content: (\n        <div>\n          <p>确定要结束拍卖会 <strong>\"{auction.title}\"</strong> 吗？</p>\n          <p style={{ color: '#ff4d4f', fontSize: '12px', fontWeight: 'bold' }}>\n            ⚠️ 警告：结束后将无法恢复，请确保所有交易已完成\n          </p>\n        </div>\n      ),\n      okText: '确认结束',\n      cancelText: '取消',\n      okButtonProps: { danger: true },\n      onOk: async () => {\n        try {\n          const response = await auctionService.endAuction(id);\n          if (response.success) {\n            message.success({\n              content: `拍卖会\"${auction.title}\"已成功结束！`,\n              duration: 3,\n            });\n            fetchAuctions();\n          } else {\n            message.error({\n              content: response.message || '结束拍卖失败，请稍后重试',\n              duration: 5,\n            });\n          }\n        } catch (error: any) {\n          console.error('结束拍卖失败:', error);\n          message.error({\n            content: error.message || '结束拍卖失败，请检查网络连接后重试',\n            duration: 5,\n          });\n        }\n      },\n    });\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Auction> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '拍卖会标题',\n      dataIndex: 'title',\n      key: 'title',\n      width: 200,\n      render: (text: string) => (\n        <div style={{ fontWeight: 500 }}>{text}</div>\n      ),\n    },\n    {\n      title: '拍卖状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: AuctionStatus) => {\n        const statusInfo = auctionStatusMap[status];\n        return (\n          <Badge\n            status={\n              status === AuctionStatus.ONGOING ? 'processing' :\n              status === AuctionStatus.COMPLETED ? 'success' :\n              status === AuctionStatus.CANCELLED ? 'error' : 'default'\n            }\n            text={\n              <Tag color={statusInfo?.color || 'default'}>\n                {statusInfo?.label || '未知'}\n              </Tag>\n            }\n          />\n        );\n      },\n    },\n    {\n      title: '商品数量',\n      dataIndex: 'totalItems',\n      key: 'totalItems',\n      width: 100,\n      render: (total: number, record: Auction) => (\n        <div>\n          <div>{total}件</div>\n          <div style={{ fontSize: 12, color: '#999' }}>\n            已售: {record.soldItems}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '成交金额',\n      dataIndex: 'totalAmount',\n      key: 'totalAmount',\n      width: 120,\n      render: (amount: number) => (\n        <div style={{ fontWeight: 500, color: '#f50' }}>\n          ¥{amount.toFixed(2)}\n        </div>\n      ),\n    },\n    {\n      title: '参与人数',\n      dataIndex: 'participantCount',\n      key: 'participantCount',\n      width: 100,\n    },\n    {\n      title: '创建人',\n      dataIndex: 'creatorName',\n      key: 'creatorName',\n      width: 100,\n    },\n    {\n      title: '拍卖时间',\n      dataIndex: 'startTime',\n      key: 'startTime',\n      width: 160,\n      render: (text: string, record: Auction) => (\n        <div>\n          <div>{new Date(text).toLocaleString()}</div>\n          <div style={{ fontSize: 12, color: '#999' }}>\n            至 {new Date(record.endTime).toLocaleString()}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right',\n      render: (_, record: Auction) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleView(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          {record.status === AuctionStatus.SCHEDULED && (\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<PlayCircleOutlined />}\n              onClick={() => handleStart(record.id)}\n            >\n              开始\n            </Button>\n          )}\n          {record.status === AuctionStatus.ONGOING && (\n            <>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<PauseCircleOutlined />}\n                onClick={() => handlePause(record.id)}\n              >\n                暂停\n              </Button>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<StopOutlined />}\n                onClick={() => handleEnd(record.id)}\n              >\n                结束\n              </Button>\n            </>\n          )}\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n          >\n            删除\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"auction-list-container\">\n      <Title level={2}>拍卖管理</Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总拍卖会\"\n              value={statistics.totalAuctions}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"进行中\"\n              value={statistics.ongoingAuctions}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"今日拍卖\"\n              value={statistics.todayAuctions}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总参与人数\"\n              value={statistics.totalParticipants}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          form={searchForm}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"title\" label=\"拍卖会标题\">\n                <Input placeholder=\"请输入拍卖会标题\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"拍卖状态\">\n                <Select placeholder=\"请选择拍卖状态\" allowClear>\n                  <Option value={AuctionStatus.DRAFT}>草稿</Option>\n                  <Option value={AuctionStatus.SCHEDULED}>已安排</Option>\n                  <Option value={AuctionStatus.ONGOING}>进行中</Option>\n                  <Option value={AuctionStatus.PAUSED}>已暂停</Option>\n                  <Option value={AuctionStatus.COMPLETED}>已完成</Option>\n                  <Option value={AuctionStatus.CANCELLED}>已取消</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"creatorName\" label=\"创建人\">\n                <Input placeholder=\"请输入创建人姓名\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"dateRange\" label=\"拍卖时间\">\n                <RangePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={4}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              新增拍卖会\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchAuctions}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 拍卖会列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={auctions}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 拍卖会编辑模态框 */}\n      <Modal\n        title={\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            {editingAuction ? <EditOutlined /> : <PlusOutlined />}\n            <span>{editingAuction ? `编辑拍卖会 - ${editingAuction.title}` : '新增拍卖会'}</span>\n          </div>\n        }\n        open={isModalVisible}\n        onCancel={() => {\n          if (saving) {\n            message.warning('正在保存中，请稍候...');\n            return;\n          }\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingAuction(null);\n        }}\n        footer={null}\n        width={700}\n        destroyOnClose\n        maskClosable={!saving}\n        closable={!saving}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Form.Item\n            name=\"title\"\n            label=\"拍卖会标题\"\n            rules={[\n              { required: true, message: '请输入拍卖会标题' },\n              { min: 2, max: 100, message: '标题长度为2-100个字符' },\n            ]}\n          >\n            <Input\n              placeholder=\"请输入拍卖会标题\"\n              showCount\n              maxLength={100}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"拍卖会描述\"\n            rules={[\n              { max: 500, message: '描述不能超过500个字符' },\n            ]}\n          >\n            <Input.TextArea\n              placeholder=\"请输入拍卖会描述\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"timeRange\"\n            label=\"拍卖时间\"\n            rules={[\n              { required: true, message: '请选择拍卖时间' },\n              {\n                validator: (_, value) => {\n                  if (!value || !value[0] || !value[1]) {\n                    return Promise.resolve();\n                  }\n                  const [start, end] = value;\n                  const now = new Date();\n\n                  // 检查开始时间不能早于当前时间\n                  if (start.isBefore(now)) {\n                    return Promise.reject(new Error('开始时间不能早于当前时间'));\n                  }\n\n                  // 检查结束时间必须晚于开始时间\n                  if (end.isBefore(start)) {\n                    return Promise.reject(new Error('结束时间必须晚于开始时间'));\n                  }\n\n                  // 检查拍卖时长不能少于30分钟\n                  const duration = end.diff(start, 'minutes');\n                  if (duration < 30) {\n                    return Promise.reject(new Error('拍卖时长不能少于30分钟'));\n                  }\n\n                  // 检查拍卖时长不能超过24小时\n                  if (duration > 24 * 60) {\n                    return Promise.reject(new Error('拍卖时长不能超过24小时'));\n                  }\n\n                  return Promise.resolve();\n                },\n              },\n            ]}\n            extra=\"拍卖时长建议在30分钟到24小时之间\"\n          >\n            <RangePicker\n              showTime={{\n                format: 'HH:mm',\n                minuteStep: 15, // 15分钟间隔\n              }}\n              format=\"YYYY-MM-DD HH:mm\"\n              style={{ width: '100%' }}\n              placeholder={['选择开始时间', '选择结束时间']}\n              disabledDate={(current) => {\n                // 禁用今天之前的日期\n                return current && current.isBefore(new Date(), 'day');\n              }}\n              disabledTime={(current, type) => {\n                const now = new Date();\n                const isToday = current && current.isSame(now, 'day');\n\n                if (type === 'start' && isToday) {\n                  // 如果是今天，禁用当前时间之前的时间\n                  return {\n                    disabledHours: () => {\n                      const hours = [];\n                      for (let i = 0; i < now.getHours(); i++) {\n                        hours.push(i);\n                      }\n                      return hours;\n                    },\n                    disabledMinutes: (selectedHour: number) => {\n                      if (selectedHour === now.getHours()) {\n                        const minutes = [];\n                        for (let i = 0; i < now.getMinutes(); i++) {\n                          minutes.push(i);\n                        }\n                        return minutes;\n                      }\n                      return [];\n                    },\n                  };\n                }\n                return {};\n              }}\n              showNow={false}\n              allowClear={false}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"location\"\n            label=\"拍卖地点\"\n            rules={[\n              { required: true, message: '请输入拍卖地点' },\n              { min: 2, max: 200, message: '地点长度为2-200个字符' },\n            ]}\n          >\n            <Input\n              placeholder=\"请输入拍卖地点\"\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"auctioneerID\"\n            label=\"拍卖师\"\n            rules={[\n              { required: true, message: '请选择拍卖师' },\n            ]}\n          >\n            <Select\n              placeholder=\"请选择拍卖师\"\n              showSearch\n              optionFilterProp=\"children\"\n              filterOption={(input, option) =>\n                String(option?.children ?? '').toLowerCase().includes(input.toLowerCase())\n              }\n            >\n              {auctioneers.map(auctioneer => (\n                <Option key={auctioneer.id} value={auctioneer.id}>\n                  {auctioneer.name}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          {/* 错误和成功消息显示 */}\n          <FormMessage type=\"error\" message={formError} visible={!!formError} />\n          <FormMessage type=\"success\" message={formSuccess} visible={!!formSuccess} />\n\n          <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>\n            <div style={{\n              borderTop: '1px solid #f0f0f0',\n              paddingTop: '16px',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            }}>\n              <div style={{ color: '#666', fontSize: '12px' }}>\n                {editingAuction ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建拍卖会'}\n              </div>\n              <Space>\n                <Button\n                  onClick={() => {\n                    if (saving) {\n                      message.warning('正在保存中，请稍候...');\n                      return;\n                    }\n                    setIsModalVisible(false);\n                    form.resetFields();\n                    setEditingAuction(null);\n                    clearAllMessages();\n                  }}\n                  disabled={saving}\n                  size=\"middle\"\n                >\n                  取消\n                </Button>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={saving}\n                  disabled={saving}\n                  size=\"middle\"\n                  icon={editingAuction ? <EditOutlined /> : <PlusOutlined />}\n                >\n                  {saving ? '保存中...' : (editingAuction ? '更新拍卖会' : '创建拍卖会')}\n                </Button>\n              </Space>\n            </div>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 查看详情模态框 */}\n      <Modal\n        title=\"拍卖会详情\"\n        open={isViewModalVisible}\n        onCancel={() => {\n          setIsViewModalVisible(false);\n          setViewingAuction(null);\n        }}\n        footer={[\n          <Button key=\"close\" onClick={() => {\n            setIsViewModalVisible(false);\n            setViewingAuction(null);\n          }}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {viewingAuction && (\n          <div style={{ padding: '16px 0' }}>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>拍卖会标题：</strong>\n                  <div style={{ marginTop: 4 }}>{viewingAuction.title}</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>拍卖状态：</strong>\n                  <div style={{ marginTop: 4 }}>\n                    <Tag color={auctionStatusMap[viewingAuction.status]?.color || 'default'}>\n                      {auctionStatusMap[viewingAuction.status]?.label || '未知'}\n                    </Tag>\n                  </div>\n                </div>\n              </Col>\n              <Col span={24}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>拍卖描述：</strong>\n                  <div style={{ marginTop: 4, padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>\n                    {viewingAuction.description || '暂无描述'}\n                  </div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>拍卖地点：</strong>\n                  <div style={{ marginTop: 4 }}>{viewingAuction.location || '暂无地点信息'}</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>创建人：</strong>\n                  <div style={{ marginTop: 4 }}>{viewingAuction.creatorName}</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>开始时间：</strong>\n                  <div style={{ marginTop: 4 }}>{new Date(viewingAuction.startTime).toLocaleString()}</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>结束时间：</strong>\n                  <div style={{ marginTop: 4 }}>{new Date(viewingAuction.endTime).toLocaleString()}</div>\n                </div>\n              </Col>\n              <Col span={8}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>商品总数：</strong>\n                  <div style={{ marginTop: 4, fontSize: '18px', color: '#1890ff' }}>{viewingAuction.totalItems}件</div>\n                </div>\n              </Col>\n              <Col span={8}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>已售商品：</strong>\n                  <div style={{ marginTop: 4, fontSize: '18px', color: '#52c41a' }}>{viewingAuction.soldItems}件</div>\n                </div>\n              </Col>\n              <Col span={8}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>参与人数：</strong>\n                  <div style={{ marginTop: 4, fontSize: '18px', color: '#722ed1' }}>{viewingAuction.participantCount}人</div>\n                </div>\n              </Col>\n              <Col span={24}>\n                <div style={{ marginBottom: 16 }}>\n                  <strong>成交金额：</strong>\n                  <div style={{ marginTop: 4, fontSize: '24px', color: '#f50', fontWeight: 'bold' }}>\n                    ¥{viewingAuction.totalAmount.toFixed(2)}\n                  </div>\n                </div>\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default AuctionList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,mBAAmB,EACnBC,YAAY,EACZC,cAAc,EACdC,yBAAyB,QACpB,mBAAmB;AAE1B,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,+BAA+B;AACjG,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAG1B,UAAU;AAC5B,MAAM;EAAE2B;AAAO,CAAC,GAAGhC,MAAM;AACzB,MAAM;EAAEiC;AAAY,CAAC,GAAGzB,UAAU;;AAElC;AACA,WAAY0B,aAAa,0BAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EACL;EADRA,aAAa,CAAbA,aAAa;EAEL;EAFRA,aAAa,CAAbA,aAAa;EAGL;EAHRA,aAAa,CAAbA,aAAa;EAIL;EAJRA,aAAa,CAAbA,aAAa;EAKL;EALRA,aAAa,CAAbA,aAAa,kCAML;EAAA,OANRA,aAAa;AAAA;;AASzB;;AAoBA;;AAUA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAqB;IACjEsD,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACgE,MAAM,EAAEC,SAAS,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC;IAC3CoE,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,CAAC;IAClBC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAA+B,EAAE,CAAC;EAChF,MAAM,CAAC0E,IAAI,CAAC,GAAGhE,IAAI,CAACiE,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAGlE,IAAI,CAACiE,OAAO,CAAC,CAAC;EAEnC,MAAM;IACJE,SAAS;IACTC,WAAW;IACXC,YAAY;IACZC,cAAc;IACdC;EACF,CAAC,GAAGnD,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMoD,gBAAgB,GAAG;IACvB,CAACzC,aAAa,CAAC0C,KAAK,GAAG;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAU,CAAC;IACxD,CAAC5C,aAAa,CAAC6C,SAAS,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC1D,CAAC5C,aAAa,CAAC8C,OAAO,GAAG;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACzD,CAAC5C,aAAa,CAAC+C,MAAM,GAAG;MAAEJ,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS,CAAC;IACzD,CAAC5C,aAAa,CAACgD,SAAS,GAAG;MAAEL,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC5D,CAAC5C,aAAa,CAACiD,SAAS,GAAG;MAAEN,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM;EAC1D,CAAC;;EAED;EACA,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC1C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAMhE,cAAc,CAACiE,cAAc,CAACzC,WAAW,CAAC;MACjE,IAAIwC,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAEC,OAAY,KAAM;UAC/D,GAAGA,OAAO;UACVC,KAAK,EAAED,OAAO,CAACE,IAAI,IAAIF,OAAO,CAACC,KAAK;UAAE;UACtCE,UAAU,EAAEH,OAAO,CAACG,UAAU,IAAI,CAAC;UACnCC,WAAW,EAAEJ,OAAO,CAACI,WAAW,IAAI,CAAC;UACrCC,gBAAgB,EAAEL,OAAO,CAACK,gBAAgB,IAAI,CAAC;UAC/CC,SAAS,EAAEN,OAAO,CAACM,SAAS,IAAI,CAAC;UACjCC,WAAW,EAAEP,OAAO,CAACO,WAAW,IAAI,IAAI;UACxC;UACAC,YAAY,EAAER,OAAO,CAACQ;QACxB,CAAC,CAAC,CAAC;QACH5D,WAAW,CAACgD,cAAc,CAAC;QAC3B5C,QAAQ,CAACyC,QAAQ,CAACI,IAAI,CAAC9C,KAAK,CAAC;QAC7B0D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEd,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLpF,OAAO,CAACmG,KAAK,CAAClB,QAAQ,CAACjF,OAAO,IAAI,WAAW,CAAC;QAC9CoC,WAAW,CAAC,EAAE,CAAC;QACfI,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAO2D,KAAU,EAAE;MACnBF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAIC,QAAQ,GAAG,WAAW;MAC1B,IAAID,KAAK,CAAClB,QAAQ,EAAE;QAClB,MAAM;UAAEoB;QAAO,CAAC,GAAGF,KAAK,CAAClB,QAAQ;QACjC,IAAIoB,MAAM,KAAK,GAAG,EAAE;UAClBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,eAAe;QAC5B;MACF;MACApG,OAAO,CAACmG,KAAK,CAACC,QAAQ,CAAC;MACvBhE,WAAW,CAAC,EAAE,CAAC;MACfI,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgE,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMhE,cAAc,CAACsF,oBAAoB,CAAC,CAAC;MAC5D,IAAItB,QAAQ,CAACE,OAAO,EAAE;QACpB3B,aAAa,CAACyB,QAAQ,CAACI,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOc,KAAU,EAAE;MACnBF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF;MACA,MAAMvB,QAAQ,GAAG,MAAMwB,KAAK,CAAC,oEAAoE,CAAC;MAClG,MAAMpB,IAAI,GAAG,MAAMJ,QAAQ,CAACyB,IAAI,CAAC,CAAC;;MAElC;MACA,IAAIrB,IAAI,CAACF,OAAO,IAAIE,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAACC,IAAI,EAAE;QAC/C;QACA,MAAMqB,eAAe,GAAGtB,IAAI,CAACA,IAAI,CAACC,IAAI,CAACsB,MAAM,CAAEC,IAAS,IAAKA,IAAI,CAACC,QAAQ,KAAK,CAAC,CAAC;QACjF,MAAMC,cAAc,GAAGJ,eAAe,CAACpB,GAAG,CAAEsB,IAAS,KAAM;UACzDG,EAAE,EAAEH,IAAI,CAACG,EAAE;UACXtB,IAAI,EAAEmB,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACK;QAC9B,CAAC,CAAC,CAAC;QACHpD,cAAc,CAACiD,cAAc,CAAC;QAC9Bd,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEa,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLd,OAAO,CAACkB,IAAI,CAAC,YAAY,EAAE9B,IAAI,CAAC;QAChCvB,cAAc,CAAC,EAAE,CAAC;MACpB;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCrC,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC;;EAED;EACAxE,SAAS,CAAC,MAAM;IACd0F,aAAa,CAAC,CAAC;IACfsB,eAAe,CAAC,CAAC;IACjBE,gBAAgB,CAAC,CAAC;IACpB;EACA,CAAC,EAAE,CAAC/D,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM2E,YAAY,GAAIC,MAAW,IAAK;IACpC3E,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAG4E,MAAM;MACT1E,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2E,WAAW,GAAGA,CAAA,KAAM;IACxBrD,UAAU,CAACsD,WAAW,CAAC,CAAC;IACxB7E,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4E,SAAS,GAAGA,CAAA,KAAM;IACtBtE,iBAAiB,CAAC,IAAI,CAAC;IACvBa,IAAI,CAACwD,WAAW,CAAC,CAAC;;IAElB;IACAxD,IAAI,CAAC0D,cAAc,CAAC;MAClB;MACAC,SAAS,EAAE,CAACpG,KAAK,CAAC,CAAC,CAACqG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAErG,KAAK,CAAC,CAAC,CAACqG,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;IAC3D,CAAC,CAAC;IAEFrD,gBAAgB,CAAC,CAAC;IAClBxB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM8E,UAAU,GAAIpC,OAAgB,IAAK;IACvCpC,iBAAiB,CAACoC,OAAO,CAAC;IAC1BxC,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM6E,UAAU,GAAG,MAAOrC,OAAgB,IAAK;IAC7CtC,iBAAiB,CAACsC,OAAO,CAAC;IAC1B,IAAI;MACF;MACA,MAAMP,QAAQ,GAAG,MAAMhE,cAAc,CAAC6G,gBAAgB,CAACtC,OAAO,CAACwB,EAAE,CAAC;MAClE,IAAI/B,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAM4C,aAAa,GAAG9C,QAAQ,CAACI,IAAkE;QACjG;QACA,MAAMW,YAAY,GAAG+B,aAAa,CAAC/B,YAAY,IAAI+B,aAAa,CAACC,YAAY,IAAI,IAAI;QAErF/B,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE6B,aAAa,CAAC;QACpC9B,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,YAAY,CAAC;QAEvCjC,IAAI,CAAC0D,cAAc,CAAC;UAClBhC,KAAK,EAAED,OAAO,CAACC,KAAK;UAAE;UACtBwC,WAAW,EAAEzC,OAAO,CAACyC,WAAW;UAC5BC,QAAQ,EAAE1C,OAAO,CAAC0C,QAAQ,IAAI,EAAE;UAAE;UAClCF,YAAY,EAAEhC,YAAY;UAAE;UAChC0B,SAAS,EAAE,CAACpG,KAAK,CAACkE,OAAO,CAAC2C,SAAS,CAAC,EAAE7G,KAAK,CAACkE,OAAO,CAAC4C,OAAO,CAAC;QAC9D,CAAC,CAAC;MACA,CAAC,MAAM;QACL;QACArE,IAAI,CAAC0D,cAAc,CAAC;UAClBhC,KAAK,EAAED,OAAO,CAACC,KAAK;UACpBwC,WAAW,EAAEzC,OAAO,CAACyC,WAAW;UAChCC,QAAQ,EAAE1C,OAAO,CAAC0C,QAAQ,IAAI,EAAE;UAChCF,YAAY,EAAExC,OAAO,CAACQ,YAAY,IAAI,IAAI;UAAE;UAC5C0B,SAAS,EAAE,CAACpG,KAAK,CAACkE,OAAO,CAAC2C,SAAS,CAAC,EAAE7G,KAAK,CAACkE,OAAO,CAAC4C,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC;MACApC,IAAI,CAAC0D,cAAc,CAAC;QAClBhC,KAAK,EAAED,OAAO,CAACC,KAAK;QACpBwC,WAAW,EAAEzC,OAAO,CAACyC,WAAW;QAChCC,QAAQ,EAAE1C,OAAO,CAAC0C,QAAQ,IAAI,EAAE;QAChCF,YAAY,EAAExC,OAAO,CAACQ,YAAY,IAAI,IAAI;QAAE;QAC5C0B,SAAS,EAAE,CAACpG,KAAK,CAACkE,OAAO,CAAC2C,SAAS,CAAC,EAAE7G,KAAK,CAACkE,OAAO,CAAC4C,OAAO,CAAC;MAC9D,CAAC,CAAC;IACJ;IAEA9D,gBAAgB,CAAC,CAAC;IAClBxB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMuF,YAAY,GAAG,MAAOrB,EAAU,IAAK;IACzC,IAAI;MACF,MAAM/B,QAAQ,GAAG,MAAMhE,cAAc,CAACqH,aAAa,CAACtB,EAAE,CAAC;MACvD,IAAI/B,QAAQ,CAACE,OAAO,EAAE;QACpBnF,OAAO,CAACmF,OAAO,CAAC,MAAM,CAAC;QACvBH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLhF,OAAO,CAACmG,KAAK,CAAClB,QAAQ,CAACjF,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOmG,KAAU,EAAE;MACnBnG,OAAO,CAACmG,KAAK,CAACA,KAAK,CAACnG,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMuI,UAAU,GAAG,MAAOlB,MAAW,IAAK;IACxC/D,SAAS,CAAC,IAAI,CAAC;IACfgB,gBAAgB,CAAC,CAAC;IAElB,IAAI;MACF,MAAMkE,WAAW,GAAG;QAClB9C,IAAI,EAAE2B,MAAM,CAAC5B,KAAK;QAAE;QACpBwC,WAAW,EAAEZ,MAAM,CAACY,WAAW;QAC/BjC,YAAY,EAAEqB,MAAM,CAACW,YAAY;QAAE;QACnCE,QAAQ,EAAEb,MAAM,CAACa,QAAQ;QACzBC,SAAS,EAAEd,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAACe,WAAW,CAAC,CAAC;QAC5CL,OAAO,EAAEf,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAACe,WAAW,CAAC;MAC3C,CAAC;MAED,IAAIxD,QAAQ;MACZ,IAAIhC,cAAc,EAAE;QAClBgC,QAAQ,GAAG,MAAMhE,cAAc,CAACyH,aAAa,CAACzF,cAAc,CAAC+D,EAAE,EAAEwB,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLvD,QAAQ,GAAG,MAAMhE,cAAc,CAAC0H,aAAa,CAACH,WAAW,CAAC;MAC5D;MAEA,MAAMI,UAAU,GAAG3F,cAAc,GAAG,YAAY,GAAG,UAAU;MAE7D,IAAI7B,iBAAiB,CAAC6D,QAAQ,EAAEb,YAAY,EAAEC,cAAc,EAAEuE,UAAU,CAAC,EAAE;QACzE;QACAC,UAAU,CAAC,MAAM;UACf/F,iBAAiB,CAAC,KAAK,CAAC;UACxBiB,IAAI,CAACwD,WAAW,CAAC,CAAC;UAClBrE,iBAAiB,CAAC,IAAI,CAAC;UACvBoB,gBAAgB,CAAC,CAAC;UAClBU,aAAa,CAAC,CAAC;UACfsB,eAAe,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnB9E,cAAc,CAAC8E,KAAK,EAAE/B,YAAY,CAAC;IACrC,CAAC,SAAS;MACRd,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMwF,WAAW,GAAG,MAAO9B,EAAU,IAAK;IACxC,MAAMxB,OAAO,GAAGrD,QAAQ,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKA,EAAE,CAAC;IAC/C,IAAI,CAACxB,OAAO,EAAE;IAEd1F,KAAK,CAACmJ,OAAO,CAAC;MACZxD,KAAK,EAAE,QAAQ;MACfyD,OAAO,eACL1H,OAAA;QAAA2H,QAAA,gBACE3H,OAAA;UAAA2H,QAAA,GAAG,mDAAS,eAAA3H,OAAA;YAAA2H,QAAA,GAAQ,IAAC,EAAC3D,OAAO,CAACC,KAAK,EAAC,IAAC;UAAA;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,iBAAG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrD/H,OAAA;UAAGgI,KAAK,EAAE;YAAE9E,KAAK,EAAE,MAAM;YAAE+E,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;MACDG,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC;MAClCC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAM7E,QAAQ,GAAG,MAAMhE,cAAc,CAAC8I,YAAY,CAAC/C,EAAE,CAAC;UACtD,IAAI/B,QAAQ,CAACE,OAAO,EAAE;YACpBnF,OAAO,CAACmF,OAAO,CAAC;cACd+D,OAAO,EAAE,OAAO1D,OAAO,CAACC,KAAK,SAAS;cACtCuE,QAAQ,EAAE;YACZ,CAAC,CAAC;YACFhF,aAAa,CAAC,CAAC;UACjB,CAAC,MAAM;YACLhF,OAAO,CAACmG,KAAK,CAAC;cACZ+C,OAAO,EAAEjE,QAAQ,CAACjF,OAAO,IAAI,cAAc;cAC3CgK,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAO7D,KAAU,EAAE;UAAA,IAAA8D,eAAA,EAAAC,gBAAA;UACnBjE,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B,IAAIC,QAAQ,GAAG,QAAQ;UACvB,IAAI,EAAA6D,eAAA,GAAA9D,KAAK,CAAClB,QAAQ,cAAAgF,eAAA,uBAAdA,eAAA,CAAgB5D,MAAM,MAAK,GAAG,EAAE;YAClCD,QAAQ,GAAG,oBAAoB;UACjC,CAAC,MAAM,IAAI,EAAA8D,gBAAA,GAAA/D,KAAK,CAAClB,QAAQ,cAAAiF,gBAAA,uBAAdA,gBAAA,CAAgB7D,MAAM,MAAK,GAAG,EAAE;YACzCD,QAAQ,GAAG,cAAc;UAC3B,CAAC,MAAM;YACLA,QAAQ,GAAGD,KAAK,CAACnG,OAAO,IAAI,eAAe;UAC7C;UACAA,OAAO,CAACmG,KAAK,CAAC;YACZ+C,OAAO,EAAE9C,QAAQ;YACjB4D,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMG,WAAW,GAAG,MAAOnD,EAAU,IAAK;IACxC,MAAMxB,OAAO,GAAGrD,QAAQ,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKA,EAAE,CAAC;IAC/C,IAAI,CAACxB,OAAO,EAAE;IAEd1F,KAAK,CAACmJ,OAAO,CAAC;MACZxD,KAAK,EAAE,QAAQ;MACfyD,OAAO,eACL1H,OAAA;QAAA2H,QAAA,gBACE3H,OAAA;UAAA2H,QAAA,GAAG,mDAAS,eAAA3H,OAAA;YAAA2H,QAAA,GAAQ,IAAC,EAAC3D,OAAO,CAACC,KAAK,EAAC,IAAC;UAAA;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,iBAAG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrD/H,OAAA;UAAGgI,KAAK,EAAE;YAAE9E,KAAK,EAAE,MAAM;YAAE+E,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;MACDG,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC;MAClCC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAM7E,QAAQ,GAAG,MAAMhE,cAAc,CAACmJ,YAAY,CAACpD,EAAE,CAAC;UACtD,IAAI/B,QAAQ,CAACE,OAAO,EAAE;YACpBnF,OAAO,CAACmF,OAAO,CAAC;cACd+D,OAAO,EAAE,OAAO1D,OAAO,CAACC,KAAK,MAAM;cACnCuE,QAAQ,EAAE;YACZ,CAAC,CAAC;YACFhF,aAAa,CAAC,CAAC;UACjB,CAAC,MAAM;YACLhF,OAAO,CAACmG,KAAK,CAAC;cACZ+C,OAAO,EAAEjE,QAAQ,CAACjF,OAAO,IAAI,cAAc;cAC3CgK,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAO7D,KAAU,EAAE;UACnBF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BnG,OAAO,CAACmG,KAAK,CAAC;YACZ+C,OAAO,EAAE/C,KAAK,CAACnG,OAAO,IAAI,mBAAmB;YAC7CgK,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMK,SAAS,GAAG,MAAOrD,EAAU,IAAK;IACtC,MAAMxB,OAAO,GAAGrD,QAAQ,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKA,EAAE,CAAC;IAC/C,IAAI,CAACxB,OAAO,EAAE;IAEd1F,KAAK,CAACmJ,OAAO,CAAC;MACZxD,KAAK,EAAE,QAAQ;MACf6E,IAAI,eAAE9I,OAAA,CAACR,yBAAyB;QAACwI,KAAK,EAAE;UAAE9E,KAAK,EAAE;QAAU;MAAE;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAChEL,OAAO,eACL1H,OAAA;QAAA2H,QAAA,gBACE3H,OAAA;UAAA2H,QAAA,GAAG,mDAAS,eAAA3H,OAAA;YAAA2H,QAAA,GAAQ,IAAC,EAAC3D,OAAO,CAACC,KAAK,EAAC,IAAC;UAAA;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,iBAAG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrD/H,OAAA;UAAGgI,KAAK,EAAE;YAAE9E,KAAK,EAAE,SAAS;YAAE+E,QAAQ,EAAE,MAAM;YAAEc,UAAU,EAAE;UAAO,CAAE;UAAApB,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;MACDG,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE;QAAEY,MAAM,EAAE;MAAK,CAAC;MAC/BV,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAM7E,QAAQ,GAAG,MAAMhE,cAAc,CAACwJ,UAAU,CAACzD,EAAE,CAAC;UACpD,IAAI/B,QAAQ,CAACE,OAAO,EAAE;YACpBnF,OAAO,CAACmF,OAAO,CAAC;cACd+D,OAAO,EAAE,OAAO1D,OAAO,CAACC,KAAK,SAAS;cACtCuE,QAAQ,EAAE;YACZ,CAAC,CAAC;YACFhF,aAAa,CAAC,CAAC;UACjB,CAAC,MAAM;YACLhF,OAAO,CAACmG,KAAK,CAAC;cACZ+C,OAAO,EAAEjE,QAAQ,CAACjF,OAAO,IAAI,cAAc;cAC3CgK,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAO7D,KAAU,EAAE;UACnBF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BnG,OAAO,CAACmG,KAAK,CAAC;YACZ+C,OAAO,EAAE/C,KAAK,CAACnG,OAAO,IAAI,mBAAmB;YAC7CgK,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMU,OAA6B,GAAG,CACpC;IACEjF,KAAK,EAAE,IAAI;IACXkF,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEpF,KAAK,EAAE,OAAO;IACdkF,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBvJ,OAAA;MAAKgI,KAAK,EAAE;QAAEe,UAAU,EAAE;MAAI,CAAE;MAAApB,QAAA,EAAE4B;IAAI;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEhD,CAAC,EACD;IACE9D,KAAK,EAAE,MAAM;IACbkF,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGzE,MAAqB,IAAK;MACjC,MAAM2E,UAAU,GAAGzG,gBAAgB,CAAC8B,MAAM,CAAC;MAC3C,oBACE7E,OAAA,CAAClB,KAAK;QACJ+F,MAAM,EACJA,MAAM,KAAKvE,aAAa,CAAC8C,OAAO,GAAG,YAAY,GAC/CyB,MAAM,KAAKvE,aAAa,CAACgD,SAAS,GAAG,SAAS,GAC9CuB,MAAM,KAAKvE,aAAa,CAACiD,SAAS,GAAG,OAAO,GAAG,SAChD;QACDgG,IAAI,eACFvJ,OAAA,CAAC3B,GAAG;UAAC6E,KAAK,EAAE,CAAAsG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEtG,KAAK,KAAI,SAAU;UAAAyE,QAAA,EACxC,CAAA6B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEvG,KAAK,KAAI;QAAI;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEN;EACF,CAAC,EACD;IACE9D,KAAK,EAAE,MAAM;IACbkF,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACvI,KAAa,EAAE0I,MAAe,kBACrCzJ,OAAA;MAAA2H,QAAA,gBACE3H,OAAA;QAAA2H,QAAA,GAAM5G,KAAK,EAAC,QAAC;MAAA;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnB/H,OAAA;QAAKgI,KAAK,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAE/E,KAAK,EAAE;QAAO,CAAE;QAAAyE,QAAA,GAAC,gBACvC,EAAC8B,MAAM,CAACnF,SAAS;MAAA;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACE9D,KAAK,EAAE,MAAM;IACbkF,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGI,MAAc,iBACrB1J,OAAA;MAAKgI,KAAK,EAAE;QAAEe,UAAU,EAAE,GAAG;QAAE7F,KAAK,EAAE;MAAO,CAAE;MAAAyE,QAAA,GAAC,MAC7C,EAAC+B,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IAAA;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB;EAET,CAAC,EACD;IACE9D,KAAK,EAAE,MAAM;IACbkF,SAAS,EAAE,kBAAkB;IAC7BC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEpF,KAAK,EAAE,KAAK;IACZkF,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEpF,KAAK,EAAE,MAAM;IACbkF,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,IAAY,EAAEE,MAAe,kBACpCzJ,OAAA;MAAA2H,QAAA,gBACE3H,OAAA;QAAA2H,QAAA,EAAM,IAAIiC,IAAI,CAACL,IAAI,CAAC,CAACM,cAAc,CAAC;MAAC;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5C/H,OAAA;QAAKgI,KAAK,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAE/E,KAAK,EAAE;QAAO,CAAE;QAAAyE,QAAA,GAAC,SACzC,EAAC,IAAIiC,IAAI,CAACH,MAAM,CAAC7C,OAAO,CAAC,CAACiD,cAAc,CAAC,CAAC;MAAA;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACE9D,KAAK,EAAE,IAAI;IACXmF,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVS,KAAK,EAAE,OAAO;IACdR,MAAM,EAAEA,CAACS,CAAC,EAAEN,MAAe,kBACzBzJ,OAAA,CAAC9B,KAAK;MAAC8L,IAAI,EAAC,OAAO;MAAArC,QAAA,gBACjB3H,OAAA,CAAC/B,MAAM;QACLoK,IAAI,EAAC,MAAM;QACX2B,IAAI,EAAC,OAAO;QACZlB,IAAI,eAAE9I,OAAA,CAACf,WAAW;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBkC,OAAO,EAAEA,CAAA,KAAM7D,UAAU,CAACqD,MAAM,CAAE;QAAA9B,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/H,OAAA,CAAC/B,MAAM;QACLoK,IAAI,EAAC,MAAM;QACX2B,IAAI,EAAC,OAAO;QACZlB,IAAI,eAAE9I,OAAA,CAACd,YAAY;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBkC,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAACoD,MAAM,CAAE;QAAA9B,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR0B,MAAM,CAAC5E,MAAM,KAAKvE,aAAa,CAAC6C,SAAS,iBACxCnD,OAAA,CAAC/B,MAAM;QACLoK,IAAI,EAAC,MAAM;QACX2B,IAAI,EAAC,OAAO;QACZlB,IAAI,eAAE9I,OAAA,CAACZ,kBAAkB;UAAAwI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BkC,OAAO,EAAEA,CAAA,KAAM3C,WAAW,CAACmC,MAAM,CAACjE,EAAE,CAAE;QAAAmC,QAAA,EACvC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EACA0B,MAAM,CAAC5E,MAAM,KAAKvE,aAAa,CAAC8C,OAAO,iBACtCpD,OAAA,CAAAE,SAAA;QAAAyH,QAAA,gBACE3H,OAAA,CAAC/B,MAAM;UACLoK,IAAI,EAAC,MAAM;UACX2B,IAAI,EAAC,OAAO;UACZlB,IAAI,eAAE9I,OAAA,CAACX,mBAAmB;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BkC,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACc,MAAM,CAACjE,EAAE,CAAE;UAAAmC,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/H,OAAA,CAAC/B,MAAM;UACLoK,IAAI,EAAC,MAAM;UACX2B,IAAI,EAAC,OAAO;UACZlB,IAAI,eAAE9I,OAAA,CAACV,YAAY;YAAAsI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBkC,OAAO,EAAEA,CAAA,KAAMpB,SAAS,CAACY,MAAM,CAACjE,EAAE,CAAE;UAAAmC,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT,CACH,eACD/H,OAAA,CAAC/B,MAAM;QACLoK,IAAI,EAAC,MAAM;QACX2B,IAAI,EAAC,OAAO;QACZhB,MAAM;QACNF,IAAI,eAAE9I,OAAA,CAACb,cAAc;UAAAyI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBkC,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC4C,MAAM,CAACjE,EAAE,CAAE;QAAAmC,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,oBACE/H,OAAA;IAAKkK,SAAS,EAAC,wBAAwB;IAAAvC,QAAA,gBACrC3H,OAAA,CAACG,KAAK;MAACgK,KAAK,EAAE,CAAE;MAAAxC,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7B/H,OAAA,CAACtB,GAAG;MAAC0L,MAAM,EAAE,EAAG;MAACpC,KAAK,EAAE;QAAEqC,YAAY,EAAE;MAAG,CAAE;MAAA1C,QAAA,gBAC3C3H,OAAA,CAACrB,GAAG;QAAC2L,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA7C,QAAA,eACzB3H,OAAA,CAACjC,IAAI;UAAA4J,QAAA,eACH3H,OAAA,CAACnB,SAAS;YACRoF,KAAK,EAAC,0BAAM;YACZwG,KAAK,EAAE1I,UAAU,CAACE,aAAc;YAChCyI,UAAU,EAAE;cAAExH,KAAK,EAAE;YAAU;UAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/H,OAAA,CAACrB,GAAG;QAAC2L,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA7C,QAAA,eACzB3H,OAAA,CAACjC,IAAI;UAAA4J,QAAA,eACH3H,OAAA,CAACnB,SAAS;YACRoF,KAAK,EAAC,oBAAK;YACXwG,KAAK,EAAE1I,UAAU,CAACG,eAAgB;YAClCwI,UAAU,EAAE;cAAExH,KAAK,EAAE;YAAU;UAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/H,OAAA,CAACrB,GAAG;QAAC2L,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA7C,QAAA,eACzB3H,OAAA,CAACjC,IAAI;UAAA4J,QAAA,eACH3H,OAAA,CAACnB,SAAS;YACRoF,KAAK,EAAC,0BAAM;YACZwG,KAAK,EAAE1I,UAAU,CAACI,aAAc;YAChCuI,UAAU,EAAE;cAAExH,KAAK,EAAE;YAAU;UAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/H,OAAA,CAACrB,GAAG;QAAC2L,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA7C,QAAA,eACzB3H,OAAA,CAACjC,IAAI;UAAA4J,QAAA,eACH3H,OAAA,CAACnB,SAAS;YACRoF,KAAK,EAAC,gCAAO;YACbwG,KAAK,EAAE1I,UAAU,CAACK,iBAAkB;YACpCsI,UAAU,EAAE;cAAExH,KAAK,EAAE;YAAU;UAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/H,OAAA,CAACjC,IAAI;MAACmM,SAAS,EAAC,aAAa;MAACF,IAAI,EAAC,OAAO;MAAArC,QAAA,eACxC3H,OAAA,CAACzB,IAAI;QACHgE,IAAI,EAAEE,UAAW;QACjBkI,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEhF,YAAa;QACvBiF,YAAY,EAAC,KAAK;QAAAlD,QAAA,eAElB3H,OAAA,CAACtB,GAAG;UAAC0L,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACpC,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAA1B,QAAA,gBAC9C3H,OAAA,CAACrB,GAAG;YAAC2L,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7C,QAAA,eACzB3H,OAAA,CAACzB,IAAI,CAACuM,IAAI;cAAC5G,IAAI,EAAC,OAAO;cAACjB,KAAK,EAAC,gCAAO;cAAA0E,QAAA,eACnC3H,OAAA,CAAC7B,KAAK;gBAAC4M,WAAW,EAAC,kDAAU;gBAACC,UAAU;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAAC2L,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7C,QAAA,eACzB3H,OAAA,CAACzB,IAAI,CAACuM,IAAI;cAAC5G,IAAI,EAAC,QAAQ;cAACjB,KAAK,EAAC,0BAAM;cAAA0E,QAAA,eACnC3H,OAAA,CAAC5B,MAAM;gBAAC2M,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAArD,QAAA,gBACtC3H,OAAA,CAACI,MAAM;kBAACqK,KAAK,EAAEnK,aAAa,CAAC0C,KAAM;kBAAA2E,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C/H,OAAA,CAACI,MAAM;kBAACqK,KAAK,EAAEnK,aAAa,CAAC6C,SAAU;kBAAAwE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD/H,OAAA,CAACI,MAAM;kBAACqK,KAAK,EAAEnK,aAAa,CAAC8C,OAAQ;kBAAAuE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClD/H,OAAA,CAACI,MAAM;kBAACqK,KAAK,EAAEnK,aAAa,CAAC+C,MAAO;kBAAAsE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD/H,OAAA,CAACI,MAAM;kBAACqK,KAAK,EAAEnK,aAAa,CAACgD,SAAU;kBAAAqE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD/H,OAAA,CAACI,MAAM;kBAACqK,KAAK,EAAEnK,aAAa,CAACiD,SAAU;kBAAAoE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAAC2L,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7C,QAAA,eACzB3H,OAAA,CAACzB,IAAI,CAACuM,IAAI;cAAC5G,IAAI,EAAC,aAAa;cAACjB,KAAK,EAAC,oBAAK;cAAA0E,QAAA,eACvC3H,OAAA,CAAC7B,KAAK;gBAAC4M,WAAW,EAAC,kDAAU;gBAACC,UAAU;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAAC2L,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7C,QAAA,eACzB3H,OAAA,CAACzB,IAAI,CAACuM,IAAI;cAAC5G,IAAI,EAAC,WAAW;cAACjB,KAAK,EAAC,0BAAM;cAAA0E,QAAA,eACtC3H,OAAA,CAACK,WAAW;gBAAC2H,KAAK,EAAE;kBAAEqB,KAAK,EAAE;gBAAO;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAAC2L,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7C,QAAA,eACzB3H,OAAA,CAACzB,IAAI,CAACuM,IAAI;cAAAnD,QAAA,eACR3H,OAAA,CAAC9B,KAAK;gBAAAyJ,QAAA,gBACJ3H,OAAA,CAAC/B,MAAM;kBAACoK,IAAI,EAAC,SAAS;kBAAC4C,QAAQ,EAAC,QAAQ;kBAACnC,IAAI,eAAE9I,OAAA,CAAChB,cAAc;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAEnE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/H,OAAA,CAAC/B,MAAM;kBAACgM,OAAO,EAAEnE,WAAY;kBAACgD,IAAI,eAAE9I,OAAA,CAACT,cAAc;oBAAAqI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAExD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP/H,OAAA,CAACjC,IAAI;MAACmM,SAAS,EAAC,aAAa;MAACF,IAAI,EAAC,OAAO;MAAArC,QAAA,eACxC3H,OAAA,CAACtB,GAAG;QAACwM,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAxD,QAAA,gBACzC3H,OAAA,CAACrB,GAAG;UAAAgJ,QAAA,eACF3H,OAAA,CAAC/B,MAAM;YACLoK,IAAI,EAAC,SAAS;YACdS,IAAI,eAAE9I,OAAA,CAACjB,YAAY;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBkC,OAAO,EAAEjE,SAAU;YAAA2B,QAAA,EACpB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/H,OAAA,CAACrB,GAAG;UAAAgJ,QAAA,eACF3H,OAAA,CAAC/B,MAAM;YACL6K,IAAI,eAAE9I,OAAA,CAACT,cAAc;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBkC,OAAO,EAAEzG,aAAc;YACvB3C,OAAO,EAAEA,OAAQ;YAAA8G,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP/H,OAAA,CAACjC,IAAI;MAAA4J,QAAA,eACH3H,OAAA,CAAChC,KAAK;QACJkL,OAAO,EAAEA,OAAQ;QACjBkC,UAAU,EAAEzK,QAAS;QACrB0K,MAAM,EAAC,IAAI;QACXxK,OAAO,EAAEA,OAAQ;QACjByK,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAExK,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZ2K,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC7K,KAAK,EAAE8K,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ9K,KAAK,IAAI;UAC5C+K,QAAQ,EAAEA,CAAC3K,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP/H,OAAA,CAAC1B,KAAK;MACJ2F,KAAK,eACHjE,OAAA;QAAKgI,KAAK,EAAE;UAAE+D,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAtE,QAAA,GAC/DlG,cAAc,gBAAGzB,OAAA,CAACd,YAAY;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/H,OAAA,CAACjB,YAAY;UAAA6I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrD/H,OAAA;UAAA2H,QAAA,EAAOlG,cAAc,GAAG,WAAWA,cAAc,CAACwC,KAAK,EAAE,GAAG;QAAO;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CACN;MACDmE,IAAI,EAAE7K,cAAe;MACrB8K,QAAQ,EAAEA,CAAA,KAAM;QACd,IAAItK,MAAM,EAAE;UACVrD,OAAO,CAAC4N,OAAO,CAAC,cAAc,CAAC;UAC/B;QACF;QACA9K,iBAAiB,CAAC,KAAK,CAAC;QACxBiB,IAAI,CAACwD,WAAW,CAAC,CAAC;QAClBrE,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAE;MACF2K,MAAM,EAAE,IAAK;MACbhD,KAAK,EAAE,GAAI;MACXiD,cAAc;MACdC,YAAY,EAAE,CAAC1K,MAAO;MACtB2K,QAAQ,EAAE,CAAC3K,MAAO;MAAA8F,QAAA,eAElB3H,OAAA,CAACzB,IAAI;QACHgE,IAAI,EAAEA,IAAK;QACXoI,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE7D,UAAW;QACrB8D,YAAY,EAAC,KAAK;QAAAlD,QAAA,gBAElB3H,OAAA,CAACzB,IAAI,CAACuM,IAAI;UACR5G,IAAI,EAAC,OAAO;UACZjB,KAAK,EAAC,gCAAO;UACbwJ,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAElO,OAAO,EAAE;UAAW,CAAC,EACvC;YAAEmO,GAAG,EAAE,CAAC;YAAEC,GAAG,EAAE,GAAG;YAAEpO,OAAO,EAAE;UAAgB,CAAC,CAC9C;UAAAmJ,QAAA,eAEF3H,OAAA,CAAC7B,KAAK;YACJ4M,WAAW,EAAC,kDAAU;YACtB8B,SAAS;YACTC,SAAS,EAAE;UAAI;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/H,OAAA,CAACzB,IAAI,CAACuM,IAAI;UACR5G,IAAI,EAAC,aAAa;UAClBjB,KAAK,EAAC,gCAAO;UACbwJ,KAAK,EAAE,CACL;YAAEG,GAAG,EAAE,GAAG;YAAEpO,OAAO,EAAE;UAAe,CAAC,CACrC;UAAAmJ,QAAA,eAEF3H,OAAA,CAAC7B,KAAK,CAAC4O,QAAQ;YACbhC,WAAW,EAAC,kDAAU;YACtBiC,IAAI,EAAE,CAAE;YACRH,SAAS;YACTC,SAAS,EAAE;UAAI;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/H,OAAA,CAACzB,IAAI,CAACuM,IAAI;UACR5G,IAAI,EAAC,WAAW;UAChBjB,KAAK,EAAC,0BAAM;UACZwJ,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAElO,OAAO,EAAE;UAAU,CAAC,EACtC;YACEyO,SAAS,EAAEA,CAAClD,CAAC,EAAEU,KAAK,KAAK;cACvB,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,EAAE;gBACpC,OAAOyC,OAAO,CAACC,OAAO,CAAC,CAAC;cAC1B;cACA,MAAM,CAACC,KAAK,EAAEC,GAAG,CAAC,GAAG5C,KAAK;cAC1B,MAAM6C,GAAG,GAAG,IAAI1D,IAAI,CAAC,CAAC;;cAEtB;cACA,IAAIwD,KAAK,CAACG,QAAQ,CAACD,GAAG,CAAC,EAAE;gBACvB,OAAOJ,OAAO,CAACM,MAAM,CAAC,IAAIC,KAAK,CAAC,cAAc,CAAC,CAAC;cAClD;;cAEA;cACA,IAAIJ,GAAG,CAACE,QAAQ,CAACH,KAAK,CAAC,EAAE;gBACvB,OAAOF,OAAO,CAACM,MAAM,CAAC,IAAIC,KAAK,CAAC,cAAc,CAAC,CAAC;cAClD;;cAEA;cACA,MAAMjF,QAAQ,GAAG6E,GAAG,CAACK,IAAI,CAACN,KAAK,EAAE,SAAS,CAAC;cAC3C,IAAI5E,QAAQ,GAAG,EAAE,EAAE;gBACjB,OAAO0E,OAAO,CAACM,MAAM,CAAC,IAAIC,KAAK,CAAC,cAAc,CAAC,CAAC;cAClD;;cAEA;cACA,IAAIjF,QAAQ,GAAG,EAAE,GAAG,EAAE,EAAE;gBACtB,OAAO0E,OAAO,CAACM,MAAM,CAAC,IAAIC,KAAK,CAAC,cAAc,CAAC,CAAC;cAClD;cAEA,OAAOP,OAAO,CAACC,OAAO,CAAC,CAAC;YAC1B;UACF,CAAC,CACD;UACFQ,KAAK,EAAC,0FAAoB;UAAAhG,QAAA,eAE1B3H,OAAA,CAACK,WAAW;YACVuN,QAAQ,EAAE;cACRC,MAAM,EAAE,OAAO;cACfC,UAAU,EAAE,EAAE,CAAE;YAClB,CAAE;YACFD,MAAM,EAAC,kBAAkB;YACzB7F,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO,CAAE;YACzB0B,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAE;YAClCgD,YAAY,EAAGtC,OAAO,IAAK;cACzB;cACA,OAAOA,OAAO,IAAIA,OAAO,CAAC8B,QAAQ,CAAC,IAAI3D,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC;YACvD,CAAE;YACFoE,YAAY,EAAEA,CAACvC,OAAO,EAAEpD,IAAI,KAAK;cAC/B,MAAMiF,GAAG,GAAG,IAAI1D,IAAI,CAAC,CAAC;cACtB,MAAMqE,OAAO,GAAGxC,OAAO,IAAIA,OAAO,CAACyC,MAAM,CAACZ,GAAG,EAAE,KAAK,CAAC;cAErD,IAAIjF,IAAI,KAAK,OAAO,IAAI4F,OAAO,EAAE;gBAC/B;gBACA,OAAO;kBACLE,aAAa,EAAEA,CAAA,KAAM;oBACnB,MAAMC,KAAK,GAAG,EAAE;oBAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,GAAG,CAACgB,QAAQ,CAAC,CAAC,EAAED,CAAC,EAAE,EAAE;sBACvCD,KAAK,CAACG,IAAI,CAACF,CAAC,CAAC;oBACf;oBACA,OAAOD,KAAK;kBACd,CAAC;kBACDI,eAAe,EAAGC,YAAoB,IAAK;oBACzC,IAAIA,YAAY,KAAKnB,GAAG,CAACgB,QAAQ,CAAC,CAAC,EAAE;sBACnC,MAAMI,OAAO,GAAG,EAAE;sBAClB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,GAAG,CAACqB,UAAU,CAAC,CAAC,EAAEN,CAAC,EAAE,EAAE;wBACzCK,OAAO,CAACH,IAAI,CAACF,CAAC,CAAC;sBACjB;sBACA,OAAOK,OAAO;oBAChB;oBACA,OAAO,EAAE;kBACX;gBACF,CAAC;cACH;cACA,OAAO,CAAC,CAAC;YACX,CAAE;YACFE,OAAO,EAAE,KAAM;YACf5D,UAAU,EAAE;UAAM;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/H,OAAA,CAACzB,IAAI,CAACuM,IAAI;UACR5G,IAAI,EAAC,UAAU;UACfjB,KAAK,EAAC,0BAAM;UACZwJ,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAElO,OAAO,EAAE;UAAU,CAAC,EACtC;YAAEmO,GAAG,EAAE,CAAC;YAAEC,GAAG,EAAE,GAAG;YAAEpO,OAAO,EAAE;UAAgB,CAAC,CAC9C;UAAAmJ,QAAA,eAEF3H,OAAA,CAAC7B,KAAK;YACJ4M,WAAW,EAAC,4CAAS;YACrB8B,SAAS;YACTC,SAAS,EAAE;UAAI;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/H,OAAA,CAACzB,IAAI,CAACuM,IAAI;UACR5G,IAAI,EAAC,cAAc;UACnBjB,KAAK,EAAC,oBAAK;UACXwJ,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAElO,OAAO,EAAE;UAAS,CAAC,CACrC;UAAAmJ,QAAA,eAEF3H,OAAA,CAAC5B,MAAM;YACL2M,WAAW,EAAC,sCAAQ;YACpB8D,UAAU;YACVC,gBAAgB,EAAC,UAAU;YAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM;cAAA,IAAAC,gBAAA;cAAA,OAC1BC,MAAM,EAAAD,gBAAA,GAACD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEtH,QAAQ,cAAAuH,gBAAA,cAAAA,gBAAA,GAAI,EAAE,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,KAAK,CAACI,WAAW,CAAC,CAAC,CAAC;YAAA,CAC3E;YAAAzH,QAAA,EAEAtF,WAAW,CAAC0B,GAAG,CAACuL,UAAU,iBACzBtP,OAAA,CAACI,MAAM;cAAqBqK,KAAK,EAAE6E,UAAU,CAAC9J,EAAG;cAAAmC,QAAA,EAC9C2H,UAAU,CAACpL;YAAI,GADLoL,UAAU,CAAC9J,EAAE;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGZ/H,OAAA,CAACN,WAAW;UAAC2I,IAAI,EAAC,OAAO;UAAC7J,OAAO,EAAEkE,SAAU;UAAC6M,OAAO,EAAE,CAAC,CAAC7M;QAAU;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtE/H,OAAA,CAACN,WAAW;UAAC2I,IAAI,EAAC,SAAS;UAAC7J,OAAO,EAAEmE,WAAY;UAAC4M,OAAO,EAAE,CAAC,CAAC5M;QAAY;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE5E/H,OAAA,CAACzB,IAAI,CAACuM,IAAI;UAAC9C,KAAK,EAAE;YAAEqC,YAAY,EAAE,CAAC;YAAEmF,SAAS,EAAE;UAAO,CAAE;UAAA7H,QAAA,eACvD3H,OAAA;YAAKgI,KAAK,EAAE;cACVyH,SAAS,EAAE,mBAAmB;cAC9BC,UAAU,EAAE,MAAM;cAClB3D,OAAO,EAAE,MAAM;cACf4D,cAAc,EAAE,eAAe;cAC/B3D,UAAU,EAAE;YACd,CAAE;YAAArE,QAAA,gBACA3H,OAAA;cAAKgI,KAAK,EAAE;gBAAE9E,KAAK,EAAE,MAAM;gBAAE+E,QAAQ,EAAE;cAAO,CAAE;cAAAN,QAAA,EAC7ClG,cAAc,GAAG,gBAAgB,GAAG;YAAiB;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACN/H,OAAA,CAAC9B,KAAK;cAAAyJ,QAAA,gBACJ3H,OAAA,CAAC/B,MAAM;gBACLgM,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAIpI,MAAM,EAAE;oBACVrD,OAAO,CAAC4N,OAAO,CAAC,cAAc,CAAC;oBAC/B;kBACF;kBACA9K,iBAAiB,CAAC,KAAK,CAAC;kBACxBiB,IAAI,CAACwD,WAAW,CAAC,CAAC;kBAClBrE,iBAAiB,CAAC,IAAI,CAAC;kBACvBoB,gBAAgB,CAAC,CAAC;gBACpB,CAAE;gBACF8M,QAAQ,EAAE/N,MAAO;gBACjBmI,IAAI,EAAC,QAAQ;gBAAArC,QAAA,EACd;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/H,OAAA,CAAC/B,MAAM;gBACLoK,IAAI,EAAC,SAAS;gBACd4C,QAAQ,EAAC,QAAQ;gBACjBpK,OAAO,EAAEgB,MAAO;gBAChB+N,QAAQ,EAAE/N,MAAO;gBACjBmI,IAAI,EAAC,QAAQ;gBACblB,IAAI,EAAErH,cAAc,gBAAGzB,OAAA,CAACd,YAAY;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG/H,OAAA,CAACjB,YAAY;kBAAA6I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAE1D9F,MAAM,GAAG,QAAQ,GAAIJ,cAAc,GAAG,OAAO,GAAG;cAAQ;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR/H,OAAA,CAAC1B,KAAK;MACJ2F,KAAK,EAAC,gCAAO;MACbiI,IAAI,EAAE3K,kBAAmB;MACzB4K,QAAQ,EAAEA,CAAA,KAAM;QACd3K,qBAAqB,CAAC,KAAK,CAAC;QAC5BI,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAE;MACFyK,MAAM,EAAE,cACNrM,OAAA,CAAC/B,MAAM;QAAagM,OAAO,EAAEA,CAAA,KAAM;UACjCzI,qBAAqB,CAAC,KAAK,CAAC;UAC5BI,iBAAiB,CAAC,IAAI,CAAC;QACzB,CAAE;QAAA+F,QAAA,EAAC;MAEH,GALY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKX,CAAC,CACT;MACFsB,KAAK,EAAE,GAAI;MAAA1B,QAAA,EAEVhG,cAAc,iBACb3B,OAAA;QAAKgI,KAAK,EAAE;UAAE6H,OAAO,EAAE;QAAS,CAAE;QAAAlI,QAAA,eAChC3H,OAAA,CAACtB,GAAG;UAAC0L,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAzC,QAAA,gBACpB3H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,EAAG;YAAAnI,QAAA,eACZ3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE;gBAAE,CAAE;gBAAA7H,QAAA,EAAEhG,cAAc,CAACsC;cAAK;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,EAAG;YAAAnI,QAAA,eACZ3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE;gBAAE,CAAE;gBAAA7H,QAAA,eAC3B3H,OAAA,CAAC3B,GAAG;kBAAC6E,KAAK,EAAE,EAAAzC,qBAAA,GAAAsC,gBAAgB,CAACpB,cAAc,CAACkD,MAAM,CAAC,cAAApE,qBAAA,uBAAvCA,qBAAA,CAAyCyC,KAAK,KAAI,SAAU;kBAAAyE,QAAA,EACrE,EAAAjH,sBAAA,GAAAqC,gBAAgB,CAACpB,cAAc,CAACkD,MAAM,CAAC,cAAAnE,sBAAA,uBAAvCA,sBAAA,CAAyCuC,KAAK,KAAI;gBAAI;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,EAAG;YAAAnI,QAAA,eACZ3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE,CAAC;kBAAEK,OAAO,EAAE,KAAK;kBAAEE,eAAe,EAAE,SAAS;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAArI,QAAA,EAC3FhG,cAAc,CAAC8E,WAAW,IAAI;cAAM;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,EAAG;YAAAnI,QAAA,eACZ3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE;gBAAE,CAAE;gBAAA7H,QAAA,EAAEhG,cAAc,CAAC+E,QAAQ,IAAI;cAAQ;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,EAAG;YAAAnI,QAAA,eACZ3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE;gBAAE,CAAE;gBAAA7H,QAAA,EAAEhG,cAAc,CAAC4C;cAAW;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,EAAG;YAAAnI,QAAA,eACZ3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE;gBAAE,CAAE;gBAAA7H,QAAA,EAAE,IAAIiC,IAAI,CAACjI,cAAc,CAACgF,SAAS,CAAC,CAACkD,cAAc,CAAC;cAAC;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,EAAG;YAAAnI,QAAA,eACZ3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE;gBAAE,CAAE;gBAAA7H,QAAA,EAAE,IAAIiC,IAAI,CAACjI,cAAc,CAACiF,OAAO,CAAC,CAACiD,cAAc,CAAC;cAAC;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,CAAE;YAAAnI,QAAA,eACX3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE,CAAC;kBAAEvH,QAAQ,EAAE,MAAM;kBAAE/E,KAAK,EAAE;gBAAU,CAAE;gBAAAyE,QAAA,GAAEhG,cAAc,CAACwC,UAAU,EAAC,QAAC;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,CAAE;YAAAnI,QAAA,eACX3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE,CAAC;kBAAEvH,QAAQ,EAAE,MAAM;kBAAE/E,KAAK,EAAE;gBAAU,CAAE;gBAAAyE,QAAA,GAAEhG,cAAc,CAAC2C,SAAS,EAAC,QAAC;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,CAAE;YAAAnI,QAAA,eACX3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE,CAAC;kBAAEvH,QAAQ,EAAE,MAAM;kBAAE/E,KAAK,EAAE;gBAAU,CAAE;gBAAAyE,QAAA,GAAEhG,cAAc,CAAC0C,gBAAgB,EAAC,QAAC;cAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/H,OAAA,CAACrB,GAAG;YAACmR,IAAI,EAAE,EAAG;YAAAnI,QAAA,eACZ3H,OAAA;cAAKgI,KAAK,EAAE;gBAAEqC,YAAY,EAAE;cAAG,CAAE;cAAA1C,QAAA,gBAC/B3H,OAAA;gBAAA2H,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtB/H,OAAA;gBAAKgI,KAAK,EAAE;kBAAEwH,SAAS,EAAE,CAAC;kBAAEvH,QAAQ,EAAE,MAAM;kBAAE/E,KAAK,EAAE,MAAM;kBAAE6F,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,GAAC,MAChF,EAAChG,cAAc,CAACyC,WAAW,CAACuF,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvH,EAAA,CAhiCID,WAAqB;EAAA,QAoBVhC,IAAI,CAACiE,OAAO,EACNjE,IAAI,CAACiE,OAAO,EAQ7B7C,cAAc;AAAA;AAAAsQ,EAAA,GA7Bd1P,WAAqB;AAkiC3B,eAAeA,WAAW;AAAC,IAAA0P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}