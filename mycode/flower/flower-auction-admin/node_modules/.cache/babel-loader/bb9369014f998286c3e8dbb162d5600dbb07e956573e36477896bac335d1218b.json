{"ast": null, "code": "\"use client\";\n\nimport Link from './<PERSON>';\nimport Paragraph from './Paragraph';\nimport Text from './Text';\nimport Title from './Title';\nimport OriginTypography from './Typography';\nconst Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;", "map": {"version": 3, "names": ["Link", "Paragraph", "Text", "Title", "OriginTypography", "Typography"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/typography/index.js"], "sourcesContent": ["\"use client\";\n\nimport Link from './<PERSON>';\nimport Paragraph from './Paragraph';\nimport Text from './Text';\nimport Title from './Title';\nimport OriginTypography from './Typography';\nconst Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,gBAAgB,MAAM,cAAc;AAC3C,MAAMC,UAAU,GAAGD,gBAAgB;AACnCC,UAAU,CAACH,IAAI,GAAGA,IAAI;AACtBG,UAAU,CAACL,IAAI,GAAGA,IAAI;AACtBK,UAAU,CAACF,KAAK,GAAGA,KAAK;AACxBE,UAAU,CAACJ,SAAS,GAAGA,SAAS;AAChC,eAAeI,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}