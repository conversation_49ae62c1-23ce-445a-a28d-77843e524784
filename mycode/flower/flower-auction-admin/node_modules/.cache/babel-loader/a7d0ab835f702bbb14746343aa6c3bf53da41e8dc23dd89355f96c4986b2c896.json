{"ast": null, "code": "\"use client\";\n\nimport React, { useContext } from 'react';\nimport { ConfigContext } from '.';\nimport Empty from '../empty';\nconst DefaultRenderEmpty = props => {\n  const {\n    componentName\n  } = props;\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefix = getPrefixCls('empty');\n  switch (componentName) {\n    case 'Table':\n    case 'List':\n      return /*#__PURE__*/React.createElement(Empty, {\n        image: Empty.PRESENTED_IMAGE_SIMPLE\n      });\n    case 'Select':\n    case 'TreeSelect':\n    case 'Cascader':\n    case 'Transfer':\n    case 'Mentions':\n      return /*#__PURE__*/React.createElement(Empty, {\n        image: Empty.PRESENTED_IMAGE_SIMPLE,\n        className: `${prefix}-small`\n      });\n    /**\n     * This type of component should satisfy the nullish coalescing operator(??) on the left-hand side.\n     * to let the component itself implement the logic.\n     * For example `Table.filter`.\n     */\n    case 'Table.filter':\n      // why `null`? legacy react16 node type `undefined` is not allowed.\n      return null;\n    default:\n      // Should never hit if we take all the component into consider.\n      return /*#__PURE__*/React.createElement(Empty, null);\n  }\n};\nexport default DefaultRenderEmpty;", "map": {"version": 3, "names": ["React", "useContext", "ConfigContext", "Empty", "DefaultRenderEmpty", "props", "componentName", "getPrefixCls", "prefix", "createElement", "image", "PRESENTED_IMAGE_SIMPLE", "className"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/config-provider/defaultRenderEmpty.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport { ConfigContext } from '.';\nimport Empty from '../empty';\nconst DefaultRenderEmpty = props => {\n  const {\n    componentName\n  } = props;\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefix = getPrefixCls('empty');\n  switch (componentName) {\n    case 'Table':\n    case 'List':\n      return /*#__PURE__*/React.createElement(Empty, {\n        image: Empty.PRESENTED_IMAGE_SIMPLE\n      });\n    case 'Select':\n    case 'TreeSelect':\n    case 'Cascader':\n    case 'Transfer':\n    case 'Mentions':\n      return /*#__PURE__*/React.createElement(Empty, {\n        image: Empty.PRESENTED_IMAGE_SIMPLE,\n        className: `${prefix}-small`\n      });\n    /**\n     * This type of component should satisfy the nullish coalescing operator(??) on the left-hand side.\n     * to let the component itself implement the logic.\n     * For example `Table.filter`.\n     */\n    case 'Table.filter':\n      // why `null`? legacy react16 node type `undefined` is not allowed.\n      return null;\n    default:\n      // Should never hit if we take all the component into consider.\n      return /*#__PURE__*/React.createElement(Empty, null);\n  }\n};\nexport default DefaultRenderEmpty;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,aAAa,QAAQ,GAAG;AACjC,OAAOC,KAAK,MAAM,UAAU;AAC5B,MAAMC,kBAAkB,GAAGC,KAAK,IAAI;EAClC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAM;IACJE;EACF,CAAC,GAAGN,UAAU,CAACC,aAAa,CAAC;EAC7B,MAAMM,MAAM,GAAGD,YAAY,CAAC,OAAO,CAAC;EACpC,QAAQD,aAAa;IACnB,KAAK,OAAO;IACZ,KAAK,MAAM;MACT,OAAO,aAAaN,KAAK,CAACS,aAAa,CAACN,KAAK,EAAE;QAC7CO,KAAK,EAAEP,KAAK,CAACQ;MACf,CAAC,CAAC;IACJ,KAAK,QAAQ;IACb,KAAK,YAAY;IACjB,KAAK,UAAU;IACf,KAAK,UAAU;IACf,KAAK,UAAU;MACb,OAAO,aAAaX,KAAK,CAACS,aAAa,CAACN,KAAK,EAAE;QAC7CO,KAAK,EAAEP,KAAK,CAACQ,sBAAsB;QACnCC,SAAS,EAAE,GAAGJ,MAAM;MACtB,CAAC,CAAC;IACJ;AACJ;AACA;AACA;AACA;IACI,KAAK,cAAc;MACjB;MACA,OAAO,IAAI;IACb;MACE;MACA,OAAO,aAAaR,KAAK,CAACS,aAAa,CAACN,KAAK,EAAE,IAAI,CAAC;EACxD;AACF,CAAC;AACD,eAAeC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}