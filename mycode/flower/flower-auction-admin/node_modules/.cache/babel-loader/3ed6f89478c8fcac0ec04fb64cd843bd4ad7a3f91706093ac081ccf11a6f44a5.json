{"ast": null, "code": "\"use client\";\n\nimport InternalCard from './Card';\nimport Grid from './Grid';\nimport Meta from './Meta';\nconst Card = InternalCard;\nCard.Grid = Grid;\nCard.Meta = Meta;\nif (process.env.NODE_ENV !== 'production') {\n  Card.displayName = 'Card';\n}\nexport default Card;", "map": {"version": 3, "names": ["InternalCard", "Grid", "Meta", "Card", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/card/index.js"], "sourcesContent": ["\"use client\";\n\nimport InternalCard from './Card';\nimport Grid from './Grid';\nimport Meta from './Meta';\nconst Card = InternalCard;\nCard.Grid = Grid;\nCard.Meta = Meta;\nif (process.env.NODE_ENV !== 'production') {\n  Card.displayName = 'Card';\n}\nexport default Card;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,YAAY,MAAM,QAAQ;AACjC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,IAAI,MAAM,QAAQ;AACzB,MAAMC,IAAI,GAAGH,YAAY;AACzBG,IAAI,CAACF,IAAI,GAAGA,IAAI;AAChBE,IAAI,CAACD,IAAI,GAAGA,IAAI;AAChB,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,IAAI,CAACI,WAAW,GAAG,MAAM;AAC3B;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}