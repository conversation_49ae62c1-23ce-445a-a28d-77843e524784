{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/UserMenu/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dropdown, Avatar, Space, Modal, message } from 'antd';\nimport { UserOutlined, LogoutOutlined, SettingOutlined, LockOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserMenu = ({\n  showUsername = true,\n  avatarSize = 'default',\n  placement = 'bottomRight'\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n  const handleLogout = async () => {\n    setLogoutLoading(true);\n    try {\n      await logout();\n      message.success('登出成功');\n      // 跳转到登录页\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      message.error('登出失败，请重试');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n    }\n  };\n  const showLogoutModal = () => {\n    setLogoutModalVisible(true);\n  };\n  const handleChangePassword = () => {\n    message.info('修改密码功能开发中...');\n  };\n  const handleProfile = () => {\n    message.info('个人中心功能开发中...');\n  };\n  const handleSettings = () => {\n    message.info('账号设置功能开发中...');\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    label: '个人中心',\n    onClick: handleProfile\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this),\n    label: '账号设置',\n    onClick: handleSettings\n  }, {\n    key: 'change-password',\n    icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this),\n    label: '修改密码',\n    onClick: handleChangePassword\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: showLogoutModal\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n      menu: {\n        items: userMenuItems\n      },\n      placement: placement,\n      arrow: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          cursor: 'pointer'\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            size: avatarSize,\n            icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 21\n            }, this),\n            src: user === null || user === void 0 ? void 0 : user.avatar\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), showUsername && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: 500,\n              color: '#262626',\n              userSelect: 'none'\n            },\n            children: (user === null || user === void 0 ? void 0 : user.realName) || (user === null || user === void 0 ? void 0 : user.username) || '用户'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u786E\\u8BA4\\u767B\\u51FA\",\n      open: logoutModalVisible,\n      onOk: handleLogout,\n      onCancel: () => setLogoutModalVisible(false),\n      okText: \"\\u786E\\u8BA4\\u767B\\u51FA\",\n      cancelText: \"\\u53D6\\u6D88\",\n      okType: \"danger\",\n      confirmLoading: logoutLoading,\n      centered: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u60A8\\u786E\\u5B9A\\u8981\\u9000\\u51FA\\u767B\\u5F55\\u5417\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: \"\\u9000\\u51FA\\u540E\\u9700\\u8981\\u91CD\\u65B0\\u767B\\u5F55\\u624D\\u80FD\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u529F\\u80FD\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(UserMenu, \"4SlMwDXcFUYicbPZ0TKLgR4Fd1Q=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = UserMenu;\nexport default UserMenu;\nvar _c;\n$RefreshReg$(_c, \"UserMenu\");", "map": {"version": 3, "names": ["React", "useState", "Dropdown", "Avatar", "Space", "Modal", "message", "UserOutlined", "LogoutOutlined", "SettingOutlined", "LockOutlined", "useAuth", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserMenu", "showUsername", "avatarSize", "placement", "_s", "user", "logout", "navigate", "logoutModalVisible", "setLogoutModalVisible", "logoutLoading", "setLogoutLoading", "handleLogout", "success", "window", "location", "href", "error", "console", "showLogoutModal", "handleChangePassword", "info", "handleProfile", "handleSettings", "userMenuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "onClick", "type", "children", "menu", "items", "arrow", "style", "cursor", "size", "src", "avatar", "fontWeight", "color", "userSelect", "realName", "username", "title", "open", "onOk", "onCancel", "okText", "cancelText", "okType", "confirmLoading", "centered", "padding", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/UserMenu/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Dropdown, Avatar, Space, Modal, message } from 'antd';\nimport {\n  UserOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  LockOutlined,\n} from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useNavigate } from 'react-router-dom';\n\ninterface UserMenuProps {\n  showUsername?: boolean;\n  avatarSize?: number | 'large' | 'small' | 'default';\n  placement?: 'bottomLeft' | 'bottomCenter' | 'bottomRight' | 'topLeft' | 'topCenter' | 'topRight';\n}\n\nconst UserMenu: React.FC<UserMenuProps> = ({\n  showUsername = true,\n  avatarSize = 'default',\n  placement = 'bottomRight',\n}) => {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n\n  const handleLogout = async () => {\n    setLogoutLoading(true);\n    try {\n      await logout();\n      message.success('登出成功');\n      // 跳转到登录页\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      message.error('登出失败，请重试');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n    }\n  };\n\n  const showLogoutModal = () => {\n    setLogoutModalVisible(true);\n  };\n\n  const handleChangePassword = () => {\n    message.info('修改密码功能开发中...');\n  };\n\n  const handleProfile = () => {\n    message.info('个人中心功能开发中...');\n  };\n\n  const handleSettings = () => {\n    message.info('账号设置功能开发中...');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人中心',\n      onClick: handleProfile,\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '账号设置',\n      onClick: handleSettings,\n    },\n    {\n      key: 'change-password',\n      icon: <LockOutlined />,\n      label: '修改密码',\n      onClick: handleChangePassword,\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: showLogoutModal,\n    },\n  ];\n\n  return (\n    <>\n      <Dropdown\n        menu={{ items: userMenuItems }}\n        placement={placement}\n        arrow\n      >\n        <div style={{ cursor: 'pointer' }}>\n          <Space>\n            <Avatar \n              size={avatarSize}\n              icon={<UserOutlined />}\n              src={user?.avatar}\n            />\n            {showUsername && (\n              <span style={{ \n                fontWeight: 500, \n                color: '#262626',\n                userSelect: 'none'\n              }}>\n                {user?.realName || user?.username || '用户'}\n              </span>\n            )}\n          </Space>\n        </div>\n      </Dropdown>\n\n      {/* 登出确认弹窗 */}\n      <Modal\n        title=\"确认登出\"\n        open={logoutModalVisible}\n        onOk={handleLogout}\n        onCancel={() => setLogoutModalVisible(false)}\n        okText=\"确认登出\"\n        cancelText=\"取消\"\n        okType=\"danger\"\n        confirmLoading={logoutLoading}\n        centered\n      >\n        <div style={{ padding: '20px 0' }}>\n          <p>您确定要退出登录吗？</p>\n          <p style={{ color: '#666', fontSize: '14px' }}>\n            退出后需要重新登录才能继续使用系统功能。\n          </p>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default UserMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AAC9D,SACEC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,YAAY,QACP,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQ/C,MAAMC,QAAiC,GAAGA,CAAC;EACzCC,YAAY,GAAG,IAAI;EACnBC,UAAU,GAAG,SAAS;EACtBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAClC,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAM4B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BD,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAML,MAAM,CAAC,CAAC;MACdjB,OAAO,CAACwB,OAAO,CAAC,MAAM,CAAC;MACvB;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC5B,OAAO,CAAC4B,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRN,gBAAgB,CAAC,KAAK,CAAC;MACvBF,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BV,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMW,oBAAoB,GAAGA,CAAA,KAAM;IACjC/B,OAAO,CAACgC,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BjC,OAAO,CAACgC,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BlC,OAAO,CAACgC,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMG,aAAa,GAAG,CACpB;IACEC,GAAG,EAAE,SAAS;IACdC,IAAI,eAAE7B,OAAA,CAACP,YAAY;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEV;EACX,CAAC,EACD;IACEG,GAAG,EAAE,UAAU;IACfC,IAAI,eAAE7B,OAAA,CAACL,eAAe;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAET;EACX,CAAC,EACD;IACEE,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAE7B,OAAA,CAACJ,YAAY;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEZ;EACX,CAAC,EACD;IACEa,IAAI,EAAE;EACR,CAAC,EACD;IACER,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAE7B,OAAA,CAACN,cAAc;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAEb;EACX,CAAC,CACF;EAED,oBACEtB,OAAA,CAAAE,SAAA;IAAAmC,QAAA,gBACErC,OAAA,CAACZ,QAAQ;MACPkD,IAAI,EAAE;QAAEC,KAAK,EAAEZ;MAAc,CAAE;MAC/BrB,SAAS,EAAEA,SAAU;MACrBkC,KAAK;MAAAH,QAAA,eAELrC,OAAA;QAAKyC,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAU,CAAE;QAAAL,QAAA,eAChCrC,OAAA,CAACV,KAAK;UAAA+C,QAAA,gBACJrC,OAAA,CAACX,MAAM;YACLsD,IAAI,EAAEtC,UAAW;YACjBwB,IAAI,eAAE7B,OAAA,CAACP,YAAY;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBW,GAAG,EAAEpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACD7B,YAAY,iBACXJ,OAAA;YAAMyC,KAAK,EAAE;cACXK,UAAU,EAAE,GAAG;cACfC,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,EACC,CAAA7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,QAAQ,MAAIzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,QAAQ,KAAI;UAAI;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGXjC,OAAA,CAACT,KAAK;MACJ4D,KAAK,EAAC,0BAAM;MACZC,IAAI,EAAEzC,kBAAmB;MACzB0C,IAAI,EAAEtC,YAAa;MACnBuC,QAAQ,EAAEA,CAAA,KAAM1C,qBAAqB,CAAC,KAAK,CAAE;MAC7C2C,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfC,MAAM,EAAC,QAAQ;MACfC,cAAc,EAAE7C,aAAc;MAC9B8C,QAAQ;MAAAtB,QAAA,eAERrC,OAAA;QAAKyC,KAAK,EAAE;UAAEmB,OAAO,EAAE;QAAS,CAAE;QAAAvB,QAAA,gBAChCrC,OAAA;UAAAqC,QAAA,EAAG;QAAU;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjBjC,OAAA;UAAGyC,KAAK,EAAE;YAAEM,KAAK,EAAE,MAAM;YAAEc,QAAQ,EAAE;UAAO,CAAE;UAAAxB,QAAA,EAAC;QAE/C;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAAC1B,EAAA,CAxHIJ,QAAiC;EAAA,QAKZN,OAAO,EACfC,WAAW;AAAA;AAAAgE,EAAA,GANxB3D,QAAiC;AA0HvC,eAAeA,QAAQ;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}