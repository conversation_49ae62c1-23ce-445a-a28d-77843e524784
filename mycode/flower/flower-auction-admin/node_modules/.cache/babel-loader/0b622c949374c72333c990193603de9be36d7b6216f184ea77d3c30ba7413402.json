{"ast": null, "code": "import * as React from 'react';\nimport { ConfigContext } from '../../config-provider';\nfunction useBase(customizePrefixCls, direction) {\n  const {\n    getPrefixCls,\n    direction: rootDirection,\n    renderEmpty\n  } = React.useContext(ConfigContext);\n  const mergedDirection = direction || rootDirection;\n  const prefixCls = getPrefixCls('select', customizePrefixCls);\n  const cascaderPrefixCls = getPrefixCls('cascader', customizePrefixCls);\n  return [prefixCls, cascaderPrefixCls, mergedDirection, renderEmpty];\n}\nexport default useBase;", "map": {"version": 3, "names": ["React", "ConfigContext", "useBase", "customizePrefixCls", "direction", "getPrefixCls", "rootDirection", "renderEmpty", "useContext", "mergedDirection", "prefixCls", "cascaderPrefixCls"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/cascader/hooks/useBase.js"], "sourcesContent": ["import * as React from 'react';\nimport { ConfigContext } from '../../config-provider';\nfunction useBase(customizePrefixCls, direction) {\n  const {\n    getPrefixCls,\n    direction: rootDirection,\n    renderEmpty\n  } = React.useContext(ConfigContext);\n  const mergedDirection = direction || rootDirection;\n  const prefixCls = getPrefixCls('select', customizePrefixCls);\n  const cascaderPrefixCls = getPrefixCls('cascader', customizePrefixCls);\n  return [prefixCls, cascaderPrefixCls, mergedDirection, renderEmpty];\n}\nexport default useBase;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,OAAOA,CAACC,kBAAkB,EAAEC,SAAS,EAAE;EAC9C,MAAM;IACJC,YAAY;IACZD,SAAS,EAAEE,aAAa;IACxBC;EACF,CAAC,GAAGP,KAAK,CAACQ,UAAU,CAACP,aAAa,CAAC;EACnC,MAAMQ,eAAe,GAAGL,SAAS,IAAIE,aAAa;EAClD,MAAMI,SAAS,GAAGL,YAAY,CAAC,QAAQ,EAAEF,kBAAkB,CAAC;EAC5D,MAAMQ,iBAAiB,GAAGN,YAAY,CAAC,UAAU,EAAEF,kBAAkB,CAAC;EACtE,OAAO,CAACO,SAAS,EAAEC,iBAAiB,EAAEF,eAAe,EAAEF,WAAW,CAAC;AACrE;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}