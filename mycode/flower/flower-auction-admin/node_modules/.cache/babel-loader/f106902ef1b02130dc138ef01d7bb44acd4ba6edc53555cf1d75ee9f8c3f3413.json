{"ast": null, "code": "import { hcl as colorHcl } from \"d3-color\";\nimport color, { hue } from \"./color.js\";\nfunction hcl(hue) {\n  return function (start, end) {\n    var h = hue((start = colorHcl(start)).h, (end = colorHcl(end)).h),\n      c = color(start.c, end.c),\n      l = color(start.l, end.l),\n      opacity = color(start.opacity, end.opacity);\n    return function (t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  };\n}\nexport default hcl(hue);\nexport var hclLong = hcl(color);", "map": {"version": 3, "names": ["hcl", "colorHcl", "color", "hue", "start", "end", "h", "c", "l", "opacity", "t", "hclLong"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/d3-interpolate/src/hcl.js"], "sourcesContent": ["import {hcl as colorHcl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hcl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHcl(start)).h, (end = colorHcl(end)).h),\n        c = color(start.c, end.c),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hcl(hue);\nexport var hclLong = hcl(color);\n"], "mappings": "AAAA,SAAQA,GAAG,IAAIC,QAAQ,QAAO,UAAU;AACxC,OAAOC,KAAK,IAAGC,GAAG,QAAO,YAAY;AAErC,SAASH,GAAGA,CAACG,GAAG,EAAE;EAChB,OAAO,UAASC,KAAK,EAAEC,GAAG,EAAE;IAC1B,IAAIC,CAAC,GAAGH,GAAG,CAAC,CAACC,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAAC,EAAEE,CAAC,EAAE,CAACD,GAAG,GAAGJ,QAAQ,CAACI,GAAG,CAAC,EAAEC,CAAC,CAAC;MAC7DC,CAAC,GAAGL,KAAK,CAACE,KAAK,CAACG,CAAC,EAAEF,GAAG,CAACE,CAAC,CAAC;MACzBC,CAAC,GAAGN,KAAK,CAACE,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACG,CAAC,CAAC;MACzBC,OAAO,GAAGP,KAAK,CAACE,KAAK,CAACK,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAAC;IAC/C,OAAO,UAASC,CAAC,EAAE;MACjBN,KAAK,CAACE,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;MACdN,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;MACdN,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC;MACdN,KAAK,CAACK,OAAO,GAAGA,OAAO,CAACC,CAAC,CAAC;MAC1B,OAAON,KAAK,GAAG,EAAE;IACnB,CAAC;EACH,CAAC;AACH;AAEA,eAAeJ,GAAG,CAACG,GAAG,CAAC;AACvB,OAAO,IAAIQ,OAAO,GAAGX,GAAG,CAACE,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}