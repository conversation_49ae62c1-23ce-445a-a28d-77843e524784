{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport SlickCarousel from '@ant-design/react-slick';\nimport classNames from 'classnames';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle, { DotDuration } from './style';\nconst dotsClass = 'slick-dots';\nconst ArrowButton = _a => {\n  var {\n      currentSlide,\n      slideCount\n    } = _a,\n    rest = __rest(_a, [\"currentSlide\", \"slideCount\"]);\n  return /*#__PURE__*/React.createElement(\"button\", Object.assign({\n    type: \"button\"\n  }, rest));\n};\nconst Carousel = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      dots = true,\n      arrows = false,\n      prevArrow = /*#__PURE__*/React.createElement(ArrowButton, {\n        \"aria-label\": \"prev\"\n      }),\n      nextArrow = /*#__PURE__*/React.createElement(ArrowButton, {\n        \"aria-label\": \"next\"\n      }),\n      draggable = false,\n      waitForAnimate = false,\n      dotPosition = 'bottom',\n      vertical = dotPosition === 'left' || dotPosition === 'right',\n      rootClassName,\n      className: customClassName,\n      style,\n      id,\n      autoplay = false,\n      autoplaySpeed = 3000\n    } = props,\n    otherProps = __rest(props, [\"dots\", \"arrows\", \"prevArrow\", \"nextArrow\", \"draggable\", \"waitForAnimate\", \"dotPosition\", \"vertical\", \"rootClassName\", \"className\", \"style\", \"id\", \"autoplay\", \"autoplaySpeed\"]);\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('carousel');\n  const slickRef = React.useRef(null);\n  const goTo = (slide, dontAnimate = false) => {\n    slickRef.current.slickGoTo(slide, dontAnimate);\n  };\n  React.useImperativeHandle(ref, () => ({\n    goTo,\n    autoPlay: slickRef.current.innerSlider.autoPlay,\n    innerSlider: slickRef.current.innerSlider,\n    prev: slickRef.current.slickPrev,\n    next: slickRef.current.slickNext\n  }), [slickRef.current]);\n  const prevCount = React.useRef(React.Children.count(props.children));\n  React.useEffect(() => {\n    if (prevCount.current !== React.Children.count(props.children)) {\n      goTo(props.initialSlide || 0, false);\n      prevCount.current = React.Children.count(props.children);\n    }\n  }, [props.children]);\n  const newProps = Object.assign({\n    vertical,\n    className: classNames(customClassName, contextClassName),\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    autoplay: !!autoplay\n  }, otherProps);\n  if (newProps.effect === 'fade') {\n    newProps.fade = true;\n  }\n  const prefixCls = getPrefixCls('carousel', newProps.prefixCls);\n  const enableDots = !!dots;\n  const dsClass = classNames(dotsClass, `${dotsClass}-${dotPosition}`, typeof dots === 'boolean' ? false : dots === null || dots === void 0 ? void 0 : dots.className);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const className = classNames(prefixCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-vertical`]: newProps.vertical\n  }, hashId, cssVarCls, rootClassName);\n  const mergedShowDuration = autoplay && (typeof autoplay === 'object' ? autoplay.dotDuration : false);\n  const dotDurationStyle = mergedShowDuration ? {\n    [DotDuration]: `${autoplaySpeed}ms`\n  } : {};\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    id: id,\n    style: dotDurationStyle\n  }, /*#__PURE__*/React.createElement(SlickCarousel, Object.assign({\n    ref: slickRef\n  }, newProps, {\n    dots: enableDots,\n    dotsClass: dsClass,\n    arrows: arrows,\n    prevArrow: prevArrow,\n    nextArrow: nextArrow,\n    draggable: draggable,\n    verticalSwiping: vertical,\n    autoplaySpeed: autoplaySpeed,\n    waitForAnimate: waitForAnimate\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Carousel.displayName = 'Carousel';\n}\nexport default Carousel;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "SlickCarousel", "classNames", "useComponentConfig", "useStyle", "DotDuration", "dotsClass", "ArrowButton", "_a", "currentSlide", "slideCount", "rest", "createElement", "assign", "type", "Carousel", "forwardRef", "props", "ref", "dots", "arrows", "prevArrow", "nextArrow", "draggable", "waitForAnimate", "dotPosition", "vertical", "rootClassName", "className", "customClassName", "style", "id", "autoplay", "autoplaySpeed", "otherProps", "getPrefixCls", "direction", "contextClassName", "contextStyle", "slickRef", "useRef", "goTo", "slide", "dontAnimate", "current", "slickGoTo", "useImperativeHandle", "autoPlay", "innerSlider", "prev", "slick<PERSON>rev", "next", "slickNext", "prevCount", "Children", "count", "children", "useEffect", "initialSlide", "newProps", "effect", "fade", "prefixCls", "enableDots", "dsClass", "wrapCSSVar", "hashId", "cssVarCls", "mergedShowDuration", "dotDuration", "dotDurationStyle", "verticalSwiping", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/carousel/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport SlickCarousel from '@ant-design/react-slick';\nimport classNames from 'classnames';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle, { DotDuration } from './style';\nconst dotsClass = 'slick-dots';\nconst ArrowButton = _a => {\n  var {\n      currentSlide,\n      slideCount\n    } = _a,\n    rest = __rest(_a, [\"currentSlide\", \"slideCount\"]);\n  return /*#__PURE__*/React.createElement(\"button\", Object.assign({\n    type: \"button\"\n  }, rest));\n};\nconst Carousel = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      dots = true,\n      arrows = false,\n      prevArrow = /*#__PURE__*/React.createElement(ArrowButton, {\n        \"aria-label\": \"prev\"\n      }),\n      nextArrow = /*#__PURE__*/React.createElement(ArrowButton, {\n        \"aria-label\": \"next\"\n      }),\n      draggable = false,\n      waitForAnimate = false,\n      dotPosition = 'bottom',\n      vertical = dotPosition === 'left' || dotPosition === 'right',\n      rootClassName,\n      className: customClassName,\n      style,\n      id,\n      autoplay = false,\n      autoplaySpeed = 3000\n    } = props,\n    otherProps = __rest(props, [\"dots\", \"arrows\", \"prevArrow\", \"nextArrow\", \"draggable\", \"waitForAnimate\", \"dotPosition\", \"vertical\", \"rootClassName\", \"className\", \"style\", \"id\", \"autoplay\", \"autoplaySpeed\"]);\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('carousel');\n  const slickRef = React.useRef(null);\n  const goTo = (slide, dontAnimate = false) => {\n    slickRef.current.slickGoTo(slide, dontAnimate);\n  };\n  React.useImperativeHandle(ref, () => ({\n    goTo,\n    autoPlay: slickRef.current.innerSlider.autoPlay,\n    innerSlider: slickRef.current.innerSlider,\n    prev: slickRef.current.slickPrev,\n    next: slickRef.current.slickNext\n  }), [slickRef.current]);\n  const prevCount = React.useRef(React.Children.count(props.children));\n  React.useEffect(() => {\n    if (prevCount.current !== React.Children.count(props.children)) {\n      goTo(props.initialSlide || 0, false);\n      prevCount.current = React.Children.count(props.children);\n    }\n  }, [props.children]);\n  const newProps = Object.assign({\n    vertical,\n    className: classNames(customClassName, contextClassName),\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    autoplay: !!autoplay\n  }, otherProps);\n  if (newProps.effect === 'fade') {\n    newProps.fade = true;\n  }\n  const prefixCls = getPrefixCls('carousel', newProps.prefixCls);\n  const enableDots = !!dots;\n  const dsClass = classNames(dotsClass, `${dotsClass}-${dotPosition}`, typeof dots === 'boolean' ? false : dots === null || dots === void 0 ? void 0 : dots.className);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const className = classNames(prefixCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-vertical`]: newProps.vertical\n  }, hashId, cssVarCls, rootClassName);\n  const mergedShowDuration = autoplay && (typeof autoplay === 'object' ? autoplay.dotDuration : false);\n  const dotDurationStyle = mergedShowDuration ? {\n    [DotDuration]: `${autoplaySpeed}ms`\n  } : {};\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    id: id,\n    style: dotDurationStyle\n  }, /*#__PURE__*/React.createElement(SlickCarousel, Object.assign({\n    ref: slickRef\n  }, newProps, {\n    dots: enableDots,\n    dotsClass: dsClass,\n    arrows: arrows,\n    prevArrow: prevArrow,\n    nextArrow: nextArrow,\n    draggable: draggable,\n    verticalSwiping: vertical,\n    autoplaySpeed: autoplaySpeed,\n    waitForAnimate: waitForAnimate\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Carousel.displayName = 'Carousel';\n}\nexport default Carousel;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,QAAQ,IAAIC,WAAW,QAAQ,SAAS;AAC/C,MAAMC,SAAS,GAAG,YAAY;AAC9B,MAAMC,WAAW,GAAGC,EAAE,IAAI;EACxB,IAAI;MACAC,YAAY;MACZC;IACF,CAAC,GAAGF,EAAE;IACNG,IAAI,GAAGzB,MAAM,CAACsB,EAAE,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;EACnD,OAAO,aAAaR,KAAK,CAACY,aAAa,CAAC,QAAQ,EAAErB,MAAM,CAACsB,MAAM,CAAC;IAC9DC,IAAI,EAAE;EACR,CAAC,EAAEH,IAAI,CAAC,CAAC;AACX,CAAC;AACD,MAAMI,QAAQ,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC7D,MAAM;MACFC,IAAI,GAAG,IAAI;MACXC,MAAM,GAAG,KAAK;MACdC,SAAS,GAAG,aAAarB,KAAK,CAACY,aAAa,CAACL,WAAW,EAAE;QACxD,YAAY,EAAE;MAChB,CAAC,CAAC;MACFe,SAAS,GAAG,aAAatB,KAAK,CAACY,aAAa,CAACL,WAAW,EAAE;QACxD,YAAY,EAAE;MAChB,CAAC,CAAC;MACFgB,SAAS,GAAG,KAAK;MACjBC,cAAc,GAAG,KAAK;MACtBC,WAAW,GAAG,QAAQ;MACtBC,QAAQ,GAAGD,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,OAAO;MAC5DE,aAAa;MACbC,SAAS,EAAEC,eAAe;MAC1BC,KAAK;MACLC,EAAE;MACFC,QAAQ,GAAG,KAAK;MAChBC,aAAa,GAAG;IAClB,CAAC,GAAGhB,KAAK;IACTiB,UAAU,GAAGhD,MAAM,CAAC+B,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;EAC9M,MAAM;IACJkB,YAAY;IACZC,SAAS;IACTR,SAAS,EAAES,gBAAgB;IAC3BP,KAAK,EAAEQ;EACT,CAAC,GAAGnC,kBAAkB,CAAC,UAAU,CAAC;EAClC,MAAMoC,QAAQ,GAAGvC,KAAK,CAACwC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,IAAI,GAAGA,CAACC,KAAK,EAAEC,WAAW,GAAG,KAAK,KAAK;IAC3CJ,QAAQ,CAACK,OAAO,CAACC,SAAS,CAACH,KAAK,EAAEC,WAAW,CAAC;EAChD,CAAC;EACD3C,KAAK,CAAC8C,mBAAmB,CAAC5B,GAAG,EAAE,OAAO;IACpCuB,IAAI;IACJM,QAAQ,EAAER,QAAQ,CAACK,OAAO,CAACI,WAAW,CAACD,QAAQ;IAC/CC,WAAW,EAAET,QAAQ,CAACK,OAAO,CAACI,WAAW;IACzCC,IAAI,EAAEV,QAAQ,CAACK,OAAO,CAACM,SAAS;IAChCC,IAAI,EAAEZ,QAAQ,CAACK,OAAO,CAACQ;EACzB,CAAC,CAAC,EAAE,CAACb,QAAQ,CAACK,OAAO,CAAC,CAAC;EACvB,MAAMS,SAAS,GAAGrD,KAAK,CAACwC,MAAM,CAACxC,KAAK,CAACsD,QAAQ,CAACC,KAAK,CAACtC,KAAK,CAACuC,QAAQ,CAAC,CAAC;EACpExD,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpB,IAAIJ,SAAS,CAACT,OAAO,KAAK5C,KAAK,CAACsD,QAAQ,CAACC,KAAK,CAACtC,KAAK,CAACuC,QAAQ,CAAC,EAAE;MAC9Df,IAAI,CAACxB,KAAK,CAACyC,YAAY,IAAI,CAAC,EAAE,KAAK,CAAC;MACpCL,SAAS,CAACT,OAAO,GAAG5C,KAAK,CAACsD,QAAQ,CAACC,KAAK,CAACtC,KAAK,CAACuC,QAAQ,CAAC;IAC1D;EACF,CAAC,EAAE,CAACvC,KAAK,CAACuC,QAAQ,CAAC,CAAC;EACpB,MAAMG,QAAQ,GAAGpE,MAAM,CAACsB,MAAM,CAAC;IAC7Ba,QAAQ;IACRE,SAAS,EAAE1B,UAAU,CAAC2B,eAAe,EAAEQ,gBAAgB,CAAC;IACxDP,KAAK,EAAEvC,MAAM,CAACsB,MAAM,CAACtB,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAEyB,YAAY,CAAC,EAAER,KAAK,CAAC;IAC5DE,QAAQ,EAAE,CAAC,CAACA;EACd,CAAC,EAAEE,UAAU,CAAC;EACd,IAAIyB,QAAQ,CAACC,MAAM,KAAK,MAAM,EAAE;IAC9BD,QAAQ,CAACE,IAAI,GAAG,IAAI;EACtB;EACA,MAAMC,SAAS,GAAG3B,YAAY,CAAC,UAAU,EAAEwB,QAAQ,CAACG,SAAS,CAAC;EAC9D,MAAMC,UAAU,GAAG,CAAC,CAAC5C,IAAI;EACzB,MAAM6C,OAAO,GAAG9D,UAAU,CAACI,SAAS,EAAE,GAAGA,SAAS,IAAImB,WAAW,EAAE,EAAE,OAAON,IAAI,KAAK,SAAS,GAAG,KAAK,GAAGA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,SAAS,CAAC;EACpK,MAAM,CAACqC,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG/D,QAAQ,CAAC0D,SAAS,CAAC;EAC3D,MAAMlC,SAAS,GAAG1B,UAAU,CAAC4D,SAAS,EAAE;IACtC,CAAC,GAAGA,SAAS,MAAM,GAAG1B,SAAS,KAAK,KAAK;IACzC,CAAC,GAAG0B,SAAS,WAAW,GAAGH,QAAQ,CAACjC;EACtC,CAAC,EAAEwC,MAAM,EAAEC,SAAS,EAAExC,aAAa,CAAC;EACpC,MAAMyC,kBAAkB,GAAGpC,QAAQ,KAAK,OAAOA,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,CAACqC,WAAW,GAAG,KAAK,CAAC;EACpG,MAAMC,gBAAgB,GAAGF,kBAAkB,GAAG;IAC5C,CAAC/D,WAAW,GAAG,GAAG4B,aAAa;EACjC,CAAC,GAAG,CAAC,CAAC;EACN,OAAOgC,UAAU,CAAC,aAAajE,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IACxDgB,SAAS,EAAEA,SAAS;IACpBG,EAAE,EAAEA,EAAE;IACND,KAAK,EAAEwC;EACT,CAAC,EAAE,aAAatE,KAAK,CAACY,aAAa,CAACX,aAAa,EAAEV,MAAM,CAACsB,MAAM,CAAC;IAC/DK,GAAG,EAAEqB;EACP,CAAC,EAAEoB,QAAQ,EAAE;IACXxC,IAAI,EAAE4C,UAAU;IAChBzD,SAAS,EAAE0D,OAAO;IAClB5C,MAAM,EAAEA,MAAM;IACdC,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA,SAAS;IACpBgD,eAAe,EAAE7C,QAAQ;IACzBO,aAAa,EAAEA,aAAa;IAC5BT,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACF,IAAIgD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC3D,QAAQ,CAAC4D,WAAW,GAAG,UAAU;AACnC;AACA,eAAe5D,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}