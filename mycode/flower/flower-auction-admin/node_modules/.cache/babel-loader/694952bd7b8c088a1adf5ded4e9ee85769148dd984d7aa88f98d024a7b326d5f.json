{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Typography, Row, Col, InputNumber, Image, Popconfirm, Statistic, Badge, Tooltip, DatePicker } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined, AuditOutlined } from '@ant-design/icons';\n// import type { ColumnsType } from 'antd/es/table';\nimport { auctionService } from '../../../services/auctionService';\nimport { productService } from '../../../services/productService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\n\n// 拍卖商品状态枚举\nexport let AuctionItemStatus = /*#__PURE__*/function (AuctionItemStatus) {\n  AuctionItemStatus[AuctionItemStatus[\"PENDING\"] = 0] = \"PENDING\";\n  // 待拍卖\n  AuctionItemStatus[AuctionItemStatus[\"ONGOING\"] = 1] = \"ONGOING\";\n  // 拍卖中\n  AuctionItemStatus[AuctionItemStatus[\"SOLD\"] = 2] = \"SOLD\";\n  // 已成交\n  AuctionItemStatus[AuctionItemStatus[\"UNSOLD\"] = 3] = \"UNSOLD\";\n  // 流拍\n  AuctionItemStatus[AuctionItemStatus[\"WITHDRAWN\"] = 4] = \"WITHDRAWN\"; // 撤回\n  return AuctionItemStatus;\n}({});\n\n// 拍卖商品接口\n\n// 查询参数接口\n\nconst AuctionItems = () => {\n  _s();\n  const [auctionItems, setAuctionItems] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [auctions, setAuctions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingItem, setEditingItem] = useState(null);\n  const [saving, setSaving] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalItems: 0,\n    pendingItems: 0,\n    ongoingItems: 0,\n    soldItems: 0,\n    totalValue: 0\n  });\n  const [form] = Form.useForm();\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 拍卖商品状态映射\n  const itemStatusMap = {\n    [AuctionItemStatus.PENDING]: {\n      label: '待拍卖',\n      color: 'default'\n    },\n    [AuctionItemStatus.ONGOING]: {\n      label: '拍卖中',\n      color: 'blue'\n    },\n    [AuctionItemStatus.SOLD]: {\n      label: '已成交',\n      color: 'green'\n    },\n    [AuctionItemStatus.UNSOLD]: {\n      label: '流拍',\n      color: 'orange'\n    },\n    [AuctionItemStatus.WITHDRAWN]: {\n      label: '撤回',\n      color: 'red'\n    }\n  };\n\n  // 获取拍卖商品列表\n  const fetchAuctionItems = async () => {\n    setLoading(true);\n    try {\n      // 调用后端API获取拍卖商品列表\n      const apiParams = {\n        ...queryParams,\n        // 确保状态参数是数字类型\n        status: typeof queryParams.status === 'number' ? queryParams.status : undefined\n      };\n      console.log('请求参数:', apiParams);\n      console.log('请求URL:', `/auction-items`);\n      const response = await auctionService.getAuctionItemList(apiParams);\n      console.log('API响应完整数据:', JSON.stringify(response));\n      if (response.success && response.data) {\n        // 检查数据结构\n        console.log('响应数据结构:', Object.keys(response.data));\n\n        // 确保list字段存在且是数组\n        const itemsList = response.data.list || [];\n        if (!Array.isArray(itemsList)) {\n          console.error('列表数据不是数组:', itemsList);\n          setAuctionItems([]);\n          setTotal(0);\n          return;\n        }\n        console.log('原始拍卖商品数据:', itemsList);\n\n        // 处理拍卖商品数据，映射字段\n        const mappedItems = itemsList.map(item => {\n          console.log('处理项目:', item);\n\n          // 从product中获取商品信息\n          const product = item.product || {};\n          console.log('商品信息:', product);\n\n          // 创建映射后的对象\n          const mappedItem = {\n            id: item.id || 0,\n            auctionId: item.auctionId || 0,\n            productId: item.productId || 0,\n            productName: product.name || item.productName || '未知商品',\n            productCode: product.code || item.productCode || `商品${item.productId || 0}`,\n            quantity: product.quantity || item.quantity || 1,\n            unit: product.unit || item.unit || '件',\n            images: product.images || item.images || [],\n            startingPrice: item.startPrice || item.startingPrice || 0,\n            currentPrice: item.currentPrice || item.startPrice || item.startingPrice || 0,\n            reservePrice: item.reservePrice || 0,\n            bidIncrement: item.stepPrice || item.bidIncrement || 10,\n            bidCount: item.totalBids || item.bidCount || 0,\n            highestBidder: item.winnerName || item.highestBidder || '',\n            status: typeof item.status === 'number' ? item.status : item.status === 'PENDING' ? AuctionItemStatus.PENDING : item.status === 'ONGOING' ? AuctionItemStatus.ONGOING : item.status === 'SOLD' ? AuctionItemStatus.SOLD : item.status === 'UNSOLD' ? AuctionItemStatus.UNSOLD : item.status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : AuctionItemStatus.PENDING,\n            createdAt: item.createdAt || '',\n            updatedAt: item.updatedAt || '',\n            category: product.category || item.category || '',\n            quality: product.qualityLevel || item.quality || ''\n          };\n          console.log('映射后的项目:', mappedItem);\n          return mappedItem;\n        });\n        console.log('最终映射后的数据:', mappedItems);\n        setAuctionItems(mappedItems);\n        setTotal(response.data.total || 0);\n\n        // 更新统计信息\n        const pendingItems = mappedItems.filter(item => item.status === AuctionItemStatus.PENDING).length;\n        const ongoingItems = mappedItems.filter(item => item.status === AuctionItemStatus.ONGOING).length;\n        const soldItems = mappedItems.filter(item => item.status === AuctionItemStatus.SOLD).length;\n        const totalValue = mappedItems.reduce((sum, item) => sum + item.currentPrice, 0);\n        setStatistics({\n          totalItems: mappedItems.length,\n          pendingItems,\n          ongoingItems,\n          soldItems,\n          totalValue\n        });\n      } else {\n        console.warn('拍卖商品数据格式异常:', response);\n        setAuctionItems([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      console.error('获取拍卖商品列表失败:', error);\n      message.error(error.message || '获取拍卖商品列表失败');\n      setAuctionItems([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品列表（用于添加拍卖商品）\n  const fetchProducts = async () => {\n    try {\n      const response = await productService.getProductList({\n        page: 1,\n        pageSize: 100,\n        auditStatus: 'approved' // 只获取已审核的商品\n      });\n      if (response.success && response.data && response.data.list) {\n        setProducts(response.data.list);\n        console.log('获取到商品列表:', response.data.list);\n      } else {\n        console.warn('商品数据格式异常:', response);\n        setProducts([]);\n      }\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      setProducts([]);\n    }\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    try {\n      const response = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 100\n      });\n      if (response.success && response.data && response.data.list) {\n        // 将后端的name字段映射为前端的title字段\n        const mappedAuctions = response.data.list.map(auction => ({\n          ...auction,\n          title: auction.name || auction.title // 后端返回name，前端使用title\n        }));\n        setAuctions(mappedAuctions);\n        console.log('获取到拍卖会列表:', mappedAuctions);\n      } else {\n        console.warn('拍卖会数据格式异常:', response);\n        setAuctions([]);\n      }\n    } catch (error) {\n      console.error('获取拍卖会列表失败:', error);\n      setAuctions([]);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      // 这里需要调用后端API获取统计信息\n      // 暂时使用模拟数据\n      setStatistics({\n        totalItems: 0,\n        pendingItems: 0,\n        ongoingItems: 0,\n        soldItems: 0,\n        totalValue: 0\n      });\n    } catch (error) {\n      console.error('获取统计信息失败:', error);\n    }\n  };\n\n  // 检查API连接\n  const checkApiConnection = async () => {\n    try {\n      message.loading('正在检查API连接...');\n\n      // 尝试请求API基础URL\n      const baseUrl = process.env.REACT_APP_API_BASE_URL;\n\n      // 使用fetch直接请求，避免axios拦截器的影响\n      const response = await fetch(`${baseUrl}/auction-items?page=1&pageSize=1`);\n      const data = await response.json();\n      console.log('API连接测试结果:', data);\n      if (response.ok) {\n        message.success(`API连接正常，响应状态: ${response.status}`);\n      } else {\n        message.error(`API连接异常，响应状态: ${response.status}`);\n      }\n\n      // 显示响应数据结构\n      if (data) {\n        console.log('响应数据结构:', Object.keys(data));\n        message.info(`响应数据结构: ${JSON.stringify(Object.keys(data))}`);\n      }\n    } catch (error) {\n      console.error('API连接测试失败:', error);\n      message.error(`API连接测试失败: ${error.message}`);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    // 修改初始化请求，减少重复请求\n    const initData = async () => {\n      setLoading(true);\n      try {\n        console.log('开始初始化数据加载');\n        // 并行请求数据，减少请求次数\n        const [itemsResponse, auctionsResponse, productsResponse] = await Promise.all([auctionService.getAuctionItemList({\n          ...queryParams,\n          // 枚举现在已经是数字类型，可以直接使用\n          status: queryParams.status,\n          pageSize: 50 // 使用更大的页面大小，减少请求次数\n        }), auctionService.getAuctionList({\n          page: 1,\n          pageSize: 50 // 使用更大的页面大小，减少请求次数\n        }), productService.getProductList({\n          page: 1,\n          pageSize: 50,\n          // 使用更大的页面大小，减少请求次数\n          auditStatus: 'approved'\n        })]);\n        console.log('初始化数据请求完成');\n        console.log('拍卖商品响应:', JSON.stringify(itemsResponse));\n        console.log('拍卖会响应:', JSON.stringify(auctionsResponse));\n        console.log('商品响应:', JSON.stringify(productsResponse));\n\n        // 处理拍卖会数据\n        if (auctionsResponse.success && auctionsResponse.data && auctionsResponse.data.list) {\n          const mappedAuctions = auctionsResponse.data.list.map(auction => ({\n            ...auction,\n            title: auction.name || auction.title\n          }));\n          setAuctions(mappedAuctions);\n          console.log('处理后的拍卖会数据:', mappedAuctions);\n        }\n\n        // 处理商品数据\n        if (productsResponse.success && productsResponse.data && productsResponse.data.list) {\n          setProducts(productsResponse.data.list);\n          console.log('处理后的商品数据:', productsResponse.data.list);\n        }\n\n        // 处理拍卖商品数据\n        if (itemsResponse.success && itemsResponse.data) {\n          console.log('拍卖商品数据结构:', Object.keys(itemsResponse.data));\n\n          // 确保list字段存在且是数组\n          const itemsList = itemsResponse.data.list || [];\n          if (!Array.isArray(itemsList)) {\n            console.error('列表数据不是数组:', itemsList);\n            setAuctionItems([]);\n            setTotal(0);\n            return;\n          }\n          console.log('拍卖商品原始数据:', itemsList);\n          const mappedItems = itemsList.map(item => {\n            // 从product中获取商品信息\n            const product = item.product || {};\n            console.log('处理商品项:', item.id, '商品信息:', product);\n\n            // 状态字段现在是数字，可以直接使用\n            const status = typeof item.status === 'number' ? item.status : item.status === 'PENDING' ? AuctionItemStatus.PENDING : item.status === 'ONGOING' ? AuctionItemStatus.ONGOING : item.status === 'SOLD' ? AuctionItemStatus.SOLD : item.status === 'UNSOLD' ? AuctionItemStatus.UNSOLD : item.status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : AuctionItemStatus.PENDING;\n            return {\n              id: item.id,\n              auctionId: item.auctionId,\n              productId: item.productId,\n              productName: product.name || item.productName || '未知商品',\n              productCode: product.code || item.productCode || `商品${item.productId}`,\n              quantity: product.quantity || item.quantity || 1,\n              unit: product.unit || item.unit || '件',\n              images: product.images || item.images || [],\n              // 价格字段映射\n              startingPrice: item.startPrice || item.startingPrice || 0,\n              currentPrice: item.currentPrice || item.startPrice || item.startingPrice || 0,\n              reservePrice: item.reservePrice || 0,\n              bidIncrement: item.stepPrice || item.bidIncrement || 10,\n              bidCount: item.totalBids || item.bidCount || 0,\n              highestBidder: item.winnerName || item.highestBidder || '',\n              status: status,\n              createdAt: item.createdAt,\n              updatedAt: item.updatedAt,\n              category: product.category || item.category || '',\n              quality: product.qualityLevel || item.quality || ''\n            };\n          });\n          console.log('映射后的拍卖商品数据:', mappedItems);\n          setAuctionItems(mappedItems);\n          setTotal(itemsResponse.data.total || 0);\n\n          // 更新统计信息\n          const pendingItems = mappedItems.filter(item => item.status === AuctionItemStatus.PENDING).length;\n          const ongoingItems = mappedItems.filter(item => item.status === AuctionItemStatus.ONGOING).length;\n          const soldItems = mappedItems.filter(item => item.status === AuctionItemStatus.SOLD).length;\n          const totalValue = mappedItems.reduce((sum, item) => sum + item.currentPrice, 0);\n          setStatistics({\n            totalItems: mappedItems.length,\n            pendingItems,\n            ongoingItems,\n            soldItems,\n            totalValue\n          });\n        } else {\n          console.warn('拍卖商品数据格式异常:', itemsResponse);\n          setAuctionItems([]);\n          setTotal(0);\n        }\n      } catch (error) {\n        console.error('初始化数据失败:', error);\n        message.error('获取数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // 首次加载或重置查询参数时，使用并行请求\n    if (queryParams.page === 1 && !queryParams.auctionId && !queryParams.productName && !queryParams.status) {\n      initData();\n    } else {\n      // 搜索或翻页时，只请求拍卖商品列表\n      fetchAuctionItems();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增拍卖商品\n  const handleAdd = () => {\n    setEditingItem(null);\n    form.resetFields();\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 编辑拍卖商品\n  const handleEdit = item => {\n    setEditingItem(item);\n    form.setFieldsValue(item);\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖商品\n  const handleDelete = async id => {\n    try {\n      // 调用删除API\n      message.success('删除成功');\n      fetchAuctionItems();\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖商品\n  const handleSave = async values => {\n    setSaving(true);\n    clearAllMessages();\n    try {\n      let response;\n      if (editingItem) {\n        // 更新拍卖商品 - 目前暂不支持更新，显示提示信息\n        setFormError('暂不支持更新拍卖商品，请删除后重新添加');\n        setSaving(false);\n        return;\n      } else {\n        // 添加拍卖商品\n        response = await auctionService.addAuctionItem(values.auctionId, {\n          productId: values.productId,\n          startingPrice: values.startingPrice,\n          reservePrice: values.reservePrice,\n          bidIncrement: values.bidIncrement,\n          startTime: values.startTime ? values.startTime.toISOString() : undefined\n        });\n      }\n      const successMsg = '拍卖商品添加成功！';\n      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingItem(null);\n          clearAllMessages();\n          fetchAuctionItems();\n          fetchStatistics();\n        }, 1500);\n      }\n    } catch (error) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u62CD\\u5356\\u5546\\u54C1\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5546\\u54C1\\u6570\",\n            value: statistics.totalItems,\n            prefix: /*#__PURE__*/_jsxDEV(AuditOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u62CD\\u5356\",\n            value: statistics.pendingItems,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u62CD\\u5356\\u4E2D\",\n            value: statistics.ongoingItems,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u6210\\u4EA4\",\n            value: statistics.soldItems,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 590,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"productName\",\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u62CD\\u5356\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.PENDING,\n                  children: \"\\u5F85\\u62CD\\u5356\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.ONGOING,\n                  children: \"\\u62CD\\u5356\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.SOLD,\n                  children: \"\\u5DF2\\u6210\\u4EA4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.UNSOLD,\n                  children: \"\\u6D41\\u62CD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.WITHDRAWN,\n                  children: \"\\u64A4\\u56DE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"auctionId\",\n              label: \"\\u62CD\\u5356\\u4F1A\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u4F1A\",\n                allowClear: true,\n                children: auctions.map(auction => /*#__PURE__*/_jsxDEV(Option, {\n                  value: auction.id,\n                  children: auction.title\n                }, auction.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u6DFB\\u52A0\\u62CD\\u5356\\u5546\\u54C1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 21\n            }, this),\n            onClick: fetchAuctionItems,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u5F53\\u524D\\u6570\\u636E\\u72B6\\u6001: \", loading ? '加载中...' : `共 ${auctionItems.length} 条记录`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 11\n        }, this), auctionItems.length === 0 && !loading && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'red'\n          },\n          children: \"\\u672A\\u627E\\u5230\\u62CD\\u5356\\u5546\\u54C1\\u6570\\u636E\\uFF0C\\u8BF7\\u68C0\\u67E5\\u6570\\u636E\\u6620\\u5C04\\u6216\\u7F51\\u7EDC\\u8BF7\\u6C42\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: () => {\n            console.log('当前拍卖商品数据:', auctionItems);\n            message.info(`当前数据条数: ${auctionItems.length}`);\n          },\n          style: {\n            marginRight: 8\n          },\n          children: \"\\u8C03\\u8BD5\\u6570\\u636E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: checkApiConnection,\n          style: {\n            marginRight: 8\n          },\n          children: \"\\u6D4B\\u8BD5API\\u8FDE\\u63A5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: fetchAuctionItems,\n          style: {\n            marginRight: 8\n          },\n          children: \"\\u91CD\\u65B0\\u83B7\\u53D6\\u6570\\u636E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: [{\n          title: 'ID',\n          dataIndex: 'id',\n          key: 'id',\n          width: 80\n        }, {\n          title: '商品信息',\n          key: 'product',\n          width: 250,\n          render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [record.images && record.images.length > 0 && /*#__PURE__*/_jsxDEV(Image, {\n              width: 60,\n              height: 60,\n              src: record.images[0],\n              style: {\n                marginRight: 12,\n                borderRadius: 4\n              },\n              fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 500\n                },\n                children: record.productName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 12,\n                  color: '#999'\n                },\n                children: [\"\\u7F16\\u53F7: \", record.productCode]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 12,\n                  color: '#999'\n                },\n                children: [record.quantity, \" \", record.unit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 17\n          }, this)\n        }, {\n          title: '拍卖状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 100,\n          render: status => {\n            console.log('渲染状态:', status, typeof status);\n            // 确保状态是数字类型\n            const numStatus = typeof status === 'number' ? status : status === 'PENDING' ? AuctionItemStatus.PENDING : status === 'ONGOING' ? AuctionItemStatus.ONGOING : status === 'SOLD' ? AuctionItemStatus.SOLD : status === 'UNSOLD' ? AuctionItemStatus.UNSOLD : status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : AuctionItemStatus.PENDING;\n            const statusInfo = itemStatusMap[numStatus];\n            console.log('状态信息:', statusInfo);\n            return /*#__PURE__*/_jsxDEV(Badge, {\n              status: numStatus === AuctionItemStatus.ONGOING ? 'processing' : numStatus === AuctionItemStatus.SOLD ? 'success' : numStatus === AuctionItemStatus.UNSOLD ? 'warning' : numStatus === AuctionItemStatus.WITHDRAWN ? 'error' : 'default',\n              text: /*#__PURE__*/_jsxDEV(Tag, {\n                color: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || 'default',\n                children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.label) || `未知(${numStatus})`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 19\n            }, this);\n          }\n        }, {\n          title: '价格信息',\n          key: 'price',\n          width: 150,\n          render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u8D77\\u62CD: \\xA5\", record.startingPrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#f50',\n                fontWeight: 500\n              },\n              children: [\"\\u5F53\\u524D: \\xA5\", record.currentPrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 19\n            }, this), record.reservePrice && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: 12,\n                color: '#999'\n              },\n              children: [\"\\u4FDD\\u7559: \\xA5\", record.reservePrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 17\n          }, this)\n        }, {\n          title: '竞价信息',\n          key: 'bid',\n          width: 120,\n          render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u51FA\\u4EF7\\u6B21\\u6570: \", record.bidCount]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u52A0\\u4EF7\\u5E45\\u5EA6: \\xA5\", record.bidIncrement.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 19\n            }, this), record.highestBidder && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: 12,\n                color: '#999'\n              },\n              children: [\"\\u6700\\u9AD8\\u51FA\\u4EF7\\u4EBA: \", record.highestBidder]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 17\n          }, this)\n        }, {\n          title: '创建时间',\n          dataIndex: 'createdAt',\n          key: 'createdAt',\n          width: 160,\n          render: text => new Date(text).toLocaleString()\n        }, {\n          title: '操作',\n          key: 'action',\n          width: 200,\n          fixed: 'right',\n          render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 29\n                }, this),\n                onClick: () => {/* 查看详情 */}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u7F16\\u8F91\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleEdit(record),\n                disabled: record.status === AuctionItemStatus.ONGOING\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n              title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u62CD\\u5356\\u5546\\u54C1\\u5417\\uFF1F\",\n              onConfirm: () => handleDelete(record.id),\n              okText: \"\\u786E\\u5B9A\",\n              cancelText: \"\\u53D6\\u6D88\",\n              children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u5220\\u9664\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  size: \"small\",\n                  danger: true,\n                  icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 31\n                  }, this),\n                  disabled: record.status === AuctionItemStatus.ONGOING\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 17\n          }, this)\n        }],\n        dataSource: auctionItems,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 710,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingItem ? '编辑拍卖商品' : '添加拍卖商品',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"auctionId\",\n          label: \"\\u62CD\\u5356\\u4F1A\",\n          rules: [{\n            required: true,\n            message: '请选择拍卖会'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u4F1A\",\n            children: auctions.map(auction => /*#__PURE__*/_jsxDEV(Option, {\n              value: auction.id,\n              children: auction.title\n            }, auction.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 903,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"productId\",\n          label: \"\\u5546\\u54C1\",\n          rules: [{\n            required: true,\n            message: '请选择商品'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\",\n            showSearch: true,\n            optionFilterProp: \"children\",\n            children: products.map(product => /*#__PURE__*/_jsxDEV(Option, {\n              value: product.id,\n              children: [product.name, \" - \", product.code]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 917,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"startingPrice\",\n              label: \"\\u8D77\\u62CD\\u4EF7\",\n              rules: [{\n                required: true,\n                message: '请输入起拍价'\n              }, {\n                type: 'number',\n                min: 0.01,\n                message: '起拍价必须大于0'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D77\\u62CD\\u4EF7\",\n                precision: 2,\n                min: 0.01,\n                addonBefore: \"\\xA5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"reservePrice\",\n              label: \"\\u4FDD\\u7559\\u4EF7\\uFF08\\u53EF\\u9009\\uFF09\",\n              rules: [{\n                type: 'number',\n                min: 0.01,\n                message: '保留价必须大于0'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4FDD\\u7559\\u4EF7\",\n                precision: 2,\n                min: 0.01,\n                addonBefore: \"\\xA5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"bidIncrement\",\n          label: \"\\u52A0\\u4EF7\\u5E45\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入加价幅度'\n          }, {\n            type: 'number',\n            min: 0.01,\n            message: '加价幅度必须大于0'\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u52A0\\u4EF7\\u5E45\\u5EA6\",\n            precision: 2,\n            min: 0.01,\n            addonBefore: \"\\xA5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"startTime\",\n          label: \"\\u5F00\\u59CB\\u65F6\\u95F4\",\n          rules: [{\n            required: true,\n            message: '请选择开始时间'\n          }],\n          children: /*#__PURE__*/_jsxDEV(DatePicker, {\n            style: {\n              width: '100%'\n            },\n            showTime: true,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65F6\\u95F4\",\n            format: \"YYYY-MM-DD HH:mm:ss\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 986,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormMessage, {\n          type: \"error\",\n          message: formError,\n          visible: !!formError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormMessage, {\n          type: \"success\",\n          message: formSuccess,\n          visible: !!formSuccess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setIsModalVisible(false);\n                form.resetFields();\n                setEditingItem(null);\n                clearAllMessages();\n              },\n              disabled: saving,\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: saving,\n              disabled: saving,\n              children: saving ? '保存中...' : editingItem ? '更新' : '添加'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1005,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 890,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 557,\n    columnNumber: 5\n  }, this);\n};\n_s(AuctionItems, \"KkBrZ+BpuiNcLHnPDsqn2pUq398=\", false, function () {\n  return [Form.useForm, useFormMessage];\n});\n_c = AuctionItems;\nexport default AuctionItems;\nvar _c;\n$RefreshReg$(_c, \"AuctionItems\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Typography", "Row", "Col", "InputNumber", "Image", "Popconfirm", "Statistic", "Badge", "<PERSON><PERSON><PERSON>", "DatePicker", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ReloadOutlined", "AuditOutlined", "auctionService", "productService", "FormMessage", "useFormMessage", "handleApiResponse", "handleApiError", "jsxDEV", "_jsxDEV", "Title", "Option", "AuctionItemStatus", "AuctionItems", "_s", "auctionItems", "setAuctionItems", "products", "setProducts", "auctions", "setAuctions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "editingItem", "setEditingItem", "saving", "setSaving", "statistics", "setStatistics", "totalItems", "pendingItems", "ongoingItems", "soldItems", "totalValue", "form", "useForm", "formError", "formSuccess", "setFormError", "setFormSuccess", "clearAllMessages", "itemStatusMap", "PENDING", "label", "color", "ONGOING", "SOLD", "UNSOLD", "WITHDRAWN", "fetchAuctionItems", "apiParams", "status", "undefined", "console", "log", "response", "getAuctionItemList", "JSON", "stringify", "success", "data", "Object", "keys", "itemsList", "list", "Array", "isArray", "error", "mappedItems", "map", "item", "product", "mappedItem", "id", "auctionId", "productId", "productName", "name", "productCode", "code", "quantity", "unit", "images", "startingPrice", "startPrice", "currentPrice", "reservePrice", "bidIncrement", "step<PERSON><PERSON>", "bidCount", "totalBids", "highestBidder", "winner<PERSON><PERSON>", "createdAt", "updatedAt", "category", "quality", "qualityLevel", "filter", "length", "reduce", "sum", "warn", "fetchProducts", "getProductList", "auditStatus", "fetchAuctions", "getAuctionList", "mappedAuctions", "auction", "title", "fetchStatistics", "checkApiConnection", "baseUrl", "process", "env", "REACT_APP_API_BASE_URL", "fetch", "json", "ok", "info", "initData", "itemsResponse", "auctionsResponse", "productsResponse", "Promise", "all", "handleSearch", "values", "handleReset", "handleAdd", "resetFields", "handleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "handleSave", "addAuctionItem", "startTime", "toISOString", "successMsg", "setTimeout", "style", "padding", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "marginBottom", "xs", "sm", "md", "value", "prefix", "valueStyle", "className", "size", "layout", "onFinish", "autoComplete", "width", "<PERSON><PERSON>", "placeholder", "allowClear", "type", "htmlType", "icon", "onClick", "justify", "align", "marginRight", "columns", "dataIndex", "key", "render", "_", "record", "display", "alignItems", "height", "src", "borderRadius", "fallback", "fontWeight", "fontSize", "numStatus", "statusInfo", "text", "toFixed", "Date", "toLocaleString", "fixed", "disabled", "onConfirm", "okText", "cancelText", "danger", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "open", "onCancel", "footer", "rules", "required", "showSearch", "optionFilterProp", "span", "min", "precision", "addonBefore", "showTime", "format", "visible", "justifyContent", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Typography,\n  Row,\n  Col,\n  InputNumber,\n  Image,\n  Popconfirm,\n  Statistic,\n  Badge,\n  Tooltip,\n  DatePicker,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  AuditOutlined,\n} from '@ant-design/icons';\n// import type { ColumnsType } from 'antd/es/table';\nimport { auctionService } from '../../../services/auctionService';\nimport { productService } from '../../../services/productService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';\n\nconst { Title } = Typography;\nconst { Option } = Select;\n\n// 拍卖商品状态枚举\nexport enum AuctionItemStatus {\n  PENDING = 0,     // 待拍卖\n  ONGOING = 1,     // 拍卖中\n  SOLD = 2,        // 已成交\n  UNSOLD = 3,      // 流拍\n  WITHDRAWN = 4,   // 撤回\n}\n\n// 拍卖商品接口\nexport interface AuctionItem {\n  id: number;\n  auctionId: number;\n  productId: number;\n  productName: string;\n  productCode: string;\n  startingPrice: number;\n  currentPrice: number;\n  reservePrice?: number;\n  bidIncrement: number;\n  bidCount: number;\n  status: AuctionItemStatus;\n  images?: string[];\n  category: string;\n  quality: string;\n  quantity: number;\n  unit: string;\n  highestBidder?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数接口\ninterface AuctionItemQueryParams {\n  auctionId?: number;\n  productName?: string;\n  status?: AuctionItemStatus;\n  category?: string;\n  page: number;\n  pageSize: number;\n}\n\nconst AuctionItems: React.FC = () => {\n  const [auctionItems, setAuctionItems] = useState<AuctionItem[]>([]);\n  const [products, setProducts] = useState<any[]>([]);\n  const [auctions, setAuctions] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<AuctionItemQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingItem, setEditingItem] = useState<AuctionItem | null>(null);\n  const [saving, setSaving] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalItems: 0,\n    pendingItems: 0,\n    ongoingItems: 0,\n    soldItems: 0,\n    totalValue: 0,\n  });\n  const [form] = Form.useForm();\n\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 拍卖商品状态映射\n  const itemStatusMap = {\n    [AuctionItemStatus.PENDING]: { label: '待拍卖', color: 'default' },\n    [AuctionItemStatus.ONGOING]: { label: '拍卖中', color: 'blue' },\n    [AuctionItemStatus.SOLD]: { label: '已成交', color: 'green' },\n    [AuctionItemStatus.UNSOLD]: { label: '流拍', color: 'orange' },\n    [AuctionItemStatus.WITHDRAWN]: { label: '撤回', color: 'red' },\n  };\n\n  // 获取拍卖商品列表\n  const fetchAuctionItems = async () => {\n    setLoading(true);\n    try {\n      // 调用后端API获取拍卖商品列表\n      const apiParams = {\n        ...queryParams,\n        // 确保状态参数是数字类型\n        status: typeof queryParams.status === 'number' ? queryParams.status : undefined,\n      };\n      \n      console.log('请求参数:', apiParams);\n      console.log('请求URL:', `/auction-items`);\n      \n      const response = await auctionService.getAuctionItemList(apiParams);\n      console.log('API响应完整数据:', JSON.stringify(response));\n\n      if (response.success && response.data) {\n        // 检查数据结构\n        console.log('响应数据结构:', Object.keys(response.data));\n        \n        // 确保list字段存在且是数组\n        const itemsList = response.data.list || [];\n        if (!Array.isArray(itemsList)) {\n          console.error('列表数据不是数组:', itemsList);\n          setAuctionItems([]);\n          setTotal(0);\n          return;\n        }\n        \n        console.log('原始拍卖商品数据:', itemsList);\n        \n        // 处理拍卖商品数据，映射字段\n        const mappedItems = itemsList.map((item: any) => {\n          console.log('处理项目:', item);\n          \n          // 从product中获取商品信息\n          const product = item.product || {};\n          console.log('商品信息:', product);\n          \n          // 创建映射后的对象\n          const mappedItem: AuctionItem = {\n            id: item.id || 0,\n            auctionId: item.auctionId || 0,\n            productId: item.productId || 0,\n            productName: product.name || item.productName || '未知商品',\n            productCode: product.code || item.productCode || `商品${item.productId || 0}`,\n            quantity: product.quantity || item.quantity || 1,\n            unit: product.unit || item.unit || '件',\n            images: product.images || item.images || [],\n            startingPrice: item.startPrice || item.startingPrice || 0,\n            currentPrice: item.currentPrice || item.startPrice || item.startingPrice || 0,\n            reservePrice: item.reservePrice || 0,\n            bidIncrement: item.stepPrice || item.bidIncrement || 10,\n            bidCount: item.totalBids || item.bidCount || 0,\n            highestBidder: item.winnerName || item.highestBidder || '',\n            status: typeof item.status === 'number' ? item.status : \n                   item.status === 'PENDING' ? AuctionItemStatus.PENDING :\n                   item.status === 'ONGOING' ? AuctionItemStatus.ONGOING :\n                   item.status === 'SOLD' ? AuctionItemStatus.SOLD :\n                   item.status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :\n                   item.status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : \n                   AuctionItemStatus.PENDING,\n            createdAt: item.createdAt || '',\n            updatedAt: item.updatedAt || '',\n            category: product.category || item.category || '',\n            quality: product.qualityLevel || item.quality || '',\n          };\n          \n          console.log('映射后的项目:', mappedItem);\n          return mappedItem;\n        });\n\n        console.log('最终映射后的数据:', mappedItems);\n        setAuctionItems(mappedItems);\n        setTotal(response.data.total || 0);\n        \n        // 更新统计信息\n        const pendingItems = mappedItems.filter(item => item.status === AuctionItemStatus.PENDING).length;\n        const ongoingItems = mappedItems.filter(item => item.status === AuctionItemStatus.ONGOING).length;\n        const soldItems = mappedItems.filter(item => item.status === AuctionItemStatus.SOLD).length;\n        const totalValue = mappedItems.reduce((sum, item) => sum + item.currentPrice, 0);\n        \n        setStatistics({\n          totalItems: mappedItems.length,\n          pendingItems,\n          ongoingItems,\n          soldItems,\n          totalValue,\n        });\n      } else {\n        console.warn('拍卖商品数据格式异常:', response);\n        setAuctionItems([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖商品列表失败:', error);\n      message.error(error.message || '获取拍卖商品列表失败');\n      setAuctionItems([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品列表（用于添加拍卖商品）\n  const fetchProducts = async () => {\n    try {\n      const response = await productService.getProductList({\n        page: 1,\n        pageSize: 100,\n        auditStatus: 'approved', // 只获取已审核的商品\n      });\n\n      if (response.success && response.data && response.data.list) {\n        setProducts(response.data.list);\n        console.log('获取到商品列表:', response.data.list);\n      } else {\n        console.warn('商品数据格式异常:', response);\n        setProducts([]);\n      }\n    } catch (error: any) {\n      console.error('获取商品列表失败:', error);\n      setProducts([]);\n    }\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    try {\n      const response = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 100,\n      });\n\n      if (response.success && response.data && response.data.list) {\n        // 将后端的name字段映射为前端的title字段\n        const mappedAuctions = response.data.list.map((auction: any) => ({\n          ...auction,\n          title: auction.name || auction.title, // 后端返回name，前端使用title\n        }));\n        setAuctions(mappedAuctions);\n        console.log('获取到拍卖会列表:', mappedAuctions);\n      } else {\n        console.warn('拍卖会数据格式异常:', response);\n        setAuctions([]);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会列表失败:', error);\n      setAuctions([]);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      // 这里需要调用后端API获取统计信息\n      // 暂时使用模拟数据\n      setStatistics({\n        totalItems: 0,\n        pendingItems: 0,\n        ongoingItems: 0,\n        soldItems: 0,\n        totalValue: 0,\n      });\n    } catch (error: any) {\n      console.error('获取统计信息失败:', error);\n    }\n  };\n\n  // 检查API连接\n  const checkApiConnection = async () => {\n    try {\n      message.loading('正在检查API连接...');\n      \n      // 尝试请求API基础URL\n      const baseUrl = process.env.REACT_APP_API_BASE_URL;\n      \n      // 使用fetch直接请求，避免axios拦截器的影响\n      const response = await fetch(`${baseUrl}/auction-items?page=1&pageSize=1`);\n      const data = await response.json();\n      \n      console.log('API连接测试结果:', data);\n      \n      if (response.ok) {\n        message.success(`API连接正常，响应状态: ${response.status}`);\n      } else {\n        message.error(`API连接异常，响应状态: ${response.status}`);\n      }\n      \n      // 显示响应数据结构\n      if (data) {\n        console.log('响应数据结构:', Object.keys(data));\n        message.info(`响应数据结构: ${JSON.stringify(Object.keys(data))}`);\n      }\n    } catch (error: any) {\n      console.error('API连接测试失败:', error);\n      message.error(`API连接测试失败: ${error.message}`);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    // 修改初始化请求，减少重复请求\n    const initData = async () => {\n      setLoading(true);\n      try {\n        console.log('开始初始化数据加载');\n        // 并行请求数据，减少请求次数\n        const [itemsResponse, auctionsResponse, productsResponse] = await Promise.all([\n          auctionService.getAuctionItemList({\n            ...queryParams,\n            // 枚举现在已经是数字类型，可以直接使用\n            status: queryParams.status,\n            pageSize: 50, // 使用更大的页面大小，减少请求次数\n          }),\n          auctionService.getAuctionList({\n            page: 1,\n            pageSize: 50, // 使用更大的页面大小，减少请求次数\n          }),\n          productService.getProductList({\n            page: 1,\n            pageSize: 50, // 使用更大的页面大小，减少请求次数\n            auditStatus: 'approved',\n          }),\n        ]);\n\n        console.log('初始化数据请求完成');\n        console.log('拍卖商品响应:', JSON.stringify(itemsResponse));\n        console.log('拍卖会响应:', JSON.stringify(auctionsResponse));\n        console.log('商品响应:', JSON.stringify(productsResponse));\n\n        // 处理拍卖会数据\n        if (auctionsResponse.success && auctionsResponse.data && auctionsResponse.data.list) {\n          const mappedAuctions = auctionsResponse.data.list.map((auction: any) => ({\n            ...auction,\n            title: auction.name || auction.title,\n          }));\n          setAuctions(mappedAuctions);\n          console.log('处理后的拍卖会数据:', mappedAuctions);\n        }\n\n        // 处理商品数据\n        if (productsResponse.success && productsResponse.data && productsResponse.data.list) {\n          setProducts(productsResponse.data.list);\n          console.log('处理后的商品数据:', productsResponse.data.list);\n        }\n\n        // 处理拍卖商品数据\n        if (itemsResponse.success && itemsResponse.data) {\n          console.log('拍卖商品数据结构:', Object.keys(itemsResponse.data));\n          \n          // 确保list字段存在且是数组\n          const itemsList = itemsResponse.data.list || [];\n          if (!Array.isArray(itemsList)) {\n            console.error('列表数据不是数组:', itemsList);\n            setAuctionItems([]);\n            setTotal(0);\n            return;\n          }\n          \n          console.log('拍卖商品原始数据:', itemsList);\n          \n          const mappedItems = itemsList.map((item: any) => {\n            // 从product中获取商品信息\n            const product = item.product || {};\n            console.log('处理商品项:', item.id, '商品信息:', product);\n            \n            // 状态字段现在是数字，可以直接使用\n            const status = typeof item.status === 'number' ? item.status : \n                          item.status === 'PENDING' ? AuctionItemStatus.PENDING :\n                          item.status === 'ONGOING' ? AuctionItemStatus.ONGOING :\n                          item.status === 'SOLD' ? AuctionItemStatus.SOLD :\n                          item.status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :\n                          item.status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : \n                          AuctionItemStatus.PENDING;\n            \n            return {\n              id: item.id,\n              auctionId: item.auctionId,\n              productId: item.productId,\n              productName: product.name || item.productName || '未知商品',\n              productCode: product.code || item.productCode || `商品${item.productId}`,\n              quantity: product.quantity || item.quantity || 1,\n              unit: product.unit || item.unit || '件',\n              images: product.images || item.images || [],\n              // 价格字段映射\n              startingPrice: item.startPrice || item.startingPrice || 0,\n              currentPrice: item.currentPrice || item.startPrice || item.startingPrice || 0,\n              reservePrice: item.reservePrice || 0,\n              bidIncrement: item.stepPrice || item.bidIncrement || 10,\n              bidCount: item.totalBids || item.bidCount || 0,\n              highestBidder: item.winnerName || item.highestBidder || '',\n              status: status,\n              createdAt: item.createdAt,\n              updatedAt: item.updatedAt,\n              category: product.category || item.category || '',\n              quality: product.qualityLevel || item.quality || '',\n            };\n          });\n\n          console.log('映射后的拍卖商品数据:', mappedItems);\n          setAuctionItems(mappedItems);\n          setTotal(itemsResponse.data.total || 0);\n          \n          // 更新统计信息\n          const pendingItems = mappedItems.filter(item => item.status === AuctionItemStatus.PENDING).length;\n          const ongoingItems = mappedItems.filter(item => item.status === AuctionItemStatus.ONGOING).length;\n          const soldItems = mappedItems.filter(item => item.status === AuctionItemStatus.SOLD).length;\n          const totalValue = mappedItems.reduce((sum, item) => sum + item.currentPrice, 0);\n          \n          setStatistics({\n            totalItems: mappedItems.length,\n            pendingItems,\n            ongoingItems,\n            soldItems,\n            totalValue,\n          });\n        } else {\n          console.warn('拍卖商品数据格式异常:', itemsResponse);\n          setAuctionItems([]);\n          setTotal(0);\n        }\n      } catch (error) {\n        console.error('初始化数据失败:', error);\n        message.error('获取数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // 首次加载或重置查询参数时，使用并行请求\n    if (\n      queryParams.page === 1 && \n      !queryParams.auctionId && \n      !queryParams.productName && \n      !queryParams.status\n    ) {\n      initData();\n    } else {\n      // 搜索或翻页时，只请求拍卖商品列表\n      fetchAuctionItems();\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增拍卖商品\n  const handleAdd = () => {\n    setEditingItem(null);\n    form.resetFields();\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 编辑拍卖商品\n  const handleEdit = (item: AuctionItem) => {\n    setEditingItem(item);\n    form.setFieldsValue(item);\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖商品\n  const handleDelete = async (id: number) => {\n    try {\n      // 调用删除API\n      message.success('删除成功');\n      fetchAuctionItems();\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖商品\n  const handleSave = async (values: any) => {\n    setSaving(true);\n    clearAllMessages();\n\n    try {\n      let response;\n      if (editingItem) {\n        // 更新拍卖商品 - 目前暂不支持更新，显示提示信息\n        setFormError('暂不支持更新拍卖商品，请删除后重新添加');\n        setSaving(false);\n        return;\n      } else {\n        // 添加拍卖商品\n        response = await auctionService.addAuctionItem(values.auctionId, {\n          productId: values.productId,\n          startingPrice: values.startingPrice,\n          reservePrice: values.reservePrice,\n          bidIncrement: values.bidIncrement,\n          startTime: values.startTime ? values.startTime.toISOString() : undefined,\n        });\n      }\n\n      const successMsg = '拍卖商品添加成功！';\n\n      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingItem(null);\n          clearAllMessages();\n          fetchAuctionItems();\n          fetchStatistics();\n        }, 1500);\n      }\n    } catch (error: any) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>拍卖商品管理</Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总商品数\"\n              value={statistics.totalItems}\n              prefix={<AuditOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"待拍卖\"\n              value={statistics.pendingItems}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"拍卖中\"\n              value={statistics.ongoingItems}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"已成交\"\n              value={statistics.soldItems}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\" style={{ marginBottom: 16 }}>\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"productName\" label=\"商品名称\">\n                <Input placeholder=\"请输入商品名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"拍卖状态\">\n                <Select placeholder=\"请选择拍卖状态\" allowClear>\n                  <Option value={AuctionItemStatus.PENDING}>待拍卖</Option>\n                  <Option value={AuctionItemStatus.ONGOING}>拍卖中</Option>\n                  <Option value={AuctionItemStatus.SOLD}>已成交</Option>\n                  <Option value={AuctionItemStatus.UNSOLD}>流拍</Option>\n                  <Option value={AuctionItemStatus.WITHDRAWN}>撤回</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"auctionId\" label=\"拍卖会\">\n                <Select placeholder=\"请选择拍卖会\" allowClear>\n                  {auctions.map(auction => (\n                    <Option key={auction.id} value={auction.id}>\n                      {auction.title}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\" style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              添加拍卖商品\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchAuctionItems}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 拍卖商品列表表格 */}\n      <Card>\n        {/* 添加调试信息，显示当前数据状态 */}\n        <div style={{ marginBottom: 16 }}>\n          <p>当前数据状态: {loading ? '加载中...' : `共 ${auctionItems.length} 条记录`}</p>\n          {auctionItems.length === 0 && !loading && (\n            <p style={{ color: 'red' }}>未找到拍卖商品数据，请检查数据映射或网络请求</p>\n          )}\n          {/* 添加调试按钮 */}\n          <Button \n            type=\"primary\" \n            onClick={() => {\n              console.log('当前拍卖商品数据:', auctionItems);\n              message.info(`当前数据条数: ${auctionItems.length}`);\n            }}\n            style={{ marginRight: 8 }}\n          >\n            调试数据\n          </Button>\n          <Button \n            type=\"primary\" \n            onClick={checkApiConnection}\n            style={{ marginRight: 8 }}\n          >\n            测试API连接\n          </Button>\n          <Button \n            onClick={fetchAuctionItems}\n            style={{ marginRight: 8 }}\n          >\n            重新获取数据\n          </Button>\n        </div>\n        \n        <Table\n          columns={[\n            {\n              title: 'ID',\n              dataIndex: 'id',\n              key: 'id',\n              width: 80,\n            },\n            {\n              title: '商品信息',\n              key: 'product',\n              width: 250,\n              render: (_, record: AuctionItem) => (\n                <div style={{ display: 'flex', alignItems: 'center' }}>\n                  {record.images && record.images.length > 0 && (\n                    <Image\n                      width={60}\n                      height={60}\n                      src={record.images[0]}\n                      style={{ marginRight: 12, borderRadius: 4 }}\n                      fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n                    />\n                  )}\n                  <div>\n                    <div style={{ fontWeight: 500 }}>{record.productName}</div>\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      编号: {record.productCode}\n                    </div>\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      {record.quantity} {record.unit}\n                    </div>\n                  </div>\n                </div>\n              ),\n            },\n            {\n              title: '拍卖状态',\n              dataIndex: 'status',\n              key: 'status',\n              width: 100,\n              render: (status: AuctionItemStatus) => {\n                console.log('渲染状态:', status, typeof status);\n                // 确保状态是数字类型\n                const numStatus = typeof status === 'number' ? status : \n                                 status === 'PENDING' ? AuctionItemStatus.PENDING :\n                                 status === 'ONGOING' ? AuctionItemStatus.ONGOING :\n                                 status === 'SOLD' ? AuctionItemStatus.SOLD :\n                                 status === 'UNSOLD' ? AuctionItemStatus.UNSOLD :\n                                 status === 'WITHDRAWN' ? AuctionItemStatus.WITHDRAWN : \n                                 AuctionItemStatus.PENDING;\n                \n                const statusInfo = itemStatusMap[numStatus];\n                console.log('状态信息:', statusInfo);\n                \n                return (\n                  <Badge\n                    status={\n                      numStatus === AuctionItemStatus.ONGOING ? 'processing' :\n                      numStatus === AuctionItemStatus.SOLD ? 'success' :\n                      numStatus === AuctionItemStatus.UNSOLD ? 'warning' :\n                      numStatus === AuctionItemStatus.WITHDRAWN ? 'error' : 'default'\n                    }\n                    text={\n                      <Tag color={statusInfo?.color || 'default'}>\n                        {statusInfo?.label || `未知(${numStatus})`}\n                      </Tag>\n                    }\n                  />\n                );\n              },\n            },\n            {\n              title: '价格信息',\n              key: 'price',\n              width: 150,\n              render: (_, record: AuctionItem) => (\n                <div>\n                  <div>起拍: ¥{record.startingPrice.toFixed(2)}</div>\n                  <div style={{ color: '#f50', fontWeight: 500 }}>\n                    当前: ¥{record.currentPrice.toFixed(2)}\n                  </div>\n                  {record.reservePrice && (\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      保留: ¥{record.reservePrice.toFixed(2)}\n                    </div>\n                  )}\n                </div>\n              ),\n            },\n            {\n              title: '竞价信息',\n              key: 'bid',\n              width: 120,\n              render: (_, record: AuctionItem) => (\n                <div>\n                  <div>出价次数: {record.bidCount}</div>\n                  <div>加价幅度: ¥{record.bidIncrement.toFixed(2)}</div>\n                  {record.highestBidder && (\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      最高出价人: {record.highestBidder}\n                    </div>\n                  )}\n                </div>\n              ),\n            },\n            {\n              title: '创建时间',\n              dataIndex: 'createdAt',\n              key: 'createdAt',\n              width: 160,\n              render: (text: string) => new Date(text).toLocaleString(),\n            },\n            {\n              title: '操作',\n              key: 'action',\n              width: 200,\n              fixed: 'right',\n              render: (_, record: AuctionItem) => (\n                <Space size=\"small\">\n                  <Tooltip title=\"查看详情\">\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      icon={<EyeOutlined />}\n                      onClick={() => {/* 查看详情 */}}\n                    />\n                  </Tooltip>\n                  <Tooltip title=\"编辑\">\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      icon={<EditOutlined />}\n                      onClick={() => handleEdit(record)}\n                      disabled={record.status === AuctionItemStatus.ONGOING}\n                    />\n                  </Tooltip>\n                  <Popconfirm\n                    title=\"确定要删除这个拍卖商品吗？\"\n                    onConfirm={() => handleDelete(record.id)}\n                    okText=\"确定\"\n                    cancelText=\"取消\"\n                  >\n                    <Tooltip title=\"删除\">\n                      <Button\n                        type=\"link\"\n                        size=\"small\"\n                        danger\n                        icon={<DeleteOutlined />}\n                        disabled={record.status === AuctionItemStatus.ONGOING}\n                      />\n                    </Tooltip>\n                  </Popconfirm>\n                </Space>\n              ),\n            },\n          ]}\n          dataSource={auctionItems}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 拍卖商品编辑模态框 */}\n      <Modal\n        title={editingItem ? '编辑拍卖商品' : '添加拍卖商品'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Form.Item\n            name=\"auctionId\"\n            label=\"拍卖会\"\n            rules={[{ required: true, message: '请选择拍卖会' }]}\n          >\n            <Select placeholder=\"请选择拍卖会\">\n              {auctions.map(auction => (\n                <Option key={auction.id} value={auction.id}>\n                  {auction.title}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"productId\"\n            label=\"商品\"\n            rules={[{ required: true, message: '请选择商品' }]}\n          >\n            <Select placeholder=\"请选择商品\" showSearch optionFilterProp=\"children\">\n              {products.map(product => (\n                <Option key={product.id} value={product.id}>\n                  {product.name} - {product.code}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"startingPrice\"\n                label=\"起拍价\"\n                rules={[\n                  { required: true, message: '请输入起拍价' },\n                  { type: 'number', min: 0.01, message: '起拍价必须大于0' },\n                ]}\n              >\n                <InputNumber\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入起拍价\"\n                  precision={2}\n                  min={0.01}\n                  addonBefore=\"¥\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"reservePrice\"\n                label=\"保留价（可选）\"\n                rules={[\n                  { type: 'number', min: 0.01, message: '保留价必须大于0' },\n                ]}\n              >\n                <InputNumber\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入保留价\"\n                  precision={2}\n                  min={0.01}\n                  addonBefore=\"¥\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"bidIncrement\"\n            label=\"加价幅度\"\n            rules={[\n              { required: true, message: '请输入加价幅度' },\n              { type: 'number', min: 0.01, message: '加价幅度必须大于0' },\n            ]}\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              placeholder=\"请输入加价幅度\"\n              precision={2}\n              min={0.01}\n              addonBefore=\"¥\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"startTime\"\n            label=\"开始时间\"\n            rules={[\n              { required: true, message: '请选择开始时间' },\n            ]}\n          >\n            <DatePicker\n              style={{ width: '100%' }}\n              showTime\n              placeholder=\"请选择开始时间\"\n              format=\"YYYY-MM-DD HH:mm:ss\"\n            />\n          </Form.Item>\n\n          {/* 错误和成功消息显示 */}\n          <FormMessage type=\"error\" message={formError} visible={!!formError} />\n          <FormMessage type=\"success\" message={formSuccess} visible={!!formSuccess} />\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button\n                onClick={() => {\n                  setIsModalVisible(false);\n                  form.resetFields();\n                  setEditingItem(null);\n                  clearAllMessages();\n                }}\n                disabled={saving}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={saving}\n                disabled={saving}\n              >\n                {saving ? '保存中...' : (editingItem ? '更新' : '添加')}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AuctionItems;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,OAAO,EACPC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,cAAc,EACdC,aAAa,QACR,mBAAmB;AAC1B;AACA,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElG,MAAM;EAAEC;AAAM,CAAC,GAAGzB,UAAU;AAC5B,MAAM;EAAE0B;AAAO,CAAC,GAAG/B,MAAM;;AAEzB;AACA,WAAYgC,iBAAiB,0BAAjBA,iBAAiB;EAAjBA,iBAAiB,CAAjBA,iBAAiB;EACV;EADPA,iBAAiB,CAAjBA,iBAAiB;EAEV;EAFPA,iBAAiB,CAAjBA,iBAAiB;EAGV;EAHPA,iBAAiB,CAAjBA,iBAAiB;EAIV;EAJPA,iBAAiB,CAAjBA,iBAAiB,kCAKV;EAAA,OALPA,iBAAiB;AAAA;;AAQ7B;;AAuBA;;AAUA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAyB;IACrEsD,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM,CAAC4D,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC;IAC3CgE,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,CAAC,GAAG3D,IAAI,CAAC4D,OAAO,CAAC,CAAC;EAE7B,MAAM;IACJC,SAAS;IACTC,WAAW;IACXC,YAAY;IACZC,cAAc;IACdC;EACF,CAAC,GAAG3C,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM4C,aAAa,GAAG;IACpB,CAACrC,iBAAiB,CAACsC,OAAO,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC;IAC/D,CAACxC,iBAAiB,CAACyC,OAAO,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC5D,CAACxC,iBAAiB,CAAC0C,IAAI,GAAG;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAQ,CAAC;IAC1D,CAACxC,iBAAiB,CAAC2C,MAAM,GAAG;MAAEJ,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC5D,CAACxC,iBAAiB,CAAC4C,SAAS,GAAG;MAAEL,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAM;EAC7D,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCnC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMoC,SAAS,GAAG;QAChB,GAAGjC,WAAW;QACd;QACAkC,MAAM,EAAE,OAAOlC,WAAW,CAACkC,MAAM,KAAK,QAAQ,GAAGlC,WAAW,CAACkC,MAAM,GAAGC;MACxE,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEJ,SAAS,CAAC;MAC/BG,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC;MAEvC,MAAMC,QAAQ,GAAG,MAAM7D,cAAc,CAAC8D,kBAAkB,CAACN,SAAS,CAAC;MACnEG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEG,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAAC;MAEnD,IAAIA,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,EAAE;QACrC;QACAP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEO,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,IAAI,CAAC,CAAC;;QAElD;QACA,MAAMG,SAAS,GAAGR,QAAQ,CAACK,IAAI,CAACI,IAAI,IAAI,EAAE;QAC1C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,EAAE;UAC7BV,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEJ,SAAS,CAAC;UACrCvD,eAAe,CAAC,EAAE,CAAC;UACnBQ,QAAQ,CAAC,CAAC,CAAC;UACX;QACF;QAEAqC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAES,SAAS,CAAC;;QAEnC;QACA,MAAMK,WAAW,GAAGL,SAAS,CAACM,GAAG,CAAEC,IAAS,IAAK;UAC/CjB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEgB,IAAI,CAAC;;UAE1B;UACA,MAAMC,OAAO,GAAGD,IAAI,CAACC,OAAO,IAAI,CAAC,CAAC;UAClClB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEiB,OAAO,CAAC;;UAE7B;UACA,MAAMC,UAAuB,GAAG;YAC9BC,EAAE,EAAEH,IAAI,CAACG,EAAE,IAAI,CAAC;YAChBC,SAAS,EAAEJ,IAAI,CAACI,SAAS,IAAI,CAAC;YAC9BC,SAAS,EAAEL,IAAI,CAACK,SAAS,IAAI,CAAC;YAC9BC,WAAW,EAAEL,OAAO,CAACM,IAAI,IAAIP,IAAI,CAACM,WAAW,IAAI,MAAM;YACvDE,WAAW,EAAEP,OAAO,CAACQ,IAAI,IAAIT,IAAI,CAACQ,WAAW,IAAI,KAAKR,IAAI,CAACK,SAAS,IAAI,CAAC,EAAE;YAC3EK,QAAQ,EAAET,OAAO,CAACS,QAAQ,IAAIV,IAAI,CAACU,QAAQ,IAAI,CAAC;YAChDC,IAAI,EAAEV,OAAO,CAACU,IAAI,IAAIX,IAAI,CAACW,IAAI,IAAI,GAAG;YACtCC,MAAM,EAAEX,OAAO,CAACW,MAAM,IAAIZ,IAAI,CAACY,MAAM,IAAI,EAAE;YAC3CC,aAAa,EAAEb,IAAI,CAACc,UAAU,IAAId,IAAI,CAACa,aAAa,IAAI,CAAC;YACzDE,YAAY,EAAEf,IAAI,CAACe,YAAY,IAAIf,IAAI,CAACc,UAAU,IAAId,IAAI,CAACa,aAAa,IAAI,CAAC;YAC7EG,YAAY,EAAEhB,IAAI,CAACgB,YAAY,IAAI,CAAC;YACpCC,YAAY,EAAEjB,IAAI,CAACkB,SAAS,IAAIlB,IAAI,CAACiB,YAAY,IAAI,EAAE;YACvDE,QAAQ,EAAEnB,IAAI,CAACoB,SAAS,IAAIpB,IAAI,CAACmB,QAAQ,IAAI,CAAC;YAC9CE,aAAa,EAAErB,IAAI,CAACsB,UAAU,IAAItB,IAAI,CAACqB,aAAa,IAAI,EAAE;YAC1DxC,MAAM,EAAE,OAAOmB,IAAI,CAACnB,MAAM,KAAK,QAAQ,GAAGmB,IAAI,CAACnB,MAAM,GAC9CmB,IAAI,CAACnB,MAAM,KAAK,SAAS,GAAG/C,iBAAiB,CAACsC,OAAO,GACrD4B,IAAI,CAACnB,MAAM,KAAK,SAAS,GAAG/C,iBAAiB,CAACyC,OAAO,GACrDyB,IAAI,CAACnB,MAAM,KAAK,MAAM,GAAG/C,iBAAiB,CAAC0C,IAAI,GAC/CwB,IAAI,CAACnB,MAAM,KAAK,QAAQ,GAAG/C,iBAAiB,CAAC2C,MAAM,GACnDuB,IAAI,CAACnB,MAAM,KAAK,WAAW,GAAG/C,iBAAiB,CAAC4C,SAAS,GACzD5C,iBAAiB,CAACsC,OAAO;YAChCmD,SAAS,EAAEvB,IAAI,CAACuB,SAAS,IAAI,EAAE;YAC/BC,SAAS,EAAExB,IAAI,CAACwB,SAAS,IAAI,EAAE;YAC/BC,QAAQ,EAAExB,OAAO,CAACwB,QAAQ,IAAIzB,IAAI,CAACyB,QAAQ,IAAI,EAAE;YACjDC,OAAO,EAAEzB,OAAO,CAAC0B,YAAY,IAAI3B,IAAI,CAAC0B,OAAO,IAAI;UACnD,CAAC;UAED3C,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkB,UAAU,CAAC;UAClC,OAAOA,UAAU;QACnB,CAAC,CAAC;QAEFnB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEc,WAAW,CAAC;QACrC5D,eAAe,CAAC4D,WAAW,CAAC;QAC5BpD,QAAQ,CAACuC,QAAQ,CAACK,IAAI,CAAC7C,KAAK,IAAI,CAAC,CAAC;;QAElC;QACA,MAAMe,YAAY,GAAGsC,WAAW,CAAC8B,MAAM,CAAC5B,IAAI,IAAIA,IAAI,CAACnB,MAAM,KAAK/C,iBAAiB,CAACsC,OAAO,CAAC,CAACyD,MAAM;QACjG,MAAMpE,YAAY,GAAGqC,WAAW,CAAC8B,MAAM,CAAC5B,IAAI,IAAIA,IAAI,CAACnB,MAAM,KAAK/C,iBAAiB,CAACyC,OAAO,CAAC,CAACsD,MAAM;QACjG,MAAMnE,SAAS,GAAGoC,WAAW,CAAC8B,MAAM,CAAC5B,IAAI,IAAIA,IAAI,CAACnB,MAAM,KAAK/C,iBAAiB,CAAC0C,IAAI,CAAC,CAACqD,MAAM;QAC3F,MAAMlE,UAAU,GAAGmC,WAAW,CAACgC,MAAM,CAAC,CAACC,GAAG,EAAE/B,IAAI,KAAK+B,GAAG,GAAG/B,IAAI,CAACe,YAAY,EAAE,CAAC,CAAC;QAEhFzD,aAAa,CAAC;UACZC,UAAU,EAAEuC,WAAW,CAAC+B,MAAM;UAC9BrE,YAAY;UACZC,YAAY;UACZC,SAAS;UACTC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLoB,OAAO,CAACiD,IAAI,CAAC,aAAa,EAAE/C,QAAQ,CAAC;QACrC/C,eAAe,CAAC,EAAE,CAAC;QACnBQ,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOmD,KAAU,EAAE;MACnBd,OAAO,CAACc,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC3F,OAAO,CAAC2F,KAAK,CAACA,KAAK,CAAC3F,OAAO,IAAI,YAAY,CAAC;MAC5CgC,eAAe,CAAC,EAAE,CAAC;MACnBQ,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMhD,QAAQ,GAAG,MAAM5D,cAAc,CAAC6G,cAAc,CAAC;QACnDrF,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE,GAAG;QACbqF,WAAW,EAAE,UAAU,CAAE;MAC3B,CAAC,CAAC;MAEF,IAAIlD,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,IAAIL,QAAQ,CAACK,IAAI,CAACI,IAAI,EAAE;QAC3DtD,WAAW,CAAC6C,QAAQ,CAACK,IAAI,CAACI,IAAI,CAAC;QAC/BX,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,QAAQ,CAACK,IAAI,CAACI,IAAI,CAAC;MAC7C,CAAC,MAAM;QACLX,OAAO,CAACiD,IAAI,CAAC,WAAW,EAAE/C,QAAQ,CAAC;QACnC7C,WAAW,CAAC,EAAE,CAAC;MACjB;IACF,CAAC,CAAC,OAAOyD,KAAU,EAAE;MACnBd,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCzD,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;;EAED;EACA,MAAMgG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMnD,QAAQ,GAAG,MAAM7D,cAAc,CAACiH,cAAc,CAAC;QACnDxF,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAImC,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACK,IAAI,IAAIL,QAAQ,CAACK,IAAI,CAACI,IAAI,EAAE;QAC3D;QACA,MAAM4C,cAAc,GAAGrD,QAAQ,CAACK,IAAI,CAACI,IAAI,CAACK,GAAG,CAAEwC,OAAY,KAAM;UAC/D,GAAGA,OAAO;UACVC,KAAK,EAAED,OAAO,CAAChC,IAAI,IAAIgC,OAAO,CAACC,KAAK,CAAE;QACxC,CAAC,CAAC,CAAC;QACHlG,WAAW,CAACgG,cAAc,CAAC;QAC3BvD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsD,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLvD,OAAO,CAACiD,IAAI,CAAC,YAAY,EAAE/C,QAAQ,CAAC;QACpC3C,WAAW,CAAC,EAAE,CAAC;MACjB;IACF,CAAC,CAAC,OAAOuD,KAAU,EAAE;MACnBd,OAAO,CAACc,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCvD,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;;EAED;EACA,MAAMmG,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA;MACAnF,aAAa,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOkC,KAAU,EAAE;MACnBd,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAM6C,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFxI,OAAO,CAACqC,OAAO,CAAC,cAAc,CAAC;;MAE/B;MACA,MAAMoG,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;;MAElD;MACA,MAAM7D,QAAQ,GAAG,MAAM8D,KAAK,CAAC,GAAGJ,OAAO,kCAAkC,CAAC;MAC1E,MAAMrD,IAAI,GAAG,MAAML,QAAQ,CAAC+D,IAAI,CAAC,CAAC;MAElCjE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEM,IAAI,CAAC;MAE/B,IAAIL,QAAQ,CAACgE,EAAE,EAAE;QACf/I,OAAO,CAACmF,OAAO,CAAC,iBAAiBJ,QAAQ,CAACJ,MAAM,EAAE,CAAC;MACrD,CAAC,MAAM;QACL3E,OAAO,CAAC2F,KAAK,CAAC,iBAAiBZ,QAAQ,CAACJ,MAAM,EAAE,CAAC;MACnD;;MAEA;MACA,IAAIS,IAAI,EAAE;QACRP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEO,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAAC;QACzCpF,OAAO,CAACgJ,IAAI,CAAC,WAAW/D,IAAI,CAACC,SAAS,CAACG,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAAC,EAAE,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOO,KAAU,EAAE;MACnBd,OAAO,CAACc,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC3F,OAAO,CAAC2F,KAAK,CAAC,cAAcA,KAAK,CAAC3F,OAAO,EAAE,CAAC;IAC9C;EACF,CAAC;;EAED;EACAV,SAAS,CAAC,MAAM;IACd;IACA,MAAM2J,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B3G,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACFuC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB;QACA,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,EAAEC,gBAAgB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5EpI,cAAc,CAAC8D,kBAAkB,CAAC;UAChC,GAAGvC,WAAW;UACd;UACAkC,MAAM,EAAElC,WAAW,CAACkC,MAAM;UAC1B/B,QAAQ,EAAE,EAAE,CAAE;QAChB,CAAC,CAAC,EACF1B,cAAc,CAACiH,cAAc,CAAC;UAC5BxF,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE,EAAE,CAAE;QAChB,CAAC,CAAC,EACFzB,cAAc,CAAC6G,cAAc,CAAC;UAC5BrF,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE,EAAE;UAAE;UACdqF,WAAW,EAAE;QACf,CAAC,CAAC,CACH,CAAC;QAEFpD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxBD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEG,IAAI,CAACC,SAAS,CAACgE,aAAa,CAAC,CAAC;QACrDrE,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEG,IAAI,CAACC,SAAS,CAACiE,gBAAgB,CAAC,CAAC;QACvDtE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEG,IAAI,CAACC,SAAS,CAACkE,gBAAgB,CAAC,CAAC;;QAEtD;QACA,IAAID,gBAAgB,CAAChE,OAAO,IAAIgE,gBAAgB,CAAC/D,IAAI,IAAI+D,gBAAgB,CAAC/D,IAAI,CAACI,IAAI,EAAE;UACnF,MAAM4C,cAAc,GAAGe,gBAAgB,CAAC/D,IAAI,CAACI,IAAI,CAACK,GAAG,CAAEwC,OAAY,KAAM;YACvE,GAAGA,OAAO;YACVC,KAAK,EAAED,OAAO,CAAChC,IAAI,IAAIgC,OAAO,CAACC;UACjC,CAAC,CAAC,CAAC;UACHlG,WAAW,CAACgG,cAAc,CAAC;UAC3BvD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEsD,cAAc,CAAC;QAC3C;;QAEA;QACA,IAAIgB,gBAAgB,CAACjE,OAAO,IAAIiE,gBAAgB,CAAChE,IAAI,IAAIgE,gBAAgB,CAAChE,IAAI,CAACI,IAAI,EAAE;UACnFtD,WAAW,CAACkH,gBAAgB,CAAChE,IAAI,CAACI,IAAI,CAAC;UACvCX,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsE,gBAAgB,CAAChE,IAAI,CAACI,IAAI,CAAC;QACtD;;QAEA;QACA,IAAI0D,aAAa,CAAC/D,OAAO,IAAI+D,aAAa,CAAC9D,IAAI,EAAE;UAC/CP,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEO,MAAM,CAACC,IAAI,CAAC4D,aAAa,CAAC9D,IAAI,CAAC,CAAC;;UAEzD;UACA,MAAMG,SAAS,GAAG2D,aAAa,CAAC9D,IAAI,CAACI,IAAI,IAAI,EAAE;UAC/C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,EAAE;YAC7BV,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEJ,SAAS,CAAC;YACrCvD,eAAe,CAAC,EAAE,CAAC;YACnBQ,QAAQ,CAAC,CAAC,CAAC;YACX;UACF;UAEAqC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAES,SAAS,CAAC;UAEnC,MAAMK,WAAW,GAAGL,SAAS,CAACM,GAAG,CAAEC,IAAS,IAAK;YAC/C;YACA,MAAMC,OAAO,GAAGD,IAAI,CAACC,OAAO,IAAI,CAAC,CAAC;YAClClB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEgB,IAAI,CAACG,EAAE,EAAE,OAAO,EAAEF,OAAO,CAAC;;YAEhD;YACA,MAAMpB,MAAM,GAAG,OAAOmB,IAAI,CAACnB,MAAM,KAAK,QAAQ,GAAGmB,IAAI,CAACnB,MAAM,GAC9CmB,IAAI,CAACnB,MAAM,KAAK,SAAS,GAAG/C,iBAAiB,CAACsC,OAAO,GACrD4B,IAAI,CAACnB,MAAM,KAAK,SAAS,GAAG/C,iBAAiB,CAACyC,OAAO,GACrDyB,IAAI,CAACnB,MAAM,KAAK,MAAM,GAAG/C,iBAAiB,CAAC0C,IAAI,GAC/CwB,IAAI,CAACnB,MAAM,KAAK,QAAQ,GAAG/C,iBAAiB,CAAC2C,MAAM,GACnDuB,IAAI,CAACnB,MAAM,KAAK,WAAW,GAAG/C,iBAAiB,CAAC4C,SAAS,GACzD5C,iBAAiB,CAACsC,OAAO;YAEvC,OAAO;cACL+B,EAAE,EAAEH,IAAI,CAACG,EAAE;cACXC,SAAS,EAAEJ,IAAI,CAACI,SAAS;cACzBC,SAAS,EAAEL,IAAI,CAACK,SAAS;cACzBC,WAAW,EAAEL,OAAO,CAACM,IAAI,IAAIP,IAAI,CAACM,WAAW,IAAI,MAAM;cACvDE,WAAW,EAAEP,OAAO,CAACQ,IAAI,IAAIT,IAAI,CAACQ,WAAW,IAAI,KAAKR,IAAI,CAACK,SAAS,EAAE;cACtEK,QAAQ,EAAET,OAAO,CAACS,QAAQ,IAAIV,IAAI,CAACU,QAAQ,IAAI,CAAC;cAChDC,IAAI,EAAEV,OAAO,CAACU,IAAI,IAAIX,IAAI,CAACW,IAAI,IAAI,GAAG;cACtCC,MAAM,EAAEX,OAAO,CAACW,MAAM,IAAIZ,IAAI,CAACY,MAAM,IAAI,EAAE;cAC3C;cACAC,aAAa,EAAEb,IAAI,CAACc,UAAU,IAAId,IAAI,CAACa,aAAa,IAAI,CAAC;cACzDE,YAAY,EAAEf,IAAI,CAACe,YAAY,IAAIf,IAAI,CAACc,UAAU,IAAId,IAAI,CAACa,aAAa,IAAI,CAAC;cAC7EG,YAAY,EAAEhB,IAAI,CAACgB,YAAY,IAAI,CAAC;cACpCC,YAAY,EAAEjB,IAAI,CAACkB,SAAS,IAAIlB,IAAI,CAACiB,YAAY,IAAI,EAAE;cACvDE,QAAQ,EAAEnB,IAAI,CAACoB,SAAS,IAAIpB,IAAI,CAACmB,QAAQ,IAAI,CAAC;cAC9CE,aAAa,EAAErB,IAAI,CAACsB,UAAU,IAAItB,IAAI,CAACqB,aAAa,IAAI,EAAE;cAC1DxC,MAAM,EAAEA,MAAM;cACd0C,SAAS,EAAEvB,IAAI,CAACuB,SAAS;cACzBC,SAAS,EAAExB,IAAI,CAACwB,SAAS;cACzBC,QAAQ,EAAExB,OAAO,CAACwB,QAAQ,IAAIzB,IAAI,CAACyB,QAAQ,IAAI,EAAE;cACjDC,OAAO,EAAEzB,OAAO,CAAC0B,YAAY,IAAI3B,IAAI,CAAC0B,OAAO,IAAI;YACnD,CAAC;UACH,CAAC,CAAC;UAEF3C,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEc,WAAW,CAAC;UACvC5D,eAAe,CAAC4D,WAAW,CAAC;UAC5BpD,QAAQ,CAAC0G,aAAa,CAAC9D,IAAI,CAAC7C,KAAK,IAAI,CAAC,CAAC;;UAEvC;UACA,MAAMe,YAAY,GAAGsC,WAAW,CAAC8B,MAAM,CAAC5B,IAAI,IAAIA,IAAI,CAACnB,MAAM,KAAK/C,iBAAiB,CAACsC,OAAO,CAAC,CAACyD,MAAM;UACjG,MAAMpE,YAAY,GAAGqC,WAAW,CAAC8B,MAAM,CAAC5B,IAAI,IAAIA,IAAI,CAACnB,MAAM,KAAK/C,iBAAiB,CAACyC,OAAO,CAAC,CAACsD,MAAM;UACjG,MAAMnE,SAAS,GAAGoC,WAAW,CAAC8B,MAAM,CAAC5B,IAAI,IAAIA,IAAI,CAACnB,MAAM,KAAK/C,iBAAiB,CAAC0C,IAAI,CAAC,CAACqD,MAAM;UAC3F,MAAMlE,UAAU,GAAGmC,WAAW,CAACgC,MAAM,CAAC,CAACC,GAAG,EAAE/B,IAAI,KAAK+B,GAAG,GAAG/B,IAAI,CAACe,YAAY,EAAE,CAAC,CAAC;UAEhFzD,aAAa,CAAC;YACZC,UAAU,EAAEuC,WAAW,CAAC+B,MAAM;YAC9BrE,YAAY;YACZC,YAAY;YACZC,SAAS;YACTC;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLoB,OAAO,CAACiD,IAAI,CAAC,aAAa,EAAEoB,aAAa,CAAC;UAC1ClH,eAAe,CAAC,EAAE,CAAC;UACnBQ,QAAQ,CAAC,CAAC,CAAC;QACb;MACF,CAAC,CAAC,OAAOmD,KAAK,EAAE;QACdd,OAAO,CAACc,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAChC3F,OAAO,CAAC2F,KAAK,CAAC,gBAAgB,CAAC;MACjC,CAAC,SAAS;QACRrD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,IACEG,WAAW,CAACE,IAAI,KAAK,CAAC,IACtB,CAACF,WAAW,CAACyD,SAAS,IACtB,CAACzD,WAAW,CAAC2D,WAAW,IACxB,CAAC3D,WAAW,CAACkC,MAAM,EACnB;MACAsE,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACAxE,iBAAiB,CAAC,CAAC;IACrB;IACF;EACA,CAAC,EAAE,CAAChC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM8G,YAAY,GAAIC,MAAW,IAAK;IACpC9G,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAG+G,MAAM;MACT7G,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8G,WAAW,GAAGA,CAAA,KAAM;IACxB/G,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8G,SAAS,GAAGA,CAAA,KAAM;IACtB1G,cAAc,CAAC,IAAI,CAAC;IACpBU,IAAI,CAACiG,WAAW,CAAC,CAAC;IAClB3F,gBAAgB,CAAC,CAAC;IAClBlB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM8G,UAAU,GAAI9D,IAAiB,IAAK;IACxC9C,cAAc,CAAC8C,IAAI,CAAC;IACpBpC,IAAI,CAACmG,cAAc,CAAC/D,IAAI,CAAC;IACzB9B,gBAAgB,CAAC,CAAC;IAClBlB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMgH,YAAY,GAAG,MAAO7D,EAAU,IAAK;IACzC,IAAI;MACF;MACAjG,OAAO,CAACmF,OAAO,CAAC,MAAM,CAAC;MACvBV,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOkB,KAAU,EAAE;MACnB3F,OAAO,CAAC2F,KAAK,CAACA,KAAK,CAAC3F,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAM+J,UAAU,GAAG,MAAOP,MAAW,IAAK;IACxCtG,SAAS,CAAC,IAAI,CAAC;IACfc,gBAAgB,CAAC,CAAC;IAElB,IAAI;MACF,IAAIe,QAAQ;MACZ,IAAIhC,WAAW,EAAE;QACf;QACAe,YAAY,CAAC,qBAAqB,CAAC;QACnCZ,SAAS,CAAC,KAAK,CAAC;QAChB;MACF,CAAC,MAAM;QACL;QACA6B,QAAQ,GAAG,MAAM7D,cAAc,CAAC8I,cAAc,CAACR,MAAM,CAACtD,SAAS,EAAE;UAC/DC,SAAS,EAAEqD,MAAM,CAACrD,SAAS;UAC3BQ,aAAa,EAAE6C,MAAM,CAAC7C,aAAa;UACnCG,YAAY,EAAE0C,MAAM,CAAC1C,YAAY;UACjCC,YAAY,EAAEyC,MAAM,CAACzC,YAAY;UACjCkD,SAAS,EAAET,MAAM,CAACS,SAAS,GAAGT,MAAM,CAACS,SAAS,CAACC,WAAW,CAAC,CAAC,GAAGtF;QACjE,CAAC,CAAC;MACJ;MAEA,MAAMuF,UAAU,GAAG,WAAW;MAE9B,IAAI7I,iBAAiB,CAACyD,QAAQ,EAAEjB,YAAY,EAAEC,cAAc,EAAEoG,UAAU,CAAC,EAAE;QACzE;QACAC,UAAU,CAAC,MAAM;UACftH,iBAAiB,CAAC,KAAK,CAAC;UACxBY,IAAI,CAACiG,WAAW,CAAC,CAAC;UAClB3G,cAAc,CAAC,IAAI,CAAC;UACpBgB,gBAAgB,CAAC,CAAC;UAClBS,iBAAiB,CAAC,CAAC;UACnB8D,eAAe,CAAC,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAO5C,KAAU,EAAE;MACnBpE,cAAc,CAACoE,KAAK,EAAE7B,YAAY,CAAC;IACrC,CAAC,SAAS;MACRZ,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,oBACEzB,OAAA;IAAK4I,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAC,QAAA,gBAC1B9I,OAAA,CAACC,KAAK;MAAC8I,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG/BnJ,OAAA,CAACvB,GAAG;MAAC2K,MAAM,EAAE,EAAG;MAACR,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,gBAC3C9I,OAAA,CAACtB,GAAG;QAAC4K,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACzB9I,OAAA,CAAClC,IAAI;UAAAgL,QAAA,eACH9I,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,0BAAM;YACZ4C,KAAK,EAAE/H,UAAU,CAACE,UAAW;YAC7B8H,MAAM,eAAE1J,OAAA,CAACR,aAAa;cAAAwJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BQ,UAAU,EAAE;cAAEhH,KAAK,EAAE;YAAU;UAAE;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnJ,OAAA,CAACtB,GAAG;QAAC4K,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACzB9I,OAAA,CAAClC,IAAI;UAAAgL,QAAA,eACH9I,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,oBAAK;YACX4C,KAAK,EAAE/H,UAAU,CAACG,YAAa;YAC/B8H,UAAU,EAAE;cAAEhH,KAAK,EAAE;YAAU;UAAE;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnJ,OAAA,CAACtB,GAAG;QAAC4K,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACzB9I,OAAA,CAAClC,IAAI;UAAAgL,QAAA,eACH9I,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,oBAAK;YACX4C,KAAK,EAAE/H,UAAU,CAACI,YAAa;YAC/B6H,UAAU,EAAE;cAAEhH,KAAK,EAAE;YAAU;UAAE;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnJ,OAAA,CAACtB,GAAG;QAAC4K,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACzB9I,OAAA,CAAClC,IAAI;UAAAgL,QAAA,eACH9I,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,oBAAK;YACX4C,KAAK,EAAE/H,UAAU,CAACK,SAAU;YAC5B4H,UAAU,EAAE;cAAEhH,KAAK,EAAE;YAAU;UAAE;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnJ,OAAA,CAAClC,IAAI;MAAC8L,SAAS,EAAC,aAAa;MAACC,IAAI,EAAC,OAAO;MAACjB,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,eACrE9I,OAAA,CAAC1B,IAAI;QACHwL,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEjC,YAAa;QACvBkC,YAAY,EAAC,KAAK;QAAAlB,QAAA,eAElB9I,OAAA,CAACvB,GAAG;UAAC2K,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACR,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAAnB,QAAA,gBAC9C9I,OAAA,CAACtB,GAAG;YAAC4K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACzB9I,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;cAACtF,IAAI,EAAC,aAAa;cAAClC,KAAK,EAAC,0BAAM;cAAAoG,QAAA,eACxC9I,OAAA,CAAC9B,KAAK;gBAACiM,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnJ,OAAA,CAACtB,GAAG;YAAC4K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACzB9I,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;cAACtF,IAAI,EAAC,QAAQ;cAAClC,KAAK,EAAC,0BAAM;cAAAoG,QAAA,eACnC9I,OAAA,CAAC7B,MAAM;gBAACgM,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAtB,QAAA,gBACtC9I,OAAA,CAACE,MAAM;kBAACuJ,KAAK,EAAEtJ,iBAAiB,CAACsC,OAAQ;kBAAAqG,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDnJ,OAAA,CAACE,MAAM;kBAACuJ,KAAK,EAAEtJ,iBAAiB,CAACyC,OAAQ;kBAAAkG,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDnJ,OAAA,CAACE,MAAM;kBAACuJ,KAAK,EAAEtJ,iBAAiB,CAAC0C,IAAK;kBAAAiG,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnDnJ,OAAA,CAACE,MAAM;kBAACuJ,KAAK,EAAEtJ,iBAAiB,CAAC2C,MAAO;kBAAAgG,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDnJ,OAAA,CAACE,MAAM;kBAACuJ,KAAK,EAAEtJ,iBAAiB,CAAC4C,SAAU;kBAAA+F,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnJ,OAAA,CAACtB,GAAG;YAAC4K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACzB9I,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;cAACtF,IAAI,EAAC,WAAW;cAAClC,KAAK,EAAC,oBAAK;cAAAoG,QAAA,eACrC9I,OAAA,CAAC7B,MAAM;gBAACgM,WAAW,EAAC,sCAAQ;gBAACC,UAAU;gBAAAtB,QAAA,EACpCpI,QAAQ,CAAC0D,GAAG,CAACwC,OAAO,iBACnB5G,OAAA,CAACE,MAAM;kBAAkBuJ,KAAK,EAAE7C,OAAO,CAACpC,EAAG;kBAAAsE,QAAA,EACxClC,OAAO,CAACC;gBAAK,GADHD,OAAO,CAACpC,EAAE;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnJ,OAAA,CAACtB,GAAG;YAAC4K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACzB9I,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;cAAApB,QAAA,eACR9I,OAAA,CAAC/B,KAAK;gBAAA6K,QAAA,gBACJ9I,OAAA,CAAChC,MAAM;kBAACqM,IAAI,EAAC,SAAS;kBAACC,QAAQ,EAAC,QAAQ;kBAACC,IAAI,eAAEvK,OAAA,CAACb,cAAc;oBAAA6J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAL,QAAA,EAAC;gBAEnE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnJ,OAAA,CAAChC,MAAM;kBAACwM,OAAO,EAAExC,WAAY;kBAACuC,IAAI,eAAEvK,OAAA,CAACT,cAAc;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAL,QAAA,EAAC;gBAExD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPnJ,OAAA,CAAClC,IAAI;MAAC8L,SAAS,EAAC,aAAa;MAACC,IAAI,EAAC,OAAO;MAACjB,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,eACrE9I,OAAA,CAACvB,GAAG;QAACgM,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAA5B,QAAA,gBACzC9I,OAAA,CAACtB,GAAG;UAAAoK,QAAA,eACF9I,OAAA,CAAChC,MAAM;YACLqM,IAAI,EAAC,SAAS;YACdE,IAAI,eAAEvK,OAAA,CAACd,YAAY;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBqB,OAAO,EAAEvC,SAAU;YAAAa,QAAA,EACpB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNnJ,OAAA,CAACtB,GAAG;UAAAoK,QAAA,eACF9I,OAAA,CAAChC,MAAM;YACLuM,IAAI,eAAEvK,OAAA,CAACT,cAAc;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBqB,OAAO,EAAExH,iBAAkB;YAC3BpC,OAAO,EAAEA,OAAQ;YAAAkI,QAAA,EAClB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnJ,OAAA,CAAClC,IAAI;MAAAgL,QAAA,gBAEH9I,OAAA;QAAK4I,KAAK,EAAE;UAAES,YAAY,EAAE;QAAG,CAAE;QAAAP,QAAA,gBAC/B9I,OAAA;UAAA8I,QAAA,GAAG,wCAAQ,EAAClI,OAAO,GAAG,QAAQ,GAAG,KAAKN,YAAY,CAAC4F,MAAM,MAAM;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACnE7I,YAAY,CAAC4F,MAAM,KAAK,CAAC,IAAI,CAACtF,OAAO,iBACpCZ,OAAA;UAAG4I,KAAK,EAAE;YAAEjG,KAAK,EAAE;UAAM,CAAE;UAAAmG,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACtD,eAEDnJ,OAAA,CAAChC,MAAM;UACLqM,IAAI,EAAC,SAAS;UACdG,OAAO,EAAEA,CAAA,KAAM;YACbpH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE/C,YAAY,CAAC;YACtC/B,OAAO,CAACgJ,IAAI,CAAC,WAAWjH,YAAY,CAAC4F,MAAM,EAAE,CAAC;UAChD,CAAE;UACF0C,KAAK,EAAE;YAAE+B,WAAW,EAAE;UAAE,CAAE;UAAA7B,QAAA,EAC3B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnJ,OAAA,CAAChC,MAAM;UACLqM,IAAI,EAAC,SAAS;UACdG,OAAO,EAAEzD,kBAAmB;UAC5B6B,KAAK,EAAE;YAAE+B,WAAW,EAAE;UAAE,CAAE;UAAA7B,QAAA,EAC3B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnJ,OAAA,CAAChC,MAAM;UACLwM,OAAO,EAAExH,iBAAkB;UAC3B4F,KAAK,EAAE;YAAE+B,WAAW,EAAE;UAAE,CAAE;UAAA7B,QAAA,EAC3B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENnJ,OAAA,CAACjC,KAAK;QACJ6M,OAAO,EAAE,CACP;UACE/D,KAAK,EAAE,IAAI;UACXgE,SAAS,EAAE,IAAI;UACfC,GAAG,EAAE,IAAI;UACTb,KAAK,EAAE;QACT,CAAC,EACD;UACEpD,KAAK,EAAE,MAAM;UACbiE,GAAG,EAAE,SAAS;UACdb,KAAK,EAAE,GAAG;UACVc,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAmB,kBAC7BjL,OAAA;YAAK4I,KAAK,EAAE;cAAEsC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAArC,QAAA,GACnDmC,MAAM,CAAChG,MAAM,IAAIgG,MAAM,CAAChG,MAAM,CAACiB,MAAM,GAAG,CAAC,iBACxClG,OAAA,CAACpB,KAAK;cACJqL,KAAK,EAAE,EAAG;cACVmB,MAAM,EAAE,EAAG;cACXC,GAAG,EAAEJ,MAAM,CAAChG,MAAM,CAAC,CAAC,CAAE;cACtB2D,KAAK,EAAE;gBAAE+B,WAAW,EAAE,EAAE;gBAAEW,YAAY,EAAE;cAAE,CAAE;cAC5CC,QAAQ,EAAC;YAAgoB;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1oB,CACF,eACDnJ,OAAA;cAAA8I,QAAA,gBACE9I,OAAA;gBAAK4I,KAAK,EAAE;kBAAE4C,UAAU,EAAE;gBAAI,CAAE;gBAAA1C,QAAA,EAAEmC,MAAM,CAACtG;cAAW;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DnJ,OAAA;gBAAK4I,KAAK,EAAE;kBAAE6C,QAAQ,EAAE,EAAE;kBAAE9I,KAAK,EAAE;gBAAO,CAAE;gBAAAmG,QAAA,GAAC,gBACvC,EAACmC,MAAM,CAACpG,WAAW;cAAA;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACNnJ,OAAA;gBAAK4I,KAAK,EAAE;kBAAE6C,QAAQ,EAAE,EAAE;kBAAE9I,KAAK,EAAE;gBAAO,CAAE;gBAAAmG,QAAA,GACzCmC,MAAM,CAAClG,QAAQ,EAAC,GAAC,EAACkG,MAAM,CAACjG,IAAI;cAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAET,CAAC,EACD;UACEtC,KAAK,EAAE,MAAM;UACbgE,SAAS,EAAE,QAAQ;UACnBC,GAAG,EAAE,QAAQ;UACbb,KAAK,EAAE,GAAG;UACVc,MAAM,EAAG7H,MAAyB,IAAK;YACrCE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEH,MAAM,EAAE,OAAOA,MAAM,CAAC;YAC3C;YACA,MAAMwI,SAAS,GAAG,OAAOxI,MAAM,KAAK,QAAQ,GAAGA,MAAM,GACpCA,MAAM,KAAK,SAAS,GAAG/C,iBAAiB,CAACsC,OAAO,GAChDS,MAAM,KAAK,SAAS,GAAG/C,iBAAiB,CAACyC,OAAO,GAChDM,MAAM,KAAK,MAAM,GAAG/C,iBAAiB,CAAC0C,IAAI,GAC1CK,MAAM,KAAK,QAAQ,GAAG/C,iBAAiB,CAAC2C,MAAM,GAC9CI,MAAM,KAAK,WAAW,GAAG/C,iBAAiB,CAAC4C,SAAS,GACpD5C,iBAAiB,CAACsC,OAAO;YAE1C,MAAMkJ,UAAU,GAAGnJ,aAAa,CAACkJ,SAAS,CAAC;YAC3CtI,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEsI,UAAU,CAAC;YAEhC,oBACE3L,OAAA,CAACjB,KAAK;cACJmE,MAAM,EACJwI,SAAS,KAAKvL,iBAAiB,CAACyC,OAAO,GAAG,YAAY,GACtD8I,SAAS,KAAKvL,iBAAiB,CAAC0C,IAAI,GAAG,SAAS,GAChD6I,SAAS,KAAKvL,iBAAiB,CAAC2C,MAAM,GAAG,SAAS,GAClD4I,SAAS,KAAKvL,iBAAiB,CAAC4C,SAAS,GAAG,OAAO,GAAG,SACvD;cACD6I,IAAI,eACF5L,OAAA,CAAC5B,GAAG;gBAACuE,KAAK,EAAE,CAAAgJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEhJ,KAAK,KAAI,SAAU;gBAAAmG,QAAA,EACxC,CAAA6C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEjJ,KAAK,KAAI,MAAMgJ,SAAS;cAAG;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAEN;QACF,CAAC,EACD;UACEtC,KAAK,EAAE,MAAM;UACbiE,GAAG,EAAE,OAAO;UACZb,KAAK,EAAE,GAAG;UACVc,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAmB,kBAC7BjL,OAAA;YAAA8I,QAAA,gBACE9I,OAAA;cAAA8I,QAAA,GAAK,oBAAK,EAACmC,MAAM,CAAC/F,aAAa,CAAC2G,OAAO,CAAC,CAAC,CAAC;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDnJ,OAAA;cAAK4I,KAAK,EAAE;gBAAEjG,KAAK,EAAE,MAAM;gBAAE6I,UAAU,EAAE;cAAI,CAAE;cAAA1C,QAAA,GAAC,oBACzC,EAACmC,MAAM,CAAC7F,YAAY,CAACyG,OAAO,CAAC,CAAC,CAAC;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,EACL8B,MAAM,CAAC5F,YAAY,iBAClBrF,OAAA;cAAK4I,KAAK,EAAE;gBAAE6C,QAAQ,EAAE,EAAE;gBAAE9I,KAAK,EAAE;cAAO,CAAE;cAAAmG,QAAA,GAAC,oBACtC,EAACmC,MAAM,CAAC5F,YAAY,CAACwG,OAAO,CAAC,CAAC,CAAC;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAET,CAAC,EACD;UACEtC,KAAK,EAAE,MAAM;UACbiE,GAAG,EAAE,KAAK;UACVb,KAAK,EAAE,GAAG;UACVc,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAmB,kBAC7BjL,OAAA;YAAA8I,QAAA,gBACE9I,OAAA;cAAA8I,QAAA,GAAK,4BAAM,EAACmC,MAAM,CAACzF,QAAQ;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCnJ,OAAA;cAAA8I,QAAA,GAAK,gCAAO,EAACmC,MAAM,CAAC3F,YAAY,CAACuG,OAAO,CAAC,CAAC,CAAC;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACjD8B,MAAM,CAACvF,aAAa,iBACnB1F,OAAA;cAAK4I,KAAK,EAAE;gBAAE6C,QAAQ,EAAE,EAAE;gBAAE9I,KAAK,EAAE;cAAO,CAAE;cAAAmG,QAAA,GAAC,kCACpC,EAACmC,MAAM,CAACvF,aAAa;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAET,CAAC,EACD;UACEtC,KAAK,EAAE,MAAM;UACbgE,SAAS,EAAE,WAAW;UACtBC,GAAG,EAAE,WAAW;UAChBb,KAAK,EAAE,GAAG;UACVc,MAAM,EAAGa,IAAY,IAAK,IAAIE,IAAI,CAACF,IAAI,CAAC,CAACG,cAAc,CAAC;QAC1D,CAAC,EACD;UACElF,KAAK,EAAE,IAAI;UACXiE,GAAG,EAAE,QAAQ;UACbb,KAAK,EAAE,GAAG;UACV+B,KAAK,EAAE,OAAO;UACdjB,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAmB,kBAC7BjL,OAAA,CAAC/B,KAAK;YAAC4L,IAAI,EAAC,OAAO;YAAAf,QAAA,gBACjB9I,OAAA,CAAChB,OAAO;cAAC6H,KAAK,EAAC,0BAAM;cAAAiC,QAAA,eACnB9I,OAAA,CAAChC,MAAM;gBACLqM,IAAI,EAAC,MAAM;gBACXR,IAAI,EAAC,OAAO;gBACZU,IAAI,eAAEvK,OAAA,CAACV,WAAW;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBqB,OAAO,EAAEA,CAAA,KAAM,CAAC;cAAY;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACVnJ,OAAA,CAAChB,OAAO;cAAC6H,KAAK,EAAC,cAAI;cAAAiC,QAAA,eACjB9I,OAAA,CAAChC,MAAM;gBACLqM,IAAI,EAAC,MAAM;gBACXR,IAAI,EAAC,OAAO;gBACZU,IAAI,eAAEvK,OAAA,CAACZ,YAAY;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBqB,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAAC8C,MAAM,CAAE;gBAClCgB,QAAQ,EAAEhB,MAAM,CAAC/H,MAAM,KAAK/C,iBAAiB,CAACyC;cAAQ;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACVnJ,OAAA,CAACnB,UAAU;cACTgI,KAAK,EAAC,gFAAe;cACrBqF,SAAS,EAAEA,CAAA,KAAM7D,YAAY,CAAC4C,MAAM,CAACzG,EAAE,CAAE;cACzC2H,MAAM,EAAC,cAAI;cACXC,UAAU,EAAC,cAAI;cAAAtD,QAAA,eAEf9I,OAAA,CAAChB,OAAO;gBAAC6H,KAAK,EAAC,cAAI;gBAAAiC,QAAA,eACjB9I,OAAA,CAAChC,MAAM;kBACLqM,IAAI,EAAC,MAAM;kBACXR,IAAI,EAAC,OAAO;kBACZwC,MAAM;kBACN9B,IAAI,eAAEvK,OAAA,CAACX,cAAc;oBAAA2J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzB8C,QAAQ,EAAEhB,MAAM,CAAC/H,MAAM,KAAK/C,iBAAiB,CAACyC;gBAAQ;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAEX,CAAC,CACD;QACFmD,UAAU,EAAEhM,YAAa;QACzBiM,MAAM,EAAC,IAAI;QACX3L,OAAO,EAAEA,OAAQ;QACjB4L,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAE3L,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZ8L,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAChM,KAAK,EAAEiM,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQjM,KAAK,IAAI;UAC5CkM,QAAQ,EAAEA,CAAC9L,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnJ,OAAA,CAAC3B,KAAK;MACJwI,KAAK,EAAEvF,WAAW,GAAG,QAAQ,GAAG,QAAS;MACzC2L,IAAI,EAAE7L,cAAe;MACrB8L,QAAQ,EAAEA,CAAA,KAAM7L,iBAAiB,CAAC,KAAK,CAAE;MACzC8L,MAAM,EAAE,IAAK;MACblD,KAAK,EAAE,GAAI;MAAAnB,QAAA,eAEX9I,OAAA,CAAC1B,IAAI;QACH2D,IAAI,EAAEA,IAAK;QACX6H,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEzB,UAAW;QACrB0B,YAAY,EAAC,KAAK;QAAAlB,QAAA,gBAElB9I,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;UACRtF,IAAI,EAAC,WAAW;UAChBlC,KAAK,EAAC,oBAAK;UACX0K,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9O,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAuK,QAAA,eAE/C9I,OAAA,CAAC7B,MAAM;YAACgM,WAAW,EAAC,sCAAQ;YAAArB,QAAA,EACzBpI,QAAQ,CAAC0D,GAAG,CAACwC,OAAO,iBACnB5G,OAAA,CAACE,MAAM;cAAkBuJ,KAAK,EAAE7C,OAAO,CAACpC,EAAG;cAAAsE,QAAA,EACxClC,OAAO,CAACC;YAAK,GADHD,OAAO,CAACpC,EAAE;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZnJ,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;UACRtF,IAAI,EAAC,WAAW;UAChBlC,KAAK,EAAC,cAAI;UACV0K,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9O,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAuK,QAAA,eAE9C9I,OAAA,CAAC7B,MAAM;YAACgM,WAAW,EAAC,gCAAO;YAACmD,UAAU;YAACC,gBAAgB,EAAC,UAAU;YAAAzE,QAAA,EAC/DtI,QAAQ,CAAC4D,GAAG,CAACE,OAAO,iBACnBtE,OAAA,CAACE,MAAM;cAAkBuJ,KAAK,EAAEnF,OAAO,CAACE,EAAG;cAAAsE,QAAA,GACxCxE,OAAO,CAACM,IAAI,EAAC,KAAG,EAACN,OAAO,CAACQ,IAAI;YAAA,GADnBR,OAAO,CAACE,EAAE;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZnJ,OAAA,CAACvB,GAAG;UAAC2K,MAAM,EAAE,EAAG;UAAAN,QAAA,gBACd9I,OAAA,CAACtB,GAAG;YAAC8O,IAAI,EAAE,EAAG;YAAA1E,QAAA,eACZ9I,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;cACRtF,IAAI,EAAC,eAAe;cACpBlC,KAAK,EAAC,oBAAK;cACX0K,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9O,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE8L,IAAI,EAAE,QAAQ;gBAAEoD,GAAG,EAAE,IAAI;gBAAElP,OAAO,EAAE;cAAW,CAAC,CAClD;cAAAuK,QAAA,eAEF9I,OAAA,CAACrB,WAAW;gBACViK,KAAK,EAAE;kBAAEqB,KAAK,EAAE;gBAAO,CAAE;gBACzBE,WAAW,EAAC,sCAAQ;gBACpBuD,SAAS,EAAE,CAAE;gBACbD,GAAG,EAAE,IAAK;gBACVE,WAAW,EAAC;cAAG;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnJ,OAAA,CAACtB,GAAG;YAAC8O,IAAI,EAAE,EAAG;YAAA1E,QAAA,eACZ9I,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;cACRtF,IAAI,EAAC,cAAc;cACnBlC,KAAK,EAAC,4CAAS;cACf0K,KAAK,EAAE,CACL;gBAAE/C,IAAI,EAAE,QAAQ;gBAAEoD,GAAG,EAAE,IAAI;gBAAElP,OAAO,EAAE;cAAW,CAAC,CAClD;cAAAuK,QAAA,eAEF9I,OAAA,CAACrB,WAAW;gBACViK,KAAK,EAAE;kBAAEqB,KAAK,EAAE;gBAAO,CAAE;gBACzBE,WAAW,EAAC,sCAAQ;gBACpBuD,SAAS,EAAE,CAAE;gBACbD,GAAG,EAAE,IAAK;gBACVE,WAAW,EAAC;cAAG;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnJ,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;UACRtF,IAAI,EAAC,cAAc;UACnBlC,KAAK,EAAC,0BAAM;UACZ0K,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE9O,OAAO,EAAE;UAAU,CAAC,EACtC;YAAE8L,IAAI,EAAE,QAAQ;YAAEoD,GAAG,EAAE,IAAI;YAAElP,OAAO,EAAE;UAAY,CAAC,CACnD;UAAAuK,QAAA,eAEF9I,OAAA,CAACrB,WAAW;YACViK,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO,CAAE;YACzBE,WAAW,EAAC,4CAAS;YACrBuD,SAAS,EAAE,CAAE;YACbD,GAAG,EAAE,IAAK;YACVE,WAAW,EAAC;UAAG;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZnJ,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;UACRtF,IAAI,EAAC,WAAW;UAChBlC,KAAK,EAAC,0BAAM;UACZ0K,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE9O,OAAO,EAAE;UAAU,CAAC,CACtC;UAAAuK,QAAA,eAEF9I,OAAA,CAACf,UAAU;YACT2J,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAO,CAAE;YACzB2D,QAAQ;YACRzD,WAAW,EAAC,4CAAS;YACrB0D,MAAM,EAAC;UAAqB;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGZnJ,OAAA,CAACL,WAAW;UAAC0K,IAAI,EAAC,OAAO;UAAC9L,OAAO,EAAE4D,SAAU;UAAC2L,OAAO,EAAE,CAAC,CAAC3L;QAAU;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtEnJ,OAAA,CAACL,WAAW;UAAC0K,IAAI,EAAC,SAAS;UAAC9L,OAAO,EAAE6D,WAAY;UAAC0L,OAAO,EAAE,CAAC,CAAC1L;QAAY;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE5EnJ,OAAA,CAAC1B,IAAI,CAAC4L,IAAI;UAAApB,QAAA,eACR9I,OAAA,CAAC/B,KAAK;YAAC2K,KAAK,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAE8D,cAAc,EAAE;YAAW,CAAE;YAAAjF,QAAA,gBAC1D9I,OAAA,CAAChC,MAAM;cACLwM,OAAO,EAAEA,CAAA,KAAM;gBACbnJ,iBAAiB,CAAC,KAAK,CAAC;gBACxBY,IAAI,CAACiG,WAAW,CAAC,CAAC;gBAClB3G,cAAc,CAAC,IAAI,CAAC;gBACpBgB,gBAAgB,CAAC,CAAC;cACpB,CAAE;cACF0J,QAAQ,EAAEzK,MAAO;cAAAsH,QAAA,EAClB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnJ,OAAA,CAAChC,MAAM;cACLqM,IAAI,EAAC,SAAS;cACdC,QAAQ,EAAC,QAAQ;cACjB1J,OAAO,EAAEY,MAAO;cAChByK,QAAQ,EAAEzK,MAAO;cAAAsH,QAAA,EAEhBtH,MAAM,GAAG,QAAQ,GAAIF,WAAW,GAAG,IAAI,GAAG;YAAK;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9I,EAAA,CAp7BID,YAAsB;EAAA,QAoBX9B,IAAI,CAAC4D,OAAO,EAQvBtC,cAAc;AAAA;AAAAoO,EAAA,GA5Bd5N,YAAsB;AAs7B5B,eAAeA,YAAY;AAAC,IAAA4N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}