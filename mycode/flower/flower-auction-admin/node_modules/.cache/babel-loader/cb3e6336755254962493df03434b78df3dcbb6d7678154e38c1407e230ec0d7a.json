{"ast": null, "code": "import { format } from \"../util\";\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nvar whitespace = function whitespace(rule, value, source, errors, options) {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(format(options.messages.whitespace, rule.fullField));\n  }\n};\nexport default whitespace;", "map": {"version": 3, "names": ["format", "whitespace", "rule", "value", "source", "errors", "options", "test", "push", "messages", "fullField"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/@rc-component/async-validator/es/rule/whitespace.js"], "sourcesContent": ["import { format } from \"../util\";\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nvar whitespace = function whitespace(rule, value, source, errors, options) {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(format(options.messages.whitespace, rule.fullField));\n  }\n};\nexport default whitespace;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACzE,IAAI,OAAO,CAACC,IAAI,CAACJ,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;IACvCE,MAAM,CAACG,IAAI,CAACR,MAAM,CAACM,OAAO,CAACG,QAAQ,CAACR,UAAU,EAAEC,IAAI,CAACQ,SAAS,CAAC,CAAC;EAClE;AACF,CAAC;AACD,eAAeT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}