{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Typography, Row, Col, Image, Descriptions, Radio, Badge } from 'antd';\nimport { SearchOutlined, EyeOutlined, CheckOutlined, CloseOutlined, ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { productService } from '../../../services/productService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\n\n// 审核状态枚举\nexport let AuditStatus = /*#__PURE__*/function (AuditStatus) {\n  AuditStatus[\"PENDING\"] = \"pending\";\n  // 待审核\n  AuditStatus[\"APPROVED\"] = \"approved\";\n  // 已通过\n  AuditStatus[\"REJECTED\"] = \"rejected\"; // 已拒绝\n  return AuditStatus;\n}({});\n\n// 商品数据接口\n\n// 查询参数接口\n\nconst ProductAudit = () => {\n  _s();\n  var _viewingProduct$categ, _qualityLevelMap, _qualityLevelMap2, _auditStatusMap$viewi, _auditStatusMap$viewi2;\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isDetailVisible, setIsDetailVisible] = useState(false);\n  const [isAuditVisible, setIsAuditVisible] = useState(false);\n  const [viewingProduct, setViewingProduct] = useState(null);\n  const [auditingProduct, setAuditingProduct] = useState(null);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 审核状态映射\n  const auditStatusMap = {\n    [AuditStatus.PENDING]: {\n      label: '待审核',\n      color: 'orange'\n    },\n    [AuditStatus.APPROVED]: {\n      label: '已通过',\n      color: 'green'\n    },\n    [AuditStatus.REJECTED]: {\n      label: '已拒绝',\n      color: 'red'\n    }\n  };\n\n  // 质量等级映射\n  const qualityLevelMap = {\n    1: {\n      label: '优',\n      color: 'green'\n    },\n    2: {\n      label: '良',\n      color: 'blue'\n    },\n    3: {\n      label: '中',\n      color: 'orange'\n    }\n  };\n\n  // 获取待审核商品列表\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await productService.getProductList(queryParams);\n      if (response.success) {\n        // 将Product类型转换为AuditProduct类型\n        const auditProducts = response.data.list.map(product => ({\n          ...product,\n          auditStatus: product.auditStatus || AuditStatus.PENDING,\n          auditReason: product.auditReason || '',\n          auditTime: product.auditTime || '',\n          auditorName: product.auditorName || ''\n        }));\n        setProducts(auditProducts);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取商品列表失败');\n      }\n    } catch (error) {\n      message.error(error.message || '获取商品列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 查看商品详情\n  const handleView = product => {\n    setViewingProduct(product);\n    setIsDetailVisible(true);\n  };\n\n  // 开始审核\n  const handleStartAudit = product => {\n    setAuditingProduct(product);\n    form.resetFields();\n    clearAllMessages();\n    setIsAuditVisible(true);\n  };\n\n  // 提交审核\n  const handleSubmitAudit = async values => {\n    if (!auditingProduct) return;\n    setSaving(true);\n    clearAllMessages();\n    try {\n      const response = await productService.auditProduct(auditingProduct.id, values.status, values.reason);\n      const successMsg = '商品审核完成！';\n      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsAuditVisible(false);\n          form.resetFields();\n          setAuditingProduct(null);\n          clearAllMessages();\n          fetchProducts();\n        }, 1500);\n      }\n    } catch (error) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 快速审核通过\n  const handleQuickApprove = async product => {\n    try {\n      const response = await productService.auditProduct(product.id, 'approved');\n      if (response.success) {\n        message.success('审核通过');\n        fetchProducts();\n      } else {\n        message.error(response.message || '审核失败');\n      }\n    } catch (error) {\n      message.error(error.message || '审核失败');\n    }\n  };\n\n  // 快速审核拒绝\n  const handleQuickReject = product => {\n    Modal.confirm({\n      title: '确认拒绝',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 13\n      }, this),\n      content: '确定要拒绝这个商品吗？请输入拒绝原因：',\n      okText: '确定',\n      cancelText: '取消',\n      onOk: async () => {\n        // 这里应该弹出一个输入框让用户输入拒绝原因\n        // 为了简化，这里直接使用默认原因\n        try {\n          const response = await productService.auditProduct(product.id, 'rejected', '商品信息不符合要求');\n          if (response.success) {\n            message.success('审核拒绝');\n            fetchProducts();\n          } else {\n            message.error(response.message || '审核失败');\n          }\n        } catch (error) {\n          message.error(error.message || '审核失败');\n        }\n      }\n    });\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '商品图片',\n    dataIndex: 'images',\n    key: 'images',\n    width: 100,\n    render: images => images && images.length > 0 ? /*#__PURE__*/_jsxDEV(Image, {\n      width: 60,\n      height: 60,\n      src: images[0],\n      style: {\n        objectFit: 'cover',\n        borderRadius: 4\n      },\n      fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: 60,\n        height: 60,\n        background: '#f5f5f5',\n        borderRadius: 4,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: \"\\u65E0\\u56FE\\u7247\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '商品名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontWeight: 500\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '分类',\n    dataIndex: 'category',\n    key: 'category',\n    width: 120,\n    render: (category, record) => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: (category === null || category === void 0 ? void 0 : category.name) || record.categoryName || '未分类'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '质量等级',\n    dataIndex: 'qualityLevel',\n    key: 'qualityLevel',\n    width: 100,\n    render: level => {\n      const levelInfo = qualityLevelMap[level];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: (levelInfo === null || levelInfo === void 0 ? void 0 : levelInfo.color) || 'default',\n        children: (levelInfo === null || levelInfo === void 0 ? void 0 : levelInfo.label) || '未知'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '产地',\n    dataIndex: 'origin',\n    key: 'origin',\n    width: 120\n  }, {\n    title: '供应商',\n    dataIndex: 'supplierName',\n    key: 'supplierName',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"green\",\n      children: text || '未知供应商'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '审核状态',\n    dataIndex: 'auditStatus',\n    key: 'auditStatus',\n    width: 100,\n    render: status => {\n      const statusInfo = auditStatusMap[status];\n      return /*#__PURE__*/_jsxDEV(Badge, {\n        status: status === AuditStatus.PENDING ? 'processing' : status === AuditStatus.APPROVED ? 'success' : 'error',\n        text: /*#__PURE__*/_jsxDEV(Tag, {\n          color: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || 'default',\n          children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.label) || '未知'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '提交时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleView(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 11\n      }, this), record.auditStatus === AuditStatus.PENDING && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(CheckOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 23\n          }, this),\n          style: {\n            color: '#52c41a'\n          },\n          onClick: () => handleQuickApprove(record),\n          children: \"\\u901A\\u8FC7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(CloseOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 23\n          }, this),\n          danger: true,\n          onClick: () => handleQuickReject(record),\n          children: \"\\u62D2\\u7EDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          onClick: () => handleStartAudit(record),\n          children: \"\\u8BE6\\u7EC6\\u5BA1\\u6838\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-audit-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u5546\\u54C1\\u5BA1\\u6838\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"auditStatus\",\n              label: \"\\u5BA1\\u6838\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5BA1\\u6838\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: AuditStatus.PENDING,\n                  children: \"\\u5F85\\u5BA1\\u6838\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuditStatus.APPROVED,\n                  children: \"\\u5DF2\\u901A\\u8FC7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuditStatus.REJECTED,\n                  children: \"\\u5DF2\\u62D2\\u7EDD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"supplierName\",\n              label: \"\\u4F9B\\u5E94\\u5546\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F9B\\u5E94\\u5546\\u540D\\u79F0\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: /*#__PURE__*/_jsxDEV(Badge, {\n              count: products.filter(p => p.auditStatus === AuditStatus.PENDING).length,\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                children: \"\\u5F85\\u5BA1\\u6838\\u5546\\u54C1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 21\n            }, this),\n            onClick: fetchProducts,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: products,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5546\\u54C1\\u8BE6\\u60C5\",\n      open: isDetailVisible,\n      onCancel: () => setIsDetailVisible(false),\n      footer: null,\n      width: 800,\n      children: viewingProduct && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n          title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          column: 2,\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n            children: viewingProduct.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5546\\u54C1\\u5206\\u7C7B\",\n            children: ((_viewingProduct$categ = viewingProduct.category) === null || _viewingProduct$categ === void 0 ? void 0 : _viewingProduct$categ.name) || viewingProduct.categoryName || '未分类'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u8D28\\u91CF\\u7B49\\u7EA7\",\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: (_qualityLevelMap = qualityLevelMap[viewingProduct.qualityLevel]) === null || _qualityLevelMap === void 0 ? void 0 : _qualityLevelMap.color,\n              children: (_qualityLevelMap2 = qualityLevelMap[viewingProduct.qualityLevel]) === null || _qualityLevelMap2 === void 0 ? void 0 : _qualityLevelMap2.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4EA7\\u5730\",\n            children: viewingProduct.origin\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4F9B\\u5E94\\u5546\",\n            children: viewingProduct.supplierName || '未知供应商'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5BA1\\u6838\\u72B6\\u6001\",\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: (_auditStatusMap$viewi = auditStatusMap[viewingProduct.auditStatus]) === null || _auditStatusMap$viewi === void 0 ? void 0 : _auditStatusMap$viewi.color,\n              children: (_auditStatusMap$viewi2 = auditStatusMap[viewingProduct.auditStatus]) === null || _auditStatusMap$viewi2 === void 0 ? void 0 : _auditStatusMap$viewi2.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 13\n        }, this), viewingProduct.description && /*#__PURE__*/_jsxDEV(Descriptions, {\n          title: \"\\u5546\\u54C1\\u63CF\\u8FF0\",\n          column: 1,\n          size: \"small\",\n          style: {\n            marginTop: 24\n          },\n          children: /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u63CF\\u8FF0\",\n            children: viewingProduct.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 15\n        }, this), viewingProduct.images && viewingProduct.images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\u5546\\u54C1\\u56FE\\u7247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: viewingProduct.images.map((image, index) => /*#__PURE__*/_jsxDEV(Image, {\n              width: 100,\n              height: 100,\n              src: image,\n              style: {\n                objectFit: 'cover',\n                borderRadius: 4\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 15\n        }, this), viewingProduct.auditReason && /*#__PURE__*/_jsxDEV(Descriptions, {\n          title: \"\\u5BA1\\u6838\\u4FE1\\u606F\",\n          column: 1,\n          size: \"small\",\n          style: {\n            marginTop: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5BA1\\u6838\\u539F\\u56E0\",\n            children: viewingProduct.auditReason\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 17\n          }, this), viewingProduct.auditTime && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5BA1\\u6838\\u65F6\\u95F4\",\n            children: new Date(viewingProduct.auditTime).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 19\n          }, this), viewingProduct.auditorName && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5BA1\\u6838\\u4EBA\",\n            children: viewingProduct.auditorName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5546\\u54C1\\u5BA1\\u6838\",\n      open: isAuditVisible,\n      onCancel: () => setIsAuditVisible(false),\n      footer: null,\n      width: 600,\n      children: auditingProduct && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n          title: \"\\u5546\\u54C1\\u4FE1\\u606F\",\n          column: 2,\n          size: \"small\",\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n            children: auditingProduct.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4F9B\\u5E94\\u5546\",\n            children: auditingProduct.supplierName || '未知供应商'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          onFinish: handleSubmitAudit,\n          autoComplete: \"off\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"status\",\n            label: \"\\u5BA1\\u6838\\u7ED3\\u679C\",\n            rules: [{\n              required: true,\n              message: '请选择审核结果'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Radio.Group, {\n              children: [/*#__PURE__*/_jsxDEV(Radio, {\n                value: \"approved\",\n                children: \"\\u901A\\u8FC7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Radio, {\n                value: \"rejected\",\n                children: \"\\u62D2\\u7EDD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"reason\",\n            label: \"\\u5BA1\\u6838\\u610F\\u89C1\",\n            rules: [{\n              required: true,\n              message: '请输入审核意见'\n            }, {\n              max: 500,\n              message: '审核意见不能超过500个字符'\n            }],\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA1\\u6838\\u610F\\u89C1\",\n              rows: 4,\n              showCount: true,\n              maxLength: 500\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormMessage, {\n            type: \"error\",\n            message: formError,\n            visible: !!formError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormMessage, {\n            type: \"success\",\n            message: formSuccess,\n            visible: !!formSuccess\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                width: '100%',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => {\n                  setIsAuditVisible(false);\n                  form.resetFields();\n                  setAuditingProduct(null);\n                  clearAllMessages();\n                },\n                disabled: saving,\n                children: \"\\u53D6\\u6D88\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: saving,\n                disabled: saving,\n                children: saving ? '提交中...' : '提交审核'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 401,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductAudit, \"HatM9i2d/HovfpN2erLSUDVfFhY=\", false, function () {\n  return [Form.useForm, useFormMessage];\n});\n_c = ProductAudit;\nexport default ProductAudit;\nvar _c;\n$RefreshReg$(_c, \"ProductAudit\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Typography", "Row", "Col", "Image", "Descriptions", "Radio", "Badge", "SearchOutlined", "EyeOutlined", "CheckOutlined", "CloseOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "productService", "FormMessage", "useFormMessage", "handleApiResponse", "handleApiError", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Option", "TextArea", "AuditStatus", "ProductAudit", "_s", "_viewingProduct$categ", "_qualityLevelMap", "_qualityLevelMap2", "_auditStatusMap$viewi", "_auditStatusMap$viewi2", "products", "setProducts", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isDetailVisible", "setIsDetailVisible", "isAuditVisible", "setIsAuditVisible", "viewingProduct", "setViewingProduct", "auditingProduct", "setAuditingProduct", "saving", "setSaving", "form", "useForm", "formError", "formSuccess", "setFormError", "setFormSuccess", "clearAllMessages", "auditStatusMap", "PENDING", "label", "color", "APPROVED", "REJECTED", "qualityLevelMap", "fetchProducts", "response", "getProductList", "success", "auditProducts", "data", "list", "map", "product", "auditStatus", "auditReason", "auditTime", "auditorName", "error", "handleSearch", "values", "handleReset", "handleView", "handleStartAudit", "resetFields", "handleSubmitAudit", "auditProduct", "id", "status", "reason", "successMsg", "setTimeout", "handleQuickApprove", "handleQuickReject", "confirm", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "okText", "cancelText", "onOk", "columns", "dataIndex", "key", "width", "render", "images", "length", "height", "src", "style", "objectFit", "borderRadius", "fallback", "background", "display", "alignItems", "justifyContent", "children", "text", "fontWeight", "category", "record", "name", "categoryName", "level", "levelInfo", "statusInfo", "Date", "toLocaleString", "fixed", "_", "size", "type", "onClick", "danger", "className", "layout", "onFinish", "autoComplete", "gutter", "xs", "sm", "md", "<PERSON><PERSON>", "placeholder", "allowClear", "value", "htmlType", "justify", "align", "count", "filter", "p", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "open", "onCancel", "footer", "column", "qualityLevel", "origin", "supplierName", "description", "marginTop", "wrap", "image", "index", "marginBottom", "rules", "required", "Group", "max", "rows", "showCount", "max<PERSON><PERSON><PERSON>", "visible", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Typography,\n  Row,\n  Col,\n  Image,\n  Descriptions,\n  Radio,\n  Badge,\n} from 'antd';\nimport {\n  SearchOutlined,\n  EyeOutlined,\n  CheckOutlined,\n  CloseOutlined,\n  ReloadOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { productService } from '../../../services/productService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiResponse, handleApiError } from '../../../hooks/useFormMessage';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// 审核状态枚举\nexport enum AuditStatus {\n  PENDING = 'pending',    // 待审核\n  APPROVED = 'approved',  // 已通过\n  REJECTED = 'rejected',  // 已拒绝\n}\n\n// 商品数据接口\nexport interface AuditProduct {\n  id: number;\n  name: string;\n  category?: { name: string };\n  categoryName?: string; // 兼容字段\n  description?: string;\n  qualityLevel: number;\n  origin: string;\n  supplierName?: string;\n  images?: string[];\n  auditStatus: AuditStatus;\n  auditReason?: string;\n  auditTime?: string;\n  auditorName?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数接口\ninterface AuditQueryParams {\n  name?: string;\n  auditStatus?: AuditStatus;\n  supplierName?: string;\n  page: number;\n  pageSize: number;\n}\n\nconst ProductAudit: React.FC = () => {\n  const [products, setProducts] = useState<AuditProduct[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<AuditQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isDetailVisible, setIsDetailVisible] = useState(false);\n  const [isAuditVisible, setIsAuditVisible] = useState(false);\n  const [viewingProduct, setViewingProduct] = useState<AuditProduct | null>(null);\n  const [auditingProduct, setAuditingProduct] = useState<AuditProduct | null>(null);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 审核状态映射\n  const auditStatusMap = {\n    [AuditStatus.PENDING]: { label: '待审核', color: 'orange' },\n    [AuditStatus.APPROVED]: { label: '已通过', color: 'green' },\n    [AuditStatus.REJECTED]: { label: '已拒绝', color: 'red' },\n  };\n\n  // 质量等级映射\n  const qualityLevelMap = {\n    1: { label: '优', color: 'green' },\n    2: { label: '良', color: 'blue' },\n    3: { label: '中', color: 'orange' },\n  };\n\n  // 获取待审核商品列表\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await productService.getProductList(queryParams);\n      if (response.success) {\n        // 将Product类型转换为AuditProduct类型\n        const auditProducts: AuditProduct[] = response.data.list.map((product: any) => ({\n          ...product,\n          auditStatus: product.auditStatus || AuditStatus.PENDING,\n          auditReason: product.auditReason || '',\n          auditTime: product.auditTime || '',\n          auditorName: product.auditorName || '',\n        }));\n        setProducts(auditProducts);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取商品列表失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '获取商品列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 查看商品详情\n  const handleView = (product: AuditProduct) => {\n    setViewingProduct(product);\n    setIsDetailVisible(true);\n  };\n\n  // 开始审核\n  const handleStartAudit = (product: AuditProduct) => {\n    setAuditingProduct(product);\n    form.resetFields();\n    clearAllMessages();\n    setIsAuditVisible(true);\n  };\n\n  // 提交审核\n  const handleSubmitAudit = async (values: any) => {\n    if (!auditingProduct) return;\n\n    setSaving(true);\n    clearAllMessages();\n\n    try {\n      const response = await productService.auditProduct(\n        auditingProduct.id,\n        values.status,\n        values.reason\n      );\n\n      const successMsg = '商品审核完成！';\n\n      if (handleApiResponse(response, setFormError, setFormSuccess, successMsg)) {\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsAuditVisible(false);\n          form.resetFields();\n          setAuditingProduct(null);\n          clearAllMessages();\n          fetchProducts();\n        }, 1500);\n      }\n    } catch (error: any) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 快速审核通过\n  const handleQuickApprove = async (product: AuditProduct) => {\n    try {\n      const response = await productService.auditProduct(product.id, 'approved');\n      if (response.success) {\n        message.success('审核通过');\n        fetchProducts();\n      } else {\n        message.error(response.message || '审核失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '审核失败');\n    }\n  };\n\n  // 快速审核拒绝\n  const handleQuickReject = (product: AuditProduct) => {\n    Modal.confirm({\n      title: '确认拒绝',\n      icon: <ExclamationCircleOutlined />,\n      content: '确定要拒绝这个商品吗？请输入拒绝原因：',\n      okText: '确定',\n      cancelText: '取消',\n      onOk: async () => {\n        // 这里应该弹出一个输入框让用户输入拒绝原因\n        // 为了简化，这里直接使用默认原因\n        try {\n          const response = await productService.auditProduct(\n            product.id,\n            'rejected',\n            '商品信息不符合要求'\n          );\n          if (response.success) {\n            message.success('审核拒绝');\n            fetchProducts();\n          } else {\n            message.error(response.message || '审核失败');\n          }\n        } catch (error: any) {\n          message.error(error.message || '审核失败');\n        }\n      },\n    });\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<AuditProduct> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '商品图片',\n      dataIndex: 'images',\n      key: 'images',\n      width: 100,\n      render: (images: string[]) => (\n        images && images.length > 0 ? (\n          <Image\n            width={60}\n            height={60}\n            src={images[0]}\n            style={{ objectFit: 'cover', borderRadius: 4 }}\n            fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n          />\n        ) : (\n          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            无图片\n          </div>\n        )\n      ),\n    },\n    {\n      title: '商品名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontWeight: 500 }}>{text}</div>\n      ),\n    },\n    {\n      title: '分类',\n      dataIndex: 'category',\n      key: 'category',\n      width: 120,\n      render: (category: any, record: AuditProduct) => (\n        <Tag color=\"blue\">{category?.name || record.categoryName || '未分类'}</Tag>\n      ),\n    },\n    {\n      title: '质量等级',\n      dataIndex: 'qualityLevel',\n      key: 'qualityLevel',\n      width: 100,\n      render: (level: number) => {\n        const levelInfo = qualityLevelMap[level as keyof typeof qualityLevelMap];\n        return (\n          <Tag color={levelInfo?.color || 'default'}>\n            {levelInfo?.label || '未知'}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '产地',\n      dataIndex: 'origin',\n      key: 'origin',\n      width: 120,\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplierName',\n      key: 'supplierName',\n      width: 120,\n      render: (text: string) => <Tag color=\"green\">{text || '未知供应商'}</Tag>,\n    },\n    {\n      title: '审核状态',\n      dataIndex: 'auditStatus',\n      key: 'auditStatus',\n      width: 100,\n      render: (status: AuditStatus) => {\n        const statusInfo = auditStatusMap[status];\n        return (\n          <Badge\n            status={status === AuditStatus.PENDING ? 'processing' : status === AuditStatus.APPROVED ? 'success' : 'error'}\n            text={\n              <Tag color={statusInfo?.color || 'default'}>\n                {statusInfo?.label || '未知'}\n              </Tag>\n            }\n          />\n        );\n      },\n    },\n    {\n      title: '提交时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right',\n      render: (_, record: AuditProduct) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleView(record)}\n          >\n            查看\n          </Button>\n          {record.auditStatus === AuditStatus.PENDING && (\n            <>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<CheckOutlined />}\n                style={{ color: '#52c41a' }}\n                onClick={() => handleQuickApprove(record)}\n              >\n                通过\n              </Button>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<CloseOutlined />}\n                danger\n                onClick={() => handleQuickReject(record)}\n              >\n                拒绝\n              </Button>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                onClick={() => handleStartAudit(record)}\n              >\n                详细审核\n              </Button>\n            </>\n          )}\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"product-audit-container\">\n      <Title level={2}>商品审核</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"name\" label=\"商品名称\">\n                <Input placeholder=\"请输入商品名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"auditStatus\" label=\"审核状态\">\n                <Select placeholder=\"请选择审核状态\" allowClear>\n                  <Option value={AuditStatus.PENDING}>待审核</Option>\n                  <Option value={AuditStatus.APPROVED}>已通过</Option>\n                  <Option value={AuditStatus.REJECTED}>已拒绝</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"supplierName\" label=\"供应商\">\n                <Input placeholder=\"请输入供应商名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Badge count={products.filter(p => p.auditStatus === AuditStatus.PENDING).length}>\n                <Button>待审核商品</Button>\n              </Badge>\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchProducts}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 商品列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={products}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 商品详情模态框 */}\n      <Modal\n        title=\"商品详情\"\n        open={isDetailVisible}\n        onCancel={() => setIsDetailVisible(false)}\n        footer={null}\n        width={800}\n      >\n        {viewingProduct && (\n          <div>\n            <Descriptions title=\"基本信息\" column={2} size=\"small\">\n              <Descriptions.Item label=\"商品名称\">{viewingProduct.name}</Descriptions.Item>\n              <Descriptions.Item label=\"商品分类\">{viewingProduct.category?.name || viewingProduct.categoryName || '未分类'}</Descriptions.Item>\n              <Descriptions.Item label=\"质量等级\">\n                <Tag color={qualityLevelMap[viewingProduct.qualityLevel as keyof typeof qualityLevelMap]?.color}>\n                  {qualityLevelMap[viewingProduct.qualityLevel as keyof typeof qualityLevelMap]?.label}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"产地\">{viewingProduct.origin}</Descriptions.Item>\n              <Descriptions.Item label=\"供应商\">{viewingProduct.supplierName || '未知供应商'}</Descriptions.Item>\n              <Descriptions.Item label=\"审核状态\">\n                <Tag color={auditStatusMap[viewingProduct.auditStatus]?.color}>\n                  {auditStatusMap[viewingProduct.auditStatus]?.label}\n                </Tag>\n              </Descriptions.Item>\n            </Descriptions>\n\n            {viewingProduct.description && (\n              <Descriptions title=\"商品描述\" column={1} size=\"small\" style={{ marginTop: 24 }}>\n                <Descriptions.Item label=\"描述\">{viewingProduct.description}</Descriptions.Item>\n              </Descriptions>\n            )}\n\n            {viewingProduct.images && viewingProduct.images.length > 0 && (\n              <div style={{ marginTop: 24 }}>\n                <h4>商品图片</h4>\n                <Space wrap>\n                  {viewingProduct.images.map((image, index) => (\n                    <Image\n                      key={index}\n                      width={100}\n                      height={100}\n                      src={image}\n                      style={{ objectFit: 'cover', borderRadius: 4 }}\n                    />\n                  ))}\n                </Space>\n              </div>\n            )}\n\n            {viewingProduct.auditReason && (\n              <Descriptions title=\"审核信息\" column={1} size=\"small\" style={{ marginTop: 24 }}>\n                <Descriptions.Item label=\"审核原因\">{viewingProduct.auditReason}</Descriptions.Item>\n                {viewingProduct.auditTime && (\n                  <Descriptions.Item label=\"审核时间\">\n                    {new Date(viewingProduct.auditTime).toLocaleString()}\n                  </Descriptions.Item>\n                )}\n                {viewingProduct.auditorName && (\n                  <Descriptions.Item label=\"审核人\">{viewingProduct.auditorName}</Descriptions.Item>\n                )}\n              </Descriptions>\n            )}\n          </div>\n        )}\n      </Modal>\n\n      {/* 审核模态框 */}\n      <Modal\n        title=\"商品审核\"\n        open={isAuditVisible}\n        onCancel={() => setIsAuditVisible(false)}\n        footer={null}\n        width={600}\n      >\n        {auditingProduct && (\n          <div>\n            <Descriptions title=\"商品信息\" column={2} size=\"small\" style={{ marginBottom: 24 }}>\n              <Descriptions.Item label=\"商品名称\">{auditingProduct.name}</Descriptions.Item>\n              <Descriptions.Item label=\"供应商\">{auditingProduct.supplierName || '未知供应商'}</Descriptions.Item>\n            </Descriptions>\n\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={handleSubmitAudit}\n              autoComplete=\"off\"\n            >\n              <Form.Item\n                name=\"status\"\n                label=\"审核结果\"\n                rules={[{ required: true, message: '请选择审核结果' }]}\n              >\n                <Radio.Group>\n                  <Radio value=\"approved\">通过</Radio>\n                  <Radio value=\"rejected\">拒绝</Radio>\n                </Radio.Group>\n              </Form.Item>\n\n              <Form.Item\n                name=\"reason\"\n                label=\"审核意见\"\n                rules={[\n                  { required: true, message: '请输入审核意见' },\n                  { max: 500, message: '审核意见不能超过500个字符' },\n                ]}\n              >\n                <TextArea\n                  placeholder=\"请输入审核意见\"\n                  rows={4}\n                  showCount\n                  maxLength={500}\n                />\n              </Form.Item>\n\n              {/* 错误和成功消息显示 */}\n              <FormMessage type=\"error\" message={formError} visible={!!formError} />\n              <FormMessage type=\"success\" message={formSuccess} visible={!!formSuccess} />\n\n              <Form.Item>\n                <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n                  <Button\n                    onClick={() => {\n                      setIsAuditVisible(false);\n                      form.resetFields();\n                      setAuditingProduct(null);\n                      clearAllMessages();\n                    }}\n                    disabled={saving}\n                  >\n                    取消\n                  </Button>\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    loading={saving}\n                    disabled={saving}\n                  >\n                    {saving ? '提交中...' : '提交审核'}\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Form>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProductAudit;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,YAAY,EACZC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,yBAAyB,QACpB,mBAAmB;AAE1B,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,+BAA+B;AACjG,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGtB,UAAU;AAC5B,MAAM;EAAEuB;AAAO,CAAC,GAAG5B,MAAM;AACzB,MAAM;EAAE6B;AAAS,CAAC,GAAG9B,KAAK;;AAE1B;AACA,WAAY+B,WAAW,0BAAXA,WAAW;EAAXA,WAAW;EACG;EADdA,WAAW;EAEG;EAFdA,WAAW,2BAGG;EAAA,OAHdA,WAAW;AAAA;;AAMvB;;AAmBA;;AASA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACnC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAiB,EAAE,CAAC;EAC5D,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAmB;IAC/DqD,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAsB,IAAI,CAAC;EAC/E,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAsB,IAAI,CAAC;EACjF,MAAM,CAAC+D,MAAM,EAAEC,SAAS,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiE,IAAI,CAAC,GAAGvD,IAAI,CAACwD,OAAO,CAAC,CAAC;EAE7B,MAAM;IACJC,SAAS;IACTC,WAAW;IACXC,YAAY;IACZC,cAAc;IACdC;EACF,CAAC,GAAG5C,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM6C,cAAc,GAAG;IACrB,CAACnC,WAAW,CAACoC,OAAO,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS,CAAC;IACxD,CAACtC,WAAW,CAACuC,QAAQ,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACxD,CAACtC,WAAW,CAACwC,QAAQ,GAAG;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM;EACvD,CAAC;;EAED;EACA,MAAMG,eAAe,GAAG;IACtB,CAAC,EAAE;MAAEJ,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACjC,CAAC,EAAE;MAAED,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAO,CAAC;IAChC,CAAC,EAAE;MAAED,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAS;EACnC,CAAC;;EAED;EACA,MAAMI,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC/B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMgC,QAAQ,GAAG,MAAMvD,cAAc,CAACwD,cAAc,CAAC9B,WAAW,CAAC;MACjE,IAAI6B,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMC,aAA6B,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAEC,OAAY,KAAM;UAC9E,GAAGA,OAAO;UACVC,WAAW,EAAED,OAAO,CAACC,WAAW,IAAInD,WAAW,CAACoC,OAAO;UACvDgB,WAAW,EAAEF,OAAO,CAACE,WAAW,IAAI,EAAE;UACtCC,SAAS,EAAEH,OAAO,CAACG,SAAS,IAAI,EAAE;UAClCC,WAAW,EAAEJ,OAAO,CAACI,WAAW,IAAI;QACtC,CAAC,CAAC,CAAC;QACH7C,WAAW,CAACqC,aAAa,CAAC;QAC1BjC,QAAQ,CAAC8B,QAAQ,CAACI,IAAI,CAACnC,KAAK,CAAC;MAC/B,CAAC,MAAM;QACLtC,OAAO,CAACiF,KAAK,CAACZ,QAAQ,CAACrE,OAAO,IAAI,UAAU,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOiF,KAAU,EAAE;MACnBjF,OAAO,CAACiF,KAAK,CAACA,KAAK,CAACjF,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRqC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA/C,SAAS,CAAC,MAAM;IACd8E,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC5B,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM0C,YAAY,GAAIC,MAAW,IAAK;IACpC1C,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAG2C,MAAM;MACTzC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0C,WAAW,GAAGA,CAAA,KAAM;IACxB3C,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0C,UAAU,GAAIT,OAAqB,IAAK;IAC5C3B,iBAAiB,CAAC2B,OAAO,CAAC;IAC1B/B,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAIV,OAAqB,IAAK;IAClDzB,kBAAkB,CAACyB,OAAO,CAAC;IAC3BtB,IAAI,CAACiC,WAAW,CAAC,CAAC;IAClB3B,gBAAgB,CAAC,CAAC;IAClBb,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMyC,iBAAiB,GAAG,MAAOL,MAAW,IAAK;IAC/C,IAAI,CAACjC,eAAe,EAAE;IAEtBG,SAAS,CAAC,IAAI,CAAC;IACfO,gBAAgB,CAAC,CAAC;IAElB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMvD,cAAc,CAAC2E,YAAY,CAChDvC,eAAe,CAACwC,EAAE,EAClBP,MAAM,CAACQ,MAAM,EACbR,MAAM,CAACS,MACT,CAAC;MAED,MAAMC,UAAU,GAAG,SAAS;MAE5B,IAAI5E,iBAAiB,CAACoD,QAAQ,EAAEX,YAAY,EAAEC,cAAc,EAAEkC,UAAU,CAAC,EAAE;QACzE;QACAC,UAAU,CAAC,MAAM;UACf/C,iBAAiB,CAAC,KAAK,CAAC;UACxBO,IAAI,CAACiC,WAAW,CAAC,CAAC;UAClBpC,kBAAkB,CAAC,IAAI,CAAC;UACxBS,gBAAgB,CAAC,CAAC;UAClBQ,aAAa,CAAC,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOa,KAAU,EAAE;MACnB/D,cAAc,CAAC+D,KAAK,EAAEvB,YAAY,CAAC;IACrC,CAAC,SAAS;MACRL,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM0C,kBAAkB,GAAG,MAAOnB,OAAqB,IAAK;IAC1D,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMvD,cAAc,CAAC2E,YAAY,CAACb,OAAO,CAACc,EAAE,EAAE,UAAU,CAAC;MAC1E,IAAIrB,QAAQ,CAACE,OAAO,EAAE;QACpBvE,OAAO,CAACuE,OAAO,CAAC,MAAM,CAAC;QACvBH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLpE,OAAO,CAACiF,KAAK,CAACZ,QAAQ,CAACrE,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOiF,KAAU,EAAE;MACnBjF,OAAO,CAACiF,KAAK,CAACA,KAAK,CAACjF,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMgG,iBAAiB,GAAIpB,OAAqB,IAAK;IACnD9E,KAAK,CAACmG,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,IAAI,eAAE/E,OAAA,CAACP,yBAAyB;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,EAAE,qBAAqB;MAC9BC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB;QACA;QACA,IAAI;UACF,MAAMtC,QAAQ,GAAG,MAAMvD,cAAc,CAAC2E,YAAY,CAChDb,OAAO,CAACc,EAAE,EACV,UAAU,EACV,WACF,CAAC;UACD,IAAIrB,QAAQ,CAACE,OAAO,EAAE;YACpBvE,OAAO,CAACuE,OAAO,CAAC,MAAM,CAAC;YACvBH,aAAa,CAAC,CAAC;UACjB,CAAC,MAAM;YACLpE,OAAO,CAACiF,KAAK,CAACZ,QAAQ,CAACrE,OAAO,IAAI,MAAM,CAAC;UAC3C;QACF,CAAC,CAAC,OAAOiF,KAAU,EAAE;UACnBjF,OAAO,CAACiF,KAAK,CAACA,KAAK,CAACjF,OAAO,IAAI,MAAM,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4G,OAAkC,GAAG,CACzC;IACEV,KAAK,EAAE,IAAI;IACXW,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbW,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,MAAgB,IACvBA,MAAM,IAAIA,MAAM,CAACC,MAAM,GAAG,CAAC,gBACzB9F,OAAA,CAAChB,KAAK;MACJ2G,KAAK,EAAE,EAAG;MACVI,MAAM,EAAE,EAAG;MACXC,GAAG,EAAEH,MAAM,CAAC,CAAC,CAAE;MACfI,KAAK,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,YAAY,EAAE;MAAE,CAAE;MAC/CC,QAAQ,EAAC;IAAgoB;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1oB,CAAC,gBAEFnF,OAAA;MAAKiG,KAAK,EAAE;QAAEN,KAAK,EAAE,EAAE;QAAEI,MAAM,EAAE,EAAE;QAAEM,UAAU,EAAE,SAAS;QAAEF,YAAY,EAAE,CAAC;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,EAAC;IAEhJ;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAGX,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbW,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGc,IAAY,iBACnB1G,OAAA;MAAKiG,KAAK,EAAE;QAAEU,UAAU,EAAE;MAAI,CAAE;MAAAF,QAAA,EAAEC;IAAI;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEhD,CAAC,EACD;IACEL,KAAK,EAAE,IAAI;IACXW,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACgB,QAAa,EAAEC,MAAoB,kBAC1C7G,OAAA,CAACvB,GAAG;MAACmE,KAAK,EAAC,MAAM;MAAA6D,QAAA,EAAE,CAAAG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,KAAID,MAAM,CAACE,YAAY,IAAI;IAAK;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAE3E,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbW,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGoB,KAAa,IAAK;MACzB,MAAMC,SAAS,GAAGlE,eAAe,CAACiE,KAAK,CAAiC;MACxE,oBACEhH,OAAA,CAACvB,GAAG;QAACmE,KAAK,EAAE,CAAAqE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAErE,KAAK,KAAI,SAAU;QAAA6D,QAAA,EACvC,CAAAQ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEtE,KAAK,KAAI;MAAI;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAEV;EACF,CAAC,EACD;IACEL,KAAK,EAAE,IAAI;IACXW,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACEb,KAAK,EAAE,KAAK;IACZW,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGc,IAAY,iBAAK1G,OAAA,CAACvB,GAAG;MAACmE,KAAK,EAAC,OAAO;MAAA6D,QAAA,EAAEC,IAAI,IAAI;IAAO;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACrE,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbW,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGrB,MAAmB,IAAK;MAC/B,MAAM2C,UAAU,GAAGzE,cAAc,CAAC8B,MAAM,CAAC;MACzC,oBACEvE,OAAA,CAACb,KAAK;QACJoF,MAAM,EAAEA,MAAM,KAAKjE,WAAW,CAACoC,OAAO,GAAG,YAAY,GAAG6B,MAAM,KAAKjE,WAAW,CAACuC,QAAQ,GAAG,SAAS,GAAG,OAAQ;QAC9G6D,IAAI,eACF1G,OAAA,CAACvB,GAAG;UAACmE,KAAK,EAAE,CAAAsE,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEtE,KAAK,KAAI,SAAU;UAAA6D,QAAA,EACxC,CAAAS,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEvE,KAAK,KAAI;QAAI;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEN;EACF,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbW,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGc,IAAY,IAAK,IAAIS,IAAI,CAACT,IAAI,CAAC,CAACU,cAAc,CAAC;EAC1D,CAAC,EACD;IACEtC,KAAK,EAAE,IAAI;IACXY,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACV0B,KAAK,EAAE,OAAO;IACdzB,MAAM,EAAEA,CAAC0B,CAAC,EAAET,MAAoB,kBAC9B7G,OAAA,CAAC1B,KAAK;MAACiJ,IAAI,EAAC,OAAO;MAAAd,QAAA,gBACjBzG,OAAA,CAAC3B,MAAM;QACLmJ,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZxC,IAAI,eAAE/E,OAAA,CAACX,WAAW;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBsC,OAAO,EAAEA,CAAA,KAAMxD,UAAU,CAAC4C,MAAM,CAAE;QAAAJ,QAAA,EACnC;MAED;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR0B,MAAM,CAACpD,WAAW,KAAKnD,WAAW,CAACoC,OAAO,iBACzC1C,OAAA,CAAAE,SAAA;QAAAuG,QAAA,gBACEzG,OAAA,CAAC3B,MAAM;UACLmJ,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZxC,IAAI,eAAE/E,OAAA,CAACV,aAAa;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBc,KAAK,EAAE;YAAErD,KAAK,EAAE;UAAU,CAAE;UAC5B6E,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACkC,MAAM,CAAE;UAAAJ,QAAA,EAC3C;QAED;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnF,OAAA,CAAC3B,MAAM;UACLmJ,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZxC,IAAI,eAAE/E,OAAA,CAACT,aAAa;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBuC,MAAM;UACND,OAAO,EAAEA,CAAA,KAAM7C,iBAAiB,CAACiC,MAAM,CAAE;UAAAJ,QAAA,EAC1C;QAED;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnF,OAAA,CAAC3B,MAAM;UACLmJ,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZE,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAAC2C,MAAM,CAAE;UAAAJ,QAAA,EACzC;QAED;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,CACF;EAED,oBACEnF,OAAA;IAAK2H,SAAS,EAAC,yBAAyB;IAAAlB,QAAA,gBACtCzG,OAAA,CAACG,KAAK;MAAC6G,KAAK,EAAE,CAAE;MAAAP,QAAA,EAAC;IAAI;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7BnF,OAAA,CAAC7B,IAAI;MAACwJ,SAAS,EAAC,aAAa;MAACJ,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxCzG,OAAA,CAACrB,IAAI;QACHiJ,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAE/D,YAAa;QACvBgE,YAAY,EAAC,KAAK;QAAArB,QAAA,eAElBzG,OAAA,CAAClB,GAAG;UAACiJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAC9B,KAAK,EAAE;YAAEN,KAAK,EAAE;UAAO,CAAE;UAAAc,QAAA,gBAC9CzG,OAAA,CAACjB,GAAG;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzBzG,OAAA,CAACrB,IAAI,CAACwJ,IAAI;cAACrB,IAAI,EAAC,MAAM;cAACnE,KAAK,EAAC,0BAAM;cAAA8D,QAAA,eACjCzG,OAAA,CAACzB,KAAK;gBAAC6J,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzBzG,OAAA,CAACrB,IAAI,CAACwJ,IAAI;cAACrB,IAAI,EAAC,aAAa;cAACnE,KAAK,EAAC,0BAAM;cAAA8D,QAAA,eACxCzG,OAAA,CAACxB,MAAM;gBAAC4J,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAA5B,QAAA,gBACtCzG,OAAA,CAACI,MAAM;kBAACkI,KAAK,EAAEhI,WAAW,CAACoC,OAAQ;kBAAA+D,QAAA,EAAC;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDnF,OAAA,CAACI,MAAM;kBAACkI,KAAK,EAAEhI,WAAW,CAACuC,QAAS;kBAAA4D,QAAA,EAAC;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjDnF,OAAA,CAACI,MAAM;kBAACkI,KAAK,EAAEhI,WAAW,CAACwC,QAAS;kBAAA2D,QAAA,EAAC;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzBzG,OAAA,CAACrB,IAAI,CAACwJ,IAAI;cAACrB,IAAI,EAAC,cAAc;cAACnE,KAAK,EAAC,oBAAK;cAAA8D,QAAA,eACxCzG,OAAA,CAACzB,KAAK;gBAAC6J,WAAW,EAAC,kDAAU;gBAACC,UAAU;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzBzG,OAAA,CAACrB,IAAI,CAACwJ,IAAI;cAAA1B,QAAA,eACRzG,OAAA,CAAC1B,KAAK;gBAAAmI,QAAA,gBACJzG,OAAA,CAAC3B,MAAM;kBAACmJ,IAAI,EAAC,SAAS;kBAACe,QAAQ,EAAC,QAAQ;kBAACxD,IAAI,eAAE/E,OAAA,CAACZ,cAAc;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAsB,QAAA,EAAC;gBAEnE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnF,OAAA,CAAC3B,MAAM;kBAACoJ,OAAO,EAAEzD,WAAY;kBAACe,IAAI,eAAE/E,OAAA,CAACR,cAAc;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAsB,QAAA,EAAC;gBAExD;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPnF,OAAA,CAAC7B,IAAI;MAACwJ,SAAS,EAAC,aAAa;MAACJ,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxCzG,OAAA,CAAClB,GAAG;QAAC0J,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAhC,QAAA,gBACzCzG,OAAA,CAACjB,GAAG;UAAA0H,QAAA,eACFzG,OAAA,CAAC1B,KAAK;YAAAmI,QAAA,eACJzG,OAAA,CAACb,KAAK;cAACuJ,KAAK,EAAE5H,QAAQ,CAAC6H,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnF,WAAW,KAAKnD,WAAW,CAACoC,OAAO,CAAC,CAACoD,MAAO;cAAAW,QAAA,eAC/EzG,OAAA,CAAC3B,MAAM;gBAAAoI,QAAA,EAAC;cAAK;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNnF,OAAA,CAACjB,GAAG;UAAA0H,QAAA,eACFzG,OAAA,CAAC3B,MAAM;YACL0G,IAAI,eAAE/E,OAAA,CAACR,cAAc;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBsC,OAAO,EAAEzE,aAAc;YACvBhC,OAAO,EAAEA,OAAQ;YAAAyF,QAAA,EAClB;UAED;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnF,OAAA,CAAC7B,IAAI;MAAAsI,QAAA,eACHzG,OAAA,CAAC5B,KAAK;QACJoH,OAAO,EAAEA,OAAQ;QACjBqD,UAAU,EAAE/H,QAAS;QACrBgI,MAAM,EAAC,IAAI;QACX9H,OAAO,EAAEA,OAAQ;QACjB+H,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAE9H,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZiI,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACnI,KAAK,EAAEoI,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQpI,KAAK,IAAI;UAC5CqI,QAAQ,EAAEA,CAACjI,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnF,OAAA,CAACtB,KAAK;MACJoG,KAAK,EAAC,0BAAM;MACZ0E,IAAI,EAAEhI,eAAgB;MACtBiI,QAAQ,EAAEA,CAAA,KAAMhI,kBAAkB,CAAC,KAAK,CAAE;MAC1CiI,MAAM,EAAE,IAAK;MACb/D,KAAK,EAAE,GAAI;MAAAc,QAAA,EAEV7E,cAAc,iBACb5B,OAAA;QAAAyG,QAAA,gBACEzG,OAAA,CAACf,YAAY;UAAC6F,KAAK,EAAC,0BAAM;UAAC6E,MAAM,EAAE,CAAE;UAACpC,IAAI,EAAC,OAAO;UAAAd,QAAA,gBAChDzG,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,0BAAM;YAAA8D,QAAA,EAAE7E,cAAc,CAACkF;UAAI;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACzEnF,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,0BAAM;YAAA8D,QAAA,EAAE,EAAAhG,qBAAA,GAAAmB,cAAc,CAACgF,QAAQ,cAAAnG,qBAAA,uBAAvBA,qBAAA,CAAyBqG,IAAI,KAAIlF,cAAc,CAACmF,YAAY,IAAI;UAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC3HnF,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,0BAAM;YAAA8D,QAAA,eAC7BzG,OAAA,CAACvB,GAAG;cAACmE,KAAK,GAAAlC,gBAAA,GAAEqC,eAAe,CAACnB,cAAc,CAACgI,YAAY,CAAiC,cAAAlJ,gBAAA,uBAA5EA,gBAAA,CAA8EkC,KAAM;cAAA6D,QAAA,GAAA9F,iBAAA,GAC7FoC,eAAe,CAACnB,cAAc,CAACgI,YAAY,CAAiC,cAAAjJ,iBAAA,uBAA5EA,iBAAA,CAA8EgC;YAAK;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC,eACpBnF,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,cAAI;YAAA8D,QAAA,EAAE7E,cAAc,CAACiI;UAAM;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACzEnF,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,oBAAK;YAAA8D,QAAA,EAAE7E,cAAc,CAACkI,YAAY,IAAI;UAAO;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC3FnF,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,0BAAM;YAAA8D,QAAA,eAC7BzG,OAAA,CAACvB,GAAG;cAACmE,KAAK,GAAAhC,qBAAA,GAAE6B,cAAc,CAACb,cAAc,CAAC6B,WAAW,CAAC,cAAA7C,qBAAA,uBAA1CA,qBAAA,CAA4CgC,KAAM;cAAA6D,QAAA,GAAA5F,sBAAA,GAC3D4B,cAAc,CAACb,cAAc,CAAC6B,WAAW,CAAC,cAAA5C,sBAAA,uBAA1CA,sBAAA,CAA4C8B;YAAK;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEdvD,cAAc,CAACmI,WAAW,iBACzB/J,OAAA,CAACf,YAAY;UAAC6F,KAAK,EAAC,0BAAM;UAAC6E,MAAM,EAAE,CAAE;UAACpC,IAAI,EAAC,OAAO;UAACtB,KAAK,EAAE;YAAE+D,SAAS,EAAE;UAAG,CAAE;UAAAvD,QAAA,eAC1EzG,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,cAAI;YAAA8D,QAAA,EAAE7E,cAAc,CAACmI;UAAW;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CACf,EAEAvD,cAAc,CAACiE,MAAM,IAAIjE,cAAc,CAACiE,MAAM,CAACC,MAAM,GAAG,CAAC,iBACxD9F,OAAA;UAAKiG,KAAK,EAAE;YAAE+D,SAAS,EAAE;UAAG,CAAE;UAAAvD,QAAA,gBAC5BzG,OAAA;YAAAyG,QAAA,EAAI;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbnF,OAAA,CAAC1B,KAAK;YAAC2L,IAAI;YAAAxD,QAAA,EACR7E,cAAc,CAACiE,MAAM,CAACtC,GAAG,CAAC,CAAC2G,KAAK,EAAEC,KAAK,kBACtCnK,OAAA,CAAChB,KAAK;cAEJ2G,KAAK,EAAE,GAAI;cACXI,MAAM,EAAE,GAAI;cACZC,GAAG,EAAEkE,KAAM;cACXjE,KAAK,EAAE;gBAAEC,SAAS,EAAE,OAAO;gBAAEC,YAAY,EAAE;cAAE;YAAE,GAJ1CgE,KAAK;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAEAvD,cAAc,CAAC8B,WAAW,iBACzB1D,OAAA,CAACf,YAAY;UAAC6F,KAAK,EAAC,0BAAM;UAAC6E,MAAM,EAAE,CAAE;UAACpC,IAAI,EAAC,OAAO;UAACtB,KAAK,EAAE;YAAE+D,SAAS,EAAE;UAAG,CAAE;UAAAvD,QAAA,gBAC1EzG,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,0BAAM;YAAA8D,QAAA,EAAE7E,cAAc,CAAC8B;UAAW;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,EAC/EvD,cAAc,CAAC+B,SAAS,iBACvB3D,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,0BAAM;YAAA8D,QAAA,EAC5B,IAAIU,IAAI,CAACvF,cAAc,CAAC+B,SAAS,CAAC,CAACyD,cAAc,CAAC;UAAC;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACpB,EACAvD,cAAc,CAACgC,WAAW,iBACzB5D,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,oBAAK;YAAA8D,QAAA,EAAE7E,cAAc,CAACgC;UAAW;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAC/E;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CACf;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRnF,OAAA,CAACtB,KAAK;MACJoG,KAAK,EAAC,0BAAM;MACZ0E,IAAI,EAAE9H,cAAe;MACrB+H,QAAQ,EAAEA,CAAA,KAAM9H,iBAAiB,CAAC,KAAK,CAAE;MACzC+H,MAAM,EAAE,IAAK;MACb/D,KAAK,EAAE,GAAI;MAAAc,QAAA,EAEV3E,eAAe,iBACd9B,OAAA;QAAAyG,QAAA,gBACEzG,OAAA,CAACf,YAAY;UAAC6F,KAAK,EAAC,0BAAM;UAAC6E,MAAM,EAAE,CAAE;UAACpC,IAAI,EAAC,OAAO;UAACtB,KAAK,EAAE;YAAEmE,YAAY,EAAE;UAAG,CAAE;UAAA3D,QAAA,gBAC7EzG,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,0BAAM;YAAA8D,QAAA,EAAE3E,eAAe,CAACgF;UAAI;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC1EnF,OAAA,CAACf,YAAY,CAACkJ,IAAI;YAACxF,KAAK,EAAC,oBAAK;YAAA8D,QAAA,EAAE3E,eAAe,CAACgI,YAAY,IAAI;UAAO;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eAEfnF,OAAA,CAACrB,IAAI;UACHuD,IAAI,EAAEA,IAAK;UACX0F,MAAM,EAAC,UAAU;UACjBC,QAAQ,EAAEzD,iBAAkB;UAC5B0D,YAAY,EAAC,KAAK;UAAArB,QAAA,gBAElBzG,OAAA,CAACrB,IAAI,CAACwJ,IAAI;YACRrB,IAAI,EAAC,QAAQ;YACbnE,KAAK,EAAC,0BAAM;YACZ0H,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE1L,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA6H,QAAA,eAEhDzG,OAAA,CAACd,KAAK,CAACqL,KAAK;cAAA9D,QAAA,gBACVzG,OAAA,CAACd,KAAK;gBAACoJ,KAAK,EAAC,UAAU;gBAAA7B,QAAA,EAAC;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCnF,OAAA,CAACd,KAAK;gBAACoJ,KAAK,EAAC,UAAU;gBAAA7B,QAAA,EAAC;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEZnF,OAAA,CAACrB,IAAI,CAACwJ,IAAI;YACRrB,IAAI,EAAC,QAAQ;YACbnE,KAAK,EAAC,0BAAM;YACZ0H,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE1L,OAAO,EAAE;YAAU,CAAC,EACtC;cAAE4L,GAAG,EAAE,GAAG;cAAE5L,OAAO,EAAE;YAAiB,CAAC,CACvC;YAAA6H,QAAA,eAEFzG,OAAA,CAACK,QAAQ;cACP+H,WAAW,EAAC,4CAAS;cACrBqC,IAAI,EAAE,CAAE;cACRC,SAAS;cACTC,SAAS,EAAE;YAAI;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAGZnF,OAAA,CAACL,WAAW;YAAC6H,IAAI,EAAC,OAAO;YAAC5I,OAAO,EAAEwD,SAAU;YAACwI,OAAO,EAAE,CAAC,CAACxI;UAAU;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtEnF,OAAA,CAACL,WAAW;YAAC6H,IAAI,EAAC,SAAS;YAAC5I,OAAO,EAAEyD,WAAY;YAACuI,OAAO,EAAE,CAAC,CAACvI;UAAY;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE5EnF,OAAA,CAACrB,IAAI,CAACwJ,IAAI;YAAA1B,QAAA,eACRzG,OAAA,CAAC1B,KAAK;cAAC2H,KAAK,EAAE;gBAAEN,KAAK,EAAE,MAAM;gBAAEa,cAAc,EAAE;cAAW,CAAE;cAAAC,QAAA,gBAC1DzG,OAAA,CAAC3B,MAAM;gBACLoJ,OAAO,EAAEA,CAAA,KAAM;kBACb9F,iBAAiB,CAAC,KAAK,CAAC;kBACxBO,IAAI,CAACiC,WAAW,CAAC,CAAC;kBAClBpC,kBAAkB,CAAC,IAAI,CAAC;kBACxBS,gBAAgB,CAAC,CAAC;gBACpB,CAAE;gBACFqI,QAAQ,EAAE7I,MAAO;gBAAAyE,QAAA,EAClB;cAED;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnF,OAAA,CAAC3B,MAAM;gBACLmJ,IAAI,EAAC,SAAS;gBACde,QAAQ,EAAC,QAAQ;gBACjBvH,OAAO,EAAEgB,MAAO;gBAChB6I,QAAQ,EAAE7I,MAAO;gBAAAyE,QAAA,EAEhBzE,MAAM,GAAG,QAAQ,GAAG;cAAM;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC3E,EAAA,CA1jBID,YAAsB;EAAA,QAaX5B,IAAI,CAACwD,OAAO,EAQvBvC,cAAc;AAAA;AAAAkL,EAAA,GArBdvK,YAAsB;AA4jB5B,eAAeA,YAAY;AAAC,IAAAuK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}