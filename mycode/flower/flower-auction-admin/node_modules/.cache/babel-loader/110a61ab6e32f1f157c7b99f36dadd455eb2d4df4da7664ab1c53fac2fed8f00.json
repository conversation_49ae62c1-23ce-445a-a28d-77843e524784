{"ast": null, "code": "import React from 'react';\nimport useToken from '../../theme/useToken';\nimport { devUseWarning } from '../warning';\nimport zIndexContext from '../zindexContext';\n// Z-Index control range\n// Container: 1000 + offset 100 (max base + 10 * offset = 2000)\n// Popover: offset 50\n// Notification: Container Max zIndex + componentOffset\nconst CONTAINER_OFFSET = 100;\nconst CONTAINER_OFFSET_MAX_COUNT = 10;\nexport const CONTAINER_MAX_OFFSET = CONTAINER_OFFSET * CONTAINER_OFFSET_MAX_COUNT;\n/**\n * Static function will default be the `CONTAINER_MAX_OFFSET`.\n * But it still may have children component like Select, Dropdown.\n * So the warning zIndex should exceed the `CONTAINER_MAX_OFFSET`.\n */\nconst CONTAINER_MAX_OFFSET_WITH_CHILDREN = CONTAINER_MAX_OFFSET + CONTAINER_OFFSET;\nexport const containerBaseZIndexOffset = {\n  Modal: CONTAINER_OFFSET,\n  Drawer: CONTAINER_OFFSET,\n  Popover: CONTAINER_OFFSET,\n  Popconfirm: CONTAINER_OFFSET,\n  Tooltip: CONTAINER_OFFSET,\n  Tour: CONTAINER_OFFSET,\n  FloatButton: CONTAINER_OFFSET\n};\nexport const consumerBaseZIndexOffset = {\n  SelectLike: 50,\n  Dropdown: 50,\n  DatePicker: 50,\n  Menu: 50,\n  ImagePreview: 1\n};\nfunction isContainerType(type) {\n  return type in containerBaseZIndexOffset;\n}\nexport const useZIndex = (componentType, customZIndex) => {\n  const [, token] = useToken();\n  const parentZIndex = React.useContext(zIndexContext);\n  const isContainer = isContainerType(componentType);\n  let result;\n  if (customZIndex !== undefined) {\n    result = [customZIndex, customZIndex];\n  } else {\n    let zIndex = parentZIndex !== null && parentZIndex !== void 0 ? parentZIndex : 0;\n    if (isContainer) {\n      zIndex +=\n      // Use preset token zIndex by default but not stack when has parent container\n      (parentZIndex ? 0 : token.zIndexPopupBase) +\n      // Container offset\n      containerBaseZIndexOffset[componentType];\n    } else {\n      zIndex += consumerBaseZIndexOffset[componentType];\n    }\n    result = [parentZIndex === undefined ? customZIndex : zIndex, zIndex];\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning(componentType);\n    const maxZIndex = token.zIndexPopupBase + CONTAINER_MAX_OFFSET_WITH_CHILDREN;\n    const currentZIndex = result[0] || 0;\n    process.env.NODE_ENV !== \"production\" ? warning(customZIndex !== undefined || currentZIndex <= maxZIndex, 'usage', '`zIndex` is over design token `zIndexPopupBase` too much. It may cause unexpected override.') : void 0;\n  }\n  return result;\n};", "map": {"version": 3, "names": ["React", "useToken", "devUseW<PERSON>ning", "zIndexContext", "CONTAINER_OFFSET", "CONTAINER_OFFSET_MAX_COUNT", "CONTAINER_MAX_OFFSET", "CONTAINER_MAX_OFFSET_WITH_CHILDREN", "containerBaseZIndexOffset", "Modal", "Drawer", "Popover", "Popconfirm", "<PERSON><PERSON><PERSON>", "Tour", "FloatButton", "consumerBaseZIndexOffset", "SelectLike", "Dropdown", "DatePicker", "<PERSON><PERSON>", "ImagePreview", "isContainerType", "type", "useZIndex", "componentType", "customZIndex", "token", "parentZIndex", "useContext", "<PERSON><PERSON><PERSON><PERSON>", "result", "undefined", "zIndex", "zIndexPopupBase", "process", "env", "NODE_ENV", "warning", "maxZIndex", "currentZIndex"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/_util/hooks/useZIndex.js"], "sourcesContent": ["import React from 'react';\nimport useToken from '../../theme/useToken';\nimport { devUseWarning } from '../warning';\nimport zIndexContext from '../zindexContext';\n// Z-Index control range\n// Container: 1000 + offset 100 (max base + 10 * offset = 2000)\n// Popover: offset 50\n// Notification: Container Max zIndex + componentOffset\nconst CONTAINER_OFFSET = 100;\nconst CONTAINER_OFFSET_MAX_COUNT = 10;\nexport const CONTAINER_MAX_OFFSET = CONTAINER_OFFSET * CONTAINER_OFFSET_MAX_COUNT;\n/**\n * Static function will default be the `CONTAINER_MAX_OFFSET`.\n * But it still may have children component like Select, Dropdown.\n * So the warning zIndex should exceed the `CONTAINER_MAX_OFFSET`.\n */\nconst CONTAINER_MAX_OFFSET_WITH_CHILDREN = CONTAINER_MAX_OFFSET + CONTAINER_OFFSET;\nexport const containerBaseZIndexOffset = {\n  Modal: CONTAINER_OFFSET,\n  Drawer: CONTAINER_OFFSET,\n  Popover: CONTAINER_OFFSET,\n  Popconfirm: CONTAINER_OFFSET,\n  Tooltip: CONTAINER_OFFSET,\n  Tour: CONTAINER_OFFSET,\n  FloatButton: CONTAINER_OFFSET\n};\nexport const consumerBaseZIndexOffset = {\n  SelectLike: 50,\n  Dropdown: 50,\n  DatePicker: 50,\n  Menu: 50,\n  ImagePreview: 1\n};\nfunction isContainerType(type) {\n  return type in containerBaseZIndexOffset;\n}\nexport const useZIndex = (componentType, customZIndex) => {\n  const [, token] = useToken();\n  const parentZIndex = React.useContext(zIndexContext);\n  const isContainer = isContainerType(componentType);\n  let result;\n  if (customZIndex !== undefined) {\n    result = [customZIndex, customZIndex];\n  } else {\n    let zIndex = parentZIndex !== null && parentZIndex !== void 0 ? parentZIndex : 0;\n    if (isContainer) {\n      zIndex +=\n      // Use preset token zIndex by default but not stack when has parent container\n      (parentZIndex ? 0 : token.zIndexPopupBase) +\n      // Container offset\n      containerBaseZIndexOffset[componentType];\n    } else {\n      zIndex += consumerBaseZIndexOffset[componentType];\n    }\n    result = [parentZIndex === undefined ? customZIndex : zIndex, zIndex];\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning(componentType);\n    const maxZIndex = token.zIndexPopupBase + CONTAINER_MAX_OFFSET_WITH_CHILDREN;\n    const currentZIndex = result[0] || 0;\n    process.env.NODE_ENV !== \"production\" ? warning(customZIndex !== undefined || currentZIndex <= maxZIndex, 'usage', '`zIndex` is over design token `zIndexPopupBase` too much. It may cause unexpected override.') : void 0;\n  }\n  return result;\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,aAAa,QAAQ,YAAY;AAC1C,OAAOC,aAAa,MAAM,kBAAkB;AAC5C;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,0BAA0B,GAAG,EAAE;AACrC,OAAO,MAAMC,oBAAoB,GAAGF,gBAAgB,GAAGC,0BAA0B;AACjF;AACA;AACA;AACA;AACA;AACA,MAAME,kCAAkC,GAAGD,oBAAoB,GAAGF,gBAAgB;AAClF,OAAO,MAAMI,yBAAyB,GAAG;EACvCC,KAAK,EAAEL,gBAAgB;EACvBM,MAAM,EAAEN,gBAAgB;EACxBO,OAAO,EAAEP,gBAAgB;EACzBQ,UAAU,EAAER,gBAAgB;EAC5BS,OAAO,EAAET,gBAAgB;EACzBU,IAAI,EAAEV,gBAAgB;EACtBW,WAAW,EAAEX;AACf,CAAC;AACD,OAAO,MAAMY,wBAAwB,GAAG;EACtCC,UAAU,EAAE,EAAE;EACdC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAE;AAChB,CAAC;AACD,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,OAAOA,IAAI,IAAIf,yBAAyB;AAC1C;AACA,OAAO,MAAMgB,SAAS,GAAGA,CAACC,aAAa,EAAEC,YAAY,KAAK;EACxD,MAAM,GAAGC,KAAK,CAAC,GAAG1B,QAAQ,CAAC,CAAC;EAC5B,MAAM2B,YAAY,GAAG5B,KAAK,CAAC6B,UAAU,CAAC1B,aAAa,CAAC;EACpD,MAAM2B,WAAW,GAAGR,eAAe,CAACG,aAAa,CAAC;EAClD,IAAIM,MAAM;EACV,IAAIL,YAAY,KAAKM,SAAS,EAAE;IAC9BD,MAAM,GAAG,CAACL,YAAY,EAAEA,YAAY,CAAC;EACvC,CAAC,MAAM;IACL,IAAIO,MAAM,GAAGL,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC;IAChF,IAAIE,WAAW,EAAE;MACfG,MAAM;MACN;MACA,CAACL,YAAY,GAAG,CAAC,GAAGD,KAAK,CAACO,eAAe;MACzC;MACA1B,yBAAyB,CAACiB,aAAa,CAAC;IAC1C,CAAC,MAAM;MACLQ,MAAM,IAAIjB,wBAAwB,CAACS,aAAa,CAAC;IACnD;IACAM,MAAM,GAAG,CAACH,YAAY,KAAKI,SAAS,GAAGN,YAAY,GAAGO,MAAM,EAAEA,MAAM,CAAC;EACvE;EACA,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGpC,aAAa,CAACuB,aAAa,CAAC;IAC5C,MAAMc,SAAS,GAAGZ,KAAK,CAACO,eAAe,GAAG3B,kCAAkC;IAC5E,MAAMiC,aAAa,GAAGT,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IACpCI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAACZ,YAAY,KAAKM,SAAS,IAAIQ,aAAa,IAAID,SAAS,EAAE,OAAO,EAAE,6FAA6F,CAAC,GAAG,KAAK,CAAC;EAC5N;EACA,OAAOR,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}