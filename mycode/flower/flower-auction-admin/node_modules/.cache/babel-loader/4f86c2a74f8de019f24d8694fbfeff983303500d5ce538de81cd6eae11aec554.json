{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Layout, Dropdown, Avatar, Badge, Space } from 'antd';\nimport { UserOutlined, BellOutlined, LogoutOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header\n} = Layout;\nconst HeaderComponent = ({\n  collapsed,\n  toggle\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const handleLogout = () => {\n    logout();\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    label: '个人中心'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    label: '账号设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: handleLogout\n  }];\n  const notificationMenuItems = [{\n    key: 'notification1',\n    label: '系统通知：新版本已发布'\n  }, {\n    key: 'notification2',\n    label: '业务通知：有新的拍卖会已创建'\n  }, {\n    key: 'notification3',\n    label: '提醒：今日有3个订单待处理'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'all',\n    label: '查看全部通知'\n  }];\n  return /*#__PURE__*/_jsxDEV(Header, {\n    className: \"site-header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-left\",\n      children: /*#__PURE__*/React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n        className: 'trigger',\n        onClick: toggle\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-right\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items: notificationMenuItems\n          },\n          placement: \"bottomRight\",\n          children: /*#__PURE__*/_jsxDEV(Badge, {\n            count: 5,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(BellOutlined, {\n              className: \"header-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items: userMenuItems\n          },\n          placement: \"bottomRight\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"username\",\n              children: (user === null || user === void 0 ? void 0 : user.username) || '用户'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(HeaderComponent, \"SlSPRKmTohGnoLiiApupaRii2Oc=\", false, function () {\n  return [useAuth];\n});\n_c = HeaderComponent;\nexport default HeaderComponent;\nvar _c;\n$RefreshReg$(_c, \"HeaderComponent\");", "map": {"version": 3, "names": ["React", "Layout", "Dropdown", "Avatar", "Badge", "Space", "UserOutlined", "BellOutlined", "LogoutOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "useAuth", "jsxDEV", "_jsxDEV", "Header", "HeaderComponent", "collapsed", "toggle", "_s", "user", "logout", "handleLogout", "userMenuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "type", "onClick", "notificationMenuItems", "className", "children", "createElement", "size", "menu", "items", "placement", "count", "username", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/Header/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Layout, Dropdown, Avatar, Badge, Space, Modal, message } from 'antd';\nimport {\n  UserOutlined,\n  BellOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  LockOutlined,\n} from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\n\nconst { Header } = Layout;\n\ninterface HeaderProps {\n  collapsed: boolean;\n  toggle: () => void;\n}\n\nconst HeaderComponent: React.FC<HeaderProps> = ({ collapsed, toggle }) => {\n  const { user, logout } = useAuth();\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人中心',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '账号设置',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  const notificationMenuItems = [\n    {\n      key: 'notification1',\n      label: '系统通知：新版本已发布',\n    },\n    {\n      key: 'notification2',\n      label: '业务通知：有新的拍卖会已创建',\n    },\n    {\n      key: 'notification3',\n      label: '提醒：今日有3个订单待处理',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'all',\n      label: '查看全部通知',\n    },\n  ];\n\n  return (\n    <Header className=\"site-header\">\n      <div className=\"header-left\">\n        {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {\n          className: 'trigger',\n          onClick: toggle,\n        })}\n      </div>\n      <div className=\"header-right\">\n        <Space size=\"large\">\n          <Dropdown menu={{ items: notificationMenuItems }} placement=\"bottomRight\">\n            <Badge count={5} size=\"small\">\n              <BellOutlined className=\"header-icon\" />\n            </Badge>\n          </Dropdown>\n          <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n            <Space>\n              <Avatar icon={<UserOutlined />} />\n              <span className=\"username\">{user?.username || '用户'}</span>\n            </Space>\n          </Dropdown>\n        </Space>\n      </div>\n    </Header>\n  );\n};\n\nexport default HeaderComponent;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAwB,MAAM;AAC7E,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,QAEb,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAO,CAAC,GAAGd,MAAM;AAOzB,MAAMe,eAAsC,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EAElC,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBD,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAME,aAAa,GAAG,CACpB;IACEC,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEX,OAAA,CAACR,YAAY;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEX,OAAA,CAACL,eAAe;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEX,OAAA,CAACN,cAAc;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbE,OAAO,EAAEV;EACX,CAAC,CACF;EAED,MAAMW,qBAAqB,GAAG,CAC5B;IACET,GAAG,EAAE,eAAe;IACpBM,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,eAAe;IACpBM,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,eAAe;IACpBM,KAAK,EAAE;EACT,CAAC,EACD;IACEC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,KAAK;IACVM,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEhB,OAAA,CAACC,MAAM;IAACmB,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC7BrB,OAAA;MAAKoB,SAAS,EAAC,aAAa;MAAAC,QAAA,eACzBnC,KAAK,CAACoC,aAAa,CAACnB,SAAS,GAAGN,kBAAkB,GAAGD,gBAAgB,EAAE;QACtEwB,SAAS,EAAE,SAAS;QACpBF,OAAO,EAAEd;MACX,CAAC;IAAC;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNf,OAAA;MAAKoB,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BrB,OAAA,CAACT,KAAK;QAACgC,IAAI,EAAC,OAAO;QAAAF,QAAA,gBACjBrB,OAAA,CAACZ,QAAQ;UAACoC,IAAI,EAAE;YAAEC,KAAK,EAAEN;UAAsB,CAAE;UAACO,SAAS,EAAC,aAAa;UAAAL,QAAA,eACvErB,OAAA,CAACV,KAAK;YAACqC,KAAK,EAAE,CAAE;YAACJ,IAAI,EAAC,OAAO;YAAAF,QAAA,eAC3BrB,OAAA,CAACP,YAAY;cAAC2B,SAAS,EAAC;YAAa;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACXf,OAAA,CAACZ,QAAQ;UAACoC,IAAI,EAAE;YAAEC,KAAK,EAAEhB;UAAc,CAAE;UAACiB,SAAS,EAAC,aAAa;UAAAL,QAAA,eAC/DrB,OAAA,CAACT,KAAK;YAAA8B,QAAA,gBACJrB,OAAA,CAACX,MAAM;cAACsB,IAAI,eAAEX,OAAA,CAACR,YAAY;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClCf,OAAA;cAAMoB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAE,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,QAAQ,KAAI;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACV,EAAA,CA5EIH,eAAsC;EAAA,QACjBJ,OAAO;AAAA;AAAA+B,EAAA,GAD5B3B,eAAsC;AA8E5C,eAAeA,eAAe;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}