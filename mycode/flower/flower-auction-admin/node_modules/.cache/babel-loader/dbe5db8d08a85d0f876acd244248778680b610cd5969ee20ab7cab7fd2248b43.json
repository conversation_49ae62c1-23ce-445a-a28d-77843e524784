{"ast": null, "code": "import axios from'axios';import{message}from'antd';// 防止重复刷新token的标志\nlet isRefreshing=false;let failedQueue=[];// 创建axios实例\nexport const apiClient=axios.create({baseURL:process.env.REACT_APP_API_BASE_URL||'http://localhost:8081/api/v1',timeout:parseInt(process.env.REACT_APP_REQUEST_TIMEOUT||'10000'),headers:{'Content-Type':'application/json'}});// 请求拦截器\napiClient.interceptors.request.use(config=>{// 添加认证token\nconst token=localStorage.getItem('token');if(token&&config.headers){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>{return Promise.reject(error);});// 处理失败队列\nconst processQueue=function(error){let token=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;failedQueue.forEach(_ref=>{let{resolve,reject}=_ref;if(error){reject(error);}else{resolve(token);}});failedQueue=[];};// 响应拦截器\napiClient.interceptors.response.use(response=>{return response;},async error=>{var _response$data;const originalRequest=error.config;const{response}=error;if(response){switch(response.status){case 401:// token过期或无效\nif(originalRequest._retry){// 已经重试过，直接跳转登录页\nlocalStorage.removeItem('token');localStorage.removeItem('refreshToken');if(window.location.pathname!=='/login'){window.location.href='/login';message.error('登录已过期，请重新登录');}return Promise.reject(error);}if(isRefreshing){// 正在刷新token，将请求加入队列\nreturn new Promise((resolve,reject)=>{failedQueue.push({resolve,reject});}).then(token=>{originalRequest.headers.Authorization=\"Bearer \".concat(token);return apiClient(originalRequest);}).catch(err=>{return Promise.reject(err);});}originalRequest._retry=true;isRefreshing=true;const refreshToken=localStorage.getItem('refreshToken');if(refreshToken){try{// 尝试刷新token\nconst refreshResponse=await axios.post(\"\".concat(process.env.REACT_APP_API_BASE_URL||'http://localhost:8081/api/v1',\"/auth/refresh\"),{refreshToken});if(refreshResponse.data.success){const newToken=refreshResponse.data.data.token;const newRefreshToken=refreshResponse.data.data.refreshToken;// 更新token\nlocalStorage.setItem('token',newToken);localStorage.setItem('refreshToken',newRefreshToken);// 处理队列中的请求\nprocessQueue(null,newToken);isRefreshing=false;// 重新发送原请求\noriginalRequest.headers.Authorization=\"Bearer \".concat(newToken);return apiClient(originalRequest);}}catch(refreshError){console.error('Token refresh failed:',refreshError);processQueue(refreshError,null);}}// 刷新失败，清除认证信息并跳转到登录页\nisRefreshing=false;localStorage.removeItem('token');localStorage.removeItem('refreshToken');// 只有当前不在登录页时才跳转，避免无限重定向\nif(window.location.pathname!=='/login'){window.location.href='/login';message.error('登录已过期，请重新登录');}break;case 403:message.error('没有权限访问该资源');break;case 404:message.error('请求的资源不存在');break;case 500:message.error('服务器内部错误');break;default:message.error(((_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.message)||'请求失败');}}else{// 网络错误\nmessage.error('网络连接失败，请检查网络设置');}return Promise.reject(error);});export default apiClient;", "map": {"version": 3, "names": ["axios", "message", "isRefreshing", "failedQueue", "apiClient", "create", "baseURL", "process", "env", "REACT_APP_API_BASE_URL", "timeout", "parseInt", "REACT_APP_REQUEST_TIMEOUT", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "concat", "error", "Promise", "reject", "processQueue", "arguments", "length", "undefined", "for<PERSON>ach", "_ref", "resolve", "response", "_response$data", "originalRequest", "status", "_retry", "removeItem", "window", "location", "pathname", "href", "push", "then", "catch", "err", "refreshToken", "refreshResponse", "post", "data", "success", "newToken", "newRefreshToken", "setItem", "refreshError", "console"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/apiClient.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';\nimport { message } from 'antd';\n\n// 防止重复刷新token的标志\nlet isRefreshing = false;\nlet failedQueue: Array<{\n  resolve: (value?: any) => void;\n  reject: (reason?: any) => void;\n}> = [];\n\n// 创建axios实例\nexport const apiClient: AxiosInstance = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1',\n  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napiClient.interceptors.request.use(\n  (config: InternalAxiosRequestConfig) => {\n    // 添加认证token\n    const token = localStorage.getItem('token');\n    if (token && config.headers) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 处理失败队列\nconst processQueue = (error: any, token: string | null = null) => {\n  failedQueue.forEach(({ resolve, reject }) => {\n    if (error) {\n      reject(error);\n    } else {\n      resolve(token);\n    }\n  });\n\n  failedQueue = [];\n};\n\n// 响应拦截器\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response;\n  },\n  async (error) => {\n    const originalRequest = error.config;\n    const { response } = error;\n\n    if (response) {\n      switch (response.status) {\n        case 401:\n          // token过期或无效\n          if (originalRequest._retry) {\n            // 已经重试过，直接跳转登录页\n            localStorage.removeItem('token');\n            localStorage.removeItem('refreshToken');\n            if (window.location.pathname !== '/login') {\n              window.location.href = '/login';\n              message.error('登录已过期，请重新登录');\n            }\n            return Promise.reject(error);\n          }\n\n          if (isRefreshing) {\n            // 正在刷新token，将请求加入队列\n            return new Promise((resolve, reject) => {\n              failedQueue.push({ resolve, reject });\n            }).then(token => {\n              originalRequest.headers.Authorization = `Bearer ${token}`;\n              return apiClient(originalRequest);\n            }).catch(err => {\n              return Promise.reject(err);\n            });\n          }\n\n          originalRequest._retry = true;\n          isRefreshing = true;\n\n          const refreshToken = localStorage.getItem('refreshToken');\n          if (refreshToken) {\n            try {\n              // 尝试刷新token\n              const refreshResponse = await axios.post(\n                `${process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1'}/auth/refresh`,\n                { refreshToken }\n              );\n\n              if (refreshResponse.data.success) {\n                const newToken = refreshResponse.data.data.token;\n                const newRefreshToken = refreshResponse.data.data.refreshToken;\n\n                // 更新token\n                localStorage.setItem('token', newToken);\n                localStorage.setItem('refreshToken', newRefreshToken);\n\n                // 处理队列中的请求\n                processQueue(null, newToken);\n                isRefreshing = false;\n\n                // 重新发送原请求\n                originalRequest.headers.Authorization = `Bearer ${newToken}`;\n                return apiClient(originalRequest);\n              }\n            } catch (refreshError) {\n              console.error('Token refresh failed:', refreshError);\n              processQueue(refreshError, null);\n            }\n          }\n\n          // 刷新失败，清除认证信息并跳转到登录页\n          isRefreshing = false;\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n\n          // 只有当前不在登录页时才跳转，避免无限重定向\n          if (window.location.pathname !== '/login') {\n            window.location.href = '/login';\n            message.error('登录已过期，请重新登录');\n          }\n          break;\n\n        case 403:\n          message.error('没有权限访问该资源');\n          break;\n\n        case 404:\n          message.error('请求的资源不存在');\n          break;\n\n        case 500:\n          message.error('服务器内部错误');\n          break;\n\n        default:\n          message.error(response.data?.message || '请求失败');\n      }\n    } else {\n      // 网络错误\n      message.error('网络连接失败，请检查网络设置');\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport default apiClient;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAoE,OAAO,CACvF,OAASC,OAAO,KAAQ,MAAM,CAE9B;AACA,GAAI,CAAAC,YAAY,CAAG,KAAK,CACxB,GAAI,CAAAC,WAGF,CAAG,EAAE,CAEP;AACA,MAAO,MAAM,CAAAC,SAAwB,CAAGJ,KAAK,CAACK,MAAM,CAAC,CACnDC,OAAO,CAAEC,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAI,8BAA8B,CAC7EC,OAAO,CAAEC,QAAQ,CAACJ,OAAO,CAACC,GAAG,CAACI,yBAAyB,EAAI,OAAO,CAAC,CACnEC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAT,SAAS,CAACU,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAkC,EAAK,CACtC;AACA,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,EAAID,MAAM,CAACJ,OAAO,CAAE,CAC3BI,MAAM,CAACJ,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAM,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,YAAY,CAAG,QAAAA,CAACH,KAAU,CAAkC,IAAhC,CAAAL,KAAoB,CAAAS,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC3DxB,WAAW,CAAC2B,OAAO,CAACC,IAAA,EAAyB,IAAxB,CAAEC,OAAO,CAAEP,MAAO,CAAC,CAAAM,IAAA,CACtC,GAAIR,KAAK,CAAE,CACTE,MAAM,CAACF,KAAK,CAAC,CACf,CAAC,IAAM,CACLS,OAAO,CAACd,KAAK,CAAC,CAChB,CACF,CAAC,CAAC,CAEFf,WAAW,CAAG,EAAE,CAClB,CAAC,CAED;AACAC,SAAS,CAACU,YAAY,CAACmB,QAAQ,CAACjB,GAAG,CAChCiB,QAAuB,EAAK,CAC3B,MAAO,CAAAA,QAAQ,CACjB,CAAC,CACD,KAAO,CAAAV,KAAK,EAAK,KAAAW,cAAA,CACf,KAAM,CAAAC,eAAe,CAAGZ,KAAK,CAACN,MAAM,CACpC,KAAM,CAAEgB,QAAS,CAAC,CAAGV,KAAK,CAE1B,GAAIU,QAAQ,CAAE,CACZ,OAAQA,QAAQ,CAACG,MAAM,EACrB,IAAK,IAAG,CACN;AACA,GAAID,eAAe,CAACE,MAAM,CAAE,CAC1B;AACAlB,YAAY,CAACmB,UAAU,CAAC,OAAO,CAAC,CAChCnB,YAAY,CAACmB,UAAU,CAAC,cAAc,CAAC,CACvC,GAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,QAAQ,CAAE,CACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAG,QAAQ,CAC/BzC,OAAO,CAACsB,KAAK,CAAC,aAAa,CAAC,CAC9B,CACA,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CAEA,GAAIrB,YAAY,CAAE,CAChB;AACA,MAAO,IAAI,CAAAsB,OAAO,CAAC,CAACQ,OAAO,CAAEP,MAAM,GAAK,CACtCtB,WAAW,CAACwC,IAAI,CAAC,CAAEX,OAAO,CAAEP,MAAO,CAAC,CAAC,CACvC,CAAC,CAAC,CAACmB,IAAI,CAAC1B,KAAK,EAAI,CACfiB,eAAe,CAACtB,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CACzD,MAAO,CAAAd,SAAS,CAAC+B,eAAe,CAAC,CACnC,CAAC,CAAC,CAACU,KAAK,CAACC,GAAG,EAAI,CACd,MAAO,CAAAtB,OAAO,CAACC,MAAM,CAACqB,GAAG,CAAC,CAC5B,CAAC,CAAC,CACJ,CAEAX,eAAe,CAACE,MAAM,CAAG,IAAI,CAC7BnC,YAAY,CAAG,IAAI,CAEnB,KAAM,CAAA6C,YAAY,CAAG5B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACzD,GAAI2B,YAAY,CAAE,CAChB,GAAI,CACF;AACA,KAAM,CAAAC,eAAe,CAAG,KAAM,CAAAhD,KAAK,CAACiD,IAAI,IAAA3B,MAAA,CACnCf,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAI,8BAA8B,kBACvE,CAAEsC,YAAa,CACjB,CAAC,CAED,GAAIC,eAAe,CAACE,IAAI,CAACC,OAAO,CAAE,CAChC,KAAM,CAAAC,QAAQ,CAAGJ,eAAe,CAACE,IAAI,CAACA,IAAI,CAAChC,KAAK,CAChD,KAAM,CAAAmC,eAAe,CAAGL,eAAe,CAACE,IAAI,CAACA,IAAI,CAACH,YAAY,CAE9D;AACA5B,YAAY,CAACmC,OAAO,CAAC,OAAO,CAAEF,QAAQ,CAAC,CACvCjC,YAAY,CAACmC,OAAO,CAAC,cAAc,CAAED,eAAe,CAAC,CAErD;AACA3B,YAAY,CAAC,IAAI,CAAE0B,QAAQ,CAAC,CAC5BlD,YAAY,CAAG,KAAK,CAEpB;AACAiC,eAAe,CAACtB,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAa8B,QAAQ,CAAE,CAC5D,MAAO,CAAAhD,SAAS,CAAC+B,eAAe,CAAC,CACnC,CACF,CAAE,MAAOoB,YAAY,CAAE,CACrBC,OAAO,CAACjC,KAAK,CAAC,uBAAuB,CAAEgC,YAAY,CAAC,CACpD7B,YAAY,CAAC6B,YAAY,CAAE,IAAI,CAAC,CAClC,CACF,CAEA;AACArD,YAAY,CAAG,KAAK,CACpBiB,YAAY,CAACmB,UAAU,CAAC,OAAO,CAAC,CAChCnB,YAAY,CAACmB,UAAU,CAAC,cAAc,CAAC,CAEvC;AACA,GAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,QAAQ,CAAE,CACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAG,QAAQ,CAC/BzC,OAAO,CAACsB,KAAK,CAAC,aAAa,CAAC,CAC9B,CACA,MAEF,IAAK,IAAG,CACNtB,OAAO,CAACsB,KAAK,CAAC,WAAW,CAAC,CAC1B,MAEF,IAAK,IAAG,CACNtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAC,CACzB,MAEF,IAAK,IAAG,CACNtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAC,CACxB,MAEF,QACEtB,OAAO,CAACsB,KAAK,CAAC,EAAAW,cAAA,CAAAD,QAAQ,CAACiB,IAAI,UAAAhB,cAAA,iBAAbA,cAAA,CAAejC,OAAO,GAAI,MAAM,CAAC,CACnD,CACF,CAAC,IAAM,CACL;AACAA,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC,CACjC,CAEA,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,cAAe,CAAAnB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}