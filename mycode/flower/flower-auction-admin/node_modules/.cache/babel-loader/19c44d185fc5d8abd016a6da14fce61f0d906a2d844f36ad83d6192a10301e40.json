{"ast": null, "code": "\"use client\";\n\nimport InternalLayout, { Content, Footer, Header } from './layout';\nimport Sider, { SiderContext } from './Sider';\nconst Layout = InternalLayout;\nLayout.Header = Header;\nLayout.Footer = Footer;\nLayout.Content = Content;\nLayout.Sider = Sider;\nLayout._InternalSiderContext = SiderContext;\nexport default Layout;", "map": {"version": 3, "names": ["InternalLayout", "Content", "Footer", "Header", "<PERSON><PERSON>", "SiderContext", "Layout", "_InternalSiderContext"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/layout/index.js"], "sourcesContent": ["\"use client\";\n\nimport InternalLayout, { Content, Footer, Header } from './layout';\nimport Sider, { SiderContext } from './Sider';\nconst Layout = InternalLayout;\nLayout.Header = Header;\nLayout.Footer = Footer;\nLayout.Content = Content;\nLayout.Sider = Sider;\nLayout._InternalSiderContext = SiderContext;\nexport default Layout;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,cAAc,IAAIC,OAAO,EAAEC,MAAM,EAAEC,MAAM,QAAQ,UAAU;AAClE,OAAOC,KAAK,IAAIC,YAAY,QAAQ,SAAS;AAC7C,MAAMC,MAAM,GAAGN,cAAc;AAC7BM,MAAM,CAACH,MAAM,GAAGA,MAAM;AACtBG,MAAM,CAACJ,MAAM,GAAGA,MAAM;AACtBI,MAAM,CAACL,OAAO,GAAGA,OAAO;AACxBK,MAAM,CAACF,KAAK,GAAGA,KAAK;AACpBE,MAAM,CAACC,qBAAqB,GAAGF,YAAY;AAC3C,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}