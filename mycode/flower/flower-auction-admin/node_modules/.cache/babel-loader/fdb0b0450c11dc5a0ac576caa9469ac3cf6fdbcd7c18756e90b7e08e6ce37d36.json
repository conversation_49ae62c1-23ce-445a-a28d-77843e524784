{"ast": null, "code": "import axios from'axios';const API_BASE_URL=process.env.REACT_APP_API_BASE_URL||'http://localhost:8081/api/v1';// 创建axios实例\nconst api=axios.create({baseURL:API_BASE_URL,timeout:10000});// 请求拦截器\napi.interceptors.request.use(config=>{const token=localStorage.getItem('token');if(token){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>{return Promise.reject(error);});// 响应拦截器\napi.interceptors.response.use(response=>{return response.data;},error=>{console.error('API Error:',error);return Promise.reject(error);});// 销售报表相关接口\nexport const salesReportAPI={// 获取销售报表\ngetSalesReport:params=>api.get('/reports/sales',{params}),// 获取销售趋势\ngetSalesTrend:params=>api.get('/reports/sales/trend',{params}),// 获取商品销售排行\ngetProductSalesRank:params=>api.get('/reports/sales/products',{params}),// 获取销售渠道分布\ngetSalesChannelDistribution:params=>api.get('/reports/sales/channels',{params})};// 用户报表相关接口\nexport const userReportAPI={// 获取用户报表\ngetUserReport:params=>api.get('/reports/users',{params}),// 获取用户增长趋势\ngetUserGrowthTrend:params=>api.get('/reports/users/growth',{params}),// 获取用户分布\ngetUserDistribution:()=>api.get('/reports/users/distribution'),// 获取用户活跃度排行\ngetUserActivityRank:params=>api.get('/reports/users/activity',{params})};// 商品报表相关接口\nexport const productReportAPI={// 获取商品报表\ngetProductReport:params=>api.get('/reports/products',{params}),// 获取分类销售数据\ngetCategorySales:params=>api.get('/reports/products/categories',{params}),// 获取商品性能数据\ngetProductPerformance:params=>api.get('/reports/products/performance',{params}),// 获取价格分布\ngetPriceDistribution:()=>api.get('/reports/products/price-distribution')};// 拍卖报表相关接口\nexport const auctionReportAPI={// 获取拍卖报表\ngetAuctionReport:params=>api.get('/reports/auctions',{params}),// 获取拍卖趋势\ngetAuctionTrend:params=>api.get('/reports/auctions/trend',{params}),// 获取拍卖性能数据\ngetAuctionPerformance:params=>api.get('/reports/auctions/performance',{params}),// 获取拍卖状态分布\ngetAuctionStatusDistribution:()=>api.get('/reports/auctions/status')};// 导出报表\nexport const exportReportAPI={exportReport:(type,params)=>{const url=\"/reports/export/\".concat(type);return api.get(url,{params,responseType:'blob'// 用于文件下载\n});}};const reportService={salesReportAPI,userReportAPI,productReportAPI,auctionReportAPI,exportReportAPI};export default reportService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "api", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "concat", "error", "Promise", "reject", "response", "data", "console", "salesReportAPI", "getSalesReport", "params", "get", "getSalesTrend", "getProductSalesRank", "getSalesChannelDistribution", "userReportAPI", "getUserReport", "getUserGrowthTrend", "getUserDistribution", "getUserActivityRank", "productReportAPI", "getProductReport", "getCategorySales", "getProductPerformance", "getPriceDistribution", "auctionReportAPI", "getAuctionReport", "getAuctionTrend", "getAuctionPerformance", "getAuctionStatusDistribution", "exportReportAPI", "exportReport", "type", "url", "responseType", "reportService"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/reportService.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    console.error('API Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// 销售报表相关接口\nexport const salesReportAPI = {\n  // 获取销售报表\n  getSalesReport: (params: {\n    start_date: string;\n    end_date: string;\n    granularity?: string;\n  }) => api.get('/reports/sales', { params }),\n\n  // 获取销售趋势\n  getSalesTrend: (params: {\n    start_date: string;\n    end_date: string;\n    granularity?: string;\n  }) => api.get('/reports/sales/trend', { params }),\n\n  // 获取商品销售排行\n  getProductSalesRank: (params: {\n    start_date: string;\n    end_date: string;\n    limit?: number;\n  }) => api.get('/reports/sales/products', { params }),\n\n  // 获取销售渠道分布\n  getSalesChannelDistribution: (params: {\n    start_date: string;\n    end_date: string;\n  }) => api.get('/reports/sales/channels', { params }),\n};\n\n// 用户报表相关接口\nexport const userReportAPI = {\n  // 获取用户报表\n  getUserReport: (params: {\n    start_date: string;\n    end_date: string;\n    user_type?: string;\n  }) => api.get('/reports/users', { params }),\n\n  // 获取用户增长趋势\n  getUserGrowthTrend: (params: {\n    start_date: string;\n    end_date: string;\n  }) => api.get('/reports/users/growth', { params }),\n\n  // 获取用户分布\n  getUserDistribution: () => api.get('/reports/users/distribution'),\n\n  // 获取用户活跃度排行\n  getUserActivityRank: (params: {\n    user_type?: string;\n    limit?: number;\n  }) => api.get('/reports/users/activity', { params }),\n};\n\n// 商品报表相关接口\nexport const productReportAPI = {\n  // 获取商品报表\n  getProductReport: (params: {\n    start_date: string;\n    end_date: string;\n    category?: string;\n  }) => api.get('/reports/products', { params }),\n\n  // 获取分类销售数据\n  getCategorySales: (params: {\n    start_date: string;\n    end_date: string;\n  }) => api.get('/reports/products/categories', { params }),\n\n  // 获取商品性能数据\n  getProductPerformance: (params: {\n    start_date: string;\n    end_date: string;\n    limit?: number;\n  }) => api.get('/reports/products/performance', { params }),\n\n  // 获取价格分布\n  getPriceDistribution: () => api.get('/reports/products/price-distribution'),\n};\n\n// 拍卖报表相关接口\nexport const auctionReportAPI = {\n  // 获取拍卖报表\n  getAuctionReport: (params: {\n    start_date: string;\n    end_date: string;\n    auction_type?: string;\n  }) => api.get('/reports/auctions', { params }),\n\n  // 获取拍卖趋势\n  getAuctionTrend: (params: {\n    start_date: string;\n    end_date: string;\n  }) => api.get('/reports/auctions/trend', { params }),\n\n  // 获取拍卖性能数据\n  getAuctionPerformance: (params: {\n    start_date: string;\n    end_date: string;\n    limit?: number;\n  }) => api.get('/reports/auctions/performance', { params }),\n\n  // 获取拍卖状态分布\n  getAuctionStatusDistribution: () => api.get('/reports/auctions/status'),\n};\n\n// 导出报表\nexport const exportReportAPI = {\n  exportReport: (type: string, params: {\n    start_date: string;\n    end_date: string;\n    format?: string;\n  }) => {\n    const url = `/reports/export/${type}`;\n    return api.get(url, { \n      params,\n      responseType: 'blob' // 用于文件下载\n    });\n  },\n};\n\nconst reportService = {\n  salesReportAPI,\n  userReportAPI,\n  productReportAPI,\n  auctionReportAPI,\n  exportReportAPI,\n};\n\nexport default reportService;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAI,8BAA8B,CAEzF;AACA,KAAM,CAAAC,GAAG,CAAGL,KAAK,CAACM,MAAM,CAAC,CACvBC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,KACX,CAAC,CAAC,CAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACI,OAAO,CAACC,aAAa,WAAAC,MAAA,CAAaL,KAAK,CAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAO,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAd,GAAG,CAACI,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,EAAK,CACZ,MAAO,CAAAA,QAAQ,CAACC,IAAI,CACtB,CAAC,CACAJ,KAAK,EAAK,CACTK,OAAO,CAACL,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAM,cAAc,CAAG,CAC5B;AACAC,cAAc,CAAGC,MAIhB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,gBAAgB,CAAE,CAAED,MAAO,CAAC,CAAC,CAE3C;AACAE,aAAa,CAAGF,MAIf,EAAKtB,GAAG,CAACuB,GAAG,CAAC,sBAAsB,CAAE,CAAED,MAAO,CAAC,CAAC,CAEjD;AACAG,mBAAmB,CAAGH,MAIrB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,yBAAyB,CAAE,CAAED,MAAO,CAAC,CAAC,CAEpD;AACAI,2BAA2B,CAAGJ,MAG7B,EAAKtB,GAAG,CAACuB,GAAG,CAAC,yBAAyB,CAAE,CAAED,MAAO,CAAC,CACrD,CAAC,CAED;AACA,MAAO,MAAM,CAAAK,aAAa,CAAG,CAC3B;AACAC,aAAa,CAAGN,MAIf,EAAKtB,GAAG,CAACuB,GAAG,CAAC,gBAAgB,CAAE,CAAED,MAAO,CAAC,CAAC,CAE3C;AACAO,kBAAkB,CAAGP,MAGpB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,uBAAuB,CAAE,CAAED,MAAO,CAAC,CAAC,CAElD;AACAQ,mBAAmB,CAAEA,CAAA,GAAM9B,GAAG,CAACuB,GAAG,CAAC,6BAA6B,CAAC,CAEjE;AACAQ,mBAAmB,CAAGT,MAGrB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,yBAAyB,CAAE,CAAED,MAAO,CAAC,CACrD,CAAC,CAED;AACA,MAAO,MAAM,CAAAU,gBAAgB,CAAG,CAC9B;AACAC,gBAAgB,CAAGX,MAIlB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,mBAAmB,CAAE,CAAED,MAAO,CAAC,CAAC,CAE9C;AACAY,gBAAgB,CAAGZ,MAGlB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,8BAA8B,CAAE,CAAED,MAAO,CAAC,CAAC,CAEzD;AACAa,qBAAqB,CAAGb,MAIvB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,+BAA+B,CAAE,CAAED,MAAO,CAAC,CAAC,CAE1D;AACAc,oBAAoB,CAAEA,CAAA,GAAMpC,GAAG,CAACuB,GAAG,CAAC,sCAAsC,CAC5E,CAAC,CAED;AACA,MAAO,MAAM,CAAAc,gBAAgB,CAAG,CAC9B;AACAC,gBAAgB,CAAGhB,MAIlB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,mBAAmB,CAAE,CAAED,MAAO,CAAC,CAAC,CAE9C;AACAiB,eAAe,CAAGjB,MAGjB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,yBAAyB,CAAE,CAAED,MAAO,CAAC,CAAC,CAEpD;AACAkB,qBAAqB,CAAGlB,MAIvB,EAAKtB,GAAG,CAACuB,GAAG,CAAC,+BAA+B,CAAE,CAAED,MAAO,CAAC,CAAC,CAE1D;AACAmB,4BAA4B,CAAEA,CAAA,GAAMzC,GAAG,CAACuB,GAAG,CAAC,0BAA0B,CACxE,CAAC,CAED;AACA,MAAO,MAAM,CAAAmB,eAAe,CAAG,CAC7BC,YAAY,CAAEA,CAACC,IAAY,CAAEtB,MAI5B,GAAK,CACJ,KAAM,CAAAuB,GAAG,oBAAAhC,MAAA,CAAsB+B,IAAI,CAAE,CACrC,MAAO,CAAA5C,GAAG,CAACuB,GAAG,CAACsB,GAAG,CAAE,CAClBvB,MAAM,CACNwB,YAAY,CAAE,MAAO;AACvB,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG,CACpB3B,cAAc,CACdO,aAAa,CACbK,gBAAgB,CAChBK,gBAAgB,CAChBK,eACF,CAAC,CAED,cAAe,CAAAK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}