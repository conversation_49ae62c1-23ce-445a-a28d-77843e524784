{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Tree, Button, Space, Input, Modal, Form, message, Typography, Row, Col, Switch, InputNumber, Descriptions, Tag, Alert } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, FolderOutlined, FileOutlined, MinusSquareOutlined, PlusSquareOutlined } from '@ant-design/icons';\nimport { categoryService } from '../../../services/categoryService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiError } from '../../../hooks/useFormMessage';\nimport './index.css';\nimport './CategoryManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  TextArea\n} = Input;\n\n// 分类数据接口\n\nconst CategoryManagement = () => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [selectedCategory, setSelectedCategory] = useState(null);\n  const [expandedKeys, setExpandedKeys] = useState([]);\n  const [form] = Form.useForm();\n  const [currentLevel, setCurrentLevel] = useState(1);\n  const [selectedParentForNew, setSelectedParentForNew] = useState(null);\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n  // 获取同级分类的下一个排序值\n  const getNextSortOrder = parentId => {\n    try {\n      var _categories$find;\n      const siblings = parentId ? ((_categories$find = categories.find(cat => cat.id === parentId)) === null || _categories$find === void 0 ? void 0 : _categories$find.children) || [] : categories;\n      if (!siblings || siblings.length === 0) return 1;\n      const sortValues = siblings.map(cat => cat.sortOrder || cat.sort || 0).filter(sort => typeof sort === 'number' && !isNaN(sort));\n      if (sortValues.length === 0) return 1;\n      const maxSort = Math.max(...sortValues);\n      return maxSort + 1;\n    } catch (error) {\n      console.error('Error in getNextSortOrder:', error);\n      return 1;\n    }\n  };\n\n  // 获取分类列表\n  const fetchCategories = async () => {\n    setLoading(true);\n    try {\n      const response = await categoryService.getCategoryTree();\n      console.log('分类树API响应:', response);\n\n      // 处理API响应：如果是直接数组，则直接使用；如果是包装格式，则使用data字段\n      let categoriesData;\n      if (Array.isArray(response)) {\n        // API直接返回数组\n        categoriesData = response;\n      } else if (response.success && response.data) {\n        // API返回包装格式\n        categoriesData = response.data;\n      } else {\n        // 处理错误情况\n        message.error(response.message || '获取分类列表失败');\n        return;\n      }\n\n      // 检查是否有子分类数据，如果没有则尝试手动构建树形结构\n      const hasChildrenData = categoriesData.some(cat => cat.children && cat.children.length > 0);\n      if (!hasChildrenData) {\n        console.log('后端未返回子分类数据，尝试手动构建树形结构');\n        // 如果后端没有返回子分类，我们手动构建树形结构\n        categoriesData = buildTreeStructure(categoriesData);\n      }\n      setCategories(categoriesData);\n      // 默认展开第一级分类（有子分类的节点）\n      const firstLevelKeysWithChildren = categoriesData.filter(cat => cat.children && cat.children.length > 0).map(cat => cat.id);\n      setExpandedKeys(firstLevelKeysWithChildren);\n      console.log('处理后的分类数据:', categoriesData);\n    } catch (error) {\n      console.error('获取分类列表失败:', error);\n      message.error(error.message || '获取分类列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 手动构建树形结构\n  const buildTreeStructure = flatCategories => {\n    const categoryMap = new Map();\n    const rootCategories = [];\n\n    // 创建分类映射\n    flatCategories.forEach(cat => {\n      categoryMap.set(cat.id, {\n        ...cat,\n        children: []\n      });\n    });\n\n    // 构建树形结构\n    flatCategories.forEach(cat => {\n      const category = categoryMap.get(cat.id);\n      if (cat.parentId === null || cat.parentId === undefined) {\n        // 根分类\n        rootCategories.push(category);\n      } else {\n        // 子分类\n        const parent = categoryMap.get(cat.parentId);\n        if (parent) {\n          if (!parent.children) {\n            parent.children = [];\n          }\n          parent.children.push(category);\n        }\n      }\n    });\n    return rootCategories;\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchCategories();\n  }, []);\n\n  // 监听categories变化，更新selectedCategory\n  useEffect(() => {\n    if (selectedCategory && categories.length > 0) {\n      const updatedCategory = findCategoryById(categories, selectedCategory.id);\n      if (updatedCategory && JSON.stringify(updatedCategory) !== JSON.stringify(selectedCategory)) {\n        setSelectedCategory(updatedCategory);\n      }\n    }\n  }, [categories]);\n\n  // 处理删除按钮点击\n  const handleDeleteClick = category => {\n    handleDeleteWithChildren(category);\n    console.log('🔥🔥🔥 [DELETE] 步骤2完成: Modal.confirm 已调用');\n  };\n\n  // 查找分类\n  const findCategoryById = (cats, id) => {\n    for (const cat of cats) {\n      if (cat.id === id) return cat;\n      if (cat.children) {\n        const found = findCategoryById(cat.children, id);\n        if (found) return found;\n      }\n    }\n    return null;\n  };\n\n  // 新增分类 - 简化流程\n  const handleAdd = parent => {\n    setEditingCategory(null);\n    form.resetFields();\n    clearAllMessages();\n    if (parent) {\n      // 从分类树节点添加子分类\n      setSelectedParentForNew(parent);\n      setCurrentLevel(parent.level + 1);\n\n      // 直接设置表单值\n      const nextSortOrder = getNextSortOrder(parent.id);\n      form.setFieldsValue({\n        parentId: parent.id,\n        level: parent.level + 1,\n        sortOrder: nextSortOrder,\n        status: true\n      });\n    } else {\n      // 添加根分类\n      setSelectedParentForNew(null);\n      setCurrentLevel(1);\n\n      // 直接设置表单值\n      const nextSortOrder = getNextSortOrder(null);\n      form.setFieldsValue({\n        parentId: null,\n        level: 1,\n        sortOrder: nextSortOrder,\n        status: true\n      });\n    }\n    setIsModalVisible(true);\n  };\n\n  // 编辑分类\n  const handleEdit = category => {\n    setEditingCategory(category);\n    setCurrentLevel(category.level);\n    form.setFieldsValue({\n      name: category.name,\n      description: category.description,\n      status: category.status === 1,\n      sortOrder: category.sortOrder || 0\n    });\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 删除分类及其所有子分类（物理删除）\n  const handleDeleteWithChildren = async category => {\n    try {\n      // 收集所有需要删除的分类ID（包括子分类）\n      const collectCategoryIds = cat => {\n        let ids = [cat.id];\n        if (cat.children && cat.children.length > 0) {\n          cat.children.forEach(child => {\n            ids = ids.concat(collectCategoryIds(child));\n          });\n        }\n        return ids;\n      };\n      const idsToDelete = collectCategoryIds(category);\n\n      // 按层级从深到浅删除（先删除子分类，再删除父分类）\n      const sortedIds = idsToDelete.sort((a, b) => {\n        const catA = findCategoryById(categories, a);\n        const catB = findCategoryById(categories, b);\n        return ((catB === null || catB === void 0 ? void 0 : catB.level) || 0) - ((catA === null || catA === void 0 ? void 0 : catA.level) || 0);\n      });\n      let deletedCount = 0;\n      for (const id of sortedIds) {\n        try {\n          const response = await categoryService.deleteCategory(id);\n\n          // 改进删除成功的判断逻辑\n          let isSuccess = false;\n          if (response) {\n            var _response$message;\n            // 如果响应包含success字段且为true（删除API的标准响应）\n            if ('success' in response && response.success === true) {\n              isSuccess = true;\n            }\n            // 检查后端标准响应格式 {code: 200, data: {...}, message: \"...\"}\n            else if ('code' in response && response.code === 200) {\n              isSuccess = true;\n            }\n            // 如果message包含\"成功\"，则认为是成功的\n            else if (response.message && response.message.includes('成功')) {\n              isSuccess = true;\n            }\n            // 如果响应是对象且没有错误信息，认为成功\n            else if (typeof response === 'object' && !response.error && !((_response$message = response.message) !== null && _response$message !== void 0 && _response$message.includes('失败'))) {\n              isSuccess = true;\n            }\n          }\n          if (isSuccess) {\n            deletedCount++;\n          }\n        } catch (error) {\n          console.error(`🔥🔥🔥 [DELETE] 删除分类 ${id} 失败:`, error);\n        }\n      }\n      if (deletedCount > 0) {\n        message.success(`成功删除 ${deletedCount} 个分类`);\n        fetchCategories();\n        // 清除选中状态\n        setSelectedCategory(null);\n      } else {\n        message.error('删除失败，请稍后重试');\n      }\n    } catch (error) {\n      console.error('🔥🔥🔥 [DELETE] handleDeleteWithChildren 异常:', error);\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存分类\n  const handleSave = async values => {\n    setSaving(true);\n    clearAllMessages();\n    try {\n      let response;\n      if (editingCategory) {\n        // 编辑分类\n        const updateData = {\n          name: values.name,\n          description: values.description,\n          status: values.status ? 1 : 0,\n          sortOrder: values.sortOrder\n        };\n        response = await categoryService.updateCategory(editingCategory.id, updateData);\n      } else {\n        // 新增分类\n        const createData = {\n          name: values.name,\n          description: values.description,\n          parentId: (selectedParentForNew === null || selectedParentForNew === void 0 ? void 0 : selectedParentForNew.id) || null,\n          level: currentLevel,\n          sortOrder: getNextSortOrder((selectedParentForNew === null || selectedParentForNew === void 0 ? void 0 : selectedParentForNew.id) || null),\n          status: values.status ? 1 : 0\n        };\n        response = await categoryService.createCategory(createData);\n      }\n      const successMsg = editingCategory ? '分类更新成功！' : '分类创建成功！';\n\n      // 处理API响应：检查是否成功\n      let isSuccess = false;\n      if (response && typeof response === 'object') {\n        // 如果直接返回分类对象且包含id字段，说明创建/更新成功（后端直接返回对象）\n        if ('id' in response && 'name' in response) {\n          isSuccess = true;\n        }\n        // 检查后端标准响应格式 {code: 200, data: {...}, message: \"...\"}\n        else if ('code' in response && response.code === 200) {\n          isSuccess = true;\n        }\n        // 特殊处理：后端bug - 返回 {success: false, message: \"分类更新成功\"}\n        else if ('success' in response && 'message' in response && response.message && response.message.includes('成功')) {\n          isSuccess = true;\n        }\n        // 检查前端service层包装的响应格式 {success: true, data: {...}}\n        else if ('success' in response) {\n          isSuccess = response.success;\n          if (!isSuccess && response.message) {\n            setFormError(response.message);\n          }\n        }\n      }\n      if (isSuccess) {\n        setFormSuccess(successMsg);\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingCategory(null);\n          setSelectedParentForNew(null);\n          clearAllMessages();\n          fetchCategories();\n        }, 1500);\n      } else {\n        setFormError('操作失败，请稍后重试');\n      }\n    } catch (error) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 转换分类数据为树形结构 - 优化版\n  const convertCategoriesToTreeData = categories => {\n    return categories.map(category => {\n      const hasChildren = category.children && category.children.length > 0;\n      const productCount = category.productCount || 0;\n      return {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"category-tree-node\",\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            width: '100%',\n            paddingRight: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"category-info\",\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-name\",\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"category-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"category-code\",\n                children: [\"(\", category.code, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this), productCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"product-count\",\n                children: [productCount, \"\\u4E2A\\u5546\\u54C1\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), hasChildren && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"children-count\",\n                children: [category.children.length, \"\\u4E2A\\u5B50\\u5206\\u7C7B\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this), category.status === 0 && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"volcano\",\n                style: {\n                  fontSize: '11px'\n                },\n                children: \"\\u5DF2\\u7981\\u7528\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"category-actions\",\n            style: {\n              display: 'flex',\n              gap: '4px',\n              opacity: 1,\n              transition: 'all 0.2s',\n              backgroundColor: 'rgba(255, 255, 255, 0.9)',\n              borderRadius: '4px',\n              padding: '2px',\n              boxShadow: '0 1px 4px rgba(0,0,0,0.1)'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 1)';\n              e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';\n              e.currentTarget.style.boxShadow = '0 1px 4px rgba(0,0,0,0.1)';\n            },\n            onClick: e => e.stopPropagation(),\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 23\n              }, this),\n              onClick: e => {\n                e.stopPropagation();\n                handleEdit(category);\n              },\n              style: {\n                padding: '2px 4px',\n                height: '22px',\n                minWidth: '22px',\n                color: '#1890ff',\n                border: 'none'\n              },\n              title: \"\\u7F16\\u8F91\\u5206\\u7C7B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), category.level < 3 && /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 25\n              }, this),\n              onClick: e => {\n                e.stopPropagation();\n                handleAdd(category);\n              },\n              style: {\n                padding: '2px 4px',\n                height: '22px',\n                minWidth: '22px',\n                color: '#52c41a',\n                border: 'none'\n              },\n              title: \"\\u6DFB\\u52A0\\u5B50\\u5206\\u7C7B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 23\n              }, this),\n              onClick: e => {\n                e.stopPropagation();\n                handleDeleteClick(category);\n              },\n              style: {\n                padding: '2px 4px',\n                height: '22px',\n                minWidth: '22px',\n                color: '#ff4d4f',\n                border: 'none',\n                display: 'inline-flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              title: \"\\u5220\\u9664\\u5206\\u7C7B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this),\n        key: category.id,\n        icon: hasChildren ? /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(FileOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 50\n        }, this),\n        children: hasChildren ? convertCategoriesToTreeData(category.children) : undefined,\n        isLeaf: !hasChildren\n      };\n    });\n  };\n\n  // 树节点选择处理\n  const handleTreeSelect = selectedKeys => {\n    if (selectedKeys.length > 0) {\n      const category = findCategoryById(categories, selectedKeys[0]);\n      setSelectedCategory(category);\n    } else {\n      setSelectedCategory(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"category-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u5206\\u7C7B\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5206\\u7C7B\\u6811\",\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 25\n              }, this),\n              onClick: () => handleAdd(),\n              children: \"\\u6DFB\\u52A0\\u6839\\u5206\\u7C7B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 25\n              }, this),\n              onClick: fetchCategories,\n              loading: loading,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Tree, {\n            showIcon: true,\n            showLine: false,\n            expandedKeys: expandedKeys,\n            onExpand: setExpandedKeys,\n            onSelect: handleTreeSelect,\n            selectedKeys: selectedCategory ? [selectedCategory.id] : [],\n            treeData: convertCategoriesToTreeData(categories),\n            height: 600,\n            blockNode: true,\n            switcherIcon: ({\n              expanded,\n              isLeaf\n            }) => {\n              if (isLeaf) return null;\n              return expanded ? /*#__PURE__*/_jsxDEV(MinusSquareOutlined, {\n                style: {\n                  color: '#1890ff',\n                  fontSize: '14px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(PlusSquareOutlined, {\n                style: {\n                  color: '#1890ff',\n                  fontSize: '14px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 19\n              }, this);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5206\\u7C7B\\u8BE6\\u60C5\",\n          children: selectedCategory ? /*#__PURE__*/_jsxDEV(Descriptions, {\n            column: 1,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5206\\u7C7B\\u540D\\u79F0\",\n              children: selectedCategory.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5206\\u7C7B\\u7F16\\u7801\",\n              children: selectedCategory.code\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5206\\u7C7B\\u63CF\\u8FF0\",\n              children: selectedCategory.description || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5206\\u7C7B\\u7EA7\\u522B\",\n              children: [\"\\u7B2C\", selectedCategory.level, \"\\u7EA7\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6392\\u5E8F\",\n              children: selectedCategory.sortOrder || selectedCategory.sort || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: selectedCategory.status === 1 ? 'green' : 'red',\n                children: selectedCategory.status === 1 ? '启用' : '禁用'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5546\\u54C1\\u6570\\u91CF\",\n              children: [selectedCategory.productCount, \"\\u4E2A\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n              children: new Date(selectedCategory.createdAt).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n              children: new Date(selectedCategory.updatedAt).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              color: '#999',\n              padding: '40px 0'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u4E00\\u4E2A\\u5206\\u7C7B\\u67E5\\u770B\\u8BE6\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingCategory ? '编辑分类' : '新增分类',\n      open: isModalVisible,\n      onCancel: () => {\n        setIsModalVisible(false);\n        form.resetFields();\n        setEditingCategory(null);\n        clearAllMessages();\n      },\n      footer: null,\n      width: 600,\n      destroyOnClose: true,\n      children: [!editingCategory && /*#__PURE__*/_jsxDEV(Alert, {\n        message: selectedParentForNew ? `在 \"${selectedParentForNew.name}\" 下创建 ${currentLevel} 级分类` : \"创建一级分类\",\n        type: \"info\",\n        style: {\n          marginBottom: 16\n        },\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u5206\\u7C7B\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入分类名称'\n          }, {\n            max: 50,\n            message: '分类名称不能超过50个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5206\\u7C7B\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u5206\\u7C7B\\u63CF\\u8FF0\",\n          rules: [{\n            max: 200,\n            message: '分类描述不能超过200个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5206\\u7C7B\\u63CF\\u8FF0\",\n            rows: 4,\n            showCount: true,\n            maxLength: 200\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"sortOrder\",\n              label: \"\\u6392\\u5E8F\",\n              tooltip: \"\\u6570\\u503C\\u8D8A\\u5C0F\\u6392\\u5E8F\\u8D8A\\u9760\\u524D\\uFF0C\\u7F16\\u8F91\\u65F6\\u53EF\\u4EE5\\u8C03\\u6574\",\n              children: editingCategory ? /*#__PURE__*/_jsxDEV(InputNumber, {\n                placeholder: \"\\u6392\\u5E8F\\u503C\",\n                min: 1,\n                max: 9999,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '4px 11px',\n                  border: '1px solid #d9d9d9',\n                  borderRadius: '6px',\n                  backgroundColor: '#f5f5f5',\n                  color: '#666'\n                },\n                children: [\"\\u7CFB\\u7EDF\\u81EA\\u52A8\\u5206\\u914D: \", getNextSortOrder((selectedParentForNew === null || selectedParentForNew === void 0 ? void 0 : selectedParentForNew.id) || null)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              valuePropName: \"checked\",\n              initialValue: true,\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checkedChildren: \"\\u542F\\u7528\",\n                unCheckedChildren: \"\\u7981\\u7528\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"parentId\",\n          hidden: true,\n          children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"level\",\n          hidden: true,\n          children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 771,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormMessage, {\n          type: \"error\",\n          message: formError,\n          visible: !!formError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 776,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormMessage, {\n          type: \"success\",\n          message: formSuccess,\n          visible: !!formSuccess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setIsModalVisible(false);\n                form.resetFields();\n                setEditingCategory(null);\n                clearAllMessages();\n              },\n              disabled: saving,\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: saving,\n              disabled: saving,\n              children: saving ? '保存中...' : editingCategory ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 572,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"ui7v1Amp5mqkzb8Es/LoqxAxGCg=\", false, function () {\n  return [Form.useForm, useFormMessage];\n});\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Tree", "<PERSON><PERSON>", "Space", "Input", "Modal", "Form", "message", "Typography", "Row", "Col", "Switch", "InputNumber", "Descriptions", "Tag", "<PERSON><PERSON>", "PlusOutlined", "EditOutlined", "DeleteOutlined", "ReloadOutlined", "FolderOutlined", "FileOutlined", "MinusSquareOutlined", "PlusSquareOutlined", "categoryService", "FormMessage", "useFormMessage", "handleApiError", "jsxDEV", "_jsxDEV", "Title", "TextArea", "CategoryManagement", "_s", "categories", "setCategories", "loading", "setLoading", "saving", "setSaving", "isModalVisible", "setIsModalVisible", "editingCategory", "setEditingCategory", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "expandedKeys", "setExpandedKeys", "form", "useForm", "currentLevel", "setCurrentLevel", "selectedParentForNew", "setSelectedParentForNew", "formError", "formSuccess", "setFormError", "setFormSuccess", "clearAllMessages", "getNextSortOrder", "parentId", "_categories$find", "siblings", "find", "cat", "id", "children", "length", "sortValues", "map", "sortOrder", "sort", "filter", "isNaN", "maxSort", "Math", "max", "error", "console", "fetchCategories", "response", "getCategoryTree", "log", "categoriesData", "Array", "isArray", "success", "data", "hasChildrenData", "some", "buildTreeStructure", "firstLevelKeysWithChildren", "flatCategories", "categoryMap", "Map", "rootCategories", "for<PERSON>ach", "set", "category", "get", "undefined", "push", "parent", "updatedCategory", "findCategoryById", "JSON", "stringify", "handleDeleteClick", "handleDeleteWithChildren", "cats", "found", "handleAdd", "resetFields", "level", "nextSortOrder", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status", "handleEdit", "name", "description", "collectCategoryIds", "ids", "child", "concat", "idsToDelete", "sortedIds", "a", "b", "catA", "catB", "deletedCount", "deleteCategory", "isSuccess", "_response$message", "code", "includes", "handleSave", "values", "updateData", "updateCategory", "createData", "createCategory", "successMsg", "setTimeout", "convertCategoriesToTreeData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productCount", "title", "className", "style", "display", "alignItems", "justifyContent", "width", "paddingRight", "flex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "gap", "opacity", "transition", "backgroundColor", "borderRadius", "padding", "boxShadow", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "onClick", "stopPropagation", "type", "size", "icon", "height", "min<PERSON><PERSON><PERSON>", "border", "key", "<PERSON><PERSON><PERSON><PERSON>", "handleTreeSelect", "<PERSON><PERSON><PERSON><PERSON>", "gutter", "xs", "lg", "extra", "showIcon", "showLine", "onExpand", "onSelect", "treeData", "blockNode", "switcherIcon", "expanded", "column", "<PERSON><PERSON>", "label", "Date", "createdAt", "toLocaleString", "updatedAt", "textAlign", "open", "onCancel", "footer", "destroyOnClose", "marginBottom", "layout", "onFinish", "autoComplete", "rules", "required", "placeholder", "rows", "showCount", "max<PERSON><PERSON><PERSON>", "span", "tooltip", "min", "valuePropName", "initialValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "hidden", "visible", "disabled", "htmlType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Tree,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Typography,\n  Row,\n  Col,\n  Switch,\n  InputNumber,\n  Descriptions,\n  Tag,\n  Alert,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  FolderOutlined,\n  FileOutlined,\n  MinusSquareOutlined,\n  PlusSquareOutlined,\n} from '@ant-design/icons';\nimport type { DataNode, EventDataNode } from 'antd/es/tree';\nimport { categoryService } from '../../../services/categoryService';\nimport FormMessage from '../../../components/FormMessage';\nimport { useFormMessage, handleApiError } from '../../../hooks/useFormMessage';\nimport './index.css';\nimport './CategoryManagement.css';\n\nconst { Title } = Typography;\nconst { TextArea } = Input;\n\n\n\n// 分类数据接口\nexport interface Category {\n  id: number;\n  name: string;\n  code: string;\n  description?: string;\n  parentId?: number;\n  level: number;\n  sort: number;\n  sortOrder?: number; // 添加sortOrder字段\n  status: number;\n  productCount: number;\n  children?: Category[];\n  createdAt: string;\n  updatedAt: string;\n}\n\nconst CategoryManagement: React.FC = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);\n  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);\n  const [form] = Form.useForm();\n  const [currentLevel, setCurrentLevel] = useState<number>(1);\n  const [selectedParentForNew, setSelectedParentForNew] = useState<Category | null>(null);\n\n\n  const {\n    formError,\n    formSuccess,\n    setFormError,\n    setFormSuccess,\n    clearAllMessages\n  } = useFormMessage();\n\n\n\n\n\n  // 获取同级分类的下一个排序值\n  const getNextSortOrder = (parentId: number | null): number => {\n    try {\n      const siblings = parentId\n        ? categories.find(cat => cat.id === parentId)?.children || []\n        : categories;\n\n      if (!siblings || siblings.length === 0) return 1;\n\n      const sortValues = siblings\n        .map(cat => cat.sortOrder || cat.sort || 0)\n        .filter(sort => typeof sort === 'number' && !isNaN(sort));\n\n      if (sortValues.length === 0) return 1;\n\n      const maxSort = Math.max(...sortValues);\n      return maxSort + 1;\n    } catch (error) {\n      console.error('Error in getNextSortOrder:', error);\n      return 1;\n    }\n  };\n\n\n\n  // 获取分类列表\n  const fetchCategories = async () => {\n    setLoading(true);\n    try {\n      const response = await categoryService.getCategoryTree();\n      console.log('分类树API响应:', response);\n\n      // 处理API响应：如果是直接数组，则直接使用；如果是包装格式，则使用data字段\n      let categoriesData: Category[];\n      if (Array.isArray(response)) {\n        // API直接返回数组\n        categoriesData = response;\n      } else if (response.success && response.data) {\n        // API返回包装格式\n        categoriesData = response.data;\n      } else {\n        // 处理错误情况\n        message.error(response.message || '获取分类列表失败');\n        return;\n      }\n\n      // 检查是否有子分类数据，如果没有则尝试手动构建树形结构\n      const hasChildrenData = categoriesData.some(cat => cat.children && cat.children.length > 0);\n\n      if (!hasChildrenData) {\n        console.log('后端未返回子分类数据，尝试手动构建树形结构');\n        // 如果后端没有返回子分类，我们手动构建树形结构\n        categoriesData = buildTreeStructure(categoriesData);\n      }\n\n      setCategories(categoriesData);\n      // 默认展开第一级分类（有子分类的节点）\n      const firstLevelKeysWithChildren = categoriesData\n        .filter((cat: Category) => cat.children && cat.children.length > 0)\n        .map((cat: Category) => cat.id);\n      setExpandedKeys(firstLevelKeysWithChildren);\n\n      console.log('处理后的分类数据:', categoriesData);\n    } catch (error: any) {\n      console.error('获取分类列表失败:', error);\n      message.error(error.message || '获取分类列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 手动构建树形结构\n  const buildTreeStructure = (flatCategories: Category[]): Category[] => {\n    const categoryMap = new Map<number, Category>();\n    const rootCategories: Category[] = [];\n\n    // 创建分类映射\n    flatCategories.forEach(cat => {\n      categoryMap.set(cat.id, { ...cat, children: [] });\n    });\n\n    // 构建树形结构\n    flatCategories.forEach(cat => {\n      const category = categoryMap.get(cat.id)!;\n      if (cat.parentId === null || cat.parentId === undefined) {\n        // 根分类\n        rootCategories.push(category);\n      } else {\n        // 子分类\n        const parent = categoryMap.get(cat.parentId);\n        if (parent) {\n          if (!parent.children) {\n            parent.children = [];\n          }\n          parent.children.push(category);\n        }\n      }\n    });\n\n    return rootCategories;\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchCategories();\n  }, []);\n\n  // 监听categories变化，更新selectedCategory\n  useEffect(() => {\n    if (selectedCategory && categories.length > 0) {\n      const updatedCategory = findCategoryById(categories, selectedCategory.id);\n      if (updatedCategory && JSON.stringify(updatedCategory) !== JSON.stringify(selectedCategory)) {\n        setSelectedCategory(updatedCategory);\n      }\n    }\n  }, [categories]);\n\n\n\n  // 处理删除按钮点击\n  const handleDeleteClick = (category: Category) => {\n   \n    handleDeleteWithChildren(category);\n    \n\n    console.log('🔥🔥🔥 [DELETE] 步骤2完成: Modal.confirm 已调用');\n  };\n\n  // 查找分类\n  const findCategoryById = (cats: Category[], id: number): Category | null => {\n    for (const cat of cats) {\n      if (cat.id === id) return cat;\n      if (cat.children) {\n        const found = findCategoryById(cat.children, id);\n        if (found) return found;\n      }\n    }\n    return null;\n  };\n\n\n\n  // 新增分类 - 简化流程\n  const handleAdd = (parent?: Category) => {\n    setEditingCategory(null);\n    form.resetFields();\n    clearAllMessages();\n\n    if (parent) {\n      // 从分类树节点添加子分类\n      setSelectedParentForNew(parent);\n      setCurrentLevel(parent.level + 1);\n\n      // 直接设置表单值\n      const nextSortOrder = getNextSortOrder(parent.id);\n      form.setFieldsValue({\n        parentId: parent.id,\n        level: parent.level + 1,\n        sortOrder: nextSortOrder,\n        status: true,\n      });\n    } else {\n      // 添加根分类\n      setSelectedParentForNew(null);\n      setCurrentLevel(1);\n\n      // 直接设置表单值\n      const nextSortOrder = getNextSortOrder(null);\n      form.setFieldsValue({\n        parentId: null,\n        level: 1,\n        sortOrder: nextSortOrder,\n        status: true,\n      });\n    }\n\n    setIsModalVisible(true);\n  };\n\n\n\n  // 编辑分类\n  const handleEdit = (category: Category) => {\n    setEditingCategory(category);\n    setCurrentLevel(category.level);\n\n    form.setFieldsValue({\n      name: category.name,\n      description: category.description,\n      status: category.status === 1,\n      sortOrder: category.sortOrder || 0,\n    });\n    clearAllMessages();\n    setIsModalVisible(true);\n  };\n\n  // 删除分类及其所有子分类（物理删除）\n  const handleDeleteWithChildren = async (category: Category) => {\n\n    try {\n      // 收集所有需要删除的分类ID（包括子分类）\n      const collectCategoryIds = (cat: Category): number[] => {\n        let ids = [cat.id];\n        if (cat.children && cat.children.length > 0) {\n          cat.children.forEach(child => {\n            ids = ids.concat(collectCategoryIds(child));\n          });\n        }\n        return ids;\n      };\n\n      const idsToDelete = collectCategoryIds(category);\n\n      // 按层级从深到浅删除（先删除子分类，再删除父分类）\n      const sortedIds = idsToDelete.sort((a, b) => {\n        const catA = findCategoryById(categories, a);\n        const catB = findCategoryById(categories, b);\n        return (catB?.level || 0) - (catA?.level || 0);\n      });\n\n      let deletedCount = 0;\n      for (const id of sortedIds) {\n        try {\n          \n          const response = await categoryService.deleteCategory(id);\n\n          // 改进删除成功的判断逻辑\n          let isSuccess = false;\n          if (response) {\n            // 如果响应包含success字段且为true（删除API的标准响应）\n            if ('success' in response && response.success === true) {\n              isSuccess = true;\n            }\n            // 检查后端标准响应格式 {code: 200, data: {...}, message: \"...\"}\n            else if ('code' in response && response.code === 200) {\n              isSuccess = true;\n            }\n            // 如果message包含\"成功\"，则认为是成功的\n            else if (response.message && response.message.includes('成功')) {\n              isSuccess = true;\n            }\n            // 如果响应是对象且没有错误信息，认为成功\n            else if (typeof response === 'object' && !(response as any).error && !response.message?.includes('失败')) {\n              isSuccess = true;\n            }\n          }\n\n          if (isSuccess) {\n            deletedCount++;\n          } \n        } catch (error) {\n          console.error(`🔥🔥🔥 [DELETE] 删除分类 ${id} 失败:`, error);\n        }\n      }\n\n\n      if (deletedCount > 0) {\n        message.success(`成功删除 ${deletedCount} 个分类`);\n        fetchCategories();\n        // 清除选中状态\n        setSelectedCategory(null);\n      } else {\n        message.error('删除失败，请稍后重试');\n      }\n    } catch (error: any) {\n      console.error('🔥🔥🔥 [DELETE] handleDeleteWithChildren 异常:', error);\n      message.error(error.message || '删除失败');\n    }\n  };\n\n\n\n\n\n  // 保存分类\n  const handleSave = async (values: any) => {\n    setSaving(true);\n    clearAllMessages();\n\n    try {\n      let response;\n\n      if (editingCategory) {\n        // 编辑分类\n        const updateData = {\n          name: values.name,\n          description: values.description,\n          status: values.status ? 1 : 0,\n          sortOrder: values.sortOrder,\n        };\n        response = await categoryService.updateCategory(editingCategory.id, updateData);\n      } else {\n        // 新增分类\n        const createData = {\n          name: values.name,\n          description: values.description,\n          parentId: selectedParentForNew?.id || null,\n          level: currentLevel,\n          sortOrder: getNextSortOrder(selectedParentForNew?.id || null),\n          status: values.status ? 1 : 0,\n        };\n        response = await categoryService.createCategory(createData);\n      }\n\n      const successMsg = editingCategory ? '分类更新成功！' : '分类创建成功！';\n\n      // 处理API响应：检查是否成功\n      let isSuccess = false;\n      if (response && typeof response === 'object') {\n        // 如果直接返回分类对象且包含id字段，说明创建/更新成功（后端直接返回对象）\n        if ('id' in response && 'name' in response) {\n          isSuccess = true;\n        }\n        // 检查后端标准响应格式 {code: 200, data: {...}, message: \"...\"}\n        else if ('code' in response && response.code === 200) {\n          isSuccess = true;\n        }\n        // 特殊处理：后端bug - 返回 {success: false, message: \"分类更新成功\"}\n        else if ('success' in response && 'message' in response &&\n                 response.message && response.message.includes('成功')) {\n          isSuccess = true;\n        }\n        // 检查前端service层包装的响应格式 {success: true, data: {...}}\n        else if ('success' in response) {\n          isSuccess = response.success;\n          if (!isSuccess && response.message) {\n            setFormError(response.message);\n          }\n        }\n      }\n\n      if (isSuccess) {\n        setFormSuccess(successMsg);\n        // 成功：延迟关闭模态框\n        setTimeout(() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingCategory(null);\n          setSelectedParentForNew(null);\n          clearAllMessages();\n          fetchCategories();\n        }, 1500);\n      } else {\n        setFormError('操作失败，请稍后重试');\n      }\n    } catch (error: any) {\n      handleApiError(error, setFormError);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 转换分类数据为树形结构 - 优化版\n  const convertCategoriesToTreeData = (categories: Category[]): DataNode[] => {\n    return categories.map((category) => {\n      const hasChildren = category.children && category.children.length > 0;\n      const productCount = category.productCount || 0;\n\n      return {\n        title: (\n          <div className=\"category-tree-node\" style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            width: '100%',\n            paddingRight: '8px'\n          }}>\n            <div className=\"category-info\" style={{ flex: 1 }}>\n              <span className=\"category-name\">{category.name}</span>\n              <div className=\"category-meta\">\n                <span className=\"category-code\">({category.code})</span>\n                {productCount > 0 && (\n                  <span className=\"product-count\">{productCount}个商品</span>\n                )}\n                {hasChildren && (\n                  <span className=\"children-count\">\n                    {category.children!.length}个子分类\n                  </span>\n                )}\n                {category.status === 0 && (\n                  <Tag color=\"volcano\" style={{ fontSize: '11px' }}>\n                    已禁用\n                  </Tag>\n                )}\n              </div>\n            </div>\n\n            {/* 操作按钮组 */}\n            <div\n              className=\"category-actions\"\n              style={{\n                display: 'flex',\n                gap: '4px',\n                opacity: 1,\n                transition: 'all 0.2s',\n                backgroundColor: 'rgba(255, 255, 255, 0.9)',\n                borderRadius: '4px',\n                padding: '2px',\n                boxShadow: '0 1px 4px rgba(0,0,0,0.1)'\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 1)';\n                e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';\n                e.currentTarget.style.boxShadow = '0 1px 4px rgba(0,0,0,0.1)';\n              }}\n              onClick={(e) => e.stopPropagation()}\n            >\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<EditOutlined />}\n                onClick={(e) => {\n                  e.stopPropagation();\n                  handleEdit(category);\n                }}\n                style={{\n                  padding: '2px 4px',\n                  height: '22px',\n                  minWidth: '22px',\n                  color: '#1890ff',\n                  border: 'none'\n                }}\n                title=\"编辑分类\"\n              />\n              {category.level < 3 && (\n                <Button\n                  type=\"text\"\n                  size=\"small\"\n                  icon={<PlusOutlined />}\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    handleAdd(category);\n                  }}\n                  style={{\n                    padding: '2px 4px',\n                    height: '22px',\n                    minWidth: '22px',\n                    color: '#52c41a',\n                    border: 'none'\n                  }}\n                  title=\"添加子分类\"\n                />\n              )}\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<DeleteOutlined />}\n                onClick={(e) => {\n                  e.stopPropagation();\n                  handleDeleteClick(category);\n                }}\n                style={{\n                  padding: '2px 4px',\n                  height: '22px',\n                  minWidth: '22px',\n                  color: '#ff4d4f',\n                  border: 'none',\n                  display: 'inline-flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}\n                title=\"删除分类\"\n              />\n            </div>\n          </div>\n        ),\n        key: category.id,\n        icon: hasChildren ? <FolderOutlined /> : <FileOutlined />,\n        children: hasChildren ? convertCategoriesToTreeData(category.children!) : undefined,\n        isLeaf: !hasChildren,\n      };\n    });\n  };\n\n  // 树节点选择处理\n  const handleTreeSelect = (selectedKeys: React.Key[]) => {\n    if (selectedKeys.length > 0) {\n      const category = findCategoryById(categories, selectedKeys[0] as number);\n      setSelectedCategory(category);\n    } else {\n      setSelectedCategory(null);\n    }\n  };\n\n  return (\n    <div className=\"category-management-container\">\n      <Title level={2}>分类管理</Title>\n\n      <Row gutter={24}>\n        {/* 左侧分类树 */}\n        <Col xs={24} lg={16}>\n          <Card\n            title=\"分类树\"\n            extra={\n              <Space>\n                <Button\n                  type=\"primary\"\n                  icon={<PlusOutlined />}\n                  onClick={() => handleAdd()}\n                >\n                  添加根分类\n                </Button>\n                <Button\n                  icon={<ReloadOutlined />}\n                  onClick={fetchCategories}\n                  loading={loading}\n                >\n                  刷新\n                </Button>\n              </Space>\n            }\n          >\n            <Tree\n              showIcon\n              showLine={false}\n              expandedKeys={expandedKeys}\n              onExpand={setExpandedKeys}\n              onSelect={handleTreeSelect}\n              selectedKeys={selectedCategory ? [selectedCategory.id] : []}\n              treeData={convertCategoriesToTreeData(categories)}\n              height={600}\n              blockNode\n              switcherIcon={({ expanded, isLeaf }) => {\n                if (isLeaf) return null;\n                return expanded ? (\n                  <MinusSquareOutlined style={{ color: '#1890ff', fontSize: '14px' }} />\n                ) : (\n                  <PlusSquareOutlined style={{ color: '#1890ff', fontSize: '14px' }} />\n                );\n              }}\n            />\n\n\n          </Card>\n        </Col>\n\n        {/* 右侧分类详情 */}\n        <Col xs={24} lg={8}>\n          <Card title=\"分类详情\">\n            {selectedCategory ? (\n              <Descriptions column={1} size=\"small\">\n                <Descriptions.Item label=\"分类名称\">\n                  {selectedCategory.name}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"分类编码\">\n                  {selectedCategory.code}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"分类描述\">\n                  {selectedCategory.description || '-'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"分类级别\">\n                  第{selectedCategory.level}级\n                </Descriptions.Item>\n                <Descriptions.Item label=\"排序\">\n                  {selectedCategory.sortOrder || selectedCategory.sort || 0}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Tag color={selectedCategory.status === 1 ? 'green' : 'red'}>\n                    {selectedCategory.status === 1 ? '启用' : '禁用'}\n                  </Tag>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"商品数量\">\n                  {selectedCategory.productCount}个\n                </Descriptions.Item>\n                <Descriptions.Item label=\"创建时间\">\n                  {new Date(selectedCategory.createdAt).toLocaleString()}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"更新时间\">\n                  {new Date(selectedCategory.updatedAt).toLocaleString()}\n                </Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>\n                请选择一个分类查看详情\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 分类编辑模态框 - 简化设计 */}\n      <Modal\n        title={editingCategory ? '编辑分类' : '新增分类'}\n        open={isModalVisible}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingCategory(null);\n          clearAllMessages();\n        }}\n        footer={null}\n        width={600}\n        destroyOnClose\n      >\n        {/* 位置信息显示 */}\n        {!editingCategory && (\n          <Alert\n            message={\n              selectedParentForNew\n                ? `在 \"${selectedParentForNew.name}\" 下创建 ${currentLevel} 级分类`\n                : \"创建一级分类\"\n            }\n            type=\"info\"\n            style={{ marginBottom: 16 }}\n            showIcon\n          />\n        )}\n\n        {(\n          <Form\n            form={form}\n            layout=\"vertical\"\n            onFinish={handleSave}\n            autoComplete=\"off\"\n          >\n            <Form.Item\n              name=\"name\"\n              label=\"分类名称\"\n              rules={[\n                { required: true, message: '请输入分类名称' },\n                { max: 50, message: '分类名称不能超过50个字符' },\n              ]}\n            >\n              <Input placeholder=\"请输入分类名称\" />\n            </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"分类描述\"\n            rules={[\n              { max: 200, message: '分类描述不能超过200个字符' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入分类描述\"\n              rows={4}\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"sortOrder\"\n                  label=\"排序\"\n                  tooltip=\"数值越小排序越靠前，编辑时可以调整\"\n                >\n                  {editingCategory ? (\n                    <InputNumber\n                      placeholder=\"排序值\"\n                      min={1}\n                      max={9999}\n                      style={{ width: '100%' }}\n                    />\n                  ) : (\n                    <div style={{\n                      padding: '4px 11px',\n                      border: '1px solid #d9d9d9',\n                      borderRadius: '6px',\n                      backgroundColor: '#f5f5f5',\n                      color: '#666'\n                    }}>\n                      系统自动分配: {getNextSortOrder(selectedParentForNew?.id || null)}\n                    </div>\n                  )}\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"status\"\n                  label=\"状态\"\n                  valuePropName=\"checked\"\n                  initialValue={true}\n                >\n                  <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n                </Form.Item>\n              </Col>\n            </Row>\n\n            {/* 隐藏字段 */}\n            <Form.Item name=\"parentId\" hidden>\n              <Input />\n            </Form.Item>\n            <Form.Item name=\"level\" hidden>\n              <Input />\n            </Form.Item>\n\n            {/* 错误和成功消息显示 */}\n            <FormMessage type=\"error\" message={formError} visible={!!formError} />\n            <FormMessage type=\"success\" message={formSuccess} visible={!!formSuccess} />\n\n            <Form.Item>\n              <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n                <Button\n                  onClick={() => {\n                    setIsModalVisible(false);\n                    form.resetFields();\n                    setEditingCategory(null);\n                    clearAllMessages();\n                  }}\n                  disabled={saving}\n                >\n                  取消\n                </Button>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={saving}\n                  disabled={saving}\n                >\n                  {saving ? '保存中...' : (editingCategory ? '更新' : '创建')}\n                </Button>\n              </Space>\n            </Form.Item>\n          </Form>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default CategoryManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,YAAY,EACZC,GAAG,EACHC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,YAAY,EACZC,mBAAmB,EACnBC,kBAAkB,QACb,mBAAmB;AAE1B,SAASC,eAAe,QAAQ,mCAAmC;AACnE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,SAASC,cAAc,EAAEC,cAAc,QAAQ,+BAA+B;AAC9E,OAAO,aAAa;AACpB,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC;AAAM,CAAC,GAAGtB,UAAU;AAC5B,MAAM;EAAEuB;AAAS,CAAC,GAAG3B,KAAK;;AAI1B;;AAiBA,MAAM4B,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAkB,IAAI,CAAC;EAC/E,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAc,EAAE,CAAC;EACjE,MAAM,CAACkD,IAAI,CAAC,GAAG1C,IAAI,CAAC2C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAS,CAAC,CAAC;EAC3D,MAAM,CAACsD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvD,QAAQ,CAAkB,IAAI,CAAC;EAGvF,MAAM;IACJwD,SAAS;IACTC,WAAW;IACXC,YAAY;IACZC,cAAc;IACdC;EACF,CAAC,GAAGhC,cAAc,CAAC,CAAC;;EAMpB;EACA,MAAMiC,gBAAgB,GAAIC,QAAuB,IAAa;IAC5D,IAAI;MAAA,IAAAC,gBAAA;MACF,MAAMC,QAAQ,GAAGF,QAAQ,GACrB,EAAAC,gBAAA,GAAA3B,UAAU,CAAC6B,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKL,QAAQ,CAAC,cAAAC,gBAAA,uBAA3CA,gBAAA,CAA6CK,QAAQ,KAAI,EAAE,GAC3DhC,UAAU;MAEd,IAAI,CAAC4B,QAAQ,IAAIA,QAAQ,CAACK,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;MAEhD,MAAMC,UAAU,GAAGN,QAAQ,CACxBO,GAAG,CAACL,GAAG,IAAIA,GAAG,CAACM,SAAS,IAAIN,GAAG,CAACO,IAAI,IAAI,CAAC,CAAC,CAC1CC,MAAM,CAACD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACF,IAAI,CAAC,CAAC;MAE3D,IAAIH,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;MAErC,MAAMO,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGR,UAAU,CAAC;MACvC,OAAOM,OAAO,GAAG,CAAC;IACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO,CAAC;IACV;EACF,CAAC;;EAID;EACA,MAAME,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC1C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAMxD,eAAe,CAACyD,eAAe,CAAC,CAAC;MACxDH,OAAO,CAACI,GAAG,CAAC,WAAW,EAAEF,QAAQ,CAAC;;MAElC;MACA,IAAIG,cAA0B;MAC9B,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,EAAE;QAC3B;QACAG,cAAc,GAAGH,QAAQ;MAC3B,CAAC,MAAM,IAAIA,QAAQ,CAACM,OAAO,IAAIN,QAAQ,CAACO,IAAI,EAAE;QAC5C;QACAJ,cAAc,GAAGH,QAAQ,CAACO,IAAI;MAChC,CAAC,MAAM;QACL;QACAhF,OAAO,CAACsE,KAAK,CAACG,QAAQ,CAACzE,OAAO,IAAI,UAAU,CAAC;QAC7C;MACF;;MAEA;MACA,MAAMiF,eAAe,GAAGL,cAAc,CAACM,IAAI,CAACzB,GAAG,IAAIA,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACE,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;MAE3F,IAAI,CAACqB,eAAe,EAAE;QACpBV,OAAO,CAACI,GAAG,CAAC,uBAAuB,CAAC;QACpC;QACAC,cAAc,GAAGO,kBAAkB,CAACP,cAAc,CAAC;MACrD;MAEAhD,aAAa,CAACgD,cAAc,CAAC;MAC7B;MACA,MAAMQ,0BAA0B,GAAGR,cAAc,CAC9CX,MAAM,CAAER,GAAa,IAAKA,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACE,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC,CAClEE,GAAG,CAAEL,GAAa,IAAKA,GAAG,CAACC,EAAE,CAAC;MACjClB,eAAe,CAAC4C,0BAA0B,CAAC;MAE3Cb,OAAO,CAACI,GAAG,CAAC,WAAW,EAAEC,cAAc,CAAC;IAC1C,CAAC,CAAC,OAAON,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCtE,OAAO,CAACsE,KAAK,CAACA,KAAK,CAACtE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACR8B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqD,kBAAkB,GAAIE,cAA0B,IAAiB;IACrE,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAmB,CAAC;IAC/C,MAAMC,cAA0B,GAAG,EAAE;;IAErC;IACAH,cAAc,CAACI,OAAO,CAAChC,GAAG,IAAI;MAC5B6B,WAAW,CAACI,GAAG,CAACjC,GAAG,CAACC,EAAE,EAAE;QAAE,GAAGD,GAAG;QAAEE,QAAQ,EAAE;MAAG,CAAC,CAAC;IACnD,CAAC,CAAC;;IAEF;IACA0B,cAAc,CAACI,OAAO,CAAChC,GAAG,IAAI;MAC5B,MAAMkC,QAAQ,GAAGL,WAAW,CAACM,GAAG,CAACnC,GAAG,CAACC,EAAE,CAAE;MACzC,IAAID,GAAG,CAACJ,QAAQ,KAAK,IAAI,IAAII,GAAG,CAACJ,QAAQ,KAAKwC,SAAS,EAAE;QACvD;QACAL,cAAc,CAACM,IAAI,CAACH,QAAQ,CAAC;MAC/B,CAAC,MAAM;QACL;QACA,MAAMI,MAAM,GAAGT,WAAW,CAACM,GAAG,CAACnC,GAAG,CAACJ,QAAQ,CAAC;QAC5C,IAAI0C,MAAM,EAAE;UACV,IAAI,CAACA,MAAM,CAACpC,QAAQ,EAAE;YACpBoC,MAAM,CAACpC,QAAQ,GAAG,EAAE;UACtB;UACAoC,MAAM,CAACpC,QAAQ,CAACmC,IAAI,CAACH,QAAQ,CAAC;QAChC;MACF;IACF,CAAC,CAAC;IAEF,OAAOH,cAAc;EACvB,CAAC;;EAED;EACAhG,SAAS,CAAC,MAAM;IACdgF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhF,SAAS,CAAC,MAAM;IACd,IAAI6C,gBAAgB,IAAIV,UAAU,CAACiC,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMoC,eAAe,GAAGC,gBAAgB,CAACtE,UAAU,EAAEU,gBAAgB,CAACqB,EAAE,CAAC;MACzE,IAAIsC,eAAe,IAAIE,IAAI,CAACC,SAAS,CAACH,eAAe,CAAC,KAAKE,IAAI,CAACC,SAAS,CAAC9D,gBAAgB,CAAC,EAAE;QAC3FC,mBAAmB,CAAC0D,eAAe,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CAACrE,UAAU,CAAC,CAAC;;EAIhB;EACA,MAAMyE,iBAAiB,GAAIT,QAAkB,IAAK;IAEhDU,wBAAwB,CAACV,QAAQ,CAAC;IAGlCpB,OAAO,CAACI,GAAG,CAAC,0CAA0C,CAAC;EACzD,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAGA,CAACK,IAAgB,EAAE5C,EAAU,KAAsB;IAC1E,KAAK,MAAMD,GAAG,IAAI6C,IAAI,EAAE;MACtB,IAAI7C,GAAG,CAACC,EAAE,KAAKA,EAAE,EAAE,OAAOD,GAAG;MAC7B,IAAIA,GAAG,CAACE,QAAQ,EAAE;QAChB,MAAM4C,KAAK,GAAGN,gBAAgB,CAACxC,GAAG,CAACE,QAAQ,EAAED,EAAE,CAAC;QAChD,IAAI6C,KAAK,EAAE,OAAOA,KAAK;MACzB;IACF;IACA,OAAO,IAAI;EACb,CAAC;;EAID;EACA,MAAMC,SAAS,GAAIT,MAAiB,IAAK;IACvC3D,kBAAkB,CAAC,IAAI,CAAC;IACxBK,IAAI,CAACgE,WAAW,CAAC,CAAC;IAClBtD,gBAAgB,CAAC,CAAC;IAElB,IAAI4C,MAAM,EAAE;MACV;MACAjD,uBAAuB,CAACiD,MAAM,CAAC;MAC/BnD,eAAe,CAACmD,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC;;MAEjC;MACA,MAAMC,aAAa,GAAGvD,gBAAgB,CAAC2C,MAAM,CAACrC,EAAE,CAAC;MACjDjB,IAAI,CAACmE,cAAc,CAAC;QAClBvD,QAAQ,EAAE0C,MAAM,CAACrC,EAAE;QACnBgD,KAAK,EAAEX,MAAM,CAACW,KAAK,GAAG,CAAC;QACvB3C,SAAS,EAAE4C,aAAa;QACxBE,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA/D,uBAAuB,CAAC,IAAI,CAAC;MAC7BF,eAAe,CAAC,CAAC,CAAC;;MAElB;MACA,MAAM+D,aAAa,GAAGvD,gBAAgB,CAAC,IAAI,CAAC;MAC5CX,IAAI,CAACmE,cAAc,CAAC;QAClBvD,QAAQ,EAAE,IAAI;QACdqD,KAAK,EAAE,CAAC;QACR3C,SAAS,EAAE4C,aAAa;QACxBE,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IAEA3E,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAID;EACA,MAAM4E,UAAU,GAAInB,QAAkB,IAAK;IACzCvD,kBAAkB,CAACuD,QAAQ,CAAC;IAC5B/C,eAAe,CAAC+C,QAAQ,CAACe,KAAK,CAAC;IAE/BjE,IAAI,CAACmE,cAAc,CAAC;MAClBG,IAAI,EAAEpB,QAAQ,CAACoB,IAAI;MACnBC,WAAW,EAAErB,QAAQ,CAACqB,WAAW;MACjCH,MAAM,EAAElB,QAAQ,CAACkB,MAAM,KAAK,CAAC;MAC7B9C,SAAS,EAAE4B,QAAQ,CAAC5B,SAAS,IAAI;IACnC,CAAC,CAAC;IACFZ,gBAAgB,CAAC,CAAC;IAClBjB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMmE,wBAAwB,GAAG,MAAOV,QAAkB,IAAK;IAE7D,IAAI;MACF;MACA,MAAMsB,kBAAkB,GAAIxD,GAAa,IAAe;QACtD,IAAIyD,GAAG,GAAG,CAACzD,GAAG,CAACC,EAAE,CAAC;QAClB,IAAID,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACE,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3CH,GAAG,CAACE,QAAQ,CAAC8B,OAAO,CAAC0B,KAAK,IAAI;YAC5BD,GAAG,GAAGA,GAAG,CAACE,MAAM,CAACH,kBAAkB,CAACE,KAAK,CAAC,CAAC;UAC7C,CAAC,CAAC;QACJ;QACA,OAAOD,GAAG;MACZ,CAAC;MAED,MAAMG,WAAW,GAAGJ,kBAAkB,CAACtB,QAAQ,CAAC;;MAEhD;MACA,MAAM2B,SAAS,GAAGD,WAAW,CAACrD,IAAI,CAAC,CAACuD,CAAC,EAAEC,CAAC,KAAK;QAC3C,MAAMC,IAAI,GAAGxB,gBAAgB,CAACtE,UAAU,EAAE4F,CAAC,CAAC;QAC5C,MAAMG,IAAI,GAAGzB,gBAAgB,CAACtE,UAAU,EAAE6F,CAAC,CAAC;QAC5C,OAAO,CAAC,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhB,KAAK,KAAI,CAAC,KAAK,CAAAe,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEf,KAAK,KAAI,CAAC,CAAC;MAChD,CAAC,CAAC;MAEF,IAAIiB,YAAY,GAAG,CAAC;MACpB,KAAK,MAAMjE,EAAE,IAAI4D,SAAS,EAAE;QAC1B,IAAI;UAEF,MAAM7C,QAAQ,GAAG,MAAMxD,eAAe,CAAC2G,cAAc,CAAClE,EAAE,CAAC;;UAEzD;UACA,IAAImE,SAAS,GAAG,KAAK;UACrB,IAAIpD,QAAQ,EAAE;YAAA,IAAAqD,iBAAA;YACZ;YACA,IAAI,SAAS,IAAIrD,QAAQ,IAAIA,QAAQ,CAACM,OAAO,KAAK,IAAI,EAAE;cACtD8C,SAAS,GAAG,IAAI;YAClB;YACA;YAAA,KACK,IAAI,MAAM,IAAIpD,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,KAAK,GAAG,EAAE;cACpDF,SAAS,GAAG,IAAI;YAClB;YACA;YAAA,KACK,IAAIpD,QAAQ,CAACzE,OAAO,IAAIyE,QAAQ,CAACzE,OAAO,CAACgI,QAAQ,CAAC,IAAI,CAAC,EAAE;cAC5DH,SAAS,GAAG,IAAI;YAClB;YACA;YAAA,KACK,IAAI,OAAOpD,QAAQ,KAAK,QAAQ,IAAI,CAAEA,QAAQ,CAASH,KAAK,IAAI,GAAAwD,iBAAA,GAACrD,QAAQ,CAACzE,OAAO,cAAA8H,iBAAA,eAAhBA,iBAAA,CAAkBE,QAAQ,CAAC,IAAI,CAAC,GAAE;cACtGH,SAAS,GAAG,IAAI;YAClB;UACF;UAEA,IAAIA,SAAS,EAAE;YACbF,YAAY,EAAE;UAChB;QACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,wBAAwBZ,EAAE,MAAM,EAAEY,KAAK,CAAC;QACxD;MACF;MAGA,IAAIqD,YAAY,GAAG,CAAC,EAAE;QACpB3H,OAAO,CAAC+E,OAAO,CAAC,QAAQ4C,YAAY,MAAM,CAAC;QAC3CnD,eAAe,CAAC,CAAC;QACjB;QACAlC,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLtC,OAAO,CAACsE,KAAK,CAAC,YAAY,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpEtE,OAAO,CAACsE,KAAK,CAACA,KAAK,CAACtE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAMD;EACA,MAAMiI,UAAU,GAAG,MAAOC,MAAW,IAAK;IACxClG,SAAS,CAAC,IAAI,CAAC;IACfmB,gBAAgB,CAAC,CAAC;IAElB,IAAI;MACF,IAAIsB,QAAQ;MAEZ,IAAItC,eAAe,EAAE;QACnB;QACA,MAAMgG,UAAU,GAAG;UACjBpB,IAAI,EAAEmB,MAAM,CAACnB,IAAI;UACjBC,WAAW,EAAEkB,MAAM,CAAClB,WAAW;UAC/BH,MAAM,EAAEqB,MAAM,CAACrB,MAAM,GAAG,CAAC,GAAG,CAAC;UAC7B9C,SAAS,EAAEmE,MAAM,CAACnE;QACpB,CAAC;QACDU,QAAQ,GAAG,MAAMxD,eAAe,CAACmH,cAAc,CAACjG,eAAe,CAACuB,EAAE,EAAEyE,UAAU,CAAC;MACjF,CAAC,MAAM;QACL;QACA,MAAME,UAAU,GAAG;UACjBtB,IAAI,EAAEmB,MAAM,CAACnB,IAAI;UACjBC,WAAW,EAAEkB,MAAM,CAAClB,WAAW;UAC/B3D,QAAQ,EAAE,CAAAR,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEa,EAAE,KAAI,IAAI;UAC1CgD,KAAK,EAAE/D,YAAY;UACnBoB,SAAS,EAAEX,gBAAgB,CAAC,CAAAP,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEa,EAAE,KAAI,IAAI,CAAC;UAC7DmD,MAAM,EAAEqB,MAAM,CAACrB,MAAM,GAAG,CAAC,GAAG;QAC9B,CAAC;QACDpC,QAAQ,GAAG,MAAMxD,eAAe,CAACqH,cAAc,CAACD,UAAU,CAAC;MAC7D;MAEA,MAAME,UAAU,GAAGpG,eAAe,GAAG,SAAS,GAAG,SAAS;;MAE1D;MACA,IAAI0F,SAAS,GAAG,KAAK;MACrB,IAAIpD,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAC5C;QACA,IAAI,IAAI,IAAIA,QAAQ,IAAI,MAAM,IAAIA,QAAQ,EAAE;UAC1CoD,SAAS,GAAG,IAAI;QAClB;QACA;QAAA,KACK,IAAI,MAAM,IAAIpD,QAAQ,IAAIA,QAAQ,CAACsD,IAAI,KAAK,GAAG,EAAE;UACpDF,SAAS,GAAG,IAAI;QAClB;QACA;QAAA,KACK,IAAI,SAAS,IAAIpD,QAAQ,IAAI,SAAS,IAAIA,QAAQ,IAC9CA,QAAQ,CAACzE,OAAO,IAAIyE,QAAQ,CAACzE,OAAO,CAACgI,QAAQ,CAAC,IAAI,CAAC,EAAE;UAC5DH,SAAS,GAAG,IAAI;QAClB;QACA;QAAA,KACK,IAAI,SAAS,IAAIpD,QAAQ,EAAE;UAC9BoD,SAAS,GAAGpD,QAAQ,CAACM,OAAO;UAC5B,IAAI,CAAC8C,SAAS,IAAIpD,QAAQ,CAACzE,OAAO,EAAE;YAClCiD,YAAY,CAACwB,QAAQ,CAACzE,OAAO,CAAC;UAChC;QACF;MACF;MAEA,IAAI6H,SAAS,EAAE;QACb3E,cAAc,CAACqF,UAAU,CAAC;QAC1B;QACAC,UAAU,CAAC,MAAM;UACftG,iBAAiB,CAAC,KAAK,CAAC;UACxBO,IAAI,CAACgE,WAAW,CAAC,CAAC;UAClBrE,kBAAkB,CAAC,IAAI,CAAC;UACxBU,uBAAuB,CAAC,IAAI,CAAC;UAC7BK,gBAAgB,CAAC,CAAC;UAClBqB,eAAe,CAAC,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLvB,YAAY,CAAC,YAAY,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOqB,KAAU,EAAE;MACnBlD,cAAc,CAACkD,KAAK,EAAErB,YAAY,CAAC;IACrC,CAAC,SAAS;MACRjB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMyG,2BAA2B,GAAI9G,UAAsB,IAAiB;IAC1E,OAAOA,UAAU,CAACmC,GAAG,CAAE6B,QAAQ,IAAK;MAClC,MAAM+C,WAAW,GAAG/C,QAAQ,CAAChC,QAAQ,IAAIgC,QAAQ,CAAChC,QAAQ,CAACC,MAAM,GAAG,CAAC;MACrE,MAAM+E,YAAY,GAAGhD,QAAQ,CAACgD,YAAY,IAAI,CAAC;MAE/C,OAAO;QACLC,KAAK,eACHtH,OAAA;UAAKuH,SAAS,EAAC,oBAAoB;UAACC,KAAK,EAAE;YACzCC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BC,KAAK,EAAE,MAAM;YACbC,YAAY,EAAE;UAChB,CAAE;UAAAxF,QAAA,gBACArC,OAAA;YAAKuH,SAAS,EAAC,eAAe;YAACC,KAAK,EAAE;cAAEM,IAAI,EAAE;YAAE,CAAE;YAAAzF,QAAA,gBAChDrC,OAAA;cAAMuH,SAAS,EAAC,eAAe;cAAAlF,QAAA,EAAEgC,QAAQ,CAACoB;YAAI;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDlI,OAAA;cAAKuH,SAAS,EAAC,eAAe;cAAAlF,QAAA,gBAC5BrC,OAAA;gBAAMuH,SAAS,EAAC,eAAe;gBAAAlF,QAAA,GAAC,GAAC,EAACgC,QAAQ,CAACoC,IAAI,EAAC,GAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACvDb,YAAY,GAAG,CAAC,iBACfrH,OAAA;gBAAMuH,SAAS,EAAC,eAAe;gBAAAlF,QAAA,GAAEgF,YAAY,EAAC,oBAAG;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACxD,EACAd,WAAW,iBACVpH,OAAA;gBAAMuH,SAAS,EAAC,gBAAgB;gBAAAlF,QAAA,GAC7BgC,QAAQ,CAAChC,QAAQ,CAAEC,MAAM,EAAC,0BAC7B;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,EACA7D,QAAQ,CAACkB,MAAM,KAAK,CAAC,iBACpBvF,OAAA,CAACf,GAAG;gBAACkJ,KAAK,EAAC,SAAS;gBAACX,KAAK,EAAE;kBAAEY,QAAQ,EAAE;gBAAO,CAAE;gBAAA/F,QAAA,EAAC;cAElD;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlI,OAAA;YACEuH,SAAS,EAAC,kBAAkB;YAC5BC,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfY,GAAG,EAAE,KAAK;cACVC,OAAO,EAAE,CAAC;cACVC,UAAU,EAAE,UAAU;cACtBC,eAAe,EAAE,0BAA0B;cAC3CC,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,KAAK;cACdC,SAAS,EAAE;YACb,CAAE;YACFC,YAAY,EAAGC,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACtB,KAAK,CAACgB,eAAe,GAAG,wBAAwB;cAChEK,CAAC,CAACC,aAAa,CAACtB,KAAK,CAACmB,SAAS,GAAG,4BAA4B;YAChE,CAAE;YACFI,YAAY,EAAGF,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACtB,KAAK,CAACgB,eAAe,GAAG,0BAA0B;cAClEK,CAAC,CAACC,aAAa,CAACtB,KAAK,CAACmB,SAAS,GAAG,2BAA2B;YAC/D,CAAE;YACFK,OAAO,EAAGH,CAAC,IAAKA,CAAC,CAACI,eAAe,CAAC,CAAE;YAAA5G,QAAA,gBAEpCrC,OAAA,CAAC3B,MAAM;cACL6K,IAAI,EAAC,MAAM;cACXC,IAAI,EAAC,OAAO;cACZC,IAAI,eAAEpJ,OAAA,CAACZ,YAAY;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBc,OAAO,EAAGH,CAAC,IAAK;gBACdA,CAAC,CAACI,eAAe,CAAC,CAAC;gBACnBzD,UAAU,CAACnB,QAAQ,CAAC;cACtB,CAAE;cACFmD,KAAK,EAAE;gBACLkB,OAAO,EAAE,SAAS;gBAClBW,MAAM,EAAE,MAAM;gBACdC,QAAQ,EAAE,MAAM;gBAChBnB,KAAK,EAAE,SAAS;gBAChBoB,MAAM,EAAE;cACV,CAAE;cACFjC,KAAK,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACD7D,QAAQ,CAACe,KAAK,GAAG,CAAC,iBACjBpF,OAAA,CAAC3B,MAAM;cACL6K,IAAI,EAAC,MAAM;cACXC,IAAI,EAAC,OAAO;cACZC,IAAI,eAAEpJ,OAAA,CAACb,YAAY;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBc,OAAO,EAAGH,CAAC,IAAK;gBACdA,CAAC,CAACI,eAAe,CAAC,CAAC;gBACnB/D,SAAS,CAACb,QAAQ,CAAC;cACrB,CAAE;cACFmD,KAAK,EAAE;gBACLkB,OAAO,EAAE,SAAS;gBAClBW,MAAM,EAAE,MAAM;gBACdC,QAAQ,EAAE,MAAM;gBAChBnB,KAAK,EAAE,SAAS;gBAChBoB,MAAM,EAAE;cACV,CAAE;cACFjC,KAAK,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACF,eACDlI,OAAA,CAAC3B,MAAM;cACL6K,IAAI,EAAC,MAAM;cACXC,IAAI,EAAC,OAAO;cACZC,IAAI,eAAEpJ,OAAA,CAACX,cAAc;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBc,OAAO,EAAGH,CAAC,IAAK;gBACdA,CAAC,CAACI,eAAe,CAAC,CAAC;gBACnBnE,iBAAiB,CAACT,QAAQ,CAAC;cAC7B,CAAE;cACFmD,KAAK,EAAE;gBACLkB,OAAO,EAAE,SAAS;gBAClBW,MAAM,EAAE,MAAM;gBACdC,QAAQ,EAAE,MAAM;gBAChBnB,KAAK,EAAE,SAAS;gBAChBoB,MAAM,EAAE,MAAM;gBACd9B,OAAO,EAAE,aAAa;gBACtBC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cACFL,KAAK,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;QACDsB,GAAG,EAAEnF,QAAQ,CAACjC,EAAE;QAChBgH,IAAI,EAAEhC,WAAW,gBAAGpH,OAAA,CAACT,cAAc;UAAAwI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlI,OAAA,CAACR,YAAY;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACzD7F,QAAQ,EAAE+E,WAAW,GAAGD,2BAA2B,CAAC9C,QAAQ,CAAChC,QAAS,CAAC,GAAGkC,SAAS;QACnFkF,MAAM,EAAE,CAACrC;MACX,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIC,YAAyB,IAAK;IACtD,IAAIA,YAAY,CAACrH,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAM+B,QAAQ,GAAGM,gBAAgB,CAACtE,UAAU,EAAEsJ,YAAY,CAAC,CAAC,CAAW,CAAC;MACxE3I,mBAAmB,CAACqD,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLrD,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKuH,SAAS,EAAC,+BAA+B;IAAAlF,QAAA,gBAC5CrC,OAAA,CAACC,KAAK;MAACmF,KAAK,EAAE,CAAE;MAAA/C,QAAA,EAAC;IAAI;MAAA0F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAE7BlI,OAAA,CAACpB,GAAG;MAACgL,MAAM,EAAE,EAAG;MAAAvH,QAAA,gBAEdrC,OAAA,CAACnB,GAAG;QAACgL,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAzH,QAAA,eAClBrC,OAAA,CAAC7B,IAAI;UACHmJ,KAAK,EAAC,oBAAK;UACXyC,KAAK,eACH/J,OAAA,CAAC1B,KAAK;YAAA+D,QAAA,gBACJrC,OAAA,CAAC3B,MAAM;cACL6K,IAAI,EAAC,SAAS;cACdE,IAAI,eAAEpJ,OAAA,CAACb,YAAY;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBc,OAAO,EAAEA,CAAA,KAAM9D,SAAS,CAAC,CAAE;cAAA7C,QAAA,EAC5B;YAED;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlI,OAAA,CAAC3B,MAAM;cACL+K,IAAI,eAAEpJ,OAAA,CAACV,cAAc;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBc,OAAO,EAAE9F,eAAgB;cACzB3C,OAAO,EAAEA,OAAQ;cAAA8B,QAAA,EAClB;YAED;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;UAAA7F,QAAA,eAEDrC,OAAA,CAAC5B,IAAI;YACH4L,QAAQ;YACRC,QAAQ,EAAE,KAAM;YAChBhJ,YAAY,EAAEA,YAAa;YAC3BiJ,QAAQ,EAAEhJ,eAAgB;YAC1BiJ,QAAQ,EAAET,gBAAiB;YAC3BC,YAAY,EAAE5I,gBAAgB,GAAG,CAACA,gBAAgB,CAACqB,EAAE,CAAC,GAAG,EAAG;YAC5DgI,QAAQ,EAAEjD,2BAA2B,CAAC9G,UAAU,CAAE;YAClDgJ,MAAM,EAAE,GAAI;YACZgB,SAAS;YACTC,YAAY,EAAEA,CAAC;cAAEC,QAAQ;cAAEd;YAAO,CAAC,KAAK;cACtC,IAAIA,MAAM,EAAE,OAAO,IAAI;cACvB,OAAOc,QAAQ,gBACbvK,OAAA,CAACP,mBAAmB;gBAAC+H,KAAK,EAAE;kBAAEW,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE;gBAAO;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEtElI,OAAA,CAACN,kBAAkB;gBAAC8H,KAAK,EAAE;kBAAEW,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE;gBAAO;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACrE;YACH;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlI,OAAA,CAACnB,GAAG;QAACgL,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzH,QAAA,eACjBrC,OAAA,CAAC7B,IAAI;UAACmJ,KAAK,EAAC,0BAAM;UAAAjF,QAAA,EACftB,gBAAgB,gBACff,OAAA,CAAChB,YAAY;YAACwL,MAAM,EAAE,CAAE;YAACrB,IAAI,EAAC,OAAO;YAAA9G,QAAA,gBACnCrC,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArI,QAAA,EAC5BtB,gBAAgB,CAAC0E;YAAI;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACpBlI,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArI,QAAA,EAC5BtB,gBAAgB,CAAC0F;YAAI;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACpBlI,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArI,QAAA,EAC5BtB,gBAAgB,CAAC2E,WAAW,IAAI;YAAG;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACpBlI,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArI,QAAA,GAAC,QAC7B,EAACtB,gBAAgB,CAACqE,KAAK,EAAC,QAC3B;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACpBlI,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,cAAI;cAAArI,QAAA,EAC1BtB,gBAAgB,CAAC0B,SAAS,IAAI1B,gBAAgB,CAAC2B,IAAI,IAAI;YAAC;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACpBlI,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,cAAI;cAAArI,QAAA,eAC3BrC,OAAA,CAACf,GAAG;gBAACkJ,KAAK,EAAEpH,gBAAgB,CAACwE,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,KAAM;gBAAAlD,QAAA,EACzDtB,gBAAgB,CAACwE,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG;cAAI;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACpBlI,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArI,QAAA,GAC5BtB,gBAAgB,CAACsG,YAAY,EAAC,QACjC;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACpBlI,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArI,QAAA,EAC5B,IAAIsI,IAAI,CAAC5J,gBAAgB,CAAC6J,SAAS,CAAC,CAACC,cAAc,CAAC;YAAC;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACpBlI,OAAA,CAAChB,YAAY,CAACyL,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArI,QAAA,EAC5B,IAAIsI,IAAI,CAAC5J,gBAAgB,CAAC+J,SAAS,CAAC,CAACD,cAAc,CAAC;YAAC;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,gBAEflI,OAAA;YAAKwH,KAAK,EAAE;cAAEuD,SAAS,EAAE,QAAQ;cAAE5C,KAAK,EAAE,MAAM;cAAEO,OAAO,EAAE;YAAS,CAAE;YAAArG,QAAA,EAAC;UAEvE;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlI,OAAA,CAACxB,KAAK;MACJ8I,KAAK,EAAEzG,eAAe,GAAG,MAAM,GAAG,MAAO;MACzCmK,IAAI,EAAErK,cAAe;MACrBsK,QAAQ,EAAEA,CAAA,KAAM;QACdrK,iBAAiB,CAAC,KAAK,CAAC;QACxBO,IAAI,CAACgE,WAAW,CAAC,CAAC;QAClBrE,kBAAkB,CAAC,IAAI,CAAC;QACxBe,gBAAgB,CAAC,CAAC;MACpB,CAAE;MACFqJ,MAAM,EAAE,IAAK;MACbtD,KAAK,EAAE,GAAI;MACXuD,cAAc;MAAA9I,QAAA,GAGb,CAACxB,eAAe,iBACfb,OAAA,CAACd,KAAK;QACJR,OAAO,EACL6C,oBAAoB,GAChB,MAAMA,oBAAoB,CAACkE,IAAI,SAASpE,YAAY,MAAM,GAC1D,QACL;QACD6H,IAAI,EAAC,MAAM;QACX1B,KAAK,EAAE;UAAE4D,YAAY,EAAE;QAAG,CAAE;QAC5BpB,QAAQ;MAAA;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACF,eAGClI,OAAA,CAACvB,IAAI;QACH0C,IAAI,EAAEA,IAAK;QACXkK,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE3E,UAAW;QACrB4E,YAAY,EAAC,KAAK;QAAAlJ,QAAA,gBAElBrC,OAAA,CAACvB,IAAI,CAACgM,IAAI;UACRhF,IAAI,EAAC,MAAM;UACXiF,KAAK,EAAC,0BAAM;UACZc,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE/M,OAAO,EAAE;UAAU,CAAC,EACtC;YAAEqE,GAAG,EAAE,EAAE;YAAErE,OAAO,EAAE;UAAgB,CAAC,CACrC;UAAA2D,QAAA,eAEFrC,OAAA,CAACzB,KAAK;YAACmN,WAAW,EAAC;UAAS;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEdlI,OAAA,CAACvB,IAAI,CAACgM,IAAI;UACRhF,IAAI,EAAC,aAAa;UAClBiF,KAAK,EAAC,0BAAM;UACZc,KAAK,EAAE,CACL;YAAEzI,GAAG,EAAE,GAAG;YAAErE,OAAO,EAAE;UAAiB,CAAC,CACvC;UAAA2D,QAAA,eAEFrC,OAAA,CAACE,QAAQ;YACPwL,WAAW,EAAC,4CAAS;YACrBC,IAAI,EAAE,CAAE;YACRC,SAAS;YACTC,SAAS,EAAE;UAAI;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEVlI,OAAA,CAACpB,GAAG;UAACgL,MAAM,EAAE,EAAG;UAAAvH,QAAA,gBACdrC,OAAA,CAACnB,GAAG;YAACiN,IAAI,EAAE,EAAG;YAAAzJ,QAAA,eACZrC,OAAA,CAACvB,IAAI,CAACgM,IAAI;cACRhF,IAAI,EAAC,WAAW;cAChBiF,KAAK,EAAC,cAAI;cACVqB,OAAO,EAAC,wGAAmB;cAAA1J,QAAA,EAE1BxB,eAAe,gBACdb,OAAA,CAACjB,WAAW;gBACV2M,WAAW,EAAC,oBAAK;gBACjBM,GAAG,EAAE,CAAE;gBACPjJ,GAAG,EAAE,IAAK;gBACVyE,KAAK,EAAE;kBAAEI,KAAK,EAAE;gBAAO;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,gBAEFlI,OAAA;gBAAKwH,KAAK,EAAE;kBACVkB,OAAO,EAAE,UAAU;kBACnBa,MAAM,EAAE,mBAAmB;kBAC3Bd,YAAY,EAAE,KAAK;kBACnBD,eAAe,EAAE,SAAS;kBAC1BL,KAAK,EAAE;gBACT,CAAE;gBAAA9F,QAAA,GAAC,wCACO,EAACP,gBAAgB,CAAC,CAAAP,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEa,EAAE,KAAI,IAAI,CAAC;cAAA;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlI,OAAA,CAACnB,GAAG;YAACiN,IAAI,EAAE,EAAG;YAAAzJ,QAAA,eACZrC,OAAA,CAACvB,IAAI,CAACgM,IAAI;cACRhF,IAAI,EAAC,QAAQ;cACbiF,KAAK,EAAC,cAAI;cACVuB,aAAa,EAAC,SAAS;cACvBC,YAAY,EAAE,IAAK;cAAA7J,QAAA,eAEnBrC,OAAA,CAAClB,MAAM;gBAACqN,eAAe,EAAC,cAAI;gBAACC,iBAAiB,EAAC;cAAI;gBAAArE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlI,OAAA,CAACvB,IAAI,CAACgM,IAAI;UAAChF,IAAI,EAAC,UAAU;UAAC4G,MAAM;UAAAhK,QAAA,eAC/BrC,OAAA,CAACzB,KAAK;YAAAwJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZlI,OAAA,CAACvB,IAAI,CAACgM,IAAI;UAAChF,IAAI,EAAC,OAAO;UAAC4G,MAAM;UAAAhK,QAAA,eAC5BrC,OAAA,CAACzB,KAAK;YAAAwJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGZlI,OAAA,CAACJ,WAAW;UAACsJ,IAAI,EAAC,OAAO;UAACxK,OAAO,EAAE+C,SAAU;UAAC6K,OAAO,EAAE,CAAC,CAAC7K;QAAU;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtElI,OAAA,CAACJ,WAAW;UAACsJ,IAAI,EAAC,SAAS;UAACxK,OAAO,EAAEgD,WAAY;UAAC4K,OAAO,EAAE,CAAC,CAAC5K;QAAY;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE5ElI,OAAA,CAACvB,IAAI,CAACgM,IAAI;UAAApI,QAAA,eACRrC,OAAA,CAAC1B,KAAK;YAACkJ,KAAK,EAAE;cAAEI,KAAK,EAAE,MAAM;cAAED,cAAc,EAAE;YAAW,CAAE;YAAAtF,QAAA,gBAC1DrC,OAAA,CAAC3B,MAAM;cACL2K,OAAO,EAAEA,CAAA,KAAM;gBACbpI,iBAAiB,CAAC,KAAK,CAAC;gBACxBO,IAAI,CAACgE,WAAW,CAAC,CAAC;gBAClBrE,kBAAkB,CAAC,IAAI,CAAC;gBACxBe,gBAAgB,CAAC,CAAC;cACpB,CAAE;cACF0K,QAAQ,EAAE9L,MAAO;cAAA4B,QAAA,EAClB;YAED;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlI,OAAA,CAAC3B,MAAM;cACL6K,IAAI,EAAC,SAAS;cACdsD,QAAQ,EAAC,QAAQ;cACjBjM,OAAO,EAAEE,MAAO;cAChB8L,QAAQ,EAAE9L,MAAO;cAAA4B,QAAA,EAEhB5B,MAAM,GAAG,QAAQ,GAAII,eAAe,GAAG,IAAI,GAAG;YAAK;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9H,EAAA,CA5uBID,kBAA4B;EAAA,QAQjB1B,IAAI,CAAC2C,OAAO,EAWvBvB,cAAc;AAAA;AAAA4M,EAAA,GAnBdtM,kBAA4B;AA8uBlC,eAAeA,kBAAkB;AAAC,IAAAsM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}