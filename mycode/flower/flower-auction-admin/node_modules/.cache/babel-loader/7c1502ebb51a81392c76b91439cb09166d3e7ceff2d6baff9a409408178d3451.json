{"ast": null, "code": "// 36 Hex to reduce the size of the file\nexport default {\n  aliceblue: '9ehhb',\n  antiquewhite: '9sgk7',\n  aqua: '1ekf',\n  aquamarine: '4zsno',\n  azure: '9eiv3',\n  beige: '9lhp8',\n  bisque: '9zg04',\n  black: '0',\n  blanchedalmond: '9zhe5',\n  blue: '73',\n  blueviolet: '5e31e',\n  brown: '6g016',\n  burlywood: '8ouiv',\n  cadetblue: '3qba8',\n  chartreuse: '4zshs',\n  chocolate: '87k0u',\n  coral: '9yvyo',\n  cornflowerblue: '3xael',\n  cornsilk: '9zjz0',\n  crimson: '8l4xo',\n  cyan: '1ekf',\n  darkblue: '3v',\n  darkcyan: 'rkb',\n  darkgoldenrod: '776yz',\n  darkgray: '6mbhl',\n  darkgreen: 'jr4',\n  darkgrey: '6mbhl',\n  darkkhaki: '7ehkb',\n  darkmagenta: '5f91n',\n  darkolivegreen: '3bzfz',\n  darkorange: '9yygw',\n  darkorchid: '5z6x8',\n  darkred: '5f8xs',\n  darksalmon: '9441m',\n  darkseagreen: '5lwgf',\n  darkslateblue: '2th1n',\n  darkslategray: '1ugcv',\n  darkslategrey: '1ugcv',\n  darkturquoise: '14up',\n  darkviolet: '5rw7n',\n  deeppink: '9yavn',\n  deepskyblue: '11xb',\n  dimgray: '442g9',\n  dimgrey: '442g9',\n  dodgerblue: '16xof',\n  firebrick: '6y7tu',\n  floralwhite: '9zkds',\n  forestgreen: '1cisi',\n  fuchsia: '9y70f',\n  gainsboro: '8m8kc',\n  ghostwhite: '9pq0v',\n  goldenrod: '8j4f4',\n  gold: '9zda8',\n  gray: '50i2o',\n  green: 'pa8',\n  greenyellow: '6senj',\n  grey: '50i2o',\n  honeydew: '9eiuo',\n  hotpink: '9yrp0',\n  indianred: '80gnw',\n  indigo: '2xcoy',\n  ivory: '9zldc',\n  khaki: '9edu4',\n  lavenderblush: '9ziet',\n  lavender: '90c8q',\n  lawngreen: '4vk74',\n  lemonchiffon: '9zkct',\n  lightblue: '6s73a',\n  lightcoral: '9dtog',\n  lightcyan: '8s1rz',\n  lightgoldenrodyellow: '9sjiq',\n  lightgray: '89jo3',\n  lightgreen: '5nkwg',\n  lightgrey: '89jo3',\n  lightpink: '9z6wx',\n  lightsalmon: '9z2ii',\n  lightseagreen: '19xgq',\n  lightskyblue: '5arju',\n  lightslategray: '4nwk9',\n  lightslategrey: '4nwk9',\n  lightsteelblue: '6wau6',\n  lightyellow: '9zlcw',\n  lime: '1edc',\n  limegreen: '1zcxe',\n  linen: '9shk6',\n  magenta: '9y70f',\n  maroon: '4zsow',\n  mediumaquamarine: '40eju',\n  mediumblue: '5p',\n  mediumorchid: '79qkz',\n  mediumpurple: '5r3rv',\n  mediumseagreen: '2d9ip',\n  mediumslateblue: '4tcku',\n  mediumspringgreen: '1di2',\n  mediumturquoise: '2uabw',\n  mediumvioletred: '7rn9h',\n  midnightblue: 'z980',\n  mintcream: '9ljp6',\n  mistyrose: '9zg0x',\n  moccasin: '9zfzp',\n  navajowhite: '9zest',\n  navy: '3k',\n  oldlace: '9wq92',\n  olive: '50hz4',\n  olivedrab: '472ub',\n  orange: '9z3eo',\n  orangered: '9ykg0',\n  orchid: '8iu3a',\n  palegoldenrod: '9bl4a',\n  palegreen: '5yw0o',\n  paleturquoise: '6v4ku',\n  palevioletred: '8k8lv',\n  papayawhip: '9zi6t',\n  peachpuff: '9ze0p',\n  peru: '80oqn',\n  pink: '9z8wb',\n  plum: '8nba5',\n  powderblue: '6wgdi',\n  purple: '4zssg',\n  rebeccapurple: '3zk49',\n  red: '9y6tc',\n  rosybrown: '7cv4f',\n  royalblue: '2jvtt',\n  saddlebrown: '5fmkz',\n  salmon: '9rvci',\n  sandybrown: '9jn1c',\n  seagreen: '1tdnb',\n  seashell: '9zje6',\n  sienna: '6973h',\n  silver: '7ir40',\n  skyblue: '5arjf',\n  slateblue: '45e4t',\n  slategray: '4e100',\n  slategrey: '4e100',\n  snow: '9zke2',\n  springgreen: '1egv',\n  steelblue: '2r1kk',\n  tan: '87yx8',\n  teal: 'pds',\n  thistle: '8ggk8',\n  tomato: '9yqfb',\n  turquoise: '2j4r4',\n  violet: '9b10u',\n  wheat: '9ld4j',\n  white: '9zldr',\n  whitesmoke: '9lhpx',\n  yellow: '9zl6o',\n  yellowgreen: '61fzm'\n};", "map": {"version": 3, "names": ["aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "goldenrod", "gold", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavenderblush", "lavender", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/@ant-design/fast-color/es/presetColors.js"], "sourcesContent": ["// 36 Hex to reduce the size of the file\nexport default {\n  aliceblue: '9ehhb',\n  antiquewhite: '9sgk7',\n  aqua: '1ekf',\n  aquamarine: '4zsno',\n  azure: '9eiv3',\n  beige: '9lhp8',\n  bisque: '9zg04',\n  black: '0',\n  blanchedalmond: '9zhe5',\n  blue: '73',\n  blueviolet: '5e31e',\n  brown: '6g016',\n  burlywood: '8ouiv',\n  cadetblue: '3qba8',\n  chartreuse: '4zshs',\n  chocolate: '87k0u',\n  coral: '9yvyo',\n  cornflowerblue: '3xael',\n  cornsilk: '9zjz0',\n  crimson: '8l4xo',\n  cyan: '1ekf',\n  darkblue: '3v',\n  darkcyan: 'rkb',\n  darkgoldenrod: '776yz',\n  darkgray: '6mbhl',\n  darkgreen: 'jr4',\n  darkgrey: '6mbhl',\n  darkkhaki: '7ehkb',\n  darkmagenta: '5f91n',\n  darkolivegreen: '3bzfz',\n  darkorange: '9yygw',\n  darkorchid: '5z6x8',\n  darkred: '5f8xs',\n  darksalmon: '9441m',\n  darkseagreen: '5lwgf',\n  darkslateblue: '2th1n',\n  darkslategray: '1ugcv',\n  darkslategrey: '1ugcv',\n  darkturquoise: '14up',\n  darkviolet: '5rw7n',\n  deeppink: '9yavn',\n  deepskyblue: '11xb',\n  dimgray: '442g9',\n  dimgrey: '442g9',\n  dodgerblue: '16xof',\n  firebrick: '6y7tu',\n  floralwhite: '9zkds',\n  forestgreen: '1cisi',\n  fuchsia: '9y70f',\n  gainsboro: '8m8kc',\n  ghostwhite: '9pq0v',\n  goldenrod: '8j4f4',\n  gold: '9zda8',\n  gray: '50i2o',\n  green: 'pa8',\n  greenyellow: '6senj',\n  grey: '50i2o',\n  honeydew: '9eiuo',\n  hotpink: '9yrp0',\n  indianred: '80gnw',\n  indigo: '2xcoy',\n  ivory: '9zldc',\n  khaki: '9edu4',\n  lavenderblush: '9ziet',\n  lavender: '90c8q',\n  lawngreen: '4vk74',\n  lemonchiffon: '9zkct',\n  lightblue: '6s73a',\n  lightcoral: '9dtog',\n  lightcyan: '8s1rz',\n  lightgoldenrodyellow: '9sjiq',\n  lightgray: '89jo3',\n  lightgreen: '5nkwg',\n  lightgrey: '89jo3',\n  lightpink: '9z6wx',\n  lightsalmon: '9z2ii',\n  lightseagreen: '19xgq',\n  lightskyblue: '5arju',\n  lightslategray: '4nwk9',\n  lightslategrey: '4nwk9',\n  lightsteelblue: '6wau6',\n  lightyellow: '9zlcw',\n  lime: '1edc',\n  limegreen: '1zcxe',\n  linen: '9shk6',\n  magenta: '9y70f',\n  maroon: '4zsow',\n  mediumaquamarine: '40eju',\n  mediumblue: '5p',\n  mediumorchid: '79qkz',\n  mediumpurple: '5r3rv',\n  mediumseagreen: '2d9ip',\n  mediumslateblue: '4tcku',\n  mediumspringgreen: '1di2',\n  mediumturquoise: '2uabw',\n  mediumvioletred: '7rn9h',\n  midnightblue: 'z980',\n  mintcream: '9ljp6',\n  mistyrose: '9zg0x',\n  moccasin: '9zfzp',\n  navajowhite: '9zest',\n  navy: '3k',\n  oldlace: '9wq92',\n  olive: '50hz4',\n  olivedrab: '472ub',\n  orange: '9z3eo',\n  orangered: '9ykg0',\n  orchid: '8iu3a',\n  palegoldenrod: '9bl4a',\n  palegreen: '5yw0o',\n  paleturquoise: '6v4ku',\n  palevioletred: '8k8lv',\n  papayawhip: '9zi6t',\n  peachpuff: '9ze0p',\n  peru: '80oqn',\n  pink: '9z8wb',\n  plum: '8nba5',\n  powderblue: '6wgdi',\n  purple: '4zssg',\n  rebeccapurple: '3zk49',\n  red: '9y6tc',\n  rosybrown: '7cv4f',\n  royalblue: '2jvtt',\n  saddlebrown: '5fmkz',\n  salmon: '9rvci',\n  sandybrown: '9jn1c',\n  seagreen: '1tdnb',\n  seashell: '9zje6',\n  sienna: '6973h',\n  silver: '7ir40',\n  skyblue: '5arjf',\n  slateblue: '45e4t',\n  slategray: '4e100',\n  slategrey: '4e100',\n  snow: '9zke2',\n  springgreen: '1egv',\n  steelblue: '2r1kk',\n  tan: '87yx8',\n  teal: 'pds',\n  thistle: '8ggk8',\n  tomato: '9yqfb',\n  turquoise: '2j4r4',\n  violet: '9b10u',\n  wheat: '9ld4j',\n  white: '9zldr',\n  whitesmoke: '9lhpx',\n  yellow: '9zl6o',\n  yellowgreen: '61fzm'\n};"], "mappings": "AAAA;AACA,eAAe;EACbA,SAAS,EAAE,OAAO;EAClBC,YAAY,EAAE,OAAO;EACrBC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,OAAO;EACnBC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,OAAO;EACfC,KAAK,EAAE,GAAG;EACVC,cAAc,EAAE,OAAO;EACvBC,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE,OAAO;EACnBC,KAAK,EAAE,OAAO;EACdC,SAAS,EAAE,OAAO;EAClBC,SAAS,EAAE,OAAO;EAClBC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,OAAO;EAClBC,KAAK,EAAE,OAAO;EACdC,cAAc,EAAE,OAAO;EACvBC,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,OAAO;EACtBC,QAAQ,EAAE,OAAO;EACjBC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,OAAO;EACjBC,SAAS,EAAE,OAAO;EAClBC,WAAW,EAAE,OAAO;EACpBC,cAAc,EAAE,OAAO;EACvBC,UAAU,EAAE,OAAO;EACnBC,UAAU,EAAE,OAAO;EACnBC,OAAO,EAAE,OAAO;EAChBC,UAAU,EAAE,OAAO;EACnBC,YAAY,EAAE,OAAO;EACrBC,aAAa,EAAE,OAAO;EACtBC,aAAa,EAAE,OAAO;EACtBC,aAAa,EAAE,OAAO;EACtBC,aAAa,EAAE,MAAM;EACrBC,UAAU,EAAE,OAAO;EACnBC,QAAQ,EAAE,OAAO;EACjBC,WAAW,EAAE,MAAM;EACnBC,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,OAAO;EAChBC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,OAAO;EAClBC,WAAW,EAAE,OAAO;EACpBC,WAAW,EAAE,OAAO;EACpBC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,OAAO;EAClBC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,OAAO;EAClBC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE,OAAO;EACpBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,OAAO;EAClBC,MAAM,EAAE,OAAO;EACfC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE,OAAO;EACtBC,QAAQ,EAAE,OAAO;EACjBC,SAAS,EAAE,OAAO;EAClBC,YAAY,EAAE,OAAO;EACrBC,SAAS,EAAE,OAAO;EAClBC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,OAAO;EAClBC,oBAAoB,EAAE,OAAO;EAC7BC,SAAS,EAAE,OAAO;EAClBC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,OAAO;EAClBC,SAAS,EAAE,OAAO;EAClBC,WAAW,EAAE,OAAO;EACpBC,aAAa,EAAE,OAAO;EACtBC,YAAY,EAAE,OAAO;EACrBC,cAAc,EAAE,OAAO;EACvBC,cAAc,EAAE,OAAO;EACvBC,cAAc,EAAE,OAAO;EACvBC,WAAW,EAAE,OAAO;EACpBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,OAAO;EAClBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,OAAO;EAChBC,MAAM,EAAE,OAAO;EACfC,gBAAgB,EAAE,OAAO;EACzBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,OAAO;EACrBC,YAAY,EAAE,OAAO;EACrBC,cAAc,EAAE,OAAO;EACvBC,eAAe,EAAE,OAAO;EACxBC,iBAAiB,EAAE,MAAM;EACzBC,eAAe,EAAE,OAAO;EACxBC,eAAe,EAAE,OAAO;EACxBC,YAAY,EAAE,MAAM;EACpBC,SAAS,EAAE,OAAO;EAClBC,SAAS,EAAE,OAAO;EAClBC,QAAQ,EAAE,OAAO;EACjBC,WAAW,EAAE,OAAO;EACpBC,IAAI,EAAE,IAAI;EACVC,OAAO,EAAE,OAAO;EAChBC,KAAK,EAAE,OAAO;EACdC,SAAS,EAAE,OAAO;EAClBC,MAAM,EAAE,OAAO;EACfC,SAAS,EAAE,OAAO;EAClBC,MAAM,EAAE,OAAO;EACfC,aAAa,EAAE,OAAO;EACtBC,SAAS,EAAE,OAAO;EAClBC,aAAa,EAAE,OAAO;EACtBC,aAAa,EAAE,OAAO;EACtBC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,OAAO;EAClBC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,OAAO;EACbC,UAAU,EAAE,OAAO;EACnBC,MAAM,EAAE,OAAO;EACfC,aAAa,EAAE,OAAO;EACtBC,GAAG,EAAE,OAAO;EACZC,SAAS,EAAE,OAAO;EAClBC,SAAS,EAAE,OAAO;EAClBC,WAAW,EAAE,OAAO;EACpBC,MAAM,EAAE,OAAO;EACfC,UAAU,EAAE,OAAO;EACnBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,OAAO;EACfC,MAAM,EAAE,OAAO;EACfC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,OAAO;EAClBC,SAAS,EAAE,OAAO;EAClBC,SAAS,EAAE,OAAO;EAClBC,IAAI,EAAE,OAAO;EACbC,WAAW,EAAE,MAAM;EACnBC,SAAS,EAAE,OAAO;EAClBC,GAAG,EAAE,OAAO;EACZC,IAAI,EAAE,KAAK;EACXC,OAAO,EAAE,OAAO;EAChBC,MAAM,EAAE,OAAO;EACfC,SAAS,EAAE,OAAO;EAClBC,MAAM,EAAE,OAAO;EACfC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE,OAAO;EACnBC,MAAM,EAAE,OAAO;EACfC,WAAW,EAAE;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}