{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SecuritySettings/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Form, Input, InputNumber, Switch, Button, message, Row, Col, Select, Alert, Table, Tag, Space, Modal, Progress } from 'antd';\nimport { SaveOutlined, ReloadOutlined, SecurityScanOutlined, LockOutlined, EyeOutlined, DeleteOutlined, SafetyOutlined, KeyOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  confirm\n} = Modal;\n\n// 安全配置接口\n\n// 登录记录接口\n\nconst SecuritySettings = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [config, setConfig] = useState({\n    minPasswordLength: 8,\n    requireUppercase: true,\n    requireLowercase: true,\n    requireNumbers: true,\n    requireSpecialChars: false,\n    passwordExpireDays: 90,\n    enableTwoFactor: false,\n    maxLoginAttempts: 5,\n    lockoutDuration: 30,\n    enableCaptcha: true,\n    sessionTimeout: 120,\n    maxConcurrentSessions: 3,\n    enableSessionMonitoring: true,\n    enableIpWhitelist: false,\n    allowedIps: [],\n    enableSecurityLog: true,\n    logRetentionDays: 30\n  });\n  const [loginRecords, setLoginRecords] = useState([]);\n  const [ipModalVisible, setIpModalVisible] = useState(false);\n  const [newIp, setNewIp] = useState('');\n  const [form] = Form.useForm();\n\n  // 获取安全配置\n  const fetchConfig = async () => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API获取配置\n      // const response = await securityService.getConfig();\n      // if (response.success) {\n      //   setConfig(response.data);\n      //   form.setFieldsValue(response.data);\n      // }\n\n      // 暂时使用模拟数据\n      form.setFieldsValue(config);\n      message.success('安全配置加载成功');\n    } catch (error) {\n      console.error('获取安全配置失败:', error);\n      message.error('获取安全配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取登录记录\n  const fetchLoginRecords = async () => {\n    try {\n      // 模拟登录记录数据\n      const mockRecords = [{\n        id: 1,\n        userId: 1,\n        username: 'admin',\n        ip: '*************',\n        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        loginTime: '2023-12-15 09:30:00',\n        status: 'success',\n        location: '北京市'\n      }, {\n        id: 2,\n        userId: 2,\n        username: 'user001',\n        ip: '*************',\n        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n        loginTime: '2023-12-15 08:45:00',\n        status: 'failed',\n        location: '上海市'\n      }];\n      setLoginRecords(mockRecords);\n    } catch (error) {\n      console.error('获取登录记录失败:', error);\n    }\n  };\n  useEffect(() => {\n    fetchConfig();\n    fetchLoginRecords();\n  }, []);\n\n  // 保存配置\n  const handleSave = async values => {\n    setSaving(true);\n    try {\n      const updatedConfig = {\n        ...config,\n        ...values\n      };\n\n      // 这里应该调用后端API保存配置\n      // const response = await securityService.updateConfig(updatedConfig);\n      // if (response.success) {\n      //   setConfig(updatedConfig);\n      //   message.success('安全配置保存成功');\n      // }\n\n      // 暂时使用模拟保存\n      setConfig(updatedConfig);\n      message.success('安全配置保存成功');\n    } catch (error) {\n      console.error('保存安全配置失败:', error);\n      message.error('保存安全配置失败');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 添加IP白名单\n  const handleAddIp = () => {\n    if (!newIp) {\n      message.error('请输入IP地址');\n      return;\n    }\n\n    // 简单的IP格式验证\n    const ipRegex = /^(\\d{1,3}\\.){3}\\d{1,3}$/;\n    if (!ipRegex.test(newIp)) {\n      message.error('请输入有效的IP地址');\n      return;\n    }\n    if (config.allowedIps.includes(newIp)) {\n      message.error('该IP地址已存在');\n      return;\n    }\n    const updatedConfig = {\n      ...config,\n      allowedIps: [...config.allowedIps, newIp]\n    };\n    setConfig(updatedConfig);\n    setNewIp('');\n    setIpModalVisible(false);\n    message.success('IP地址添加成功');\n  };\n\n  // 删除IP白名单\n  const handleRemoveIp = ip => {\n    confirm({\n      title: '确认删除',\n      content: `确定要删除IP地址 ${ip} 吗？`,\n      onOk() {\n        const updatedConfig = {\n          ...config,\n          allowedIps: config.allowedIps.filter(item => item !== ip)\n        };\n        setConfig(updatedConfig);\n        message.success('IP地址删除成功');\n      }\n    });\n  };\n\n  // 计算密码强度\n  const calculatePasswordStrength = () => {\n    let strength = 0;\n    if (config.minPasswordLength >= 8) strength += 20;\n    if (config.requireUppercase) strength += 20;\n    if (config.requireLowercase) strength += 20;\n    if (config.requireNumbers) strength += 20;\n    if (config.requireSpecialChars) strength += 20;\n    return strength;\n  };\n\n  // 登录记录表格列\n  const loginColumns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ip',\n    key: 'ip'\n  }, {\n    title: '登录时间',\n    dataIndex: 'loginTime',\n    key: 'loginTime'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => {\n      const statusMap = {\n        success: {\n          color: 'green',\n          text: '成功'\n        },\n        failed: {\n          color: 'red',\n          text: '失败'\n        },\n        blocked: {\n          color: 'orange',\n          text: '被阻止'\n        }\n      };\n      const statusInfo = statusMap[status];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: statusInfo.color,\n        children: statusInfo.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '位置',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 19\n        }, this),\n        onClick: () => {\n          Modal.info({\n            title: '登录详情',\n            content: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u7528\\u6237\\u540D:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 24\n                }, this), \" \", record.username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"IP\\u5730\\u5740:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 24\n                }, this), \" \", record.ip]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u7528\\u6237\\u4EE3\\u7406:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 24\n                }, this), \" \", record.userAgent]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u767B\\u5F55\\u65F6\\u95F4:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 24\n                }, this), \" \", record.loginTime]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u4F4D\\u7F6E:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 24\n                }, this), \" \", record.location]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this)\n          });\n        },\n        children: \"\\u8BE6\\u60C5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(SecurityScanOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), \" \\u5B89\\u5168\\u8BBE\\u7F6E\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 30\n            }, this), \" \\u5B89\\u5168\\u914D\\u7F6E\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 24\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            onFinish: handleSave,\n            initialValues: config,\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u5BC6\\u7801\\u7B56\\u7565\",\n              description: \"\\u914D\\u7F6E\\u7528\\u6237\\u5BC6\\u7801\\u7684\\u5B89\\u5168\\u8981\\u6C42\",\n              type: \"info\",\n              showIcon: true,\n              style: {\n                marginBottom: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"minPasswordLength\",\n                  label: \"\\u6700\\u5C0F\\u5BC6\\u7801\\u957F\\u5EA6\",\n                  rules: [{\n                    required: true,\n                    message: '请输入最小密码长度'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 6,\n                    max: 20,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"passwordExpireDays\",\n                  label: \"\\u5BC6\\u7801\\u8FC7\\u671F\\u5929\\u6570\",\n                  rules: [{\n                    required: true,\n                    message: '请输入密码过期天数'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 30,\n                    max: 365,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"requireUppercase\",\n                  label: \"\\u9700\\u8981\\u5927\\u5199\\u5B57\\u6BCD\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"requireLowercase\",\n                  label: \"\\u9700\\u8981\\u5C0F\\u5199\\u5B57\\u6BCD\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"requireNumbers\",\n                  label: \"\\u9700\\u8981\\u6570\\u5B57\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"requireSpecialChars\",\n                  label: \"\\u9700\\u8981\\u7279\\u6B8A\\u5B57\\u7B26\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 24\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5BC6\\u7801\\u5F3A\\u5EA6: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                percent: calculatePasswordStrength(),\n                size: \"small\",\n                status: calculatePasswordStrength() >= 80 ? 'success' : 'active',\n                style: {\n                  width: 200,\n                  display: 'inline-block',\n                  marginLeft: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u767B\\u5F55\\u5B89\\u5168\",\n              description: \"\\u914D\\u7F6E\\u767B\\u5F55\\u76F8\\u5173\\u7684\\u5B89\\u5168\\u7B56\\u7565\",\n              type: \"info\",\n              showIcon: true,\n              style: {\n                marginBottom: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"maxLoginAttempts\",\n                  label: \"\\u6700\\u5927\\u767B\\u5F55\\u5C1D\\u8BD5\\u6B21\\u6570\",\n                  rules: [{\n                    required: true,\n                    message: '请输入最大登录尝试次数'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 3,\n                    max: 10,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"lockoutDuration\",\n                  label: \"\\u9501\\u5B9A\\u65F6\\u957F\\uFF08\\u5206\\u949F\\uFF09\",\n                  rules: [{\n                    required: true,\n                    message: '请输入锁定时长'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 5,\n                    max: 1440,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"sessionTimeout\",\n                  label: \"\\u4F1A\\u8BDD\\u8D85\\u65F6\\uFF08\\u5206\\u949F\\uFF09\",\n                  rules: [{\n                    required: true,\n                    message: '请输入会话超时时间'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 30,\n                    max: 480,\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"enableTwoFactor\",\n                  label: \"\\u542F\\u7528\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"enableCaptcha\",\n                  label: \"\\u542F\\u7528\\u9A8C\\u8BC1\\u7801\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"enableSessionMonitoring\",\n                  label: \"\\u542F\\u7528\\u4F1A\\u8BDD\\u76D1\\u63A7\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 27\n                  }, this),\n                  loading: saving,\n                  children: \"\\u4FDD\\u5B58\\u914D\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 27\n                  }, this),\n                  onClick: fetchConfig,\n                  loading: loading,\n                  children: \"\\u91CD\\u65B0\\u52A0\\u8F7D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 26\n            }, this), \" IP\\u767D\\u540D\\u5355\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 20\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"small\",\n            onClick: () => setIpModalVisible(true),\n            children: \"\\u6DFB\\u52A0IP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this),\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u542F\\u7528IP\\u767D\\u540D\\u5355\",\n            style: {\n              marginBottom: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Switch, {\n              checked: config.enableIpWhitelist,\n              onChange: checked => setConfig({\n                ...config,\n                enableIpWhitelist: checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), config.allowedIps.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: config.allowedIps.map((ip, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8,\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                code: true,\n                children: ip\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                danger: true,\n                icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleRemoveIp(ip)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u6682\\u65E0IP\\u767D\\u540D\\u5355\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(KeyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 30\n            }, this), \" \\u5B89\\u5168\\u72B6\\u6001\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 24\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5BC6\\u7801\\u5F3A\\u5EA6: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: calculatePasswordStrength(),\n              size: \"small\",\n              status: calculatePasswordStrength() >= 80 ? 'success' : 'active'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: config.enableTwoFactor ? 'green' : 'red',\n              children: config.enableTwoFactor ? '已启用' : '未启用'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"IP\\u767D\\u540D\\u5355: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: config.enableIpWhitelist ? 'green' : 'red',\n              children: config.enableIpWhitelist ? '已启用' : '未启用'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u4F1A\\u8BDD\\u76D1\\u63A7: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: config.enableSessionMonitoring ? 'green' : 'red',\n              children: config.enableSessionMonitoring ? '已启用' : '未启用'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6700\\u8FD1\\u767B\\u5F55\\u8BB0\\u5F55\",\n      style: {\n        marginTop: 24\n      },\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 19\n        }, this),\n        onClick: fetchLoginRecords,\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: loginColumns,\n        dataSource: loginRecords,\n        rowKey: \"id\",\n        pagination: {\n          pageSize: 10\n        },\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u6DFB\\u52A0IP\\u767D\\u540D\\u5355\",\n      open: ipModalVisible,\n      onOk: handleAddIp,\n      onCancel: () => {\n        setIpModalVisible(false);\n        setNewIp('');\n      },\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1A*************\",\n        value: newIp,\n        onChange: e => setNewIp(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 323,\n    columnNumber: 5\n  }, this);\n};\n_s(SecuritySettings, \"OBRXRMjyQvLdFEGvxBIa8EwUWIk=\", false, function () {\n  return [Form.useForm];\n});\n_c = SecuritySettings;\nexport default SecuritySettings;\nvar _c;\n$RefreshReg$(_c, \"SecuritySettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Form", "Input", "InputNumber", "Switch", "<PERSON><PERSON>", "message", "Row", "Col", "Select", "<PERSON><PERSON>", "Table", "Tag", "Space", "Modal", "Progress", "SaveOutlined", "ReloadOutlined", "SecurityScanOutlined", "LockOutlined", "EyeOutlined", "DeleteOutlined", "SafetyOutlined", "KeyOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "confirm", "SecuritySettings", "_s", "loading", "setLoading", "saving", "setSaving", "config", "setConfig", "min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requireUppercase", "requireLowercase", "requireNumbers", "requireSpecialChars", "passwordExpireDays", "enableTwoFactor", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutDuration", "enableCaptcha", "sessionTimeout", "maxConcurrentSessions", "enableSessionMonitoring", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedIps", "enableSecurityLog", "logRetentionDays", "loginRecords", "setLoginRecords", "ipModalVisible", "setIpModalVisible", "newIp", "setNewIp", "form", "useForm", "fetchConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "success", "error", "console", "fetchLoginRecords", "mockRecords", "id", "userId", "username", "ip", "userAgent", "loginTime", "status", "location", "handleSave", "values", "updatedConfig", "handleAddIp", "ipRegex", "test", "includes", "handleRemoveIp", "title", "content", "onOk", "filter", "item", "calculatePasswordStrength", "strength", "loginColumns", "dataIndex", "key", "render", "statusMap", "color", "text", "failed", "blocked", "statusInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "record", "type", "size", "icon", "onClick", "info", "style", "padding", "level", "gutter", "span", "layout", "onFinish", "initialValues", "description", "showIcon", "marginBottom", "<PERSON><PERSON>", "name", "label", "rules", "required", "min", "max", "width", "valuePropName", "strong", "percent", "display", "marginLeft", "htmlType", "extra", "checked", "onChange", "length", "map", "index", "justifyContent", "alignItems", "code", "danger", "marginTop", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "open", "onCancel", "placeholder", "value", "e", "target", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Settings/SecuritySettings/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Form,\n  Input,\n  InputNumber,\n  Switch,\n  Button,\n  message,\n  Row,\n  Col,\n  Select,\n  Alert,\n  Table,\n  Tag,\n  Space,\n  Modal,\n  Tooltip,\n  Progress,\n} from 'antd';\nimport {\n  SaveOutlined,\n  ReloadOutlined,\n  SecurityScanOutlined,\n  LockOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n  SafetyOutlined,\n  KeyOutlined,\n  LogoutOutlined,\n  UserDeleteOutlined,\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { confirm } = Modal;\n\n// 安全配置接口\ninterface SecurityConfig {\n  // 密码策略\n  minPasswordLength: number;\n  requireUppercase: boolean;\n  requireLowercase: boolean;\n  requireNumbers: boolean;\n  requireSpecialChars: boolean;\n  passwordExpireDays: number;\n  \n  // 登录安全\n  enableTwoFactor: boolean;\n  maxLoginAttempts: number;\n  lockoutDuration: number; // 锁定时长（分钟）\n  enableCaptcha: boolean;\n  \n  // 会话管理\n  sessionTimeout: number; // 会话超时（分钟）\n  maxConcurrentSessions: number;\n  enableSessionMonitoring: boolean;\n  \n  // IP白名单\n  enableIpWhitelist: boolean;\n  allowedIps: string[];\n  \n  // 安全日志\n  enableSecurityLog: boolean;\n  logRetentionDays: number;\n}\n\n// 登录记录接口\ninterface LoginRecord {\n  id: number;\n  userId: number;\n  username: string;\n  ip: string;\n  userAgent: string;\n  loginTime: string;\n  status: 'success' | 'failed' | 'blocked';\n  location?: string;\n}\n\nconst SecuritySettings: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [config, setConfig] = useState<SecurityConfig>({\n    minPasswordLength: 8,\n    requireUppercase: true,\n    requireLowercase: true,\n    requireNumbers: true,\n    requireSpecialChars: false,\n    passwordExpireDays: 90,\n    \n    enableTwoFactor: false,\n    maxLoginAttempts: 5,\n    lockoutDuration: 30,\n    enableCaptcha: true,\n    \n    sessionTimeout: 120,\n    maxConcurrentSessions: 3,\n    enableSessionMonitoring: true,\n    \n    enableIpWhitelist: false,\n    allowedIps: [],\n    \n    enableSecurityLog: true,\n    logRetentionDays: 30,\n  });\n\n  const [loginRecords, setLoginRecords] = useState<LoginRecord[]>([]);\n  const [ipModalVisible, setIpModalVisible] = useState(false);\n  const [newIp, setNewIp] = useState('');\n\n  const [form] = Form.useForm();\n\n  // 获取安全配置\n  const fetchConfig = async () => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API获取配置\n      // const response = await securityService.getConfig();\n      // if (response.success) {\n      //   setConfig(response.data);\n      //   form.setFieldsValue(response.data);\n      // }\n      \n      // 暂时使用模拟数据\n      form.setFieldsValue(config);\n      message.success('安全配置加载成功');\n    } catch (error: any) {\n      console.error('获取安全配置失败:', error);\n      message.error('获取安全配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取登录记录\n  const fetchLoginRecords = async () => {\n    try {\n      // 模拟登录记录数据\n      const mockRecords: LoginRecord[] = [\n        {\n          id: 1,\n          userId: 1,\n          username: 'admin',\n          ip: '*************',\n          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          loginTime: '2023-12-15 09:30:00',\n          status: 'success',\n          location: '北京市',\n        },\n        {\n          id: 2,\n          userId: 2,\n          username: 'user001',\n          ip: '*************',\n          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',\n          loginTime: '2023-12-15 08:45:00',\n          status: 'failed',\n          location: '上海市',\n        },\n      ];\n      setLoginRecords(mockRecords);\n    } catch (error: any) {\n      console.error('获取登录记录失败:', error);\n    }\n  };\n\n  useEffect(() => {\n    fetchConfig();\n    fetchLoginRecords();\n  }, []);\n\n  // 保存配置\n  const handleSave = async (values: any) => {\n    setSaving(true);\n    try {\n      const updatedConfig = { ...config, ...values };\n      \n      // 这里应该调用后端API保存配置\n      // const response = await securityService.updateConfig(updatedConfig);\n      // if (response.success) {\n      //   setConfig(updatedConfig);\n      //   message.success('安全配置保存成功');\n      // }\n      \n      // 暂时使用模拟保存\n      setConfig(updatedConfig);\n      message.success('安全配置保存成功');\n    } catch (error: any) {\n      console.error('保存安全配置失败:', error);\n      message.error('保存安全配置失败');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 添加IP白名单\n  const handleAddIp = () => {\n    if (!newIp) {\n      message.error('请输入IP地址');\n      return;\n    }\n    \n    // 简单的IP格式验证\n    const ipRegex = /^(\\d{1,3}\\.){3}\\d{1,3}$/;\n    if (!ipRegex.test(newIp)) {\n      message.error('请输入有效的IP地址');\n      return;\n    }\n    \n    if (config.allowedIps.includes(newIp)) {\n      message.error('该IP地址已存在');\n      return;\n    }\n    \n    const updatedConfig = {\n      ...config,\n      allowedIps: [...config.allowedIps, newIp],\n    };\n    setConfig(updatedConfig);\n    setNewIp('');\n    setIpModalVisible(false);\n    message.success('IP地址添加成功');\n  };\n\n  // 删除IP白名单\n  const handleRemoveIp = (ip: string) => {\n    confirm({\n      title: '确认删除',\n      content: `确定要删除IP地址 ${ip} 吗？`,\n      onOk() {\n        const updatedConfig = {\n          ...config,\n          allowedIps: config.allowedIps.filter(item => item !== ip),\n        };\n        setConfig(updatedConfig);\n        message.success('IP地址删除成功');\n      },\n    });\n  };\n\n  // 计算密码强度\n  const calculatePasswordStrength = () => {\n    let strength = 0;\n    if (config.minPasswordLength >= 8) strength += 20;\n    if (config.requireUppercase) strength += 20;\n    if (config.requireLowercase) strength += 20;\n    if (config.requireNumbers) strength += 20;\n    if (config.requireSpecialChars) strength += 20;\n    return strength;\n  };\n\n  // 登录记录表格列\n  const loginColumns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ip',\n      key: 'ip',\n    },\n    {\n      title: '登录时间',\n      dataIndex: 'loginTime',\n      key: 'loginTime',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => {\n        const statusMap = {\n          success: { color: 'green', text: '成功' },\n          failed: { color: 'red', text: '失败' },\n          blocked: { color: 'orange', text: '被阻止' },\n        };\n        const statusInfo = statusMap[status as keyof typeof statusMap];\n        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\n      },\n    },\n    {\n      title: '位置',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_: any, record: LoginRecord) => (\n        <Space>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => {\n              Modal.info({\n                title: '登录详情',\n                content: (\n                  <div>\n                    <p><strong>用户名:</strong> {record.username}</p>\n                    <p><strong>IP地址:</strong> {record.ip}</p>\n                    <p><strong>用户代理:</strong> {record.userAgent}</p>\n                    <p><strong>登录时间:</strong> {record.loginTime}</p>\n                    <p><strong>位置:</strong> {record.location}</p>\n                  </div>\n                ),\n              });\n            }}\n          >\n            详情\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>\n        <SecurityScanOutlined /> 安全设置\n      </Title>\n\n      <Row gutter={24}>\n        <Col span={16}>\n          <Card title={<span><LockOutlined /> 安全配置</span>}>\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={handleSave}\n              initialValues={config}\n            >\n              {/* 密码策略 */}\n              <Alert\n                message=\"密码策略\"\n                description=\"配置用户密码的安全要求\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 24 }}\n              />\n              \n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"minPasswordLength\"\n                    label=\"最小密码长度\"\n                    rules={[{ required: true, message: '请输入最小密码长度' }]}\n                  >\n                    <InputNumber\n                      min={6}\n                      max={20}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"passwordExpireDays\"\n                    label=\"密码过期天数\"\n                    rules={[{ required: true, message: '请输入密码过期天数' }]}\n                  >\n                    <InputNumber\n                      min={30}\n                      max={365}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n              </Row>\n              \n              <Row gutter={16}>\n                <Col span={6}>\n                  <Form.Item\n                    name=\"requireUppercase\"\n                    label=\"需要大写字母\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={6}>\n                  <Form.Item\n                    name=\"requireLowercase\"\n                    label=\"需要小写字母\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={6}>\n                  <Form.Item\n                    name=\"requireNumbers\"\n                    label=\"需要数字\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={6}>\n                  <Form.Item\n                    name=\"requireSpecialChars\"\n                    label=\"需要特殊字符\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              {/* 密码强度指示器 */}\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>密码强度: </Text>\n                <Progress\n                  percent={calculatePasswordStrength()}\n                  size=\"small\"\n                  status={calculatePasswordStrength() >= 80 ? 'success' : 'active'}\n                  style={{ width: 200, display: 'inline-block', marginLeft: 8 }}\n                />\n              </div>\n\n              {/* 登录安全 */}\n              <Alert\n                message=\"登录安全\"\n                description=\"配置登录相关的安全策略\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 24 }}\n              />\n              \n              <Row gutter={16}>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"maxLoginAttempts\"\n                    label=\"最大登录尝试次数\"\n                    rules={[{ required: true, message: '请输入最大登录尝试次数' }]}\n                  >\n                    <InputNumber\n                      min={3}\n                      max={10}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"lockoutDuration\"\n                    label=\"锁定时长（分钟）\"\n                    rules={[{ required: true, message: '请输入锁定时长' }]}\n                  >\n                    <InputNumber\n                      min={5}\n                      max={1440}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"sessionTimeout\"\n                    label=\"会话超时（分钟）\"\n                    rules={[{ required: true, message: '请输入会话超时时间' }]}\n                  >\n                    <InputNumber\n                      min={30}\n                      max={480}\n                      style={{ width: '100%' }}\n                    />\n                  </Form.Item>\n                </Col>\n              </Row>\n              \n              <Row gutter={16}>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"enableTwoFactor\"\n                    label=\"启用双因子认证\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"enableCaptcha\"\n                    label=\"启用验证码\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item\n                    name=\"enableSessionMonitoring\"\n                    label=\"启用会话监控\"\n                    valuePropName=\"checked\"\n                  >\n                    <Switch />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item>\n                <Space>\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    icon={<SaveOutlined />}\n                    loading={saving}\n                  >\n                    保存配置\n                  </Button>\n                  <Button\n                    icon={<ReloadOutlined />}\n                    onClick={fetchConfig}\n                    loading={loading}\n                  >\n                    重新加载\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        <Col span={8}>\n          {/* IP白名单管理 */}\n          <Card \n            title={<span><SafetyOutlined /> IP白名单</span>}\n            extra={\n              <Button\n                type=\"primary\"\n                size=\"small\"\n                onClick={() => setIpModalVisible(true)}\n              >\n                添加IP\n              </Button>\n            }\n            style={{ marginBottom: 16 }}\n          >\n            <Form.Item\n              label=\"启用IP白名单\"\n              style={{ marginBottom: 16 }}\n            >\n              <Switch\n                checked={config.enableIpWhitelist}\n                onChange={(checked) => setConfig({ ...config, enableIpWhitelist: checked })}\n              />\n            </Form.Item>\n            \n            {config.allowedIps.length > 0 ? (\n              <div>\n                {config.allowedIps.map((ip, index) => (\n                  <div key={index} style={{ marginBottom: 8, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Text code>{ip}</Text>\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      danger\n                      icon={<DeleteOutlined />}\n                      onClick={() => handleRemoveIp(ip)}\n                    />\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <Text type=\"secondary\">暂无IP白名单</Text>\n            )}\n          </Card>\n\n          {/* 安全状态 */}\n          <Card title={<span><KeyOutlined /> 安全状态</span>}>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>密码强度: </Text>\n              <Progress\n                percent={calculatePasswordStrength()}\n                size=\"small\"\n                status={calculatePasswordStrength() >= 80 ? 'success' : 'active'}\n              />\n            </div>\n            \n            <div style={{ marginBottom: 8 }}>\n              <Text>双因子认证: </Text>\n              <Tag color={config.enableTwoFactor ? 'green' : 'red'}>\n                {config.enableTwoFactor ? '已启用' : '未启用'}\n              </Tag>\n            </div>\n            \n            <div style={{ marginBottom: 8 }}>\n              <Text>IP白名单: </Text>\n              <Tag color={config.enableIpWhitelist ? 'green' : 'red'}>\n                {config.enableIpWhitelist ? '已启用' : '未启用'}\n              </Tag>\n            </div>\n            \n            <div>\n              <Text>会话监控: </Text>\n              <Tag color={config.enableSessionMonitoring ? 'green' : 'red'}>\n                {config.enableSessionMonitoring ? '已启用' : '未启用'}\n              </Tag>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 登录记录 */}\n      <Card \n        title=\"最近登录记录\" \n        style={{ marginTop: 24 }}\n        extra={\n          <Button\n            icon={<ReloadOutlined />}\n            onClick={fetchLoginRecords}\n          >\n            刷新\n          </Button>\n        }\n      >\n        <Table\n          columns={loginColumns}\n          dataSource={loginRecords}\n          rowKey=\"id\"\n          pagination={{ pageSize: 10 }}\n          size=\"small\"\n        />\n      </Card>\n\n      {/* 添加IP模态框 */}\n      <Modal\n        title=\"添加IP白名单\"\n        open={ipModalVisible}\n        onOk={handleAddIp}\n        onCancel={() => {\n          setIpModalVisible(false);\n          setNewIp('');\n        }}\n      >\n        <Input\n          placeholder=\"请输入IP地址，例如：*************\"\n          value={newIp}\n          onChange={(e) => setNewIp(e.target.value)}\n        />\n      </Modal>\n    </div>\n  );\n};\n\nexport default SecuritySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,KAAK,EAELC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,oBAAoB,EACpBC,YAAY,EACZC,WAAW,EACXC,cAAc,EAEdC,cAAc,EACdC,WAAW,QAGN,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG3B,UAAU;AAClC,MAAM;EAAE4B;AAAS,CAAC,GAAG1B,KAAK;AAC1B,MAAM;EAAE2B;AAAO,CAAC,GAAGpB,MAAM;AACzB,MAAM;EAAEqB;AAAQ,CAAC,GAAGhB,KAAK;;AAEzB;;AA8BA;;AAYA,MAAMiB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAAiB;IACnD0C,iBAAiB,EAAE,CAAC;IACpBC,gBAAgB,EAAE,IAAI;IACtBC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,mBAAmB,EAAE,KAAK;IAC1BC,kBAAkB,EAAE,EAAE;IAEtBC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE,EAAE;IACnBC,aAAa,EAAE,IAAI;IAEnBC,cAAc,EAAE,GAAG;IACnBC,qBAAqB,EAAE,CAAC;IACxBC,uBAAuB,EAAE,IAAI;IAE7BC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,EAAE;IAEdC,iBAAiB,EAAE,IAAI;IACvBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+D,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM,CAACiE,IAAI,CAAC,GAAG7D,IAAI,CAAC8D,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B9B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA4B,IAAI,CAACG,cAAc,CAAC5B,MAAM,CAAC;MAC3B/B,OAAO,CAAC4D,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC7D,OAAO,CAAC6D,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF;MACA,MAAMC,WAA0B,GAAG,CACjC;QACEC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,OAAO;QACjBC,EAAE,EAAE,eAAe;QACnBC,SAAS,EAAE,8DAA8D;QACzEC,SAAS,EAAE,qBAAqB;QAChCC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEP,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,SAAS;QACnBC,EAAE,EAAE,eAAe;QACnBC,SAAS,EAAE,oEAAoE;QAC/EC,SAAS,EAAE,qBAAqB;QAChCC,MAAM,EAAE,QAAQ;QAChBC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDrB,eAAe,CAACa,WAAW,CAAC;IAC9B,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAEDrE,SAAS,CAAC,MAAM;IACdkE,WAAW,CAAC,CAAC;IACbK,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,UAAU,GAAG,MAAOC,MAAW,IAAK;IACxC5C,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF,MAAM6C,aAAa,GAAG;QAAE,GAAG5C,MAAM;QAAE,GAAG2C;MAAO,CAAC;;MAE9C;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA1C,SAAS,CAAC2C,aAAa,CAAC;MACxB3E,OAAO,CAAC4D,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC7D,OAAO,CAAC6D,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR/B,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM8C,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACtB,KAAK,EAAE;MACVtD,OAAO,CAAC6D,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;;IAEA;IACA,MAAMgB,OAAO,GAAG,yBAAyB;IACzC,IAAI,CAACA,OAAO,CAACC,IAAI,CAACxB,KAAK,CAAC,EAAE;MACxBtD,OAAO,CAAC6D,KAAK,CAAC,YAAY,CAAC;MAC3B;IACF;IAEA,IAAI9B,MAAM,CAACgB,UAAU,CAACgC,QAAQ,CAACzB,KAAK,CAAC,EAAE;MACrCtD,OAAO,CAAC6D,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,MAAMc,aAAa,GAAG;MACpB,GAAG5C,MAAM;MACTgB,UAAU,EAAE,CAAC,GAAGhB,MAAM,CAACgB,UAAU,EAAEO,KAAK;IAC1C,CAAC;IACDtB,SAAS,CAAC2C,aAAa,CAAC;IACxBpB,QAAQ,CAAC,EAAE,CAAC;IACZF,iBAAiB,CAAC,KAAK,CAAC;IACxBrD,OAAO,CAAC4D,OAAO,CAAC,UAAU,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMoB,cAAc,GAAIZ,EAAU,IAAK;IACrC5C,OAAO,CAAC;MACNyD,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,aAAad,EAAE,KAAK;MAC7Be,IAAIA,CAAA,EAAG;QACL,MAAMR,aAAa,GAAG;UACpB,GAAG5C,MAAM;UACTgB,UAAU,EAAEhB,MAAM,CAACgB,UAAU,CAACqC,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKjB,EAAE;QAC1D,CAAC;QACDpC,SAAS,CAAC2C,aAAa,CAAC;QACxB3E,OAAO,CAAC4D,OAAO,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0B,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIxD,MAAM,CAACE,iBAAiB,IAAI,CAAC,EAAEsD,QAAQ,IAAI,EAAE;IACjD,IAAIxD,MAAM,CAACG,gBAAgB,EAAEqD,QAAQ,IAAI,EAAE;IAC3C,IAAIxD,MAAM,CAACI,gBAAgB,EAAEoD,QAAQ,IAAI,EAAE;IAC3C,IAAIxD,MAAM,CAACK,cAAc,EAAEmD,QAAQ,IAAI,EAAE;IACzC,IAAIxD,MAAM,CAACM,mBAAmB,EAAEkD,QAAQ,IAAI,EAAE;IAC9C,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IACEP,KAAK,EAAE,KAAK;IACZQ,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACET,KAAK,EAAE,MAAM;IACbQ,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE;EACP,CAAC,EACD;IACET,KAAK,EAAE,MAAM;IACbQ,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACET,KAAK,EAAE,IAAI;IACXQ,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGpB,MAAc,IAAK;MAC1B,MAAMqB,SAAS,GAAG;QAChBhC,OAAO,EAAE;UAAEiC,KAAK,EAAE,OAAO;UAAEC,IAAI,EAAE;QAAK,CAAC;QACvCC,MAAM,EAAE;UAAEF,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE;QAAK,CAAC;QACpCE,OAAO,EAAE;UAAEH,KAAK,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAM;MAC1C,CAAC;MACD,MAAMG,UAAU,GAAGL,SAAS,CAACrB,MAAM,CAA2B;MAC9D,oBAAOpD,OAAA,CAACb,GAAG;QAACuF,KAAK,EAAEI,UAAU,CAACJ,KAAM;QAAAK,QAAA,EAAED,UAAU,CAACH;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC9D;EACF,CAAC,EACD;IACErB,KAAK,EAAE,IAAI;IACXQ,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACET,KAAK,EAAE,IAAI;IACXS,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACY,CAAM,EAAEC,MAAmB,kBAClCrF,OAAA,CAACZ,KAAK;MAAA2F,QAAA,eACJ/E,OAAA,CAACpB,MAAM;QACL0G,IAAI,EAAC,MAAM;QACXC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAExF,OAAA,CAACL,WAAW;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBM,OAAO,EAAEA,CAAA,KAAM;UACbpG,KAAK,CAACqG,IAAI,CAAC;YACT5B,KAAK,EAAE,MAAM;YACbC,OAAO,eACL/D,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAA+E,QAAA,gBAAG/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACE,MAAM,CAACrC,QAAQ;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CnF,OAAA;gBAAA+E,QAAA,gBAAG/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACE,MAAM,CAACpC,EAAE;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzCnF,OAAA;gBAAA+E,QAAA,gBAAG/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACE,MAAM,CAACnC,SAAS;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDnF,OAAA;gBAAA+E,QAAA,gBAAG/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACE,MAAM,CAAClC,SAAS;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDnF,OAAA;gBAAA+E,QAAA,gBAAG/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACE,MAAM,CAAChC,QAAQ;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAET,CAAC,CAAC;QACJ,CAAE;QAAAJ,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,oBACEnF,OAAA;IAAK2F,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAb,QAAA,gBAC1B/E,OAAA,CAACC,KAAK;MAAC4F,KAAK,EAAE,CAAE;MAAAd,QAAA,gBACd/E,OAAA,CAACP,oBAAoB;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BAC1B;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERnF,OAAA,CAAClB,GAAG;MAACgH,MAAM,EAAE,EAAG;MAAAf,QAAA,gBACd/E,OAAA,CAACjB,GAAG;QAACgH,IAAI,EAAE,EAAG;QAAAhB,QAAA,eACZ/E,OAAA,CAAC1B,IAAI;UAACwF,KAAK,eAAE9D,OAAA;YAAA+E,QAAA,gBAAM/E,OAAA,CAACN,YAAY;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAAK;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAJ,QAAA,eAC9C/E,OAAA,CAACxB,IAAI;YACH6D,IAAI,EAAEA,IAAK;YACX2D,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAE3C,UAAW;YACrB4C,aAAa,EAAEtF,MAAO;YAAAmE,QAAA,gBAGtB/E,OAAA,CAACf,KAAK;cACJJ,OAAO,EAAC,0BAAM;cACdsH,WAAW,EAAC,oEAAa;cACzBb,IAAI,EAAC,MAAM;cACXc,QAAQ;cACRT,KAAK,EAAE;gBAAEU,YAAY,EAAE;cAAG;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eAEFnF,OAAA,CAAClB,GAAG;cAACgH,MAAM,EAAE,EAAG;cAAAf,QAAA,gBACd/E,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,EAAG;gBAAAhB,QAAA,eACZ/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,mBAAmB;kBACxBC,KAAK,EAAC,sCAAQ;kBACdC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE7H,OAAO,EAAE;kBAAY,CAAC,CAAE;kBAAAkG,QAAA,eAElD/E,OAAA,CAACtB,WAAW;oBACViI,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,EAAG;oBACRjB,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAO;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,EAAG;gBAAAhB,QAAA,eACZ/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,oBAAoB;kBACzBC,KAAK,EAAC,sCAAQ;kBACdC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE7H,OAAO,EAAE;kBAAY,CAAC,CAAE;kBAAAkG,QAAA,eAElD/E,OAAA,CAACtB,WAAW;oBACViI,GAAG,EAAE,EAAG;oBACRC,GAAG,EAAE,GAAI;oBACTjB,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAO;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnF,OAAA,CAAClB,GAAG;cAACgH,MAAM,EAAE,EAAG;cAAAf,QAAA,gBACd/E,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAC,sCAAQ;kBACdM,aAAa,EAAC,SAAS;kBAAA/B,QAAA,eAEvB/E,OAAA,CAACrB,MAAM;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAC,sCAAQ;kBACdM,aAAa,EAAC,SAAS;kBAAA/B,QAAA,eAEvB/E,OAAA,CAACrB,MAAM;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAC,0BAAM;kBACZM,aAAa,EAAC,SAAS;kBAAA/B,QAAA,eAEvB/E,OAAA,CAACrB,MAAM;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAC,sCAAQ;kBACdM,aAAa,EAAC,SAAS;kBAAA/B,QAAA,eAEvB/E,OAAA,CAACrB,MAAM;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnF,OAAA;cAAK2F,KAAK,EAAE;gBAAEU,YAAY,EAAE;cAAG,CAAE;cAAAtB,QAAA,gBAC/B/E,OAAA,CAACE,IAAI;gBAAC6G,MAAM;gBAAAhC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BnF,OAAA,CAACV,QAAQ;gBACP0H,OAAO,EAAE7C,yBAAyB,CAAC,CAAE;gBACrCoB,IAAI,EAAC,OAAO;gBACZnC,MAAM,EAAEe,yBAAyB,CAAC,CAAC,IAAI,EAAE,GAAG,SAAS,GAAG,QAAS;gBACjEwB,KAAK,EAAE;kBAAEkB,KAAK,EAAE,GAAG;kBAAEI,OAAO,EAAE,cAAc;kBAAEC,UAAU,EAAE;gBAAE;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNnF,OAAA,CAACf,KAAK;cACJJ,OAAO,EAAC,0BAAM;cACdsH,WAAW,EAAC,oEAAa;cACzBb,IAAI,EAAC,MAAM;cACXc,QAAQ;cACRT,KAAK,EAAE;gBAAEU,YAAY,EAAE;cAAG;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eAEFnF,OAAA,CAAClB,GAAG;cAACgH,MAAM,EAAE,EAAG;cAAAf,QAAA,gBACd/E,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAC,kDAAU;kBAChBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE7H,OAAO,EAAE;kBAAc,CAAC,CAAE;kBAAAkG,QAAA,eAEpD/E,OAAA,CAACtB,WAAW;oBACViI,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,EAAG;oBACRjB,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAO;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,iBAAiB;kBACtBC,KAAK,EAAC,kDAAU;kBAChBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE7H,OAAO,EAAE;kBAAU,CAAC,CAAE;kBAAAkG,QAAA,eAEhD/E,OAAA,CAACtB,WAAW;oBACViI,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,IAAK;oBACVjB,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAO;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAC,kDAAU;kBAChBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE7H,OAAO,EAAE;kBAAY,CAAC,CAAE;kBAAAkG,QAAA,eAElD/E,OAAA,CAACtB,WAAW;oBACViI,GAAG,EAAE,EAAG;oBACRC,GAAG,EAAE,GAAI;oBACTjB,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAO;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnF,OAAA,CAAClB,GAAG;cAACgH,MAAM,EAAE,EAAG;cAAAf,QAAA,gBACd/E,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,iBAAiB;kBACtBC,KAAK,EAAC,4CAAS;kBACfM,aAAa,EAAC,SAAS;kBAAA/B,QAAA,eAEvB/E,OAAA,CAACrB,MAAM;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,eAAe;kBACpBC,KAAK,EAAC,gCAAO;kBACbM,aAAa,EAAC,SAAS;kBAAA/B,QAAA,eAEvB/E,OAAA,CAACrB,MAAM;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNnF,OAAA,CAACjB,GAAG;gBAACgH,IAAI,EAAE,CAAE;gBAAAhB,QAAA,eACX/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;kBACRC,IAAI,EAAC,yBAAyB;kBAC9BC,KAAK,EAAC,sCAAQ;kBACdM,aAAa,EAAC,SAAS;kBAAA/B,QAAA,eAEvB/E,OAAA,CAACrB,MAAM;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnF,OAAA,CAACxB,IAAI,CAAC8H,IAAI;cAAAvB,QAAA,eACR/E,OAAA,CAACZ,KAAK;gBAAA2F,QAAA,gBACJ/E,OAAA,CAACpB,MAAM;kBACL0G,IAAI,EAAC,SAAS;kBACd6B,QAAQ,EAAC,QAAQ;kBACjB3B,IAAI,eAAExF,OAAA,CAACT,YAAY;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvB3E,OAAO,EAAEE,MAAO;kBAAAqE,QAAA,EACjB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnF,OAAA,CAACpB,MAAM;kBACL4G,IAAI,eAAExF,OAAA,CAACR,cAAc;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBM,OAAO,EAAElD,WAAY;kBACrB/B,OAAO,EAAEA,OAAQ;kBAAAuE,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENnF,OAAA,CAACjB,GAAG;QAACgH,IAAI,EAAE,CAAE;QAAAhB,QAAA,gBAEX/E,OAAA,CAAC1B,IAAI;UACHwF,KAAK,eAAE9D,OAAA;YAAA+E,QAAA,gBAAM/E,OAAA,CAACH,cAAc;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAAM;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAC7CiC,KAAK,eACHpH,OAAA,CAACpB,MAAM;YACL0G,IAAI,EAAC,SAAS;YACdC,IAAI,EAAC,OAAO;YACZE,OAAO,EAAEA,CAAA,KAAMvD,iBAAiB,CAAC,IAAI,CAAE;YAAA6C,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UACDQ,KAAK,EAAE;YAAEU,YAAY,EAAE;UAAG,CAAE;UAAAtB,QAAA,gBAE5B/E,OAAA,CAACxB,IAAI,CAAC8H,IAAI;YACRE,KAAK,EAAC,kCAAS;YACfb,KAAK,EAAE;cAAEU,YAAY,EAAE;YAAG,CAAE;YAAAtB,QAAA,eAE5B/E,OAAA,CAACrB,MAAM;cACL0I,OAAO,EAAEzG,MAAM,CAACe,iBAAkB;cAClC2F,QAAQ,EAAGD,OAAO,IAAKxG,SAAS,CAAC;gBAAE,GAAGD,MAAM;gBAAEe,iBAAiB,EAAE0F;cAAQ,CAAC;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,EAEXvE,MAAM,CAACgB,UAAU,CAAC2F,MAAM,GAAG,CAAC,gBAC3BvH,OAAA;YAAA+E,QAAA,EACGnE,MAAM,CAACgB,UAAU,CAAC4F,GAAG,CAAC,CAACvE,EAAE,EAAEwE,KAAK,kBAC/BzH,OAAA;cAAiB2F,KAAK,EAAE;gBAAEU,YAAY,EAAE,CAAC;gBAAEY,OAAO,EAAE,MAAM;gBAAES,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAA5C,QAAA,gBAClH/E,OAAA,CAACE,IAAI;gBAAC0H,IAAI;gBAAA7C,QAAA,EAAE9B;cAAE;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBnF,OAAA,CAACpB,MAAM;gBACL0G,IAAI,EAAC,MAAM;gBACXC,IAAI,EAAC,OAAO;gBACZsC,MAAM;gBACNrC,IAAI,eAAExF,OAAA,CAACJ,cAAc;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBM,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAACZ,EAAE;cAAE;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA,GARMsC,KAAK;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENnF,OAAA,CAACE,IAAI;YAACoF,IAAI,EAAC,WAAW;YAAAP,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACrC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGPnF,OAAA,CAAC1B,IAAI;UAACwF,KAAK,eAAE9D,OAAA;YAAA+E,QAAA,gBAAM/E,OAAA,CAACF,WAAW;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAAK;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAE;UAAAJ,QAAA,gBAC7C/E,OAAA;YAAK2F,KAAK,EAAE;cAAEU,YAAY,EAAE;YAAG,CAAE;YAAAtB,QAAA,gBAC/B/E,OAAA,CAACE,IAAI;cAAC6G,MAAM;cAAAhC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BnF,OAAA,CAACV,QAAQ;cACP0H,OAAO,EAAE7C,yBAAyB,CAAC,CAAE;cACrCoB,IAAI,EAAC,OAAO;cACZnC,MAAM,EAAEe,yBAAyB,CAAC,CAAC,IAAI,EAAE,GAAG,SAAS,GAAG;YAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnF,OAAA;YAAK2F,KAAK,EAAE;cAAEU,YAAY,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAC9B/E,OAAA,CAACE,IAAI;cAAA6E,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpBnF,OAAA,CAACb,GAAG;cAACuF,KAAK,EAAE9D,MAAM,CAACQ,eAAe,GAAG,OAAO,GAAG,KAAM;cAAA2D,QAAA,EAClDnE,MAAM,CAACQ,eAAe,GAAG,KAAK,GAAG;YAAK;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnF,OAAA;YAAK2F,KAAK,EAAE;cAAEU,YAAY,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAC9B/E,OAAA,CAACE,IAAI;cAAA6E,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpBnF,OAAA,CAACb,GAAG;cAACuF,KAAK,EAAE9D,MAAM,CAACe,iBAAiB,GAAG,OAAO,GAAG,KAAM;cAAAoD,QAAA,EACpDnE,MAAM,CAACe,iBAAiB,GAAG,KAAK,GAAG;YAAK;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnF,OAAA;YAAA+E,QAAA,gBACE/E,OAAA,CAACE,IAAI;cAAA6E,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBnF,OAAA,CAACb,GAAG;cAACuF,KAAK,EAAE9D,MAAM,CAACc,uBAAuB,GAAG,OAAO,GAAG,KAAM;cAAAqD,QAAA,EAC1DnE,MAAM,CAACc,uBAAuB,GAAG,KAAK,GAAG;YAAK;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA,CAAC1B,IAAI;MACHwF,KAAK,EAAC,sCAAQ;MACd6B,KAAK,EAAE;QAAEmC,SAAS,EAAE;MAAG,CAAE;MACzBV,KAAK,eACHpH,OAAA,CAACpB,MAAM;QACL4G,IAAI,eAAExF,OAAA,CAACR,cAAc;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBM,OAAO,EAAE7C,iBAAkB;QAAAmC,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAJ,QAAA,eAED/E,OAAA,CAACd,KAAK;QACJ6I,OAAO,EAAE1D,YAAa;QACtB2D,UAAU,EAAEjG,YAAa;QACzBkG,MAAM,EAAC,IAAI;QACXC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAC7B5C,IAAI,EAAC;MAAO;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnF,OAAA,CAACX,KAAK;MACJyE,KAAK,EAAC,kCAAS;MACfsE,IAAI,EAAEnG,cAAe;MACrB+B,IAAI,EAAEP,WAAY;MAClB4E,QAAQ,EAAEA,CAAA,KAAM;QACdnG,iBAAiB,CAAC,KAAK,CAAC;QACxBE,QAAQ,CAAC,EAAE,CAAC;MACd,CAAE;MAAA2C,QAAA,eAEF/E,OAAA,CAACvB,KAAK;QACJ6J,WAAW,EAAC,uEAA0B;QACtCC,KAAK,EAAEpG,KAAM;QACbmF,QAAQ,EAAGkB,CAAC,IAAKpG,QAAQ,CAACoG,CAAC,CAACC,MAAM,CAACF,KAAK;MAAE;QAAAvD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAtjBID,gBAA0B;EAAA,QA+Bf9B,IAAI,CAAC8D,OAAO;AAAA;AAAAoG,EAAA,GA/BvBpI,gBAA0B;AAwjBhC,eAAeA,gBAAgB;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}