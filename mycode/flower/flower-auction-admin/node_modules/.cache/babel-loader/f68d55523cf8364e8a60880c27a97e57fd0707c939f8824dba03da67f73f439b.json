{"ast": null, "code": "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n\n/**\n * @param {(string | number)[]} updatedModules updated modules\n * @param {(string | number)[] | null} renewedModules renewed modules\n */\nmodule.exports = function (updatedModules, renewedModules) {\n  var unacceptedModules = updatedModules.filter(function (moduleId) {\n    return renewedModules && renewedModules.indexOf(moduleId) < 0;\n  });\n  var log = require(\"./log\");\n  if (unacceptedModules.length > 0) {\n    log(\"warning\", \"[HMR] The following modules couldn't be hot updated: (They would need a full reload!)\");\n    unacceptedModules.forEach(function (moduleId) {\n      log(\"warning\", \"[HMR]  - \" + moduleId);\n    });\n  }\n  if (!renewedModules || renewedModules.length === 0) {\n    log(\"info\", \"[HMR] Nothing hot updated.\");\n  } else {\n    log(\"info\", \"[HMR] Updated modules:\");\n    renewedModules.forEach(function (moduleId) {\n      if (typeof moduleId === \"string\" && moduleId.indexOf(\"!\") !== -1) {\n        var parts = moduleId.split(\"!\");\n        log.groupCollapsed(\"info\", \"[HMR]  - \" + parts.pop());\n        log(\"info\", \"[HMR]  - \" + moduleId);\n        log.groupEnd(\"info\");\n      } else {\n        log(\"info\", \"[HMR]  - \" + moduleId);\n      }\n    });\n    var numberIds = renewedModules.every(function (moduleId) {\n      return typeof moduleId === \"number\";\n    });\n    if (numberIds) log(\"info\", '[HMR] Consider using the optimization.moduleIds: \"named\" for module names.');\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "updatedModules", "renewedModules", "unacceptedModules", "filter", "moduleId", "indexOf", "log", "require", "length", "for<PERSON>ach", "parts", "split", "groupCollapsed", "pop", "groupEnd", "numberIds", "every"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/webpack/hot/log-apply-result.js"], "sourcesContent": ["/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n\n/**\n * @param {(string | number)[]} updatedModules updated modules\n * @param {(string | number)[] | null} renewedModules renewed modules\n */\nmodule.exports = function (updatedModules, renewedModules) {\n\tvar unacceptedModules = updatedModules.filter(function (moduleId) {\n\t\treturn renewedModules && renewedModules.indexOf(moduleId) < 0;\n\t});\n\tvar log = require(\"./log\");\n\n\tif (unacceptedModules.length > 0) {\n\t\tlog(\n\t\t\t\"warning\",\n\t\t\t\"[HMR] The following modules couldn't be hot updated: (They would need a full reload!)\"\n\t\t);\n\t\tunacceptedModules.forEach(function (moduleId) {\n\t\t\tlog(\"warning\", \"[HMR]  - \" + moduleId);\n\t\t});\n\t}\n\n\tif (!renewedModules || renewedModules.length === 0) {\n\t\tlog(\"info\", \"[HMR] Nothing hot updated.\");\n\t} else {\n\t\tlog(\"info\", \"[HMR] Updated modules:\");\n\t\trenewedModules.forEach(function (moduleId) {\n\t\t\tif (typeof moduleId === \"string\" && moduleId.indexOf(\"!\") !== -1) {\n\t\t\t\tvar parts = moduleId.split(\"!\");\n\t\t\t\tlog.groupCollapsed(\"info\", \"[HMR]  - \" + parts.pop());\n\t\t\t\tlog(\"info\", \"[HMR]  - \" + moduleId);\n\t\t\t\tlog.groupEnd(\"info\");\n\t\t\t} else {\n\t\t\t\tlog(\"info\", \"[HMR]  - \" + moduleId);\n\t\t\t}\n\t\t});\n\t\tvar numberIds = renewedModules.every(function (moduleId) {\n\t\t\treturn typeof moduleId === \"number\";\n\t\t});\n\t\tif (numberIds)\n\t\t\tlog(\n\t\t\t\t\"info\",\n\t\t\t\t'[HMR] Consider using the optimization.moduleIds: \"named\" for module names.'\n\t\t\t);\n\t}\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,UAAUC,cAAc,EAAEC,cAAc,EAAE;EAC1D,IAAIC,iBAAiB,GAAGF,cAAc,CAACG,MAAM,CAAC,UAAUC,QAAQ,EAAE;IACjE,OAAOH,cAAc,IAAIA,cAAc,CAACI,OAAO,CAACD,QAAQ,CAAC,GAAG,CAAC;EAC9D,CAAC,CAAC;EACF,IAAIE,GAAG,GAAGC,OAAO,CAAC,OAAO,CAAC;EAE1B,IAAIL,iBAAiB,CAACM,MAAM,GAAG,CAAC,EAAE;IACjCF,GAAG,CACF,SAAS,EACT,uFACD,CAAC;IACDJ,iBAAiB,CAACO,OAAO,CAAC,UAAUL,QAAQ,EAAE;MAC7CE,GAAG,CAAC,SAAS,EAAE,WAAW,GAAGF,QAAQ,CAAC;IACvC,CAAC,CAAC;EACH;EAEA,IAAI,CAACH,cAAc,IAAIA,cAAc,CAACO,MAAM,KAAK,CAAC,EAAE;IACnDF,GAAG,CAAC,MAAM,EAAE,4BAA4B,CAAC;EAC1C,CAAC,MAAM;IACNA,GAAG,CAAC,MAAM,EAAE,wBAAwB,CAAC;IACrCL,cAAc,CAACQ,OAAO,CAAC,UAAUL,QAAQ,EAAE;MAC1C,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACjE,IAAIK,KAAK,GAAGN,QAAQ,CAACO,KAAK,CAAC,GAAG,CAAC;QAC/BL,GAAG,CAACM,cAAc,CAAC,MAAM,EAAE,WAAW,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC,CAAC;QACrDP,GAAG,CAAC,MAAM,EAAE,WAAW,GAAGF,QAAQ,CAAC;QACnCE,GAAG,CAACQ,QAAQ,CAAC,MAAM,CAAC;MACrB,CAAC,MAAM;QACNR,GAAG,CAAC,MAAM,EAAE,WAAW,GAAGF,QAAQ,CAAC;MACpC;IACD,CAAC,CAAC;IACF,IAAIW,SAAS,GAAGd,cAAc,CAACe,KAAK,CAAC,UAAUZ,QAAQ,EAAE;MACxD,OAAO,OAAOA,QAAQ,KAAK,QAAQ;IACpC,CAAC,CAAC;IACF,IAAIW,SAAS,EACZT,GAAG,CACF,MAAM,EACN,4EACD,CAAC;EACH;AACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}