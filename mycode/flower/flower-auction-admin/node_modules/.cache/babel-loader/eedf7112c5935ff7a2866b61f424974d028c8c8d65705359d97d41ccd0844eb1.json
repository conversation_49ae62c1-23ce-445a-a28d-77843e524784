{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Typography, Row, Col, Modal, Statistic, Avatar, Tooltip, Badge, message } from 'antd';\nimport { SearchOutlined, ReloadOutlined, ExportOutlined, UserOutlined, TrophyOutlined, DollarOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { auctionService } from '../../../services/auctionService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\n\n// 竞价记录接口\n\n// 查询参数接口\n\nconst BidRecords = () => {\n  _s();\n  var _bidStatusMap$selecte, _bidStatusMap$selecte2;\n  const [bidRecords, setBidRecords] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [statistics, setStatistics] = useState({\n    totalBids: 0,\n    totalAmount: 0,\n    successfulBids: 0,\n    activeUsers: 0\n  });\n  const [selectedRecord, setSelectedRecord] = useState(null);\n  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);\n  const [searchForm, setSearchForm] = useState({\n    productName: '',\n    username: '',\n    status: undefined\n  });\n\n  // 竞价状态映射\n  const bidStatusMap = {\n    active: {\n      label: '当前最高',\n      color: 'blue'\n    },\n    outbid: {\n      label: '已被超越',\n      color: 'default'\n    },\n    winning: {\n      label: '领先中',\n      color: 'orange'\n    },\n    won: {\n      label: '竞拍成功',\n      color: 'green'\n    },\n    lost: {\n      label: '竞拍失败',\n      color: 'red'\n    }\n  };\n\n  // 模拟数据\n  const mockBidRecords = [{\n    id: 1,\n    auctionId: 1,\n    auctionTitle: '春季花卉拍卖会',\n    productId: 1,\n    productName: '荷兰郁金香 - 红色经典',\n    productCode: 'TLP-001',\n    userId: 1,\n    username: 'flower_lover',\n    bidAmount: 350.00,\n    bidTime: '2024-01-15 14:30:25',\n    isWinning: true,\n    isSuccessful: false,\n    status: 'winning',\n    bidIncrement: 10.00,\n    previousPrice: 340.00\n  }, {\n    id: 2,\n    auctionId: 1,\n    auctionTitle: '春季花卉拍卖会',\n    productId: 1,\n    productName: '荷兰郁金香 - 红色经典',\n    productCode: 'TLP-001',\n    userId: 2,\n    username: 'garden_master',\n    bidAmount: 340.00,\n    bidTime: '2024-01-15 14:29:15',\n    isWinning: false,\n    isSuccessful: false,\n    status: 'outbid',\n    bidIncrement: 10.00,\n    previousPrice: 330.00\n  }, {\n    id: 3,\n    auctionId: 2,\n    auctionTitle: '精品玫瑰专场',\n    productId: 2,\n    productName: '法国玫瑰 - 香槟色',\n    productCode: 'RSE-002',\n    userId: 3,\n    username: 'rose_collector',\n    bidAmount: 280.00,\n    bidTime: '2024-01-14 16:45:30',\n    isWinning: true,\n    isSuccessful: true,\n    status: 'won',\n    bidIncrement: 15.00,\n    previousPrice: 265.00\n  }];\n\n  // 获取竞价记录列表\n  const fetchBidRecords = async () => {\n    setLoading(true);\n    try {\n      // 首先获取拍卖会列表\n      const auctionResponse = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 100\n      });\n      if (auctionResponse.success && auctionResponse.data.list.length > 0) {\n        // 获取所有拍卖会的竞价记录\n        const allBidRecords = [];\n        for (const auction of auctionResponse.data.list) {\n          try {\n            const bidResponse = await auctionService.getBidRecordsWithFilter(auction.id, {\n              page: queryParams.page,\n              pageSize: queryParams.pageSize,\n              productName: queryParams.productName,\n              username: queryParams.username,\n              status: queryParams.status\n            });\n            if (bidResponse.success && bidResponse.data.list.length > 0) {\n              const records = bidResponse.data.list.map(bid => ({\n                id: bid.id,\n                auctionId: auction.id,\n                auctionTitle: auction.title || `拍卖会${auction.id}`,\n                productId: bid.productId || 0,\n                productName: bid.productName || '未知商品',\n                productCode: `ITEM-${bid.productId || bid.id}`,\n                userId: bid.userId || 0,\n                username: bid.bidderName || '匿名用户',\n                bidAmount: bid.bidAmount || 0,\n                bidTime: bid.bidTime || new Date().toISOString(),\n                isWinning: bid.isWinning || false,\n                isSuccessful: bid.isWinning || false,\n                status: bid.isWinning ? 'winning' : 'outbid',\n                bidIncrement: 10.00,\n                previousPrice: (bid.bidAmount || 0) - 10\n              }));\n              allBidRecords.push(...records);\n            }\n          } catch (error) {\n            console.error(`获取拍卖会${auction.id}的竞价记录失败:`, error);\n          }\n        }\n        if (allBidRecords.length > 0) {\n          // 按时间倒序排列\n          allBidRecords.sort((a, b) => new Date(b.bidTime).getTime() - new Date(a.bidTime).getTime());\n          setBidRecords(allBidRecords);\n          setTotal(allBidRecords.length);\n        } else {\n          // 如果没有真实数据，使用模拟数据\n          setBidRecords(mockBidRecords);\n          setTotal(mockBidRecords.length);\n        }\n      } else {\n        // 如果没有拍卖会，使用模拟数据\n        setBidRecords(mockBidRecords);\n        setTotal(mockBidRecords.length);\n      }\n    } catch (error) {\n      console.error('获取竞价记录失败:', error);\n      // 出错时使用模拟数据\n      setBidRecords(mockBidRecords);\n      setTotal(mockBidRecords.length);\n      message.error(error.message || '获取竞价记录失败，显示模拟数据');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      // 这里应该调用后端API获取统计信息\n      setStatistics({\n        totalBids: 156,\n        totalAmount: 45680.50,\n        successfulBids: 23,\n        activeUsers: 45\n      });\n    } catch (error) {\n      console.error('获取统计信息失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchBidRecords();\n    fetchStatistics();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = () => {\n    setQueryParams({\n      ...queryParams,\n      productName: searchForm.productName || undefined,\n      username: searchForm.username || undefined,\n      status: searchForm.status || undefined,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setSearchForm({\n      productName: '',\n      username: '',\n      status: undefined\n    });\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 查看详情\n  const handleViewDetail = record => {\n    setSelectedRecord(record);\n    setIsDetailModalVisible(true);\n  };\n\n  // 导出记录\n  const handleExport = async () => {\n    try {\n      setLoading(true);\n\n      // 准备导出数据\n      const exportData = bidRecords.map((record, index) => ({\n        '序号': index + 1,\n        '商品名称': record.productName,\n        '竞价人': record.username,\n        '竞价金额': `¥${record.bidAmount.toFixed(2)}`,\n        '竞价时间': new Date(record.bidTime).toLocaleString('zh-CN'),\n        '状态': record.isWinning ? '中标' : '未中标',\n        '拍卖会': record.auctionTitle || '未知拍卖会'\n      }));\n\n      // 创建CSV内容\n      const headers = Object.keys(exportData[0] || {});\n      const csvContent = [headers.join(','), ...exportData.map(row => headers.map(header => `\"${row[header]}\"`).join(','))].join('\\n');\n\n      // 创建并下载文件\n      const blob = new Blob(['\\uFEFF' + csvContent], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `竞价记录_${new Date().toISOString().slice(0, 10)}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      message.success('导出成功');\n    } catch (error) {\n      console.error('导出失败:', error);\n      message.error('导出失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '拍卖会',\n    dataIndex: 'auctionTitle',\n    key: 'auctionTitle',\n    width: 150,\n    ellipsis: true\n  }, {\n    title: '商品信息',\n    key: 'product',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 500\n        },\n        children: record.productName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: record.productCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '竞价用户',\n    key: 'user',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        src: record.userAvatar,\n        icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 19\n        }, this),\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: record.username\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '竞价金额',\n    key: 'bidAmount',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 500,\n          color: '#f50'\n        },\n        children: [\"\\xA5\", record.bidAmount.toFixed(2)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: [\"+\\xA5\", record.bidIncrement.toFixed(2)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '竞价状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => {\n      const statusInfo = bidStatusMap[status];\n      return /*#__PURE__*/_jsxDEV(Badge, {\n        status: status === 'winning' || status === 'active' ? 'processing' : status === 'won' ? 'success' : status === 'lost' ? 'error' : 'default',\n        text: /*#__PURE__*/_jsxDEV(Tag, {\n          color: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || 'default',\n          children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.label) || '未知'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '竞价时间',\n    dataIndex: 'bidTime',\n    key: 'bidTime',\n    width: 160,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: dayjs(text).format('YYYY-MM-DD')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: 12\n        },\n        children: dayjs(text).format('HH:mm:ss')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 100,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          onClick: () => handleViewDetail(record),\n          children: \"\\u8BE6\\u60C5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u7ADE\\u4EF7\\u8BB0\\u5F55\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u7ADE\\u4EF7\\u6B21\\u6570\",\n            value: statistics.totalBids,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u7ADE\\u4EF7\\u91D1\\u989D\",\n            value: statistics.totalAmount,\n            precision: 2,\n            prefix: \"\\xA5\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6210\\u529F\\u7ADE\\u4EF7\",\n            value: statistics.successfulBids,\n            prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D3B\\u8DC3\\u7528\\u6237\",\n            value: statistics.activeUsers,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5546\\u54C1\\u540D\\u79F0\",\n            allowClear: true,\n            value: searchForm.productName,\n            onChange: e => setSearchForm({\n              ...searchForm,\n              productName: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u7528\\u6237\\u540D\",\n            allowClear: true,\n            value: searchForm.username,\n            onChange: e => setSearchForm({\n              ...searchForm,\n              username: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u7ADE\\u4EF7\\u72B6\\u6001\",\n            allowClear: true,\n            style: {\n              width: '100%'\n            },\n            value: searchForm.status,\n            onChange: value => setSearchForm({\n              ...searchForm,\n              status: value\n            }),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"1\",\n              children: \"\\u4E2D\\u6807\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"0\",\n              children: \"\\u672A\\u4E2D\\u6807\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 23\n              }, this),\n              onClick: handleSearch,\n              children: \"\\u641C\\u7D22\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 23\n              }, this),\n              onClick: handleReset,\n              children: \"\\u91CD\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 23\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\\u8BB0\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 21\n            }, this),\n            onClick: fetchBidRecords,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: bidRecords,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u7ADE\\u4EF7\\u8BB0\\u5F55\\u8BE6\\u60C5\",\n      open: isDetailModalVisible,\n      onCancel: () => setIsDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setIsDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: selectedRecord && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u62CD\\u5356\\u4F1A\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: selectedRecord.auctionTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5546\\u54C1\\u540D\\u79F0\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: selectedRecord.productName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5546\\u54C1\\u7F16\\u7801\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: selectedRecord.productCode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u7ADE\\u4EF7\\u7528\\u6237\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: selectedRecord.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u7ADE\\u4EF7\\u91D1\\u989D\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#f50',\n                fontWeight: 'bold'\n              },\n              children: [\"\\xA5\", selectedRecord.bidAmount.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u52A0\\u4EF7\\u5E45\\u5EA6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\xA5\", selectedRecord.bidIncrement.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u524D\\u4E00\\u4EF7\\u683C\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\xA5\", selectedRecord.previousPrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u7ADE\\u4EF7\\u72B6\\u6001\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: ((_bidStatusMap$selecte = bidStatusMap[selectedRecord.status]) === null || _bidStatusMap$selecte === void 0 ? void 0 : _bidStatusMap$selecte.color) || 'default',\n                children: ((_bidStatusMap$selecte2 = bidStatusMap[selectedRecord.status]) === null || _bidStatusMap$selecte2 === void 0 ? void 0 : _bidStatusMap$selecte2.label) || '未知'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u7ADE\\u4EF7\\u65F6\\u95F4\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: dayjs(selectedRecord.bidTime).format('YYYY-MM-DD HH:mm:ss')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u662F\\u5426\\u4E2D\\u6807\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: selectedRecord.isWinning ? 'green' : 'red',\n                children: selectedRecord.isWinning ? '是' : '否'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 439,\n    columnNumber: 5\n  }, this);\n};\n_s(BidRecords, \"rLt4mY42d7wSMFta6iclbALPw9Y=\");\n_c = BidRecords;\nexport default BidRecords;\nvar _c;\n$RefreshReg$(_c, \"BidRecords\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Typography", "Row", "Col", "Modal", "Statistic", "Avatar", "<PERSON><PERSON><PERSON>", "Badge", "message", "SearchOutlined", "ReloadOutlined", "ExportOutlined", "UserOutlined", "TrophyOutlined", "DollarOutlined", "dayjs", "auctionService", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "BidRecords", "_s", "_bidStatusMap$selecte", "_bidStatusMap$selecte2", "bidRecords", "setBidRecords", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "statistics", "setStatistics", "totalBids", "totalAmount", "successfulBids", "activeUsers", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRecord", "isDetailModalVisible", "setIsDetailModalVisible", "searchForm", "setSearchForm", "productName", "username", "status", "undefined", "bidStatusMap", "active", "label", "color", "outbid", "winning", "won", "lost", "mockBidRecords", "id", "auctionId", "auctionTitle", "productId", "productCode", "userId", "bidAmount", "bidTime", "isWinning", "isSuccessful", "bidIncrement", "previousPrice", "fetchBidRecords", "auctionResponse", "getAuctionList", "success", "data", "list", "length", "allBidRecords", "auction", "bidResponse", "getBidRecordsWithFilter", "records", "map", "bid", "title", "bidderName", "Date", "toISOString", "push", "error", "console", "sort", "a", "b", "getTime", "fetchStatistics", "handleSearch", "handleReset", "handleViewDetail", "record", "handleExport", "exportData", "index", "toFixed", "toLocaleString", "headers", "Object", "keys", "csv<PERSON><PERSON>nt", "join", "row", "header", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "slice", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "columns", "dataIndex", "key", "width", "ellipsis", "render", "_", "children", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "src", "userAvatar", "icon", "size", "statusInfo", "text", "format", "fixed", "onClick", "padding", "level", "gutter", "marginBottom", "xs", "sm", "md", "value", "prefix", "valueStyle", "precision", "className", "placeholder", "allowClear", "onChange", "e", "target", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "footer", "span", "strong", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Typography,\n  Row,\n  Col,\n  Modal,\n  Statistic,\n  Avatar,\n  Tooltip,\n  Badge,\n  message,\n} from 'antd';\nimport {\n  SearchOutlined,\n  ReloadOutlined,\n  ExportOutlined,\n  UserOutlined,\n  TrophyOutlined,\n  DollarOutlined,\n\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\nimport { auctionService } from '../../../services/auctionService';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\n// 竞价记录接口\ninterface BidRecord {\n  id: number;\n  auctionId: number;\n  auctionTitle: string;\n  productId: number;\n  productName: string;\n  productCode: string;\n  userId: number;\n  username: string;\n  userAvatar?: string;\n  bidAmount: number;\n  bidTime: string;\n  isWinning: boolean;\n  isSuccessful: boolean; // 是否最终成交\n  status: 'active' | 'outbid' | 'winning' | 'won' | 'lost';\n  bidIncrement: number;\n  previousPrice: number;\n}\n\n// 查询参数接口\ninterface BidRecordQueryParams {\n  auctionId?: number;\n  productName?: string;\n  username?: string;\n  status?: string;\n  dateRange?: [string, string];\n  page: number;\n  pageSize: number;\n}\n\nconst BidRecords: React.FC = () => {\n  const [bidRecords, setBidRecords] = useState<BidRecord[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<BidRecordQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [statistics, setStatistics] = useState({\n    totalBids: 0,\n    totalAmount: 0,\n    successfulBids: 0,\n    activeUsers: 0,\n  });\n  const [selectedRecord, setSelectedRecord] = useState<BidRecord | null>(null);\n  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);\n  const [searchForm, setSearchForm] = useState({\n    productName: '',\n    username: '',\n    status: undefined as string | undefined,\n  });\n\n  // 竞价状态映射\n  const bidStatusMap = {\n    active: { label: '当前最高', color: 'blue' },\n    outbid: { label: '已被超越', color: 'default' },\n    winning: { label: '领先中', color: 'orange' },\n    won: { label: '竞拍成功', color: 'green' },\n    lost: { label: '竞拍失败', color: 'red' },\n  };\n\n  // 模拟数据\n  const mockBidRecords: BidRecord[] = [\n    {\n      id: 1,\n      auctionId: 1,\n      auctionTitle: '春季花卉拍卖会',\n      productId: 1,\n      productName: '荷兰郁金香 - 红色经典',\n      productCode: 'TLP-001',\n      userId: 1,\n      username: 'flower_lover',\n      bidAmount: 350.00,\n      bidTime: '2024-01-15 14:30:25',\n      isWinning: true,\n      isSuccessful: false,\n      status: 'winning',\n      bidIncrement: 10.00,\n      previousPrice: 340.00,\n    },\n    {\n      id: 2,\n      auctionId: 1,\n      auctionTitle: '春季花卉拍卖会',\n      productId: 1,\n      productName: '荷兰郁金香 - 红色经典',\n      productCode: 'TLP-001',\n      userId: 2,\n      username: 'garden_master',\n      bidAmount: 340.00,\n      bidTime: '2024-01-15 14:29:15',\n      isWinning: false,\n      isSuccessful: false,\n      status: 'outbid',\n      bidIncrement: 10.00,\n      previousPrice: 330.00,\n    },\n    {\n      id: 3,\n      auctionId: 2,\n      auctionTitle: '精品玫瑰专场',\n      productId: 2,\n      productName: '法国玫瑰 - 香槟色',\n      productCode: 'RSE-002',\n      userId: 3,\n      username: 'rose_collector',\n      bidAmount: 280.00,\n      bidTime: '2024-01-14 16:45:30',\n      isWinning: true,\n      isSuccessful: true,\n      status: 'won',\n      bidIncrement: 15.00,\n      previousPrice: 265.00,\n    },\n  ];\n\n  // 获取竞价记录列表\n  const fetchBidRecords = async () => {\n    setLoading(true);\n    try {\n      // 首先获取拍卖会列表\n      const auctionResponse = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 100,\n      });\n\n      if (auctionResponse.success && auctionResponse.data.list.length > 0) {\n        // 获取所有拍卖会的竞价记录\n        const allBidRecords: BidRecord[] = [];\n\n        for (const auction of auctionResponse.data.list) {\n          try {\n            const bidResponse = await auctionService.getBidRecordsWithFilter(auction.id, {\n              page: queryParams.page,\n              pageSize: queryParams.pageSize,\n              productName: queryParams.productName,\n              username: queryParams.username,\n              status: queryParams.status,\n            });\n\n            if (bidResponse.success && bidResponse.data.list.length > 0) {\n              const records: BidRecord[] = bidResponse.data.list.map((bid: any) => ({\n                id: bid.id,\n                auctionId: auction.id,\n                auctionTitle: auction.title || `拍卖会${auction.id}`,\n                productId: bid.productId || 0,\n                productName: bid.productName || '未知商品',\n                productCode: `ITEM-${bid.productId || bid.id}`,\n                userId: bid.userId || 0,\n                username: bid.bidderName || '匿名用户',\n                bidAmount: bid.bidAmount || 0,\n                bidTime: bid.bidTime || new Date().toISOString(),\n                isWinning: bid.isWinning || false,\n                isSuccessful: bid.isWinning || false,\n                status: bid.isWinning ? 'winning' : 'outbid' as any,\n                bidIncrement: 10.00,\n                previousPrice: (bid.bidAmount || 0) - 10,\n              }));\n              allBidRecords.push(...records);\n            }\n          } catch (error) {\n            console.error(`获取拍卖会${auction.id}的竞价记录失败:`, error);\n          }\n        }\n\n        if (allBidRecords.length > 0) {\n          // 按时间倒序排列\n          allBidRecords.sort((a, b) => new Date(b.bidTime).getTime() - new Date(a.bidTime).getTime());\n          setBidRecords(allBidRecords);\n          setTotal(allBidRecords.length);\n        } else {\n          // 如果没有真实数据，使用模拟数据\n          setBidRecords(mockBidRecords);\n          setTotal(mockBidRecords.length);\n        }\n      } else {\n        // 如果没有拍卖会，使用模拟数据\n        setBidRecords(mockBidRecords);\n        setTotal(mockBidRecords.length);\n      }\n    } catch (error: any) {\n      console.error('获取竞价记录失败:', error);\n      // 出错时使用模拟数据\n      setBidRecords(mockBidRecords);\n      setTotal(mockBidRecords.length);\n      message.error(error.message || '获取竞价记录失败，显示模拟数据');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      // 这里应该调用后端API获取统计信息\n      setStatistics({\n        totalBids: 156,\n        totalAmount: 45680.50,\n        successfulBids: 23,\n        activeUsers: 45,\n      });\n    } catch (error: any) {\n      console.error('获取统计信息失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchBidRecords();\n    fetchStatistics();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = () => {\n    setQueryParams({\n      ...queryParams,\n      productName: searchForm.productName || undefined,\n      username: searchForm.username || undefined,\n      status: searchForm.status || undefined,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setSearchForm({\n      productName: '',\n      username: '',\n      status: undefined,\n    });\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 查看详情\n  const handleViewDetail = (record: BidRecord) => {\n    setSelectedRecord(record);\n    setIsDetailModalVisible(true);\n  };\n\n  // 导出记录\n  const handleExport = async () => {\n    try {\n      setLoading(true);\n\n      // 准备导出数据\n      const exportData = bidRecords.map((record, index) => ({\n        '序号': index + 1,\n        '商品名称': record.productName,\n        '竞价人': record.username,\n        '竞价金额': `¥${record.bidAmount.toFixed(2)}`,\n        '竞价时间': new Date(record.bidTime).toLocaleString('zh-CN'),\n        '状态': record.isWinning ? '中标' : '未中标',\n        '拍卖会': record.auctionTitle || '未知拍卖会',\n      }));\n\n      // 创建CSV内容\n      const headers = Object.keys(exportData[0] || {});\n      const csvContent = [\n        headers.join(','),\n        ...exportData.map(row => headers.map(header => `\"${row[header as keyof typeof row]}\"`).join(','))\n      ].join('\\n');\n\n      // 创建并下载文件\n      const blob = new Blob(['\\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `竞价记录_${new Date().toISOString().slice(0, 10)}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      message.success('导出成功');\n    } catch (error) {\n      console.error('导出失败:', error);\n      message.error('导出失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<BidRecord> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '拍卖会',\n      dataIndex: 'auctionTitle',\n      key: 'auctionTitle',\n      width: 150,\n      ellipsis: true,\n    },\n    {\n      title: '商品信息',\n      key: 'product',\n      width: 200,\n      render: (_, record: BidRecord) => (\n        <div>\n          <div style={{ fontWeight: 500 }}>{record.productName}</div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {record.productCode}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '竞价用户',\n      key: 'user',\n      width: 120,\n      render: (_, record: BidRecord) => (\n        <Space>\n          <Avatar\n            src={record.userAvatar}\n            icon={<UserOutlined />}\n            size=\"small\"\n          />\n          <Text>{record.username}</Text>\n        </Space>\n      ),\n    },\n    {\n      title: '竞价金额',\n      key: 'bidAmount',\n      width: 120,\n      render: (_, record: BidRecord) => (\n        <div>\n          <div style={{ fontWeight: 500, color: '#f50' }}>\n            ¥{record.bidAmount.toFixed(2)}\n          </div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            +¥{record.bidIncrement.toFixed(2)}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '竞价状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: keyof typeof bidStatusMap) => {\n        const statusInfo = bidStatusMap[status];\n        return (\n          <Badge\n            status={\n              status === 'winning' || status === 'active' ? 'processing' :\n              status === 'won' ? 'success' :\n              status === 'lost' ? 'error' : 'default'\n            }\n            text={\n              <Tag color={statusInfo?.color || 'default'}>\n                {statusInfo?.label || '未知'}\n              </Tag>\n            }\n          />\n        );\n      },\n    },\n    {\n      title: '竞价时间',\n      dataIndex: 'bidTime',\n      key: 'bidTime',\n      width: 160,\n      render: (text: string) => (\n        <div>\n          <div>{dayjs(text).format('YYYY-MM-DD')}</div>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            {dayjs(text).format('HH:mm:ss')}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 100,\n      fixed: 'right',\n      render: (_, record: BidRecord) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"link\"\n              size=\"small\"\n              onClick={() => handleViewDetail(record)}\n            >\n              详情\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>竞价记录</Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总竞价次数\"\n              value={statistics.totalBids}\n              prefix={<DollarOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总竞价金额\"\n              value={statistics.totalAmount}\n              precision={2}\n              prefix=\"¥\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"成功竞价\"\n              value={statistics.successfulBids}\n              prefix={<TrophyOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"活跃用户\"\n              value={statistics.activeUsers}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\" style={{ marginBottom: 16 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={6}>\n            <Input\n              placeholder=\"商品名称\"\n              allowClear\n              value={searchForm.productName}\n              onChange={(e) => setSearchForm({ ...searchForm, productName: e.target.value })}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Input\n              placeholder=\"用户名\"\n              allowClear\n              value={searchForm.username}\n              onChange={(e) => setSearchForm({ ...searchForm, username: e.target.value })}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Select\n              placeholder=\"竞价状态\"\n              allowClear\n              style={{ width: '100%' }}\n              value={searchForm.status}\n              onChange={(value) => setSearchForm({ ...searchForm, status: value })}\n            >\n              <Option value=\"1\">中标</Option>\n              <Option value=\"0\">未中标</Option>\n            </Select>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<SearchOutlined />}\n                onClick={handleSearch}\n              >\n                搜索\n              </Button>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={handleReset}\n              >\n                重置\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\" style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={handleExport}\n              >\n                导出记录\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchBidRecords}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 竞价记录表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={bidRecords}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 详情模态框 */}\n      <Modal\n        title=\"竞价记录详情\"\n        open={isDetailModalVisible}\n        onCancel={() => setIsDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setIsDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={600}\n      >\n        {selectedRecord && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>拍卖会：</Text>\n                <div>{selectedRecord.auctionTitle}</div>\n              </Col>\n              <Col span={12}>\n                <Text strong>商品名称：</Text>\n                <div>{selectedRecord.productName}</div>\n              </Col>\n              <Col span={12}>\n                <Text strong>商品编码：</Text>\n                <div>{selectedRecord.productCode}</div>\n              </Col>\n              <Col span={12}>\n                <Text strong>竞价用户：</Text>\n                <div>{selectedRecord.username}</div>\n              </Col>\n              <Col span={12}>\n                <Text strong>竞价金额：</Text>\n                <div style={{ color: '#f50', fontWeight: 'bold' }}>\n                  ¥{selectedRecord.bidAmount.toFixed(2)}\n                </div>\n              </Col>\n              <Col span={12}>\n                <Text strong>加价幅度：</Text>\n                <div>¥{selectedRecord.bidIncrement.toFixed(2)}</div>\n              </Col>\n              <Col span={12}>\n                <Text strong>前一价格：</Text>\n                <div>¥{selectedRecord.previousPrice.toFixed(2)}</div>\n              </Col>\n              <Col span={12}>\n                <Text strong>竞价状态：</Text>\n                <div>\n                  <Tag color={bidStatusMap[selectedRecord.status]?.color || 'default'}>\n                    {bidStatusMap[selectedRecord.status]?.label || '未知'}\n                  </Tag>\n                </div>\n              </Col>\n              <Col span={24}>\n                <Text strong>竞价时间：</Text>\n                <div>{dayjs(selectedRecord.bidTime).format('YYYY-MM-DD HH:mm:ss')}</div>\n              </Col>\n              <Col span={24}>\n                <Text strong>是否中标：</Text>\n                <div>\n                  <Tag color={selectedRecord.isWinning ? 'green' : 'red'}>\n                    {selectedRecord.isWinning ? '是' : '否'}\n                  </Tag>\n                </div>\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default BidRecords;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,QAET,mBAAmB;AAE1B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAO,CAAC,GAAGvB,MAAM;;AAEzB;;AAoBA;;AAWA,MAAMwB,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACjC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAuB;IACnE2C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC;IAC3C+C,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAmB,IAAI,CAAC;EAC5E,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC;IAC3CyD,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAEC;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxCC,MAAM,EAAE;MAAEF,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAU,CAAC;IAC3CE,OAAO,EAAE;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC1CG,GAAG,EAAE;MAAEJ,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACtCI,IAAI,EAAE;MAAEL,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM;EACtC,CAAC;;EAED;EACA,MAAMK,cAA2B,GAAG,CAClC;IACEC,EAAE,EAAE,CAAC;IACLC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,CAAC;IACZhB,WAAW,EAAE,cAAc;IAC3BiB,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,CAAC;IACTjB,QAAQ,EAAE,cAAc;IACxBkB,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,KAAK;IACnBpB,MAAM,EAAE,SAAS;IACjBqB,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,CAAC;IACZhB,WAAW,EAAE,cAAc;IAC3BiB,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,CAAC;IACTjB,QAAQ,EAAE,eAAe;IACzBkB,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,KAAK;IACnBpB,MAAM,EAAE,QAAQ;IAChBqB,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,QAAQ;IACtBC,SAAS,EAAE,CAAC;IACZhB,WAAW,EAAE,YAAY;IACzBiB,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,CAAC;IACTjB,QAAQ,EAAE,gBAAgB;IAC1BkB,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,IAAI;IAClBpB,MAAM,EAAE,KAAK;IACbqB,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAE;EACjB,CAAC,CACF;;EAED;EACA,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC5C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM6C,eAAe,GAAG,MAAM1D,cAAc,CAAC2D,cAAc,CAAC;QAC1DzC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAIuC,eAAe,CAACE,OAAO,IAAIF,eAAe,CAACG,IAAI,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACnE;QACA,MAAMC,aAA0B,GAAG,EAAE;QAErC,KAAK,MAAMC,OAAO,IAAIP,eAAe,CAACG,IAAI,CAACC,IAAI,EAAE;UAC/C,IAAI;YACF,MAAMI,WAAW,GAAG,MAAMlE,cAAc,CAACmE,uBAAuB,CAACF,OAAO,CAACpB,EAAE,EAAE;cAC3E3B,IAAI,EAAEF,WAAW,CAACE,IAAI;cACtBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;cAC9Ba,WAAW,EAAEhB,WAAW,CAACgB,WAAW;cACpCC,QAAQ,EAAEjB,WAAW,CAACiB,QAAQ;cAC9BC,MAAM,EAAElB,WAAW,CAACkB;YACtB,CAAC,CAAC;YAEF,IAAIgC,WAAW,CAACN,OAAO,IAAIM,WAAW,CAACL,IAAI,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;cAC3D,MAAMK,OAAoB,GAAGF,WAAW,CAACL,IAAI,CAACC,IAAI,CAACO,GAAG,CAAEC,GAAQ,KAAM;gBACpEzB,EAAE,EAAEyB,GAAG,CAACzB,EAAE;gBACVC,SAAS,EAAEmB,OAAO,CAACpB,EAAE;gBACrBE,YAAY,EAAEkB,OAAO,CAACM,KAAK,IAAI,MAAMN,OAAO,CAACpB,EAAE,EAAE;gBACjDG,SAAS,EAAEsB,GAAG,CAACtB,SAAS,IAAI,CAAC;gBAC7BhB,WAAW,EAAEsC,GAAG,CAACtC,WAAW,IAAI,MAAM;gBACtCiB,WAAW,EAAE,QAAQqB,GAAG,CAACtB,SAAS,IAAIsB,GAAG,CAACzB,EAAE,EAAE;gBAC9CK,MAAM,EAAEoB,GAAG,CAACpB,MAAM,IAAI,CAAC;gBACvBjB,QAAQ,EAAEqC,GAAG,CAACE,UAAU,IAAI,MAAM;gBAClCrB,SAAS,EAAEmB,GAAG,CAACnB,SAAS,IAAI,CAAC;gBAC7BC,OAAO,EAAEkB,GAAG,CAAClB,OAAO,IAAI,IAAIqB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBAChDrB,SAAS,EAAEiB,GAAG,CAACjB,SAAS,IAAI,KAAK;gBACjCC,YAAY,EAAEgB,GAAG,CAACjB,SAAS,IAAI,KAAK;gBACpCnB,MAAM,EAAEoC,GAAG,CAACjB,SAAS,GAAG,SAAS,GAAG,QAAe;gBACnDE,YAAY,EAAE,KAAK;gBACnBC,aAAa,EAAE,CAACc,GAAG,CAACnB,SAAS,IAAI,CAAC,IAAI;cACxC,CAAC,CAAC,CAAC;cACHa,aAAa,CAACW,IAAI,CAAC,GAAGP,OAAO,CAAC;YAChC;UACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,QAAQX,OAAO,CAACpB,EAAE,UAAU,EAAE+B,KAAK,CAAC;UACpD;QACF;QAEA,IAAIZ,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;UAC5B;UACAC,aAAa,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIP,IAAI,CAACO,CAAC,CAAC5B,OAAO,CAAC,CAAC6B,OAAO,CAAC,CAAC,GAAG,IAAIR,IAAI,CAACM,CAAC,CAAC3B,OAAO,CAAC,CAAC6B,OAAO,CAAC,CAAC,CAAC;UAC3FtE,aAAa,CAACqD,aAAa,CAAC;UAC5BjD,QAAQ,CAACiD,aAAa,CAACD,MAAM,CAAC;QAChC,CAAC,MAAM;UACL;UACApD,aAAa,CAACiC,cAAc,CAAC;UAC7B7B,QAAQ,CAAC6B,cAAc,CAACmB,MAAM,CAAC;QACjC;MACF,CAAC,MAAM;QACL;QACApD,aAAa,CAACiC,cAAc,CAAC;QAC7B7B,QAAQ,CAAC6B,cAAc,CAACmB,MAAM,CAAC;MACjC;IACF,CAAC,CAAC,OAAOa,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;MACAjE,aAAa,CAACiC,cAAc,CAAC;MAC7B7B,QAAQ,CAAC6B,cAAc,CAACmB,MAAM,CAAC;MAC/BvE,OAAO,CAACoF,KAAK,CAACA,KAAK,CAACpF,OAAO,IAAI,iBAAiB,CAAC;IACnD,CAAC,SAAS;MACRqB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqE,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA7D,aAAa,CAAC;QACZC,SAAS,EAAE,GAAG;QACdC,WAAW,EAAE,QAAQ;QACrBC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOmD,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACApG,SAAS,CAAC,MAAM;IACdiF,eAAe,CAAC,CAAC;IACjByB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAClE,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMmE,YAAY,GAAGA,CAAA,KAAM;IACzBlE,cAAc,CAAC;MACb,GAAGD,WAAW;MACdgB,WAAW,EAAEF,UAAU,CAACE,WAAW,IAAIG,SAAS;MAChDF,QAAQ,EAAEH,UAAU,CAACG,QAAQ,IAAIE,SAAS;MAC1CD,MAAM,EAAEJ,UAAU,CAACI,MAAM,IAAIC,SAAS;MACtCjB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkE,WAAW,GAAGA,CAAA,KAAM;IACxBrD,aAAa,CAAC;MACZC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAEC;IACV,CAAC,CAAC;IACFlB,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkE,gBAAgB,GAAIC,MAAiB,IAAK;IAC9C3D,iBAAiB,CAAC2D,MAAM,CAAC;IACzBzD,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM0D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF1E,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM2E,UAAU,GAAG9E,UAAU,CAAC2D,GAAG,CAAC,CAACiB,MAAM,EAAEG,KAAK,MAAM;QACpD,IAAI,EAAEA,KAAK,GAAG,CAAC;QACf,MAAM,EAAEH,MAAM,CAACtD,WAAW;QAC1B,KAAK,EAAEsD,MAAM,CAACrD,QAAQ;QACtB,MAAM,EAAE,IAAIqD,MAAM,CAACnC,SAAS,CAACuC,OAAO,CAAC,CAAC,CAAC,EAAE;QACzC,MAAM,EAAE,IAAIjB,IAAI,CAACa,MAAM,CAAClC,OAAO,CAAC,CAACuC,cAAc,CAAC,OAAO,CAAC;QACxD,IAAI,EAAEL,MAAM,CAACjC,SAAS,GAAG,IAAI,GAAG,KAAK;QACrC,KAAK,EAAEiC,MAAM,CAACvC,YAAY,IAAI;MAChC,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM6C,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACN,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;MAChD,MAAMO,UAAU,GAAG,CACjBH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,EACjB,GAAGR,UAAU,CAACnB,GAAG,CAAC4B,GAAG,IAAIL,OAAO,CAACvB,GAAG,CAAC6B,MAAM,IAAI,IAAID,GAAG,CAACC,MAAM,CAAqB,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAClG,CAACA,IAAI,CAAC,IAAI,CAAC;;MAEZ;MACA,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,QAAQ,GAAGL,UAAU,CAAC,EAAE;QAAEM,IAAI,EAAE;MAA0B,CAAC,CAAC;MACnF,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;MACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;MAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,QAAQ,IAAInC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACmC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;MAClFP,IAAI,CAACQ,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCR,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACX,IAAI,CAAC;MAC/BA,IAAI,CAACY,KAAK,CAAC,CAAC;MACZX,QAAQ,CAACS,IAAI,CAACG,WAAW,CAACb,IAAI,CAAC;MAE/B9G,OAAO,CAACoE,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BpF,OAAO,CAACoF,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACR/D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuG,OAA+B,GAAG,CACtC;IACE7C,KAAK,EAAE,IAAI;IACX8C,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEhD,KAAK,EAAE,KAAK;IACZ8C,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEjD,KAAK,EAAE,MAAM;IACb+C,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACC,CAAC,EAAEpC,MAAiB,kBAC3BpF,OAAA;MAAAyH,QAAA,gBACEzH,OAAA;QAAK4G,KAAK,EAAE;UAAEc,UAAU,EAAE;QAAI,CAAE;QAAAD,QAAA,EAAErC,MAAM,CAACtD;MAAW;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3D9H,OAAA,CAACE,IAAI;QAACiG,IAAI,EAAC,WAAW;QAACS,KAAK,EAAE;UAAEmB,QAAQ,EAAE;QAAG,CAAE;QAAAN,QAAA,EAC5CrC,MAAM,CAACrC;MAAW;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACEzD,KAAK,EAAE,MAAM;IACb+C,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACC,CAAC,EAAEpC,MAAiB,kBAC3BpF,OAAA,CAACtB,KAAK;MAAA+I,QAAA,gBACJzH,OAAA,CAACb,MAAM;QACL6I,GAAG,EAAE5C,MAAM,CAAC6C,UAAW;QACvBC,IAAI,eAAElI,OAAA,CAACN,YAAY;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBK,IAAI,EAAC;MAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACF9H,OAAA,CAACE,IAAI;QAAAuH,QAAA,EAAErC,MAAM,CAACrD;MAAQ;QAAA4F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB;EAEX,CAAC,EACD;IACEzD,KAAK,EAAE,MAAM;IACb+C,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACC,CAAC,EAAEpC,MAAiB,kBAC3BpF,OAAA;MAAAyH,QAAA,gBACEzH,OAAA;QAAK4G,KAAK,EAAE;UAAEc,UAAU,EAAE,GAAG;UAAErF,KAAK,EAAE;QAAO,CAAE;QAAAoF,QAAA,GAAC,MAC7C,EAACrC,MAAM,CAACnC,SAAS,CAACuC,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACN9H,OAAA,CAACE,IAAI;QAACiG,IAAI,EAAC,WAAW;QAACS,KAAK,EAAE;UAAEmB,QAAQ,EAAE;QAAG,CAAE;QAAAN,QAAA,GAAC,OAC5C,EAACrC,MAAM,CAAC/B,YAAY,CAACmC,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACEzD,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGvF,MAAiC,IAAK;MAC7C,MAAMoG,UAAU,GAAGlG,YAAY,CAACF,MAAM,CAAC;MACvC,oBACEhC,OAAA,CAACX,KAAK;QACJ2C,MAAM,EACJA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,QAAQ,GAAG,YAAY,GAC1DA,MAAM,KAAK,KAAK,GAAG,SAAS,GAC5BA,MAAM,KAAK,MAAM,GAAG,OAAO,GAAG,SAC/B;QACDqG,IAAI,eACFrI,OAAA,CAACnB,GAAG;UAACwD,KAAK,EAAE,CAAA+F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE/F,KAAK,KAAI,SAAU;UAAAoF,QAAA,EACxC,CAAAW,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEhG,KAAK,KAAI;QAAI;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEN;EACF,CAAC,EACD;IACEzD,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGc,IAAY,iBACnBrI,OAAA;MAAAyH,QAAA,gBACEzH,OAAA;QAAAyH,QAAA,EAAM5H,KAAK,CAACwI,IAAI,CAAC,CAACC,MAAM,CAAC,YAAY;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7C9H,OAAA,CAACE,IAAI;QAACiG,IAAI,EAAC,WAAW;QAACS,KAAK,EAAE;UAAEmB,QAAQ,EAAE;QAAG,CAAE;QAAAN,QAAA,EAC5C5H,KAAK,CAACwI,IAAI,CAAC,CAACC,MAAM,CAAC,UAAU;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,EACD;IACEzD,KAAK,EAAE,IAAI;IACX+C,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVkB,KAAK,EAAE,OAAO;IACdhB,MAAM,EAAEA,CAACC,CAAC,EAAEpC,MAAiB,kBAC3BpF,OAAA,CAACtB,KAAK;MAACyJ,IAAI,EAAC,OAAO;MAAAV,QAAA,eACjBzH,OAAA,CAACZ,OAAO;QAACiF,KAAK,EAAC,0BAAM;QAAAoD,QAAA,eACnBzH,OAAA,CAACvB,MAAM;UACL0H,IAAI,EAAC,MAAM;UACXgC,IAAI,EAAC,OAAO;UACZK,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACC,MAAM,CAAE;UAAAqC,QAAA,EACzC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;EAED,oBACE9H,OAAA;IAAK4G,KAAK,EAAE;MAAE6B,OAAO,EAAE;IAAG,CAAE;IAAAhB,QAAA,gBAC1BzH,OAAA,CAACC,KAAK;MAACyI,KAAK,EAAE,CAAE;MAAAjB,QAAA,EAAC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7B9H,OAAA,CAACjB,GAAG;MAAC4J,MAAM,EAAE,EAAG;MAAC/B,KAAK,EAAE;QAAEgC,YAAY,EAAE;MAAG,CAAE;MAAAnB,QAAA,gBAC3CzH,OAAA,CAAChB,GAAG;QAAC6J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACzBzH,OAAA,CAACzB,IAAI;UAAAkJ,QAAA,eACHzH,OAAA,CAACd,SAAS;YACRmF,KAAK,EAAC,gCAAO;YACb2E,KAAK,EAAE9H,UAAU,CAACE,SAAU;YAC5B6H,MAAM,eAAEjJ,OAAA,CAACJ,cAAc;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BoB,UAAU,EAAE;cAAE7G,KAAK,EAAE;YAAU;UAAE;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9H,OAAA,CAAChB,GAAG;QAAC6J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACzBzH,OAAA,CAACzB,IAAI;UAAAkJ,QAAA,eACHzH,OAAA,CAACd,SAAS;YACRmF,KAAK,EAAC,gCAAO;YACb2E,KAAK,EAAE9H,UAAU,CAACG,WAAY;YAC9B8H,SAAS,EAAE,CAAE;YACbF,MAAM,EAAC,MAAG;YACVC,UAAU,EAAE;cAAE7G,KAAK,EAAE;YAAU;UAAE;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9H,OAAA,CAAChB,GAAG;QAAC6J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACzBzH,OAAA,CAACzB,IAAI;UAAAkJ,QAAA,eACHzH,OAAA,CAACd,SAAS;YACRmF,KAAK,EAAC,0BAAM;YACZ2E,KAAK,EAAE9H,UAAU,CAACI,cAAe;YACjC2H,MAAM,eAAEjJ,OAAA,CAACL,cAAc;cAAAgI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BoB,UAAU,EAAE;cAAE7G,KAAK,EAAE;YAAU;UAAE;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9H,OAAA,CAAChB,GAAG;QAAC6J,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACzBzH,OAAA,CAACzB,IAAI;UAAAkJ,QAAA,eACHzH,OAAA,CAACd,SAAS;YACRmF,KAAK,EAAC,0BAAM;YACZ2E,KAAK,EAAE9H,UAAU,CAACK,WAAY;YAC9B0H,MAAM,eAAEjJ,OAAA,CAACN,YAAY;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBoB,UAAU,EAAE;cAAE7G,KAAK,EAAE;YAAU;UAAE;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9H,OAAA,CAACzB,IAAI;MAAC6K,SAAS,EAAC,aAAa;MAACjB,IAAI,EAAC,OAAO;MAACvB,KAAK,EAAE;QAAEgC,YAAY,EAAE;MAAG,CAAE;MAAAnB,QAAA,eACrEzH,OAAA,CAACjB,GAAG;QAAC4J,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAlB,QAAA,gBACpBzH,OAAA,CAAChB,GAAG;UAAC6J,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAtB,QAAA,eACzBzH,OAAA,CAACrB,KAAK;YACJ0K,WAAW,EAAC,0BAAM;YAClBC,UAAU;YACVN,KAAK,EAAEpH,UAAU,CAACE,WAAY;YAC9ByH,QAAQ,EAAGC,CAAC,IAAK3H,aAAa,CAAC;cAAE,GAAGD,UAAU;cAAEE,WAAW,EAAE0H,CAAC,CAACC,MAAM,CAACT;YAAM,CAAC;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9H,OAAA,CAAChB,GAAG;UAAC6J,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAtB,QAAA,eACzBzH,OAAA,CAACrB,KAAK;YACJ0K,WAAW,EAAC,oBAAK;YACjBC,UAAU;YACVN,KAAK,EAAEpH,UAAU,CAACG,QAAS;YAC3BwH,QAAQ,EAAGC,CAAC,IAAK3H,aAAa,CAAC;cAAE,GAAGD,UAAU;cAAEG,QAAQ,EAAEyH,CAAC,CAACC,MAAM,CAACT;YAAM,CAAC;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9H,OAAA,CAAChB,GAAG;UAAC6J,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAtB,QAAA,eACzBzH,OAAA,CAACpB,MAAM;YACLyK,WAAW,EAAC,0BAAM;YAClBC,UAAU;YACV1C,KAAK,EAAE;cAAES,KAAK,EAAE;YAAO,CAAE;YACzB2B,KAAK,EAAEpH,UAAU,CAACI,MAAO;YACzBuH,QAAQ,EAAGP,KAAK,IAAKnH,aAAa,CAAC;cAAE,GAAGD,UAAU;cAAEI,MAAM,EAAEgH;YAAM,CAAC,CAAE;YAAAvB,QAAA,gBAErEzH,OAAA,CAACG,MAAM;cAAC6I,KAAK,EAAC,GAAG;cAAAvB,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7B9H,OAAA,CAACG,MAAM;cAAC6I,KAAK,EAAC,GAAG;cAAAvB,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN9H,OAAA,CAAChB,GAAG;UAAC6J,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAtB,QAAA,eACzBzH,OAAA,CAACtB,KAAK;YAAA+I,QAAA,gBACJzH,OAAA,CAACvB,MAAM;cACL0H,IAAI,EAAC,SAAS;cACd+B,IAAI,eAAElI,OAAA,CAACT,cAAc;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBU,OAAO,EAAEvD,YAAa;cAAAwC,QAAA,EACvB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9H,OAAA,CAACvB,MAAM;cACLyJ,IAAI,eAAElI,OAAA,CAACR,cAAc;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBU,OAAO,EAAEtD,WAAY;cAAAuC,QAAA,EACtB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP9H,OAAA,CAACzB,IAAI;MAAC6K,SAAS,EAAC,aAAa;MAACjB,IAAI,EAAC,OAAO;MAACvB,KAAK,EAAE;QAAEgC,YAAY,EAAE;MAAG,CAAE;MAAAnB,QAAA,eACrEzH,OAAA,CAACjB,GAAG;QAAC2K,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAlC,QAAA,gBACzCzH,OAAA,CAAChB,GAAG;UAAAyI,QAAA,eACFzH,OAAA,CAACtB,KAAK;YAAA+I,QAAA,eACJzH,OAAA,CAACvB,MAAM;cACLyJ,IAAI,eAAElI,OAAA,CAACP,cAAc;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBU,OAAO,EAAEnD,YAAa;cAAAoC,QAAA,EACvB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9H,OAAA,CAAChB,GAAG;UAAAyI,QAAA,eACFzH,OAAA,CAACvB,MAAM;YACLyJ,IAAI,eAAElI,OAAA,CAACR,cAAc;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBU,OAAO,EAAEjF,eAAgB;YACzB7C,OAAO,EAAEA,OAAQ;YAAA+G,QAAA,EAClB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP9H,OAAA,CAACzB,IAAI;MAAAkJ,QAAA,eACHzH,OAAA,CAACxB,KAAK;QACJ0I,OAAO,EAAEA,OAAQ;QACjB0C,UAAU,EAAEpJ,UAAW;QACvBqJ,MAAM,EAAC,IAAI;QACXnJ,OAAO,EAAEA,OAAQ;QACjBoJ,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAEnJ,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZsJ,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACxJ,KAAK,EAAEyJ,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQzJ,KAAK,IAAI;UAC5C2I,QAAQ,EAAEA,CAACvI,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP9H,OAAA,CAACf,KAAK;MACJoF,KAAK,EAAC,sCAAQ;MACdiG,IAAI,EAAE5I,oBAAqB;MAC3B6I,QAAQ,EAAEA,CAAA,KAAM5I,uBAAuB,CAAC,KAAK,CAAE;MAC/C6I,MAAM,EAAE,cACNxK,OAAA,CAACvB,MAAM;QAAa+J,OAAO,EAAEA,CAAA,KAAM7G,uBAAuB,CAAC,KAAK,CAAE;QAAA8F,QAAA,EAAC;MAEnE,GAFY,OAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFT,KAAK,EAAE,GAAI;MAAAI,QAAA,EAEVjG,cAAc,iBACbxB,OAAA;QAAAyH,QAAA,eACEzH,OAAA,CAACjB,GAAG;UAAC4J,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACpBzH,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxB9H,OAAA;cAAAyH,QAAA,EAAMjG,cAAc,CAACqB;YAAY;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAAyH,QAAA,EAAMjG,cAAc,CAACM;YAAW;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAAyH,QAAA,EAAMjG,cAAc,CAACuB;YAAW;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAAyH,QAAA,EAAMjG,cAAc,CAACO;YAAQ;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAK4G,KAAK,EAAE;gBAAEvE,KAAK,EAAE,MAAM;gBAAEqF,UAAU,EAAE;cAAO,CAAE;cAAAD,QAAA,GAAC,MAChD,EAACjG,cAAc,CAACyB,SAAS,CAACuC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAAyH,QAAA,GAAK,MAAC,EAACjG,cAAc,CAAC6B,YAAY,CAACmC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAAyH,QAAA,GAAK,MAAC,EAACjG,cAAc,CAAC8B,aAAa,CAACkC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAAyH,QAAA,eACEzH,OAAA,CAACnB,GAAG;gBAACwD,KAAK,EAAE,EAAA/B,qBAAA,GAAA4B,YAAY,CAACV,cAAc,CAACQ,MAAM,CAAC,cAAA1B,qBAAA,uBAAnCA,qBAAA,CAAqC+B,KAAK,KAAI,SAAU;gBAAAoF,QAAA,EACjE,EAAAlH,sBAAA,GAAA2B,YAAY,CAACV,cAAc,CAACQ,MAAM,CAAC,cAAAzB,sBAAA,uBAAnCA,sBAAA,CAAqC6B,KAAK,KAAI;cAAI;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAAyH,QAAA,EAAM5H,KAAK,CAAC2B,cAAc,CAAC0B,OAAO,CAAC,CAACoF,MAAM,CAAC,qBAAqB;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACN9H,OAAA,CAAChB,GAAG;YAACyL,IAAI,EAAE,EAAG;YAAAhD,QAAA,gBACZzH,OAAA,CAACE,IAAI;cAACwK,MAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9H,OAAA;cAAAyH,QAAA,eACEzH,OAAA,CAACnB,GAAG;gBAACwD,KAAK,EAAEb,cAAc,CAAC2B,SAAS,GAAG,OAAO,GAAG,KAAM;gBAAAsE,QAAA,EACpDjG,cAAc,CAAC2B,SAAS,GAAG,GAAG,GAAG;cAAG;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzH,EAAA,CAllBID,UAAoB;AAAAuK,EAAA,GAApBvK,UAAoB;AAolB1B,eAAeA,UAAU;AAAC,IAAAuK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}