{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/LogoutButton/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Button, Modal, message } from 'antd';\nimport { LogoutOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LogoutButton = ({\n  type = 'default',\n  size = 'middle',\n  block = false,\n  icon = true,\n  children,\n  className,\n  style\n}) => {\n  _s();\n  const {\n    logout\n  } = useAuth();\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n  const handleLogout = async () => {\n    setLogoutLoading(true);\n    try {\n      await logout();\n      message.success('登出成功');\n      // 跳转到登录页\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      message.error('登出失败，请重试');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n    }\n  };\n  const showLogoutModal = () => {\n    setLogoutModalVisible(true);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      type: type,\n      size: size,\n      block: block,\n      icon: icon ? /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 22\n      }, this) : undefined,\n      onClick: showLogoutModal,\n      className: className,\n      style: style,\n      children: children || '退出登录'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u786E\\u8BA4\\u767B\\u51FA\",\n      open: logoutModalVisible,\n      onOk: handleLogout,\n      onCancel: () => setLogoutModalVisible(false),\n      okText: \"\\u786E\\u8BA4\\u767B\\u51FA\",\n      cancelText: \"\\u53D6\\u6D88\",\n      okType: \"danger\",\n      confirmLoading: logoutLoading,\n      centered: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u60A8\\u786E\\u5B9A\\u8981\\u9000\\u51FA\\u767B\\u5F55\\u5417\\uFF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: \"\\u9000\\u51FA\\u540E\\u9700\\u8981\\u91CD\\u65B0\\u767B\\u5F55\\u624D\\u80FD\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u529F\\u80FD\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(LogoutButton, \"pPuVgYpHmyvT0u7nw4IpOpGGp9E=\", false, function () {\n  return [useAuth];\n});\n_c = LogoutButton;\nexport default LogoutButton;\nvar _c;\n$RefreshReg$(_c, \"LogoutButton\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "Modal", "message", "LogoutOutlined", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LogoutButton", "type", "size", "block", "icon", "children", "className", "style", "_s", "logout", "logoutModalVisible", "setLogoutModalVisible", "logoutLoading", "setLogoutLoading", "handleLogout", "success", "window", "location", "href", "error", "console", "showLogoutModal", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "undefined", "onClick", "title", "open", "onOk", "onCancel", "okText", "cancelText", "okType", "confirmLoading", "centered", "padding", "color", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/LogoutButton/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Button, Modal, message } from 'antd';\nimport { LogoutOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../hooks/useAuth';\n\ninterface LogoutButtonProps {\n  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';\n  size?: 'large' | 'middle' | 'small';\n  block?: boolean;\n  icon?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst LogoutButton: React.FC<LogoutButtonProps> = ({\n  type = 'default',\n  size = 'middle',\n  block = false,\n  icon = true,\n  children,\n  className,\n  style,\n}) => {\n  const { logout } = useAuth();\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n\n  const handleLogout = async () => {\n    setLogoutLoading(true);\n    try {\n      await logout();\n      message.success('登出成功');\n      // 跳转到登录页\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('Logout error:', error);\n      message.error('登出失败，请重试');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n    }\n  };\n\n  const showLogoutModal = () => {\n    setLogoutModalVisible(true);\n  };\n\n  return (\n    <>\n      <Button\n        type={type}\n        size={size}\n        block={block}\n        icon={icon ? <LogoutOutlined /> : undefined}\n        onClick={showLogoutModal}\n        className={className}\n        style={style}\n      >\n        {children || '退出登录'}\n      </Button>\n\n      {/* 登出确认弹窗 */}\n      <Modal\n        title=\"确认登出\"\n        open={logoutModalVisible}\n        onOk={handleLogout}\n        onCancel={() => setLogoutModalVisible(false)}\n        okText=\"确认登出\"\n        cancelText=\"取消\"\n        okType=\"danger\"\n        confirmLoading={logoutLoading}\n        centered\n      >\n        <div style={{ padding: '20px 0' }}>\n          <p>您确定要退出登录吗？</p>\n          <p style={{ color: '#666', fontSize: '14px' }}>\n            退出后需要重新登录才能继续使用系统功能。\n          </p>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default LogoutButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY9C,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,IAAI,GAAG,SAAS;EAChBC,IAAI,GAAG,QAAQ;EACfC,KAAK,GAAG,KAAK;EACbC,IAAI,GAAG,IAAI;EACXC,QAAQ;EACRC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAO,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC5B,MAAM,CAACe,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAMwB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BD,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMJ,MAAM,CAAC,CAAC;MACdhB,OAAO,CAACsB,OAAO,CAAC,MAAM,CAAC;MACvB;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRN,gBAAgB,CAAC,KAAK,CAAC;MACvBF,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BV,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,oBACEd,OAAA,CAAAE,SAAA;IAAAM,QAAA,gBACER,OAAA,CAACN,MAAM;MACLU,IAAI,EAAEA,IAAK;MACXC,IAAI,EAAEA,IAAK;MACXC,KAAK,EAAEA,KAAM;MACbC,IAAI,EAAEA,IAAI,gBAAGP,OAAA,CAACH,cAAc;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAAGC,SAAU;MAC5CC,OAAO,EAAEN,eAAgB;MACzBf,SAAS,EAAEA,SAAU;MACrBC,KAAK,EAAEA,KAAM;MAAAF,QAAA,EAEZA,QAAQ,IAAI;IAAM;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAGT5B,OAAA,CAACL,KAAK;MACJoC,KAAK,EAAC,0BAAM;MACZC,IAAI,EAAEnB,kBAAmB;MACzBoB,IAAI,EAAEhB,YAAa;MACnBiB,QAAQ,EAAEA,CAAA,KAAMpB,qBAAqB,CAAC,KAAK,CAAE;MAC7CqB,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MACfC,MAAM,EAAC,QAAQ;MACfC,cAAc,EAAEvB,aAAc;MAC9BwB,QAAQ;MAAA/B,QAAA,eAERR,OAAA;QAAKU,KAAK,EAAE;UAAE8B,OAAO,EAAE;QAAS,CAAE;QAAAhC,QAAA,gBAChCR,OAAA;UAAAQ,QAAA,EAAG;QAAU;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjB5B,OAAA;UAAGU,KAAK,EAAE;YAAE+B,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAlC,QAAA,EAAC;QAE/C;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACjB,EAAA,CApEIR,YAAyC;EAAA,QAS1BL,OAAO;AAAA;AAAA6C,EAAA,GATtBxC,YAAyC;AAsE/C,eAAeA,YAAY;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}