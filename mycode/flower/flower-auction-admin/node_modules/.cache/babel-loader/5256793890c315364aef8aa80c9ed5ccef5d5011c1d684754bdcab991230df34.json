{"ast": null, "code": "import { apiClient } from './apiClient';\nexport const authService = {\n  // 用户登录\n  login: async credentials => {\n    try {\n      var _response$data;\n      const response = await apiClient.post('/auth/login', credentials);\n\n      // 后端返回：{ success: true, data: { user: {...}, token: \"...\" }, message: \"...\" }\n      console.log('Login response:', response.data);\n      if (response.data && response.data.success && response.data.data) {\n        const responseData = response.data.data;\n        if (responseData.user && responseData.token) {\n          return {\n            success: true,\n            data: {\n              token: responseData.token,\n              refreshToken: responseData.refreshToken || responseData.token,\n              // 如果没有refreshToken，使用token\n              expiresAt: responseData.expiresAt,\n              user: {\n                id: responseData.user.id,\n                username: responseData.user.username,\n                email: responseData.user.email || '',\n                role: responseData.user.userType === 3 ? 'admin' : responseData.user.userType === 1 ? 'auctioneer' : 'buyer',\n                realName: responseData.user.realName,\n                userType: responseData.user.userType,\n                phone: responseData.user.phone,\n                companyName: responseData.user.companyName,\n                balance: responseData.user.balance,\n                frozenAmount: responseData.user.frozenAmount,\n                creditLevel: responseData.user.creditLevel,\n                roles: responseData.user.roles\n              }\n            }\n          };\n        }\n      }\n      return {\n        success: false,\n        message: ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '登录响应数据格式错误'\n      };\n    } catch (error) {\n      console.error('Login error:', error);\n\n      // 处理不同类型的网络错误\n      let errorMessage = '登录失败';\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        errorMessage = '无法连接到服务器，请检查服务器是否启动';\n      } else if (error.code === 'TIMEOUT' || error.code === 'ECONNABORTED') {\n        errorMessage = '请求超时，请检查网络连接';\n      } else if (error.response) {\n        // 服务器响应了错误状态码\n        const status = error.response.status;\n        if (status >= 500) {\n          errorMessage = '服务器内部错误，请稍后重试';\n        } else if (status === 404) {\n          errorMessage = '登录接口不存在，请联系系统管理员';\n        } else if (status === 401) {\n          errorMessage = '用户名或密码错误';\n        } else {\n          var _error$response$data, _error$response$data2;\n          errorMessage = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.message) || `服务器错误 (${status})`;\n        }\n      } else if (error.request) {\n        // 请求已发出但没有收到响应\n        errorMessage = '服务器无响应，请检查网络连接或服务器状态';\n      } else {\n        // 其他错误\n        errorMessage = error.message || '未知错误';\n      }\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n  // 用户登出\n  logout: async () => {\n    try {\n      // 调用后端登出API，使token失效\n      const response = await apiClient.post('/auth/logout');\n\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n\n      // 清除会话存储\n      sessionStorage.removeItem('token');\n      sessionStorage.removeItem('refreshToken');\n      sessionStorage.removeItem('user');\n\n      // 清除管理端相关的缓存数据\n      localStorage.removeItem('admin_preferences');\n      localStorage.removeItem('admin_dashboard_config');\n      localStorage.removeItem('admin_recent_actions');\n      return response.data;\n    } catch (error) {\n      var _error$response, _error$response$data3;\n      // 即使后端调用失败，也要清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('token');\n      sessionStorage.removeItem('refreshToken');\n      sessionStorage.removeItem('user');\n      localStorage.removeItem('admin_preferences');\n      localStorage.removeItem('admin_dashboard_config');\n      localStorage.removeItem('admin_recent_actions');\n      throw new Error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data3 = _error$response.data) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) || '登出失败');\n    }\n  },\n  // 获取用户信息\n  getUserInfo: async () => {\n    try {\n      const response = await apiClient.get('/auth/me');\n\n      // 后端返回的数据结构需要转换\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          data: {\n            id: response.data.data.id,\n            username: response.data.data.username,\n            email: response.data.data.email || '',\n            role: response.data.data.role || 'user',\n            realName: response.data.data.realName\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '获取用户信息失败'\n        };\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message || '获取用户信息失败'\n      };\n    }\n  },\n  // 刷新token\n  refreshToken: async refreshToken => {\n    try {\n      const response = await apiClient.post('/auth/refresh', {\n        refreshToken\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '刷新token失败');\n    }\n  },\n  // 修改密码\n  changePassword: async (oldPassword, newPassword) => {\n    try {\n      const response = await apiClient.post('/auth/change-password', {\n        oldPassword,\n        newPassword\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '修改密码失败');\n    }\n  }\n};", "map": {"version": 3, "names": ["apiClient", "authService", "login", "credentials", "_response$data", "response", "post", "console", "log", "data", "success", "responseData", "user", "token", "refreshToken", "expiresAt", "id", "username", "email", "role", "userType", "realName", "phone", "companyName", "balance", "frozenAmount", "creditLevel", "roles", "message", "error", "errorMessage", "code", "status", "_error$response$data", "_error$response$data2", "request", "logout", "localStorage", "removeItem", "sessionStorage", "_error$response", "_error$response$data3", "Error", "getUserInfo", "get", "_error$response2", "_error$response2$data", "_error$response3", "_error$response3$data", "changePassword", "oldPassword", "newPassword", "_error$response4", "_error$response4$data"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/services/authService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\nimport { LoginCredentials, User } from '../hooks/useAuth';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n}\n\nexport interface LoginResponse {\n  token: string;\n  refreshToken: string;\n  expiresAt?: number;\n  user: User;\n}\n\nexport const authService = {\n  // 用户登录\n  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {\n    try {\n      const response = await apiClient.post('/auth/login', credentials);\n\n      // 后端返回：{ success: true, data: { user: {...}, token: \"...\" }, message: \"...\" }\n      console.log('Login response:', response.data);\n\n      if (response.data && response.data.success && response.data.data) {\n        const responseData = response.data.data;\n        if (responseData.user && responseData.token) {\n          return {\n            success: true,\n            data: {\n              token: responseData.token,\n              refreshToken: responseData.refreshToken || responseData.token, // 如果没有refreshToken，使用token\n              expiresAt: responseData.expiresAt,\n              user: {\n                id: responseData.user.id,\n                username: responseData.user.username,\n                email: responseData.user.email || '',\n                role: responseData.user.userType === 3 ? 'admin' :\n                      responseData.user.userType === 1 ? 'auctioneer' : 'buyer',\n                realName: responseData.user.realName,\n                userType: responseData.user.userType,\n                phone: responseData.user.phone,\n                companyName: responseData.user.companyName,\n                balance: responseData.user.balance,\n                frozenAmount: responseData.user.frozenAmount,\n                creditLevel: responseData.user.creditLevel,\n                roles: responseData.user.roles,\n              }\n            }\n          };\n        }\n      }\n\n      return {\n        success: false,\n        message: response.data?.message || '登录响应数据格式错误'\n      };\n    } catch (error: any) {\n      console.error('Login error:', error);\n\n      // 处理不同类型的网络错误\n      let errorMessage = '登录失败';\n\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        errorMessage = '无法连接到服务器，请检查服务器是否启动';\n      } else if (error.code === 'TIMEOUT' || error.code === 'ECONNABORTED') {\n        errorMessage = '请求超时，请检查网络连接';\n      } else if (error.response) {\n        // 服务器响应了错误状态码\n        const status = error.response.status;\n        if (status >= 500) {\n          errorMessage = '服务器内部错误，请稍后重试';\n        } else if (status === 404) {\n          errorMessage = '登录接口不存在，请联系系统管理员';\n        } else if (status === 401) {\n          errorMessage = '用户名或密码错误';\n        } else {\n          errorMessage = error.response.data?.error || error.response.data?.message || `服务器错误 (${status})`;\n        }\n      } else if (error.request) {\n        // 请求已发出但没有收到响应\n        errorMessage = '服务器无响应，请检查网络连接或服务器状态';\n      } else {\n        // 其他错误\n        errorMessage = error.message || '未知错误';\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n\n  // 用户登出\n  logout: async (): Promise<ApiResponse> => {\n    try {\n      // 调用后端登出API，使token失效\n      const response = await apiClient.post('/auth/logout');\n\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n\n      // 清除会话存储\n      sessionStorage.removeItem('token');\n      sessionStorage.removeItem('refreshToken');\n      sessionStorage.removeItem('user');\n\n      // 清除管理端相关的缓存数据\n      localStorage.removeItem('admin_preferences');\n      localStorage.removeItem('admin_dashboard_config');\n      localStorage.removeItem('admin_recent_actions');\n\n      return response.data;\n    } catch (error: any) {\n      // 即使后端调用失败，也要清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n      sessionStorage.removeItem('token');\n      sessionStorage.removeItem('refreshToken');\n      sessionStorage.removeItem('user');\n      localStorage.removeItem('admin_preferences');\n      localStorage.removeItem('admin_dashboard_config');\n      localStorage.removeItem('admin_recent_actions');\n\n      throw new Error(error.response?.data?.message || '登出失败');\n    }\n  },\n\n  // 获取用户信息\n  getUserInfo: async (): Promise<ApiResponse<User>> => {\n    try {\n      const response = await apiClient.get('/auth/me');\n\n      // 后端返回的数据结构需要转换\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          data: {\n            id: response.data.data.id,\n            username: response.data.data.username,\n            email: response.data.data.email || '',\n            role: response.data.data.role || 'user',\n            realName: response.data.data.realName,\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '获取用户信息失败'\n        };\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        message: error.response?.data?.error || error.message || '获取用户信息失败'\n      };\n    }\n  },\n\n  // 刷新token\n  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {\n    try {\n      const response = await apiClient.post('/auth/refresh', { refreshToken });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '刷新token失败');\n    }\n  },\n\n  // 修改密码\n  changePassword: async (oldPassword: string, newPassword: string): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.post('/auth/change-password', {\n        oldPassword,\n        newPassword,\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '修改密码失败');\n    }\n  },\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAgBvC,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,KAAK,EAAE,MAAOC,WAA6B,IAA0C;IACnF,IAAI;MAAA,IAAAC,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAML,SAAS,CAACM,IAAI,CAAC,aAAa,EAAEH,WAAW,CAAC;;MAEjE;MACAI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,QAAQ,CAACI,IAAI,CAAC;MAE7C,IAAIJ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,IAAIL,QAAQ,CAACI,IAAI,CAACA,IAAI,EAAE;QAChE,MAAME,YAAY,GAAGN,QAAQ,CAACI,IAAI,CAACA,IAAI;QACvC,IAAIE,YAAY,CAACC,IAAI,IAAID,YAAY,CAACE,KAAK,EAAE;UAC3C,OAAO;YACLH,OAAO,EAAE,IAAI;YACbD,IAAI,EAAE;cACJI,KAAK,EAAEF,YAAY,CAACE,KAAK;cACzBC,YAAY,EAAEH,YAAY,CAACG,YAAY,IAAIH,YAAY,CAACE,KAAK;cAAE;cAC/DE,SAAS,EAAEJ,YAAY,CAACI,SAAS;cACjCH,IAAI,EAAE;gBACJI,EAAE,EAAEL,YAAY,CAACC,IAAI,CAACI,EAAE;gBACxBC,QAAQ,EAAEN,YAAY,CAACC,IAAI,CAACK,QAAQ;gBACpCC,KAAK,EAAEP,YAAY,CAACC,IAAI,CAACM,KAAK,IAAI,EAAE;gBACpCC,IAAI,EAAER,YAAY,CAACC,IAAI,CAACQ,QAAQ,KAAK,CAAC,GAAG,OAAO,GAC1CT,YAAY,CAACC,IAAI,CAACQ,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,OAAO;gBAC/DC,QAAQ,EAAEV,YAAY,CAACC,IAAI,CAACS,QAAQ;gBACpCD,QAAQ,EAAET,YAAY,CAACC,IAAI,CAACQ,QAAQ;gBACpCE,KAAK,EAAEX,YAAY,CAACC,IAAI,CAACU,KAAK;gBAC9BC,WAAW,EAAEZ,YAAY,CAACC,IAAI,CAACW,WAAW;gBAC1CC,OAAO,EAAEb,YAAY,CAACC,IAAI,CAACY,OAAO;gBAClCC,YAAY,EAAEd,YAAY,CAACC,IAAI,CAACa,YAAY;gBAC5CC,WAAW,EAAEf,YAAY,CAACC,IAAI,CAACc,WAAW;gBAC1CC,KAAK,EAAEhB,YAAY,CAACC,IAAI,CAACe;cAC3B;YACF;UACF,CAAC;QACH;MACF;MAEA,OAAO;QACLjB,OAAO,EAAE,KAAK;QACdkB,OAAO,EAAE,EAAAxB,cAAA,GAAAC,QAAQ,CAACI,IAAI,cAAAL,cAAA,uBAAbA,cAAA,CAAewB,OAAO,KAAI;MACrC,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;;MAEpC;MACA,IAAIC,YAAY,GAAG,MAAM;MAEzB,IAAID,KAAK,CAACE,IAAI,KAAK,cAAc,IAAIF,KAAK,CAACE,IAAI,KAAK,aAAa,EAAE;QACjED,YAAY,GAAG,qBAAqB;MACtC,CAAC,MAAM,IAAID,KAAK,CAACE,IAAI,KAAK,SAAS,IAAIF,KAAK,CAACE,IAAI,KAAK,cAAc,EAAE;QACpED,YAAY,GAAG,cAAc;MAC/B,CAAC,MAAM,IAAID,KAAK,CAACxB,QAAQ,EAAE;QACzB;QACA,MAAM2B,MAAM,GAAGH,KAAK,CAACxB,QAAQ,CAAC2B,MAAM;QACpC,IAAIA,MAAM,IAAI,GAAG,EAAE;UACjBF,YAAY,GAAG,eAAe;QAChC,CAAC,MAAM,IAAIE,MAAM,KAAK,GAAG,EAAE;UACzBF,YAAY,GAAG,kBAAkB;QACnC,CAAC,MAAM,IAAIE,MAAM,KAAK,GAAG,EAAE;UACzBF,YAAY,GAAG,UAAU;QAC3B,CAAC,MAAM;UAAA,IAAAG,oBAAA,EAAAC,qBAAA;UACLJ,YAAY,GAAG,EAAAG,oBAAA,GAAAJ,KAAK,CAACxB,QAAQ,CAACI,IAAI,cAAAwB,oBAAA,uBAAnBA,oBAAA,CAAqBJ,KAAK,OAAAK,qBAAA,GAAIL,KAAK,CAACxB,QAAQ,CAACI,IAAI,cAAAyB,qBAAA,uBAAnBA,qBAAA,CAAqBN,OAAO,KAAI,UAAUI,MAAM,GAAG;QAClG;MACF,CAAC,MAAM,IAAIH,KAAK,CAACM,OAAO,EAAE;QACxB;QACAL,YAAY,GAAG,sBAAsB;MACvC,CAAC,MAAM;QACL;QACAA,YAAY,GAAGD,KAAK,CAACD,OAAO,IAAI,MAAM;MACxC;MAEA,OAAO;QACLlB,OAAO,EAAE,KAAK;QACdkB,OAAO,EAAEE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAM,MAAM,EAAE,MAAAA,CAAA,KAAkC;IACxC,IAAI;MACF;MACA,MAAM/B,QAAQ,GAAG,MAAML,SAAS,CAACM,IAAI,CAAC,cAAc,CAAC;;MAErD;MACA+B,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;MAChCD,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;MACvCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;MAE/B;MACAC,cAAc,CAACD,UAAU,CAAC,OAAO,CAAC;MAClCC,cAAc,CAACD,UAAU,CAAC,cAAc,CAAC;MACzCC,cAAc,CAACD,UAAU,CAAC,MAAM,CAAC;;MAEjC;MACAD,YAAY,CAACC,UAAU,CAAC,mBAAmB,CAAC;MAC5CD,YAAY,CAACC,UAAU,CAAC,wBAAwB,CAAC;MACjDD,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC;MAE/C,OAAOjC,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOoB,KAAU,EAAE;MAAA,IAAAW,eAAA,EAAAC,qBAAA;MACnB;MACAJ,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;MAChCD,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;MACvCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACD,UAAU,CAAC,OAAO,CAAC;MAClCC,cAAc,CAACD,UAAU,CAAC,cAAc,CAAC;MACzCC,cAAc,CAACD,UAAU,CAAC,MAAM,CAAC;MACjCD,YAAY,CAACC,UAAU,CAAC,mBAAmB,CAAC;MAC5CD,YAAY,CAACC,UAAU,CAAC,wBAAwB,CAAC;MACjDD,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC;MAE/C,MAAM,IAAII,KAAK,CAAC,EAAAF,eAAA,GAAAX,KAAK,CAACxB,QAAQ,cAAAmC,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgB/B,IAAI,cAAAgC,qBAAA,uBAApBA,qBAAA,CAAsBb,OAAO,KAAI,MAAM,CAAC;IAC1D;EACF,CAAC;EAED;EACAe,WAAW,EAAE,MAAAA,CAAA,KAAwC;IACnD,IAAI;MACF,MAAMtC,QAAQ,GAAG,MAAML,SAAS,CAAC4C,GAAG,CAAC,UAAU,CAAC;;MAEhD;MACA,IAAIvC,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QAC1C,OAAO;UACLA,OAAO,EAAE,IAAI;UACbD,IAAI,EAAE;YACJO,EAAE,EAAEX,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACO,EAAE;YACzBC,QAAQ,EAAEZ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACQ,QAAQ;YACrCC,KAAK,EAAEb,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACS,KAAK,IAAI,EAAE;YACrCC,IAAI,EAAEd,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACU,IAAI,IAAI,MAAM;YACvCE,QAAQ,EAAEhB,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACY;UAC/B;QACF,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLX,OAAO,EAAE,KAAK;UACdkB,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QACLpC,OAAO,EAAE,KAAK;QACdkB,OAAO,EAAE,EAAAiB,gBAAA,GAAAhB,KAAK,CAACxB,QAAQ,cAAAwC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpC,IAAI,cAAAqC,qBAAA,uBAApBA,qBAAA,CAAsBjB,KAAK,KAAIA,KAAK,CAACD,OAAO,IAAI;MAC3D,CAAC;IACH;EACF,CAAC;EAED;EACAd,YAAY,EAAE,MAAOA,YAAoB,IAAoE;IAC3G,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAML,SAAS,CAACM,IAAI,CAAC,eAAe,EAAE;QAAEQ;MAAa,CAAC,CAAC;MACxE,OAAOT,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOoB,KAAU,EAAE;MAAA,IAAAkB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIN,KAAK,CAAC,EAAAK,gBAAA,GAAAlB,KAAK,CAACxB,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtC,IAAI,cAAAuC,qBAAA,uBAApBA,qBAAA,CAAsBpB,OAAO,KAAI,WAAW,CAAC;IAC/D;EACF,CAAC;EAED;EACAqB,cAAc,EAAE,MAAAA,CAAOC,WAAmB,EAAEC,WAAmB,KAA2B;IACxF,IAAI;MACF,MAAM9C,QAAQ,GAAG,MAAML,SAAS,CAACM,IAAI,CAAC,uBAAuB,EAAE;QAC7D4C,WAAW;QACXC;MACF,CAAC,CAAC;MACF,OAAO9C,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOoB,KAAU,EAAE;MAAA,IAAAuB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIX,KAAK,CAAC,EAAAU,gBAAA,GAAAvB,KAAK,CAACxB,QAAQ,cAAA+C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBzB,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}