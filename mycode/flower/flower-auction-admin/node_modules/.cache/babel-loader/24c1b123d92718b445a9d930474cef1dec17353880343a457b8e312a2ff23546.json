{"ast": null, "code": "import React,{useState}from'react';import{Card,Form,Input,Button,message,Typography,Space,Alert,Table,Tag,Modal,Steps,QRCode}from'antd';import{LockOutlined,SafetyOutlined,DeleteOutlined,SecurityScanOutlined}from'@ant-design/icons';import{LogoutButton}from'../../../components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const{Step}=Steps;const PersonalSecurity=()=>{const[passwordForm]=Form.useForm();const[phoneForm]=Form.useForm();const[emailForm]=Form.useForm();const[loading,setLoading]=useState(false);const[twoFactorModalVisible,setTwoFactorModalVisible]=useState(false);const[twoFactorStep,setTwoFactorStep]=useState(0);const[twoFactorEnabled,setTwoFactorEnabled]=useState(false);const[loginDevices]=useState([{id:'1',deviceName:'MacBook Pro',deviceType:'desktop',browser:'Chrome 120.0',os:'macOS 14.0',ip:'*************',location:'昆明市',lastActive:'2024-01-15 10:30:00',isCurrent:true},{id:'2',deviceName:'iPhone 15',deviceType:'mobile',browser:'Safari 17.0',os:'iOS 17.0',ip:'*************',location:'昆明市',lastActive:'2024-01-14 18:45:00',isCurrent:false}]);const handleChangePassword=async values=>{setLoading(true);try{// 这里应该调用后端API修改密码\n// await authService.changePassword(values);\nmessage.success('密码修改成功，请重新登录');passwordForm.resetFields();// 延迟后跳转到登录页\nsetTimeout(()=>{window.location.href='/login';},2000);}catch(error){console.error('修改密码失败:',error);message.error('修改密码失败');}finally{setLoading(false);}};const handleChangePhone=async values=>{setLoading(true);try{// 这里应该调用后端API修改手机号\n// await userService.changePhone(values);\nmessage.success('手机号修改成功');phoneForm.resetFields();}catch(error){console.error('修改手机号失败:',error);message.error('修改手机号失败');}finally{setLoading(false);}};const handleChangeEmail=async values=>{setLoading(true);try{// 这里应该调用后端API修改邮箱\n// await userService.changeEmail(values);\nmessage.success('邮箱修改成功');emailForm.resetFields();}catch(error){console.error('修改邮箱失败:',error);message.error('修改邮箱失败');}finally{setLoading(false);}};const handleRemoveDevice=deviceId=>{Modal.confirm({title:'移除设备',content:'确定要移除此设备的登录状态吗？该设备将被强制登出。',onOk:async()=>{try{// 这里应该调用后端API移除设备\n// await authService.removeDevice(deviceId);\nmessage.success('设备已移除');}catch(error){console.error('移除设备失败:',error);message.error('移除设备失败');}}});};const handleEnableTwoFactor=()=>{setTwoFactorModalVisible(true);setTwoFactorStep(0);};const deviceColumns=[{title:'设备信息',key:'device',render:record=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:500},children:[record.deviceName,record.isCurrent&&/*#__PURE__*/_jsx(Tag,{color:\"green\",style:{marginLeft:8},children:\"\\u5F53\\u524D\\u8BBE\\u5907\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{color:'#666',fontSize:'12px'},children:[record.browser,\" \\xB7 \",record.os]})]})},{title:'IP地址',dataIndex:'ip',key:'ip'},{title:'位置',dataIndex:'location',key:'location'},{title:'最后活跃',dataIndex:'lastActive',key:'lastActive'},{title:'操作',key:'action',render:record=>/*#__PURE__*/_jsx(Space,{children:!record.isCurrent&&/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>handleRemoveDevice(record.id),children:\"\\u79FB\\u9664\"})})}];return/*#__PURE__*/_jsxs(\"div\",{style:{padding:24},children:[/*#__PURE__*/_jsxs(Title,{level:2,children:[/*#__PURE__*/_jsx(SafetyOutlined,{}),\" \\u5B89\\u5168\\u8BBE\\u7F6E\"]}),/*#__PURE__*/_jsxs(Card,{title:\"\\u4FEE\\u6539\\u5BC6\\u7801\",style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u5BC6\\u7801\\u5B89\\u5168\\u63D0\\u793A\",description:\"\\u4E3A\\u4E86\\u60A8\\u7684\\u8D26\\u6237\\u5B89\\u5168\\uFF0C\\u5EFA\\u8BAE\\u5B9A\\u671F\\u66F4\\u6362\\u5BC6\\u7801\\uFF0C\\u5BC6\\u7801\\u5E94\\u5305\\u542B\\u5927\\u5C0F\\u5199\\u5B57\\u6BCD\\u3001\\u6570\\u5B57\\u548C\\u7279\\u6B8A\\u5B57\\u7B26\\u3002\",type:\"info\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsxs(Form,{form:passwordForm,layout:\"vertical\",onFinish:handleChangePassword,style:{maxWidth:400},children:[/*#__PURE__*/_jsx(Form.Item,{name:\"currentPassword\",label:\"\\u5F53\\u524D\\u5BC6\\u7801\",rules:[{required:true,message:'请输入当前密码'}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(LockOutlined,{})})}),/*#__PURE__*/_jsx(Form.Item,{name:\"newPassword\",label:\"\\u65B0\\u5BC6\\u7801\",rules:[{required:true,message:'请输入新密码'},{min:8,message:'密码至少8个字符'}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(LockOutlined,{})})}),/*#__PURE__*/_jsx(Form.Item,{name:\"confirmPassword\",label:\"\\u786E\\u8BA4\\u65B0\\u5BC6\\u7801\",dependencies:['newPassword'],rules:[{required:true,message:'请确认新密码'},_ref=>{let{getFieldValue}=_ref;return{validator(_,value){if(!value||getFieldValue('newPassword')===value){return Promise.resolve();}return Promise.reject(new Error('两次输入的密码不一致'));}};}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(LockOutlined,{})})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,children:\"\\u4FEE\\u6539\\u5BC6\\u7801\"})})]})]}),/*#__PURE__*/_jsx(Card,{title:\"\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1\",style:{marginBottom:24},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:500,marginBottom:4},children:[\"\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1 \",twoFactorEnabled?/*#__PURE__*/_jsx(Tag,{color:\"green\",children:\"\\u5DF2\\u542F\\u7528\"}):/*#__PURE__*/_jsx(Tag,{color:\"red\",children:\"\\u672A\\u542F\\u7528\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#666'},children:\"\\u542F\\u7528\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1\\u53EF\\u4EE5\\u5927\\u5927\\u63D0\\u9AD8\\u60A8\\u8D26\\u6237\\u7684\\u5B89\\u5168\\u6027\"})]}),/*#__PURE__*/_jsx(Button,{type:twoFactorEnabled?'default':'primary',icon:/*#__PURE__*/_jsx(SecurityScanOutlined,{}),onClick:handleEnableTwoFactor,children:twoFactorEnabled?'管理':'启用'})]})}),/*#__PURE__*/_jsxs(Card,{title:\"\\u767B\\u5F55\\u8BBE\\u5907\\u7BA1\\u7406\",style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u8BBE\\u5907\\u5B89\\u5168\",description:\"\\u5B9A\\u671F\\u68C0\\u67E5\\u767B\\u5F55\\u8BBE\\u5907\\uFF0C\\u5982\\u53D1\\u73B0\\u5F02\\u5E38\\u8BBE\\u5907\\u8BF7\\u53CA\\u65F6\\u79FB\\u9664\\u3002\",type:\"warning\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Table,{columns:deviceColumns,dataSource:loginDevices,rowKey:\"id\",pagination:false,size:\"small\"})]}),/*#__PURE__*/_jsx(Card,{title:\"\\u5B89\\u5168\\u64CD\\u4F5C\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u5371\\u9669\\u64CD\\u4F5C\",description:\"\\u4EE5\\u4E0B\\u64CD\\u4F5C\\u53EF\\u80FD\\u4F1A\\u5F71\\u54CD\\u60A8\\u7684\\u8D26\\u6237\\u5B89\\u5168\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\\u3002\",type:\"error\",showIcon:true}),/*#__PURE__*/_jsx(\"div\",{style:{padding:'16px 0'},children:/*#__PURE__*/_jsx(LogoutButton,{type:\"primary\",style:{backgroundColor:'#ff4d4f',borderColor:'#ff4d4f'},children:\"\\u7ACB\\u5373\\u767B\\u51FA\"})})]})}),/*#__PURE__*/_jsxs(Modal,{title:\"\\u8BBE\\u7F6E\\u53CC\\u56E0\\u5B50\\u8BA4\\u8BC1\",open:twoFactorModalVisible,onCancel:()=>setTwoFactorModalVisible(false),footer:null,width:600,children:[/*#__PURE__*/_jsxs(Steps,{current:twoFactorStep,style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Step,{title:\"\\u4E0B\\u8F7D\\u5E94\\u7528\"}),/*#__PURE__*/_jsx(Step,{title:\"\\u626B\\u63CF\\u4E8C\\u7EF4\\u7801\"}),/*#__PURE__*/_jsx(Step,{title:\"\\u9A8C\\u8BC1\\u7ED1\\u5B9A\"})]}),twoFactorStep===0&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16},children:\"\\u8BF7\\u5148\\u4E0B\\u8F7D\\u5E76\\u5B89\\u88C5\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5668\\u5E94\\u7528\\uFF0C\\u63A8\\u8350\\u4F7F\\u7528\\uFF1A\"}),/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{children:\"Google Authenticator\"}),/*#__PURE__*/_jsx(Button,{children:\"Microsoft Authenticator\"}),/*#__PURE__*/_jsx(Button,{children:\"Authy\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>setTwoFactorStep(1),children:\"\\u4E0B\\u4E00\\u6B65\"})})]}),twoFactorStep===1&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16},children:\"\\u8BF7\\u4F7F\\u7528\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5668\\u5E94\\u7528\\u626B\\u63CF\\u4E0B\\u65B9\\u4E8C\\u7EF4\\u7801\\uFF1A\"}),/*#__PURE__*/_jsx(QRCode,{value:\"otpauth://totp/FlowerAuction:admin?secret=JBSWY3DPEHPK3PXP&issuer=FlowerAuction\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:16},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setTwoFactorStep(0),style:{marginRight:8},children:\"\\u4E0A\\u4E00\\u6B65\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>setTwoFactorStep(2),children:\"\\u4E0B\\u4E00\\u6B65\"})]})]}),twoFactorStep===2&&/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16},children:\"\\u8BF7\\u8F93\\u5165\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u5668\\u5E94\\u7528\\u4E2D\\u663E\\u793A\\u76846\\u4F4D\\u6570\\u5B57\\u9A8C\\u8BC1\\u7801\\uFF1A\"}),/*#__PURE__*/_jsx(Input,{style:{width:200,textAlign:'center',fontSize:'18px'},maxLength:6,placeholder:\"000000\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:16},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setTwoFactorStep(1),style:{marginRight:8},children:\"\\u4E0A\\u4E00\\u6B65\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>{setTwoFactorEnabled(true);setTwoFactorModalVisible(false);message.success('双因子认证设置成功');},children:\"\\u5B8C\\u6210\\u8BBE\\u7F6E\"})]})]})]})]});};export default PersonalSecurity;", "map": {"version": 3, "names": ["React", "useState", "Card", "Form", "Input", "<PERSON><PERSON>", "message", "Typography", "Space", "<PERSON><PERSON>", "Table", "Tag", "Modal", "Steps", "QRCode", "LockOutlined", "SafetyOutlined", "DeleteOutlined", "SecurityScanOutlined", "LogoutButton", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Step", "PersonalSecurity", "passwordForm", "useForm", "phoneForm", "emailForm", "loading", "setLoading", "twoFactorModalVisible", "setTwoFactorModalVisible", "twoFactorStep", "setTwoFactorStep", "twoFactorEnabled", "setTwoFactorEnabled", "loginDevices", "id", "deviceName", "deviceType", "browser", "os", "ip", "location", "lastActive", "isCurrent", "handleChangePassword", "values", "success", "resetFields", "setTimeout", "window", "href", "error", "console", "handleChangePhone", "handleChangeEmail", "handleRemoveDevice", "deviceId", "confirm", "title", "content", "onOk", "handleEnableTwoFactor", "deviceColumns", "key", "render", "record", "children", "style", "fontWeight", "color", "marginLeft", "fontSize", "dataIndex", "type", "size", "danger", "icon", "onClick", "padding", "level", "marginBottom", "description", "showIcon", "form", "layout", "onFinish", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "name", "label", "rules", "required", "Password", "prefix", "min", "dependencies", "_ref", "getFieldValue", "validator", "_", "value", "Promise", "resolve", "reject", "Error", "htmlType", "display", "justifyContent", "alignItems", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "direction", "width", "backgroundColor", "borderColor", "open", "onCancel", "footer", "current", "textAlign", "marginTop", "marginRight", "max<PERSON><PERSON><PERSON>", "placeholder"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/pages/Profile/PersonalSecurity/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  message,\n  Typography,\n  Space,\n  Alert,\n  Table,\n  Tag,\n  Modal,\n  Steps,\n  QRCode,\n} from 'antd';\nimport {\n  LockOutlined,\n  SafetyOutlined,\n  DeleteOutlined,\n  SecurityScanOutlined,\n} from '@ant-design/icons';\nimport { LogoutButton } from '../../../components';\n\nconst { Title } = Typography;\nconst { Step } = Steps;\n\ninterface LoginDevice {\n  id: string;\n  deviceName: string;\n  deviceType: 'desktop' | 'mobile' | 'tablet';\n  browser: string;\n  os: string;\n  ip: string;\n  location: string;\n  lastActive: string;\n  isCurrent: boolean;\n}\n\nconst PersonalSecurity: React.FC = () => {\n  const [passwordForm] = Form.useForm();\n  const [phoneForm] = Form.useForm();\n  const [emailForm] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [twoFactorModalVisible, setTwoFactorModalVisible] = useState(false);\n  const [twoFactorStep, setTwoFactorStep] = useState(0);\n  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);\n\n  const [loginDevices] = useState<LoginDevice[]>([\n    {\n      id: '1',\n      deviceName: 'MacBook Pro',\n      deviceType: 'desktop',\n      browser: 'Chrome 120.0',\n      os: 'macOS 14.0',\n      ip: '*************',\n      location: '昆明市',\n      lastActive: '2024-01-15 10:30:00',\n      isCurrent: true,\n    },\n    {\n      id: '2',\n      deviceName: 'iPhone 15',\n      deviceType: 'mobile',\n      browser: 'Safari 17.0',\n      os: 'iOS 17.0',\n      ip: '*************',\n      location: '昆明市',\n      lastActive: '2024-01-14 18:45:00',\n      isCurrent: false,\n    },\n  ]);\n\n  const handleChangePassword = async (values: any) => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改密码\n      // await authService.changePassword(values);\n      \n      message.success('密码修改成功，请重新登录');\n      passwordForm.resetFields();\n      \n      // 延迟后跳转到登录页\n      setTimeout(() => {\n        window.location.href = '/login';\n      }, 2000);\n    } catch (error) {\n      console.error('修改密码失败:', error);\n      message.error('修改密码失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChangePhone = async (values: any) => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改手机号\n      // await userService.changePhone(values);\n      \n      message.success('手机号修改成功');\n      phoneForm.resetFields();\n    } catch (error) {\n      console.error('修改手机号失败:', error);\n      message.error('修改手机号失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChangeEmail = async (values: any) => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API修改邮箱\n      // await userService.changeEmail(values);\n      \n      message.success('邮箱修改成功');\n      emailForm.resetFields();\n    } catch (error) {\n      console.error('修改邮箱失败:', error);\n      message.error('修改邮箱失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRemoveDevice = (deviceId: string) => {\n    Modal.confirm({\n      title: '移除设备',\n      content: '确定要移除此设备的登录状态吗？该设备将被强制登出。',\n      onOk: async () => {\n        try {\n          // 这里应该调用后端API移除设备\n          // await authService.removeDevice(deviceId);\n          \n          message.success('设备已移除');\n        } catch (error) {\n          console.error('移除设备失败:', error);\n          message.error('移除设备失败');\n        }\n      },\n    });\n  };\n\n  const handleEnableTwoFactor = () => {\n    setTwoFactorModalVisible(true);\n    setTwoFactorStep(0);\n  };\n\n  const deviceColumns = [\n    {\n      title: '设备信息',\n      key: 'device',\n      render: (record: LoginDevice) => (\n        <div>\n          <div style={{ fontWeight: 500 }}>\n            {record.deviceName}\n            {record.isCurrent && <Tag color=\"green\" style={{ marginLeft: 8 }}>当前设备</Tag>}\n          </div>\n          <div style={{ color: '#666', fontSize: '12px' }}>\n            {record.browser} · {record.os}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ip',\n      key: 'ip',\n    },\n    {\n      title: '位置',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: '最后活跃',\n      dataIndex: 'lastActive',\n      key: 'lastActive',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (record: LoginDevice) => (\n        <Space>\n          {!record.isCurrent && (\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => handleRemoveDevice(record.id)}\n            >\n              移除\n            </Button>\n          )}\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>\n        <SafetyOutlined /> 安全设置\n      </Title>\n\n      {/* 密码修改 */}\n      <Card title=\"修改密码\" style={{ marginBottom: 24 }}>\n        <Alert\n          message=\"密码安全提示\"\n          description=\"为了您的账户安全，建议定期更换密码，密码应包含大小写字母、数字和特殊字符。\"\n          type=\"info\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        \n        <Form\n          form={passwordForm}\n          layout=\"vertical\"\n          onFinish={handleChangePassword}\n          style={{ maxWidth: 400 }}\n        >\n          <Form.Item\n            name=\"currentPassword\"\n            label=\"当前密码\"\n            rules={[{ required: true, message: '请输入当前密码' }]}\n          >\n            <Input.Password prefix={<LockOutlined />} />\n          </Form.Item>\n\n          <Form.Item\n            name=\"newPassword\"\n            label=\"新密码\"\n            rules={[\n              { required: true, message: '请输入新密码' },\n              { min: 8, message: '密码至少8个字符' },\n            ]}\n          >\n            <Input.Password prefix={<LockOutlined />} />\n          </Form.Item>\n\n          <Form.Item\n            name=\"confirmPassword\"\n            label=\"确认新密码\"\n            dependencies={['newPassword']}\n            rules={[\n              { required: true, message: '请确认新密码' },\n              ({ getFieldValue }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('newPassword') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                },\n              }),\n            ]}\n          >\n            <Input.Password prefix={<LockOutlined />} />\n          </Form.Item>\n\n          <Form.Item>\n            <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n              修改密码\n            </Button>\n          </Form.Item>\n        </Form>\n      </Card>\n\n      {/* 双因子认证 */}\n      <Card title=\"双因子认证\" style={{ marginBottom: 24 }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <div style={{ fontWeight: 500, marginBottom: 4 }}>\n              双因子认证 {twoFactorEnabled ? <Tag color=\"green\">已启用</Tag> : <Tag color=\"red\">未启用</Tag>}\n            </div>\n            <div style={{ color: '#666' }}>\n              启用双因子认证可以大大提高您账户的安全性\n            </div>\n          </div>\n          <Button\n            type={twoFactorEnabled ? 'default' : 'primary'}\n            icon={<SecurityScanOutlined />}\n            onClick={handleEnableTwoFactor}\n          >\n            {twoFactorEnabled ? '管理' : '启用'}\n          </Button>\n        </div>\n      </Card>\n\n      {/* 登录设备管理 */}\n      <Card title=\"登录设备管理\" style={{ marginBottom: 24 }}>\n        <Alert\n          message=\"设备安全\"\n          description=\"定期检查登录设备，如发现异常设备请及时移除。\"\n          type=\"warning\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        \n        <Table\n          columns={deviceColumns}\n          dataSource={loginDevices}\n          rowKey=\"id\"\n          pagination={false}\n          size=\"small\"\n        />\n      </Card>\n\n      {/* 安全操作 */}\n      <Card title=\"安全操作\">\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Alert\n            message=\"危险操作\"\n            description=\"以下操作可能会影响您的账户安全，请谨慎操作。\"\n            type=\"error\"\n            showIcon\n          />\n          \n          <div style={{ padding: '16px 0' }}>\n            <LogoutButton type=\"primary\" style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}>\n              立即登出\n            </LogoutButton>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 双因子认证设置弹窗 */}\n      <Modal\n        title=\"设置双因子认证\"\n        open={twoFactorModalVisible}\n        onCancel={() => setTwoFactorModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Steps current={twoFactorStep} style={{ marginBottom: 24 }}>\n          <Step title=\"下载应用\" />\n          <Step title=\"扫描二维码\" />\n          <Step title=\"验证绑定\" />\n        </Steps>\n\n        {twoFactorStep === 0 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ marginBottom: 16 }}>\n              请先下载并安装身份验证器应用，推荐使用：\n            </div>\n            <Space>\n              <Button>Google Authenticator</Button>\n              <Button>Microsoft Authenticator</Button>\n              <Button>Authy</Button>\n            </Space>\n            <div style={{ marginTop: 16 }}>\n              <Button type=\"primary\" onClick={() => setTwoFactorStep(1)}>\n                下一步\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {twoFactorStep === 1 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ marginBottom: 16 }}>\n              请使用身份验证器应用扫描下方二维码：\n            </div>\n            <QRCode value=\"otpauth://totp/FlowerAuction:admin?secret=JBSWY3DPEHPK3PXP&issuer=FlowerAuction\" />\n            <div style={{ marginTop: 16 }}>\n              <Button onClick={() => setTwoFactorStep(0)} style={{ marginRight: 8 }}>\n                上一步\n              </Button>\n              <Button type=\"primary\" onClick={() => setTwoFactorStep(2)}>\n                下一步\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {twoFactorStep === 2 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ marginBottom: 16 }}>\n              请输入身份验证器应用中显示的6位数字验证码：\n            </div>\n            <Input\n              style={{ width: 200, textAlign: 'center', fontSize: '18px' }}\n              maxLength={6}\n              placeholder=\"000000\"\n            />\n            <div style={{ marginTop: 16 }}>\n              <Button onClick={() => setTwoFactorStep(1)} style={{ marginRight: 8 }}>\n                上一步\n              </Button>\n              <Button\n                type=\"primary\"\n                onClick={() => {\n                  setTwoFactorEnabled(true);\n                  setTwoFactorModalVisible(false);\n                  message.success('双因子认证设置成功');\n                }}\n              >\n                完成设置\n              </Button>\n            </div>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default PersonalSecurity;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,IAAI,CACJC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,OAAO,CACPC,UAAU,CACVC,KAAK,CACLC,KAAK,CACLC,KAAK,CACLC,GAAG,CACHC,KAAK,CACLC,KAAK,CACLC,MAAM,KACD,MAAM,CACb,OACEC,YAAY,CACZC,cAAc,CACdC,cAAc,CACdC,oBAAoB,KACf,mBAAmB,CAC1B,OAASC,YAAY,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAEC,KAAM,CAAC,CAAGjB,UAAU,CAC5B,KAAM,CAAEkB,IAAK,CAAC,CAAGZ,KAAK,CActB,KAAM,CAAAa,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAACC,YAAY,CAAC,CAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC,CACrC,KAAM,CAACC,SAAS,CAAC,CAAG1B,IAAI,CAACyB,OAAO,CAAC,CAAC,CAClC,KAAM,CAACE,SAAS,CAAC,CAAG3B,IAAI,CAACyB,OAAO,CAAC,CAAC,CAClC,KAAM,CAACG,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACzE,KAAM,CAACkC,aAAa,CAAEC,gBAAgB,CAAC,CAAGnC,QAAQ,CAAC,CAAC,CAAC,CACrD,KAAM,CAACoC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAE/D,KAAM,CAACsC,YAAY,CAAC,CAAGtC,QAAQ,CAAgB,CAC7C,CACEuC,EAAE,CAAE,GAAG,CACPC,UAAU,CAAE,aAAa,CACzBC,UAAU,CAAE,SAAS,CACrBC,OAAO,CAAE,cAAc,CACvBC,EAAE,CAAE,YAAY,CAChBC,EAAE,CAAE,eAAe,CACnBC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,qBAAqB,CACjCC,SAAS,CAAE,IACb,CAAC,CACD,CACER,EAAE,CAAE,GAAG,CACPC,UAAU,CAAE,WAAW,CACvBC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,aAAa,CACtBC,EAAE,CAAE,UAAU,CACdC,EAAE,CAAE,eAAe,CACnBC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,qBAAqB,CACjCC,SAAS,CAAE,KACb,CAAC,CACF,CAAC,CAEF,KAAM,CAAAC,oBAAoB,CAAG,KAAO,CAAAC,MAAW,EAAK,CAClDlB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA;AAEA1B,OAAO,CAAC6C,OAAO,CAAC,cAAc,CAAC,CAC/BxB,YAAY,CAACyB,WAAW,CAAC,CAAC,CAE1B;AACAC,UAAU,CAAC,IAAM,CACfC,MAAM,CAACR,QAAQ,CAACS,IAAI,CAAG,QAAQ,CACjC,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BlD,OAAO,CAACkD,KAAK,CAAC,QAAQ,CAAC,CACzB,CAAC,OAAS,CACRxB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA0B,iBAAiB,CAAG,KAAO,CAAAR,MAAW,EAAK,CAC/ClB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA;AAEA1B,OAAO,CAAC6C,OAAO,CAAC,SAAS,CAAC,CAC1BtB,SAAS,CAACuB,WAAW,CAAC,CAAC,CACzB,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChClD,OAAO,CAACkD,KAAK,CAAC,SAAS,CAAC,CAC1B,CAAC,OAAS,CACRxB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA2B,iBAAiB,CAAG,KAAO,CAAAT,MAAW,EAAK,CAC/ClB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA;AAEA1B,OAAO,CAAC6C,OAAO,CAAC,QAAQ,CAAC,CACzBrB,SAAS,CAACsB,WAAW,CAAC,CAAC,CACzB,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BlD,OAAO,CAACkD,KAAK,CAAC,QAAQ,CAAC,CACzB,CAAC,OAAS,CACRxB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4B,kBAAkB,CAAIC,QAAgB,EAAK,CAC/CjD,KAAK,CAACkD,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,2BAA2B,CACpCC,IAAI,CAAE,KAAAA,CAAA,GAAY,CAChB,GAAI,CACF;AACA;AAEA3D,OAAO,CAAC6C,OAAO,CAAC,OAAO,CAAC,CAC1B,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BlD,OAAO,CAACkD,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAU,qBAAqB,CAAGA,CAAA,GAAM,CAClChC,wBAAwB,CAAC,IAAI,CAAC,CAC9BE,gBAAgB,CAAC,CAAC,CAAC,CACrB,CAAC,CAED,KAAM,CAAA+B,aAAa,CAAG,CACpB,CACEJ,KAAK,CAAE,MAAM,CACbK,GAAG,CAAE,QAAQ,CACbC,MAAM,CAAGC,MAAmB,eAC1B/C,KAAA,QAAAgD,QAAA,eACEhD,KAAA,QAAKiD,KAAK,CAAE,CAAEC,UAAU,CAAE,GAAI,CAAE,CAAAF,QAAA,EAC7BD,MAAM,CAAC7B,UAAU,CACjB6B,MAAM,CAACtB,SAAS,eAAI3B,IAAA,CAACV,GAAG,EAAC+D,KAAK,CAAC,OAAO,CAACF,KAAK,CAAE,CAAEG,UAAU,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,0BAAI,CAAK,CAAC,EACzE,CAAC,cACNhD,KAAA,QAAKiD,KAAK,CAAE,CAAEE,KAAK,CAAE,MAAM,CAAEE,QAAQ,CAAE,MAAO,CAAE,CAAAL,QAAA,EAC7CD,MAAM,CAAC3B,OAAO,CAAC,QAAG,CAAC2B,MAAM,CAAC1B,EAAE,EAC1B,CAAC,EACH,CAET,CAAC,CACD,CACEmB,KAAK,CAAE,MAAM,CACbc,SAAS,CAAE,IAAI,CACfT,GAAG,CAAE,IACP,CAAC,CACD,CACEL,KAAK,CAAE,IAAI,CACXc,SAAS,CAAE,UAAU,CACrBT,GAAG,CAAE,UACP,CAAC,CACD,CACEL,KAAK,CAAE,MAAM,CACbc,SAAS,CAAE,YAAY,CACvBT,GAAG,CAAE,YACP,CAAC,CACD,CACEL,KAAK,CAAE,IAAI,CACXK,GAAG,CAAE,QAAQ,CACbC,MAAM,CAAGC,MAAmB,eAC1BjD,IAAA,CAACb,KAAK,EAAA+D,QAAA,CACH,CAACD,MAAM,CAACtB,SAAS,eAChB3B,IAAA,CAAChB,MAAM,EACLyE,IAAI,CAAC,MAAM,CACXC,IAAI,CAAC,OAAO,CACZC,MAAM,MACNC,IAAI,cAAE5D,IAAA,CAACJ,cAAc,GAAE,CAAE,CACzBiE,OAAO,CAAEA,CAAA,GAAMtB,kBAAkB,CAACU,MAAM,CAAC9B,EAAE,CAAE,CAAA+B,QAAA,CAC9C,cAED,CAAQ,CACT,CACI,CAEX,CAAC,CACF,CAED,mBACEhD,KAAA,QAAKiD,KAAK,CAAE,CAAEW,OAAO,CAAE,EAAG,CAAE,CAAAZ,QAAA,eAC1BhD,KAAA,CAACC,KAAK,EAAC4D,KAAK,CAAE,CAAE,CAAAb,QAAA,eACdlD,IAAA,CAACL,cAAc,GAAE,CAAC,4BACpB,EAAO,CAAC,cAGRO,KAAA,CAACrB,IAAI,EAAC6D,KAAK,CAAC,0BAAM,CAACS,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAAAd,QAAA,eAC7ClD,IAAA,CAACZ,KAAK,EACJH,OAAO,CAAC,sCAAQ,CAChBgF,WAAW,CAAC,gOAAuC,CACnDR,IAAI,CAAC,MAAM,CACXS,QAAQ,MACRf,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEF9D,KAAA,CAACpB,IAAI,EACHqF,IAAI,CAAE7D,YAAa,CACnB8D,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAEzC,oBAAqB,CAC/BuB,KAAK,CAAE,CAAEmB,QAAQ,CAAE,GAAI,CAAE,CAAApB,QAAA,eAEzBlD,IAAA,CAAClB,IAAI,CAACyF,IAAI,EACRC,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE1F,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAiE,QAAA,cAEhDlD,IAAA,CAACjB,KAAK,CAAC6F,QAAQ,EAACC,MAAM,cAAE7E,IAAA,CAACN,YAAY,GAAE,CAAE,CAAE,CAAC,CACnC,CAAC,cAEZM,IAAA,CAAClB,IAAI,CAACyF,IAAI,EACRC,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAC,oBAAK,CACXC,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAE1F,OAAO,CAAE,QAAS,CAAC,CACrC,CAAE6F,GAAG,CAAE,CAAC,CAAE7F,OAAO,CAAE,UAAW,CAAC,CAC/B,CAAAiE,QAAA,cAEFlD,IAAA,CAACjB,KAAK,CAAC6F,QAAQ,EAACC,MAAM,cAAE7E,IAAA,CAACN,YAAY,GAAE,CAAE,CAAE,CAAC,CACnC,CAAC,cAEZM,IAAA,CAAClB,IAAI,CAACyF,IAAI,EACRC,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAC,gCAAO,CACbM,YAAY,CAAE,CAAC,aAAa,CAAE,CAC9BL,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAE1F,OAAO,CAAE,QAAS,CAAC,CACrC+F,IAAA,MAAC,CAAEC,aAAc,CAAC,CAAAD,IAAA,OAAM,CACtBE,SAASA,CAACC,CAAC,CAAEC,KAAK,CAAE,CAClB,GAAI,CAACA,KAAK,EAAIH,aAAa,CAAC,aAAa,CAAC,GAAKG,KAAK,CAAE,CACpD,MAAO,CAAAC,OAAO,CAACC,OAAO,CAAC,CAAC,CAC1B,CACA,MAAO,CAAAD,OAAO,CAACE,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAChD,CACF,CAAC,EAAC,CACF,CAAAtC,QAAA,cAEFlD,IAAA,CAACjB,KAAK,CAAC6F,QAAQ,EAACC,MAAM,cAAE7E,IAAA,CAACN,YAAY,GAAE,CAAE,CAAE,CAAC,CACnC,CAAC,cAEZM,IAAA,CAAClB,IAAI,CAACyF,IAAI,EAAArB,QAAA,cACRlD,IAAA,CAAChB,MAAM,EAACyE,IAAI,CAAC,SAAS,CAACgC,QAAQ,CAAC,QAAQ,CAAC/E,OAAO,CAAEA,OAAQ,CAAAwC,QAAA,CAAC,0BAE3D,CAAQ,CAAC,CACA,CAAC,EACR,CAAC,EACH,CAAC,cAGPlD,IAAA,CAACnB,IAAI,EAAC6D,KAAK,CAAC,gCAAO,CAACS,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAAAd,QAAA,cAC9ChD,KAAA,QAAKiD,KAAK,CAAE,CAAEuC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAA1C,QAAA,eACrFhD,KAAA,QAAAgD,QAAA,eACEhD,KAAA,QAAKiD,KAAK,CAAE,CAAEC,UAAU,CAAE,GAAG,CAAEY,YAAY,CAAE,CAAE,CAAE,CAAAd,QAAA,EAAC,iCAC1C,CAAClC,gBAAgB,cAAGhB,IAAA,CAACV,GAAG,EAAC+D,KAAK,CAAC,OAAO,CAAAH,QAAA,CAAC,oBAAG,CAAK,CAAC,cAAGlD,IAAA,CAACV,GAAG,EAAC+D,KAAK,CAAC,KAAK,CAAAH,QAAA,CAAC,oBAAG,CAAK,CAAC,EAChF,CAAC,cACNlD,IAAA,QAAKmD,KAAK,CAAE,CAAEE,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,CAAC,0HAE/B,CAAK,CAAC,EACH,CAAC,cACNlD,IAAA,CAAChB,MAAM,EACLyE,IAAI,CAAEzC,gBAAgB,CAAG,SAAS,CAAG,SAAU,CAC/C4C,IAAI,cAAE5D,IAAA,CAACH,oBAAoB,GAAE,CAAE,CAC/BgE,OAAO,CAAEhB,qBAAsB,CAAAK,QAAA,CAE9BlC,gBAAgB,CAAG,IAAI,CAAG,IAAI,CACzB,CAAC,EACN,CAAC,CACF,CAAC,cAGPd,KAAA,CAACrB,IAAI,EAAC6D,KAAK,CAAC,sCAAQ,CAACS,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAAAd,QAAA,eAC/ClD,IAAA,CAACZ,KAAK,EACJH,OAAO,CAAC,0BAAM,CACdgF,WAAW,CAAC,sIAAwB,CACpCR,IAAI,CAAC,SAAS,CACdS,QAAQ,MACRf,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEFhE,IAAA,CAACX,KAAK,EACJwG,OAAO,CAAE/C,aAAc,CACvBgD,UAAU,CAAE5E,YAAa,CACzB6E,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBtC,IAAI,CAAC,OAAO,CACb,CAAC,EACE,CAAC,cAGP1D,IAAA,CAACnB,IAAI,EAAC6D,KAAK,CAAC,0BAAM,CAAAQ,QAAA,cAChBhD,KAAA,CAACf,KAAK,EAAC8G,SAAS,CAAC,UAAU,CAAC9C,KAAK,CAAE,CAAE+C,KAAK,CAAE,MAAO,CAAE,CAAAhD,QAAA,eACnDlD,IAAA,CAACZ,KAAK,EACJH,OAAO,CAAC,0BAAM,CACdgF,WAAW,CAAC,sIAAwB,CACpCR,IAAI,CAAC,OAAO,CACZS,QAAQ,MACT,CAAC,cAEFlE,IAAA,QAAKmD,KAAK,CAAE,CAAEW,OAAO,CAAE,QAAS,CAAE,CAAAZ,QAAA,cAChClD,IAAA,CAACF,YAAY,EAAC2D,IAAI,CAAC,SAAS,CAACN,KAAK,CAAE,CAAEgD,eAAe,CAAE,SAAS,CAAEC,WAAW,CAAE,SAAU,CAAE,CAAAlD,QAAA,CAAC,0BAE5F,CAAc,CAAC,CACZ,CAAC,EACD,CAAC,CACJ,CAAC,cAGPhD,KAAA,CAACX,KAAK,EACJmD,KAAK,CAAC,4CAAS,CACf2D,IAAI,CAAEzF,qBAAsB,CAC5B0F,QAAQ,CAAEA,CAAA,GAAMzF,wBAAwB,CAAC,KAAK,CAAE,CAChD0F,MAAM,CAAE,IAAK,CACbL,KAAK,CAAE,GAAI,CAAAhD,QAAA,eAEXhD,KAAA,CAACV,KAAK,EAACgH,OAAO,CAAE1F,aAAc,CAACqC,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAAAd,QAAA,eACzDlD,IAAA,CAACI,IAAI,EAACsC,KAAK,CAAC,0BAAM,CAAE,CAAC,cACrB1C,IAAA,CAACI,IAAI,EAACsC,KAAK,CAAC,gCAAO,CAAE,CAAC,cACtB1C,IAAA,CAACI,IAAI,EAACsC,KAAK,CAAC,0BAAM,CAAE,CAAC,EAChB,CAAC,CAEP5B,aAAa,GAAK,CAAC,eAClBZ,KAAA,QAAKiD,KAAK,CAAE,CAAEsD,SAAS,CAAE,QAAS,CAAE,CAAAvD,QAAA,eAClClD,IAAA,QAAKmD,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAAAd,QAAA,CAAC,0HAElC,CAAK,CAAC,cACNhD,KAAA,CAACf,KAAK,EAAA+D,QAAA,eACJlD,IAAA,CAAChB,MAAM,EAAAkE,QAAA,CAAC,sBAAoB,CAAQ,CAAC,cACrClD,IAAA,CAAChB,MAAM,EAAAkE,QAAA,CAAC,yBAAuB,CAAQ,CAAC,cACxClD,IAAA,CAAChB,MAAM,EAAAkE,QAAA,CAAC,OAAK,CAAQ,CAAC,EACjB,CAAC,cACRlD,IAAA,QAAKmD,KAAK,CAAE,CAAEuD,SAAS,CAAE,EAAG,CAAE,CAAAxD,QAAA,cAC5BlD,IAAA,CAAChB,MAAM,EAACyE,IAAI,CAAC,SAAS,CAACI,OAAO,CAAEA,CAAA,GAAM9C,gBAAgB,CAAC,CAAC,CAAE,CAAAmC,QAAA,CAAC,oBAE3D,CAAQ,CAAC,CACN,CAAC,EACH,CACN,CAEApC,aAAa,GAAK,CAAC,eAClBZ,KAAA,QAAKiD,KAAK,CAAE,CAAEsD,SAAS,CAAE,QAAS,CAAE,CAAAvD,QAAA,eAClClD,IAAA,QAAKmD,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAAAd,QAAA,CAAC,8GAElC,CAAK,CAAC,cACNlD,IAAA,CAACP,MAAM,EAAC2F,KAAK,CAAC,iFAAiF,CAAE,CAAC,cAClGlF,KAAA,QAAKiD,KAAK,CAAE,CAAEuD,SAAS,CAAE,EAAG,CAAE,CAAAxD,QAAA,eAC5BlD,IAAA,CAAChB,MAAM,EAAC6E,OAAO,CAAEA,CAAA,GAAM9C,gBAAgB,CAAC,CAAC,CAAE,CAACoC,KAAK,CAAE,CAAEwD,WAAW,CAAE,CAAE,CAAE,CAAAzD,QAAA,CAAC,oBAEvE,CAAQ,CAAC,cACTlD,IAAA,CAAChB,MAAM,EAACyE,IAAI,CAAC,SAAS,CAACI,OAAO,CAAEA,CAAA,GAAM9C,gBAAgB,CAAC,CAAC,CAAE,CAAAmC,QAAA,CAAC,oBAE3D,CAAQ,CAAC,EACN,CAAC,EACH,CACN,CAEApC,aAAa,GAAK,CAAC,eAClBZ,KAAA,QAAKiD,KAAK,CAAE,CAAEsD,SAAS,CAAE,QAAS,CAAE,CAAAvD,QAAA,eAClClD,IAAA,QAAKmD,KAAK,CAAE,CAAEa,YAAY,CAAE,EAAG,CAAE,CAAAd,QAAA,CAAC,iIAElC,CAAK,CAAC,cACNlD,IAAA,CAACjB,KAAK,EACJoE,KAAK,CAAE,CAAE+C,KAAK,CAAE,GAAG,CAAEO,SAAS,CAAE,QAAQ,CAAElD,QAAQ,CAAE,MAAO,CAAE,CAC7DqD,SAAS,CAAE,CAAE,CACbC,WAAW,CAAC,QAAQ,CACrB,CAAC,cACF3G,KAAA,QAAKiD,KAAK,CAAE,CAAEuD,SAAS,CAAE,EAAG,CAAE,CAAAxD,QAAA,eAC5BlD,IAAA,CAAChB,MAAM,EAAC6E,OAAO,CAAEA,CAAA,GAAM9C,gBAAgB,CAAC,CAAC,CAAE,CAACoC,KAAK,CAAE,CAAEwD,WAAW,CAAE,CAAE,CAAE,CAAAzD,QAAA,CAAC,oBAEvE,CAAQ,CAAC,cACTlD,IAAA,CAAChB,MAAM,EACLyE,IAAI,CAAC,SAAS,CACdI,OAAO,CAAEA,CAAA,GAAM,CACb5C,mBAAmB,CAAC,IAAI,CAAC,CACzBJ,wBAAwB,CAAC,KAAK,CAAC,CAC/B5B,OAAO,CAAC6C,OAAO,CAAC,WAAW,CAAC,CAC9B,CAAE,CAAAoB,QAAA,CACH,0BAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,EACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7C,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}