{"ast": null, "code": "// 导出所有组件\nexport { default as Header } from './Header';\nexport { default as SideMenu } from './SideMenu';\nexport { default as UserInfo } from './UserInfo';\nexport { default as UserMenu } from './UserMenu';\nexport { default as LogoutButton } from './LogoutButton';\nexport { default as Breadcrumb } from './Breadcrumb';\nexport { default as FormMessage } from './FormMessage';\nexport { default as AuthProvider } from './AuthProvider';", "map": {"version": 3, "names": ["default", "Header", "SideMenu", "UserInfo", "UserMenu", "LogoutButton", "Breadcrumb", "FormMessage", "<PERSON>th<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/src/components/index.ts"], "sourcesContent": ["// 导出所有组件\nexport { default as Header } from './Header';\nexport { default as SideMenu } from './SideMenu';\nexport { default as UserInfo } from './UserInfo';\nexport { default as UserMenu } from './UserMenu';\nexport { default as LogoutButton } from './LogoutButton';\nexport { default as Breadcrumb } from './Breadcrumb';\nexport { default as FormMessage } from './FormMessage';\nexport { default as AuthProvider } from './AuthProvider';\n"], "mappings": "AAAA;AACA,SAASA,OAAO,IAAIC,MAAM,QAAQ,UAAU;AAC5C,SAASD,OAAO,IAAIE,QAAQ,QAAQ,YAAY;AAChD,SAASF,OAAO,IAAIG,QAAQ,QAAQ,YAAY;AAChD,SAASH,OAAO,IAAII,QAAQ,QAAQ,YAAY;AAChD,SAASJ,OAAO,IAAIK,YAAY,QAAQ,gBAAgB;AACxD,SAASL,OAAO,IAAIM,UAAU,QAAQ,cAAc;AACpD,SAASN,OAAO,IAAIO,WAAW,QAAQ,eAAe;AACtD,SAASP,OAAO,IAAIQ,YAAY,QAAQ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}