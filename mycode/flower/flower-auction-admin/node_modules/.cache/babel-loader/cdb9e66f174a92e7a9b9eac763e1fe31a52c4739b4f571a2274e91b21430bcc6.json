{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { isPresetSize } from '../_util/gapSize';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport createFlexClassNames from './utils';\nconst Flex = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      rootClassName,\n      className,\n      style,\n      flex,\n      gap,\n      children,\n      vertical = false,\n      component: Component = 'div'\n    } = props,\n    othersProps = __rest(props, [\"prefixCls\", \"rootClassName\", \"className\", \"style\", \"flex\", \"gap\", \"children\", \"vertical\", \"component\"]);\n  const {\n    flex: ctxFlex,\n    direction: ctxDirection,\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('flex', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedVertical = vertical !== null && vertical !== void 0 ? vertical : ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.vertical;\n  const mergedCls = classNames(className, rootClassName, ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.className, prefixCls, hashId, cssVarCls, createFlexClassNames(prefixCls, props), {\n    [`${prefixCls}-rtl`]: ctxDirection === 'rtl',\n    [`${prefixCls}-gap-${gap}`]: isPresetSize(gap),\n    [`${prefixCls}-vertical`]: mergedVertical\n  });\n  const mergedStyle = Object.assign(Object.assign({}, ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.style), style);\n  if (flex) {\n    mergedStyle.flex = flex;\n  }\n  if (gap && !isPresetSize(gap)) {\n    mergedStyle.gap = gap;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Component, Object.assign({\n    ref: ref,\n    className: mergedCls,\n    style: mergedStyle\n  }, omit(othersProps, ['justify', 'wrap', 'align'])), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Flex.displayName = 'Flex';\n}\nexport default Flex;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "omit", "isPresetSize", "ConfigContext", "useStyle", "createFlexClassNames", "Flex", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "rootClassName", "className", "style", "flex", "gap", "children", "vertical", "component", "Component", "othersProps", "ctxFlex", "direction", "ctxDirection", "getPrefixCls", "useContext", "wrapCSSVar", "hashId", "cssVarCls", "mergedVertical", "mergedCls", "mergedStyle", "assign", "createElement", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/flex/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { isPresetSize } from '../_util/gapSize';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport createFlexClassNames from './utils';\nconst Flex = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      rootClassName,\n      className,\n      style,\n      flex,\n      gap,\n      children,\n      vertical = false,\n      component: Component = 'div'\n    } = props,\n    othersProps = __rest(props, [\"prefixCls\", \"rootClassName\", \"className\", \"style\", \"flex\", \"gap\", \"children\", \"vertical\", \"component\"]);\n  const {\n    flex: ctxFlex,\n    direction: ctxDirection,\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('flex', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedVertical = vertical !== null && vertical !== void 0 ? vertical : ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.vertical;\n  const mergedCls = classNames(className, rootClassName, ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.className, prefixCls, hashId, cssVarCls, createFlexClassNames(prefixCls, props), {\n    [`${prefixCls}-rtl`]: ctxDirection === 'rtl',\n    [`${prefixCls}-gap-${gap}`]: isPresetSize(gap),\n    [`${prefixCls}-vertical`]: mergedVertical\n  });\n  const mergedStyle = Object.assign(Object.assign({}, ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.style), style);\n  if (flex) {\n    mergedStyle.flex = flex;\n  }\n  if (gap && !isPresetSize(gap)) {\n    mergedStyle.gap = gap;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Component, Object.assign({\n    ref: ref,\n    className: mergedCls,\n    style: mergedStyle\n  }, omit(othersProps, ['justify', 'wrap', 'align'])), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Flex.displayName = 'Flex';\n}\nexport default Flex;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAOC,oBAAoB,MAAM,SAAS;AAC1C,MAAMC,IAAI,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACzD,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,aAAa;MACbC,SAAS;MACTC,KAAK;MACLC,IAAI;MACJC,GAAG;MACHC,QAAQ;MACRC,QAAQ,GAAG,KAAK;MAChBC,SAAS,EAAEC,SAAS,GAAG;IACzB,CAAC,GAAGZ,KAAK;IACTa,WAAW,GAAGpC,MAAM,CAACuB,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;EACvI,MAAM;IACJO,IAAI,EAAEO,OAAO;IACbC,SAAS,EAAEC,YAAY;IACvBC;EACF,CAAC,GAAG1B,KAAK,CAAC2B,UAAU,CAACvB,aAAa,CAAC;EACnC,MAAMO,SAAS,GAAGe,YAAY,CAAC,MAAM,EAAEd,kBAAkB,CAAC;EAC1D,MAAM,CAACgB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAACM,SAAS,CAAC;EAC3D,MAAMoB,cAAc,GAAGZ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACJ,QAAQ;EAC/I,MAAMa,SAAS,GAAG/B,UAAU,CAACa,SAAS,EAAED,aAAa,EAAEU,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACT,SAAS,EAAEH,SAAS,EAAEkB,MAAM,EAAEC,SAAS,EAAExB,oBAAoB,CAACK,SAAS,EAAEF,KAAK,CAAC,EAAE;IAChM,CAAC,GAAGE,SAAS,MAAM,GAAGc,YAAY,KAAK,KAAK;IAC5C,CAAC,GAAGd,SAAS,QAAQM,GAAG,EAAE,GAAGd,YAAY,CAACc,GAAG,CAAC;IAC9C,CAAC,GAAGN,SAAS,WAAW,GAAGoB;EAC7B,CAAC,CAAC;EACF,MAAME,WAAW,GAAG1C,MAAM,CAAC2C,MAAM,CAAC3C,MAAM,CAAC2C,MAAM,CAAC,CAAC,CAAC,EAAEX,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACR,KAAK,CAAC,EAAEA,KAAK,CAAC;EAC5H,IAAIC,IAAI,EAAE;IACRiB,WAAW,CAACjB,IAAI,GAAGA,IAAI;EACzB;EACA,IAAIC,GAAG,IAAI,CAACd,YAAY,CAACc,GAAG,CAAC,EAAE;IAC7BgB,WAAW,CAAChB,GAAG,GAAGA,GAAG;EACvB;EACA,OAAOW,UAAU,CAAC,aAAa5B,KAAK,CAACmC,aAAa,CAACd,SAAS,EAAE9B,MAAM,CAAC2C,MAAM,CAAC;IAC1ExB,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAEkB,SAAS;IACpBjB,KAAK,EAAEkB;EACT,CAAC,EAAE/B,IAAI,CAACoB,WAAW,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAEJ,QAAQ,CAAC,CAAC;AACjE,CAAC,CAAC;AACF,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC/B,IAAI,CAACgC,WAAW,GAAG,MAAM;AAC3B;AACA,eAAehC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}