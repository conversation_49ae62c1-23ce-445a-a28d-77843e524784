{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Checkbox from './Checkbox';\nimport GroupContext from './GroupContext';\nimport useStyle from './style';\nconst CheckboxGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      defaultValue,\n      children,\n      options = [],\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      style,\n      onChange\n    } = props,\n    restProps = __rest(props, [\"defaultValue\", \"children\", \"options\", \"prefixCls\", \"className\", \"rootClassName\", \"style\", \"onChange\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const [value, setValue] = React.useState(restProps.value || defaultValue || []);\n  const [registeredValues, setRegisteredValues] = React.useState([]);\n  React.useEffect(() => {\n    if ('value' in restProps) {\n      setValue(restProps.value || []);\n    }\n  }, [restProps.value]);\n  const memoizedOptions = React.useMemo(() => options.map(option => {\n    if (typeof option === 'string' || typeof option === 'number') {\n      return {\n        label: option,\n        value: option\n      };\n    }\n    return option;\n  }), [options]);\n  const cancelValue = val => {\n    setRegisteredValues(prevValues => prevValues.filter(v => v !== val));\n  };\n  const registerValue = val => {\n    setRegisteredValues(prevValues => [].concat(_toConsumableArray(prevValues), [val]));\n  };\n  const toggleOption = option => {\n    const optionIndex = value.indexOf(option.value);\n    const newValue = _toConsumableArray(value);\n    if (optionIndex === -1) {\n      newValue.push(option.value);\n    } else {\n      newValue.splice(optionIndex, 1);\n    }\n    if (!('value' in restProps)) {\n      setValue(newValue);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(newValue.filter(val => registeredValues.includes(val)).sort((a, b) => {\n      const indexA = memoizedOptions.findIndex(opt => opt.value === a);\n      const indexB = memoizedOptions.findIndex(opt => opt.value === b);\n      return indexA - indexB;\n    }));\n  };\n  const prefixCls = getPrefixCls('checkbox', customizePrefixCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const domProps = omit(restProps, ['value', 'disabled']);\n  const childrenNode = options.length ? memoizedOptions.map(option => (/*#__PURE__*/React.createElement(Checkbox, {\n    prefixCls: prefixCls,\n    key: option.value.toString(),\n    disabled: 'disabled' in option ? option.disabled : restProps.disabled,\n    value: option.value,\n    checked: value.includes(option.value),\n    onChange: option.onChange,\n    className: classNames(`${groupPrefixCls}-item`, option.className),\n    style: option.style,\n    title: option.title,\n    id: option.id,\n    required: option.required\n  }, option.label))) : children;\n  const memoizedContext = React.useMemo(() => ({\n    toggleOption,\n    value,\n    disabled: restProps.disabled,\n    name: restProps.name,\n    // https://github.com/ant-design/ant-design/issues/16376\n    registerValue,\n    cancelValue\n  }), [toggleOption, value, restProps.disabled, restProps.name, registerValue, cancelValue]);\n  const classString = classNames(groupPrefixCls, {\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, cssVarCls, rootCls, hashId);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    className: classString,\n    style: style\n  }, domProps, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(GroupContext.Provider, {\n    value: memoizedContext\n  }, childrenNode)));\n});\nexport { GroupContext };\nexport default CheckboxGroup;", "map": {"version": 3, "names": ["_toConsumableArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "omit", "ConfigContext", "useCSSVarCls", "Checkbox", "GroupContext", "useStyle", "CheckboxGroup", "forwardRef", "props", "ref", "defaultValue", "children", "options", "prefixCls", "customizePrefixCls", "className", "rootClassName", "style", "onChange", "restProps", "getPrefixCls", "direction", "useContext", "value", "setValue", "useState", "registeredValues", "setRegisteredValues", "useEffect", "memoizedOptions", "useMemo", "map", "option", "label", "cancelValue", "val", "prevV<PERSON><PERSON>", "filter", "v", "registerValue", "concat", "toggleOption", "optionIndex", "newValue", "push", "splice", "includes", "sort", "a", "b", "indexA", "findIndex", "opt", "indexB", "groupPrefixCls", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "domProps", "childrenNode", "createElement", "key", "toString", "disabled", "checked", "title", "id", "required", "memoizedContext", "name", "classString", "assign", "Provider"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/checkbox/Group.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Checkbox from './Checkbox';\nimport GroupContext from './GroupContext';\nimport useStyle from './style';\nconst CheckboxGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      defaultValue,\n      children,\n      options = [],\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      style,\n      onChange\n    } = props,\n    restProps = __rest(props, [\"defaultValue\", \"children\", \"options\", \"prefixCls\", \"className\", \"rootClassName\", \"style\", \"onChange\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const [value, setValue] = React.useState(restProps.value || defaultValue || []);\n  const [registeredValues, setRegisteredValues] = React.useState([]);\n  React.useEffect(() => {\n    if ('value' in restProps) {\n      setValue(restProps.value || []);\n    }\n  }, [restProps.value]);\n  const memoizedOptions = React.useMemo(() => options.map(option => {\n    if (typeof option === 'string' || typeof option === 'number') {\n      return {\n        label: option,\n        value: option\n      };\n    }\n    return option;\n  }), [options]);\n  const cancelValue = val => {\n    setRegisteredValues(prevValues => prevValues.filter(v => v !== val));\n  };\n  const registerValue = val => {\n    setRegisteredValues(prevValues => [].concat(_toConsumableArray(prevValues), [val]));\n  };\n  const toggleOption = option => {\n    const optionIndex = value.indexOf(option.value);\n    const newValue = _toConsumableArray(value);\n    if (optionIndex === -1) {\n      newValue.push(option.value);\n    } else {\n      newValue.splice(optionIndex, 1);\n    }\n    if (!('value' in restProps)) {\n      setValue(newValue);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(newValue.filter(val => registeredValues.includes(val)).sort((a, b) => {\n      const indexA = memoizedOptions.findIndex(opt => opt.value === a);\n      const indexB = memoizedOptions.findIndex(opt => opt.value === b);\n      return indexA - indexB;\n    }));\n  };\n  const prefixCls = getPrefixCls('checkbox', customizePrefixCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const domProps = omit(restProps, ['value', 'disabled']);\n  const childrenNode = options.length ? memoizedOptions.map(option => (/*#__PURE__*/React.createElement(Checkbox, {\n    prefixCls: prefixCls,\n    key: option.value.toString(),\n    disabled: 'disabled' in option ? option.disabled : restProps.disabled,\n    value: option.value,\n    checked: value.includes(option.value),\n    onChange: option.onChange,\n    className: classNames(`${groupPrefixCls}-item`, option.className),\n    style: option.style,\n    title: option.title,\n    id: option.id,\n    required: option.required\n  }, option.label))) : children;\n  const memoizedContext = React.useMemo(() => ({\n    toggleOption,\n    value,\n    disabled: restProps.disabled,\n    name: restProps.name,\n    // https://github.com/ant-design/ant-design/issues/16376\n    registerValue,\n    cancelValue\n  }), [toggleOption, value, restProps.disabled, restProps.name, registerValue, cancelValue]);\n  const classString = classNames(groupPrefixCls, {\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, cssVarCls, rootCls, hashId);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    className: classString,\n    style: style\n  }, domProps, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(GroupContext.Provider, {\n    value: memoizedContext\n  }, childrenNode)));\n});\nexport { GroupContext };\nexport default CheckboxGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,aAAa,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAClE,MAAM;MACFC,YAAY;MACZC,QAAQ;MACRC,OAAO,GAAG,EAAE;MACZC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS;MACTC,aAAa;MACbC,KAAK;MACLC;IACF,CAAC,GAAGV,KAAK;IACTW,SAAS,GAAGnC,MAAM,CAACwB,KAAK,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;EACpI,MAAM;IACJY,YAAY;IACZC;EACF,CAAC,GAAGvB,KAAK,CAACwB,UAAU,CAACrB,aAAa,CAAC;EACnC,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAACN,SAAS,CAACI,KAAK,IAAIb,YAAY,IAAI,EAAE,CAAC;EAC/E,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,KAAK,CAAC2B,QAAQ,CAAC,EAAE,CAAC;EAClE3B,KAAK,CAAC8B,SAAS,CAAC,MAAM;IACpB,IAAI,OAAO,IAAIT,SAAS,EAAE;MACxBK,QAAQ,CAACL,SAAS,CAACI,KAAK,IAAI,EAAE,CAAC;IACjC;EACF,CAAC,EAAE,CAACJ,SAAS,CAACI,KAAK,CAAC,CAAC;EACrB,MAAMM,eAAe,GAAG/B,KAAK,CAACgC,OAAO,CAAC,MAAMlB,OAAO,CAACmB,GAAG,CAACC,MAAM,IAAI;IAChE,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC5D,OAAO;QACLC,KAAK,EAAED,MAAM;QACbT,KAAK,EAAES;MACT,CAAC;IACH;IACA,OAAOA,MAAM;EACf,CAAC,CAAC,EAAE,CAACpB,OAAO,CAAC,CAAC;EACd,MAAMsB,WAAW,GAAGC,GAAG,IAAI;IACzBR,mBAAmB,CAACS,UAAU,IAAIA,UAAU,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,GAAG,CAAC,CAAC;EACtE,CAAC;EACD,MAAMI,aAAa,GAAGJ,GAAG,IAAI;IAC3BR,mBAAmB,CAACS,UAAU,IAAI,EAAE,CAACI,MAAM,CAACzD,kBAAkB,CAACqD,UAAU,CAAC,EAAE,CAACD,GAAG,CAAC,CAAC,CAAC;EACrF,CAAC;EACD,MAAMM,YAAY,GAAGT,MAAM,IAAI;IAC7B,MAAMU,WAAW,GAAGnB,KAAK,CAAC9B,OAAO,CAACuC,MAAM,CAACT,KAAK,CAAC;IAC/C,MAAMoB,QAAQ,GAAG5D,kBAAkB,CAACwC,KAAK,CAAC;IAC1C,IAAImB,WAAW,KAAK,CAAC,CAAC,EAAE;MACtBC,QAAQ,CAACC,IAAI,CAACZ,MAAM,CAACT,KAAK,CAAC;IAC7B,CAAC,MAAM;MACLoB,QAAQ,CAACE,MAAM,CAACH,WAAW,EAAE,CAAC,CAAC;IACjC;IACA,IAAI,EAAE,OAAO,IAAIvB,SAAS,CAAC,EAAE;MAC3BK,QAAQ,CAACmB,QAAQ,CAAC;IACpB;IACAzB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyB,QAAQ,CAACN,MAAM,CAACF,GAAG,IAAIT,gBAAgB,CAACoB,QAAQ,CAACX,GAAG,CAAC,CAAC,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACjI,MAAMC,MAAM,GAAGrB,eAAe,CAACsB,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAKyB,CAAC,CAAC;MAChE,MAAMK,MAAM,GAAGxB,eAAe,CAACsB,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAK0B,CAAC,CAAC;MAChE,OAAOC,MAAM,GAAGG,MAAM;IACxB,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMxC,SAAS,GAAGO,YAAY,CAAC,UAAU,EAAEN,kBAAkB,CAAC;EAC9D,MAAMwC,cAAc,GAAG,GAAGzC,SAAS,QAAQ;EAC3C,MAAM0C,OAAO,GAAGrD,YAAY,CAACW,SAAS,CAAC;EACvC,MAAM,CAAC2C,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAACQ,SAAS,EAAE0C,OAAO,CAAC;EACpE,MAAMI,QAAQ,GAAG3D,IAAI,CAACmB,SAAS,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;EACvD,MAAMyC,YAAY,GAAGhD,OAAO,CAAChB,MAAM,GAAGiC,eAAe,CAACE,GAAG,CAACC,MAAM,KAAK,aAAalC,KAAK,CAAC+D,aAAa,CAAC1D,QAAQ,EAAE;IAC9GU,SAAS,EAAEA,SAAS;IACpBiD,GAAG,EAAE9B,MAAM,CAACT,KAAK,CAACwC,QAAQ,CAAC,CAAC;IAC5BC,QAAQ,EAAE,UAAU,IAAIhC,MAAM,GAAGA,MAAM,CAACgC,QAAQ,GAAG7C,SAAS,CAAC6C,QAAQ;IACrEzC,KAAK,EAAES,MAAM,CAACT,KAAK;IACnB0C,OAAO,EAAE1C,KAAK,CAACuB,QAAQ,CAACd,MAAM,CAACT,KAAK,CAAC;IACrCL,QAAQ,EAAEc,MAAM,CAACd,QAAQ;IACzBH,SAAS,EAAEhB,UAAU,CAAC,GAAGuD,cAAc,OAAO,EAAEtB,MAAM,CAACjB,SAAS,CAAC;IACjEE,KAAK,EAAEe,MAAM,CAACf,KAAK;IACnBiD,KAAK,EAAElC,MAAM,CAACkC,KAAK;IACnBC,EAAE,EAAEnC,MAAM,CAACmC,EAAE;IACbC,QAAQ,EAAEpC,MAAM,CAACoC;EACnB,CAAC,EAAEpC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGtB,QAAQ;EAC7B,MAAM0D,eAAe,GAAGvE,KAAK,CAACgC,OAAO,CAAC,OAAO;IAC3CW,YAAY;IACZlB,KAAK;IACLyC,QAAQ,EAAE7C,SAAS,CAAC6C,QAAQ;IAC5BM,IAAI,EAAEnD,SAAS,CAACmD,IAAI;IACpB;IACA/B,aAAa;IACbL;EACF,CAAC,CAAC,EAAE,CAACO,YAAY,EAAElB,KAAK,EAAEJ,SAAS,CAAC6C,QAAQ,EAAE7C,SAAS,CAACmD,IAAI,EAAE/B,aAAa,EAAEL,WAAW,CAAC,CAAC;EAC1F,MAAMqC,WAAW,GAAGxE,UAAU,CAACuD,cAAc,EAAE;IAC7C,CAAC,GAAGA,cAAc,MAAM,GAAGjC,SAAS,KAAK;EAC3C,CAAC,EAAEN,SAAS,EAAEC,aAAa,EAAE0C,SAAS,EAAEH,OAAO,EAAEE,MAAM,CAAC;EACxD,OAAOD,UAAU,CAAC,aAAa1D,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAExE,MAAM,CAACmF,MAAM,CAAC;IACtEzD,SAAS,EAAEwD,WAAW;IACtBtD,KAAK,EAAEA;EACT,CAAC,EAAE0C,QAAQ,EAAE;IACXlD,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAaX,KAAK,CAAC+D,aAAa,CAACzD,YAAY,CAACqE,QAAQ,EAAE;IAC1DlD,KAAK,EAAE8C;EACT,CAAC,EAAET,YAAY,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC;AACF,SAASxD,YAAY;AACrB,eAAeE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}