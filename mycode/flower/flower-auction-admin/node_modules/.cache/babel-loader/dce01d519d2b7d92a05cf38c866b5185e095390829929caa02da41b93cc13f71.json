{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext } from 'react';\nimport { QRCodeCanvas, QRCodeSVG } from '@rc-component/qrcode';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport { useLocale } from '../locale';\nimport { useToken } from '../theme/internal';\nimport QRcodeStatus from './QrcodeStatus';\nimport useStyle from './style/index';\nconst QRCode = props => {\n  var _a, _b, _c, _d;\n  const [, token] = useToken();\n  const {\n      value,\n      type = 'canvas',\n      icon = '',\n      size = 160,\n      iconSize,\n      color = token.colorText,\n      errorLevel = 'M',\n      status = 'active',\n      bordered = true,\n      onRefresh,\n      style,\n      className,\n      rootClassName,\n      prefixCls: customizePrefixCls,\n      bgColor = 'transparent',\n      statusRender\n    } = props,\n    rest = __rest(props, [\"value\", \"type\", \"icon\", \"size\", \"iconSize\", \"color\", \"errorLevel\", \"status\", \"bordered\", \"onRefresh\", \"style\", \"className\", \"rootClassName\", \"prefixCls\", \"bgColor\", \"statusRender\"]);\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('qrcode', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const imageSettings = {\n    src: icon,\n    x: undefined,\n    y: undefined,\n    height: typeof iconSize === 'number' ? iconSize : (_a = iconSize === null || iconSize === void 0 ? void 0 : iconSize.height) !== null && _a !== void 0 ? _a : 40,\n    width: typeof iconSize === 'number' ? iconSize : (_b = iconSize === null || iconSize === void 0 ? void 0 : iconSize.width) !== null && _b !== void 0 ? _b : 40,\n    excavate: true,\n    crossOrigin: 'anonymous'\n  };\n  const a11yProps = pickAttrs(rest, true);\n  const restProps = omit(rest, Object.keys(a11yProps));\n  const qrCodeProps = Object.assign({\n    value,\n    size,\n    level: errorLevel,\n    bgColor,\n    fgColor: color,\n    style: {\n      width: style === null || style === void 0 ? void 0 : style.width,\n      height: style === null || style === void 0 ? void 0 : style.height\n    },\n    imageSettings: icon ? imageSettings : undefined\n  }, a11yProps);\n  const [locale] = useLocale('QRCode');\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('QRCode');\n    process.env.NODE_ENV !== \"production\" ? warning(!!value, 'usage', 'need to receive `value` props') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(icon && errorLevel === 'L'), 'usage', 'ErrorLevel `L` is not recommended to be used with `icon`, for scanning result would be affected by low level.') : void 0;\n  }\n  if (!value) {\n    return null;\n  }\n  const mergedCls = classNames(prefixCls, className, rootClassName, hashId, cssVarCls, {\n    [`${prefixCls}-borderless`]: !bordered\n  });\n  const mergedStyle = Object.assign(Object.assign({\n    backgroundColor: bgColor\n  }, style), {\n    width: (_c = style === null || style === void 0 ? void 0 : style.width) !== null && _c !== void 0 ? _c : size,\n    height: (_d = style === null || style === void 0 ? void 0 : style.height) !== null && _d !== void 0 ? _d : size\n  });\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({}, restProps, {\n    className: mergedCls,\n    style: mergedStyle\n  }), status !== 'active' && (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-mask`\n  }, /*#__PURE__*/React.createElement(QRcodeStatus, {\n    prefixCls: prefixCls,\n    locale: locale,\n    status: status,\n    onRefresh: onRefresh,\n    statusRender: statusRender\n  }))), type === 'canvas' ? /*#__PURE__*/React.createElement(QRCodeCanvas, Object.assign({}, qrCodeProps)) : /*#__PURE__*/React.createElement(QRCodeSVG, Object.assign({}, qrCodeProps))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  QRCode.displayName = 'QRCode';\n}\nexport default QRCode;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useContext", "QRCodeCanvas", "QRCodeSVG", "classNames", "omit", "pickAttrs", "devUseW<PERSON>ning", "ConfigContext", "useLocale", "useToken", "QRcodeStatus", "useStyle", "QRCode", "props", "_a", "_b", "_c", "_d", "token", "value", "type", "icon", "size", "iconSize", "color", "colorText", "errorLevel", "status", "bordered", "onRefresh", "style", "className", "rootClassName", "prefixCls", "customizePrefixCls", "bgColor", "statusRender", "rest", "getPrefixCls", "wrapCSSVar", "hashId", "cssVarCls", "imageSettings", "src", "x", "undefined", "y", "height", "width", "excavate", "crossOrigin", "a11yProps", "restProps", "keys", "qrCodeProps", "assign", "level", "fgColor", "locale", "process", "env", "NODE_ENV", "warning", "mergedCls", "mergedStyle", "backgroundColor", "createElement", "displayName"], "sources": ["/Users/<USER>/Documents/mycode/flower/flower-auction-admin/node_modules/antd/es/qr-code/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext } from 'react';\nimport { QRCodeCanvas, QRCodeSVG } from '@rc-component/qrcode';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport { useLocale } from '../locale';\nimport { useToken } from '../theme/internal';\nimport QRcodeStatus from './QrcodeStatus';\nimport useStyle from './style/index';\nconst QRCode = props => {\n  var _a, _b, _c, _d;\n  const [, token] = useToken();\n  const {\n      value,\n      type = 'canvas',\n      icon = '',\n      size = 160,\n      iconSize,\n      color = token.colorText,\n      errorLevel = 'M',\n      status = 'active',\n      bordered = true,\n      onRefresh,\n      style,\n      className,\n      rootClassName,\n      prefixCls: customizePrefixCls,\n      bgColor = 'transparent',\n      statusRender\n    } = props,\n    rest = __rest(props, [\"value\", \"type\", \"icon\", \"size\", \"iconSize\", \"color\", \"errorLevel\", \"status\", \"bordered\", \"onRefresh\", \"style\", \"className\", \"rootClassName\", \"prefixCls\", \"bgColor\", \"statusRender\"]);\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('qrcode', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const imageSettings = {\n    src: icon,\n    x: undefined,\n    y: undefined,\n    height: typeof iconSize === 'number' ? iconSize : (_a = iconSize === null || iconSize === void 0 ? void 0 : iconSize.height) !== null && _a !== void 0 ? _a : 40,\n    width: typeof iconSize === 'number' ? iconSize : (_b = iconSize === null || iconSize === void 0 ? void 0 : iconSize.width) !== null && _b !== void 0 ? _b : 40,\n    excavate: true,\n    crossOrigin: 'anonymous'\n  };\n  const a11yProps = pickAttrs(rest, true);\n  const restProps = omit(rest, Object.keys(a11yProps));\n  const qrCodeProps = Object.assign({\n    value,\n    size,\n    level: errorLevel,\n    bgColor,\n    fgColor: color,\n    style: {\n      width: style === null || style === void 0 ? void 0 : style.width,\n      height: style === null || style === void 0 ? void 0 : style.height\n    },\n    imageSettings: icon ? imageSettings : undefined\n  }, a11yProps);\n  const [locale] = useLocale('QRCode');\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('QRCode');\n    process.env.NODE_ENV !== \"production\" ? warning(!!value, 'usage', 'need to receive `value` props') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(icon && errorLevel === 'L'), 'usage', 'ErrorLevel `L` is not recommended to be used with `icon`, for scanning result would be affected by low level.') : void 0;\n  }\n  if (!value) {\n    return null;\n  }\n  const mergedCls = classNames(prefixCls, className, rootClassName, hashId, cssVarCls, {\n    [`${prefixCls}-borderless`]: !bordered\n  });\n  const mergedStyle = Object.assign(Object.assign({\n    backgroundColor: bgColor\n  }, style), {\n    width: (_c = style === null || style === void 0 ? void 0 : style.width) !== null && _c !== void 0 ? _c : size,\n    height: (_d = style === null || style === void 0 ? void 0 : style.height) !== null && _d !== void 0 ? _d : size\n  });\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({}, restProps, {\n    className: mergedCls,\n    style: mergedStyle\n  }), status !== 'active' && (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-mask`\n  }, /*#__PURE__*/React.createElement(QRcodeStatus, {\n    prefixCls: prefixCls,\n    locale: locale,\n    status: status,\n    onRefresh: onRefresh,\n    statusRender: statusRender\n  }))), type === 'canvas' ? /*#__PURE__*/React.createElement(QRCodeCanvas, Object.assign({}, qrCodeProps)) : /*#__PURE__*/React.createElement(QRCodeSVG, Object.assign({}, qrCodeProps))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  QRCode.displayName = 'QRCode';\n}\nexport default QRCode;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,YAAY,EAAEC,SAAS,QAAQ,sBAAsB;AAC9D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,eAAe;AACpC,MAAMC,MAAM,GAAGC,KAAK,IAAI;EACtB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,MAAM,GAAGC,KAAK,CAAC,GAAGT,QAAQ,CAAC,CAAC;EAC5B,MAAM;MACFU,KAAK;MACLC,IAAI,GAAG,QAAQ;MACfC,IAAI,GAAG,EAAE;MACTC,IAAI,GAAG,GAAG;MACVC,QAAQ;MACRC,KAAK,GAAGN,KAAK,CAACO,SAAS;MACvBC,UAAU,GAAG,GAAG;MAChBC,MAAM,GAAG,QAAQ;MACjBC,QAAQ,GAAG,IAAI;MACfC,SAAS;MACTC,KAAK;MACLC,SAAS;MACTC,aAAa;MACbC,SAAS,EAAEC,kBAAkB;MAC7BC,OAAO,GAAG,aAAa;MACvBC;IACF,CAAC,GAAGvB,KAAK;IACTwB,IAAI,GAAGpD,MAAM,CAAC4B,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;EAC9M,MAAM;IACJyB;EACF,CAAC,GAAGtC,UAAU,CAACO,aAAa,CAAC;EAC7B,MAAM0B,SAAS,GAAGK,YAAY,CAAC,QAAQ,EAAEJ,kBAAkB,CAAC;EAC5D,MAAM,CAACK,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAACsB,SAAS,CAAC;EAC3D,MAAMS,aAAa,GAAG;IACpBC,GAAG,EAAEtB,IAAI;IACTuB,CAAC,EAAEC,SAAS;IACZC,CAAC,EAAED,SAAS;IACZE,MAAM,EAAE,OAAOxB,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAG,CAACT,EAAE,GAAGS,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACwB,MAAM,MAAM,IAAI,IAAIjC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;IAChKkC,KAAK,EAAE,OAAOzB,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAG,CAACR,EAAE,GAAGQ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyB,KAAK,MAAM,IAAI,IAAIjC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;IAC9JkC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC;EACD,MAAMC,SAAS,GAAG9C,SAAS,CAACgC,IAAI,EAAE,IAAI,CAAC;EACvC,MAAMe,SAAS,GAAGhD,IAAI,CAACiC,IAAI,EAAE/C,MAAM,CAAC+D,IAAI,CAACF,SAAS,CAAC,CAAC;EACpD,MAAMG,WAAW,GAAGhE,MAAM,CAACiE,MAAM,CAAC;IAChCpC,KAAK;IACLG,IAAI;IACJkC,KAAK,EAAE9B,UAAU;IACjBS,OAAO;IACPsB,OAAO,EAAEjC,KAAK;IACdM,KAAK,EAAE;MACLkB,KAAK,EAAElB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACkB,KAAK;MAChED,MAAM,EAAEjB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACiB;IAC9D,CAAC;IACDL,aAAa,EAAErB,IAAI,GAAGqB,aAAa,GAAGG;EACxC,CAAC,EAAEM,SAAS,CAAC;EACb,MAAM,CAACO,MAAM,CAAC,GAAGlD,SAAS,CAAC,QAAQ,CAAC;EACpC,IAAImD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGxD,aAAa,CAAC,QAAQ,CAAC;IACvCqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,CAAC,CAAC3C,KAAK,EAAE,OAAO,EAAE,+BAA+B,CAAC,GAAG,KAAK,CAAC;IAC3GwC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,EAAEzC,IAAI,IAAIK,UAAU,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,+GAA+G,CAAC,GAAG,KAAK,CAAC;EACnN;EACA,IAAI,CAACP,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,MAAM4C,SAAS,GAAG5D,UAAU,CAAC8B,SAAS,EAAEF,SAAS,EAAEC,aAAa,EAAEQ,MAAM,EAAEC,SAAS,EAAE;IACnF,CAAC,GAAGR,SAAS,aAAa,GAAG,CAACL;EAChC,CAAC,CAAC;EACF,MAAMoC,WAAW,GAAG1E,MAAM,CAACiE,MAAM,CAACjE,MAAM,CAACiE,MAAM,CAAC;IAC9CU,eAAe,EAAE9B;EACnB,CAAC,EAAEL,KAAK,CAAC,EAAE;IACTkB,KAAK,EAAE,CAAChC,EAAE,GAAGc,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACkB,KAAK,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGM,IAAI;IAC7GyB,MAAM,EAAE,CAAC9B,EAAE,GAAGa,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACiB,MAAM,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGK;EAC7G,CAAC,CAAC;EACF,OAAOiB,UAAU,CAAC,aAAaxC,KAAK,CAACmE,aAAa,CAAC,KAAK,EAAE5E,MAAM,CAACiE,MAAM,CAAC,CAAC,CAAC,EAAEH,SAAS,EAAE;IACrFrB,SAAS,EAAEgC,SAAS;IACpBjC,KAAK,EAAEkC;EACT,CAAC,CAAC,EAAErC,MAAM,KAAK,QAAQ,KAAK,aAAa5B,KAAK,CAACmE,aAAa,CAAC,KAAK,EAAE;IAClEnC,SAAS,EAAE,GAAGE,SAAS;EACzB,CAAC,EAAE,aAAalC,KAAK,CAACmE,aAAa,CAACxD,YAAY,EAAE;IAChDuB,SAAS,EAAEA,SAAS;IACpByB,MAAM,EAAEA,MAAM;IACd/B,MAAM,EAAEA,MAAM;IACdE,SAAS,EAAEA,SAAS;IACpBO,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC,CAAC,EAAEhB,IAAI,KAAK,QAAQ,GAAG,aAAarB,KAAK,CAACmE,aAAa,CAACjE,YAAY,EAAEX,MAAM,CAACiE,MAAM,CAAC,CAAC,CAAC,EAAED,WAAW,CAAC,CAAC,GAAG,aAAavD,KAAK,CAACmE,aAAa,CAAChE,SAAS,EAAEZ,MAAM,CAACiE,MAAM,CAAC,CAAC,CAAC,EAAED,WAAW,CAAC,CAAC,CAAC,CAAC;AAC1L,CAAC;AACD,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjD,MAAM,CAACuD,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAevD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}