import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Statistic, Progress, Typography, Tag, Space, Button, Descriptions } from 'antd';
import {
  ClockCircleOutlined,
  DollarOutlined,
  UserOutlined,
  TrophyOutlined,
  FireOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { ClockStatus, BatchStatus } from '../../types';
import './index.css';

const { Title, Text } = Typography;
const { Countdown } = Statistic;

interface ClockPanelProps {
  clockNumber: number;
}

const ClockPanel: React.FC<ClockPanelProps> = ({ clockNumber }) => {
  const { clocks } = useSelector((state: RootState) => state.auction);
  const [currentTime, setCurrentTime] = useState(Date.now());
  
  const clock = clocks.find(c => c.clockNumber === clockNumber);
  const currentBatch = clock?.currentBatch;

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getStatusColor = (status: ClockStatus) => {
    switch (status) {
      case ClockStatus.IDLE:
        return 'default';
      case ClockStatus.PREPARING:
        return 'processing';
      case ClockStatus.BIDDING:
        return 'success';
      case ClockStatus.PAUSED:
        return 'warning';
      case ClockStatus.COMPLETED:
        return 'default';
      default:
        return 'default';
    }
  };

  const getBatchStatusColor = (status: BatchStatus) => {
    switch (status) {
      case BatchStatus.PENDING:
        return 'default';
      case BatchStatus.BIDDING:
        return 'processing';
      case BatchStatus.SOLD:
        return 'success';
      case BatchStatus.UNSOLD:
        return 'warning';
      case BatchStatus.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getBatchStatusText = (status: BatchStatus) => {
    switch (status) {
      case BatchStatus.PENDING:
        return '待起拍';
      case BatchStatus.BIDDING:
        return '竞拍中';
      case BatchStatus.SOLD:
        return '已成交';
      case BatchStatus.UNSOLD:
        return '流拍';
      case BatchStatus.CANCELLED:
        return '已取消';
      default:
        return '未知';
    }
  };

  const calculateProgress = () => {
    if (!clock?.timeRemaining) return 0;
    const maxTime = 300; // 假设最大拍卖时间为5分钟
    return Math.max(0, Math.min(100, ((maxTime - clock.timeRemaining) / maxTime) * 100));
  };

  if (!clock) {
    return (
      <Card className="clock-panel">
        <div className="no-clock">
          <ClockCircleOutlined className="no-clock-icon" />
          <Title level={4}>钟号 {clockNumber} 未找到</Title>
          <Text type="secondary">请检查钟号配置</Text>
        </div>
      </Card>
    );
  }

  return (
    <div className="clock-panel">
      <Card className="panel-header" bordered={false}>
        <Row align="middle" justify="space-between">
          <Col>
            <Space align="center">
              <ClockCircleOutlined className="clock-icon" />
              <div>
                <Title level={3} className="clock-title">
                  钟号 {clockNumber}
                </Title>
                <Tag color={getStatusColor(clock.status)} className="status-tag">
                  {clock.status === ClockStatus.IDLE && '空闲'}
                  {clock.status === ClockStatus.PREPARING && '准备中'}
                  {clock.status === ClockStatus.BIDDING && '竞拍中'}
                  {clock.status === ClockStatus.PAUSED && '暂停'}
                  {clock.status === ClockStatus.COMPLETED && '已完成'}
                </Tag>
              </div>
            </Space>
          </Col>
          <Col>
            <Space>
              <Statistic
                title="在线买家"
                value={clock.onlineBuyers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff', fontSize: '18px' }}
              />
              <Statistic
                title="出价次数"
                value={clock.totalBids}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#52c41a', fontSize: '18px' }}
              />
            </Space>
          </Col>
        </Row>
      </Card>

      {currentBatch ? (
        <Card className="batch-info" title="当前批次信息">
          <Row gutter={16}>
            <Col span={12}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="批次号">
                  <Text strong>{currentBatch.batchNumber}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="商品名称">
                  {currentBatch.productName}
                </Descriptions.Item>
                <Descriptions.Item label="分类">
                  {currentBatch.categoryName}
                </Descriptions.Item>
                <Descriptions.Item label="供货商">
                  {currentBatch.supplierName}
                </Descriptions.Item>
                <Descriptions.Item label="质量等级">
                  <Tag color="blue">等级 {currentBatch.qualityLevel}</Tag>
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="数量">
                  {currentBatch.quantity} {currentBatch.unit}
                </Descriptions.Item>
                <Descriptions.Item label="起拍价">
                  <Text type="secondary">¥{currentBatch.startPrice}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="当前价格">
                  <Text strong style={{ fontSize: '18px', color: '#f5222d' }}>
                    ¥{clock.currentPrice || currentBatch.startPrice}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={getBatchStatusColor(currentBatch.status)}>
                    {getBatchStatusText(currentBatch.status)}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="仓位">
                  {currentBatch.warehouseLocation}
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>

          {clock.status === ClockStatus.BIDDING && clock.timeRemaining && (
            <div className="auction-progress">
              <Row gutter={16} align="middle">
                <Col span={8}>
                  <Countdown
                    title="剩余时间"
                    value={currentTime + clock.timeRemaining * 1000}
                    format="mm:ss"
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Col>
                <Col span={16}>
                  <div className="progress-container">
                    <Text type="secondary">拍卖进度</Text>
                    <Progress
                      percent={calculateProgress()}
                      strokeColor={{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                      }}
                      showInfo={false}
                    />
                  </div>
                </Col>
              </Row>
            </div>
          )}
        </Card>
      ) : (
        <Card className="no-batch">
          <div className="no-batch-content">
            <EyeOutlined className="no-batch-icon" />
            <Title level={4}>暂无拍卖批次</Title>
            <Text type="secondary">
              当前钟号没有进行中的拍卖批次
            </Text>
          </div>
        </Card>
      )}

      <Card className="real-time-stats" title="实时数据">
        <Row gutter={16}>
          <Col span={8}>
            <Card className="stat-card">
              <Statistic
                title="当前价格"
                value={clock.currentPrice || 0}
                prefix={<DollarOutlined />}
                suffix="元"
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card className="stat-card">
              <Statistic
                title="关注人数"
                value={Math.floor(Math.random() * 100) + 50}
                prefix={<EyeOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card className="stat-card">
              <Statistic
                title="热度指数"
                value={Math.floor(Math.random() * 100) + 60}
                prefix={<FireOutlined />}
                suffix="/100"
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default ClockPanel;
