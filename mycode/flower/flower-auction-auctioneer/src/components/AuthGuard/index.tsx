import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin, Result, Button } from 'antd';
import { RootState, AppDispatch } from '../../store';
import { getCurrentUserAsync, setUser, clearUser } from '../../store/slices/authSlice';
import authService from '../../services/authService';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuctioneer?: boolean;
  requiredClockNumbers?: number[];
}

const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  requireAuctioneer = true,
  requiredClockNumbers = []
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const { user, isAuthenticated, loading } = useSelector((state: RootState) => state.auth);
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // 检查本地存储的认证状态
        if (authService.isAuthenticated()) {
          const storedUser = authService.getStoredUser();
          if (storedUser) {
            dispatch(setUser(storedUser));
            
            // 尝试获取最新的用户信息
            try {
              await dispatch(getCurrentUserAsync()).unwrap();
            } catch (error) {
              console.warn('Failed to refresh user info:', error);
              // 如果获取用户信息失败，清除认证状态
              dispatch(clearUser());
            }
          } else {
            dispatch(clearUser());
          }
        } else {
          dispatch(clearUser());
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        dispatch(clearUser());
      } finally {
        setInitializing(false);
      }
    };

    initializeAuth();
  }, [dispatch]);

  // 如果正在初始化或加载中，显示加载状态
  if (initializing || loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        background: '#f0f2f5'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果未认证，重定向到登录页
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 检查是否需要拍卖师权限
  if (requireAuctioneer && !authService.isAuctioneer(user)) {
    return (
      <Result
        status="403"
        title="权限不足"
        subTitle="您需要拍卖师权限才能访问此页面。"
        extra={
          <Button
            type="primary"
            onClick={() => {
              // 清除认证信息
              localStorage.removeItem('token');
              localStorage.removeItem('refreshToken');
              localStorage.removeItem('user');
              // 跳转到登录页
              window.location.href = '/login';
            }}
          >
            重新登录
          </Button>
        }
      />
    );
  }

  // 检查钟号权限
  if (requiredClockNumbers.length > 0) {
    const hasPermission = requiredClockNumbers.some(clockNumber => 
      authService.hasClockPermission(clockNumber, user)
    );
    
    if (!hasPermission) {
      return (
        <Result
          status="403"
          title="钟号权限不足"
          subTitle={`您需要钟号 ${requiredClockNumbers.join(', ')} 的权限才能访问此页面。`}
          extra={
            <Button type="primary" onClick={() => window.location.href = '/dashboard'}>
              返回首页
            </Button>
          }
        />
      );
    }
  }

  // 权限检查通过，渲染子组件
  return <>{children}</>;
};

export default AuthGuard;
