import React, { useState } from 'react';
import { Layout as AntLayout, Menu, Dropdown, Avatar, Space, Typography, Button, Modal, message } from 'antd';
import {
  DashboardOutlined,
  ShoppingCartOutlined,
  ControlOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  LockOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { logoutAsync } from '../../store/slices/authSlice';
import './index.css';

const { Header, Sider, Content } = AntLayout;
const { Text } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const [collapsed, setCollapsed] = useState(false);
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '工作台',
    },
    {
      key: '/batches',
      icon: <ShoppingCartOutlined />,
      label: '批次管理',
    },
    {
      key: '/auction-control',
      icon: <ControlOutlined />,
      label: '拍卖控制',
    },
    {
      key: '/statistics',
      icon: <BarChartOutlined />,
      label: '统计分析',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleLogout = async () => {
    try {
      await dispatch(logoutAsync()).unwrap();
      message.success('登出成功');
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      message.error('登出失败');
    } finally {
      setLogoutModalVisible(false);
    }
  };

  const handleChangePassword = () => {
    message.info('修改密码功能开发中...');
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => message.info('个人信息功能开发中...'),
    },
    {
      key: 'change-password',
      icon: <LockOutlined />,
      label: '修改密码',
      onClick: handleChangePassword,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => setLogoutModalVisible(true),
    },
  ];

  return (
    <AntLayout className="layout-container">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="layout-sider"
        width={240}
      >
        <div className="logo">
          <div className="logo-icon">🌸</div>
          {!collapsed && (
            <div className="logo-text">
              <div className="logo-title">花卉拍卖</div>
              <div className="logo-subtitle">拍卖师端</div>
            </div>
          )}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="layout-menu"
        />
      </Sider>

      <AntLayout className="layout-main">
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="collapse-button"
            />
          </div>

          <div className="header-right">
            <Space>
              <Text className="welcome-text">
                欢迎，{user?.realName || user?.username}
              </Text>
              
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                arrow
              >
                <div className="user-avatar">
                  <Avatar 
                    icon={<UserOutlined />} 
                    className="avatar"
                  />
                  <span className="username">
                    {user?.realName || user?.username}
                  </span>
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>

        <Content className="layout-content">
          {children}
        </Content>
      </AntLayout>

      {/* 登出确认弹窗 */}
      <Modal
        title="确认登出"
        open={logoutModalVisible}
        onOk={handleLogout}
        onCancel={() => setLogoutModalVisible(false)}
        okText="确认"
        cancelText="取消"
        okType="danger"
      >
        <p>您确定要退出登录吗？</p>
      </Modal>
    </AntLayout>
  );
};

export default Layout;
