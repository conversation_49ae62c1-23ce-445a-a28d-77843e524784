import React, { useState } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Modal,
  Form,
  InputNumber,
  Select,
  message,
  Popconfirm
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  DollarOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import {
  startAuctionAsync,
  controlAuctionAsync,
  adjustPriceAsync
} from '../../store/slices/auctionSlice';
import { auctioneerWebSocket } from '../../services/websocketService';
import { ClockStatus } from '../../types';

const { Title } = Typography;
const { Option } = Select;

interface AuctionControlProps {
  clockNumber: number;
}

const AuctionControl: React.FC<AuctionControlProps> = ({ clockNumber }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { clocks, loading } = useSelector((state: RootState) => state.auction);
  const [startModalVisible, setStartModalVisible] = useState(false);
  const [adjustPriceModalVisible, setAdjustPriceModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [adjustForm] = Form.useForm();

  // 获取当前钟号信息
  const currentClock = clocks.find(clock => clock.clockNumber === clockNumber);
  const isActive = currentClock?.status === ClockStatus.BIDDING;
  const isPaused = currentClock?.status === ClockStatus.PAUSED;
  const isIdle = currentClock?.status === ClockStatus.IDLE;

  // 开始拍卖
  const handleStartAuction = async (values: any) => {
    try {
      await dispatch(startAuctionAsync({
        batchId: values.batchId,
        clockNumber,
        startPrice: values.startPrice,
        stepPrice: values.stepPrice,
      })).unwrap();

      message.success('拍卖已开始');
      setStartModalVisible(false);
      form.resetFields();
    } catch (error: any) {
      message.error(error || '开始拍卖失败');
    }
  };

  // 暂停拍卖
  const handlePauseAuction = async () => {
    if (!currentClock?.currentBatch) {
      message.error('没有正在进行的拍卖');
      return;
    }

    try {
      await dispatch(controlAuctionAsync({
        action: 'pause',
        batchId: currentClock.currentBatch.id,
        clockNumber,
      })).unwrap();

      message.success('拍卖已暂停');
    } catch (error: any) {
      message.error(error || '暂停拍卖失败');
    }
  };

  // 恢复拍卖
  const handleResumeAuction = async () => {
    if (!currentClock?.currentBatch) {
      message.error('没有暂停的拍卖');
      return;
    }

    try {
      await dispatch(controlAuctionAsync({
        action: 'resume',
        batchId: currentClock.currentBatch.id,
        clockNumber,
      })).unwrap();

      message.success('拍卖已恢复');
    } catch (error: any) {
      message.error(error || '恢复拍卖失败');
    }
  };

  // 结束拍卖
  const handleStopAuction = async () => {
    if (!currentClock?.currentBatch) {
      message.error('没有正在进行的拍卖');
      return;
    }

    try {
      await dispatch(controlAuctionAsync({
        action: 'stop',
        batchId: currentClock.currentBatch.id,
        clockNumber,
      })).unwrap();

      message.success('拍卖已结束');
    } catch (error: any) {
      message.error(error || '结束拍卖失败');
    }
  };

  // 标记流拍
  const handleMarkUnsold = async () => {
    if (!currentClock?.currentBatch) {
      message.error('没有正在进行的拍卖');
      return;
    }

    try {
      await dispatch(controlAuctionAsync({
        action: 'unsold',
        batchId: currentClock.currentBatch.id,
        clockNumber,
        reason: '流拍',
      })).unwrap();

      message.success('已标记为流拍');
    } catch (error: any) {
      message.error(error || '标记流拍失败');
    }
  };

  // 调整价格
  const handleAdjustPrice = async (values: any) => {
    if (!currentClock?.currentBatch) {
      message.error('没有正在进行的拍卖');
      return;
    }

    try {
      await dispatch(adjustPriceAsync({
        batchId: currentClock.currentBatch.id,
        clockNumber,
        newPrice: values.newPrice,
      })).unwrap();

      message.success('价格已调整');
      setAdjustPriceModalVisible(false);
      adjustForm.resetFields();
    } catch (error: any) {
      message.error(error || '调整价格失败');
    }
  };

  return (
    <>
      <Card title={`钟号 ${clockNumber} 控制`} size="small">
        <Space direction="vertical" style={{ width: '100%' }} size="small">
          {/* 当前状态显示 */}
          <div style={{ textAlign: 'center', marginBottom: 8 }}>
            <Typography.Text type="secondary">
              状态: {currentClock?.status || '未知'}
            </Typography.Text>
          </div>

          {/* 开始拍卖按钮 */}
          {isIdle && (
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              block
              loading={loading}
              onClick={() => setStartModalVisible(true)}
            >
              开始拍卖
            </Button>
          )}

          {/* 暂停/恢复按钮 */}
          {isActive && (
            <Button
              icon={<PauseCircleOutlined />}
              block
              loading={loading}
              onClick={handlePauseAuction}
            >
              暂停拍卖
            </Button>
          )}

          {isPaused && (
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              block
              loading={loading}
              onClick={handleResumeAuction}
            >
              恢复拍卖
            </Button>
          )}

          {/* 结束拍卖按钮 */}
          {(isActive || isPaused) && (
            <Popconfirm
              title="确定要结束拍卖吗？"
              onConfirm={handleStopAuction}
              okText="确定"
              cancelText="取消"
            >
              <Button
                danger
                icon={<StopOutlined />}
                block
                loading={loading}
              >
                结束拍卖
              </Button>
            </Popconfirm>
          )}

          {/* 流拍按钮 */}
          {(isActive || isPaused) && (
            <Popconfirm
              title="确定要标记为流拍吗？"
              onConfirm={handleMarkUnsold}
              okText="确定"
              cancelText="取消"
            >
              <Button
                danger
                icon={<ExclamationCircleOutlined />}
                block
                loading={loading}
              >
                标记流拍
              </Button>
            </Popconfirm>
          )}

          {/* 调价按钮 */}
          {(isActive || isPaused) && (
            <Button
              icon={<DollarOutlined />}
              block
              loading={loading}
              onClick={() => setAdjustPriceModalVisible(true)}
            >
              调整价格
            </Button>
          )}
        </Space>
      </Card>

      {/* 开始拍卖模态框 */}
      <Modal
        title="开始拍卖"
        open={startModalVisible}
        onCancel={() => {
          setStartModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleStartAuction}
        >
          <Form.Item
            name="batchId"
            label="批次ID"
            rules={[{ required: true, message: '请输入批次ID' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入批次ID"
              min={1}
            />
          </Form.Item>

          <Form.Item
            name="startPrice"
            label="起拍价"
            rules={[{ required: true, message: '请输入起拍价' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入起拍价"
              min={0}
              step={0.01}
              addonAfter="元"
            />
          </Form.Item>

          <Form.Item
            name="stepPrice"
            label="加价幅度"
            rules={[{ required: true, message: '请输入加价幅度' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入加价幅度"
              min={0.01}
              step={0.01}
              addonAfter="元"
            />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => {
                setStartModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始拍卖
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 调整价格模态框 */}
      <Modal
        title="调整价格"
        open={adjustPriceModalVisible}
        onCancel={() => {
          setAdjustPriceModalVisible(false);
          adjustForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={adjustForm}
          layout="vertical"
          onFinish={handleAdjustPrice}
        >
          <Form.Item
            name="newPrice"
            label="新价格"
            rules={[{ required: true, message: '请输入新价格' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入新价格"
              min={0}
              step={0.01}
              addonAfter="元"
            />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => {
                setAdjustPriceModalVisible(false);
                adjustForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                确认调整
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default AuctionControl;
