import React from 'react';
import { <PERSON>, Button, Space, Typography } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface AuctionControlProps {
  clockNumber: number;
}

const AuctionControl: React.FC<AuctionControlProps> = ({ clockNumber }) => {
  return (
    <Card title={`钟号 ${clockNumber} 控制`} size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Button type="primary" icon={<PlayCircleOutlined />} block>
          开始拍卖
        </Button>
        <Button icon={<PauseCircleOutlined />} block>
          暂停拍卖
        </Button>
        <Button danger icon={<StopOutlined />} block>
          结束拍卖
        </Button>
      </Space>
    </Card>
  );
};

export default AuctionControl;
