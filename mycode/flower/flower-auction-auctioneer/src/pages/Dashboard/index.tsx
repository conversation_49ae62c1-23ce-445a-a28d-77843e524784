import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic, Button, Badge, Typography, Space, Spin } from 'antd';
import {
  StopOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { getAuctionStatisticsAsync, setSelectedClock } from '../../store/slices/auctionSlice';
import { ClockStatus } from '../../types';
import ClockPanel from '../../components/ClockPanel';
import AuctionControl from '../../components/AuctionControl';
import RealTimeData from '../../components/RealTimeData';
import './index.css';
const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { clocks, statistics, selectedClock, loading, wsConnected } = useSelector((state: RootState) => state.auction);
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    // 获取拍卖统计数据
    dispatch(getAuctionStatisticsAsync());
    
    // 初始化钟号数据（模拟数据，实际应该从WebSocket获取）
    const mockClocks = user?.clockNumbers?.map(clockNumber => ({
      clockNumber,
      status: ClockStatus.IDLE,
      onlineBuyers: Math.floor(Math.random() * 50) + 10,
      totalBids: Math.floor(Math.random() * 100),
      currentPrice: undefined,
      timeRemaining: undefined,
    })) || [];
    
    // TODO: 这里应该通过WebSocket连接获取实时数据
    // dispatch(initializeClocks(mockClocks));
  }, [dispatch, user]);

  const handleClockSelect = (clockNumber: number) => {
    dispatch(setSelectedClock(clockNumber));
  };

  const getStatusColor = (status: ClockStatus) => {
    switch (status) {
      case ClockStatus.IDLE:
        return 'default';
      case ClockStatus.PREPARING:
        return 'processing';
      case ClockStatus.BIDDING:
        return 'success';
      case ClockStatus.PAUSED:
        return 'warning';
      case ClockStatus.COMPLETED:
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: ClockStatus) => {
    switch (status) {
      case ClockStatus.IDLE:
        return '空闲';
      case ClockStatus.PREPARING:
        return '准备中';
      case ClockStatus.BIDDING:
        return '竞拍中';
      case ClockStatus.PAUSED:
        return '暂停';
      case ClockStatus.COMPLETED:
        return '已完成';
      default:
        return '未知';
    }
  };

  return (
    <div className="dashboard-layout">
      {/* 页面头部 */}
      <div className="dashboard-header">
        <div className="header-left">
          <Title level={3} className="header-title">
            拍卖师工作台
          </Title>
          <Badge
            status={wsConnected ? 'success' : 'error'}
            text={wsConnected ? '已连接' : '连接断开'}
            className="connection-status"
          />
        </div>

        <div className="header-right">
          <Space>
            <Text>欢迎，{user?.realName || user?.username}</Text>
            <Button type="primary" danger icon={<StopOutlined />}>
              紧急停拍
            </Button>
          </Space>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="dashboard-body">
        <Row gutter={16} style={{ height: '100%' }}>
          {/* 左侧钟号控制面板 */}
          <Col xs={24} sm={24} md={8} lg={6} xl={6} className="dashboard-sider-col">
            <Card
              className="dashboard-sider-card"
              title="钟号控制"
              bodyStyle={{ padding: '16px', height: 'calc(100vh - 200px)', overflowY: 'auto' }}
            >
              <div className="clock-list">
                {user?.clockNumbers?.map(clockNumber => {
                  const clock = clocks.find(c => c.clockNumber === clockNumber);
                  const isSelected = selectedClock === clockNumber;

                  return (
                    <Card
                      key={clockNumber}
                      className={`clock-card ${isSelected ? 'selected' : ''}`}
                      size="small"
                      onClick={() => handleClockSelect(clockNumber)}
                      hoverable
                    >
                      <div className="clock-header">
                        <Title level={5}>钟号 {clockNumber}</Title>
                        <Badge
                          status={getStatusColor(clock?.status || ClockStatus.IDLE)}
                          text={getStatusText(clock?.status || ClockStatus.IDLE)}
                        />
                      </div>

                      <div className="clock-stats">
                        <Row gutter={8}>
                          <Col span={12}>
                            <Statistic
                              title="在线买家"
                              value={clock?.onlineBuyers || 0}
                              prefix={<UserOutlined />}
                              valueStyle={{ fontSize: '14px' }}
                            />
                          </Col>
                          <Col span={12}>
                            <Statistic
                              title="出价次数"
                              value={clock?.totalBids || 0}
                              prefix={<TrophyOutlined />}
                              valueStyle={{ fontSize: '14px' }}
                            />
                          </Col>
                        </Row>
                      </div>

                      {clock?.currentPrice && (
                        <div className="current-price">
                          <Text strong>当前价格: ¥{clock.currentPrice}</Text>
                        </div>
                      )}
                    </Card>
                  );
                })}
              </div>
            </Card>
          </Col>

          {/* 右侧主要内容区域 */}
          <Col xs={24} sm={24} md={16} lg={18} xl={18} className="dashboard-content-col">
            <div className="dashboard-content">
              {/* 统计卡片 */}
              <div className="content-header">
                <Row gutter={[16, 16]}>
                  <Col xs={12} sm={12} md={6} lg={6} xl={6}>
                    <Card>
                      <Statistic
                        title="今日拍卖批次"
                        value={statistics?.todayStats.batches || 0}
                        prefix={<ShoppingCartOutlined />}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Card>
                  </Col>
                  <Col xs={12} sm={12} md={6} lg={6} xl={6}>
                    <Card>
                      <Statistic
                        title="今日成交金额"
                        value={statistics?.todayStats.revenue || 0}
                        prefix={<DollarOutlined />}
                        precision={2}
                        valueStyle={{ color: '#52c41a' }}
                        suffix="元"
                      />
                    </Card>
                  </Col>
                  <Col xs={12} sm={12} md={6} lg={6} xl={6}>
                    <Card>
                      <Statistic
                        title="在线买家"
                        value={statistics?.todayStats.buyers || 0}
                        prefix={<UserOutlined />}
                        valueStyle={{ color: '#722ed1' }}
                      />
                    </Card>
                  </Col>
                  <Col xs={12} sm={12} md={6} lg={6} xl={6}>
                    <Card>
                      <Statistic
                        title="成功率"
                        value={statistics?.successRate || 0}
                        prefix={<TrophyOutlined />}
                        precision={1}
                        valueStyle={{ color: '#fa8c16' }}
                        suffix="%"
                      />
                    </Card>
                  </Col>
                </Row>
              </div>

              {/* 主要工作区域 */}
              <div className="content-main">
                <Row gutter={16}>
                  <Col xs={24} sm={24} md={24} lg={16} xl={16}>
                    {selectedClock ? (
                      <ClockPanel clockNumber={selectedClock} />
                    ) : (
                      <Card className="no-selection">
                        <div className="no-selection-content">
                          <ClockCircleOutlined className="no-selection-icon" />
                          <Title level={4}>请选择钟号</Title>
                          <Text type="secondary">
                            从左侧选择一个钟号开始拍卖控制
                          </Text>
                        </div>
                      </Card>
                    )}
                  </Col>

                  <Col xs={24} sm={24} md={24} lg={8} xl={8}>
                    <Space direction="vertical" style={{ width: '100%' }} size="middle">
                      {selectedClock && <AuctionControl clockNumber={selectedClock} />}
                      <RealTimeData />
                    </Space>
                  </Col>
                </Row>
              </div>
            </div>
          </Col>
        </Row>
      </div>

      {loading && (
        <div className="loading-overlay">
          <Spin size="large" />
        </div>
      )}
    </div>
  );
};

export default Dashboard;
