.dashboard-layout {
  min-height: 100vh;
  background: #f0f2f5;
}

.dashboard-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-title {
  margin: 0 !important;
  color: #262626;
}

.connection-status {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

.dashboard-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.sider-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.clock-list {
  margin-top: 16px;
}

.clock-card {
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.clock-card:hover {
  border-color: #1890ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.clock-card.selected {
  border-color: #1890ff;
  background: #f6ffed;
}

.clock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.clock-header h5 {
  margin: 0 !important;
}

.clock-stats {
  margin-bottom: 8px;
}

.current-price {
  text-align: center;
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #91d5ff;
}

.dashboard-content {
  padding: 16px;
  background: #f0f2f5;
}

.content-header {
  margin-bottom: 16px;
}

.content-header .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.content-main {
  min-height: calc(100vh - 200px);
}

.no-selection {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.no-selection-content {
  text-align: center;
}

.no-selection-icon {
  font-size: 4rem;
  color: #d9d9d9;
  margin-bottom: 16px;
  display: block;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-sider {
    width: 250px !important;
  }
  
  .sider-content {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 0 16px;
    flex-direction: column;
    height: auto;
    padding-top: 12px;
    padding-bottom: 12px;
  }
  
  .header-left,
  .header-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .header-title {
    font-size: 1.25rem !important;
  }
  
  .dashboard-content {
    padding: 12px;
  }
  
  .content-header .ant-col {
    margin-bottom: 12px;
  }
  
  .content-main .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .dashboard-sider {
    position: fixed;
    left: 0;
    top: 64px;
    height: calc(100vh - 64px);
    z-index: 200;
  }
  
  .dashboard-content {
    margin-left: 0;
  }
  
  .content-header .ant-col {
    span: 12;
  }
  
  .content-main .ant-row {
    flex-direction: column;
  }
  
  .content-main .ant-col {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.clock-card,
.content-header .ant-card,
.content-main .ant-card {
  animation: slideIn 0.3s ease-out;
}

/* 状态指示器样式 */
.ant-badge-status-dot {
  width: 8px;
  height: 8px;
}

.ant-badge-status-success {
  background-color: #52c41a;
  animation: pulse 2s infinite;
}

.ant-badge-status-processing {
  background-color: #1890ff;
  animation: pulse 2s infinite;
}

.ant-badge-status-warning {
  background-color: #faad14;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
