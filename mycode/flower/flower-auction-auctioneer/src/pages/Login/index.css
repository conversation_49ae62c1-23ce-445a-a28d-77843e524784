.login-container {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>');
  background-size: cover;
  animation: float 20s ease-in-out infinite;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.login-content {
  display: flex;
  gap: 2rem;
  max-width: 1200px;
  width: 100%;
  padding: 2rem;
  z-index: 1;
}

.login-card {
  flex: 1;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo {
  margin-bottom: 1rem;
}

.logo-icon {
  font-size: 3rem;
  color: #1890ff;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.login-title {
  margin-bottom: 0.5rem !important;
  color: #262626;
  font-weight: 600;
}

.login-subtitle {
  color: #8c8c8c;
  font-size: 16px;
}

.login-form {
  margin-top: 2rem;
}

.login-form .ant-form-item {
  margin-bottom: 1.5rem;
}

.login-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.fingerprint-button {
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  color: #8c8c8c;
  transition: all 0.3s ease;
}

.fingerprint-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f0f0f0;
}

.footer-text {
  display: block;
  margin-bottom: 1rem;
}

.footer-links {
  margin-top: 0.5rem;
}

.info-card {
  flex: 1;
  max-width: 300px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.feature-list li {
  padding: 0.5rem 0;
  color: #595959;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    padding: 1rem;
  }
  
  .login-card {
    max-width: 100%;
  }
  
  .info-card {
    max-width: 100%;
    order: -1;
  }
  
  .login-title {
    font-size: 1.5rem !important;
  }
}

@media (max-width: 480px) {
  .login-content {
    padding: 0.5rem;
  }
  
  .login-card .ant-card-body {
    padding: 1.5rem;
  }
  
  .login-title {
    font-size: 1.25rem !important;
  }
  
  .logo-icon {
    font-size: 2.5rem;
  }
}
