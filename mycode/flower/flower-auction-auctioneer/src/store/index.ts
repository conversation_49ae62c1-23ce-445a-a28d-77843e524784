import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import auctionSlice from './slices/auctionSlice';
import batchSlice from './slices/batchSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    auction: auctionSlice,
    batch: batchSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
