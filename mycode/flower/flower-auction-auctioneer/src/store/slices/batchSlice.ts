import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Batch, BatchQueryParams, PaginatedResponse, BatchCreateForm, BatchStatus } from '../../types';
import apiClient from '../../services/api';

interface BatchState {
  batches: Batch[];
  currentBatch: Batch | null;
  total: number;
  page: number;
  size: number;
  loading: boolean;
  error: string | null;
  queryParams: BatchQueryParams;
}

const initialState: BatchState = {
  batches: [],
  currentBatch: null,
  total: 0,
  page: 1,
  size: 20,
  loading: false,
  error: null,
  queryParams: {
    page: 1,
    size: 20,
  },
};

// 异步actions
export const getBatchesAsync = createAsyncThunk(
  'batch/getBatches',
  async (params: BatchQueryParams, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/batches', { params });
      if (response.data.success) {
        return response.data.data as PaginatedResponse<Batch>;
      } else {
        return rejectWithValue(response.data.message || '获取批次列表失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取批次列表失败');
    }
  }
);

export const getBatchByIdAsync = createAsyncThunk(
  'batch/getBatchById',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(`/batches/${id}`);
      if (response.data.success) {
        return response.data.data as Batch;
      } else {
        return rejectWithValue(response.data.message || '获取批次详情失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取批次详情失败');
    }
  }
);

export const createBatchAsync = createAsyncThunk(
  'batch/createBatch',
  async (batchData: BatchCreateForm, { rejectWithValue }) => {
    try {
      const response = await apiClient.post('/batches', batchData);
      if (response.data.success) {
        return response.data.data as Batch;
      } else {
        return rejectWithValue(response.data.message || '创建批次失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建批次失败');
    }
  }
);

export const updateBatchAsync = createAsyncThunk(
  'batch/updateBatch',
  async ({ id, data }: { id: number; data: Partial<BatchCreateForm> }, { rejectWithValue }) => {
    try {
      const response = await apiClient.put(`/batches/${id}`, data);
      if (response.data.success) {
        return response.data.data as Batch;
      } else {
        return rejectWithValue(response.data.message || '更新批次失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新批次失败');
    }
  }
);

export const deleteBatchAsync = createAsyncThunk(
  'batch/deleteBatch',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.delete(`/batches/${id}`);
      if (response.data.success) {
        return id;
      } else {
        return rejectWithValue(response.data.message || '删除批次失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除批次失败');
    }
  }
);

export const updateBatchStatusAsync = createAsyncThunk(
  'batch/updateBatchStatus',
  async ({ id, status }: { id: number; status: BatchStatus }, { rejectWithValue }) => {
    try {
      const response = await apiClient.patch(`/batches/${id}/status`, { status });
      if (response.data.success) {
        return { id, status };
      } else {
        return rejectWithValue(response.data.message || '更新批次状态失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新批次状态失败');
    }
  }
);

export const importBatchesAsync = createAsyncThunk(
  'batch/importBatches',
  async (file: File, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await apiClient.post('/batches/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      if (response.data.success) {
        return response.data.data;
      } else {
        return rejectWithValue(response.data.message || '导入批次失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '导入批次失败');
    }
  }
);

const batchSlice = createSlice({
  name: 'batch',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setQueryParams: (state, action: PayloadAction<Partial<BatchQueryParams>>) => {
      state.queryParams = { ...state.queryParams, ...action.payload };
    },
    setCurrentBatch: (state, action: PayloadAction<Batch | null>) => {
      state.currentBatch = action.payload;
    },
    updateBatchInList: (state, action: PayloadAction<Batch>) => {
      const index = state.batches.findIndex(batch => batch.id === action.payload.id);
      if (index !== -1) {
        state.batches[index] = action.payload;
      }
    },
    removeBatchFromList: (state, action: PayloadAction<number>) => {
      state.batches = state.batches.filter(batch => batch.id !== action.payload);
      state.total = Math.max(0, state.total - 1);
    },
    addBatchToList: (state, action: PayloadAction<Batch>) => {
      state.batches.unshift(action.payload);
      state.total += 1;
    },
  },
  extraReducers: (builder) => {
    // 获取批次列表
    builder
      .addCase(getBatchesAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBatchesAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.batches = action.payload.items;
        state.total = action.payload.total;
        state.page = action.payload.page;
        state.size = action.payload.size;
        state.error = null;
      })
      .addCase(getBatchesAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 获取批次详情
    builder
      .addCase(getBatchByIdAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBatchByIdAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.currentBatch = action.payload;
        state.error = null;
      })
      .addCase(getBatchByIdAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 创建批次
    builder
      .addCase(createBatchAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createBatchAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.batches.unshift(action.payload);
        state.total += 1;
        state.error = null;
      })
      .addCase(createBatchAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 更新批次
    builder
      .addCase(updateBatchAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateBatchAsync.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.batches.findIndex(batch => batch.id === action.payload.id);
        if (index !== -1) {
          state.batches[index] = action.payload;
        }
        if (state.currentBatch?.id === action.payload.id) {
          state.currentBatch = action.payload;
        }
        state.error = null;
      })
      .addCase(updateBatchAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 删除批次
    builder
      .addCase(deleteBatchAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteBatchAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.batches = state.batches.filter(batch => batch.id !== action.payload);
        state.total = Math.max(0, state.total - 1);
        if (state.currentBatch?.id === action.payload) {
          state.currentBatch = null;
        }
        state.error = null;
      })
      .addCase(deleteBatchAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 更新批次状态
    builder
      .addCase(updateBatchStatusAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateBatchStatusAsync.fulfilled, (state, action) => {
        state.loading = false;
        const batch = state.batches.find(b => b.id === action.payload.id);
        if (batch) {
          batch.status = action.payload.status;
        }
        if (state.currentBatch?.id === action.payload.id) {
          state.currentBatch.status = action.payload.status;
        }
        state.error = null;
      })
      .addCase(updateBatchStatusAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 导入批次
    builder
      .addCase(importBatchesAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(importBatchesAsync.fulfilled, (state) => {
        state.loading = false;
        // 导入成功后重新获取列表
        state.error = null;
      })
      .addCase(importBatchesAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setQueryParams,
  setCurrentBatch,
  updateBatchInList,
  removeBatchFromList,
  addBatchToList,
} = batchSlice.actions;

export default batchSlice.reducer;
