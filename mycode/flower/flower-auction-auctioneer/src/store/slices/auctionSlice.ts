import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ClockInfo, ClockStatus, AuctionParams, AuctionControl, AuctionStatistics } from '../../types';
import { auctioneerApi } from '../../services/api';

interface AuctionState {
  clocks: ClockInfo[];
  selectedClock: number | null;
  statistics: AuctionStatistics | null;
  loading: boolean;
  error: string | null;
  wsConnected: boolean;
}

const initialState: AuctionState = {
  clocks: [],
  selectedClock: null,
  statistics: null,
  loading: false,
  error: null,
  wsConnected: false,
};

// 异步actions
export const getAuctionStatisticsAsync = createAsyncThunk(
  'auction/getStatistics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await auctioneerApi.getDashboardStatistics();
      if (response.data.success) {
        return response.data.data;
      } else {
        return rejectWithValue(response.data.message || '获取统计数据失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取统计数据失败');
    }
  }
);

// 获取钟号状态
export const getClocksStatusAsync = createAsyncThunk(
  'auction/getClocksStatus',
  async (_, { rejectWithValue }) => {
    try {
      const response = await auctioneerApi.getClocksStatus();
      if (response.data.success) {
        return response.data.data;
      } else {
        return rejectWithValue(response.data.message || '获取钟号状态失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取钟号状态失败');
    }
  }
);

// 获取批次列表
export const getBatchesAsync = createAsyncThunk(
  'auction/getBatches',
  async (params: {
    page?: number;
    size?: number;
    status?: string;
    clockNumber?: number;
  }, { rejectWithValue }) => {
    try {
      const response = await auctioneerApi.getBatches(params);
      if (response.data.success) {
        return response.data.data;
      } else {
        return rejectWithValue(response.data.message || '获取批次列表失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取批次列表失败');
    }
  }
);

export const startAuctionAsync = createAsyncThunk(
  'auction/startAuction',
  async (params: AuctionParams, { rejectWithValue }) => {
    try {
      const response = await auctioneerApi.startAuction({
        batchId: params.batchId,
        clockNumber: params.clockNumber,
        startPrice: params.startPrice,
        stepPrice: params.stepPrice,
      });
      if (response.data.success) {
        return { batchId: params.batchId, clockNumber: params.clockNumber };
      } else {
        return rejectWithValue(response.data.message || '启动拍卖失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '启动拍卖失败');
    }
  }
);

export const controlAuctionAsync = createAsyncThunk(
  'auction/controlAuction',
  async (control: AuctionControl, { rejectWithValue }) => {
    try {
      let response;
      const data = {
        batchId: control.batchId,
        clockNumber: control.clockNumber,
      };

      switch (control.action) {
        case 'pause':
          response = await auctioneerApi.pauseAuction(data);
          break;
        case 'resume':
          response = await auctioneerApi.resumeAuction(data);
          break;
        case 'stop':
          response = await auctioneerApi.stopAuction(data);
          break;
        case 'unsold':
          response = await auctioneerApi.markAsUnsold({
            ...data,
            reason: control.reason || '流拍',
          });
          break;
        default:
          throw new Error('不支持的操作类型');
      }

      if (response.data.success) {
        return control;
      } else {
        return rejectWithValue(response.data.message || '操作失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '操作失败');
    }
  }
);

export const adjustPriceAsync = createAsyncThunk(
  'auction/adjustPrice',
  async ({ batchId, clockNumber, newPrice }: {
    batchId: number;
    clockNumber: number;
    newPrice: number
  }, { rejectWithValue }) => {
    try {
      const response = await auctioneerApi.adjustPrice({
        batchId,
        clockNumber,
        newPrice,
      });
      if (response.data.success) {
        return { batchId, newPrice };
      } else {
        return rejectWithValue(response.data.message || '调价失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '调价失败');
    }
  }
);

const auctionSlice = createSlice({
  name: 'auction',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedClock: (state, action: PayloadAction<number>) => {
      state.selectedClock = action.payload;
    },
    updateClockInfo: (state, action: PayloadAction<ClockInfo>) => {
      const index = state.clocks.findIndex(clock => clock.clockNumber === action.payload.clockNumber);
      if (index !== -1) {
        state.clocks[index] = action.payload;
      } else {
        state.clocks.push(action.payload);
      }
    },
    updateClockStatus: (state, action: PayloadAction<{ clockNumber: number; status: ClockStatus }>) => {
      const clock = state.clocks.find(c => c.clockNumber === action.payload.clockNumber);
      if (clock) {
        clock.status = action.payload.status;
      }
    },
    updateCurrentPrice: (state, action: PayloadAction<{ clockNumber: number; price: number }>) => {
      const clock = state.clocks.find(c => c.clockNumber === action.payload.clockNumber);
      if (clock) {
        clock.currentPrice = action.payload.price;
      }
    },
    setWebSocketConnected: (state, action: PayloadAction<boolean>) => {
      state.wsConnected = action.payload;
    },
    initializeClocks: (state, action: PayloadAction<ClockInfo[]>) => {
      state.clocks = action.payload;
    },
  },
  extraReducers: (builder) => {
    // 获取统计数据
    builder
      .addCase(getAuctionStatisticsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAuctionStatisticsAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.statistics = action.payload;
        state.error = null;
      })
      .addCase(getAuctionStatisticsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 启动拍卖
    builder
      .addCase(startAuctionAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(startAuctionAsync.fulfilled, (state, action) => {
        state.loading = false;
        const clock = state.clocks.find(c => c.clockNumber === action.payload.clockNumber);
        if (clock) {
          clock.status = ClockStatus.BIDDING;
        }
        state.error = null;
      })
      .addCase(startAuctionAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 控制拍卖
    builder
      .addCase(controlAuctionAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(controlAuctionAsync.fulfilled, (state, action) => {
        state.loading = false;
        const clock = state.clocks.find(c => c.clockNumber === action.payload.clockNumber);
        if (clock) {
          switch (action.payload.action) {
            case 'pause':
              clock.status = ClockStatus.PAUSED;
              break;
            case 'resume':
              clock.status = ClockStatus.BIDDING;
              break;
            case 'stop':
            case 'unsold':
              clock.status = ClockStatus.COMPLETED;
              break;
          }
        }
        state.error = null;
      })
      .addCase(controlAuctionAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 调价
    builder
      .addCase(adjustPriceAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(adjustPriceAsync.fulfilled, (state, action) => {
        state.loading = false;
        // 更新对应批次的当前价格
        state.clocks.forEach(clock => {
          if (clock.currentBatch?.id === action.payload.batchId) {
            clock.currentPrice = action.payload.newPrice;
            if (clock.currentBatch) {
              clock.currentBatch.currentPrice = action.payload.newPrice;
            }
          }
        });
        state.error = null;
      })
      .addCase(adjustPriceAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setSelectedClock,
  updateClockInfo,
  updateClockStatus,
  updateCurrentPrice,
  setWebSocketConnected,
  initializeClocks,
} = auctionSlice.actions;

export default auctionSlice.reducer;
