import { store } from '../store';
import { 
  update<PERSON>lockInfo, 
  updateClockStatus, 
  updateCurrentPrice, 
  setWebSocketConnected 
} from '../store/slices/auctionSlice';
import { ClockStatus } from '../types';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export class AuctioneerWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private url: string;

  constructor() {
    // 使用环境变量或默认WebSocket地址
    this.url = import.meta.env.VITE_WS_URL || 'ws://localhost:8081/ws/auctioneer';
  }

  connect(): void {
    try {
      this.ws = new WebSocket(this.url);
      this.setupEventListeners();
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      this.handleReconnect();
    }
  }

  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      store.dispatch(setWebSocketConnected(true));
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      
      // 发送认证信息
      this.authenticate();
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      store.dispatch(setWebSocketConnected(false));
      this.stopHeartbeat();
      
      if (!event.wasClean) {
        this.handleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      store.dispatch(setWebSocketConnected(false));
    };
  }

  private authenticate(): void {
    const token = localStorage.getItem('token');
    if (token) {
      this.send('auth', { token });
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    const { type, data } = message;

    switch (type) {
      case 'clock_update':
        store.dispatch(updateClockInfo(data));
        break;
        
      case 'clock_status_change':
        store.dispatch(updateClockStatus({
          clockNumber: data.clockNumber,
          status: data.status as ClockStatus
        }));
        break;
        
      case 'price_update':
        store.dispatch(updateCurrentPrice({
          clockNumber: data.clockNumber,
          price: data.price
        }));
        break;
        
      case 'auction_start':
        console.log('Auction started:', data);
        break;
        
      case 'auction_end':
        console.log('Auction ended:', data);
        break;
        
      case 'bid_update':
        console.log('New bid:', data);
        break;
        
      case 'error':
        console.error('WebSocket error message:', data);
        break;
        
      case 'pong':
        // 心跳响应
        break;
        
      default:
        console.log('Unknown message type:', type, data);
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send('ping', {});
      }
    }, 30000); // 每30秒发送一次心跳
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectInterval * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  send(type: string, data: any): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type,
        data,
        timestamp: new Date().toISOString()
      };
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  // 拍卖控制相关方法
  startAuction(clockNumber: number, batchId: number, params: any): void {
    this.send('start_auction', {
      clockNumber,
      batchId,
      ...params
    });
  }

  pauseAuction(clockNumber: number, batchId: number): void {
    this.send('pause_auction', {
      clockNumber,
      batchId
    });
  }

  resumeAuction(clockNumber: number, batchId: number): void {
    this.send('resume_auction', {
      clockNumber,
      batchId
    });
  }

  stopAuction(clockNumber: number, batchId: number): void {
    this.send('stop_auction', {
      clockNumber,
      batchId
    });
  }

  adjustPrice(clockNumber: number, batchId: number, newPrice: number): void {
    this.send('adjust_price', {
      clockNumber,
      batchId,
      newPrice
    });
  }

  markAsUnsold(clockNumber: number, batchId: number, reason: string): void {
    this.send('mark_unsold', {
      clockNumber,
      batchId,
      reason
    });
  }

  // 订阅钟号数据
  subscribeClock(clockNumber: number): void {
    this.send('subscribe_clock', { clockNumber });
  }

  // 取消订阅钟号数据
  unsubscribeClock(clockNumber: number): void {
    this.send('unsubscribe_clock', { clockNumber });
  }

  disconnect(): void {
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    store.dispatch(setWebSocketConnected(false));
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

// 创建全局实例
export const auctioneerWebSocket = new AuctioneerWebSocketService();
export default auctioneerWebSocket;
