import apiClient from './api';
import { LoginRequest, LoginResponse, User, ApiResponse } from '../types';

class AuthService {
  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await apiClient.post('/auth/login', credentials);
      
      if (response.data.success) {
        const { token, refreshToken, user } = response.data.data;
        
        // 存储认证信息
        localStorage.setItem('token', token);
        localStorage.setItem('refreshToken', refreshToken);
        localStorage.setItem('user', JSON.stringify(user));
        
        return response.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      
      // 跳转到登录页
      window.location.href = '/login';
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.get('/auth/me');
      
      if (response.data.success) {
        // 更新本地存储的用户信息
        localStorage.setItem('user', JSON.stringify(response.data.data));
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Get current user error:', error);
      throw error;
    }
  }

  /**
   * 刷新token
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> {
    try {
      const response = await apiClient.post('/auth/refresh', { refreshToken });
      
      if (response.data.success) {
        const { token, refreshToken: newRefreshToken } = response.data.data;
        
        // 更新本地存储
        localStorage.setItem('token', token);
        localStorage.setItem('refreshToken', newRefreshToken);
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Refresh token error:', error);
      throw error;
    }
  }

  /**
   * 修改密码
   */
  async changePassword(oldPassword: string, newPassword: string): Promise<ApiResponse> {
    try {
      const response = await apiClient.post('/auth/change-password', {
        oldPassword,
        newPassword
      });
      
      return response.data;
    } catch (error: any) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    if (!token || !user) {
      return false;
    }

    try {
      // 检查token是否过期
      const tokenPayload = this.parseJWT(token);
      if (tokenPayload && tokenPayload.exp) {
        const expirationTime = tokenPayload.exp * 1000;
        const currentTime = Date.now();
        
        // 如果token在5分钟内过期，认为需要刷新
        if (expirationTime - currentTime < 5 * 60 * 1000) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  /**
   * 获取本地存储的用户信息
   */
  getStoredUser(): User | null {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Parse stored user error:', error);
      return null;
    }
  }

  /**
   * 获取本地存储的token
   */
  getStoredToken(): string | null {
    return localStorage.getItem('token');
  }

  /**
   * 解析JWT token
   */
  private parseJWT(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Failed to parse JWT:', error);
      return null;
    }
  }

  /**
   * 检查用户是否有拍卖师权限
   */
  isAuctioneer(user?: User): boolean {
    const currentUser = user || this.getStoredUser();
    if (!currentUser) return false;
    
    // 检查用户类型是否为拍卖师 (userType = 1)
    return currentUser.userType === 1;
  }

  /**
   * 检查用户是否有指定钟号的权限
   */
  hasClockPermission(clockNumber: number, user?: User): boolean {
    const currentUser = user || this.getStoredUser();
    if (!currentUser || !this.isAuctioneer(currentUser)) return false;
    
    // 检查用户是否有该钟号的权限
    return currentUser.clockNumbers?.includes(clockNumber) || false;
  }
}

export const authService = new AuthService();
export default authService;
