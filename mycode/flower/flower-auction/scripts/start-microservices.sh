#!/usr/bin/env bash

# 花卉拍卖系统 - 微服务启动脚本
# 启动所有后端微服务

set -e

# 检查bash版本
if [ -z "$BASH_VERSION" ]; then
    echo "错误: 此脚本需要bash环境运行"
    echo "请使用: bash $0 或 ./$(basename $0)"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BIN_DIR="$PROJECT_ROOT/bin"
CONFIG_DIR="$PROJECT_ROOT/configs"
LOG_DIR="$PROJECT_ROOT/logs"
PID_DIR="$LOG_DIR"

# 微服务配置 (服务名:端口:描述:启动顺序)
MICROSERVICES=(
    "auth-service:8081:认证服务:1"
    "user-service:8082:用户服务:2"
    "gateway:8080:API网关:3"
    "main-service:8083:主服务:4"
)

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀"
    echo "🚀                                                      🚀"
    echo "🚀           花卉拍卖系统 - 微服务启动脚本              🚀"
    echo "🚀                                                      🚀"
    echo "🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀"
    echo -e "${NC}"
    echo
    log_info "项目根目录: $PROJECT_ROOT"
    log_info "二进制目录: $BIN_DIR"
    log_info "配置目录: $CONFIG_DIR"
    log_info "日志目录: $LOG_DIR"
    echo
}

# 检查环境
check_environment() {
    log_header "🔍 检查运行环境..."
    
    # 检查二进制目录
    if [ ! -d "$BIN_DIR" ]; then
        log_error "二进制目录不存在: $BIN_DIR"
        log_error "请先运行编译脚本: ./scripts/build-all.sh"
        exit 1
    fi
    
    # 检查配置目录
    if [ ! -d "$CONFIG_DIR" ]; then
        log_warning "配置目录不存在: $CONFIG_DIR，将使用默认配置"
        mkdir -p "$CONFIG_DIR"
    fi
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    log_success "环境检查完成"
    echo
}

# 检查端口占用
check_port() {
    local port=$1
    local service_name=$2
    
    if command -v lsof &> /dev/null; then
        local pid=$(lsof -ti:$port 2>/dev/null)
        if [ ! -z "$pid" ]; then
            log_warning "端口 $port ($service_name) 被进程 $pid 占用"
            return 1
        fi
    fi
    return 0
}

# 检查所有端口
check_all_ports() {
    log_header "🔍 检查端口占用情况..."

    local occupied_ports=()

    for service_info in "${MICROSERVICES[@]}"; do
        IFS=':' read -r service_name port desc order <<< "$service_info"

        if ! check_port "$port" "$service_name"; then
            local pid=$(lsof -ti:$port 2>/dev/null)
            occupied_ports+=("$port:$service_name:$pid")
        else
            log_success "端口 $port ($desc) 可用"
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo
        log_warning "发现端口占用，是否终止占用进程并继续？"
        for port_info in "${occupied_ports[@]}"; do
            IFS=':' read -r port service pid <<< "$port_info"
            echo "  - 端口 $port ($service) 被进程 $pid 占用"
        done
        echo
        read -p "是否终止所有占用进程并继续? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            for port_info in "${occupied_ports[@]}"; do
                IFS=':' read -r port service pid <<< "$port_info"
                log_info "终止进程 $pid (端口 $port)..."
                kill -9 $pid 2>/dev/null || true
            done
            sleep 2
            log_success "所有占用进程已终止"
        else
            log_error "启动取消"
            exit 1
        fi
    fi
    
    echo
}

# 等待服务启动
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
            log_success "$service_name 启动成功"
            return 0
        fi
        
        if [ $((attempt % 5)) -eq 0 ]; then
            log_info "尝试 $attempt/$max_attempts: $service_name 尚未就绪"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name 启动失败或超时"
    return 1
}

# 启动单个微服务
start_microservice() {
    local service_name=$1
    local port=$2
    local desc=$3
    
    log_header "🚀 启动 $desc ($service_name)..."
    
    local binary_path="$BIN_DIR/$service_name"
    local config_path="$CONFIG_DIR/$service_name.yaml"
    local log_path="$LOG_DIR/$service_name.log"
    local pid_path="$PID_DIR/$service_name.pid"
    
    # 检查二进制文件
    if [ ! -f "$binary_path" ]; then
        log_error "二进制文件不存在: $binary_path"
        log_error "请先运行编译脚本: ./scripts/build-all.sh"
        return 1
    fi
    
    # 检查端口
    if ! check_port "$port" "$service_name"; then
        log_error "端口 $port 被占用，无法启动 $service_name"
        return 1
    fi
    
    # 构建启动命令
    local start_cmd="$binary_path"
    if [ -f "$config_path" ]; then
        start_cmd="$start_cmd -config=$config_path"
        log_info "使用配置文件: $config_path"
    else
        log_warning "配置文件不存在: $config_path，使用默认配置"
    fi
    
    # 启动服务
    log_info "启动命令: $start_cmd"
    nohup $start_cmd > "$log_path" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$pid_path"
    
    log_success "$desc 已启动"
    log_info "PID: $pid"
    log_info "端口: $port"
    log_info "日志: $log_path"
    
    # 等待服务就绪
    if wait_for_service "$port" "$desc"; then
        log_success "✅ $desc 启动完成"
        return 0
    else
        log_error "❌ $desc 启动失败"
        # 清理PID文件
        rm -f "$pid_path"
        return 1
    fi
}

# 按顺序启动所有微服务
start_all_microservices() {
    log_header "🚀 开始启动所有微服务..."

    # 按启动顺序排序
    local sorted_services=()
    while IFS= read -r line; do
        sorted_services+=("$line")
    done < <(
        for service_info in "${MICROSERVICES[@]}"; do
            IFS=':' read -r service_name port desc order <<< "$service_info"
            echo "$order:$service_name:$port:$desc"
        done | sort -n
    )
    
    local success_count=0
    local total_count=${#sorted_services[@]}
    local failed_services=()
    
    for service_line in "${sorted_services[@]}"; do
        IFS=':' read -r order service_name port desc <<< "$service_line"
        
        if start_microservice "$service_name" "$port" "$desc"; then
            ((success_count++))
            # 服务间启动间隔
            sleep 3
        else
            failed_services+=("$service_name")
            log_error "停止启动流程，因为 $desc 启动失败"
            break
        fi
        echo
    done
    
    # 显示启动结果
    log_header "📊 启动结果统计"
    log_info "总服务数: $total_count"
    log_success "启动成功: $success_count"
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        log_error "启动失败: ${#failed_services[@]}"
        log_error "失败的服务: ${failed_services[*]}"
        echo
        return 1
    else
        log_success "🎉 所有微服务启动成功！"
        echo
        return 0
    fi
}

# 显示服务状态
show_service_status() {
    log_header "📊 微服务状态总览"
    
    echo "┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐"
    echo "│     服务        │      端口       │      状态       │      访问地址    │"
    echo "├─────────────────┼─────────────────┼─────────────────┼─────────────────┤"

    for service_info in "${MICROSERVICES[@]}"; do
        IFS=':' read -r service_name port desc order <<< "$service_info"

        local pid_file="$PID_DIR/$service_name.pid"
        local status="❌ 未运行"
        local url="N/A"

        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if ps -p $pid > /dev/null 2>&1; then
                status="✅ 运行中"
                url="http://localhost:$port"
            else
                status="❌ 已停止"
            fi
        fi

        printf "│ %-15s │ %-15s │ %-15s │ %-15s │\n" "$desc" "$port" "$status" "$url"
    done
    
    echo "└─────────────────┴─────────────────┴─────────────────┴─────────────────┘"
    echo
}

# 停止所有微服务
stop_all_microservices() {
    log_header "🛑 停止所有微服务..."

    for service_info in "${MICROSERVICES[@]}"; do
        IFS=':' read -r service_name port desc order <<< "$service_info"

        local pid_file="$PID_DIR/$service_name.pid"
        
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if ps -p $pid > /dev/null 2>&1; then
                log_info "停止 $desc (PID: $pid)..."
                kill $pid 2>/dev/null || true
                
                # 等待进程结束
                local count=0
                while ps -p $pid > /dev/null 2>&1 && [ $count -lt 10 ]; do
                    sleep 1
                    count=$((count + 1))
                done
                
                # 如果进程仍在运行，强制杀死
                if ps -p $pid > /dev/null 2>&1; then
                    log_warning "强制停止 $desc..."
                    kill -9 $pid 2>/dev/null || true
                fi
                
                log_success "$desc 已停止"
            else
                log_warning "$desc 进程不存在"
            fi
            
            rm -f "$pid_file"
        else
            log_warning "$desc PID文件不存在"
        fi
    done
    
    log_success "所有微服务已停止"
}

# 显示使用说明
show_usage() {
    log_header "📖 使用说明"
    
    echo "微服务启动完成后，您可以："
    echo
    echo "1. 查看服务状态："
    echo "   $0 status"
    echo
    echo "2. 查看服务日志："
    echo "   tail -f logs/auth-service.log"
    echo "   tail -f logs/user-service.log"
    echo "   tail -f logs/gateway.log"
    echo
    echo "3. 健康检查："
    echo "   curl http://localhost:8081/health  # API网关"
    echo "   curl http://localhost:8081/health  # 认证服务"
    echo "   curl http://localhost:8082/health  # 用户服务"
    echo
    echo "4. 停止所有服务："
    echo "   $0 stop"
    echo
    echo "5. 重启所有服务："
    echo "   $0 restart"
    echo
}

# 显示帮助信息
show_help() {
    echo "花卉拍卖系统微服务启动脚本"
    echo
    echo "用法:"
    echo "  $0 [命令]"
    echo
    echo "命令:"
    echo "  start    启动所有微服务 (默认)"
    echo "  stop     停止所有微服务"
    echo "  restart  重启所有微服务"
    echo "  status   显示服务状态"
    echo "  help     显示帮助信息"
    echo
    echo "注意:"
    echo "  启动前请确保已编译所有微服务: ./scripts/build-all.sh"
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        "start"|"")
            show_banner
            check_environment
            check_all_ports
            
            if start_all_microservices; then
                show_service_status
                show_usage
                log_success "🎉 微服务启动流程完成！"
            else
                log_error "❌ 微服务启动流程失败！"
                exit 1
            fi
            ;;
        "stop")
            stop_all_microservices
            ;;
        "restart")
            stop_all_microservices
            sleep 2
            main start
            ;;
        "status")
            show_service_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
