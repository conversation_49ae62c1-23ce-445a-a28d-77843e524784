#!/bin/bash

# 花卉拍卖系统权限配置脚本
# 用于快速初始化权限和角色权限分配

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_USER=${DB_USER:-root}
DB_PASSWORD=${DB_PASSWORD:-}
API_BASE_URL=${API_BASE_URL:-http://localhost:8081}

echo -e "${BLUE}🔐 花卉拍卖系统权限配置脚本${NC}"
echo "=================================="

# 检查MySQL连接
check_mysql() {
    echo -e "${YELLOW}📡 检查MySQL连接...${NC}"
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" ${DB_PASSWORD:+-p"$DB_PASSWORD"} -e "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MySQL连接成功${NC}"
    else
        echo -e "${RED}❌ MySQL连接失败，请检查数据库配置${NC}"
        exit 1
    fi
}

# 检查API服务
check_api() {
    echo -e "${YELLOW}📡 检查API服务...${NC}"
    if curl -s "$API_BASE_URL/health" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ API服务正常${NC}"
    else
        echo -e "${YELLOW}⚠️  API服务未启动，将仅执行数据库初始化${NC}"
        return 1
    fi
}

# 初始化权限数据
init_permissions() {
    echo -e "${YELLOW}🔧 初始化权限数据...${NC}"
    
    local sql_file="../docs/database/init_permissions.sql"
    if [ ! -f "$sql_file" ]; then
        echo -e "${RED}❌ 权限初始化文件不存在: $sql_file${NC}"
        exit 1
    fi
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" ${DB_PASSWORD:+-p"$DB_PASSWORD"} < "$sql_file"
    echo -e "${GREEN}✅ 权限数据初始化完成${NC}"
}

# 通过API初始化权限
init_permissions_api() {
    echo -e "${YELLOW}🔧 通过API初始化权限...${NC}"
    
    response=$(curl -s -X POST "$API_BASE_URL/api/v1/permissions/init")
    if echo "$response" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ API权限初始化成功${NC}"
    else
        echo -e "${YELLOW}⚠️  API权限初始化响应: $response${NC}"
    fi
}

# 验证权限配置
verify_permissions() {
    echo -e "${YELLOW}🔍 验证权限配置...${NC}"
    
    # 检查权限总数
    local permission_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" ${DB_PASSWORD:+-p"$DB_PASSWORD"} -N -e "USE user_db; SELECT COUNT(*) FROM permission WHERE status = 1;")
    echo -e "${BLUE}📊 权限总数: $permission_count${NC}"
    
    # 检查各角色权限数量
    echo -e "${BLUE}📊 各角色权限统计:${NC}"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" ${DB_PASSWORD:+-p"$DB_PASSWORD"} -e "
    USE user_db;
    SELECT 
        r.id as '角色ID',
        r.name as '角色名称',
        r.code as '角色代码',
        COUNT(rp.permission_id) as '权限数量'
    FROM role r
    LEFT JOIN role_permission rp ON r.id = rp.role_id
    GROUP BY r.id, r.name, r.code
    ORDER BY r.id;
    "
}

# 显示权限模块统计
show_permission_modules() {
    echo -e "${YELLOW}📋 权限模块统计...${NC}"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" ${DB_PASSWORD:+-p"$DB_PASSWORD"} -e "
    USE user_db;
    SELECT 
        module as '模块',
        COUNT(*) as '权限数量'
    FROM permission 
    WHERE status = 1 
    GROUP BY module 
    ORDER BY module;
    "
}

# 显示使用说明
show_usage() {
    echo -e "${BLUE}📖 使用说明:${NC}"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  init      - 初始化权限数据"
    echo "  verify    - 验证权限配置"
    echo "  modules   - 显示权限模块统计"
    echo "  all       - 执行完整配置流程"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST     - 数据库主机 (默认: localhost)"
    echo "  DB_PORT     - 数据库端口 (默认: 3306)"
    echo "  DB_USER     - 数据库用户 (默认: root)"
    echo "  DB_PASSWORD - 数据库密码"
    echo "  API_BASE_URL - API服务地址 (默认: http://localhost:8081)"
}

# 主函数
main() {
    case "${1:-all}" in
        "init")
            check_mysql
            init_permissions
            if check_api; then
                init_permissions_api
            fi
            ;;
        "verify")
            check_mysql
            verify_permissions
            ;;
        "modules")
            check_mysql
            show_permission_modules
            ;;
        "all")
            check_mysql
            init_permissions
            if check_api; then
                init_permissions_api
            fi
            verify_permissions
            show_permission_modules
            echo -e "${GREEN}🎉 权限配置完成！${NC}"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            echo -e "${RED}❌ 未知选项: $1${NC}"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
