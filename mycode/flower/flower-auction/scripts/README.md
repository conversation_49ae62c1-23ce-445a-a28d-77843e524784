# 花卉拍卖系统 - 微服务脚本使用说明

## 📋 脚本概览

本目录包含了花卉拍卖系统微服务架构的管理脚本：

| 脚本名称 | 功能描述 | 使用场景 |
|---------|----------|----------|
| `build-all.sh` | 编译所有微服务 | 开发、部署前编译 |
| `start-microservices.sh` | 启动/管理微服务 | 开发、测试、生产运行 |
| `migrate.sh` | 数据库迁移 | 数据库初始化和更新 |

## 🔨 编译脚本 (build-all.sh)

### 功能特性
- ✅ 自动检测和编译所有微服务
- ✅ 版本信息嵌入（版本号、构建时间、Git提交）
- ✅ 编译统计和错误处理
- ✅ 自动生成启动脚本
- ✅ 跨平台兼容性

### 使用方法

```bash
# 编译所有微服务
./build-all.sh
# 或者
bash build-all.sh

# 清理构建目录
./build-all.sh clean

# 查看帮助
./build-all.sh help
```

### 编译产物

编译完成后，会在 `bin/` 目录生成：

```
bin/
├── auth-service          # 认证服务二进制文件
├── user-service          # 用户服务二进制文件
├── gateway               # API网关二进制文件
├── main-service          # 主服务二进制文件
├── start-auth-service.sh # 认证服务启动脚本
├── start-user-service.sh # 用户服务启动脚本
├── start-gateway.sh      # API网关启动脚本
└── start-main-service.sh # 主服务启动脚本
```

## 🚀 微服务启动脚本 (start-microservices.sh)

### 功能特性
- ✅ 按依赖顺序启动微服务
- ✅ 健康检查和状态监控
- ✅ 端口冲突检测和处理
- ✅ PID文件管理
- ✅ 优雅停止和强制停止
- ✅ 实时日志查看

### 使用方法

```bash
# 启动所有微服务
./start-microservices.sh
# 或者
bash start-microservices.sh

# 查看服务状态
./start-microservices.sh status

# 停止所有微服务
./start-microservices.sh stop

# 重启所有微服务
./start-microservices.sh restart

# 查看帮助
./start-microservices.sh help
```

### 微服务架构

| 服务名称 | 端口 | 描述 | 启动顺序 |
|---------|------|------|----------|
| auth-service | 8081 | 认证服务 | 1 |
| user-service | 8082 | 用户服务 | 2 |
| gateway | 8080 | API网关 | 3 |
| main-service | 8083 | 主服务 | 4 |

### 配置文件

微服务启动时会自动查找配置文件：

```
configs/
├── auth-service.yaml     # 认证服务配置
├── user-service.yaml     # 用户服务配置
├── gateway.yaml          # API网关配置
└── main-service.yaml     # 主服务配置
```

如果配置文件不存在，服务将使用默认配置启动。

### 日志管理

所有微服务的日志都保存在 `logs/` 目录：

```
logs/
├── auth-service.log      # 认证服务日志
├── auth-service.pid      # 认证服务PID文件
├── user-service.log      # 用户服务日志
├── user-service.pid      # 用户服务PID文件
├── gateway.log           # API网关日志
├── gateway.pid           # API网关PID文件
├── main-service.log      # 主服务日志
└── main-service.pid      # 主服务PID文件
```

## 🗄️ 数据库迁移脚本 (migrate.sh)

### 使用方法

```bash
# 开发环境初始化
./migrate.sh dev init

# 生产环境初始化
./migrate.sh prod init

# 查看迁移状态
./migrate.sh dev status

# 查看帮助
./migrate.sh help
```

## 🔧 故障排除

### 常见问题

#### 1. 脚本执行权限问题
```bash
# 解决方案：添加执行权限
chmod +x *.sh
```

#### 2. bash版本兼容性问题
```bash
# 错误信息：declare: -A: invalid option
# 解决方案：使用bash而不是sh执行
bash build-all.sh
# 而不是
sh build-all.sh
```

#### 3. 端口占用问题
```bash
# 脚本会自动检测端口占用并提示处理
# 或者手动查看端口占用
lsof -ti:8080
```

#### 4. Go环境问题
```bash
# 检查Go环境
go version
go env GOPATH
go env GOROOT
```

#### 5. 编译失败
```bash
# 检查依赖
go mod tidy
go mod download

# 清理模块缓存
go clean -modcache
```

### 调试模式

所有脚本都支持详细的日志输出，可以通过查看日志文件进行调试：

```bash
# 查看编译日志
cat logs/build.log

# 查看微服务启动日志
tail -f logs/auth-service.log
tail -f logs/user-service.log
tail -f logs/gateway.log
tail -f logs/main-service.log
```

## 📝 开发建议

### 开发流程

1. **代码修改后重新编译**
   ```bash
   ./build-all.sh
   ```

2. **重启相关微服务**
   ```bash
   ./start-microservices.sh restart
   ```

3. **查看服务状态**
   ```bash
   ./start-microservices.sh status
   ```

4. **查看日志调试**
   ```bash
   tail -f logs/服务名.log
   ```

### 性能优化

- 编译时会自动优化二进制文件大小
- 支持并行编译（Go默认行为）
- 生产环境建议使用编译后的二进制文件而不是 `go run`

### 安全注意事项

- 生产环境请修改默认配置
- 确保配置文件中的敏感信息使用环境变量
- 定期更新依赖包版本

## 🤝 贡献指南

如果需要添加新的微服务：

1. 在 `cmd/新服务名/` 目录创建服务代码
2. 在脚本中的 `SERVICES` 或 `MICROSERVICES` 数组添加配置
3. 创建对应的配置文件模板
4. 更新文档

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查日志文件获取详细错误信息
3. 确认环境配置是否正确
4. 联系开发团队获取支持
