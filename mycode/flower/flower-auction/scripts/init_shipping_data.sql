-- 创建物流相关表
CREATE TABLE IF NOT EXISTS `shipping_company` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '公司名称',
  `code` varchar(50) NOT NULL COMMENT '公司代码',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `website` varchar(200) DEFAULT NULL COMMENT '官网地址',
  `api_endpoint` varchar(200) DEFAULT NULL COMMENT 'API接口地址',
  `api_key` varchar(100) DEFAULT NULL COMMENT 'API密钥',
  `status` tinyint DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物流公司表';

CREATE TABLE IF NOT EXISTS `shipping` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `tracking_number` varchar(100) DEFAULT NULL COMMENT '快递单号',
  `shipping_company_id` bigint DEFAULT NULL COMMENT '物流公司ID',
  `sender_name` varchar(50) DEFAULT NULL COMMENT '发件人姓名',
  `sender_phone` varchar(20) DEFAULT NULL COMMENT '发件人电话',
  `sender_address` varchar(500) DEFAULT NULL COMMENT '发件人地址',
  `receiver_name` varchar(50) DEFAULT NULL COMMENT '收件人姓名',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收件人电话',
  `receiver_address` varchar(500) DEFAULT NULL COMMENT '收件人地址',
  `status` tinyint DEFAULT 0 COMMENT '物流状态 0-待发货 1-已发货 2-运输中 3-已送达 4-异常 5-已退回',
  `shipped_at` timestamp NULL DEFAULT NULL COMMENT '发货时间',
  `delivered_at` timestamp NULL DEFAULT NULL COMMENT '送达时间',
  `estimated_days` int DEFAULT 3 COMMENT '预计送达天数',
  `weight` decimal(8,3) DEFAULT 0.000 COMMENT '重量(kg)',
  `volume` decimal(8,3) DEFAULT 0.000 COMMENT '体积(m³)',
  `shipping_fee` decimal(10,2) DEFAULT 0.00 COMMENT '运费',
  `insurance_fee` decimal(10,2) DEFAULT 0.00 COMMENT '保险费',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_tracking_number` (`tracking_number`),
  KEY `idx_shipping_company_id` (`shipping_company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物流信息表';

CREATE TABLE IF NOT EXISTS `shipping_track` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `shipping_id` bigint NOT NULL COMMENT '物流ID',
  `status` varchar(50) DEFAULT NULL COMMENT '状态描述',
  `location` varchar(100) DEFAULT NULL COMMENT '位置',
  `description` varchar(500) DEFAULT NULL COMMENT '详细描述',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作员',
  `track_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '跟踪时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_shipping_id` (`shipping_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物流跟踪记录表';

CREATE TABLE IF NOT EXISTS `shipping_template` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `shipping_company_id` bigint DEFAULT NULL COMMENT '物流公司ID',
  `sender_name` varchar(50) DEFAULT NULL COMMENT '发件人姓名',
  `sender_phone` varchar(20) DEFAULT NULL COMMENT '发件人电话',
  `sender_address` varchar(500) DEFAULT NULL COMMENT '发件人地址',
  `default_weight` decimal(8,3) DEFAULT 1.000 COMMENT '默认重量',
  `default_volume` decimal(8,3) DEFAULT 0.001 COMMENT '默认体积',
  `estimated_days` int DEFAULT 3 COMMENT '预计送达天数',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认模板',
  `status` tinyint DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_shipping_company_id` (`shipping_company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物流模板表';

-- 插入物流公司测试数据
INSERT INTO `shipping_company` (`name`, `code`, `phone`, `website`, `status`) VALUES
('顺丰快递', 'SF', '95338', 'https://www.sf-express.com', 1),
('圆通快递', 'YTO', '95554', 'https://www.yto.net.cn', 1),
('中通快递', 'ZTO', '95311', 'https://www.zto.com', 1),
('韵达快递', 'YD', '95546', 'https://www.yunda.com', 1),
('申通快递', 'STO', '95543', 'https://www.sto.cn', 1),
('百世快递', 'BEST', '95320', 'https://www.best-inc.com', 1),
('德邦快递', 'DBL', '95353', 'https://www.deppon.com', 1),
('京东快递', 'JD', '950616', 'https://www.jdl.com', 1);

-- 插入物流模板测试数据
INSERT INTO `shipping_template` (`name`, `shipping_company_id`, `sender_name`, `sender_phone`, `sender_address`, `default_weight`, `default_volume`, `estimated_days`, `is_default`, `status`) VALUES
('默认发货模板', 1, '花卉拍卖行', '************', '北京市朝阳区花卉市场1号', 1.000, 0.001, 3, 1, 1),
('鲜花专用模板', 1, '花卉拍卖行', '************', '北京市朝阳区花卉市场1号', 0.500, 0.002, 2, 0, 1),
('大件商品模板', 7, '花卉拍卖行', '************', '北京市朝阳区花卉市场1号', 5.000, 0.010, 5, 0, 1);

-- 插入物流信息测试数据
INSERT INTO `shipping` (`order_id`, `tracking_number`, `shipping_company_id`, `sender_name`, `sender_phone`, `sender_address`, `receiver_name`, `receiver_phone`, `receiver_address`, `status`, `shipped_at`, `delivered_at`, `estimated_days`, `weight`, `volume`, `shipping_fee`, `insurance_fee`, `remark`) VALUES
(1, 'SF1234567890', 1, '花卉拍卖行', '************', '北京市朝阳区花卉市场1号', '张三', '13800138001', '上海市浦东新区陆家嘴金融区1号', 3, '2024-01-15 10:00:00', '2024-01-17 14:30:00', 3, 1.200, 0.002, 15.00, 2.00, '鲜花包装，小心轻放'),
(2, 'YTO9876543210', 2, '花卉拍卖行', '************', '北京市朝阳区花卉市场1号', '李四', '13900139002', '广州市天河区珠江新城2号', 2, '2024-01-16 09:30:00', NULL, 4, 0.800, 0.001, 12.00, 1.50, '康乃馨花束'),
(3, 'ZTO5555666677', 3, '花卉拍卖行', '************', '北京市朝阳区花卉市场1号', '王五', '13700137003', '深圳市南山区科技园3号', 1, '2024-01-17 11:15:00', NULL, 3, 1.500, 0.003, 18.00, 2.50, '百合花束，易碎品'),
(4, 'YD8888999900', 4, '花卉拍卖行', '************', '北京市朝阳区花卉市场1号', '赵六', '13600136004', '杭州市西湖区文三路4号', 0, NULL, NULL, 3, 0.600, 0.001, 10.00, 1.00, '待发货'),
(5, 'SF2222333344', 1, '花卉拍卖行', '************', '北京市朝阳区花卉市场1号', '钱七', '13500135005', '成都市锦江区春熙路5号', 4, '2024-01-14 16:20:00', NULL, 4, 2.000, 0.004, 25.00, 3.00, '运输异常，正在处理');

-- 插入物流跟踪记录测试数据
INSERT INTO `shipping_track` (`shipping_id`, `status`, `location`, `description`, `operator`, `track_time`) VALUES
-- 订单1的跟踪记录（已送达）
(1, '已送达', '上海市浦东新区陆家嘴金融区', '快件已签收，签收人：张三', '快递员小王', '2024-01-17 14:30:00'),
(1, '派件中', '上海市浦东新区', '快件正在派送中，预计30分钟内送达', '快递员小王', '2024-01-17 13:45:00'),
(1, '到达目的地', '上海转运中心', '快件已到达目的地分拣中心', '系统', '2024-01-17 08:20:00'),
(1, '运输中', '济南转运中心', '快件正在运输途中', '系统', '2024-01-16 15:30:00'),
(1, '已发货', '北京朝阳分拣中心', '快件已从北京朝阳分拣中心发出', '系统', '2024-01-15 10:00:00'),

-- 订单2的跟踪记录（运输中）
(2, '运输中', '长沙转运中心', '快件正在运输途中', '系统', '2024-01-17 09:15:00'),
(2, '运输中', '武汉转运中心', '快件已离开武汉转运中心', '系统', '2024-01-16 22:30:00'),
(2, '已发货', '北京朝阳分拣中心', '快件已从北京朝阳分拣中心发出', '系统', '2024-01-16 09:30:00'),

-- 订单3的跟踪记录（已发货）
(3, '已发货', '北京朝阳分拣中心', '快件已从北京朝阳分拣中心发出', '系统', '2024-01-17 11:15:00'),

-- 订单5的跟踪记录（异常）
(5, '运输异常', '石家庄转运中心', '快件在运输过程中出现异常，正在处理中', '客服小李', '2024-01-16 10:30:00'),
(5, '运输中', '石家庄转运中心', '快件正在运输途中', '系统', '2024-01-15 20:45:00'),
(5, '已发货', '北京朝阳分拣中心', '快件已从北京朝阳分拣中心发出', '系统', '2024-01-14 16:20:00');
