-- 插入测试用户数据
-- 注意：这些密码都是 "123456" 的bcrypt哈希值

USE `user_db`;

-- 插入测试用户（除了已存在的admin用户）
INSERT INTO `user` (`username`, `password`, `real_name`, `phone`, `email`, `user_type`, `status`) VALUES
('auctioneer01', '$2a$10$N.zmdr9k7uOCQb376NoUneSZedOgClFisA8XkXy8e.PYwB8kwXBLG', '拍卖师张三', '13800000001', '<EMAIL>', 1, 1),
('buyer01', '$2a$10$N.zmdr9k7uOCQb376NoUneSZedOgClFisA8XkXy8e.PYwB8kwXBLG', '买家李四', '13800000002', '<EMAIL>', 2, 1),
('quality01', '$2a$10$N.zmdr9k7uOCQb376NoUneSZedOgClFisA8XkXy8e.PYwB8kwXBLG', '质检员王五', '13800000003', '<EMAIL>', 4, 0);

-- 为新用户分配角色
INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r WHERE u.username = 'auctioneer01' AND r.code = 'AUCTIONEER';

INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r WHERE u.username = 'buyer01' AND r.code = 'BUYER';

INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r WHERE u.username = 'quality01' AND r.code = 'QUALITY_INSPECTOR';

-- 查看插入结果
SELECT id, username, real_name, phone, email, user_type, status, created_at FROM `user`;
