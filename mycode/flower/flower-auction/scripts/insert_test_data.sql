-- 插入测试数据
USE `user_db`;

-- 插入测试拍卖师
INSERT INTO `user` (`username`, `password`, `real_name`, `phone`, `email`, `user_type`, `status`, `created_at`) VALUES
('auctioneer1', '$2a$10$YE0zwbpm/yqiVEONVs60XukNWLHce5fqA3wGOg5LZ4W103FwIbPum', '张拍卖师', '13800000001', '<EMAIL>', 1, 1, UNIX_TIMESTAMP()),
('buyer1', '$2a$10$YE0zwbpm/yqiVEONVs60XukNWLHce5fqA3wGOg5LZ4W103FwIbPum', '李买家', '13800000002', '<EMAIL>', 2, 1, UNIX_TIMESTAMP());

-- 插入角色关联
INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r WHERE u.username = 'auctioneer1' AND r.code = 'AUCTIONEER';

INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r WHERE u.username = 'buyer1' AND r.code = 'BUYER';

USE `product_db`;

-- 插入测试商品
INSERT INTO `product` (`name`, `category_id`, `description`, `quality_level`, `origin`, `supplier_id`, `status`, `created_at`) VALUES
('红玫瑰', 3, '优质红玫瑰，花朵饱满，颜色鲜艳', 1, '昆明', 1, 1, '2025-06-02 09:00:00'),
('白百合', 4, '纯白百合，香气清雅，花型优美', 1, '昆明', 1, 1, '2025-06-02 09:00:00'),
('粉康乃馨', 5, '粉色康乃馨，花瓣层次丰富', 2, '昆明', 1, 1, '2025-06-02 09:00:00'),
('黄玫瑰', 3, '金黄色玫瑰，寓意友谊', 1, '昆明', 1, 1, '2025-06-02 09:00:00'),
('紫百合', 4, '紫色百合，神秘优雅', 2, '昆明', 1, 1, '2025-06-02 09:00:00');

USE `auction_db`;

-- 插入测试拍卖会
INSERT INTO `auction` (`name`, `start_time`, `end_time`, `status`, `auctioneer_id`, `description`, `created_at`) VALUES
('春季花卉拍卖会', '2025-06-01 09:00:00', '2025-06-01 18:00:00', 1,
 (SELECT id FROM user_db.user WHERE username = 'auctioneer1'),
 '春季优质花卉专场拍卖', '2025-06-02 09:00:00'),
('夏季花卉拍卖会', '2025-06-15 09:00:00', '2025-06-15 18:00:00', 0,
 (SELECT id FROM user_db.user WHERE username = 'auctioneer1'),
 '夏季花卉专场拍卖', '2025-06-02 09:00:00');

-- 插入测试拍卖商品
INSERT INTO `auction_item` (`auction_id`, `product_id`, `start_price`, `current_price`, `step_price`, `status`, `start_time`, `created_at`) VALUES
(1, 1, 50.00, 50.00, 5.00, 1, '2025-06-01 09:00:00', '2025-06-02 09:00:00'),
(1, 2, 80.00, 80.00, 10.00, 1, '2025-06-01 09:30:00', '2025-06-02 09:00:00'),
(1, 3, 30.00, 35.00, 5.00, 1, '2025-06-01 10:00:00', '2025-06-02 09:00:00'),
(2, 4, 60.00, 60.00, 5.00, 0, '2025-06-15 09:00:00', '2025-06-02 09:00:00'),
(2, 5, 70.00, 70.00, 10.00, 0, '2025-06-15 09:30:00', '2025-06-02 09:00:00');

-- 插入测试竞价记录
INSERT INTO `bid_202505` (`auction_item_id`, `user_id`, `price`, `status`) VALUES
(3, (SELECT id FROM user_db.user WHERE username = 'buyer1'), 35.00, 1);

-- 创建当前月份的竞价表
CREATE TABLE IF NOT EXISTS `bid_202506` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '竞价ID',
    `auction_item_id` bigint(20) NOT NULL COMMENT '拍卖商品ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `price` decimal(10,2) NOT NULL COMMENT '出价',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-有效 0-无效',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_auction_item` (`auction_item_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_create_time` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞价记录表202506';

-- 插入当前月份的竞价记录
INSERT INTO `bid_202506` (`auction_item_id`, `user_id`, `price`, `status`) VALUES
(3, (SELECT id FROM user_db.user WHERE username = 'buyer1'), 35.00, 1);
