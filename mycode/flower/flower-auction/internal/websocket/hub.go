package websocket

import (
	"encoding/json"
	"log"
	"sync"
	"time"
)

// Hub 管理所有WebSocket连接
type Hub struct {
	// 注册的客户端连接
	clients map[*Client]bool

	// 拍卖房间映射
	auctionRooms map[string]map[*Client]bool

	// 注册新客户端
	register chan *Client

	// 注销客户端
	unregister chan *Client

	// 广播消息到所有客户端
	broadcast chan []byte

	// 广播消息到特定拍卖房间
	auctionBroadcast chan *AuctionMessage

	// 互斥锁
	mutex sync.RWMutex
}

// AuctionMessage 拍卖消息
type AuctionMessage struct {
	AuctionID string      `json:"auctionId"`
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
}

// Message WebSocket消息格式
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// NewHub 创建新的Hub
func NewHub() *Hub {
	return &Hub{
		clients:          make(map[*Client]bool),
		auctionRooms:     make(map[string]map[*Client]bool),
		register:         make(chan *Client),
		unregister:       make(chan *Client),
		broadcast:        make(chan []byte),
		auctionBroadcast: make(chan *AuctionMessage),
	}
}

// Run 运行Hub
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			h.mutex.Unlock()
			log.Printf("客户端已连接: %s", client.ID)

			// 发送连接成功消息
			h.sendToClient(client, "connected", map[string]interface{}{
				"clientId": client.ID,
				"message":  "连接成功",
			})

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)

				// 从所有拍卖房间中移除客户端
				for auctionID, room := range h.auctionRooms {
					if _, exists := room[client]; exists {
						delete(room, client)
						if len(room) == 0 {
							delete(h.auctionRooms, auctionID)
						}

						// 通知房间内其他用户
						h.broadcastToAuction(auctionID, "user_left", map[string]interface{}{
							"userId":           client.ID,
							"participantCount": len(room),
						})
					}
				}
			}
			h.mutex.Unlock()
			log.Printf("客户端已断开: %s", client.ID)

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
			h.mutex.RUnlock()

		case auctionMsg := <-h.auctionBroadcast:
			h.broadcastToAuctionRoom(auctionMsg.AuctionID, auctionMsg.Type, auctionMsg.Data)
		}
	}
}

// RegisterClient 注册客户端
func (h *Hub) RegisterClient(client *Client) {
	h.register <- client
}

// UnregisterClient 注销客户端
func (h *Hub) UnregisterClient(client *Client) {
	h.unregister <- client
}

// BroadcastToAll 广播消息到所有客户端
func (h *Hub) BroadcastToAll(msgType string, data interface{}) {
	message := Message{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return
	}

	h.broadcast <- messageBytes
}

// BroadcastToAuction 广播消息到特定拍卖房间
func (h *Hub) BroadcastToAuction(auctionID, msgType string, data interface{}) {
	h.auctionBroadcast <- &AuctionMessage{
		AuctionID: auctionID,
		Type:      msgType,
		Data:      data,
	}
}

// broadcastToAuctionRoom 内部方法：广播到拍卖房间
func (h *Hub) broadcastToAuctionRoom(auctionID, msgType string, data interface{}) {
	h.mutex.RLock()
	room, exists := h.auctionRooms[auctionID]
	if !exists {
		h.mutex.RUnlock()
		return
	}

	message := Message{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化拍卖消息失败: %v", err)
		h.mutex.RUnlock()
		return
	}

	for client := range room {
		select {
		case client.send <- messageBytes:
		default:
			close(client.send)
			delete(h.clients, client)
			delete(room, client)
		}
	}
	h.mutex.RUnlock()
}

// JoinAuction 加入拍卖房间
func (h *Hub) JoinAuction(client *Client, auctionID string) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if h.auctionRooms[auctionID] == nil {
		h.auctionRooms[auctionID] = make(map[*Client]bool)
	}

	h.auctionRooms[auctionID][client] = true
	client.AuctionID = auctionID

	log.Printf("客户端 %s 加入拍卖房间 %s", client.ID, auctionID)

	// 通知房间内其他用户
	go h.broadcastToAuctionRoom(auctionID, "user_joined", map[string]interface{}{
		"userId":           client.ID,
		"participantCount": len(h.auctionRooms[auctionID]),
	})

	// 发送当前拍卖状态给新加入的用户
	h.sendAuctionStatus(client, auctionID)
}

// LeaveAuction 离开拍卖房间
func (h *Hub) LeaveAuction(client *Client, auctionID string) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if room, exists := h.auctionRooms[auctionID]; exists {
		if _, inRoom := room[client]; inRoom {
			delete(room, client)
			client.AuctionID = ""

			if len(room) == 0 {
				delete(h.auctionRooms, auctionID)
			}

			log.Printf("客户端 %s 离开拍卖房间 %s", client.ID, auctionID)

			// 通知房间内其他用户
			go h.broadcastToAuctionRoom(auctionID, "user_left", map[string]interface{}{
				"userId":           client.ID,
				"participantCount": len(room),
			})
		}
	}
}

// sendToClient 发送消息给特定客户端
func (h *Hub) sendToClient(client *Client, msgType string, data interface{}) {
	message := Message{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化客户端消息失败: %v", err)
		return
	}

	select {
	case client.send <- messageBytes:
	default:
		close(client.send)
		delete(h.clients, client)
	}
}

// sendAuctionStatus 发送拍卖状态给客户端
func (h *Hub) sendAuctionStatus(client *Client, auctionID string) {
	// 这里应该从数据库获取真实的拍卖状态
	// 暂时使用模拟数据
	status := map[string]interface{}{
		"id":               auctionID,
		"status":           "active",
		"currentPrice":     100.0,
		"startPrice":       50.0,
		"timeRemaining":    3600,
		"bidCount":         5,
		"participantCount": len(h.auctionRooms[auctionID]),
	}

	h.sendToClient(client, "auction_status", status)
}

// GetAuctionParticipantCount 获取拍卖参与人数
func (h *Hub) GetAuctionParticipantCount(auctionID string) int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	if room, exists := h.auctionRooms[auctionID]; exists {
		return len(room)
	}
	return 0
}

// broadcastToAuction 内部方法：广播到拍卖房间（避免死锁）
func (h *Hub) broadcastToAuction(auctionID, msgType string, data interface{}) {
	go func() {
		h.auctionBroadcast <- &AuctionMessage{
			AuctionID: auctionID,
			Type:      msgType,
			Data:      data,
		}
	}()
}
