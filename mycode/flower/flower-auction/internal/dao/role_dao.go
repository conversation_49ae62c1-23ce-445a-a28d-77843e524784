package dao

import (
	"context"
	"errors"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

var (
	ErrRecordNotFound = errors.New("记录不存在")
)

// RoleDAO 角色数据访问对象
type RoleDAO struct {
	db *gorm.DB
}

// NewRoleDAO 创建角色DAO
func NewRoleDAO(db *gorm.DB) *RoleDAO {
	return &RoleDAO{db: db}
}

// Create 创建角色
func (d *RoleDAO) Create(ctx context.Context, role *model.Role) error {
	if err := d.db.WithContext(ctx).Create(role).Error; err != nil {
		return err
	}
	return nil
}

// GetByID 根据ID获取角色
func (d *RoleDAO) GetByID(ctx context.Context, id int64) (*model.Role, error) {
	var role model.Role
	if err := d.db.WithContext(ctx).First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &role, nil
}

// GetByCode 根据编码获取角色
func (d *RoleDAO) GetByCode(ctx context.Context, code string) (*model.Role, error) {
	var role model.Role
	if err := d.db.WithContext(ctx).Where("code = ?", code).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &role, nil
}

// GetByName 根据名称获取角色
func (d *RoleDAO) GetByName(ctx context.Context, name string) (*model.Role, error) {
	var role model.Role
	if err := d.db.WithContext(ctx).Where("name = ?", name).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &role, nil
}

// Update 更新角色
func (d *RoleDAO) Update(ctx context.Context, role *model.Role) error {
	if err := d.db.WithContext(ctx).Save(role).Error; err != nil {
		return err
	}
	return nil
}

// Delete 删除角色
func (d *RoleDAO) Delete(ctx context.Context, id int64) error {
	if err := d.db.WithContext(ctx).Delete(&model.Role{}, id).Error; err != nil {
		return err
	}
	return nil
}

// List 获取角色列表
func (d *RoleDAO) List(ctx context.Context, offset, limit int) ([]*model.Role, int64, error) {
	var roles []*model.Role
	var total int64

	// 获取总数
	if err := d.db.WithContext(ctx).Model(&model.Role{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表
	if err := d.db.WithContext(ctx).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&roles).Error; err != nil {
		return nil, 0, err
	}

	return roles, total, nil
}

// ListWithConditions 根据条件获取角色列表
func (d *RoleDAO) ListWithConditions(ctx context.Context, conditions map[string]interface{}, offset, limit int) ([]*model.Role, int64, error) {
	var roles []*model.Role
	var total int64

	query := d.db.WithContext(ctx).Model(&model.Role{})

	// 应用查询条件
	for condition, value := range conditions {
		if condition == "name LIKE ?" || condition == "code LIKE ?" {
			query = query.Where(condition, value)
		} else {
			query = query.Where(condition+" = ?", value)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表
	if err := query.
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&roles).Error; err != nil {
		return nil, 0, err
	}

	return roles, total, nil
}

// Count 获取角色总数
func (d *RoleDAO) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := d.db.WithContext(ctx).Model(&model.Role{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// CountByStatus 根据状态获取角色数量
func (d *RoleDAO) CountByStatus(ctx context.Context, status int8) (int64, error) {
	var count int64
	if err := d.db.WithContext(ctx).Model(&model.Role{}).Where("status = ?", status).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetAll 获取所有角色
func (d *RoleDAO) GetAll(ctx context.Context) ([]*model.Role, error) {
	var roles []*model.Role
	if err := d.db.WithContext(ctx).Find(&roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}

// GetActiveRoles 获取启用的角色
func (d *RoleDAO) GetActiveRoles(ctx context.Context) ([]*model.Role, error) {
	var roles []*model.Role
	if err := d.db.WithContext(ctx).Where("status = ?", 1).Find(&roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}

// BatchUpdateStatus 批量更新角色状态
func (d *RoleDAO) BatchUpdateStatus(ctx context.Context, ids []int64, status int8) error {
	if err := d.db.WithContext(ctx).Model(&model.Role{}).Where("id IN ?", ids).Update("status", status).Error; err != nil {
		return err
	}
	return nil
}

// BatchDelete 批量删除角色
func (d *RoleDAO) BatchDelete(ctx context.Context, ids []int64) error {
	if err := d.db.WithContext(ctx).Delete(&model.Role{}, ids).Error; err != nil {
		return err
	}
	return nil
}

// GetRolesByIDs 根据ID列表获取角色
func (d *RoleDAO) GetRolesByIDs(ctx context.Context, ids []int64) ([]*model.Role, error) {
	var roles []*model.Role
	if err := d.db.WithContext(ctx).Where("id IN ?", ids).Find(&roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}

// SearchRoles 搜索角色
func (d *RoleDAO) SearchRoles(ctx context.Context, keyword string, offset, limit int) ([]*model.Role, int64, error) {
	var roles []*model.Role
	var total int64

	query := d.db.WithContext(ctx).Model(&model.Role{})

	if keyword != "" {
		searchPattern := "%" + keyword + "%"
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?", searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表
	if err := query.
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&roles).Error; err != nil {
		return nil, 0, err
	}

	return roles, total, nil
}

// ExistsWithCode 检查角色编码是否存在
func (d *RoleDAO) ExistsWithCode(ctx context.Context, code string, excludeID int64) (bool, error) {
	var count int64
	query := d.db.WithContext(ctx).Model(&model.Role{}).Where("code = ?", code)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	if err := query.Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

// ExistsWithName 检查角色名称是否存在
func (d *RoleDAO) ExistsWithName(ctx context.Context, name string, excludeID int64) (bool, error) {
	var count int64
	query := d.db.WithContext(ctx).Model(&model.Role{}).Where("name = ?", name)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	if err := query.Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}
