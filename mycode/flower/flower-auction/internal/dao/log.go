package dao

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// LogDAO 日志数据访问接口
type LogDAO interface {
	// 操作日志
	CreateOperationLog(ctx context.Context, log *model.OperationLog) error
	FindOperationLogByID(ctx context.Context, id int64) (*model.OperationLog, error)
	ListOperationLogs(ctx context.Context, query *model.LogQuery, offset, limit int) ([]*model.OperationLog, error)
	CountOperationLogs(ctx context.Context, query *model.LogQuery) (int64, error)
	DeleteOperationLogs(ctx context.Context, beforeTime time.Time) error
	CountOperationLogsByTime(ctx context.Context, startTime, endTime time.Time) (int64, error)
	CountOperationLogsByStatus(ctx context.Context, startTime, endTime time.Time, minStatus, maxStatus int) (int64, error)
	GetOperationStatsByModule(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error)
	GetOperationStatsByAction(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error)

	// 登录日志
	CreateLoginLog(ctx context.Context, log *model.LoginLog) error
	FindLoginLogByID(ctx context.Context, id int64) (*model.LoginLog, error)
	ListLoginLogs(ctx context.Context, query *model.LogQuery, offset, limit int) ([]*model.LoginLog, error)
	CountLoginLogs(ctx context.Context, query *model.LogQuery) (int64, error)
	DeleteLoginLogs(ctx context.Context, beforeTime time.Time) error
	CountLoginLogsByTime(ctx context.Context, startTime, endTime time.Time) (int64, error)
	CountLoginLogsByStatus(ctx context.Context, startTime, endTime time.Time, status int8) (int64, error)
	GetLoginStatsByUser(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error)
	GetLoginStatsByIP(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error)

	// 系统日志
	CreateSystemLog(ctx context.Context, log *model.SystemLog) error
	FindSystemLogByID(ctx context.Context, id int64) (*model.SystemLog, error)
	ListSystemLogs(ctx context.Context, query *model.LogQuery, offset, limit int) ([]*model.SystemLog, error)
	CountSystemLogs(ctx context.Context, query *model.LogQuery) (int64, error)
	DeleteSystemLogs(ctx context.Context, beforeTime time.Time) error
}

// logDAO 日志数据访问实现
type logDAO struct {
	db *gorm.DB
}

// NewLogDAO 创建日志数据访问实例
func NewLogDAO() LogDAO {
	return &logDAO{
		db: GetUserDB(), // 日志存储在用户数据库中
	}
}

// CreateOperationLog 创建操作日志
func (d *logDAO) CreateOperationLog(ctx context.Context, log *model.OperationLog) error {
	return d.db.WithContext(ctx).Create(log).Error
}

// FindOperationLogByID 根据ID查找操作日志
func (d *logDAO) FindOperationLogByID(ctx context.Context, id int64) (*model.OperationLog, error) {
	var log model.OperationLog
	err := d.db.WithContext(ctx).First(&log, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// ListOperationLogs 查询操作日志列表
func (d *logDAO) ListOperationLogs(ctx context.Context, query *model.LogQuery, offset, limit int) ([]*model.OperationLog, error) {
	var logs []*model.OperationLog
	db := d.db.WithContext(ctx)

	// 构建查询条件
	if query.UserID > 0 {
		db = db.Where("user_id = ?", query.UserID)
	}
	if query.Username != "" {
		db = db.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.Module != "" {
		db = db.Where("module = ?", query.Module)
	}
	if query.Action != "" {
		db = db.Where("action = ?", query.Action)
	}
	if query.Status > 0 {
		db = db.Where("status = ?", query.Status)
	}
	if query.IP != "" {
		db = db.Where("ip = ?", query.IP)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}

	err := db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&logs).Error
	return logs, err
}

// CountOperationLogs 统计操作日志数量
func (d *logDAO) CountOperationLogs(ctx context.Context, query *model.LogQuery) (int64, error) {
	var count int64
	db := d.db.WithContext(ctx).Model(&model.OperationLog{})

	// 构建查询条件（与ListOperationLogs相同）
	if query.UserID > 0 {
		db = db.Where("user_id = ?", query.UserID)
	}
	if query.Username != "" {
		db = db.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.Module != "" {
		db = db.Where("module = ?", query.Module)
	}
	if query.Action != "" {
		db = db.Where("action = ?", query.Action)
	}
	if query.Status > 0 {
		db = db.Where("status = ?", query.Status)
	}
	if query.IP != "" {
		db = db.Where("ip = ?", query.IP)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}

	err := db.Count(&count).Error
	return count, err
}

// DeleteOperationLogs 删除操作日志
func (d *logDAO) DeleteOperationLogs(ctx context.Context, beforeTime time.Time) error {
	return d.db.WithContext(ctx).Where("created_at < ?", beforeTime).Delete(&model.OperationLog{}).Error
}

// CountOperationLogsByTime 按时间统计操作日志
func (d *logDAO) CountOperationLogsByTime(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.OperationLog{}).
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Count(&count).Error
	return count, err
}

// CountOperationLogsByStatus 按状态统计操作日志
func (d *logDAO) CountOperationLogsByStatus(ctx context.Context, startTime, endTime time.Time, minStatus, maxStatus int) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.OperationLog{}).
		Where("created_at >= ? AND created_at <= ? AND status >= ? AND status <= ?", startTime, endTime, minStatus, maxStatus).
		Count(&count).Error
	return count, err
}

// GetOperationStatsByModule 按模块统计操作日志
func (d *logDAO) GetOperationStatsByModule(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error) {
	var results []map[string]interface{}
	err := d.db.WithContext(ctx).Model(&model.OperationLog{}).
		Select("module, COUNT(*) as count").
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Group("module").
		Order("count DESC").
		Find(&results).Error
	return results, err
}

// GetOperationStatsByAction 按操作统计操作日志
func (d *logDAO) GetOperationStatsByAction(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error) {
	var results []map[string]interface{}
	err := d.db.WithContext(ctx).Model(&model.OperationLog{}).
		Select("action, COUNT(*) as count").
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Group("action").
		Order("count DESC").
		Find(&results).Error
	return results, err
}

// CreateLoginLog 创建登录日志
func (d *logDAO) CreateLoginLog(ctx context.Context, log *model.LoginLog) error {
	return d.db.WithContext(ctx).Create(log).Error
}

// FindLoginLogByID 根据ID查找登录日志
func (d *logDAO) FindLoginLogByID(ctx context.Context, id int64) (*model.LoginLog, error) {
	var log model.LoginLog
	err := d.db.WithContext(ctx).First(&log, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// ListLoginLogs 查询登录日志列表
func (d *logDAO) ListLoginLogs(ctx context.Context, query *model.LogQuery, offset, limit int) ([]*model.LoginLog, error) {
	var logs []*model.LoginLog
	db := d.db.WithContext(ctx)

	// 构建查询条件
	if query.UserID > 0 {
		db = db.Where("user_id = ?", query.UserID)
	}
	if query.Username != "" {
		db = db.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.Status >= 0 {
		db = db.Where("status = ?", query.Status)
	}
	if query.IP != "" {
		db = db.Where("ip = ?", query.IP)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("login_time >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("login_time <= ?", query.EndTime)
	}

	err := db.Offset(offset).Limit(limit).Order("login_time DESC").Find(&logs).Error
	return logs, err
}

// CountLoginLogs 统计登录日志数量
func (d *logDAO) CountLoginLogs(ctx context.Context, query *model.LogQuery) (int64, error) {
	var count int64
	db := d.db.WithContext(ctx).Model(&model.LoginLog{})

	// 构建查询条件（与ListLoginLogs相同）
	if query.UserID > 0 {
		db = db.Where("user_id = ?", query.UserID)
	}
	if query.Username != "" {
		db = db.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.Status >= 0 {
		db = db.Where("status = ?", query.Status)
	}
	if query.IP != "" {
		db = db.Where("ip = ?", query.IP)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("login_time >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("login_time <= ?", query.EndTime)
	}

	err := db.Count(&count).Error
	return count, err
}

// DeleteLoginLogs 删除登录日志
func (d *logDAO) DeleteLoginLogs(ctx context.Context, beforeTime time.Time) error {
	return d.db.WithContext(ctx).Where("created_at < ?", beforeTime).Delete(&model.LoginLog{}).Error
}

// CountLoginLogsByTime 按时间统计登录日志
func (d *logDAO) CountLoginLogsByTime(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.LoginLog{}).
		Where("login_time >= ? AND login_time <= ?", startTime, endTime).
		Count(&count).Error
	return count, err
}

// CountLoginLogsByStatus 按状态统计登录日志
func (d *logDAO) CountLoginLogsByStatus(ctx context.Context, startTime, endTime time.Time, status int8) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.LoginLog{}).
		Where("login_time >= ? AND login_time <= ? AND status = ?", startTime, endTime, status).
		Count(&count).Error
	return count, err
}

// GetLoginStatsByUser 按用户统计登录日志
func (d *logDAO) GetLoginStatsByUser(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error) {
	var results []map[string]interface{}
	err := d.db.WithContext(ctx).Model(&model.LoginLog{}).
		Select("username, COUNT(*) as count").
		Where("login_time >= ? AND login_time <= ?", startTime, endTime).
		Group("username").
		Order("count DESC").
		Limit(10).
		Find(&results).Error
	return results, err
}

// GetLoginStatsByIP 按IP统计登录日志
func (d *logDAO) GetLoginStatsByIP(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error) {
	var results []map[string]interface{}
	err := d.db.WithContext(ctx).Model(&model.LoginLog{}).
		Select("ip, COUNT(*) as count").
		Where("login_time >= ? AND login_time <= ?", startTime, endTime).
		Group("ip").
		Order("count DESC").
		Limit(10).
		Find(&results).Error
	return results, err
}

// CreateSystemLog 创建系统日志
func (d *logDAO) CreateSystemLog(ctx context.Context, log *model.SystemLog) error {
	return d.db.WithContext(ctx).Create(log).Error
}

// FindSystemLogByID 根据ID查找系统日志
func (d *logDAO) FindSystemLogByID(ctx context.Context, id int64) (*model.SystemLog, error) {
	var log model.SystemLog
	err := d.db.WithContext(ctx).First(&log, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// ListSystemLogs 查询系统日志列表
func (d *logDAO) ListSystemLogs(ctx context.Context, query *model.LogQuery, offset, limit int) ([]*model.SystemLog, error) {
	var logs []*model.SystemLog
	db := d.db.WithContext(ctx)

	// 构建查询条件
	if query.Module != "" {
		db = db.Where("module = ?", query.Module)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}

	err := db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&logs).Error
	return logs, err
}

// CountSystemLogs 统计系统日志数量
func (d *logDAO) CountSystemLogs(ctx context.Context, query *model.LogQuery) (int64, error) {
	var count int64
	db := d.db.WithContext(ctx).Model(&model.SystemLog{})

	// 构建查询条件
	if query.Module != "" {
		db = db.Where("module = ?", query.Module)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}

	err := db.Count(&count).Error
	return count, err
}

// DeleteSystemLogs 删除系统日志
func (d *logDAO) DeleteSystemLogs(ctx context.Context, beforeTime time.Time) error {
	return d.db.WithContext(ctx).Where("created_at < ?", beforeTime).Delete(&model.SystemLog{}).Error
}
