package dao

import (
	"fmt"
	"sync"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	userDB    *gorm.DB
	productDB *gorm.DB
	auctionDB *gorm.DB
	orderDB   *gorm.DB
	once      sync.Once
	closeOnce sync.Once
)

// InitDB 初始化数据库连接
func InitDB(config *Config) error {
	var err error
	once.Do(func() {
		// 初始化用户数据库连接
		userDB, err = connectDB(&config.UserDB)
		if err != nil {
			return
		}

		// 初始化商品数据库连接
		productDB, err = connectDB(&config.ProductDB)
		if err != nil {
			return
		}

		// 初始化拍卖数据库连接
		auctionDB, err = connectDB(&config.AuctionDB)
		if err != nil {
			return
		}

		// 初始化订单数据库连接
		orderDB, err = connectDB(&config.OrderDB)
		if err != nil {
			return
		}
	})
	return err
}

// connectDB 连接数据库
func connectDB(config *DBConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.User,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("connect to database failed: %v", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("get sql.DB failed: %v", err)
	}

	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)

	return db, nil
}

// GetUserDB 获取用户数据库连接
func GetUserDB() *gorm.DB {
	return userDB
}

// GetProductDB 获取商品数据库连接
func GetProductDB() *gorm.DB {
	return productDB
}

// GetAuctionDB 获取拍卖数据库连接
func GetAuctionDB() *gorm.DB {
	return auctionDB
}

// GetOrderDB 获取订单数据库连接
func GetOrderDB() *gorm.DB {
	return orderDB
}

// GetFinanceDB 获取财务数据库连接
func GetFinanceDB() *gorm.DB {
	return orderDB // 财务数据存储在订单数据库中
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	var err error

	if userDB != nil {
		sqlDB, e := userDB.DB()
		if e == nil {
			err = sqlDB.Close()
		}
	}

	if productDB != nil {
		sqlDB, e := productDB.DB()
		if e == nil {
			if e = sqlDB.Close(); e != nil {
				err = e
			}
		}
	}

	if auctionDB != nil {
		sqlDB, e := auctionDB.DB()
		if e == nil {
			if e = sqlDB.Close(); e != nil {
				err = e
			}
		}
	}

	if orderDB != nil {
		sqlDB, e := orderDB.DB()
		if e == nil {
			if e = sqlDB.Close(); e != nil {
				err = e
			}
		}
	}

	return err
}
