package dao

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// convertDateStringToInt 将日期字符串 (2006-01-02) 转换为整数 (20060102)
func convertDateStringToInt(dateStr string) (int, error) {
	// 移除连字符
	dateStr = strings.ReplaceAll(dateStr, "-", "")
	// 验证格式
	if len(dateStr) != 8 {
		return 0, fmt.Errorf("invalid date format")
	}
	// 验证是否为有效日期
	_, err := time.Parse("20060102", dateStr)
	if err != nil {
		return 0, err
	}
	// 转换为整数
	return strconv.Atoi(dateStr)
}

// UserDAO 用户数据访问接口
type UserDAO interface {
	Create(ctx context.Context, user *model.User) error
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*model.User, error)
	FindByUsername(ctx context.Context, username string) (*model.User, error)
	FindByPhone(ctx context.Context, phone string) (*model.User, error)
	FindWithRoles(ctx context.Context, id int64) (*model.UserWithRoles, error)
	List(ctx context.Context, offset, limit int) ([]*model.User, error)
	Count(ctx context.Context) (int64, error)
	CountByRoleID(ctx context.Context, roleID int64) (int64, error)
	Search(ctx context.Context, username, phone string, userType, status *int8, startDate, endDate string, offset, limit int) ([]*model.User, error)
	CountByCondition(ctx context.Context, username, phone string, userType, status *int8, startDate, endDate string) (int64, error)
	CountActiveUsers(ctx context.Context, since time.Time) (int64, error)
}

// userDAO 用户数据访问实现
type userDAO struct {
	db *gorm.DB
}

// NewUserDAO 创建用户数据访问实例
func NewUserDAO() UserDAO {
	return &userDAO{
		db: GetUserDB(),
	}
}

// Create 创建用户
func (d *userDAO) Create(ctx context.Context, user *model.User) error {
	return d.db.WithContext(ctx).Create(user).Error
}

// Update 更新用户
func (d *userDAO) Update(ctx context.Context, user *model.User) error {
	return d.db.WithContext(ctx).Save(user).Error
}

// Delete 删除用户
func (d *userDAO) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.User{}, id).Error
}

// FindByID 根据ID查找用户
func (d *userDAO) FindByID(ctx context.Context, id int64) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByUsername 根据用户名查找用户
func (d *userDAO) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByPhone 根据手机号查找用户
func (d *userDAO) FindByPhone(ctx context.Context, phone string) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).Where("phone = ?", phone).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindWithRoles 查找用户及其角色
func (d *userDAO) FindWithRoles(ctx context.Context, id int64) (*model.UserWithRoles, error) {
	var user model.User
	if err := d.db.WithContext(ctx).First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	var roles []model.Role
	if err := d.db.WithContext(ctx).
		Joins("JOIN user_role ON user_role.role_id = role.id").
		Where("user_role.user_id = ?", id).
		Find(&roles).Error; err != nil {
		return nil, err
	}

	return &model.UserWithRoles{
		User:  user,
		Roles: roles,
	}, nil
}

// List 查询用户列表
func (d *userDAO) List(ctx context.Context, offset, limit int) ([]*model.User, error) {
	var users []*model.User
	if err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

// Count 统计用户总数
func (d *userDAO) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := d.db.WithContext(ctx).Model(&model.User{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// CountByRoleID 根据角色ID统计用户数量
func (d *userDAO) CountByRoleID(ctx context.Context, roleID int64) (int64, error) {
	var count int64

	// 通过user_role关联表统计用户数量
	if err := d.db.WithContext(ctx).
		Table("user_role").
		Where("role_id = ?", roleID).
		Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

// Search 搜索用户列表
func (d *userDAO) Search(ctx context.Context, username, phone string, userType, status *int8, startDate, endDate string, offset, limit int) ([]*model.User, error) {
	var users []*model.User
	query := d.db.WithContext(ctx).Model(&model.User{})

	// 构建搜索条件
	if username != "" {
		query = query.Where("username LIKE ?", "%"+username+"%")
	}
	if phone != "" {
		query = query.Where("phone LIKE ?", "%"+phone+"%")
	}
	if userType != nil {
		query = query.Where("user_type = ?", *userType)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	if startDate != "" {
		// 将 2006-01-02 格式转换为 20060102 整数
		startDateInt, err := convertDateStringToInt(startDate)
		if err == nil {
			query = query.Where("created_at >= ?", startDateInt)
		}
	}
	if endDate != "" {
		// 将 2006-01-02 格式转换为 20060102 整数
		endDateInt, err := convertDateStringToInt(endDate)
		if err == nil {
			query = query.Where("created_at <= ?", endDateInt)
		}
	}

	// 执行查询
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, err
	}

	return users, nil
}

// CountByCondition 根据条件统计用户数量
func (d *userDAO) CountByCondition(ctx context.Context, username, phone string, userType, status *int8, startDate, endDate string) (int64, error) {
	var count int64
	query := d.db.WithContext(ctx).Model(&model.User{})

	// 构建搜索条件
	if username != "" {
		query = query.Where("username LIKE ?", "%"+username+"%")
	}
	if phone != "" {
		query = query.Where("phone LIKE ?", "%"+phone+"%")
	}
	if userType != nil {
		query = query.Where("user_type = ?", *userType)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	if startDate != "" {
		// 将 2006-01-02 格式转换为 20060102 整数
		startDateInt, err := convertDateStringToInt(startDate)
		if err == nil {
			query = query.Where("created_at >= ?", startDateInt)
		}
	}
	if endDate != "" {
		// 将 2006-01-02 格式转换为 20060102 整数
		endDateInt, err := convertDateStringToInt(endDate)
		if err == nil {
			query = query.Where("created_at <= ?", endDateInt)
		}
	}

	// 执行统计
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

// CountActiveUsers 统计活跃用户数量
func (d *userDAO) CountActiveUsers(ctx context.Context, since time.Time) (int64, error) {
	var count int64
	// 这里可以通过多种方式实现：
	// 1. 查询最近更新时间在since之后的用户
	// 2. 查询用户会话表中活跃的用户
	// 3. 查询最近有操作记录的用户

	// 简单实现：查询最近更新时间在since之后的用户（假设用户活动会更新updated_at字段）
	if err := d.db.WithContext(ctx).
		Model(&model.User{}).
		Where("updated_at >= ? AND status = 1", since).
		Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}
