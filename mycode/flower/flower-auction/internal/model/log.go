package model

import (
	"time"
)

// OperationLog 操作日志模型
type OperationLog struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	UserID      int64     `gorm:"column:user_id" json:"userId"`                    // 操作用户ID
	Username    string    `gorm:"column:username;size:50" json:"username"`        // 操作用户名
	Module      string    `gorm:"column:module;size:50" json:"module"`            // 操作模块
	Action      string    `gorm:"column:action;size:50" json:"action"`            // 操作类型
	Resource    string    `gorm:"column:resource;size:100" json:"resource"`       // 操作资源
	ResourceID  string    `gorm:"column:resource_id;size:50" json:"resourceId"`   // 资源ID
	Method      string    `gorm:"column:method;size:10" json:"method"`            // HTTP方法
	URL         string    `gorm:"column:url;size:500" json:"url"`                 // 请求URL
	IP          string    `gorm:"column:ip;size:50" json:"ip"`                    // 客户端IP
	UserAgent   string    `gorm:"column:user_agent;size:500" json:"userAgent"`    // 用户代理
	RequestData string    `gorm:"column:request_data;type:text" json:"requestData"` // 请求数据
	ResponseData string   `gorm:"column:response_data;type:text" json:"responseData"` // 响应数据
	Status      int       `gorm:"column:status" json:"status"`                    // 响应状态码
	Duration    int64     `gorm:"column:duration" json:"duration"`               // 执行时长(毫秒)
	ErrorMsg    string    `gorm:"column:error_msg;size:1000" json:"errorMsg"`     // 错误信息
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operation_log"
}

// LoginLog 登录日志模型
type LoginLog struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	UserID    int64     `gorm:"column:user_id" json:"userId"`                  // 用户ID
	Username  string    `gorm:"column:username;size:50" json:"username"`      // 用户名
	IP        string    `gorm:"column:ip;size:50" json:"ip"`                  // 登录IP
	UserAgent string    `gorm:"column:user_agent;size:500" json:"userAgent"`  // 用户代理
	Status    int8      `gorm:"column:status" json:"status"`                  // 登录状态 1-成功 0-失败
	ErrorMsg  string    `gorm:"column:error_msg;size:500" json:"errorMsg"`    // 错误信息
	LoginTime time.Time `gorm:"column:login_time" json:"loginTime"`           // 登录时间
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (LoginLog) TableName() string {
	return "login_log"
}

// SystemLog 系统日志模型
type SystemLog struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	Level     string    `gorm:"column:level;size:20" json:"level"`             // 日志级别
	Module    string    `gorm:"column:module;size:50" json:"module"`           // 模块名称
	Message   string    `gorm:"column:message;type:text" json:"message"`       // 日志消息
	Context   string    `gorm:"column:context;type:text" json:"context"`       // 上下文信息
	TraceID   string    `gorm:"column:trace_id;size:100" json:"traceId"`       // 追踪ID
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (SystemLog) TableName() string {
	return "system_log"
}

// LogQuery 日志查询条件
type LogQuery struct {
	UserID     int64     `json:"userId"`
	Username   string    `json:"username"`
	Module     string    `json:"module"`
	Action     string    `json:"action"`
	Status     int       `json:"status"`
	IP         string    `json:"ip"`
	StartTime  time.Time `json:"startTime"`
	EndTime    time.Time `json:"endTime"`
	Page       int       `json:"page"`
	Size       int       `json:"size"`
}
