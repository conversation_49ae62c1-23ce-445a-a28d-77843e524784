package model

import (
	"encoding/json"
	"strconv"
	"time"
)

// GetDateInt 获取当前日期的整数格式 (20060102)
func GetDateInt() int {
	dateStr := time.Now().Format("20060102")
	dateInt, _ := strconv.Atoi(dateStr)
	return dateInt
}

// GetDateIntFromTime 从时间对象获取日期的整数格式 (20060102)
func GetDateIntFromTime(t time.Time) int {
	dateStr := t.Format("20060102")
	dateInt, _ := strconv.Atoi(dateStr)
	return dateInt
}

// ParseDateInt 将整数日期转换为字符串格式 (20060102 -> 2006-01-02)
func ParseDateInt(dateInt int) string {
	if dateInt == 0 {
		return ""
	}
	dateStr := strconv.Itoa(dateInt)
	if len(dateStr) != 8 {
		return ""
	}
	return dateStr[:4] + "-" + dateStr[4:6] + "-" + dateStr[6:8]
}

// User 用户模型
type User struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	Username     string    `gorm:"column:username;uniqueIndex" json:"username"`
	Password     string    `gorm:"column:password" json:"-"`
	RealName     string    `gorm:"column:real_name" json:"realName"`
	Phone        string    `gorm:"column:phone;uniqueIndex" json:"phone"`
	Email        string    `gorm:"column:email" json:"email"`
	UserType     int8      `gorm:"column:user_type" json:"userType"`                                      // 1-拍卖师 2-买家 3-管理员 4-质检员
	Status       int8      `gorm:"column:status" json:"status"`                                           // 0-禁用 1-启用
	ClockNumbers string    `gorm:"column:clock_numbers;type:json" json:"-"`                               // 拍卖师负责的钟号(JSON数组)
	Balance      float64   `gorm:"column:balance;type:decimal(15,2);default:0" json:"balance"`            // 账户余额
	FrozenAmount float64   `gorm:"column:frozen_amount;type:decimal(15,2);default:0" json:"frozenAmount"` // 冻结金额
	CompanyName  string    `gorm:"column:company_name" json:"companyName"`                                // 公司名称
	CreditLevel  int8      `gorm:"column:credit_level;default:1" json:"creditLevel"`                      // 信用等级 1-5
	CreatedAt    int       `gorm:"column:created_at;type:int;index" json:"createdAt"`                     // 格式: 20060102
	UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}

// GetClockNumbers 获取拍卖师的钟号权限
func (u *User) GetClockNumbers() []int {
	if u.ClockNumbers == "" {
		return []int{}
	}

	var clockNumbers []int
	if err := json.Unmarshal([]byte(u.ClockNumbers), &clockNumbers); err != nil {
		return []int{}
	}
	return clockNumbers
}

// SetClockNumbers 设置拍卖师的钟号权限
func (u *User) SetClockNumbers(clockNumbers []int) error {
	data, err := json.Marshal(clockNumbers)
	if err != nil {
		return err
	}
	u.ClockNumbers = string(data)
	return nil
}

// HasClockPermission 检查是否有指定钟号的权限
func (u *User) HasClockPermission(clockNumber int) bool {
	clockNumbers := u.GetClockNumbers()
	for _, num := range clockNumbers {
		if num == clockNumber {
			return true
		}
	}
	return false
}

// Role 角色模型
type Role struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name" json:"name"`
	Code        string    `gorm:"column:code;uniqueIndex" json:"code"`
	Description string    `gorm:"column:description" json:"description"`
	Status      int8      `gorm:"column:status" json:"status"` // 0-禁用 1-启用
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "role"
}

// UserRole 用户角色关联模型
type UserRole struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	UserID    int64     `gorm:"column:user_id;uniqueIndex:uk_user_role" json:"userId"`
	RoleID    int64     `gorm:"column:role_id;uniqueIndex:uk_user_role" json:"roleId"`
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "user_role"
}

// UserWithRoles 用户信息（包含角色）
type UserWithRoles struct {
	User
	Roles []Role `json:"roles"`
}

// Permission 权限模型
type Permission struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name;size:100;not null" json:"name"`             // 权限名称
	Code        string    `gorm:"column:code;size:100;uniqueIndex;not null" json:"code"` // 权限代码
	Description string    `gorm:"column:description;size:500" json:"description"`        // 权限描述
	Module      string    `gorm:"column:module;size:50" json:"module"`                   // 所属模块
	Action      string    `gorm:"column:action;size:50" json:"action"`                   // 操作类型
	Resource    string    `gorm:"column:resource;size:100" json:"resource"`              // 资源标识
	Status      int8      `gorm:"column:status;default:1" json:"status"`                 // 状态 0-禁用 1-启用
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Permission) TableName() string {
	return "permission"
}

// RolePermission 角色权限关联模型
type RolePermission struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	RoleID       int64     `gorm:"column:role_id;not null" json:"roleId"`
	PermissionID int64     `gorm:"column:permission_id;not null" json:"permissionId"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (RolePermission) TableName() string {
	return "role_permission"
}

// RoleWithPermissions 角色信息（包含权限）
type RoleWithPermissions struct {
	Role
	Permissions []Permission `json:"permissions"`
}

// UserPermission 用户权限视图
type UserPermission struct {
	UserID         int64  `json:"userId"`
	Username       string `json:"username"`
	RoleID         int64  `json:"roleId"`
	RoleName       string `json:"roleName"`
	PermissionID   int64  `json:"permissionId"`
	PermissionCode string `json:"permissionCode"`
	Module         string `json:"module"`
	Action         string `json:"action"`
	Resource       string `json:"resource"`
}

// UserVerification 用户实名认证模型
type UserVerification struct {
	ID              int64      `gorm:"primaryKey;column:id" json:"id"`
	UserID          int64      `gorm:"column:user_id;uniqueIndex;not null" json:"userId"`          // 用户ID
	RealName        string     `gorm:"column:real_name;size:50;not null" json:"realName"`          // 真实姓名
	IDCardNumber    string     `gorm:"column:id_card_number;size:20;not null" json:"idCardNumber"` // 身份证号
	IDCardFront     string     `gorm:"column:id_card_front;size:500" json:"idCardFront"`           // 身份证正面照
	IDCardBack      string     `gorm:"column:id_card_back;size:500" json:"idCardBack"`             // 身份证反面照
	BusinessLicense string     `gorm:"column:business_license;size:500" json:"businessLicense"`    // 营业执照(企业用户)
	Status          int8       `gorm:"column:status;default:0" json:"status"`                      // 认证状态 0-待审核 1-已通过 2-已拒绝
	RejectReason    string     `gorm:"column:reject_reason;size:500" json:"rejectReason"`          // 拒绝原因
	VerifiedAt      *time.Time `gorm:"column:verified_at" json:"verifiedAt"`                       // 认证通过时间
	CreatedAt       time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt       time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (UserVerification) TableName() string {
	return "user_verification"
}

// SubAccount 子账号模型
type SubAccount struct {
	ID          int64      `gorm:"primaryKey;column:id" json:"id"`
	ParentID    int64      `gorm:"column:parent_id;not null" json:"parentId"`                    // 主账号ID
	Username    string     `gorm:"column:username;size:50;uniqueIndex;not null" json:"username"` // 用户名
	Password    string     `gorm:"column:password;size:255;not null" json:"-"`                   // 密码
	RealName    string     `gorm:"column:real_name;size:50" json:"realName"`                     // 真实姓名
	Phone       string     `gorm:"column:phone;size:20" json:"phone"`                            // 手机号
	Email       string     `gorm:"column:email;size:100" json:"email"`                           // 邮箱
	Permissions []string   `gorm:"column:permissions;type:json" json:"permissions"`              // 权限列表
	Status      int8       `gorm:"column:status;default:1" json:"status"`                        // 状态 0-禁用 1-启用
	LastLoginAt *time.Time `gorm:"column:last_login_at" json:"lastLoginAt"`                      // 最后登录时间
	CreatedAt   time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (SubAccount) TableName() string {
	return "sub_account"
}

// UserProfile 用户档案模型
type UserProfile struct {
	ID          int64      `gorm:"primaryKey;column:id" json:"id"`
	UserID      int64      `gorm:"column:user_id;uniqueIndex;not null" json:"userId"` // 用户ID
	Avatar      string     `gorm:"column:avatar;size:500" json:"avatar"`              // 头像
	Gender      int8       `gorm:"column:gender;default:0" json:"gender"`             // 性别 0-未知 1-男 2-女
	Birthday    *time.Time `gorm:"column:birthday" json:"birthday"`                   // 生日
	Address     string     `gorm:"column:address;size:200" json:"address"`            // 地址
	Company     string     `gorm:"column:company;size:100" json:"company"`            // 公司
	Position    string     `gorm:"column:position;size:50" json:"position"`           // 职位
	Website     string     `gorm:"column:website;size:200" json:"website"`            // 网站
	Bio         string     `gorm:"column:bio;size:500" json:"bio"`                    // 个人简介
	Preferences string     `gorm:"column:preferences;type:json" json:"preferences"`   // 用户偏好设置
	CreatedAt   time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (UserProfile) TableName() string {
	return "user_profile"
}

// UserWithProfile 用户信息（包含档案）
type UserWithProfile struct {
	User
	Profile      *UserProfile      `json:"profile,omitempty"`
	Verification *UserVerification `json:"verification,omitempty"`
	SubAccounts  []SubAccount      `json:"subAccounts,omitempty"`
}
