package model

import (
	"time"
)

// ShippingStatus 物流状态
type ShippingStatus int8

const (
	ShippingStatusPending   ShippingStatus = 0 // 待发货
	ShippingStatusShipped   ShippingStatus = 1 // 已发货
	ShippingStatusInTransit ShippingStatus = 2 // 运输中
	ShippingStatusDelivered ShippingStatus = 3 // 已送达
	ShippingStatusException ShippingStatus = 4 // 异常
	ShippingStatusReturned  ShippingStatus = 5 // 已退回
)

// ShippingCompany 物流公司
type ShippingCompany struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name;size:100;not null" json:"name"`       // 公司名称
	Code        string    `gorm:"column:code;size:50;uniqueIndex" json:"code"`     // 公司代码
	Phone       string    `gorm:"column:phone;size:20" json:"phone"`               // 联系电话
	Website     string    `gorm:"column:website;size:200" json:"website"`          // 官网地址
	APIEndpoint string    `gorm:"column:api_endpoint;size:200" json:"apiEndpoint"` // API接口地址
	APIKey      string    `gorm:"column:api_key;size:100" json:"apiKey"`           // API密钥
	Status      int8      `gorm:"column:status;default:1" json:"status"`           // 状态 0-禁用 1-启用
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// Shipping 物流信息
type Shipping struct {
	ID                int64            `gorm:"primaryKey;column:id" json:"id"`
	OrderID           int64            `gorm:"column:order_id;not null;index" json:"orderId"`               // 订单ID
	TrackingNumber    string           `gorm:"column:tracking_number;size:100;index" json:"trackingNumber"` // 快递单号
	ShippingCompanyID int64            `gorm:"column:shipping_company_id" json:"shippingCompanyId"`         // 物流公司ID
	ShippingCompany   *ShippingCompany `gorm:"foreignKey:ShippingCompanyID" json:"shippingCompany"`         // 物流公司
	SenderName        string           `gorm:"column:sender_name;size:50" json:"senderName"`                // 发件人姓名
	SenderPhone       string           `gorm:"column:sender_phone;size:20" json:"senderPhone"`              // 发件人电话
	SenderAddress     string           `gorm:"column:sender_address;size:500" json:"senderAddress"`         // 发件人地址
	ReceiverName      string           `gorm:"column:receiver_name;size:50" json:"receiverName"`            // 收件人姓名
	ReceiverPhone     string           `gorm:"column:receiver_phone;size:20" json:"receiverPhone"`          // 收件人电话
	ReceiverAddress   string           `gorm:"column:receiver_address;size:500" json:"receiverAddress"`     // 收件人地址
	Status            ShippingStatus   `gorm:"column:status;default:0" json:"status"`                       // 物流状态
	ShippedAt         *time.Time       `gorm:"column:shipped_at" json:"shippedAt"`                          // 发货时间
	DeliveredAt       *time.Time       `gorm:"column:delivered_at" json:"deliveredAt"`                      // 送达时间
	EstimatedDays     int              `gorm:"column:estimated_days" json:"estimatedDays"`                  // 预计送达天数
	Weight            float64          `gorm:"column:weight;type:decimal(8,3)" json:"weight"`               // 重量(kg)
	Volume            float64          `gorm:"column:volume;type:decimal(8,3)" json:"volume"`               // 体积(m³)
	ShippingFee       float64          `gorm:"column:shipping_fee;type:decimal(10,2)" json:"shippingFee"`   // 运费
	InsuranceFee      float64          `gorm:"column:insurance_fee;type:decimal(10,2)" json:"insuranceFee"` // 保险费
	Remark            string           `gorm:"column:remark;size:500" json:"remark"`                        // 备注
	CreatedAt         time.Time        `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt         time.Time        `gorm:"column:updated_at" json:"updatedAt"`
}

// ShippingTrack 物流跟踪记录
type ShippingTrack struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	ShippingID  int64     `gorm:"column:shipping_id;not null;index" json:"shippingId"` // 物流ID
	Status      string    `gorm:"column:status;size:50" json:"status"`                 // 状态描述
	Location    string    `gorm:"column:location;size:100" json:"location"`            // 位置
	Description string    `gorm:"column:description;size:500" json:"description"`      // 详细描述
	Operator    string    `gorm:"column:operator;size:50" json:"operator"`             // 操作员
	TrackTime   time.Time `gorm:"column:track_time" json:"trackTime"`                  // 跟踪时间
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
}

// ShippingTemplate 物流模板
type ShippingTemplate struct {
	ID                int64     `gorm:"primaryKey;column:id" json:"id"`
	Name              string    `gorm:"column:name;size:100;not null" json:"name"`                    // 模板名称
	ShippingCompanyID int64     `gorm:"column:shipping_company_id" json:"shippingCompanyId"`          // 物流公司ID
	SenderName        string    `gorm:"column:sender_name;size:50" json:"senderName"`                 // 发件人姓名
	SenderPhone       string    `gorm:"column:sender_phone;size:20" json:"senderPhone"`               // 发件人电话
	SenderAddress     string    `gorm:"column:sender_address;size:500" json:"senderAddress"`          // 发件人地址
	DefaultWeight     float64   `gorm:"column:default_weight;type:decimal(8,3)" json:"defaultWeight"` // 默认重量
	DefaultVolume     float64   `gorm:"column:default_volume;type:decimal(8,3)" json:"defaultVolume"` // 默认体积
	EstimatedDays     int       `gorm:"column:estimated_days" json:"estimatedDays"`                   // 预计送达天数
	IsDefault         bool      `gorm:"column:is_default;default:false" json:"isDefault"`             // 是否默认模板
	Status            int8      `gorm:"column:status;default:1" json:"status"`                        // 状态 0-禁用 1-启用
	CreatedAt         time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt         time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Shipping) TableName() string {
	return "shipping"
}

func (ShippingCompany) TableName() string {
	return "shipping_company"
}

func (ShippingTrack) TableName() string {
	return "shipping_track"
}

func (ShippingTemplate) TableName() string {
	return "shipping_template"
}
