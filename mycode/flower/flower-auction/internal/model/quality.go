package model

import (
	"time"
)

// QualityCheck 质检记录模型
type QualityCheck struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	BatchID      int64     `gorm:"column:batch_id" json:"batchId"`           // 批次ID
	ProductID    int64     `gorm:"column:product_id" json:"productId"`       // 商品ID
	InspectorID  int64     `gorm:"column:inspector_id" json:"inspectorId"`   // 质检员ID
	OriginalQty  int       `gorm:"column:original_qty" json:"originalQty"`   // 申报数量
	ActualQty    int       `gorm:"column:actual_qty" json:"actualQty"`       // 实际数量
	QualityLevel int8      `gorm:"column:quality_level" json:"qualityLevel"` // 质量等级 1-优 2-良 3-中 4-差
	DefectRate   float64   `gorm:"column:defect_rate" json:"defectRate"`     // 瑕疵率
	Status       int8      `gorm:"column:status" json:"status"`              // 状态 0-待质检 1-已质检 2-需复核 3-已复核
	Remarks      string    `gorm:"column:remarks" json:"remarks"`            // 备注
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (QualityCheck) TableName() string {
	return "quality_check"
}

// QualityDefect 质检瑕疵记录模型
type QualityDefect struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	CheckID     int64     `gorm:"column:check_id" json:"checkId"`         // 质检记录ID
	DefectType  string    `gorm:"column:defect_type" json:"defectType"`   // 瑕疵类型
	DefectCode  string    `gorm:"column:defect_code" json:"defectCode"`   // 瑕疵代码
	Severity    int8      `gorm:"column:severity" json:"severity"`        // 严重程度 1-轻微 2-一般 3-严重
	Description string    `gorm:"column:description" json:"description"`  // 描述
	ImageURL    string    `gorm:"column:image_url" json:"imageUrl"`       // 图片URL
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (QualityDefect) TableName() string {
	return "quality_defect"
}

// QualityStandard 质检标准模型
type QualityStandard struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	CategoryID   int64     `gorm:"column:category_id" json:"categoryId"`     // 分类ID
	StandardName string    `gorm:"column:standard_name" json:"standardName"` // 标准名称
	Level        int8      `gorm:"column:level" json:"level"`                // 等级
	DefectRate   float64   `gorm:"column:defect_rate" json:"defectRate"`     // 允许瑕疵率
	Description  string    `gorm:"column:description" json:"description"`    // 描述
	Status       int8      `gorm:"column:status" json:"status"`              // 状态 0-禁用 1-启用
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (QualityStandard) TableName() string {
	return "quality_standard"
}

// Batch 批次模型
type Batch struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	BatchNo      string    `gorm:"column:batch_no;uniqueIndex" json:"batchNo"`   // 批次号
	ProductID    int64     `gorm:"column:product_id" json:"productId"`           // 商品ID
	SupplierID   int64     `gorm:"column:supplier_id" json:"supplierId"`         // 供应商ID
	Quantity     int       `gorm:"column:quantity" json:"quantity"`              // 数量
	Unit         string    `gorm:"column:unit" json:"unit"`                      // 单位
	QualityLevel int8      `gorm:"column:quality_level" json:"qualityLevel"`     // 质量等级
	Status       int8      `gorm:"column:status" json:"status"`                  // 状态 0-待入库 1-已入库 2-待质检 3-已质检 4-待拍卖 5-拍卖中 6-已成交 7-已流拍
	WarehouseID  int64     `gorm:"column:warehouse_id" json:"warehouseId"`       // 仓库ID
	Location     string    `gorm:"column:location" json:"location"`              // 库位
	ArrivalTime  time.Time `gorm:"column:arrival_time" json:"arrivalTime"`       // 到货时间
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Batch) TableName() string {
	return "batch"
}

// QualityCheckDetail 质检详情（包含关联信息）
type QualityCheckDetail struct {
	QualityCheck
	Product  Product         `json:"product"`
	Batch    Batch           `json:"batch"`
	Defects  []QualityDefect `json:"defects"`
	Standard QualityStandard `json:"standard"`
}

// BatchWithQuality 批次信息（包含质检信息）
type BatchWithQuality struct {
	Batch
	Product      Product      `json:"product"`
	QualityCheck QualityCheck `json:"qualityCheck"`
}
