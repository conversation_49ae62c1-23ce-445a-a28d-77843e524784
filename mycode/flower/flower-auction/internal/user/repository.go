package user

import (
	"context"
	"errors"

	"gorm.io/gorm"
)

// Repository 用户数据访问接口
type Repository interface {
	// 用户基本操作
	Create(ctx context.Context, user *User) error
	Update(ctx context.Context, user *User) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*User, error)
	FindByUsername(ctx context.Context, username string) (*User, error)
	FindByPhone(ctx context.Context, phone string) (*User, error)
	FindByEmail(ctx context.Context, email string) (*User, error)
	List(ctx context.Context, query *UserQuery) ([]*User, int64, error)
	UpdateStatus(ctx context.Context, id int64, status int8) error
	UpdatePassword(ctx context.Context, id int64, password string) error

	// 角色相关
	CreateRole(ctx context.Context, role *Role) error
	UpdateRole(ctx context.Context, role *Role) error
	DeleteRole(ctx context.Context, id int64) error
	FindRoleByID(ctx context.Context, id int64) (*Role, error)
	FindRoleByCode(ctx context.Context, code string) (*Role, error)
	ListRoles(ctx context.Context) ([]*Role, error)
	
	// 用户角色关联
	AssignRole(ctx context.Context, userID, roleID int64) error
	RemoveRole(ctx context.Context, userID, roleID int64) error
	GetUserRoles(ctx context.Context, userID int64) ([]*Role, error)
	GetUserRoleIDs(ctx context.Context, userID int64) ([]int64, error)
	FindUserWithRoles(ctx context.Context, userID int64) (*UserWithRoles, error)

	// 实名认证
	CreateVerification(ctx context.Context, verification *Verification) error
	UpdateVerification(ctx context.Context, verification *Verification) error
	FindVerificationByUserID(ctx context.Context, userID int64) (*Verification, error)
	ListVerifications(ctx context.Context, status int8, offset, limit int) ([]*Verification, error)
	CountVerifications(ctx context.Context, status int8) (int64, error)

	// 子账号
	CreateSubAccount(ctx context.Context, subAccount *SubAccount) error
	UpdateSubAccount(ctx context.Context, subAccount *SubAccount) error
	DeleteSubAccount(ctx context.Context, id int64) error
	FindSubAccountByID(ctx context.Context, id int64) (*SubAccount, error)
	FindSubAccountByUsername(ctx context.Context, username string) (*SubAccount, error)
	ListSubAccountsByParent(ctx context.Context, parentID int64) ([]*SubAccount, error)

	// 用户档案
	CreateProfile(ctx context.Context, profile *Profile) error
	UpdateProfile(ctx context.Context, profile *Profile) error
	FindProfileByUserID(ctx context.Context, userID int64) (*Profile, error)
	FindUserWithProfile(ctx context.Context, userID int64) (*UserWithProfile, error)
}

// repository 用户数据访问实现
type repository struct {
	db *gorm.DB
}

// NewRepository 创建用户数据访问实例
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

// Create 创建用户
func (r *repository) Create(ctx context.Context, user *User) error {
	return r.db.WithContext(ctx).Create(user).Error
}

// Update 更新用户
func (r *repository) Update(ctx context.Context, user *User) error {
	return r.db.WithContext(ctx).Save(user).Error
}

// Delete 删除用户
func (r *repository) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&User{}, id).Error
}

// FindByID 根据ID查找用户
func (r *repository) FindByID(ctx context.Context, id int64) (*User, error) {
	var user User
	err := r.db.WithContext(ctx).First(&user, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByUsername 根据用户名查找用户
func (r *repository) FindByUsername(ctx context.Context, username string) (*User, error) {
	var user User
	err := r.db.WithContext(ctx).Where("username = ?", username).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByPhone 根据手机号查找用户
func (r *repository) FindByPhone(ctx context.Context, phone string) (*User, error) {
	var user User
	err := r.db.WithContext(ctx).Where("phone = ?", phone).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByEmail 根据邮箱查找用户
func (r *repository) FindByEmail(ctx context.Context, email string) (*User, error) {
	var user User
	err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// List 查询用户列表
func (r *repository) List(ctx context.Context, query *UserQuery) ([]*User, int64, error) {
	var users []*User
	db := r.db.WithContext(ctx).Model(&User{})

	// 构建查询条件
	if query.Username != "" {
		db = db.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.RealName != "" {
		db = db.Where("real_name LIKE ?", "%"+query.RealName+"%")
	}
	if query.Phone != "" {
		db = db.Where("phone = ?", query.Phone)
	}
	if query.Email != "" {
		db = db.Where("email = ?", query.Email)
	}
	if query.UserType > 0 {
		db = db.Where("user_type = ?", query.UserType)
	}
	if query.Status >= 0 {
		db = db.Where("status = ?", query.Status)
	}

	// 统计总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (query.Page - 1) * query.Size
	err := db.Offset(offset).Limit(query.Size).Order("created_at DESC").Find(&users).Error
	return users, total, err
}

// UpdateStatus 更新用户状态
func (r *repository) UpdateStatus(ctx context.Context, id int64, status int8) error {
	return r.db.WithContext(ctx).Model(&User{}).Where("id = ?", id).Update("status", status).Error
}

// UpdatePassword 更新用户密码
func (r *repository) UpdatePassword(ctx context.Context, id int64, password string) error {
	return r.db.WithContext(ctx).Model(&User{}).Where("id = ?", id).Update("password", password).Error
}

// CreateRole 创建角色
func (r *repository) CreateRole(ctx context.Context, role *Role) error {
	return r.db.WithContext(ctx).Create(role).Error
}

// UpdateRole 更新角色
func (r *repository) UpdateRole(ctx context.Context, role *Role) error {
	return r.db.WithContext(ctx).Save(role).Error
}

// DeleteRole 删除角色
func (r *repository) DeleteRole(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&Role{}, id).Error
}

// FindRoleByID 根据ID查找角色
func (r *repository) FindRoleByID(ctx context.Context, id int64) (*Role, error) {
	var role Role
	err := r.db.WithContext(ctx).First(&role, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &role, nil
}

// FindRoleByCode 根据代码查找角色
func (r *repository) FindRoleByCode(ctx context.Context, code string) (*Role, error) {
	var role Role
	err := r.db.WithContext(ctx).Where("code = ?", code).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &role, nil
}

// ListRoles 查询角色列表
func (r *repository) ListRoles(ctx context.Context) ([]*Role, error) {
	var roles []*Role
	err := r.db.WithContext(ctx).Where("status = 1").Order("created_at ASC").Find(&roles).Error
	return roles, err
}

// AssignRole 分配角色
func (r *repository) AssignRole(ctx context.Context, userID, roleID int64) error {
	userRole := &UserRole{
		UserID: userID,
		RoleID: roleID,
	}
	return r.db.WithContext(ctx).Create(userRole).Error
}

// RemoveRole 移除角色
func (r *repository) RemoveRole(ctx context.Context, userID, roleID int64) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND role_id = ?", userID, roleID).Delete(&UserRole{}).Error
}

// GetUserRoles 获取用户角色
func (r *repository) GetUserRoles(ctx context.Context, userID int64) ([]*Role, error) {
	var roles []*Role
	err := r.db.WithContext(ctx).
		Table("role r").
		Joins("INNER JOIN user_role ur ON r.id = ur.role_id").
		Where("ur.user_id = ? AND r.status = 1", userID).
		Find(&roles).Error
	return roles, err
}

// GetUserRoleIDs 获取用户角色ID列表
func (r *repository) GetUserRoleIDs(ctx context.Context, userID int64) ([]int64, error) {
	var roleIDs []int64
	err := r.db.WithContext(ctx).
		Model(&UserRole{}).
		Where("user_id = ?", userID).
		Pluck("role_id", &roleIDs).Error
	return roleIDs, err
}

// FindUserWithRoles 查找用户及其角色
func (r *repository) FindUserWithRoles(ctx context.Context, userID int64) (*UserWithRoles, error) {
	user, err := r.FindByID(ctx, userID)
	if err != nil || user == nil {
		return nil, err
	}

	roles, err := r.GetUserRoles(ctx, userID)
	if err != nil {
		return nil, err
	}

	return &UserWithRoles{
		User:  *user,
		Roles: make([]Role, len(roles)),
	}, nil
}

// CreateVerification 创建实名认证
func (r *repository) CreateVerification(ctx context.Context, verification *Verification) error {
	return r.db.WithContext(ctx).Create(verification).Error
}

// UpdateVerification 更新实名认证
func (r *repository) UpdateVerification(ctx context.Context, verification *Verification) error {
	return r.db.WithContext(ctx).Save(verification).Error
}

// FindVerificationByUserID 根据用户ID查找实名认证
func (r *repository) FindVerificationByUserID(ctx context.Context, userID int64) (*Verification, error) {
	var verification Verification
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&verification).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &verification, nil
}

// ListVerifications 查询实名认证列表
func (r *repository) ListVerifications(ctx context.Context, status int8, offset, limit int) ([]*Verification, error) {
	var verifications []*Verification
	query := r.db.WithContext(ctx)
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&verifications).Error
	return verifications, err
}

// CountVerifications 统计实名认证数量
func (r *repository) CountVerifications(ctx context.Context, status int8) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&Verification{})
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Count(&count).Error
	return count, err
}

// CreateSubAccount 创建子账号
func (r *repository) CreateSubAccount(ctx context.Context, subAccount *SubAccount) error {
	return r.db.WithContext(ctx).Create(subAccount).Error
}

// UpdateSubAccount 更新子账号
func (r *repository) UpdateSubAccount(ctx context.Context, subAccount *SubAccount) error {
	return r.db.WithContext(ctx).Save(subAccount).Error
}

// DeleteSubAccount 删除子账号
func (r *repository) DeleteSubAccount(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&SubAccount{}, id).Error
}

// FindSubAccountByID 根据ID查找子账号
func (r *repository) FindSubAccountByID(ctx context.Context, id int64) (*SubAccount, error) {
	var subAccount SubAccount
	err := r.db.WithContext(ctx).First(&subAccount, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &subAccount, nil
}

// FindSubAccountByUsername 根据用户名查找子账号
func (r *repository) FindSubAccountByUsername(ctx context.Context, username string) (*SubAccount, error) {
	var subAccount SubAccount
	err := r.db.WithContext(ctx).Where("username = ?", username).First(&subAccount).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &subAccount, nil
}

// ListSubAccountsByParent 根据主账号查询子账号列表
func (r *repository) ListSubAccountsByParent(ctx context.Context, parentID int64) ([]*SubAccount, error) {
	var subAccounts []*SubAccount
	err := r.db.WithContext(ctx).Where("parent_id = ?", parentID).Order("created_at DESC").Find(&subAccounts).Error
	return subAccounts, err
}

// CreateProfile 创建用户档案
func (r *repository) CreateProfile(ctx context.Context, profile *Profile) error {
	return r.db.WithContext(ctx).Create(profile).Error
}

// UpdateProfile 更新用户档案
func (r *repository) UpdateProfile(ctx context.Context, profile *Profile) error {
	return r.db.WithContext(ctx).Save(profile).Error
}

// FindProfileByUserID 根据用户ID查找用户档案
func (r *repository) FindProfileByUserID(ctx context.Context, userID int64) (*Profile, error) {
	var profile Profile
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&profile).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &profile, nil
}

// FindUserWithProfile 查找用户完整信息
func (r *repository) FindUserWithProfile(ctx context.Context, userID int64) (*UserWithProfile, error) {
	user, err := r.FindByID(ctx, userID)
	if err != nil || user == nil {
		return nil, err
	}

	result := &UserWithProfile{User: *user}

	// 查找用户档案
	profile, _ := r.FindProfileByUserID(ctx, userID)
	result.Profile = profile

	// 查找实名认证信息
	verification, _ := r.FindVerificationByUserID(ctx, userID)
	result.Verification = verification

	// 查找子账号列表
	subAccounts, _ := r.ListSubAccountsByParent(ctx, userID)
	if len(subAccounts) > 0 {
		result.SubAccounts = make([]SubAccount, len(subAccounts))
		for i, sub := range subAccounts {
			result.SubAccounts[i] = *sub
		}
	}

	return result, nil
}
