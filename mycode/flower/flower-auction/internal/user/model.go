package user

import (
	"time"
)

// User 用户模型
type User struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	Username    string    `gorm:"column:username;size:50;uniqueIndex;not null" json:"username"`
	Password    string    `gorm:"column:password;size:255;not null" json:"-"`
	RealName    string    `gorm:"column:real_name;size:50" json:"realName"`
	Phone       string    `gorm:"column:phone;size:20;uniqueIndex" json:"phone"`
	Email       string    `gorm:"column:email;size:100;uniqueIndex" json:"email"`
	UserType    int8      `gorm:"column:user_type;default:1" json:"userType"` // 1-个人 2-企业
	Status      int8      `gorm:"column:status;default:1" json:"status"`      // 0-禁用 1-启用
	LastLoginAt *time.Time `gorm:"column:last_login_at" json:"lastLoginAt"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}

// GetID 实现auth.User接口
func (u *User) GetID() int64 {
	return u.ID
}

// GetUsername 实现auth.User接口
func (u *User) GetUsername() string {
	return u.Username
}

// GetUserType 实现auth.User接口
func (u *User) GetUserType() int8 {
	return u.UserType
}

// Role 角色模型
type Role struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name;size:50;not null" json:"name"`
	Code        string    `gorm:"column:code;size:50;uniqueIndex;not null" json:"code"`
	Description string    `gorm:"column:description;size:200" json:"description"`
	Status      int8      `gorm:"column:status;default:1" json:"status"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "role"
}

// UserRole 用户角色关联模型
type UserRole struct {
	ID     int64 `gorm:"primaryKey;column:id" json:"id"`
	UserID int64 `gorm:"column:user_id;not null" json:"userId"`
	RoleID int64 `gorm:"column:role_id;not null" json:"roleId"`
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "user_role"
}

// UserWithRoles 用户信息（包含角色）
type UserWithRoles struct {
	User
	Roles []Role `json:"roles"`
}

// Verification 用户实名认证模型
type Verification struct {
	ID              int64      `gorm:"primaryKey;column:id" json:"id"`
	UserID          int64      `gorm:"column:user_id;uniqueIndex;not null" json:"userId"`
	RealName        string     `gorm:"column:real_name;size:50;not null" json:"realName"`
	IDCardNumber    string     `gorm:"column:id_card_number;size:20;not null" json:"idCardNumber"`
	IDCardFront     string     `gorm:"column:id_card_front;size:500" json:"idCardFront"`
	IDCardBack      string     `gorm:"column:id_card_back;size:500" json:"idCardBack"`
	BusinessLicense string     `gorm:"column:business_license;size:500" json:"businessLicense"`
	Status          int8       `gorm:"column:status;default:0" json:"status"`
	RejectReason    string     `gorm:"column:reject_reason;size:500" json:"rejectReason"`
	VerifiedAt      *time.Time `gorm:"column:verified_at" json:"verifiedAt"`
	CreatedAt       time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt       time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Verification) TableName() string {
	return "user_verification"
}

// SubAccount 子账号模型
type SubAccount struct {
	ID          int64      `gorm:"primaryKey;column:id" json:"id"`
	ParentID    int64      `gorm:"column:parent_id;not null" json:"parentId"`
	Username    string     `gorm:"column:username;size:50;uniqueIndex;not null" json:"username"`
	Password    string     `gorm:"column:password;size:255;not null" json:"-"`
	RealName    string     `gorm:"column:real_name;size:50" json:"realName"`
	Phone       string     `gorm:"column:phone;size:20" json:"phone"`
	Email       string     `gorm:"column:email;size:100" json:"email"`
	Permissions []string   `gorm:"column:permissions;type:json" json:"permissions"`
	Status      int8       `gorm:"column:status;default:1" json:"status"`
	LastLoginAt *time.Time `gorm:"column:last_login_at" json:"lastLoginAt"`
	CreatedAt   time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (SubAccount) TableName() string {
	return "sub_account"
}

// Profile 用户档案模型
type Profile struct {
	ID          int64      `gorm:"primaryKey;column:id" json:"id"`
	UserID      int64      `gorm:"column:user_id;uniqueIndex;not null" json:"userId"`
	Avatar      string     `gorm:"column:avatar;size:500" json:"avatar"`
	Gender      int8       `gorm:"column:gender;default:0" json:"gender"`
	Birthday    *time.Time `gorm:"column:birthday" json:"birthday"`
	Address     string     `gorm:"column:address;size:200" json:"address"`
	Company     string     `gorm:"column:company;size:100" json:"company"`
	Position    string     `gorm:"column:position;size:50" json:"position"`
	Website     string     `gorm:"column:website;size:200" json:"website"`
	Bio         string     `gorm:"column:bio;size:500" json:"bio"`
	Preferences string     `gorm:"column:preferences;type:json" json:"preferences"`
	CreatedAt   time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Profile) TableName() string {
	return "user_profile"
}

// UserWithProfile 用户信息（包含档案）
type UserWithProfile struct {
	User
	Profile      *Profile      `json:"profile,omitempty"`
	Verification *Verification `json:"verification,omitempty"`
	SubAccounts  []SubAccount  `json:"subAccounts,omitempty"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required,min=6"`
	RealName string `json:"realName" binding:"required"`
	Phone    string `json:"phone" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	UserType int8   `json:"userType" binding:"required,oneof=1 2"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	RealName string `json:"realName" binding:"required"`
	Phone    string `json:"phone" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1"`
}

// UserQuery 用户查询条件
type UserQuery struct {
	Username string `json:"username"`
	RealName string `json:"realName"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	UserType int8   `json:"userType"`
	Status   int8   `json:"status"`
	Page     int    `json:"page"`
	Size     int    `json:"size"`
}
