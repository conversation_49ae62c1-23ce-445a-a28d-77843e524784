package middleware

import (
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/spf13/viper"
)

// JWTClaims JWT声明结构
type JWTClaims struct {
	UserID   int64   `json:"user_id"`
	Username string  `json:"username"`
	UserType int8    `json:"user_type"`
	Roles    []int64 `json:"roles"`
	jwt.RegisteredClaims
}

// JWTAuth JWT认证中间件
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "缺少认证令牌",
				"code":  "MISSING_TOKEN",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := ""
		if strings.HasPrefix(authHeader, "Bearer ") {
			tokenString = authHeader[7:]
		} else {
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "认证令牌格式错误",
				"code":  "INVALID_TOKEN_FORMAT",
			})
			c.Abort()
			return
		}

		// 解析token
		claims, err := ParseToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "认证令牌无效",
				"code":  "INVALID_TOKEN",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("roles", claims.Roles)

		c.Next()
	}
}

// RequireRole 角色权限验证中间件
func RequireRole(requiredRoles ...int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户角色
		userRoles, exists := c.Get("roles")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足",
				"code":  "INSUFFICIENT_PERMISSIONS",
			})
			c.Abort()
			return
		}

		roles, ok := userRoles.([]int64)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限信息格式错误",
				"code":  "INVALID_ROLE_FORMAT",
			})
			c.Abort()
			return
		}

		// 检查是否有所需角色
		hasPermission := false
		for _, userRole := range roles {
			for _, requiredRole := range requiredRoles {
				if userRole == requiredRole {
					hasPermission = true
					break
				}
			}
			if hasPermission {
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足",
				"code":  "INSUFFICIENT_PERMISSIONS",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireUserType 用户类型验证中间件
func RequireUserType(requiredTypes ...int8) gin.HandlerFunc {
	return func(c *gin.Context) {
		userType, exists := c.Get("user_type")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足",
				"code":  "INSUFFICIENT_PERMISSIONS",
			})
			c.Abort()
			return
		}

		userTypeValue, ok := userType.(int8)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "用户类型格式错误",
				"code":  "INVALID_USER_TYPE",
			})
			c.Abort()
			return
		}

		// 检查用户类型
		hasPermission := false
		for _, requiredType := range requiredTypes {
			if userTypeValue == requiredType {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足",
				"code":  "INSUFFICIENT_PERMISSIONS",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GenerateToken 生成JWT令牌
func GenerateToken(userID int64, username string, userType int8, roles []int64) (string, error) {
	secret := viper.GetString("jwt.secret")
	if secret == "" {
		secret = "flower_auction_secret_key"
	}

	expireHours := viper.GetInt("jwt.expireHours")
	if expireHours == 0 {
		expireHours = 24
	}

	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		UserType: userType,
		Roles:    roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(expireHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "flower-auction",
			Subject:   username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string) (*JWTClaims, error) {
	secret := viper.GetString("jwt.secret")
	if secret == "" {
		secret = "flower_auction_secret_key"
	}

	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("token无效")
}

// RefreshToken 刷新JWT令牌
func RefreshToken(tokenString string) (string, error) {
	claims, err := ParseToken(tokenString)
	if err != nil {
		return "", err
	}

	// 检查token是否即将过期（1小时内）
	if time.Until(claims.ExpiresAt.Time) > time.Hour {
		return tokenString, nil // 不需要刷新
	}

	// 生成新token
	return GenerateToken(claims.UserID, claims.Username, claims.UserType, claims.Roles)
}

// GetCurrentUser 获取当前用户信息
func GetCurrentUser(c *gin.Context) (int64, string, int8, []int64, bool) {
	userID, exists1 := c.Get("user_id")
	username, exists2 := c.Get("username")
	userType, exists3 := c.Get("user_type")
	roles, exists4 := c.Get("roles")

	if !exists1 || !exists2 || !exists3 || !exists4 {
		return 0, "", 0, nil, false
	}

	userIDValue, ok1 := userID.(int64)
	usernameValue, ok2 := username.(string)
	userTypeValue, ok3 := userType.(int8)
	rolesValue, ok4 := roles.([]int64)

	if !ok1 || !ok2 || !ok3 || !ok4 {
		return 0, "", 0, nil, false
	}

	return userIDValue, usernameValue, userTypeValue, rolesValue, true
}
