package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/putonghao/flower-auction/internal/service"
)

// responseWriter 包装响应写入器以捕获响应数据
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// OperationLogger 操作日志中间件
func OperationLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		startTime := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 包装响应写入器
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 计算执行时长
		duration := time.Since(startTime).Milliseconds()

		// 获取用户信息
		userID, username, _, _, _ := GetCurrentUser(c)

		// 解析模块和操作
		module, action := parseModuleAndAction(c.Request.URL.Path, c.Request.Method)

		// 创建操作日志
		operationLog := &model.OperationLog{
			UserID:       userID,
			Username:     username,
			Module:       module,
			Action:       action,
			Resource:     c.Request.URL.Path,
			ResourceID:   extractResourceID(c.Request.URL.Path),
			Method:       c.Request.Method,
			URL:          c.Request.URL.String(),
			IP:           getClientIP(c),
			UserAgent:    c.Request.UserAgent(),
			RequestData:  sanitizeRequestData(string(requestBody)),
			ResponseData: sanitizeResponseData(writer.body.String()),
			Status:       c.Writer.Status(),
			Duration:     duration,
			CreatedAt:    time.Now(),
		}

		// 如果有错误，记录错误信息
		if len(c.Errors) > 0 {
			operationLog.ErrorMsg = c.Errors.String()
		}

		// 异步记录日志
		go func() {
			logService := service.NewLogService()
			logService.CreateOperationLog(c, operationLog)
		}()
	}
}

// LoginLogger 登录日志记录器
func LoginLogger(userID int64, username string, status int8, errorMsg string, c *gin.Context) {
	loginLog := &model.LoginLog{
		UserID:    userID,
		Username:  username,
		IP:        getClientIP(c),
		UserAgent: c.Request.UserAgent(),
		Status:    status,
		ErrorMsg:  errorMsg,
		LoginTime: time.Now(),
		CreatedAt: time.Now(),
	}

	// 异步记录日志
	go func() {
		logService := service.NewLogService()
		logService.CreateLoginLog(c, loginLog)
	}()
}

// parseModuleAndAction 解析模块和操作
func parseModuleAndAction(path, method string) (string, string) {
	// 移除 /api/v1 前缀
	path = strings.TrimPrefix(path, "/api/v1")
	
	// 分割路径
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) == 0 {
		return "unknown", "unknown"
	}

	module := parts[0]
	action := ""

	// 根据HTTP方法和路径确定操作
	switch method {
	case "GET":
		if len(parts) == 1 {
			action = "list"
		} else {
			action = "view"
		}
	case "POST":
		action = "create"
	case "PUT":
		action = "update"
	case "DELETE":
		action = "delete"
	default:
		action = strings.ToLower(method)
	}

	// 特殊路径处理
	if len(parts) >= 2 {
		switch parts[1] {
		case "login":
			action = "login"
		case "logout":
			action = "logout"
		case "register":
			action = "register"
		case "password":
			action = "change_password"
		case "profile":
			action = "update_profile"
		case "status":
			action = "update_status"
		}
	}

	return module, action
}

// extractResourceID 提取资源ID
func extractResourceID(path string) string {
	parts := strings.Split(strings.Trim(path, "/"), "/")
	for i, part := range parts {
		// 如果是数字，可能是ID
		if isNumeric(part) {
			return part
		}
		// 如果前一个部分是已知的资源类型
		if i > 0 && (parts[i-1] == "users" || parts[i-1] == "products" || parts[i-1] == "orders") {
			return part
		}
	}
	return ""
}

// isNumeric 检查字符串是否为数字
func isNumeric(s string) bool {
	for _, r := range s {
		if r < '0' || r > '9' {
			return false
		}
	}
	return len(s) > 0
}

// getClientIP 获取客户端IP
func getClientIP(c *gin.Context) string {
	// 尝试从各种头部获取真实IP
	ip := c.GetHeader("X-Forwarded-For")
	if ip != "" {
		// X-Forwarded-For 可能包含多个IP，取第一个
		ips := strings.Split(ip, ",")
		return strings.TrimSpace(ips[0])
	}

	ip = c.GetHeader("X-Real-IP")
	if ip != "" {
		return ip
	}

	ip = c.GetHeader("X-Forwarded")
	if ip != "" {
		return ip
	}

	ip = c.GetHeader("X-Cluster-Client-IP")
	if ip != "" {
		return ip
	}

	// 最后使用RemoteAddr
	return c.ClientIP()
}

// sanitizeRequestData 清理请求数据（移除敏感信息）
func sanitizeRequestData(data string) string {
	if data == "" {
		return ""
	}

	// 尝试解析为JSON
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(data), &jsonData); err != nil {
		return data // 不是JSON，直接返回
	}

	// 移除敏感字段
	sensitiveFields := []string{"password", "oldPassword", "newPassword", "token", "secret"}
	for _, field := range sensitiveFields {
		if _, exists := jsonData[field]; exists {
			jsonData[field] = "***"
		}
	}

	// 重新序列化
	sanitized, err := json.Marshal(jsonData)
	if err != nil {
		return data
	}

	return string(sanitized)
}

// sanitizeResponseData 清理响应数据（移除敏感信息）
func sanitizeResponseData(data string) string {
	if data == "" {
		return ""
	}

	// 限制响应数据长度
	if len(data) > 1000 {
		return data[:1000] + "..."
	}

	// 尝试解析为JSON并移除敏感信息
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(data), &jsonData); err != nil {
		return data // 不是JSON，直接返回
	}

	// 移除敏感字段
	sensitiveFields := []string{"password", "token", "secret"}
	removeSensitiveFields(jsonData, sensitiveFields)

	// 重新序列化
	sanitized, err := json.Marshal(jsonData)
	if err != nil {
		return data
	}

	return string(sanitized)
}

// removeSensitiveFields 递归移除敏感字段
func removeSensitiveFields(data interface{}, sensitiveFields []string) {
	switch v := data.(type) {
	case map[string]interface{}:
		for key, value := range v {
			// 检查是否是敏感字段
			for _, field := range sensitiveFields {
				if strings.Contains(strings.ToLower(key), field) {
					v[key] = "***"
					continue
				}
			}
			// 递归处理嵌套对象
			removeSensitiveFields(value, sensitiveFields)
		}
	case []interface{}:
		for _, item := range v {
			removeSensitiveFields(item, sensitiveFields)
		}
	}
}
