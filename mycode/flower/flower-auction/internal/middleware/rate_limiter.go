package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter 限流器
type RateLimiter struct {
	visitors map[string]*Visitor
	mutex    sync.RWMutex
	rate     int           // 每分钟允许的请求数
	burst    int           // 突发请求数
	cleanup  time.Duration // 清理间隔
}

// Visitor 访问者信息
type Visitor struct {
	limiter  *TokenBucket
	lastSeen time.Time
}

// TokenBucket 令牌桶
type TokenBucket struct {
	tokens    int
	capacity  int
	refillRate int
	lastRefill time.Time
	mutex     sync.Mutex
}

// NewTokenBucket 创建令牌桶
func NewTokenBucket(capacity, refillRate int) *TokenBucket {
	return &TokenBucket{
		tokens:     capacity,
		capacity:   capacity,
		refillRate: refillRate,
		lastRefill: time.Now(),
	}
}

// Allow 检查是否允许请求
func (tb *TokenBucket) Allow() bool {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	now := time.Now()
	elapsed := now.Sub(tb.lastRefill)
	
	// 计算应该添加的令牌数
	tokensToAdd := int(elapsed.Minutes()) * tb.refillRate
	if tokensToAdd > 0 {
		tb.tokens += tokensToAdd
		if tb.tokens > tb.capacity {
			tb.tokens = tb.capacity
		}
		tb.lastRefill = now
	}

	// 检查是否有可用令牌
	if tb.tokens > 0 {
		tb.tokens--
		return true
	}

	return false
}

// NewRateLimiter 创建限流器
func NewRateLimiter(rate, burst int) *RateLimiter {
	rl := &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     rate,
		burst:    burst,
		cleanup:  5 * time.Minute,
	}

	// 启动清理协程
	go rl.cleanupVisitors()

	return rl
}

// getVisitor 获取访问者
func (rl *RateLimiter) getVisitor(ip string) *Visitor {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	visitor, exists := rl.visitors[ip]
	if !exists {
		visitor = &Visitor{
			limiter:  NewTokenBucket(rl.burst, rl.rate),
			lastSeen: time.Now(),
		}
		rl.visitors[ip] = visitor
	} else {
		visitor.lastSeen = time.Now()
	}

	return visitor
}

// cleanupVisitors 清理过期的访问者
func (rl *RateLimiter) cleanupVisitors() {
	ticker := time.NewTicker(rl.cleanup)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rl.mutex.Lock()
			for ip, visitor := range rl.visitors {
				if time.Since(visitor.lastSeen) > rl.cleanup {
					delete(rl.visitors, ip)
				}
			}
			rl.mutex.Unlock()
		}
	}
}

// Allow 检查IP是否允许请求
func (rl *RateLimiter) Allow(ip string) bool {
	visitor := rl.getVisitor(ip)
	return visitor.limiter.Allow()
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(rate, burst int) gin.HandlerFunc {
	limiter := NewRateLimiter(rate, burst)

	return func(c *gin.Context) {
		ip := c.ClientIP()
		
		if !limiter.Allow(ip) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success": false,
				"error":   "请求过于频繁，请稍后再试",
				"code":    "RATE_LIMIT_EXCEEDED",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// AuthRateLimitMiddleware 认证接口专用限流中间件
func AuthRateLimitMiddleware() gin.HandlerFunc {
	// 认证接口更严格的限流：每分钟最多10次请求，突发5次
	return RateLimitMiddleware(10, 5)
}

// APIRateLimitMiddleware 一般API限流中间件
func APIRateLimitMiddleware() gin.HandlerFunc {
	// 一般API：每分钟最多60次请求，突发20次
	return RateLimitMiddleware(60, 20)
}
