package database

import (
	"fmt"
	"log"
	"sync"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	userDB    *gorm.DB
	productDB *gorm.DB
	logDB     *gorm.DB
	once      sync.Once
)

// Config 数据库配置
type Config struct {
	Host     string
	Port     int
	Username string
	Password string
	Database string
	Charset  string
}

// InitDatabases 初始化数据库连接
func InitDatabases() error {
	var err error
	once.Do(func() {
		// 初始化用户数据库
		userConfig := Config{
			Host:     viper.GetString("database.user.host"),
			Port:     viper.GetInt("database.user.port"),
			Username: viper.GetString("database.user.username"),
			Password: viper.GetString("database.user.password"),
			Database: viper.GetString("database.user.database"),
			Charset:  viper.GetString("database.user.charset"),
		}
		if userConfig.Charset == "" {
			userConfig.Charset = "utf8mb4"
		}
		userDB, err = connectDB(userConfig)
		if err != nil {
			log.Printf("Failed to connect to user database: %v", err)
			return
		}

		// 初始化商品数据库
		productConfig := Config{
			Host:     viper.GetString("database.product.host"),
			Port:     viper.GetInt("database.product.port"),
			Username: viper.GetString("database.product.username"),
			Password: viper.GetString("database.product.password"),
			Database: viper.GetString("database.product.database"),
			Charset:  viper.GetString("database.product.charset"),
		}
		if productConfig.Charset == "" {
			productConfig.Charset = "utf8mb4"
		}
		productDB, err = connectDB(productConfig)
		if err != nil {
			log.Printf("Failed to connect to product database: %v", err)
			return
		}

		// 初始化日志数据库
		logConfig := Config{
			Host:     viper.GetString("database.log.host"),
			Port:     viper.GetInt("database.log.port"),
			Username: viper.GetString("database.log.username"),
			Password: viper.GetString("database.log.password"),
			Database: viper.GetString("database.log.database"),
			Charset:  viper.GetString("database.log.charset"),
		}
		if logConfig.Charset == "" {
			logConfig.Charset = "utf8mb4"
		}
		logDB, err = connectDB(logConfig)
		if err != nil {
			log.Printf("Failed to connect to log database: %v", err)
			return
		}
	})
	return err
}

// connectDB 连接数据库
func connectDB(config Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
		config.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	return db, nil
}

// GetUserDB 获取用户数据库连接
func GetUserDB() *gorm.DB {
	if userDB == nil {
		log.Fatal("User database not initialized")
	}
	return userDB
}

// GetProductDB 获取商品数据库连接
func GetProductDB() *gorm.DB {
	if productDB == nil {
		log.Fatal("Product database not initialized")
	}
	return productDB
}

// GetLogDB 获取日志数据库连接
func GetLogDB() *gorm.DB {
	if logDB == nil {
		log.Fatal("Log database not initialized")
	}
	return logDB
}

// Close 关闭数据库连接
func Close() error {
	var errs []error

	if userDB != nil {
		if sqlDB, err := userDB.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				errs = append(errs, err)
			}
		}
	}

	if productDB != nil {
		if sqlDB, err := productDB.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				errs = append(errs, err)
			}
		}
	}

	if logDB != nil {
		if sqlDB, err := logDB.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				errs = append(errs, err)
			}
		}
	}

	if len(errs) > 0 {
		return errs[0]
	}
	return nil
}
