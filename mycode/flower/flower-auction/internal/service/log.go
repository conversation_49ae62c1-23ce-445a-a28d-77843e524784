package service

import (
	"context"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

// LogService 日志服务接口
type LogService interface {
	// 操作日志
	CreateOperationLog(ctx context.Context, log *model.OperationLog) error
	GetOperationLog(ctx context.Context, id int64) (*model.OperationLog, error)
	ListOperationLogs(ctx context.Context, query *model.LogQuery) ([]*model.OperationLog, int64, error)
	DeleteOperationLogs(ctx context.Context, beforeTime time.Time) error

	// 登录日志
	CreateLoginLog(ctx context.Context, log *model.LoginLog) error
	GetLoginLog(ctx context.Context, id int64) (*model.LoginLog, error)
	ListLoginLogs(ctx context.Context, query *model.LogQuery) ([]*model.LoginLog, int64, error)
	DeleteLoginLogs(ctx context.Context, beforeTime time.Time) error

	// 系统日志
	CreateSystemLog(ctx context.Context, log *model.SystemLog) error
	GetSystemLog(ctx context.Context, id int64) (*model.SystemLog, error)
	ListSystemLogs(ctx context.Context, query *model.LogQuery) ([]*model.SystemLog, int64, error)
	DeleteSystemLogs(ctx context.Context, beforeTime time.Time) error

	// 统计分析
	GetOperationStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
	GetLoginStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
}

// logService 日志服务实现
type logService struct {
	logDAO dao.LogDAO
}

// NewLogService 创建日志服务实例
func NewLogService() LogService {
	return &logService{
		logDAO: dao.NewLogDAO(),
	}
}

// CreateOperationLog 创建操作日志
func (s *logService) CreateOperationLog(ctx context.Context, log *model.OperationLog) error {
	return s.logDAO.CreateOperationLog(ctx, log)
}

// GetOperationLog 获取操作日志
func (s *logService) GetOperationLog(ctx context.Context, id int64) (*model.OperationLog, error) {
	return s.logDAO.FindOperationLogByID(ctx, id)
}

// ListOperationLogs 查询操作日志列表
func (s *logService) ListOperationLogs(ctx context.Context, query *model.LogQuery) ([]*model.OperationLog, int64, error) {
	offset := (query.Page - 1) * query.Size
	logs, err := s.logDAO.ListOperationLogs(ctx, query, offset, query.Size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.logDAO.CountOperationLogs(ctx, query)
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// DeleteOperationLogs 删除操作日志
func (s *logService) DeleteOperationLogs(ctx context.Context, beforeTime time.Time) error {
	return s.logDAO.DeleteOperationLogs(ctx, beforeTime)
}

// CreateLoginLog 创建登录日志
func (s *logService) CreateLoginLog(ctx context.Context, log *model.LoginLog) error {
	return s.logDAO.CreateLoginLog(ctx, log)
}

// GetLoginLog 获取登录日志
func (s *logService) GetLoginLog(ctx context.Context, id int64) (*model.LoginLog, error) {
	return s.logDAO.FindLoginLogByID(ctx, id)
}

// ListLoginLogs 查询登录日志列表
func (s *logService) ListLoginLogs(ctx context.Context, query *model.LogQuery) ([]*model.LoginLog, int64, error) {
	offset := (query.Page - 1) * query.Size
	logs, err := s.logDAO.ListLoginLogs(ctx, query, offset, query.Size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.logDAO.CountLoginLogs(ctx, query)
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// DeleteLoginLogs 删除登录日志
func (s *logService) DeleteLoginLogs(ctx context.Context, beforeTime time.Time) error {
	return s.logDAO.DeleteLoginLogs(ctx, beforeTime)
}

// CreateSystemLog 创建系统日志
func (s *logService) CreateSystemLog(ctx context.Context, log *model.SystemLog) error {
	return s.logDAO.CreateSystemLog(ctx, log)
}

// GetSystemLog 获取系统日志
func (s *logService) GetSystemLog(ctx context.Context, id int64) (*model.SystemLog, error) {
	return s.logDAO.FindSystemLogByID(ctx, id)
}

// ListSystemLogs 查询系统日志列表
func (s *logService) ListSystemLogs(ctx context.Context, query *model.LogQuery) ([]*model.SystemLog, int64, error) {
	offset := (query.Page - 1) * query.Size
	logs, err := s.logDAO.ListSystemLogs(ctx, query, offset, query.Size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.logDAO.CountSystemLogs(ctx, query)
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// DeleteSystemLogs 删除系统日志
func (s *logService) DeleteSystemLogs(ctx context.Context, beforeTime time.Time) error {
	return s.logDAO.DeleteSystemLogs(ctx, beforeTime)
}

// GetOperationStats 获取操作统计
func (s *logService) GetOperationStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总操作数
	totalCount, err := s.logDAO.CountOperationLogsByTime(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	stats["totalCount"] = totalCount

	// 成功操作数
	successCount, err := s.logDAO.CountOperationLogsByStatus(ctx, startTime, endTime, 200, 299)
	if err != nil {
		return nil, err
	}
	stats["successCount"] = successCount

	// 失败操作数
	errorCount, err := s.logDAO.CountOperationLogsByStatus(ctx, startTime, endTime, 400, 599)
	if err != nil {
		return nil, err
	}
	stats["errorCount"] = errorCount

	// 按模块统计
	moduleStats, err := s.logDAO.GetOperationStatsByModule(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	stats["moduleStats"] = moduleStats

	// 按操作统计
	actionStats, err := s.logDAO.GetOperationStatsByAction(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	stats["actionStats"] = actionStats

	return stats, nil
}

// GetLoginStats 获取登录统计
func (s *logService) GetLoginStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总登录次数
	totalCount, err := s.logDAO.CountLoginLogsByTime(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	stats["totalCount"] = totalCount

	// 成功登录次数
	successCount, err := s.logDAO.CountLoginLogsByStatus(ctx, startTime, endTime, 1)
	if err != nil {
		return nil, err
	}
	stats["successCount"] = successCount

	// 失败登录次数
	failCount, err := s.logDAO.CountLoginLogsByStatus(ctx, startTime, endTime, 0)
	if err != nil {
		return nil, err
	}
	stats["failCount"] = failCount

	// 按用户统计
	userStats, err := s.logDAO.GetLoginStatsByUser(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	stats["userStats"] = userStats

	// 按IP统计
	ipStats, err := s.logDAO.GetLoginStatsByIP(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	stats["ipStats"] = ipStats

	return stats, nil
}
