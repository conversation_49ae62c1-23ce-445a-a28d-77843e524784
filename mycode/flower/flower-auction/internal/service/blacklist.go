package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// TokenBlacklistService Token黑名单服务接口
type TokenBlacklistService interface {
	// AddToBlacklist 将token添加到黑名单
	AddToBlacklist(ctx context.Context, tokenString string) error
	// IsBlacklisted 检查token是否在黑名单中
	IsBlacklisted(ctx context.Context, tokenString string) bool
	// CleanExpiredTokens 清理过期的token
	CleanExpiredTokens(ctx context.Context) error
}

// tokenBlacklistService Token黑名单服务实现
type tokenBlacklistService struct {
	// 使用内存存储，生产环境建议使用Redis
	blacklist map[string]time.Time // token -> 过期时间
	mutex     sync.RWMutex
}

// NewTokenBlacklistService 创建Token黑名单服务实例
func NewTokenBlacklistService() TokenBlacklistService {
	service := &tokenBlacklistService{
		blacklist: make(map[string]time.Time),
	}
	
	// 启动定期清理过期token的goroutine
	go service.startCleanupRoutine()
	
	return service
}

// AddToBlacklist 将token添加到黑名单
func (s *tokenBlacklistService) AddToBlacklist(ctx context.Context, tokenString string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	// 解析token获取过期时间
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 这里应该使用与生成token时相同的密钥
		return []byte("flower_auction_secret_key"), nil
	})
	
	if err != nil {
		// 即使解析失败，也将token加入黑名单，设置默认过期时间
		s.blacklist[tokenString] = time.Now().Add(24 * time.Hour)
		return nil
	}
	
	if claims, ok := token.Claims.(jwt.MapClaims); ok {
		if exp, ok := claims["exp"].(float64); ok {
			expirationTime := time.Unix(int64(exp), 0)
			s.blacklist[tokenString] = expirationTime
		} else {
			// 如果无法获取过期时间，设置默认过期时间
			s.blacklist[tokenString] = time.Now().Add(24 * time.Hour)
		}
	} else {
		s.blacklist[tokenString] = time.Now().Add(24 * time.Hour)
	}
	
	return nil
}

// IsBlacklisted 检查token是否在黑名单中
func (s *tokenBlacklistService) IsBlacklisted(ctx context.Context, tokenString string) bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	expirationTime, exists := s.blacklist[tokenString]
	if !exists {
		return false
	}
	
	// 如果token已过期，从黑名单中移除
	if time.Now().After(expirationTime) {
		delete(s.blacklist, tokenString)
		return false
	}
	
	return true
}

// CleanExpiredTokens 清理过期的token
func (s *tokenBlacklistService) CleanExpiredTokens(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	now := time.Now()
	expiredTokens := make([]string, 0)
	
	for token, expirationTime := range s.blacklist {
		if now.After(expirationTime) {
			expiredTokens = append(expiredTokens, token)
		}
	}
	
	for _, token := range expiredTokens {
		delete(s.blacklist, token)
	}
	
	if len(expiredTokens) > 0 {
		fmt.Printf("清理了 %d 个过期的黑名单token\n", len(expiredTokens))
	}
	
	return nil
}

// startCleanupRoutine 启动定期清理过期token的例程
func (s *tokenBlacklistService) startCleanupRoutine() {
	ticker := time.NewTicker(1 * time.Hour) // 每小时清理一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			ctx := context.Background()
			if err := s.CleanExpiredTokens(ctx); err != nil {
				fmt.Printf("清理过期token失败: %v\n", err)
			}
		}
	}
}

// GetBlacklistStats 获取黑名单统计信息（用于监控）
func (s *tokenBlacklistService) GetBlacklistStats() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	now := time.Now()
	totalTokens := len(s.blacklist)
	expiredTokens := 0
	
	for _, expirationTime := range s.blacklist {
		if now.After(expirationTime) {
			expiredTokens++
		}
	}
	
	return map[string]interface{}{
		"total_tokens":   totalTokens,
		"expired_tokens": expiredTokens,
		"active_tokens":  totalTokens - expiredTokens,
	}
}
