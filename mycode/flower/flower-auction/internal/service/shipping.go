package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

// ShippingService 物流服务接口
type ShippingService interface {
	// 物流信息管理
	CreateShipping(ctx context.Context, shipping *model.Shipping) error
	UpdateShipping(ctx context.Context, shipping *model.Shipping) error
	GetShippingByID(ctx context.Context, id int64) (*model.Shipping, error)
	GetShippingByOrderID(ctx context.Context, orderID int64) (*model.Shipping, error)
	GetShippingByTrackingNumber(ctx context.Context, trackingNumber string) (*model.Shipping, error)
	ListShippings(ctx context.Context, params dao.ShippingQueryParams) ([]*model.Shipping, int64, error)
	DeleteShipping(ctx context.Context, id int64) error

	// 发货操作
	ShipOrder(ctx context.Context, orderID int64, shippingInfo ShippingInfo) error
	UpdateShippingStatus(ctx context.Context, id int64, status model.ShippingStatus, remark string) error

	// 物流跟踪
	AddTrackingRecord(ctx context.Context, shippingID int64, track TrackingRecord) error
	GetTrackingHistory(ctx context.Context, shippingID int64) ([]*model.ShippingTrack, error)
	SyncTrackingInfo(ctx context.Context, trackingNumber string) error

	// 物流公司管理
	CreateShippingCompany(ctx context.Context, company *model.ShippingCompany) error
	UpdateShippingCompany(ctx context.Context, company *model.ShippingCompany) error
	GetShippingCompanyByID(ctx context.Context, id int64) (*model.ShippingCompany, error)
	ListShippingCompanies(ctx context.Context, status *int8) ([]*model.ShippingCompany, error)
	DeleteShippingCompany(ctx context.Context, id int64) error

	// 物流模板管理
	CreateShippingTemplate(ctx context.Context, template *model.ShippingTemplate) error
	UpdateShippingTemplate(ctx context.Context, template *model.ShippingTemplate) error
	GetShippingTemplateByID(ctx context.Context, id int64) (*model.ShippingTemplate, error)
	ListShippingTemplates(ctx context.Context, status *int8) ([]*model.ShippingTemplate, error)
	GetDefaultTemplate(ctx context.Context) (*model.ShippingTemplate, error)
	DeleteShippingTemplate(ctx context.Context, id int64) error

	// 统计分析
	GetShippingStatistics(ctx context.Context) (*ShippingStatistics, error)
}

// ShippingInfo 发货信息
type ShippingInfo struct {
	TrackingNumber    string  `json:"trackingNumber"`
	ShippingCompanyID int64   `json:"shippingCompanyId"`
	ReceiverName      string  `json:"receiverName"`
	ReceiverPhone     string  `json:"receiverPhone"`
	ReceiverAddress   string  `json:"receiverAddress"`
	Weight            float64 `json:"weight"`
	Volume            float64 `json:"volume"`
	ShippingFee       float64 `json:"shippingFee"`
	InsuranceFee      float64 `json:"insuranceFee"`
	EstimatedDays     int     `json:"estimatedDays"`
	Remark            string  `json:"remark"`
}

// TrackingRecord 跟踪记录
type TrackingRecord struct {
	Status      string    `json:"status"`
	Location    string    `json:"location"`
	Description string    `json:"description"`
	Operator    string    `json:"operator"`
	TrackTime   time.Time `json:"trackTime"`
}

// ShippingStatistics 物流统计
type ShippingStatistics struct {
	TotalShippings      int64                          `json:"totalShippings"`
	PendingShippings    int64                          `json:"pendingShippings"`
	InTransitShippings  int64                          `json:"inTransitShippings"`
	DeliveredShippings  int64                          `json:"deliveredShippings"`
	ExceptionShippings  int64                          `json:"exceptionShippings"`
	StatusDistribution  map[model.ShippingStatus]int64 `json:"statusDistribution"`
	CompanyDistribution map[string]int64               `json:"companyDistribution"`
	AvgDeliveryDays     float64                        `json:"avgDeliveryDays"`
	OnTimeRate          float64                        `json:"onTimeRate"`
}

// shippingService 物流服务实现
type shippingService struct {
	shippingDAO dao.ShippingDAO
	orderDAO    dao.OrderDAO
}

// NewShippingService 创建物流服务实例
func NewShippingService() ShippingService {
	return &shippingService{
		shippingDAO: dao.NewShippingDAO(),
		orderDAO:    dao.NewOrderDAO(),
	}
}

// CreateShipping 创建物流信息
func (s *shippingService) CreateShipping(ctx context.Context, shipping *model.Shipping) error {
	if shipping.OrderID == 0 {
		return errors.New("订单ID不能为空")
	}

	// 检查订单是否存在
	order, err := s.orderDAO.FindOrderByID(ctx, shipping.OrderID)
	if err != nil {
		return fmt.Errorf("订单不存在: %v", err)
	}
	if order == nil {
		return errors.New("订单不存在")
	}

	// 检查订单状态是否允许发货
	if order.Status != 2 { // 假设2是已付款状态
		return errors.New("订单状态不允许发货")
	}

	// 检查是否已经有物流信息
	existing, _ := s.shippingDAO.GetShippingByOrderID(ctx, shipping.OrderID)
	if existing != nil {
		return errors.New("该订单已有物流信息")
	}

	return s.shippingDAO.CreateShipping(ctx, shipping)
}

// UpdateShipping 更新物流信息
func (s *shippingService) UpdateShipping(ctx context.Context, shipping *model.Shipping) error {
	if shipping.ID == 0 {
		return errors.New("物流ID不能为空")
	}

	// 检查物流信息是否存在
	existing, err := s.shippingDAO.GetShippingByID(ctx, shipping.ID)
	if err != nil {
		return fmt.Errorf("物流信息不存在: %v", err)
	}

	// 更新时间
	shipping.UpdatedAt = time.Now()

	// 如果状态发生变化，添加跟踪记录
	if existing.Status != shipping.Status {
		track := &model.ShippingTrack{
			ShippingID:  shipping.ID,
			Status:      s.getStatusDescription(shipping.Status),
			Location:    "",
			Description: fmt.Sprintf("状态更新为: %s", s.getStatusDescription(shipping.Status)),
			Operator:    "系统",
			TrackTime:   time.Now(),
		}
		s.shippingDAO.CreateShippingTrack(ctx, track)
	}

	return s.shippingDAO.UpdateShipping(ctx, shipping)
}

// GetShippingByID 根据ID获取物流信息
func (s *shippingService) GetShippingByID(ctx context.Context, id int64) (*model.Shipping, error) {
	return s.shippingDAO.GetShippingByID(ctx, id)
}

// GetShippingByOrderID 根据订单ID获取物流信息
func (s *shippingService) GetShippingByOrderID(ctx context.Context, orderID int64) (*model.Shipping, error) {
	return s.shippingDAO.GetShippingByOrderID(ctx, orderID)
}

// GetShippingByTrackingNumber 根据快递单号获取物流信息
func (s *shippingService) GetShippingByTrackingNumber(ctx context.Context, trackingNumber string) (*model.Shipping, error) {
	if trackingNumber == "" {
		return nil, errors.New("快递单号不能为空")
	}
	return s.shippingDAO.GetShippingByTrackingNumber(ctx, trackingNumber)
}

// ListShippings 获取物流列表
func (s *shippingService) ListShippings(ctx context.Context, params dao.ShippingQueryParams) ([]*model.Shipping, int64, error) {
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}
	if params.PageSize > 100 {
		params.PageSize = 100
	}

	return s.shippingDAO.ListShippings(ctx, params)
}

// DeleteShipping 删除物流信息
func (s *shippingService) DeleteShipping(ctx context.Context, id int64) error {
	// 检查物流信息是否存在
	shipping, err := s.shippingDAO.GetShippingByID(ctx, id)
	if err != nil {
		return fmt.Errorf("物流信息不存在: %v", err)
	}

	// 检查是否可以删除（例如已发货的不能删除）
	if shipping.Status != model.ShippingStatusPending {
		return errors.New("只能删除待发货状态的物流信息")
	}

	return s.shippingDAO.DeleteShipping(ctx, id)
}

// ShipOrder 发货
func (s *shippingService) ShipOrder(ctx context.Context, orderID int64, shippingInfo ShippingInfo) error {
	// 检查订单是否存在
	order, err := s.orderDAO.FindOrderByID(ctx, orderID)
	if err != nil {
		return fmt.Errorf("订单不存在: %v", err)
	}
	if order == nil {
		return errors.New("订单不存在")
	}

	// 检查订单状态
	if order.Status != 2 { // 假设2是已付款状态
		return errors.New("订单状态不允许发货")
	}

	// 获取默认发件人信息
	template, _ := s.shippingDAO.GetDefaultTemplate(ctx)

	now := time.Now()
	shipping := &model.Shipping{
		OrderID:           orderID,
		TrackingNumber:    shippingInfo.TrackingNumber,
		ShippingCompanyID: shippingInfo.ShippingCompanyID,
		ReceiverName:      shippingInfo.ReceiverName,
		ReceiverPhone:     shippingInfo.ReceiverPhone,
		ReceiverAddress:   shippingInfo.ReceiverAddress,
		Status:            model.ShippingStatusShipped,
		ShippedAt:         &now,
		EstimatedDays:     shippingInfo.EstimatedDays,
		Weight:            shippingInfo.Weight,
		Volume:            shippingInfo.Volume,
		ShippingFee:       shippingInfo.ShippingFee,
		InsuranceFee:      shippingInfo.InsuranceFee,
		Remark:            shippingInfo.Remark,
	}

	// 如果有模板，使用模板的发件人信息
	if template != nil {
		shipping.SenderName = template.SenderName
		shipping.SenderPhone = template.SenderPhone
		shipping.SenderAddress = template.SenderAddress
	}

	// 创建物流信息
	if err := s.shippingDAO.CreateShipping(ctx, shipping); err != nil {
		return fmt.Errorf("创建物流信息失败: %v", err)
	}

	// 添加发货跟踪记录
	track := &model.ShippingTrack{
		ShippingID:  shipping.ID,
		Status:      "已发货",
		Location:    shipping.SenderAddress,
		Description: fmt.Sprintf("商品已从%s发出，快递单号：%s", shipping.SenderAddress, shipping.TrackingNumber),
		Operator:    "系统",
		TrackTime:   now,
	}
	s.shippingDAO.CreateShippingTrack(ctx, track)

	// 更新订单状态为已发货
	order.Status = 3 // 假设3是已发货状态
	return s.orderDAO.UpdateOrder(ctx, order)
}

// UpdateShippingStatus 更新物流状态
func (s *shippingService) UpdateShippingStatus(ctx context.Context, id int64, status model.ShippingStatus, remark string) error {
	shipping, err := s.shippingDAO.GetShippingByID(ctx, id)
	if err != nil {
		return fmt.Errorf("物流信息不存在: %v", err)
	}

	shipping.Status = status
	shipping.UpdatedAt = time.Now()

	// 如果是已送达状态，设置送达时间
	if status == model.ShippingStatusDelivered {
		now := time.Now()
		shipping.DeliveredAt = &now

		// 更新订单状态为已送达
		if order, err := s.orderDAO.FindOrderByID(ctx, shipping.OrderID); err == nil && order != nil {
			order.Status = 4 // 假设4是已送达状态
			s.orderDAO.UpdateOrder(ctx, order)
		}
	}

	// 添加跟踪记录
	track := &model.ShippingTrack{
		ShippingID:  id,
		Status:      s.getStatusDescription(status),
		Location:    "",
		Description: remark,
		Operator:    "系统",
		TrackTime:   time.Now(),
	}
	s.shippingDAO.CreateShippingTrack(ctx, track)

	return s.shippingDAO.UpdateShipping(ctx, shipping)
}

// AddTrackingRecord 添加跟踪记录
func (s *shippingService) AddTrackingRecord(ctx context.Context, shippingID int64, track TrackingRecord) error {
	// 检查物流信息是否存在
	_, err := s.shippingDAO.GetShippingByID(ctx, shippingID)
	if err != nil {
		return fmt.Errorf("物流信息不存在: %v", err)
	}

	trackRecord := &model.ShippingTrack{
		ShippingID:  shippingID,
		Status:      track.Status,
		Location:    track.Location,
		Description: track.Description,
		Operator:    track.Operator,
		TrackTime:   track.TrackTime,
	}

	return s.shippingDAO.CreateShippingTrack(ctx, trackRecord)
}

// GetTrackingHistory 获取跟踪历史
func (s *shippingService) GetTrackingHistory(ctx context.Context, shippingID int64) ([]*model.ShippingTrack, error) {
	return s.shippingDAO.ListShippingTracks(ctx, shippingID)
}

// SyncTrackingInfo 同步跟踪信息（从第三方API）
func (s *shippingService) SyncTrackingInfo(ctx context.Context, trackingNumber string) error {
	// 这里应该调用第三方物流API获取最新的跟踪信息
	// 为了简化，这里只是一个占位实现
	return nil
}

// getStatusDescription 获取状态描述
func (s *shippingService) getStatusDescription(status model.ShippingStatus) string {
	switch status {
	case model.ShippingStatusPending:
		return "待发货"
	case model.ShippingStatusShipped:
		return "已发货"
	case model.ShippingStatusInTransit:
		return "运输中"
	case model.ShippingStatusDelivered:
		return "已送达"
	case model.ShippingStatusException:
		return "异常"
	case model.ShippingStatusReturned:
		return "已退回"
	default:
		return "未知状态"
	}
}

// 物流公司管理方法
func (s *shippingService) CreateShippingCompany(ctx context.Context, company *model.ShippingCompany) error {
	if company.Name == "" {
		return errors.New("公司名称不能为空")
	}
	if company.Code == "" {
		return errors.New("公司代码不能为空")
	}
	return s.shippingDAO.CreateShippingCompany(ctx, company)
}

func (s *shippingService) UpdateShippingCompany(ctx context.Context, company *model.ShippingCompany) error {
	if company.ID == 0 {
		return errors.New("公司ID不能为空")
	}
	return s.shippingDAO.UpdateShippingCompany(ctx, company)
}

func (s *shippingService) GetShippingCompanyByID(ctx context.Context, id int64) (*model.ShippingCompany, error) {
	return s.shippingDAO.GetShippingCompanyByID(ctx, id)
}

func (s *shippingService) ListShippingCompanies(ctx context.Context, status *int8) ([]*model.ShippingCompany, error) {
	return s.shippingDAO.ListShippingCompanies(ctx, status)
}

func (s *shippingService) DeleteShippingCompany(ctx context.Context, id int64) error {
	return s.shippingDAO.DeleteShippingCompany(ctx, id)
}

// 物流模板管理方法
func (s *shippingService) CreateShippingTemplate(ctx context.Context, template *model.ShippingTemplate) error {
	if template.Name == "" {
		return errors.New("模板名称不能为空")
	}
	return s.shippingDAO.CreateShippingTemplate(ctx, template)
}

func (s *shippingService) UpdateShippingTemplate(ctx context.Context, template *model.ShippingTemplate) error {
	if template.ID == 0 {
		return errors.New("模板ID不能为空")
	}
	return s.shippingDAO.UpdateShippingTemplate(ctx, template)
}

func (s *shippingService) GetShippingTemplateByID(ctx context.Context, id int64) (*model.ShippingTemplate, error) {
	return s.shippingDAO.GetShippingTemplateByID(ctx, id)
}

func (s *shippingService) ListShippingTemplates(ctx context.Context, status *int8) ([]*model.ShippingTemplate, error) {
	return s.shippingDAO.ListShippingTemplates(ctx, status)
}

func (s *shippingService) GetDefaultTemplate(ctx context.Context) (*model.ShippingTemplate, error) {
	return s.shippingDAO.GetDefaultTemplate(ctx)
}

func (s *shippingService) DeleteShippingTemplate(ctx context.Context, id int64) error {
	return s.shippingDAO.DeleteShippingTemplate(ctx, id)
}

// GetShippingStatistics 获取物流统计
func (s *shippingService) GetShippingStatistics(ctx context.Context) (*ShippingStatistics, error) {
	// 这里应该实现具体的统计逻辑
	// 为了简化，返回模拟数据
	return &ShippingStatistics{
		TotalShippings:     100,
		PendingShippings:   10,
		InTransitShippings: 30,
		DeliveredShippings: 55,
		ExceptionShippings: 5,
		StatusDistribution: map[model.ShippingStatus]int64{
			model.ShippingStatusPending:   10,
			model.ShippingStatusShipped:   5,
			model.ShippingStatusInTransit: 30,
			model.ShippingStatusDelivered: 55,
		},
		CompanyDistribution: map[string]int64{
			"顺丰快递": 40,
			"圆通快递": 30,
			"中通快递": 20,
			"韵达快递": 10,
		},
		AvgDeliveryDays: 2.5,
		OnTimeRate:      95.5,
	}, nil
}
