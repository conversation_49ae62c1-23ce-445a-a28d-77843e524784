package service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrProductNotFound  = errors.New("商品不存在")
	ErrCategoryNotFound = errors.New("分类不存在")
	ErrInvalidOperation = errors.New("无效的操作")
)

// ProductService 商品服务接口
type ProductService interface {
	// 商品管理
	CreateProduct(ctx context.Context, name string, categoryID int64, description string, qualityLevel int8, origin string, supplierID int64) (*model.Product, error)
	UpdateProduct(ctx context.Context, id int64, name string, categoryID int64, description string, qualityLevel int8, origin string) error
	DeleteProduct(ctx context.Context, id int64) error
	GetProduct(ctx context.Context, id int64) (*model.ProductWithCategory, error)
	ListProducts(ctx context.Context, categoryID int64, page, size int) ([]*model.ProductWithCategory, int64, error)
	SearchProducts(ctx context.Context, params interface{}) ([]*model.ProductWithCategory, int64, error)
	UpdateProductStatus(ctx context.Context, id int64, status int8) error
	AuditProduct(ctx context.Context, id int64, status string, reason string) error
	GetAuditHistory(ctx context.Context, productID int64) ([]*model.ProductAuditHistory, error)

	// 分类管理
	CreateCategory(ctx context.Context, name string, description string, parentID *int64, level int8, sortOrder int) (*model.Category, error)
	UpdateCategory(ctx context.Context, id int64, name string, description string, sortOrder int) error
	DeleteCategory(ctx context.Context, id int64) error
	GetCategoryTree(ctx context.Context) ([]model.CategoryTree, error)

	// 统计方法
	CountProductsByStatus(ctx context.Context, status int8) (int64, error)
	CountTodayNewProducts(ctx context.Context) (int64, error)
	GetCategoryDistribution(ctx context.Context) (map[string]int64, error)
	GetQualityDistribution(ctx context.Context) (map[string]int64, error)
}

// productService 商品服务实现
type productService struct {
	productDAO dao.ProductDAO
}

// NewProductService 创建商品服务实例
func NewProductService() ProductService {
	return &productService{
		productDAO: dao.NewProductDAO(),
	}
}

// CreateProduct 创建商品
func (s *productService) CreateProduct(ctx context.Context, name string, categoryID int64, description string, qualityLevel int8, origin string, supplierID int64) (*model.Product, error) {
	// 检查分类是否存在
	category, err := s.productDAO.FindCategoryByID(ctx, categoryID)
	if err != nil {
		return nil, err
	}
	if category == nil {
		return nil, ErrCategoryNotFound
	}

	product := &model.Product{
		Name:         name,
		CategoryID:   categoryID,
		Description:  description,
		QualityLevel: qualityLevel,
		Origin:       origin,
		SupplierID:   supplierID,
		Status:       0, // 默认下架
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.productDAO.Create(ctx, product); err != nil {
		return nil, err
	}

	return product, nil
}

// UpdateProduct 更新商品
func (s *productService) UpdateProduct(ctx context.Context, id int64, name string, categoryID int64, description string, qualityLevel int8, origin string) error {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if product == nil {
		return ErrProductNotFound
	}

	// 检查分类是否存在
	category, err := s.productDAO.FindCategoryByID(ctx, categoryID)
	if err != nil {
		return err
	}
	if category == nil {
		return ErrCategoryNotFound
	}

	// 更新商品信息
	product.Name = name
	product.CategoryID = categoryID
	product.Description = description
	product.QualityLevel = qualityLevel
	product.Origin = origin
	product.UpdatedAt = time.Now()

	return s.productDAO.Update(ctx, product)
}

// DeleteProduct 删除商品
func (s *productService) DeleteProduct(ctx context.Context, id int64) error {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if product == nil {
		return ErrProductNotFound
	}

	// 这里可以添加其他业务逻辑检查，比如：
	// - 检查商品是否在拍卖中
	// - 检查商品是否有未完成的订单等

	return s.productDAO.Delete(ctx, id)
}

// GetProduct 获取商品信息
func (s *productService) GetProduct(ctx context.Context, id int64) (*model.ProductWithCategory, error) {
	product, err := s.productDAO.FindWithCategory(ctx, id)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, ErrProductNotFound
	}
	return product, nil
}

// ListProducts 分页查询商品列表
func (s *productService) ListProducts(ctx context.Context, categoryID int64, page, size int) ([]*model.ProductWithCategory, int64, error) {
	offset := (page - 1) * size

	var products []*model.ProductWithCategory
	var total int64
	var err error

	if categoryID > 0 {
		products, err = s.productDAO.ListByCategoryID(ctx, categoryID, offset, size)
		if err != nil {
			return nil, 0, err
		}
		total, err = s.productDAO.CountByCategory(ctx, categoryID)
	} else {
		// 获取所有商品（包含分类和供应商信息）
		products, err = s.productDAO.ListWithCategoryAndSupplier(ctx, offset, size)
		if err != nil {
			return nil, 0, err
		}

		total, err = s.productDAO.Count(ctx)
	}

	if err != nil {
		return nil, 0, err
	}

	return products, total, nil
}

// UpdateProductStatus 更新商品状态
func (s *productService) UpdateProductStatus(ctx context.Context, id int64, status int8) error {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if product == nil {
		return ErrProductNotFound
	}

	// 更新状态
	product.Status = status
	product.UpdatedAt = time.Now()
	return s.productDAO.Update(ctx, product)
}

// CreateCategory 创建分类
func (s *productService) CreateCategory(ctx context.Context, name string, description string, parentID *int64, level int8, sortOrder int) (*model.Category, error) {
	// 如果有父分类，检查父分类是否存在
	if parentID != nil {
		parent, err := s.productDAO.FindCategoryByID(ctx, *parentID)
		if err != nil {
			return nil, err
		}
		if parent == nil {
			return nil, ErrCategoryNotFound
		}
	}

	// 如果没有提供排序值，使用默认值
	if sortOrder == 0 {
		sortOrder = 1
	}

	// 自动生成分类编码
	code := s.generateCategoryCode(ctx, name, parentID, level)

	category := &model.Category{
		Name:        name,
		Code:        code,
		Description: description,
		ParentID:    parentID,
		Level:       level,
		SortOrder:   sortOrder,
		Status:      1, // 默认启用
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.productDAO.CreateCategory(ctx, category); err != nil {
		return nil, err
	}

	return category, nil
}

// generateCategoryCode 自动生成分类编码
func (s *productService) generateCategoryCode(ctx context.Context, name string, parentID *int64, level int8) string {
	// 简化处理：使用级别和时间戳确保唯一性
	timestamp := time.Now().Unix()

	// 如果是英文，尝试使用名称
	if !containsChinese(name) {
		code := strings.ToUpper(strings.ReplaceAll(name, " ", "_"))
		// 限制长度并添加时间戳后缀
		if len(code) > 10 {
			code = code[:10]
		}
		return fmt.Sprintf("%s_%d", code, timestamp%10000)
	}

	// 中文或其他情况，使用级别和时间戳
	return fmt.Sprintf("CAT_%d_%d", level, timestamp%100000)
}

// containsChinese 检查字符串是否包含中文
func containsChinese(str string) bool {
	for _, r := range str {
		if r >= 0x4e00 && r <= 0x9fff {
			return true
		}
	}
	return false
}

// UpdateCategory 更新分类
func (s *productService) UpdateCategory(ctx context.Context, id int64, name string, description string, sortOrder int) error {
	// 检查分类是否存在
	category, err := s.productDAO.FindCategoryByID(ctx, id)
	if err != nil {
		return err
	}
	if category == nil {
		return ErrCategoryNotFound
	}

	// 更新分类信息
	category.Name = name
	category.Description = description
	category.SortOrder = sortOrder
	category.UpdatedAt = time.Now()

	return s.productDAO.UpdateCategory(ctx, category)
}

// DeleteCategory 删除分类
func (s *productService) DeleteCategory(ctx context.Context, id int64) error {
	// 检查分类是否存在
	category, err := s.productDAO.FindCategoryByID(ctx, id)
	if err != nil {
		return err
	}
	if category == nil {
		return ErrCategoryNotFound
	}

	// 检查是否有子分类
	children, err := s.productDAO.FindCategoriesByParentID(ctx, id)
	if err != nil {
		return err
	}
	if len(children) > 0 {
		return ErrInvalidOperation
	}

	// 检查是否有关联的商品
	count, err := s.productDAO.CountByCategory(ctx, id)
	if err != nil {
		return err
	}
	if count > 0 {
		return ErrInvalidOperation
	}

	return s.productDAO.DeleteCategory(ctx, id)
}

// GetCategoryTree 获取分类树
func (s *productService) GetCategoryTree(ctx context.Context) ([]model.CategoryTree, error) {
	categories, err := s.productDAO.ListAllCategories(ctx)
	if err != nil {
		return nil, err
	}

	// 构建分类树
	categoryMap := make(map[int64]*model.CategoryTree)
	var rootPointers []*model.CategoryTree

	// 第一遍遍历，创建所有节点
	for _, category := range categories {
		tree := &model.CategoryTree{
			Category: *category,
			Children: make([]model.CategoryTree, 0),
		}
		categoryMap[category.ID] = tree

		// 如果是根节点，记录指针
		if category.ParentID == nil {
			rootPointers = append(rootPointers, tree)
		}
	}

	// 第二遍遍历，建立父子关系
	for _, category := range categories {
		if category.ParentID != nil {
			if parent, ok := categoryMap[*category.ParentID]; ok {
				if child, ok := categoryMap[category.ID]; ok {
					parent.Children = append(parent.Children, *child)
				}
			}
		}
	}

	// 转换为值类型返回
	roots := make([]model.CategoryTree, len(rootPointers))
	for i, rootPtr := range rootPointers {
		roots[i] = *rootPtr
	}

	return roots, nil
}

// ProductSearchParams 商品搜索参数
type ProductSearchParams struct {
	Name         string
	CategoryID   int64
	QualityLevel int8
	Status       *int8
	Origin       string
	Page         int
	PageSize     int
}

// SearchProducts 搜索商品（支持多条件）
func (s *productService) SearchProducts(ctx context.Context, params interface{}) ([]*model.ProductWithCategory, int64, error) {
	// 类型断言，将interface{}转换为具体的参数类型
	searchParams, ok := params.(struct {
		Name         string
		CategoryID   int64
		QualityLevel int8
		Status       *int8
		Origin       string
		AuditStatus  string
		SupplierName string
		Page         int
		PageSize     int
	})

	if !ok {
		// 如果类型断言失败，使用默认参数
		return s.ListProducts(ctx, 0, 0, 10)
	}

	// 设置默认分页参数
	page := searchParams.Page
	pageSize := searchParams.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 调用DAO层的搜索方法
	products, err := s.productDAO.SearchProducts(ctx, dao.ProductSearchFilter{
		Name:         searchParams.Name,
		CategoryID:   searchParams.CategoryID,
		QualityLevel: searchParams.QualityLevel,
		Status:       searchParams.Status,
		Origin:       searchParams.Origin,
		AuditStatus:  searchParams.AuditStatus,
		SupplierName: searchParams.SupplierName,
		Offset:       offset,
		Limit:        pageSize,
	})
	if err != nil {
		return nil, 0, err
	}

	// 获取搜索结果总数
	total, err := s.productDAO.CountSearchProducts(ctx, dao.ProductSearchFilter{
		Name:         searchParams.Name,
		CategoryID:   searchParams.CategoryID,
		QualityLevel: searchParams.QualityLevel,
		Status:       searchParams.Status,
		Origin:       searchParams.Origin,
		AuditStatus:  searchParams.AuditStatus,
		SupplierName: searchParams.SupplierName,
	})
	if err != nil {
		return nil, 0, err
	}

	// 转换为ProductWithCategory
	result := make([]*model.ProductWithCategory, len(products))
	for i, p := range products {
		// 获取分类信息
		var category model.Category
		if p.CategoryID > 0 {
			if cat, err := s.productDAO.FindCategoryByID(ctx, p.CategoryID); err == nil && cat != nil {
				category = *cat
			}
		}

		result[i] = &model.ProductWithCategory{
			Product:  *p,
			Category: category,
		}
	}

	return result, total, nil
}

// AuditProduct 审核商品
func (s *productService) AuditProduct(ctx context.Context, id int64, status string, reason string) error {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if product == nil {
		return ErrProductNotFound
	}

	// 验证审核状态
	if status != "approved" && status != "rejected" {
		return errors.New("无效的审核状态")
	}

	// 更新审核信息
	now := time.Now()
	oldStatus := product.AuditStatus
	product.AuditStatus = status
	product.AuditReason = reason
	product.AuditTime = &now
	// TODO: 从上下文中获取审核员ID
	// product.AuditorID = &auditorID
	product.UpdatedAt = now

	// 开始事务
	tx := s.productDAO.BeginTx(ctx)
	if tx == nil {
		return errors.New("开始事务失败")
	}
	defer tx.Rollback()

	// 更新商品审核信息
	if err := s.productDAO.UpdateWithTx(ctx, tx, product); err != nil {
		return err
	}

	// 记录审核历史（只有状态发生变化时才记录）
	if oldStatus != status {
		auditHistory := &model.ProductAuditHistory{
			ProductID:   id,
			AuditStatus: status,
			AuditReason: reason,
			AuditTime:   now,
			// TODO: 从上下文中获取审核员ID
			// AuditorID: &auditorID,
			CreatedAt: now,
		}
		if err := s.productDAO.CreateAuditHistory(ctx, tx, auditHistory); err != nil {
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// GetAuditHistory 获取商品审核历史
func (s *productService) GetAuditHistory(ctx context.Context, productID int64) ([]*model.ProductAuditHistory, error) {
	return s.productDAO.GetAuditHistory(ctx, productID)
}

// CountProductsByStatus 按状态统计商品数量
func (s *productService) CountProductsByStatus(ctx context.Context, status int8) (int64, error) {
	return s.productDAO.CountByStatus(ctx, status)
}

// CountTodayNewProducts 统计今日新增商品数量
func (s *productService) CountTodayNewProducts(ctx context.Context) (int64, error) {
	today := time.Now().Format("2006-01-02")
	return s.productDAO.CountByDateRange(ctx, today+" 00:00:00", today+" 23:59:59")
}

// GetCategoryDistribution 获取分类分布统计
func (s *productService) GetCategoryDistribution(ctx context.Context) (map[string]int64, error) {
	categories, err := s.productDAO.ListAllCategories(ctx)
	if err != nil {
		return nil, err
	}

	distribution := make(map[string]int64)
	for _, category := range categories {
		count, err := s.productDAO.CountByCategory(ctx, category.ID)
		if err != nil {
			continue // 忽略错误，继续处理其他分类
		}
		distribution[category.Name] = count
	}

	return distribution, nil
}

// GetQualityDistribution 获取质量等级分布统计
func (s *productService) GetQualityDistribution(ctx context.Context) (map[string]int64, error) {
	distribution := make(map[string]int64)

	// 统计各个质量等级的商品数量
	for level := int8(1); level <= 5; level++ {
		count, err := s.productDAO.CountByQualityLevel(ctx, level)
		if err != nil {
			continue // 忽略错误，继续处理其他等级
		}
		distribution[fmt.Sprintf("%d", level)] = count
	}

	return distribution, nil
}
