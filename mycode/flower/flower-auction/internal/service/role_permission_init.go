package service

import (
	"context"
	"fmt"
)

// RolePermissionInitService 角色权限初始化服务
type RolePermissionInitService interface {
	InitDefaultRolePermissions(ctx context.Context) error
}

type rolePermissionInitService struct {
	roleService       RoleService
	permissionService PermissionService
}

// NewRolePermissionInitService 创建角色权限初始化服务
func NewRolePermissionInitService(roleService RoleService, permissionService PermissionService) RolePermissionInitService {
	return &rolePermissionInitService{
		roleService:       roleService,
		permissionService: permissionService,
	}
}

// InitDefaultRolePermissions 初始化默认角色权限
func (s *rolePermissionInitService) InitDefaultRolePermissions(ctx context.Context) error {
	// 定义角色权限映射
	rolePermissions := map[string][]string{
		// 系统管理员 - 拥有所有权限
		"admin": {
			// 用户管理
			"user:view", "user:create", "user:edit", "user:delete", "user:status",
			// 角色管理
			"role:view", "role:create", "role:edit", "role:delete", "role:permission",
			// 权限管理
			"permission:view", "permission:create", "permission:edit", "permission:delete",
			// 商品管理
			"product:view", "product:create", "product:edit", "product:delete", "product:audit",
			// 分类管理
			"category:view", "category:create", "category:edit", "category:delete",
			// 拍卖管理
			"auction:view", "auction:create", "auction:edit", "auction:delete",
			"auction:publish", "auction:cancel", "auction:conduct", "auction:control",
			"auction:clock", "auction:bid", "auction:confirm_bid",
			// 订单管理
			"order:view", "order:process", "order:cancel", "order:refund", "order:ship",
			// 财务管理
			"finance:view", "finance:audit", "finance:commission", "finance:settlement", "finance:report",
			// 报表统计
			"report:sales", "report:user", "report:auction", "report:finance", "report:export",
			// 系统管理
			"system:config", "system:log", "system:monitor", "system:backup",
		},

		// 拍卖师 - 拍卖相关权限
		"auctioneer": {
			// 基础查看权限
			"user:view", "product:view", "category:view",
			// 拍卖管理权限
			"auction:view", "auction:create", "auction:edit", "auction:conduct",
			"auction:control", "auction:clock", "auction:confirm_bid",
			// 订单查看权限
			"order:view",
			// 基础报表权限
			"report:auction", "report:sales",
		},

		// 购买商 - 竞拍相关权限
		"buyer": {
			// 基础查看权限
			"product:view", "category:view", "auction:view",
			// 竞拍权限
			"auction:bid",
			// 订单查看权限
			"order:view",
		},

		// 财务人员 - 财务相关权限
		"finance": {
			// 基础查看权限
			"user:view", "order:view", "auction:view",
			// 财务管理权限
			"finance:view", "finance:audit", "finance:commission", "finance:settlement", "finance:report",
			// 订单财务处理
			"order:process", "order:refund",
			// 财务报表权限
			"report:finance", "report:sales", "report:export",
		},

		// 商品管理员 - 商品相关权限
		"product_manager": {
			// 商品管理权限
			"product:view", "product:create", "product:edit", "product:delete", "product:audit",
			// 分类管理权限
			"category:view", "category:create", "category:edit", "category:delete",
			// 拍卖查看权限
			"auction:view",
			// 基础报表权限
			"report:sales", "report:auction",
		},

		// 客服人员 - 客服相关权限
		"customer_service": {
			// 基础查看权限
			"user:view", "product:view", "category:view", "auction:view",
			// 订单处理权限
			"order:view", "order:process", "order:cancel", "order:ship",
			// 用户服务权限
			"user:edit", // 可以编辑用户基本信息
		},

		// 运营人员 - 运营相关权限
		"operator": {
			// 基础查看权限
			"user:view", "product:view", "category:view",
			// 拍卖运营权限
			"auction:view", "auction:create", "auction:edit", "auction:publish", "auction:cancel",
			// 商品运营权限
			"product:view", "product:edit", "product:audit",
			// 报表查看权限
			"report:sales", "report:user", "report:auction", "report:export",
		},

		// 投屏端 - 只读权限
		"display": {
			// 基础查看权限
			"auction:view", "product:view",
		},
	}

	// 为每个角色分配权限
	for roleCode, permissionCodes := range rolePermissions {
		// 查找角色
		params := RoleQueryParams{
			Page:     1,
			PageSize: 100,
		}
		roles, _, err := s.roleService.ListRoles(ctx, params)
		if err != nil {
			return fmt.Errorf("查找角色失败: %v", err)
		}

		var roleID int64
		found := false
		for _, role := range roles {
			if role.Code == roleCode {
				roleID = role.ID
				found = true
				break
			}
		}

		if !found {
			fmt.Printf("角色 %s 不存在，跳过权限分配\n", roleCode)
			continue
		}

		// 获取权限ID列表
		var permissionIDs []int64
		for _, permCode := range permissionCodes {
			permissions, _, err := s.permissionService.ListPermissions(ctx, "", 0, 1000)
			if err != nil {
				return fmt.Errorf("查找权限失败: %v", err)
			}

			for _, perm := range permissions {
				if perm.Code == permCode {
					permissionIDs = append(permissionIDs, perm.ID)
					break
				}
			}
		}

		// 分配权限给角色
		if len(permissionIDs) > 0 {
			err = s.permissionService.AssignPermissionsToRole(ctx, roleID, permissionIDs)
			if err != nil {
				fmt.Printf("为角色 %s 分配权限失败: %v\n", roleCode, err)
				continue
			}
			fmt.Printf("成功为角色 %s 分配了 %d 个权限\n", roleCode, len(permissionIDs))
		}
	}

	return nil
}
