package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/spf13/viper"
)

// PaymentService 支付服务接口
type PaymentService interface {
	// 支付宝支付
	CreateAlipayOrder(ctx context.Context, orderID int64, amount float64, subject string) (*model.PaymentOrder, error)
	AlipayNotify(ctx context.Context, params map[string]string) error

	// 微信支付
	CreateWechatOrder(ctx context.Context, orderID int64, amount float64, subject string) (*model.PaymentOrder, error)
	WechatNotify(ctx context.Context, params map[string]string) error

	// 银行卡支付
	CreateBankOrder(ctx context.Context, orderID int64, amount float64, bankCard string) (*model.PaymentOrder, error)

	// 通用方法
	QueryPaymentStatus(ctx context.Context, paymentNo string) (*model.PaymentStatus, error)
	RefundPayment(ctx context.Context, paymentNo string, refundAmount float64, reason string) (*model.RefundOrder, error)

	// 支付记录管理
	ListPaymentRecords(ctx context.Context, query *model.PaymentRecordQuery) ([]*model.PaymentRecord, int64, error)
	GetPaymentRecord(ctx context.Context, id int64) (*model.PaymentRecord, error)
	UpdatePaymentRecord(ctx context.Context, id int64, updates *model.PaymentRecordUpdate) error
	DeletePaymentRecord(ctx context.Context, id int64) error
}

// paymentService 支付服务实现
type paymentService struct {
	orderDAO dao.OrderDAO
}

// NewPaymentService 创建支付服务实例
func NewPaymentService() PaymentService {
	return &paymentService{
		orderDAO: dao.NewOrderDAO(),
	}
}

// CreateAlipayOrder 创建支付宝支付订单
func (s *paymentService) CreateAlipayOrder(ctx context.Context, orderID int64, amount float64, subject string) (*model.PaymentOrder, error) {
	// 查询订单
	order, err := s.orderDAO.FindOrderByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	// 检查订单状态
	if order.Status != 0 { // 0: 待支付
		return nil, ErrInvalidOperation
	}

	// 检查金额
	if amount != order.Amount {
		return nil, ErrInvalidAmount
	}

	// 生成支付订单号
	paymentNo := generatePaymentNo("ALIPAY")

	// 创建支付记录
	payment := &model.Payment{
		OrderID:       orderID,
		PaymentNo:     paymentNo,
		Amount:        amount,
		PaymentMethod: 1, // 1: 支付宝
		Status:        0, // 0: 待支付
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.orderDAO.CreatePayment(ctx, payment); err != nil {
		return nil, err
	}

	// 构建支付宝支付参数
	params := buildAlipayParams(paymentNo, amount, subject)

	return &model.PaymentOrder{
		PaymentNo:     paymentNo,
		OrderID:       orderID,
		Amount:        amount,
		PaymentMethod: 1,
		PaymentURL:    buildAlipayURL(params),
		QRCode:        generateQRCode(buildAlipayURL(params)),
		ExpireTime:    time.Now().Add(30 * time.Minute), // 30分钟过期
	}, nil
}

// CreateWechatOrder 创建微信支付订单
func (s *paymentService) CreateWechatOrder(ctx context.Context, orderID int64, amount float64, subject string) (*model.PaymentOrder, error) {
	// 查询订单
	order, err := s.orderDAO.FindOrderByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	// 检查订单状态
	if order.Status != 0 { // 0: 待支付
		return nil, ErrInvalidOperation
	}

	// 检查金额
	if amount != order.Amount {
		return nil, ErrInvalidAmount
	}

	// 生成支付订单号
	paymentNo := generatePaymentNo("WECHAT")

	// 创建支付记录
	payment := &model.Payment{
		OrderID:       orderID,
		PaymentNo:     paymentNo,
		Amount:        amount,
		PaymentMethod: 2, // 2: 微信支付
		Status:        0, // 0: 待支付
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.orderDAO.CreatePayment(ctx, payment); err != nil {
		return nil, err
	}

	// 构建微信支付参数
	params := buildWechatParams(paymentNo, amount, subject)

	return &model.PaymentOrder{
		PaymentNo:     paymentNo,
		OrderID:       orderID,
		Amount:        amount,
		PaymentMethod: 2,
		PaymentURL:    buildWechatURL(params),
		QRCode:        generateQRCode(buildWechatURL(params)),
		ExpireTime:    time.Now().Add(30 * time.Minute), // 30分钟过期
	}, nil
}

// CreateBankOrder 创建银行卡支付订单
func (s *paymentService) CreateBankOrder(ctx context.Context, orderID int64, amount float64, bankCard string) (*model.PaymentOrder, error) {
	// 查询订单
	order, err := s.orderDAO.FindOrderByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	// 检查订单状态
	if order.Status != 0 { // 0: 待支付
		return nil, ErrInvalidOperation
	}

	// 检查金额
	if amount != order.Amount {
		return nil, ErrInvalidAmount
	}

	// 验证银行卡号
	if !isValidBankCard(bankCard) {
		return nil, errors.New("银行卡号格式不正确")
	}

	// 生成支付订单号
	paymentNo := generatePaymentNo("BANK")

	// 创建支付记录
	payment := &model.Payment{
		OrderID:       orderID,
		PaymentNo:     paymentNo,
		Amount:        amount,
		PaymentMethod: 3, // 3: 银行卡
		Status:        0, // 0: 待支付
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.orderDAO.CreatePayment(ctx, payment); err != nil {
		return nil, err
	}

	return &model.PaymentOrder{
		PaymentNo:     paymentNo,
		OrderID:       orderID,
		Amount:        amount,
		PaymentMethod: 3,
		BankCard:      maskBankCard(bankCard),
		ExpireTime:    time.Now().Add(24 * time.Hour), // 24小时过期
	}, nil
}

// AlipayNotify 处理支付宝支付回调
func (s *paymentService) AlipayNotify(ctx context.Context, params map[string]string) error {
	// 验证签名
	if !verifyAlipaySign(params) {
		return errors.New("支付宝签名验证失败")
	}

	paymentNo := params["out_trade_no"]
	tradeStatus := params["trade_status"]

	// 查询支付记录
	payment, err := s.orderDAO.FindPaymentByNo(ctx, paymentNo)
	if err != nil {
		return err
	}
	if payment == nil {
		return ErrPaymentNotFound
	}

	// 更新支付状态
	var status int8
	switch tradeStatus {
	case "TRADE_SUCCESS", "TRADE_FINISHED":
		status = 1 // 支付成功
	case "TRADE_CLOSED":
		status = 2 // 支付失败
	default:
		return nil // 忽略其他状态
	}

	// 更新支付记录
	payment.Status = status
	payment.UpdatedAt = time.Now()
	if err := s.orderDAO.UpdatePayment(ctx, payment); err != nil {
		return err
	}

	// 如果支付成功，更新订单状态
	if status == 1 {
		order, err := s.orderDAO.FindOrderByID(ctx, payment.OrderID)
		if err != nil {
			return err
		}
		if order != nil {
			order.Status = 1 // 已支付
			order.UpdatedAt = time.Now()
			if err := s.orderDAO.UpdateOrder(ctx, order); err != nil {
				return err
			}
		}
	}

	return nil
}

// WechatNotify 处理微信支付回调
func (s *paymentService) WechatNotify(ctx context.Context, params map[string]string) error {
	// 验证签名
	if !verifyWechatSign(params) {
		return errors.New("微信支付签名验证失败")
	}

	paymentNo := params["out_trade_no"]
	resultCode := params["result_code"]

	// 查询支付记录
	payment, err := s.orderDAO.FindPaymentByNo(ctx, paymentNo)
	if err != nil {
		return err
	}
	if payment == nil {
		return ErrPaymentNotFound
	}

	// 更新支付状态
	var status int8
	if resultCode == "SUCCESS" {
		status = 1 // 支付成功
	} else {
		status = 2 // 支付失败
	}

	// 更新支付记录
	payment.Status = status
	payment.UpdatedAt = time.Now()
	if err := s.orderDAO.UpdatePayment(ctx, payment); err != nil {
		return err
	}

	// 如果支付成功，更新订单状态
	if status == 1 {
		order, err := s.orderDAO.FindOrderByID(ctx, payment.OrderID)
		if err != nil {
			return err
		}
		if order != nil {
			order.Status = 1 // 已支付
			order.UpdatedAt = time.Now()
			if err := s.orderDAO.UpdateOrder(ctx, order); err != nil {
				return err
			}
		}
	}

	return nil
}

// QueryPaymentStatus 查询支付状态
func (s *paymentService) QueryPaymentStatus(ctx context.Context, paymentNo string) (*model.PaymentStatus, error) {
	payment, err := s.orderDAO.FindPaymentByNo(ctx, paymentNo)
	if err != nil {
		return nil, err
	}
	if payment == nil {
		return nil, ErrPaymentNotFound
	}

	return &model.PaymentStatus{
		PaymentNo:     payment.PaymentNo,
		OrderID:       payment.OrderID,
		Amount:        payment.Amount,
		PaymentMethod: payment.PaymentMethod,
		Status:        payment.Status,
		CreatedAt:     payment.CreatedAt,
		UpdatedAt:     payment.UpdatedAt,
	}, nil
}

// RefundPayment 退款
func (s *paymentService) RefundPayment(ctx context.Context, paymentNo string, refundAmount float64, reason string) (*model.RefundOrder, error) {
	// 查询支付记录
	payment, err := s.orderDAO.FindPaymentByNo(ctx, paymentNo)
	if err != nil {
		return nil, err
	}
	if payment == nil {
		return nil, ErrPaymentNotFound
	}

	// 检查支付状态
	if payment.Status != 1 {
		return nil, errors.New("只能对已支付的订单进行退款")
	}

	// 检查退款金额
	if refundAmount <= 0 || refundAmount > payment.Amount {
		return nil, errors.New("退款金额无效")
	}

	// 生成退款订单号
	refundNo := generateRefundNo()

	// 创建退款记录
	refund := &model.RefundOrder{
		RefundNo:  refundNo,
		PaymentNo: paymentNo,
		OrderID:   payment.OrderID,
		Amount:    refundAmount,
		Reason:    reason,
		Status:    0, // 0: 退款中
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// TODO: 调用第三方退款接口
	// 这里应该根据支付方式调用相应的退款接口

	// 模拟退款成功
	refund.Status = 1 // 1: 退款成功

	return refund, nil
}

// ListPaymentRecords 获取支付记录列表
func (s *paymentService) ListPaymentRecords(ctx context.Context, query *model.PaymentRecordQuery) ([]*model.PaymentRecord, int64, error) {
	// 计算偏移量
	offset := (query.Page - 1) * query.Size

	// 获取支付记录（这里简化实现，实际应该从数据库查询）
	payments, total, err := s.getPaymentsWithFilter(ctx, query, offset, query.Size)
	if err != nil {
		return nil, 0, err
	}

	// 转换为支付记录格式
	var records []*model.PaymentRecord
	for _, payment := range payments {
		record := &model.PaymentRecord{
			Payment: *payment,
		}

		// 获取关联的订单信息
		if order, err := s.orderDAO.FindOrderByID(ctx, payment.OrderID); err == nil && order != nil {
			record.OrderNo = order.OrderNo
			// 这里可以进一步获取用户名和商品名
		}

		records = append(records, record)
	}

	return records, total, nil
}

// GetPaymentRecord 获取支付记录详情
func (s *paymentService) GetPaymentRecord(ctx context.Context, id int64) (*model.PaymentRecord, error) {
	// 获取支付信息
	payment, err := s.getPaymentByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if payment == nil {
		return nil, ErrPaymentNotFound
	}

	record := &model.PaymentRecord{
		Payment: *payment,
	}

	// 获取关联的订单信息
	if order, err := s.orderDAO.FindOrderByID(ctx, payment.OrderID); err == nil && order != nil {
		record.OrderNo = order.OrderNo
		// 这里可以进一步获取用户名和商品名
	}

	return record, nil
}

// UpdatePaymentRecord 更新支付记录
func (s *paymentService) UpdatePaymentRecord(ctx context.Context, id int64, updates *model.PaymentRecordUpdate) error {
	// 获取现有支付记录
	payment, err := s.getPaymentByID(ctx, id)
	if err != nil {
		return err
	}
	if payment == nil {
		return ErrPaymentNotFound
	}

	// 更新字段
	if updates.Status != nil {
		payment.Status = *updates.Status
	}
	if updates.Amount != nil {
		payment.Amount = *updates.Amount
	}
	payment.UpdatedAt = time.Now()

	// 保存更新
	return s.updatePayment(ctx, payment)
}

// DeletePaymentRecord 删除支付记录
func (s *paymentService) DeletePaymentRecord(ctx context.Context, id int64) error {
	// 检查支付记录是否存在
	payment, err := s.getPaymentByID(ctx, id)
	if err != nil {
		return err
	}
	if payment == nil {
		return ErrPaymentNotFound
	}

	// 检查是否可以删除（例如，只能删除失败的支付记录）
	if payment.Status == 1 { // 已支付状态不能删除
		return errors.New("已支付的记录不能删除")
	}

	return s.deletePayment(ctx, id)
}

// getPaymentsWithFilter 根据条件获取支付记录
func (s *paymentService) getPaymentsWithFilter(ctx context.Context, query *model.PaymentRecordQuery, offset, limit int) ([]*model.Payment, int64, error) {
	// 这里简化实现，实际应该在DAO层实现复杂查询
	// 为了演示，我们返回模拟数据
	var payments []*model.Payment
	var total int64 = 0

	// 实际实现应该调用DAO层的查询方法
	// payments, total, err := s.paymentDAO.ListWithFilter(ctx, query, offset, limit)

	return payments, total, nil
}

// getPaymentByID 根据ID获取支付记录
func (s *paymentService) getPaymentByID(ctx context.Context, id int64) (*model.Payment, error) {
	// 这里简化实现，实际应该从数据库查询
	// 为了演示，我们返回模拟数据
	return nil, ErrPaymentNotFound
}

// updatePayment 更新支付记录
func (s *paymentService) updatePayment(ctx context.Context, payment *model.Payment) error {
	// 这里简化实现，实际应该调用DAO层更新方法
	return nil
}

// deletePayment 删除支付记录
func (s *paymentService) deletePayment(ctx context.Context, id int64) error {
	// 这里简化实现，实际应该调用DAO层删除方法
	return nil
}

// 辅助函数

// generatePaymentNo 生成支付订单号
func generatePaymentNo(prefix string) string {
	return fmt.Sprintf("%s%s%06d", prefix, time.Now().Format("20060102150405"), time.Now().Nanosecond()%1000000)
}

// generateRefundNo 生成退款订单号
func generateRefundNo() string {
	return fmt.Sprintf("REFUND%s%06d", time.Now().Format("20060102150405"), time.Now().Nanosecond()%1000000)
}

// buildAlipayParams 构建支付宝支付参数
func buildAlipayParams(paymentNo string, amount float64, subject string) map[string]string {
	params := map[string]string{
		"app_id":       viper.GetString("alipay.app_id"),
		"method":       "alipay.trade.page.pay",
		"charset":      "utf-8",
		"sign_type":    "RSA2",
		"timestamp":    time.Now().Format("2006-01-02 15:04:05"),
		"version":      "1.0",
		"notify_url":   viper.GetString("alipay.notify_url"),
		"return_url":   viper.GetString("alipay.return_url"),
		"out_trade_no": paymentNo,
		"total_amount": fmt.Sprintf("%.2f", amount),
		"subject":      subject,
		"product_code": "FAST_INSTANT_TRADE_PAY",
	}

	// 生成签名
	params["sign"] = generateAlipaySign(params)
	return params
}

// buildWechatParams 构建微信支付参数
func buildWechatParams(paymentNo string, amount float64, subject string) map[string]string {
	params := map[string]string{
		"appid":            viper.GetString("wechat.app_id"),
		"mch_id":           viper.GetString("wechat.mch_id"),
		"nonce_str":        generateNonceStr(),
		"body":             subject,
		"out_trade_no":     paymentNo,
		"total_fee":        fmt.Sprintf("%.0f", amount*100), // 微信支付金额单位为分
		"spbill_create_ip": "127.0.0.1",
		"notify_url":       viper.GetString("wechat.notify_url"),
		"trade_type":       "NATIVE",
	}

	// 生成签名
	params["sign"] = generateWechatSign(params)
	return params
}

// buildAlipayURL 构建支付宝支付URL
func buildAlipayURL(params map[string]string) string {
	gateway := viper.GetString("alipay.gateway")
	var paramStrs []string
	for k, v := range params {
		paramStrs = append(paramStrs, fmt.Sprintf("%s=%s", k, v))
	}
	return fmt.Sprintf("%s?%s", gateway, strings.Join(paramStrs, "&"))
}

// buildWechatURL 构建微信支付URL
func buildWechatURL(params map[string]string) string {
	// 微信支付需要调用统一下单接口获取二维码链接
	// 这里简化处理，实际应该调用微信API
	return "weixin://wxpay/bizpayurl?pr=" + params["out_trade_no"]
}

// generateQRCode 生成二维码
func generateQRCode(url string) string {
	// 这里应该调用二维码生成库
	// 简化处理，返回base64编码的二维码图片
	return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
}

// generateAlipaySign 生成支付宝签名
func generateAlipaySign(params map[string]string) string {
	// 排序参数
	var keys []string
	for k := range params {
		if k != "sign" && k != "sign_type" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 拼接参数
	var paramStrs []string
	for _, k := range keys {
		if params[k] != "" {
			paramStrs = append(paramStrs, fmt.Sprintf("%s=%s", k, params[k]))
		}
	}
	signStr := strings.Join(paramStrs, "&")

	// 生成MD5签名（实际应该使用RSA2）
	hash := md5.Sum([]byte(signStr + viper.GetString("alipay.private_key")))
	return hex.EncodeToString(hash[:])
}

// generateWechatSign 生成微信支付签名
func generateWechatSign(params map[string]string) string {
	// 排序参数
	var keys []string
	for k := range params {
		if k != "sign" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 拼接参数
	var paramStrs []string
	for _, k := range keys {
		if params[k] != "" {
			paramStrs = append(paramStrs, fmt.Sprintf("%s=%s", k, params[k]))
		}
	}
	signStr := strings.Join(paramStrs, "&") + "&key=" + viper.GetString("wechat.api_key")

	// 生成MD5签名
	hash := md5.Sum([]byte(signStr))
	return strings.ToUpper(hex.EncodeToString(hash[:]))
}

// generateNonceStr 生成随机字符串
func generateNonceStr() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// verifyAlipaySign 验证支付宝签名
func verifyAlipaySign(params map[string]string) bool {
	sign := params["sign"]
	delete(params, "sign")
	delete(params, "sign_type")

	expectedSign := generateAlipaySign(params)
	return sign == expectedSign
}

// verifyWechatSign 验证微信支付签名
func verifyWechatSign(params map[string]string) bool {
	sign := params["sign"]
	delete(params, "sign")

	expectedSign := generateWechatSign(params)
	return sign == expectedSign
}

// isValidBankCard 验证银行卡号
func isValidBankCard(cardNo string) bool {
	// 简单的银行卡号验证（实际应该使用Luhn算法）
	if len(cardNo) < 16 || len(cardNo) > 19 {
		return false
	}

	for _, c := range cardNo {
		if c < '0' || c > '9' {
			return false
		}
	}

	return true
}

// maskBankCard 掩码银行卡号
func maskBankCard(cardNo string) string {
	if len(cardNo) < 8 {
		return cardNo
	}

	return cardNo[:4] + strings.Repeat("*", len(cardNo)-8) + cardNo[len(cardNo)-4:]
}
