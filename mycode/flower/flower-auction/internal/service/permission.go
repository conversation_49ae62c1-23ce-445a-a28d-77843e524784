package service

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrPermissionNotFound        = errors.New("权限不存在")
	ErrPermissionCodeExists      = errors.New("权限代码已存在")
	ErrRoleNotFound              = errors.New("角色不存在")
	ErrPermissionAlreadyAssigned = errors.New("权限已分配")
)

// PermissionService 权限服务接口
type PermissionService interface {
	// 权限管理
	CreatePermission(ctx context.Context, name, code, description, module, action, resource string) (*model.Permission, error)
	UpdatePermission(ctx context.Context, id int64, name, description, module, action, resource string) error
	DeletePermission(ctx context.Context, id int64) error
	GetPermission(ctx context.Context, id int64) (*model.Permission, error)
	ListPermissions(ctx context.Context, module string, page, size int) ([]*model.Permission, int64, error)
	UpdatePermissionStatus(ctx context.Context, id int64, status int8) error

	// 角色权限管理
	AssignPermissionsToRole(ctx context.Context, roleID int64, permissionIDs []int64) error
	RemovePermissionsFromRole(ctx context.Context, roleID int64, permissionIDs []int64) error
	GetRolePermissions(ctx context.Context, roleID int64) ([]*model.Permission, error)
	GetRoleWithPermissions(ctx context.Context, roleID int64) (*model.RoleWithPermissions, error)

	// 用户权限查询
	GetUserPermissions(ctx context.Context, userID int64) ([]*model.UserPermission, error)
	CheckUserPermission(ctx context.Context, userID int64, permissionCode string) (bool, error)
	GetUserPermissionsByModule(ctx context.Context, userID int64, module string) ([]*model.Permission, error)

	// 权限初始化
	InitDefaultPermissions(ctx context.Context) error
}

// permissionService 权限服务实现
type permissionService struct {
	permissionDAO dao.PermissionDAO
	userDAO       dao.UserDAO
}

// NewPermissionService 创建权限服务实例
func NewPermissionService() PermissionService {
	return &permissionService{
		permissionDAO: dao.NewPermissionDAO(),
		userDAO:       dao.NewUserDAO(),
	}
}

// CreatePermission 创建权限
func (s *permissionService) CreatePermission(ctx context.Context, name, code, description, module, action, resource string) (*model.Permission, error) {
	// 检查权限代码是否已存在
	existPermission, err := s.permissionDAO.FindPermissionByCode(ctx, code)
	if err != nil {
		return nil, err
	}
	if existPermission != nil {
		return nil, ErrPermissionCodeExists
	}

	permission := &model.Permission{
		Name:        name,
		Code:        code,
		Description: description,
		Module:      module,
		Action:      action,
		Resource:    resource,
		Status:      1, // 默认启用
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.permissionDAO.CreatePermission(ctx, permission); err != nil {
		return nil, err
	}

	return permission, nil
}

// UpdatePermission 更新权限
func (s *permissionService) UpdatePermission(ctx context.Context, id int64, name, description, module, action, resource string) error {
	permission, err := s.permissionDAO.FindPermissionByID(ctx, id)
	if err != nil {
		return err
	}
	if permission == nil {
		return ErrPermissionNotFound
	}

	permission.Name = name
	permission.Description = description
	permission.Module = module
	permission.Action = action
	permission.Resource = resource
	permission.UpdatedAt = time.Now()

	return s.permissionDAO.UpdatePermission(ctx, permission)
}

// DeletePermission 删除权限
func (s *permissionService) DeletePermission(ctx context.Context, id int64) error {
	permission, err := s.permissionDAO.FindPermissionByID(ctx, id)
	if err != nil {
		return err
	}
	if permission == nil {
		return ErrPermissionNotFound
	}

	return s.permissionDAO.DeletePermission(ctx, id)
}

// GetPermission 获取权限信息
func (s *permissionService) GetPermission(ctx context.Context, id int64) (*model.Permission, error) {
	permission, err := s.permissionDAO.FindPermissionByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if permission == nil {
		return nil, ErrPermissionNotFound
	}
	return permission, nil
}

// ListPermissions 查询权限列表
func (s *permissionService) ListPermissions(ctx context.Context, module string, page, size int) ([]*model.Permission, int64, error) {
	offset := (page - 1) * size
	permissions, err := s.permissionDAO.ListPermissions(ctx, module, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.permissionDAO.CountPermissions(ctx, module)
	if err != nil {
		return nil, 0, err
	}

	return permissions, total, nil
}

// UpdatePermissionStatus 更新权限状态
func (s *permissionService) UpdatePermissionStatus(ctx context.Context, id int64, status int8) error {
	permission, err := s.permissionDAO.FindPermissionByID(ctx, id)
	if err != nil {
		return err
	}
	if permission == nil {
		return ErrPermissionNotFound
	}

	permission.Status = status
	permission.UpdatedAt = time.Now()
	return s.permissionDAO.UpdatePermission(ctx, permission)
}

// AssignPermissionsToRole 为角色分配权限（完全替换）
func (s *permissionService) AssignPermissionsToRole(ctx context.Context, roleID int64, permissionIDs []int64) error {
	// 检查角色是否存在（通过查询角色权限关系来验证角色存在性）
	roleWithPermissions, err := s.permissionDAO.FindRoleWithPermissions(ctx, roleID)
	if err != nil {
		return err
	}
	if roleWithPermissions == nil {
		return ErrRoleNotFound
	}

	// 先清除角色的所有现有权限
	if err := s.permissionDAO.ClearRolePermissions(ctx, roleID); err != nil {
		return err
	}

	// 然后分配新的权限
	for _, permissionID := range permissionIDs {
		// 检查权限是否存在
		permission, err := s.permissionDAO.FindPermissionByID(ctx, permissionID)
		if err != nil {
			return err
		}
		if permission == nil {
			continue // 跳过不存在的权限
		}

		// 分配权限
		if err := s.permissionDAO.AssignPermissionToRole(ctx, roleID, permissionID); err != nil {
			return err
		}
	}

	return nil
}

// RemovePermissionsFromRole 从角色移除权限
func (s *permissionService) RemovePermissionsFromRole(ctx context.Context, roleID int64, permissionIDs []int64) error {
	// 检查角色是否存在（通过查询角色权限关系来验证角色存在性）
	roleWithPermissions, err := s.permissionDAO.FindRoleWithPermissions(ctx, roleID)
	if err != nil {
		return err
	}
	if roleWithPermissions == nil {
		return ErrRoleNotFound
	}

	// 移除权限
	for _, permissionID := range permissionIDs {
		if err := s.permissionDAO.RemovePermissionFromRole(ctx, roleID, permissionID); err != nil {
			return err
		}
	}

	return nil
}

// GetRolePermissions 获取角色权限
func (s *permissionService) GetRolePermissions(ctx context.Context, roleID int64) ([]*model.Permission, error) {
	return s.permissionDAO.FindRolePermissions(ctx, roleID)
}

// GetRoleWithPermissions 获取角色及其权限
func (s *permissionService) GetRoleWithPermissions(ctx context.Context, roleID int64) (*model.RoleWithPermissions, error) {
	return s.permissionDAO.FindRoleWithPermissions(ctx, roleID)
}

// GetUserPermissions 获取用户权限
func (s *permissionService) GetUserPermissions(ctx context.Context, userID int64) ([]*model.UserPermission, error) {
	return s.permissionDAO.FindUserPermissions(ctx, userID)
}

// CheckUserPermission 检查用户权限
func (s *permissionService) CheckUserPermission(ctx context.Context, userID int64, permissionCode string) (bool, error) {
	return s.permissionDAO.CheckUserPermission(ctx, userID, permissionCode)
}

// GetUserPermissionsByModule 根据模块获取用户权限
func (s *permissionService) GetUserPermissionsByModule(ctx context.Context, userID int64, module string) ([]*model.Permission, error) {
	return s.permissionDAO.FindUserPermissionsByModule(ctx, userID, module)
}

// InitDefaultPermissions 初始化默认权限
func (s *permissionService) InitDefaultPermissions(ctx context.Context) error {
	defaultPermissions := []struct {
		Name        string
		Code        string
		Description string
		Module      string
		Action      string
		Resource    string
	}{
		// 用户管理权限
		{"查看用户", "user:view", "查看用户信息", "user", "view", "user"},
		{"创建用户", "user:create", "创建新用户", "user", "create", "user"},
		{"编辑用户", "user:edit", "编辑用户信息", "user", "edit", "user"},
		{"删除用户", "user:delete", "删除用户", "user", "delete", "user"},
		{"管理用户状态", "user:status", "启用/禁用用户", "user", "status", "user"},

		// 角色管理权限
		{"查看角色", "role:view", "查看角色信息", "role", "view", "role"},
		{"创建角色", "role:create", "创建新角色", "role", "create", "role"},
		{"编辑角色", "role:edit", "编辑角色信息", "role", "edit", "role"},
		{"删除角色", "role:delete", "删除角色", "role", "delete", "role"},
		{"分配权限", "role:permission", "为角色分配权限", "role", "permission", "role"},

		// 商品管理权限
		{"查看商品", "product:view", "查看商品信息", "product", "view", "product"},
		{"创建商品", "product:create", "创建新商品", "product", "create", "product"},
		{"编辑商品", "product:edit", "编辑商品信息", "product", "edit", "product"},
		{"删除商品", "product:delete", "删除商品", "product", "delete", "product"},
		{"审核商品", "product:audit", "审核商品", "product", "audit", "product"},

		// 拍卖管理权限
		{"查看拍卖", "auction:view", "查看拍卖信息", "auction", "view", "auction"},
		{"创建拍卖", "auction:create", "创建拍卖会", "auction", "create", "auction"},
		{"编辑拍卖", "auction:edit", "编辑拍卖会信息", "auction", "edit", "auction"},
		{"删除拍卖", "auction:delete", "删除拍卖会", "auction", "delete", "auction"},
		{"发布拍卖", "auction:publish", "发布拍卖会", "auction", "publish", "auction"},
		{"取消拍卖", "auction:cancel", "取消拍卖会", "auction", "cancel", "auction"},
		{"主持拍卖", "auction:conduct", "主持拍卖会", "auction", "conduct", "auction"},
		{"控制拍卖", "auction:control", "控制拍卖进程", "auction", "control", "auction"},
		{"钟号管理", "auction:clock", "管理拍卖钟号", "auction", "clock", "auction"},
		{"竞价操作", "auction:bid", "参与竞价", "auction", "bid", "auction"},
		{"确认竞价", "auction:confirm_bid", "确认竞价结果", "auction", "confirm_bid", "auction"},

		// 订单管理权限
		{"查看订单", "order:view", "查看订单信息", "order", "view", "order"},
		{"处理订单", "order:process", "处理订单", "order", "process", "order"},
		{"取消订单", "order:cancel", "取消订单", "order", "cancel", "order"},
		{"退款处理", "order:refund", "处理退款申请", "order", "refund", "order"},
		{"发货管理", "order:ship", "管理订单发货", "order", "ship", "order"},

		// 财务管理权限
		{"查看财务", "finance:view", "查看财务信息", "finance", "view", "finance"},
		{"财务审核", "finance:audit", "财务审核", "finance", "audit", "finance"},
		{"佣金管理", "finance:commission", "管理拍卖佣金", "finance", "commission", "finance"},
		{"结算管理", "finance:settlement", "管理资金结算", "finance", "settlement", "finance"},
		{"生成报表", "finance:report", "生成财务报表", "finance", "report", "finance"},

		// 分类管理权限
		{"查看分类", "category:view", "查看商品分类", "category", "view", "category"},
		{"创建分类", "category:create", "创建商品分类", "category", "create", "category"},
		{"编辑分类", "category:edit", "编辑分类信息", "category", "edit", "category"},
		{"删除分类", "category:delete", "删除商品分类", "category", "delete", "category"},

		// 报表统计权限
		{"销售报表", "report:sales", "查看销售报表", "report", "sales", "report"},
		{"用户报表", "report:user", "查看用户统计", "report", "user", "report"},
		{"拍卖报表", "report:auction", "查看拍卖统计", "report", "auction", "report"},
		{"财务报表", "report:finance", "查看财务报表", "report", "finance", "report"},
		{"导出报表", "report:export", "导出报表数据", "report", "export", "report"},

		// 系统管理权限
		{"系统配置", "system:config", "系统配置管理", "system", "config", "system"},
		{"查看日志", "system:log", "查看系统日志", "system", "log", "system"},
		{"系统监控", "system:monitor", "系统监控", "system", "monitor", "system"},
		{"数据备份", "system:backup", "数据备份与恢复", "system", "backup", "system"},

		// 权限管理权限
		{"查看权限", "permission:view", "查看权限列表", "permission", "view", "permission"},
		{"创建权限", "permission:create", "创建新权限", "permission", "create", "permission"},
		{"编辑权限", "permission:edit", "编辑权限信息", "permission", "edit", "permission"},
		{"删除权限", "permission:delete", "删除权限", "permission", "delete", "permission"},
	}

	for _, perm := range defaultPermissions {
		// 检查权限是否已存在
		existPermission, err := s.permissionDAO.FindPermissionByCode(ctx, perm.Code)
		if err != nil {
			return err
		}
		if existPermission != nil {
			continue // 已存在，跳过
		}

		// 创建权限
		_, err = s.CreatePermission(ctx, perm.Name, perm.Code, perm.Description, perm.Module, perm.Action, perm.Resource)
		if err != nil {
			return err
		}
	}

	return nil
}
