package auth

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWT配置
var (
	// 在生产环境中应该从环境变量或配置文件读取
	JWTSecret     = []byte("flower_auction_secret_key")
	TokenExpiry   = 24 * time.Hour     // Token过期时间
	RefreshExpiry = 7 * 24 * time.Hour // RefreshToken过期时间
)

// JWTClaims JWT声明
type JWTClaims struct {
	UserID   int64   `json:"user_id"`
	Username string  `json:"username"`
	UserType int8    `json:"user_type"`
	Roles    []int64 `json:"roles"`
	jwt.RegisteredClaims
}

// TokenPair 令牌对
type TokenPair struct {
	AccessToken  string `json:"token"`
	RefreshToken string `json:"refreshToken"`
	ExpiresAt    int64  `json:"expiresAt"`
}

// TokenBlacklistChecker 黑名单检查接口
type TokenBlacklistChecker interface {
	IsBlacklisted(tokenString string) bool
	AddToBlacklist(tokenString string) error
}

// JWTService JWT服务
type JWTService struct {
	secret           []byte
	blacklistChecker TokenBlacklistChecker
}

// NewJWTService 创建JWT服务
func NewJWTService() *JWTService {
	return &JWTService{
		secret: JWTSecret,
	}
}

// NewJWTServiceWithBlacklist 创建带黑名单检查的JWT服务
func NewJWTServiceWithBlacklist(blacklistChecker TokenBlacklistChecker) *JWTService {
	return &JWTService{
		secret:           JWTSecret,
		blacklistChecker: blacklistChecker,
	}
}

// GenerateTokenPair 生成令牌对
func (j *JWTService) GenerateTokenPair(userID int64, username string, userType int8, roles []int64) (*TokenPair, error) {
	now := time.Now()

	// 生成访问令牌
	accessClaims := &JWTClaims{
		UserID:   userID,
		Username: username,
		UserType: userType,
		Roles:    roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(TokenExpiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "flower-auction",
			Subject:   username,
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString(j.secret)
	if err != nil {
		return nil, err
	}

	// 生成刷新令牌
	refreshClaims := &JWTClaims{
		UserID:   userID,
		Username: username,
		UserType: userType,
		Roles:    roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(RefreshExpiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "flower-auction",
			Subject:   username,
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString(j.secret)
	if err != nil {
		return nil, err
	}

	return &TokenPair{
		AccessToken:  accessTokenString,
		RefreshToken: refreshTokenString,
		ExpiresAt:    now.Add(TokenExpiry).Unix(),
	}, nil
}

// ParseToken 解析令牌
func (j *JWTService) ParseToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return j.secret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// ValidateToken 验证令牌
func (j *JWTService) ValidateToken(tokenString string) (*JWTClaims, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 检查令牌是否过期
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, errors.New("token expired")
	}

	return claims, nil
}

// RefreshToken 刷新令牌
func (j *JWTService) RefreshToken(refreshTokenString string) (*TokenPair, error) {
	claims, err := j.ParseToken(refreshTokenString)
	if err != nil {
		return nil, err
	}

	// 检查刷新令牌是否过期
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, errors.New("refresh token expired")
	}

	// 生成新的令牌对
	return j.GenerateTokenPair(claims.UserID, claims.Username, claims.UserType, claims.Roles)
}

// ExtractUserID 从令牌中提取用户ID
func (j *JWTService) ExtractUserID(tokenString string) (int64, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.UserID, nil
}

// ExtractUserInfo 从令牌中提取用户信息
func (j *JWTService) ExtractUserInfo(tokenString string) (*JWTClaims, error) {
	return j.ValidateToken(tokenString)
}
