package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/service"
)

// PermissionHandler 权限管理接口处理器
type PermissionHandler struct {
	permissionService service.PermissionService
	roleService       *service.RoleService
}

// NewPermissionHandler 创建权限管理接口处理器
func NewPermissionHandler(roleService *service.RoleService) *PermissionHandler {
	return &PermissionHandler{
		permissionService: service.NewPermissionService(),
		roleService:       roleService,
	}
}

// RegisterRoutes 注册路由
func (h *PermissionHandler) RegisterRoutes(r *gin.Engine) {
	// 角色管理路由组
	role := r.Group("/api/v1/roles")
	// 暂时注释掉JWT认证，方便测试
	// role.Use(middleware.JWTAuth())
	{
		role.POST("", h.CreateRole)                 // 创建角色
		role.PUT("/:id", h.UpdateRole)              // 更新角色
		role.DELETE("/:id", h.DeleteRole)           // 删除角色
		role.GET("/:id", h.GetRole)                 // 获取角色详情
		role.GET("", h.ListRoles)                   // 角色列表
		role.PUT("/:id/status", h.UpdateRoleStatus) // 更新角色状态
		role.POST("/export", h.ExportRoles)         // 导出角色数据
		// 角色权限管理 - 为了兼容前端路径
		role.PUT("/:id/permissions", h.AssignPermissionsToRoleByRoleID)      // 分配权限给角色
		role.GET("/:id/permissions", h.GetRolePermissionsByRoleID)           // 获取角色权限
		role.DELETE("/:id/permissions", h.RemovePermissionsFromRoleByRoleID) // 移除角色权限
	}

	// 权限管理路由组
	permission := r.Group("/api/v1/permissions")
	// 暂时移除JWT认证，方便前端测试
	// permission.Use(middleware.JWTAuth())
	{
		permission.POST("", h.CreatePermission)                 // 创建权限
		permission.PUT("/:id", h.UpdatePermission)              // 更新权限
		permission.DELETE("/:id", h.DeletePermission)           // 删除权限
		permission.GET("/:id", h.GetPermission)                 // 获取权限详情
		permission.GET("", h.ListPermissions)                   // 权限列表
		permission.PUT("/:id/status", h.UpdatePermissionStatus) // 更新权限状态
		permission.POST("/init", h.InitDefaultPermissions)      // 初始化默认权限
	}

	// 角色权限管理路由组 - 使用不同的路径避免冲突
	rolePermission := r.Group("/api/v1/role-permissions")
	rolePermission.Use(middleware.JWTAuth())
	{
		rolePermission.POST("/:roleId", middleware.RequireRole(1), h.AssignPermissionsToRole)     // 分配权限给角色
		rolePermission.DELETE("/:roleId", middleware.RequireRole(1), h.RemovePermissionsFromRole) // 移除角色权限
		rolePermission.GET("/:roleId", middleware.RequireRole(1, 2), h.GetRolePermissions)        // 获取角色权限
	}

	// 用户权限查询路由组 - 使用不同的路径避免冲突
	userPermission := r.Group("/api/v1/user-permissions")
	userPermission.Use(middleware.JWTAuth())
	{
		userPermission.GET("/:userId", h.GetUserPermissions)                         // 获取用户权限
		userPermission.GET("/:userId/check/:permissionCode", h.CheckUserPermission)  // 检查用户权限
		userPermission.GET("/:userId/modules/:module", h.GetUserPermissionsByModule) // 按模块获取用户权限
	}
}

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	Description string `json:"description"`
	Module      string `json:"module" binding:"required"`
	Action      string `json:"action" binding:"required"`
	Resource    string `json:"resource" binding:"required"`
}

// CreatePermission 创建权限
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	permission, err := h.permissionService.CreatePermission(c.Request.Context(), req.Name, req.Code, req.Description, req.Module, req.Action, req.Resource)
	if err != nil {
		if err == service.ErrPermissionCodeExists {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "权限代码已存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, permission)
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Module      string `json:"module" binding:"required"`
	Action      string `json:"action" binding:"required"`
	Resource    string `json:"resource" binding:"required"`
}

// UpdatePermission 更新权限
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	permissionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.UpdatePermission(c.Request.Context(), permissionID, req.Name, req.Description, req.Module, req.Action, req.Resource); err != nil {
		if err == service.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "权限不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限更新成功"})
}

// DeletePermission 删除权限
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	permissionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.DeletePermission(c.Request.Context(), permissionID); err != nil {
		if err == service.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "权限不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限删除成功"})
}

// GetPermission 获取权限详情
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	permissionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	permission, err := h.permissionService.GetPermission(c.Request.Context(), permissionID)
	if err != nil {
		if err == service.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "权限不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, permission)
}

// ListPermissions 权限列表
func (h *PermissionHandler) ListPermissions(c *gin.Context) {
	page, size := ParsePagination(c)
	module := c.Query("module")

	permissions, total, err := h.permissionService.ListPermissions(c.Request.Context(), module, page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list":  permissions,
			"total": total,
			"page":  page,
			"size":  size,
		},
	})
}

// UpdatePermissionStatusRequest 更新权限状态请求
type UpdatePermissionStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1"`
}

// UpdatePermissionStatus 更新权限状态
func (h *PermissionHandler) UpdatePermissionStatus(c *gin.Context) {
	permissionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdatePermissionStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.UpdatePermissionStatus(c.Request.Context(), permissionID, req.Status); err != nil {
		if err == service.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "权限不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限状态更新成功"})
}

// InitDefaultPermissions 初始化默认权限
func (h *PermissionHandler) InitDefaultPermissions(c *gin.Context) {
	if err := h.permissionService.InitDefaultPermissions(c.Request.Context()); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "默认权限初始化成功"})
}

// AssignPermissionsRequest 分配权限请求
type AssignPermissionsRequest struct {
	PermissionIDs []int64 `json:"permissionIds" binding:"required"`
}

// AssignPermissionsToRole 分配权限给角色
func (h *PermissionHandler) AssignPermissionsToRole(c *gin.Context) {
	roleID, err := ParseParamID(c, "roleId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.AssignPermissionsToRole(c.Request.Context(), roleID, req.PermissionIDs); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限分配成功"})
}

// RemovePermissionsFromRole 移除角色权限
func (h *PermissionHandler) RemovePermissionsFromRole(c *gin.Context) {
	roleID, err := ParseParamID(c, "roleId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.RemovePermissionsFromRole(c.Request.Context(), roleID, req.PermissionIDs); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限移除成功"})
}

// GetRolePermissions 获取角色权限
func (h *PermissionHandler) GetRolePermissions(c *gin.Context) {
	roleID, err := ParseParamID(c, "roleId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	roleWithPermissions, err := h.permissionService.GetRoleWithPermissions(c.Request.Context(), roleID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}
	if roleWithPermissions == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
		return
	}

	c.JSON(http.StatusOK, roleWithPermissions)
}

// GetUserPermissions 获取用户权限
func (h *PermissionHandler) GetUserPermissions(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 检查权限：只能查看自己的权限或管理员可以查看所有用户权限
	currentUserID, _, _, roles, ok := middleware.GetCurrentUser(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	// 检查是否是管理员或查看自己的权限
	isAdmin := false
	for _, role := range roles {
		if role == 1 { // 管理员角色
			isAdmin = true
			break
		}
	}

	if !isAdmin && currentUserID != userID {
		c.JSON(http.StatusForbidden, ErrorResponse{Error: "权限不足"})
		return
	}

	userPermissions, err := h.permissionService.GetUserPermissions(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, userPermissions)
}

// CheckUserPermission 检查用户权限
func (h *PermissionHandler) CheckUserPermission(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	permissionCode := c.Param("permissionCode")
	if permissionCode == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "权限代码不能为空"})
		return
	}

	hasPermission, err := h.permissionService.CheckUserPermission(c.Request.Context(), userID, permissionCode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"hasPermission":  hasPermission,
		"permissionCode": permissionCode,
	})
}

// GetUserPermissionsByModule 按模块获取用户权限
func (h *PermissionHandler) GetUserPermissionsByModule(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	module := c.Param("module")
	if module == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "模块名称不能为空"})
		return
	}

	permissions, err := h.permissionService.GetUserPermissionsByModule(c.Request.Context(), userID, module)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, permissions)
}

// ===== 角色管理相关方法 =====

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Code        string `json:"code" binding:"required,min=2,max=50"`
	Description string `json:"description"`
	Status      int8   `json:"status" binding:"oneof=0 1"` // 0:禁用 1:启用
}

// CreateRole 创建角色
func (h *PermissionHandler) CreateRole(c *gin.Context) {
	var req CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 调用service层创建角色
	role, err := h.roleService.CreateRole(c.Request.Context(), service.CreateRoleRequest{
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Status:      req.Status,
	})
	if err != nil {
		if err == service.ErrRoleCodeExists {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"code":    "ROLE_CODE_EXISTS",
				"message": "角色编码已存在",
				"data":    nil,
			})
			return
		}
		if err == service.ErrRoleNameExists {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"code":    "ROLE_NAME_EXISTS",
				"message": "角色名称已存在",
				"data":    nil,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    "SUCCESS",
		"message": "角色创建成功",
		"data":    role,
	})
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Description string `json:"description"`
	Status      int8   `json:"status" binding:"oneof=0 1"`
}

// UpdateRole 更新角色
func (h *PermissionHandler) UpdateRole(c *gin.Context) {
	roleID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 调用service层更新角色
	if err := h.roleService.UpdateRole(c.Request.Context(), roleID, service.UpdateRoleRequest{
		Name:        req.Name,
		Description: req.Description,
		Status:      req.Status,
	}); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		if err == service.ErrRoleNameExists {
			c.JSON(http.StatusConflict, ErrorResponse{Error: "角色名称已存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "角色更新成功",
	})
}

// DeleteRole 删除角色
func (h *PermissionHandler) DeleteRole(c *gin.Context) {
	roleID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 调用service层删除角色
	if err := h.roleService.DeleteRole(c.Request.Context(), roleID); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		if err == service.ErrRoleHasUsers {
			c.JSON(http.StatusConflict, ErrorResponse{Error: "角色下还有用户，无法删除"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "角色删除成功",
	})
}

// GetRole 获取角色详情
func (h *PermissionHandler) GetRole(c *gin.Context) {
	roleID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 调用service层获取角色
	role, err := h.roleService.GetRole(c.Request.Context(), roleID)
	if err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    role,
	})
}

// ListRoles 角色列表
func (h *PermissionHandler) ListRoles(c *gin.Context) {
	page, size := ParsePagination(c)
	name := c.Query("name")
	code := c.Query("code")
	statusStr := c.Query("status")

	// 构建查询参数
	params := service.RoleQueryParams{
		Name:     name,
		Code:     code,
		Page:     page,
		PageSize: size,
	}

	// 解析状态参数
	if statusStr != "" {
		status := parseStatus(statusStr)
		params.Status = &status
	}

	// 调用service层获取角色列表
	roles, total, err := h.roleService.ListRoles(c.Request.Context(), params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list":  roles,
			"total": total,
			"page":  page,
			"size":  size,
		},
	})
}

// UpdateRoleStatusRequest 更新角色状态请求
type UpdateRoleStatusRequest struct {
	Status *int8 `json:"status"`
}

// UpdateRoleStatus 更新角色状态
func (h *PermissionHandler) UpdateRoleStatus(c *gin.Context) {
	roleID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateRoleStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 检查状态值是否为空
	if req.Status == nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "状态值不能为空"})
		return
	}

	// 检查状态值是否有效
	if *req.Status != 0 && *req.Status != 1 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "状态值必须为0或1"})
		return
	}

	// 调用service层更新角色状态
	if err := h.roleService.UpdateRoleStatus(c.Request.Context(), roleID, *req.Status); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "角色状态更新成功",
	})
}

// ExportRoles 导出角色数据
func (h *PermissionHandler) ExportRoles(c *gin.Context) {
	// 模拟导出数据
	roles := []gin.H{
		{
			"id":          1,
			"name":        "系统管理员",
			"code":        "ADMIN",
			"description": "系统管理员角色",
			"status":      1,
		},
		{
			"id":          2,
			"name":        "拍卖师",
			"code":        "AUCTIONEER",
			"description": "拍卖师角色",
			"status":      1,
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    roles,
		"message": "角色数据导出成功",
	})
}

// parseStatus 解析状态字符串
func parseStatus(status string) int {
	if status == "1" {
		return 1
	}
	return 0
}

// ===== 兼容前端路径的角色权限管理方法 =====

// AssignPermissionsToRoleByRoleID 分配权限给角色 (兼容前端路径)
func (h *PermissionHandler) AssignPermissionsToRoleByRoleID(c *gin.Context) {
	roleID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.AssignPermissionsToRole(c.Request.Context(), roleID, req.PermissionIDs); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "权限分配成功",
	})
}

// GetRolePermissionsByRoleID 获取角色权限 (兼容前端路径)
func (h *PermissionHandler) GetRolePermissionsByRoleID(c *gin.Context) {
	roleID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	roleWithPermissions, err := h.permissionService.GetRoleWithPermissions(c.Request.Context(), roleID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}
	if roleWithPermissions == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    roleWithPermissions,
	})
}

// RemovePermissionsFromRoleByRoleID 移除角色权限 (兼容前端路径)
func (h *PermissionHandler) RemovePermissionsFromRoleByRoleID(c *gin.Context) {
	roleID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.RemovePermissionsFromRole(c.Request.Context(), roleID, req.PermissionIDs); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "权限移除成功",
	})
}
