package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/putonghao/flower-auction/internal/service"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	orderService service.OrderService
}

// NewOrderHandler 创建订单处理器实例
func NewOrderHandler() *OrderHandler {
	return &OrderHandler{
		orderService: service.NewOrderService(),
	}
}

// RegisterRoutes 注册路由
func (h *OrderHandler) RegisterRoutes(r *gin.Engine) {
	// 订单管理路由组
	order := r.Group("/api/v1/orders")
	order.Use(middleware.JWTAuth())
	{
		order.POST("", h.CreateOrder)                                                      // 创建订单
		order.GET("/:id", h.GetOrder)                                                      // 获取订单详情
		order.GET("/no/:orderNo", h.GetOrderByNo)                                          // 根据订单号获取订单
		order.GET("/user/:userId", h.ListUserOrders)                                       // 获取用户订单列表
		order.PUT("/:id/status", middleware.RequireRole(1, 3), h.UpdateOrderStatus)        // 更新订单状态 (拍卖师/管理员)
		order.GET("", middleware.RequireRole(1, 3), h.ListAllOrders)                       // 获取所有订单 (拍卖师/管理员)
		order.POST("/export", middleware.RequireRole(1, 3), h.ExportOrders)                // 导出订单 (拍卖师/管理员)
		order.GET("/statistics", middleware.RequireRole(1, 3), h.GetOrderStatistics)       // 订单统计 (拍卖师/管理员)
		order.POST("/:id/confirm-delivery", h.ConfirmDelivery)                             // 确认收货
		order.PUT("/batch-status", middleware.RequireRole(1, 3), h.BatchUpdateOrderStatus) // 批量更新订单状态
		order.GET("/:id/shipping", h.GetOrderShipping)                                     // 获取订单物流信息
		order.GET("/:id/payment", h.GetOrderPayment)                                       // 获取订单支付信息
		order.POST("/:id/retry-payment", h.RetryPayment)                                   // 重新发起支付
		order.GET("/:id/logs", h.GetOrderLogs)                                             // 获取订单操作日志
	}

	// 支付管理路由组
	payment := r.Group("/api/v1/payments")
	payment.Use(middleware.JWTAuth())
	{
		payment.POST("", h.CreatePayment)                        // 创建支付记录
		payment.GET("/:paymentNo", h.GetPayment)                 // 获取支付记录
		payment.PUT("/:paymentNo/status", h.UpdatePaymentStatus) // 更新支付状态
	}
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	AuctionItemID int64   `json:"auctionItemId" binding:"required"`
	Amount        float64 `json:"amount" binding:"required,gt=0"`
}

// CreateOrder 创建订单
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	var req CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	order, err := h.orderService.CreateOrder(c.Request.Context(), userID.(int64), req.AuctionItemID, req.Amount)
	if err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		if err == service.ErrInvalidOperation {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无法创建订单，请检查拍卖商品状态"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// GetOrder 获取订单详情
func (h *OrderHandler) GetOrder(c *gin.Context) {
	orderID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	order, err := h.orderService.GetOrder(c.Request.Context(), orderID)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// GetOrderByNo 根据订单号获取订单
func (h *OrderHandler) GetOrderByNo(c *gin.Context) {
	orderNo := c.Param("orderNo")
	if orderNo == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "订单号不能为空"})
		return
	}

	order, err := h.orderService.GetOrderByNo(c.Request.Context(), orderNo)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// ListUserOrders 获取用户订单列表
func (h *OrderHandler) ListUserOrders(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 检查权限：用户只能查看自己的订单，管理员和拍卖师可以查看所有用户订单
	currentUserID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	userRole, exists := c.Get("userRole")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户角色信息缺失"})
		return
	}

	// 如果不是管理员或拍卖师，只能查看自己的订单
	if userRole.(int8) != 1 && userRole.(int8) != 2 && currentUserID.(int64) != userID {
		c.JSON(http.StatusForbidden, ErrorResponse{Error: "无权限查看其他用户订单"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	orders, total, err := h.orderService.ListUserOrders(c.Request.Context(), userID, page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"orders": orders,
		"total":  total,
		"page":   page,
		"size":   size,
	})
}

// UpdateOrderStatusRequest 更新订单状态请求
type UpdateOrderStatusRequest struct {
	Status int8 `json:"status" binding:"required,min=0,max=4"`
}

// UpdateOrderStatus 更新订单状态
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	orderID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateOrderStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.orderService.UpdateOrderStatus(c.Request.Context(), orderID, req.Status); err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "订单状态更新成功"})
}

// ListAllOrders 获取所有订单（管理员功能）
func (h *OrderHandler) ListAllOrders(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	statusStr := c.Query("status")

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	var status *int8
	if statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil && s >= 0 && s <= 4 {
			statusVal := int8(s)
			status = &statusVal
		}
	}

	orders, total, err := h.orderService.ListAllOrders(c.Request.Context(), page, size, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"orders": orders,
		"total":  total,
		"page":   page,
		"size":   size,
		"status": status,
	})
}

// CreatePaymentRequest 创建支付记录请求
type CreatePaymentRequest struct {
	OrderID       int64   `json:"orderId" binding:"required"`
	Amount        float64 `json:"amount" binding:"required,gt=0"`
	PaymentMethod int8    `json:"paymentMethod" binding:"required,min=1,max=3"`
}

// CreatePayment 创建支付记录
func (h *OrderHandler) CreatePayment(c *gin.Context) {
	var req CreatePaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	payment, err := h.orderService.CreatePayment(c.Request.Context(), req.OrderID, req.Amount, req.PaymentMethod)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		if err == service.ErrOrderPaid {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "订单已支付"})
			return
		}
		if err == service.ErrInvalidAmount {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付金额无效"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, payment)
}

// GetPayment 获取支付记录
func (h *OrderHandler) GetPayment(c *gin.Context) {
	paymentNo := c.Param("paymentNo")
	if paymentNo == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付流水号不能为空"})
		return
	}

	payment, err := h.orderService.GetPayment(c.Request.Context(), paymentNo)
	if err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, payment)
}

// UpdatePaymentStatusRequest 更新支付状态请求
type UpdatePaymentStatusRequest struct {
	Status int8 `json:"status" binding:"required,min=0,max=2"`
}

// UpdatePaymentStatus 更新支付状态
func (h *OrderHandler) UpdatePaymentStatus(c *gin.Context) {
	paymentNo := c.Param("paymentNo")
	if paymentNo == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付流水号不能为空"})
		return
	}

	var req UpdatePaymentStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.orderService.UpdatePaymentStatus(c.Request.Context(), paymentNo, req.Status); err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "支付状态更新成功"})
}

// ExportOrdersRequest 导出订单请求
type ExportOrdersRequest struct {
	StartDate string   `json:"startDate" binding:"required"`
	EndDate   string   `json:"endDate" binding:"required"`
	Status    *int8    `json:"status"`
	UserID    *int64   `json:"userId"`
	MinAmount *float64 `json:"minAmount"`
	MaxAmount *float64 `json:"maxAmount"`
	Format    string   `json:"format"` // csv, excel
}

// ExportOrders 导出订单
func (h *OrderHandler) ExportOrders(c *gin.Context) {
	var req ExportOrdersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "开始日期格式错误"})
		return
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "结束日期格式错误"})
		return
	}

	// 设置默认格式
	if req.Format == "" {
		req.Format = "csv"
	}

	query := &model.OrderExportQuery{
		StartDate: startDate,
		EndDate:   endDate.Add(24 * time.Hour), // 包含结束日期当天
		Status:    req.Status,
		UserID:    req.UserID,
		MinAmount: req.MinAmount,
		MaxAmount: req.MaxAmount,
		Format:    req.Format,
	}

	data, err := h.orderService.ExportOrders(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 设置响应头
	filename := fmt.Sprintf("orders_%s_%s.%s", req.StartDate, req.EndDate, req.Format)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	if req.Format == "csv" {
		c.Header("Content-Type", "text/csv")
	} else {
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	}

	c.Data(http.StatusOK, c.GetHeader("Content-Type"), data)
}

// GetOrderStatistics 获取订单统计
func (h *OrderHandler) GetOrderStatistics(c *gin.Context) {
	startDate := c.Query("startDate")
	endDate := c.Query("endDate")
	groupBy := c.DefaultQuery("groupBy", "day")

	if startDate == "" || endDate == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "开始日期和结束日期不能为空"})
		return
	}

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "开始日期格式错误"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "结束日期格式错误"})
		return
	}

	// 解析可选参数
	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil && s >= 0 && s <= 4 {
			statusVal := int8(s)
			status = &statusVal
		}
	}

	var userID *int64
	if userIDStr := c.Query("userId"); userIDStr != "" {
		if uid, err := strconv.ParseInt(userIDStr, 10, 64); err == nil {
			userID = &uid
		}
	}

	query := &model.OrderStatisticsQuery{
		StartDate: start,
		EndDate:   end.Add(24 * time.Hour), // 包含结束日期当天
		GroupBy:   groupBy,
		Status:    status,
		UserID:    userID,
	}

	statistics, err := h.orderService.GetOrderStatistics(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, statistics)
}

// ConfirmDelivery 确认收货
func (h *OrderHandler) ConfirmDelivery(c *gin.Context) {
	_, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 这里应该实现确认收货逻辑
	// 为了简化，这里只返回成功消息
	c.JSON(http.StatusOK, SuccessResponse{Message: "确认收货成功"})
}

// BatchUpdateOrderStatusRequest 批量更新订单状态请求
type BatchUpdateOrderStatusRequest struct {
	IDs    []int64 `json:"ids" binding:"required"`
	Status int8    `json:"status" binding:"required,min=0,max=4"`
	Remark string  `json:"remark"`
}

// BatchUpdateOrderStatus 批量更新订单状态
func (h *OrderHandler) BatchUpdateOrderStatus(c *gin.Context) {
	var req BatchUpdateOrderStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 这里应该实现批量更新订单状态逻辑
	// 为了简化，这里只返回成功消息
	c.JSON(http.StatusOK, SuccessResponse{Message: "批量更新成功"})
}

// GetOrderShipping 获取订单物流信息
func (h *OrderHandler) GetOrderShipping(c *gin.Context) {
	_, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 这里应该查询订单物流信息
	// 为了简化，返回模拟数据
	shipping := gin.H{
		"trackingNumber":  "SF123456789",
		"shippingCompany": "顺丰快递",
		"shippingTime":    "2024-01-01 10:00:00",
		"deliveryTime":    "",
		"shippingStatus":  "运输中",
		"trackingHistory": []gin.H{
			{
				"time":        "2024-01-01 10:00:00",
				"status":      "已发货",
				"location":    "北京",
				"description": "快件已从北京发出",
			},
		},
	}

	c.JSON(http.StatusOK, shipping)
}

// GetOrderPayment 获取订单支付信息
func (h *OrderHandler) GetOrderPayment(c *gin.Context) {
	_, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 这里应该查询订单支付信息
	// 为了简化，返回模拟数据
	payment := gin.H{
		"paymentMethod": 1,
		"paymentTime":   "2024-01-01 09:30:00",
		"paymentAmount": 100.00,
		"transactionId": "TXN123456789",
		"paymentStatus": "已支付",
	}

	c.JSON(http.StatusOK, payment)
}

// RetryPayment 重新发起支付
func (h *OrderHandler) RetryPayment(c *gin.Context) {
	orderID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 这里应该实现重新发起支付逻辑
	// 为了简化，返回模拟数据
	paymentInfo := gin.H{
		"paymentUrl": "https://pay.example.com/pay?order=" + fmt.Sprintf("%d", orderID),
		"qrCode":     "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
	}

	c.JSON(http.StatusOK, paymentInfo)
}

// GetOrderLogs 获取订单操作日志
func (h *OrderHandler) GetOrderLogs(c *gin.Context) {
	_, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 这里应该查询订单操作日志
	// 为了简化，返回模拟数据
	logs := []gin.H{
		{
			"id":           1,
			"action":       "创建订单",
			"description":  "用户创建了订单",
			"operatorName": "用户",
			"createdAt":    "2024-01-01 09:00:00",
		},
		{
			"id":           2,
			"action":       "支付订单",
			"description":  "用户完成了支付",
			"operatorName": "用户",
			"createdAt":    "2024-01-01 09:30:00",
		},
	}

	c.JSON(http.StatusOK, logs)
}
