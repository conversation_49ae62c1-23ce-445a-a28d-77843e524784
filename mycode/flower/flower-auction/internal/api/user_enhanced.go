package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/service"
)

// UserEnhancedHandler 用户增强功能接口处理器
type UserEnhancedHandler struct {
	userEnhancedService service.UserEnhancedService
}

// NewUserEnhancedHandler 创建用户增强功能接口处理器
func NewUserEnhancedHandler() *UserEnhancedHandler {
	return &UserEnhancedHandler{
		userEnhancedService: service.NewUserEnhancedService(),
	}
}

// RegisterRoutes 注册路由
func (h *UserEnhancedHandler) RegisterRoutes(r *gin.Engine) {
	// 实名认证路由组
	verification := r.Group("/api/v1/verification")
	verification.Use(middleware.JWTAuth())
	{
		verification.POST("", h.SubmitVerification)                                          // 提交实名认证
		verification.GET("/user/:userId", h.GetUserVerification)                            // 获取用户认证信息
		verification.GET("", middleware.RequireRole(1), h.ListVerifications)               // 认证列表 (仅管理员)
		verification.PUT("/:id/approve", middleware.RequireRole(1), h.ApproveVerification) // 通过认证 (仅管理员)
		verification.PUT("/:id/reject", middleware.RequireRole(1), h.RejectVerification)   // 拒绝认证 (仅管理员)
	}

	// 子账号管理路由组
	subAccount := r.Group("/api/v1/sub-accounts")
	subAccount.Use(middleware.JWTAuth())
	{
		subAccount.POST("", h.CreateSubAccount)                                             // 创建子账号
		subAccount.PUT("/:id", h.UpdateSubAccount)                                          // 更新子账号
		subAccount.DELETE("/:id", h.DeleteSubAccount)                                       // 删除子账号
		subAccount.GET("/:id", h.GetSubAccount)                                             // 获取子账号信息
		subAccount.GET("/parent/:parentId", h.ListSubAccounts)                              // 子账号列表
		subAccount.PUT("/:id/status", h.UpdateSubAccountStatus)                             // 更新子账号状态
		subAccount.POST("/login", h.SubAccountLogin)                                        // 子账号登录
	}

	// 用户档案路由组
	profile := r.Group("/api/v1/profiles")
	profile.Use(middleware.JWTAuth())
	{
		profile.PUT("/:userId", h.CreateOrUpdateProfile)                                    // 创建或更新用户档案
		profile.GET("/:userId", h.GetUserProfile)                                          // 获取用户档案
		profile.GET("/:userId/complete", h.GetUserWithProfile)                             // 获取用户完整信息
		profile.GET("/:userId/stats", h.GetUserStats)                                      // 获取用户统计信息
	}

	// 批量操作路由组
	batch := r.Group("/api/v1/users/batch")
	batch.Use(middleware.JWTAuth(), middleware.RequireRole(1)) // 仅管理员
	{
		batch.POST("/import", h.BatchImportUsers)                                          // 批量导入用户
	}
}

// SubmitVerificationRequest 提交实名认证请求
type SubmitVerificationRequest struct {
	RealName        string `json:"realName" binding:"required"`
	IDCardNumber    string `json:"idCardNumber" binding:"required"`
	IDCardFront     string `json:"idCardFront" binding:"required"`
	IDCardBack      string `json:"idCardBack" binding:"required"`
	BusinessLicense string `json:"businessLicense"`
}

// SubmitVerification 提交实名认证
func (h *UserEnhancedHandler) SubmitVerification(c *gin.Context) {
	userID, _, _, _, ok := middleware.GetCurrentUser(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	var req SubmitVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	verification, err := h.userEnhancedService.SubmitVerification(c.Request.Context(), userID, req.RealName, req.IDCardNumber, req.IDCardFront, req.IDCardBack, req.BusinessLicense)
	if err != nil {
		if err == service.ErrVerificationExists {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "实名认证已存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, verification)
}

// GetUserVerification 获取用户认证信息
func (h *UserEnhancedHandler) GetUserVerification(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 检查权限：只能查看自己的认证信息或管理员可以查看所有用户认证信息
	currentUserID, _, _, roles, ok := middleware.GetCurrentUser(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	isAdmin := false
	for _, role := range roles {
		if role == 1 { // 管理员角色
			isAdmin = true
			break
		}
	}

	if !isAdmin && currentUserID != userID {
		c.JSON(http.StatusForbidden, ErrorResponse{Error: "权限不足"})
		return
	}

	verification, err := h.userEnhancedService.GetUserVerification(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, verification)
}

// ListVerifications 认证列表
func (h *UserEnhancedHandler) ListVerifications(c *gin.Context) {
	page, size := ParsePagination(c)
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))

	verifications, total, err := h.userEnhancedService.ListVerifications(c.Request.Context(), int8(status), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  verifications,
		Total: total,
		Page:  page,
		Size:  size,
	})
}

// ApproveVerification 通过认证
func (h *UserEnhancedHandler) ApproveVerification(c *gin.Context) {
	verificationID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.userEnhancedService.ApproveVerification(c.Request.Context(), verificationID); err != nil {
		if err == service.ErrVerificationNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "实名认证不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "认证通过成功"})
}

// RejectVerificationRequest 拒绝认证请求
type RejectVerificationRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// RejectVerification 拒绝认证
func (h *UserEnhancedHandler) RejectVerification(c *gin.Context) {
	verificationID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req RejectVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.userEnhancedService.RejectVerification(c.Request.Context(), verificationID, req.Reason); err != nil {
		if err == service.ErrVerificationNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "实名认证不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "认证拒绝成功"})
}

// CreateSubAccountRequest 创建子账号请求
type CreateSubAccountRequest struct {
	Username    string   `json:"username" binding:"required"`
	Password    string   `json:"password" binding:"required,min=6"`
	RealName    string   `json:"realName" binding:"required"`
	Phone       string   `json:"phone"`
	Email       string   `json:"email"`
	Permissions []string `json:"permissions"`
}

// CreateSubAccount 创建子账号
func (h *UserEnhancedHandler) CreateSubAccount(c *gin.Context) {
	parentID, _, _, _, ok := middleware.GetCurrentUser(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	var req CreateSubAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	subAccount, err := h.userEnhancedService.CreateSubAccount(c.Request.Context(), parentID, req.Username, req.Password, req.RealName, req.Phone, req.Email, req.Permissions)
	if err != nil {
		if err == service.ErrSubAccountExists {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "子账号用户名已存在"})
			return
		}
		if err == service.ErrSubAccountLimitExceeded {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "子账号数量超限"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, subAccount)
}

// UpdateSubAccountRequest 更新子账号请求
type UpdateSubAccountRequest struct {
	RealName    string   `json:"realName" binding:"required"`
	Phone       string   `json:"phone"`
	Email       string   `json:"email"`
	Permissions []string `json:"permissions"`
}

// UpdateSubAccount 更新子账号
func (h *UserEnhancedHandler) UpdateSubAccount(c *gin.Context) {
	subAccountID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateSubAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.userEnhancedService.UpdateSubAccount(c.Request.Context(), subAccountID, req.RealName, req.Phone, req.Email, req.Permissions); err != nil {
		if err == service.ErrSubAccountNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "子账号不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "子账号更新成功"})
}

// DeleteSubAccount 删除子账号
func (h *UserEnhancedHandler) DeleteSubAccount(c *gin.Context) {
	subAccountID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.userEnhancedService.DeleteSubAccount(c.Request.Context(), subAccountID); err != nil {
		if err == service.ErrSubAccountNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "子账号不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "子账号删除成功"})
}

// GetSubAccount 获取子账号信息
func (h *UserEnhancedHandler) GetSubAccount(c *gin.Context) {
	subAccountID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	subAccount, err := h.userEnhancedService.GetSubAccount(c.Request.Context(), subAccountID)
	if err != nil {
		if err == service.ErrSubAccountNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "子账号不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, subAccount)
}

// ListSubAccounts 子账号列表
func (h *UserEnhancedHandler) ListSubAccounts(c *gin.Context) {
	parentID, err := ParseParamID(c, "parentId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 检查权限：只能查看自己的子账号或管理员可以查看所有子账号
	currentUserID, _, _, roles, ok := middleware.GetCurrentUser(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	isAdmin := false
	for _, role := range roles {
		if role == 1 { // 管理员角色
			isAdmin = true
			break
		}
	}

	if !isAdmin && currentUserID != parentID {
		c.JSON(http.StatusForbidden, ErrorResponse{Error: "权限不足"})
		return
	}

	subAccounts, err := h.userEnhancedService.ListSubAccounts(c.Request.Context(), parentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, subAccounts)
}

// UpdateSubAccountStatusRequest 更新子账号状态请求
type UpdateSubAccountStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1"`
}

// UpdateSubAccountStatus 更新子账号状态
func (h *UserEnhancedHandler) UpdateSubAccountStatus(c *gin.Context) {
	subAccountID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateSubAccountStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.userEnhancedService.UpdateSubAccountStatus(c.Request.Context(), subAccountID, req.Status); err != nil {
		if err == service.ErrSubAccountNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "子账号不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "子账号状态更新成功"})
}

// SubAccountLoginRequest 子账号登录请求
type SubAccountLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// SubAccountLogin 子账号登录
func (h *UserEnhancedHandler) SubAccountLogin(c *gin.Context) {
	var req SubAccountLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	subAccount, err := h.userEnhancedService.SubAccountLogin(c.Request.Context(), req.Username, req.Password)
	if err != nil {
		if err == service.ErrUserNotFound || err == service.ErrInvalidPassword {
			c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户名或密码错误"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 生成JWT令牌 (子账号使用特殊的角色ID)
	authService := service.NewAuthService()
	token, err := authService.GenerateToken(subAccount.ID, subAccount.Username, 99, []int64{99}) // 99表示子账号
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"subAccount": subAccount,
		"token":      token,
	})
}

// CreateOrUpdateProfileRequest 创建或更新用户档案请求
type CreateOrUpdateProfileRequest struct {
	Avatar      string     `json:"avatar"`
	Gender      int8       `json:"gender"`
	Birthday    *time.Time `json:"birthday"`
	Address     string     `json:"address"`
	Company     string     `json:"company"`
	Position    string     `json:"position"`
	Website     string     `json:"website"`
	Bio         string     `json:"bio"`
	Preferences string     `json:"preferences"`
}

// CreateOrUpdateProfile 创建或更新用户档案
func (h *UserEnhancedHandler) CreateOrUpdateProfile(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 检查权限：只能更新自己的档案或管理员可以更新所有用户档案
	currentUserID, _, _, roles, ok := middleware.GetCurrentUser(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	isAdmin := false
	for _, role := range roles {
		if role == 1 { // 管理员角色
			isAdmin = true
			break
		}
	}

	if !isAdmin && currentUserID != userID {
		c.JSON(http.StatusForbidden, ErrorResponse{Error: "权限不足"})
		return
	}

	var req CreateOrUpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	profile, err := h.userEnhancedService.CreateOrUpdateProfile(c.Request.Context(), userID, req.Avatar, req.Gender, req.Birthday, req.Address, req.Company, req.Position, req.Website, req.Bio, req.Preferences)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, profile)
}

// GetUserProfile 获取用户档案
func (h *UserEnhancedHandler) GetUserProfile(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	profile, err := h.userEnhancedService.GetUserProfile(c.Request.Context(), userID)
	if err != nil {
		if err == service.ErrProfileNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户档案不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, profile)
}

// GetUserWithProfile 获取用户完整信息
func (h *UserEnhancedHandler) GetUserWithProfile(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	userWithProfile, err := h.userEnhancedService.GetUserWithProfile(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}
	if userWithProfile == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户不存在"})
		return
	}

	c.JSON(http.StatusOK, userWithProfile)
}

// GetUserStats 获取用户统计信息
func (h *UserEnhancedHandler) GetUserStats(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	stats, err := h.userEnhancedService.GetUserStats(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// BatchImportUsersRequest 批量导入用户请求
type BatchImportUsersRequest struct {
	Users []map[string]interface{} `json:"users" binding:"required"`
}

// BatchImportUsers 批量导入用户
func (h *UserEnhancedHandler) BatchImportUsers(c *gin.Context) {
	var req BatchImportUsersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	userIDs, errors := h.userEnhancedService.BatchImportUsers(c.Request.Context(), req.Users)

	c.JSON(http.StatusOK, gin.H{
		"userIds": userIDs,
		"errors":  errors,
	})
}
