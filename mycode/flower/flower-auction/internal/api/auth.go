package api

import (
	"context"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/auth"
	"github.com/putonghao/flower-auction/internal/service"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	jwtService  *auth.JWTService
	userService service.UserService
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler() *AuthHandler {
	return &AuthHandler{
		jwtService:  auth.NewJWTService(),
		userService: service.NewUserService(),
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required,min=3,max=32"`
	Password string `json:"password" binding:"required,min=6,max=32"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	User         interface{} `json:"user"`
	Token        string      `json:"token"`
	RefreshToken string      `json:"refreshToken"`
	ExpiresAt    int64       `json:"expiresAt"`
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	ctx := context.Background()

	// 验证用户凭据
	userWithRoles, err := h.userService.Login(ctx, req.Username, req.Password)
	if err != nil {
		if err == service.ErrUserNotFound || err == service.ErrInvalidPassword {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "用户名或密码错误",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "登录失败",
			"error":   err.Error(),
		})
		return
	}

	// 检查用户状态
	if userWithRoles.Status != 1 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户已被禁用",
		})
		return
	}

	// 提取角色ID
	roleIDs := make([]int64, len(userWithRoles.Roles))
	for i, role := range userWithRoles.Roles {
		roleIDs[i] = role.ID
	}

	// 生成JWT令牌对
	tokenPair, err := h.jwtService.GenerateTokenPair(
		userWithRoles.ID,
		userWithRoles.Username,
		userWithRoles.UserType,
		roleIDs,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "生成令牌失败",
			"error":   err.Error(),
		})
		return
	}

	// 返回登录成功响应
	userData := gin.H{
		"id":           userWithRoles.ID,
		"username":     userWithRoles.Username,
		"realName":     userWithRoles.RealName,
		"phone":        userWithRoles.Phone,
		"email":        userWithRoles.Email,
		"userType":     userWithRoles.UserType,
		"status":       userWithRoles.Status,
		"roles":        userWithRoles.Roles,
		"balance":      userWithRoles.Balance,
		"frozenAmount": userWithRoles.FrozenAmount,
		"companyName":  userWithRoles.CompanyName,
		"creditLevel":  userWithRoles.CreditLevel,
	}

	// 如果是拍卖师，添加钟号权限
	if userWithRoles.UserType == 1 {
		userData["clockNumbers"] = userWithRoles.GetClockNumbers()
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "登录成功",
		"data": LoginResponse{
			User:         userData,
			Token:        tokenPair.AccessToken,
			RefreshToken: tokenPair.RefreshToken,
			ExpiresAt:    tokenPair.ExpiresAt,
		},
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	// 从请求头获取token
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "登出成功",
		})
		return
	}

	// 解析token（这里可以实现token黑名单机制）
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	if tokenString != "" {
		// TODO: 将token加入黑名单
		// 目前简单返回成功
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "登出成功",
	})
}

// GetUserInfo 获取当前用户信息
func (h *AuthHandler) GetUserInfo(c *gin.Context) {
	// 从请求头获取token
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未提供认证令牌",
		})
		return
	}

	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	if tokenString == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "无效的认证令牌格式",
		})
		return
	}

	// 验证并解析token
	claims, err := h.jwtService.ValidateToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "无效或过期的令牌",
		})
		return
	}

	// 从数据库获取最新的用户信息
	ctx := context.Background()
	userWithRoles, err := h.userService.GetUserInfo(ctx, claims.UserID)
	if err != nil {
		if err == service.ErrUserNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "用户不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户信息失败",
			"error":   err.Error(),
		})
		return
	}

	// 检查用户状态
	if userWithRoles.User.Status != 1 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户已被禁用",
		})
		return
	}

	userData := gin.H{
		"id":           userWithRoles.User.ID,
		"username":     userWithRoles.User.Username,
		"realName":     userWithRoles.User.RealName,
		"phone":        userWithRoles.User.Phone,
		"email":        userWithRoles.User.Email,
		"userType":     userWithRoles.User.UserType,
		"status":       userWithRoles.User.Status,
		"role":         getUserRole(userWithRoles.User.UserType),
		"roles":        userWithRoles.Roles,
		"balance":      userWithRoles.User.Balance,
		"frozenAmount": userWithRoles.User.FrozenAmount,
		"companyName":  userWithRoles.User.CompanyName,
		"creditLevel":  userWithRoles.User.CreditLevel,
	}

	// 如果是拍卖师，添加钟号权限
	if userWithRoles.User.UserType == 1 {
		userData["clockNumbers"] = userWithRoles.User.GetClockNumbers()
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userData,
	})
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken" binding:"required"`
}

// RefreshToken 刷新令牌
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 刷新令牌
	tokenPair, err := h.jwtService.RefreshToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "刷新令牌失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "令牌刷新成功",
		"data": gin.H{
			"token":        tokenPair.AccessToken,
			"refreshToken": tokenPair.RefreshToken,
			"expiresAt":    tokenPair.ExpiresAt,
		},
	})
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required,min=6,max=32"`
}

// ChangePassword 修改密码
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 从token中获取用户ID
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未提供认证令牌",
		})
		return
	}

	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	claims, err := h.jwtService.ValidateToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "无效或过期的令牌",
		})
		return
	}

	// 修改密码
	ctx := context.Background()
	err = h.userService.ChangePassword(ctx, claims.UserID, req.OldPassword, req.NewPassword)
	if err != nil {
		if err == service.ErrInvalidPassword {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "原密码错误",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "修改密码失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码修改成功",
	})
}

// RegisterRoutes 注册认证路由
func (h *AuthHandler) RegisterRoutes(r *gin.Engine) {
	auth := r.Group("/api/v1/auth")
	{
		auth.POST("/login", h.Login)
		auth.POST("/logout", h.Logout)
		auth.GET("/me", h.GetUserInfo)
		auth.POST("/refresh", h.RefreshToken)
		auth.POST("/change-password", h.ChangePassword)
	}
}

// getUserRole 根据用户类型获取角色字符串
func getUserRole(userType int8) string {
	switch userType {
	case 1:
		return "auctioneer"
	case 2:
		return "buyer"
	case 3:
		return "admin"
	case 4:
		return "inspector"
	default:
		return "user"
	}
}
