package api

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/putonghao/flower-auction/internal/service"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService service.PaymentService
}

// NewPaymentHandler 创建支付处理器实例
func NewPaymentHandler() *PaymentHandler {
	return &PaymentHandler{
		paymentService: service.NewPaymentService(),
	}
}

// RegisterRoutes 注册路由
func (h *PaymentHandler) RegisterRoutes(r *gin.Engine) {
	// 支付路由组
	payment := r.Group("/api/v1/payment")
	payment.Use(middleware.JWTAuth())
	{
		payment.POST("/alipay", h.CreateAlipayOrder)            // 创建支付宝支付订单
		payment.POST("/wechat", h.CreateWechatOrder)            // 创建微信支付订单
		payment.POST("/bank", h.CreateBankOrder)                // 创建银行卡支付订单
		payment.GET("/status/:paymentNo", h.QueryPaymentStatus) // 查询支付状态
		payment.POST("/refund", h.RefundPayment)                // 申请退款
	}

	// 支付记录管理路由组 (管理员功能)
	records := r.Group("/api/v1/payment/records")
	records.Use(middleware.JWTAuth(), middleware.RequireRole(1))
	{
		records.GET("", h.ListPaymentRecords)         // 获取支付记录列表
		records.GET("/:id", h.GetPaymentRecord)       // 获取支付记录详情
		records.PUT("/:id", h.UpdatePaymentRecord)    // 更新支付记录
		records.DELETE("/:id", h.DeletePaymentRecord) // 删除支付记录
	}

	// 支付回调路由（不需要认证）
	notify := r.Group("/api/v1/notify")
	{
		notify.POST("/alipay", h.AlipayNotify) // 支付宝支付回调
		notify.POST("/wechat", h.WechatNotify) // 微信支付回调
	}
}

// CreateAlipayOrderRequest 创建支付宝支付订单请求
type CreateAlipayOrderRequest struct {
	OrderID int64   `json:"orderId" binding:"required"`
	Amount  float64 `json:"amount" binding:"required,gt=0"`
	Subject string  `json:"subject" binding:"required"`
}

// CreateAlipayOrder 创建支付宝支付订单
func (h *PaymentHandler) CreateAlipayOrder(c *gin.Context) {
	var req CreateAlipayOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	paymentOrder, err := h.paymentService.CreateAlipayOrder(c.Request.Context(), req.OrderID, req.Amount, req.Subject)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		if err == service.ErrInvalidOperation {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "订单状态不允许支付"})
			return
		}
		if err == service.ErrInvalidAmount {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付金额与订单金额不符"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, paymentOrder)
}

// CreateWechatOrderRequest 创建微信支付订单请求
type CreateWechatOrderRequest struct {
	OrderID int64   `json:"orderId" binding:"required"`
	Amount  float64 `json:"amount" binding:"required,gt=0"`
	Subject string  `json:"subject" binding:"required"`
}

// CreateWechatOrder 创建微信支付订单
func (h *PaymentHandler) CreateWechatOrder(c *gin.Context) {
	var req CreateWechatOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	paymentOrder, err := h.paymentService.CreateWechatOrder(c.Request.Context(), req.OrderID, req.Amount, req.Subject)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		if err == service.ErrInvalidOperation {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "订单状态不允许支付"})
			return
		}
		if err == service.ErrInvalidAmount {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付金额与订单金额不符"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, paymentOrder)
}

// CreateBankOrderRequest 创建银行卡支付订单请求
type CreateBankOrderRequest struct {
	OrderID  int64   `json:"orderId" binding:"required"`
	Amount   float64 `json:"amount" binding:"required,gt=0"`
	BankCard string  `json:"bankCard" binding:"required"`
}

// CreateBankOrder 创建银行卡支付订单
func (h *PaymentHandler) CreateBankOrder(c *gin.Context) {
	var req CreateBankOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	paymentOrder, err := h.paymentService.CreateBankOrder(c.Request.Context(), req.OrderID, req.Amount, req.BankCard)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		if err == service.ErrInvalidOperation {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "订单状态不允许支付"})
			return
		}
		if err == service.ErrInvalidAmount {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付金额与订单金额不符"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, paymentOrder)
}

// QueryPaymentStatus 查询支付状态
func (h *PaymentHandler) QueryPaymentStatus(c *gin.Context) {
	paymentNo := strings.TrimSpace(c.Param("paymentNo"))
	if paymentNo == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付流水号不能为空"})
		return
	}

	status, err := h.paymentService.QueryPaymentStatus(c.Request.Context(), paymentNo)
	if err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, status)
}

// RefundPaymentRequest 申请退款请求
type RefundPaymentRequest struct {
	PaymentNo    string  `json:"paymentNo" binding:"required"`
	RefundAmount float64 `json:"refundAmount" binding:"required,gt=0"`
	Reason       string  `json:"reason" binding:"required"`
}

// RefundPayment 申请退款
func (h *PaymentHandler) RefundPayment(c *gin.Context) {
	var req RefundPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	refund, err := h.paymentService.RefundPayment(c.Request.Context(), req.PaymentNo, req.RefundAmount, req.Reason)
	if err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, refund)
}

// AlipayNotify 支付宝支付回调
func (h *PaymentHandler) AlipayNotify(c *gin.Context) {
	// 获取所有POST参数
	params := make(map[string]string)
	for key, values := range c.Request.PostForm {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}

	if err := h.paymentService.AlipayNotify(c.Request.Context(), params); err != nil {
		c.String(http.StatusBadRequest, "fail")
		return
	}

	c.String(http.StatusOK, "success")
}

// WechatNotify 微信支付回调
func (h *PaymentHandler) WechatNotify(c *gin.Context) {
	// 获取所有POST参数
	params := make(map[string]string)
	for key, values := range c.Request.PostForm {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}

	if err := h.paymentService.WechatNotify(c.Request.Context(), params); err != nil {
		c.String(http.StatusBadRequest, "fail")
		return
	}

	c.String(http.StatusOK, "success")
}

// ListPaymentRecords 获取支付记录列表
func (h *PaymentHandler) ListPaymentRecords(c *gin.Context) {
	page, size := ParsePagination(c)

	// 解析查询参数
	query := &model.PaymentRecordQuery{
		Page: page,
		Size: size,
	}

	if paymentNo := c.Query("paymentNo"); paymentNo != "" {
		query.PaymentNo = paymentNo
	}

	if orderNo := c.Query("orderNo"); orderNo != "" {
		query.OrderNo = orderNo
	}

	if userIDStr := c.Query("userId"); userIDStr != "" {
		if userID, err := strconv.ParseInt(userIDStr, 10, 64); err == nil {
			query.UserID = &userID
		}
	}

	if methodStr := c.Query("paymentMethod"); methodStr != "" {
		if method, err := strconv.Atoi(methodStr); err == nil && method >= 1 && method <= 3 {
			methodVal := int8(method)
			query.PaymentMethod = &methodVal
		}
	}

	if statusStr := c.Query("status"); statusStr != "" {
		if status, err := strconv.Atoi(statusStr); err == nil && status >= 0 && status <= 2 {
			statusVal := int8(status)
			query.Status = &statusVal
		}
	}

	if startDate := c.Query("startDate"); startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			query.StartDate = date
		}
	}

	if endDate := c.Query("endDate"); endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			query.EndDate = date.Add(24 * time.Hour) // 包含结束日期当天
		}
	}

	records, total, err := h.paymentService.ListPaymentRecords(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"records": records,
		"total":   total,
		"page":    page,
		"size":    size,
	})
}

// GetPaymentRecord 获取支付记录详情
func (h *PaymentHandler) GetPaymentRecord(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	record, err := h.paymentService.GetPaymentRecord(c.Request.Context(), id)
	if err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, record)
}

// UpdatePaymentRecordRequest 更新支付记录请求
type UpdatePaymentRecordRequest struct {
	Status *int8    `json:"status"`
	Amount *float64 `json:"amount"`
}

// UpdatePaymentRecord 更新支付记录
func (h *PaymentHandler) UpdatePaymentRecord(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdatePaymentRecordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	updates := &model.PaymentRecordUpdate{
		Status: req.Status,
		Amount: req.Amount,
	}

	if err := h.paymentService.UpdatePaymentRecord(c.Request.Context(), id, updates); err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "支付记录更新成功"})
}

// DeletePaymentRecord 删除支付记录
func (h *PaymentHandler) DeletePaymentRecord(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.paymentService.DeletePaymentRecord(c.Request.Context(), id); err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "支付记录删除成功"})
}
