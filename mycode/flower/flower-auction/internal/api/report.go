package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/model"
)

// ReportAPI 报表API
type ReportAPI struct {
	reportService model.ReportService
}

// NewReportAPI 创建报表API实例
func NewReportAPI(reportService model.ReportService) *ReportAPI {
	return &ReportAPI{
		reportService: reportService,
	}
}

// RegisterRoutes 注册报表路由
func (api *ReportAPI) RegisterRoutes(r *gin.RouterGroup) {
	reports := r.Group("/reports")
	{
		// 销售报表
		reports.GET("/sales", api.GetSalesReport)
		reports.GET("/sales/trend", api.GetSalesTrend)
		reports.GET("/sales/products", api.GetProductSalesRank)
		reports.GET("/sales/channels", api.GetSalesChannelDistribution)

		// 用户报表
		reports.GET("/users", api.GetUserReport)
		reports.GET("/users/growth", api.GetUserGrowthTrend)
		reports.GET("/users/distribution", api.GetUserDistribution)
		reports.GET("/users/activity", api.GetUserActivityRank)

		// 商品报表
		reports.GET("/products", api.GetProductReport)
		reports.GET("/products/categories", api.GetCategorySales)
		reports.GET("/products/performance", api.GetProductPerformance)
		reports.GET("/products/price-distribution", api.GetPriceDistribution)

		// 拍卖报表
		reports.GET("/auctions", api.GetAuctionReport)
		reports.GET("/auctions/trend", api.GetAuctionTrend)
		reports.GET("/auctions/performance", api.GetAuctionPerformance)
		reports.GET("/auctions/status", api.GetAuctionStatusDistribution)

		// 导出功能
		reports.GET("/export/:type", api.ExportReport)
	}
}

// GetSalesReport 获取销售报表
func (api *ReportAPI) GetSalesReport(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	granularity := c.DefaultQuery("granularity", "day")

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	report, err := api.reportService.GetSalesReport(c.Request.Context(), start, end, granularity)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// GetSalesTrend 获取销售趋势
func (api *ReportAPI) GetSalesTrend(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	granularity := c.DefaultQuery("granularity", "day")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	trend, err := api.reportService.GetSalesTrend(c.Request.Context(), start, end, granularity)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    trend,
	})
}

// GetProductSalesRank 获取商品销售排行
func (api *ReportAPI) GetProductSalesRank(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	limitStr := c.DefaultQuery("limit", "10")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	ranks, err := api.reportService.GetProductSalesRank(c.Request.Context(), start, end, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    ranks,
	})
}

// GetSalesChannelDistribution 获取销售渠道分布
func (api *ReportAPI) GetSalesChannelDistribution(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	distribution, err := api.reportService.GetSalesChannelDistribution(c.Request.Context(), start, end)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    distribution,
	})
}

// GetUserReport 获取用户报表
func (api *ReportAPI) GetUserReport(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	userType := c.DefaultQuery("user_type", "all")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	report, err := api.reportService.GetUserReport(c.Request.Context(), start, end, userType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// GetUserGrowthTrend 获取用户增长趋势
func (api *ReportAPI) GetUserGrowthTrend(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	trend, err := api.reportService.GetUserGrowthTrend(c.Request.Context(), start, end)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    trend,
	})
}

// GetUserDistribution 获取用户分布
func (api *ReportAPI) GetUserDistribution(c *gin.Context) {
	distribution, err := api.reportService.GetUserDistribution(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    distribution,
	})
}

// GetUserActivityRank 获取用户活跃度排行
func (api *ReportAPI) GetUserActivityRank(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "20")
	userType := c.DefaultQuery("user_type", "all")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	ranks, err := api.reportService.GetUserActivityRank(c.Request.Context(), userType, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    ranks,
	})
}

// GetProductReport 获取商品报表
func (api *ReportAPI) GetProductReport(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	category := c.DefaultQuery("category", "all")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	report, err := api.reportService.GetProductReport(c.Request.Context(), start, end, category)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// GetCategorySales 获取分类销售数据
func (api *ReportAPI) GetCategorySales(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	sales, err := api.reportService.GetCategorySales(c.Request.Context(), start, end)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    sales,
	})
}

// GetProductPerformance 获取商品性能数据
func (api *ReportAPI) GetProductPerformance(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	limitStr := c.DefaultQuery("limit", "20")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	performance, err := api.reportService.GetProductPerformance(c.Request.Context(), start, end, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    performance,
	})
}

// GetPriceDistribution 获取价格分布
func (api *ReportAPI) GetPriceDistribution(c *gin.Context) {
	distribution, err := api.reportService.GetPriceDistribution(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    distribution,
	})
}

// GetAuctionReport 获取拍卖报表
func (api *ReportAPI) GetAuctionReport(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	auctionType := c.DefaultQuery("auction_type", "all")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	report, err := api.reportService.GetAuctionReport(c.Request.Context(), start, end, auctionType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// GetAuctionTrend 获取拍卖趋势
func (api *ReportAPI) GetAuctionTrend(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	trend, err := api.reportService.GetAuctionTrend(c.Request.Context(), start, end)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    trend,
	})
}

// GetAuctionPerformance 获取拍卖性能数据
func (api *ReportAPI) GetAuctionPerformance(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	limitStr := c.DefaultQuery("limit", "20")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	performance, err := api.reportService.GetAuctionPerformance(c.Request.Context(), start, end, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    performance,
	})
}

// GetAuctionStatusDistribution 获取拍卖状态分布
func (api *ReportAPI) GetAuctionStatusDistribution(c *gin.Context) {
	distribution, err := api.reportService.GetAuctionStatusDistribution(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    distribution,
	})
}

// ExportReport 导出报表
func (api *ReportAPI) ExportReport(c *gin.Context) {
	reportType := c.Param("type")
	format := c.DefaultQuery("format", "excel")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format"})
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format"})
		return
	}

	fileData, filename, err := api.reportService.ExportReport(c.Request.Context(), reportType, format, start, end)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/octet-stream")
	c.Data(http.StatusOK, "application/octet-stream", fileData)
}
