package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/service"
)

// UploadHandler 文件上传接口处理器
type UploadHandler struct {
	uploadService service.UploadService
}

// NewUploadHandler 创建文件上传接口处理器
func NewUploadHandler() *UploadHandler {
	return &UploadHandler{
		uploadService: service.NewUploadService(),
	}
}

// RegisterRoutes 注册路由
func (h *UploadHandler) RegisterRoutes(r *gin.Engine) {
	upload := r.Group("/api/v1/upload")
	upload.Use(middleware.JWTAuth())
	{
		upload.POST("/image", h.UploadImage)                    // 上传单张图片
		upload.POST("/images", h.UploadImages)                  // 上传多张图片
		upload.POST("/document", h.UploadDocument)              // 上传文档
		upload.DELETE("/file", h.DeleteFile)                   // 删除文件
		upload.GET("/file/info", h.GetFileInfo)                // 获取文件信息
	}
}

// UploadImageResponse 上传图片响应
type UploadImageResponse struct {
	URL      string `json:"url"`
	Filename string `json:"filename"`
	Size     int64  `json:"size"`
}

// UploadImage 上传单张图片
// @Summary 上传图片
// @Description 上传单张图片文件
// @Tags 文件上传
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "图片文件"
// @Param folder formData string false "文件夹名称" default("images")
// @Success 200 {object} UploadImageResponse "上传成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 413 {object} ErrorResponse "文件过大"
// @Failure 415 {object} ErrorResponse "不支持的文件类型"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/upload/image [post]
func (h *UploadHandler) UploadImage(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "请选择要上传的文件"})
		return
	}

	// 获取文件夹参数
	folder := c.DefaultPostForm("folder", "images")

	// 上传文件
	url, err := h.uploadService.UploadImage(c.Request.Context(), file, folder)
	if err != nil {
		if err == service.ErrInvalidFileType {
			c.JSON(http.StatusUnsupportedMediaType, ErrorResponse{Error: "不支持的文件类型"})
			return
		}
		if err == service.ErrFileTooLarge {
			c.JSON(http.StatusRequestEntityTooLarge, ErrorResponse{Error: "文件大小超限"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, UploadImageResponse{
		URL:      url,
		Filename: file.Filename,
		Size:     file.Size,
	})
}

// UploadImagesResponse 上传多张图片响应
type UploadImagesResponse struct {
	URLs     []string `json:"urls"`
	Success  int      `json:"success"`
	Failed   int      `json:"failed"`
	Total    int      `json:"total"`
}

// UploadImages 上传多张图片
// @Summary 上传多张图片
// @Description 批量上传图片文件
// @Tags 文件上传
// @Accept multipart/form-data
// @Produce json
// @Param files formData file true "图片文件" multiple
// @Param folder formData string false "文件夹名称" default("images")
// @Success 200 {object} UploadImagesResponse "上传成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/upload/images [post]
func (h *UploadHandler) UploadImages(c *gin.Context) {
	// 获取上传的文件
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "请选择要上传的文件"})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "请选择要上传的文件"})
		return
	}

	// 获取文件夹参数
	folder := c.DefaultPostForm("folder", "images")

	// 批量上传文件
	urls, err := h.uploadService.UploadImages(c.Request.Context(), files, folder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 统计上传结果
	success := 0
	for _, url := range urls {
		if url != "" {
			success++
		}
	}

	c.JSON(http.StatusOK, UploadImagesResponse{
		URLs:    urls,
		Success: success,
		Failed:  len(files) - success,
		Total:   len(files),
	})
}

// UploadDocumentResponse 上传文档响应
type UploadDocumentResponse struct {
	URL      string `json:"url"`
	Filename string `json:"filename"`
	Size     int64  `json:"size"`
}

// UploadDocument 上传文档
// @Summary 上传文档
// @Description 上传文档文件
// @Tags 文件上传
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "文档文件"
// @Param folder formData string false "文件夹名称" default("documents")
// @Success 200 {object} UploadDocumentResponse "上传成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 413 {object} ErrorResponse "文件过大"
// @Failure 415 {object} ErrorResponse "不支持的文件类型"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/upload/document [post]
func (h *UploadHandler) UploadDocument(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "请选择要上传的文件"})
		return
	}

	// 获取文件夹参数
	folder := c.DefaultPostForm("folder", "documents")

	// 上传文件
	url, err := h.uploadService.UploadDocument(c.Request.Context(), file, folder)
	if err != nil {
		if err == service.ErrInvalidFileType {
			c.JSON(http.StatusUnsupportedMediaType, ErrorResponse{Error: "不支持的文件类型"})
			return
		}
		if err == service.ErrFileTooLarge {
			c.JSON(http.StatusRequestEntityTooLarge, ErrorResponse{Error: "文件大小超限"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, UploadDocumentResponse{
		URL:      url,
		Filename: file.Filename,
		Size:     file.Size,
	})
}

// DeleteFileRequest 删除文件请求
type DeleteFileRequest struct {
	URL string `json:"url" binding:"required"`
}

// DeleteFile 删除文件
// @Summary 删除文件
// @Description 删除已上传的文件
// @Tags 文件上传
// @Accept json
// @Produce json
// @Param request body DeleteFileRequest true "文件URL"
// @Success 200 {object} SuccessResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/upload/file [delete]
func (h *UploadHandler) DeleteFile(c *gin.Context) {
	var req DeleteFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.uploadService.DeleteFile(c.Request.Context(), req.URL); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "文件删除成功"})
}

// GetFileInfo 获取文件信息
// @Summary 获取文件信息
// @Description 获取文件的详细信息
// @Tags 文件上传
// @Accept json
// @Produce json
// @Param url query string true "文件URL"
// @Success 200 {object} map[string]interface{} "文件信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/upload/file/info [get]
func (h *UploadHandler) GetFileInfo(c *gin.Context) {
	url := c.Query("url")
	if url == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "请提供文件URL"})
		return
	}

	info, err := h.uploadService.GetFileInfo(c.Request.Context(), url)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, info)
}

// ProductUploadHandler 商品相关文件上传处理器
type ProductUploadHandler struct {
	productEnhancedService service.ProductEnhancedService
}

// NewProductUploadHandler 创建商品文件上传处理器
func NewProductUploadHandler() *ProductUploadHandler {
	return &ProductUploadHandler{
		productEnhancedService: service.NewProductEnhancedService(),
	}
}

// RegisterProductUploadRoutes 注册商品上传路由
func (h *ProductUploadHandler) RegisterProductUploadRoutes(r *gin.Engine) {
	productUpload := r.Group("/api/v1/products")
	productUpload.Use(middleware.JWTAuth())
	{
		productUpload.POST("/:id/images", h.UploadProductImages)           // 上传商品图片
		productUpload.GET("/:id/images", h.GetProductImages)              // 获取商品图片
		productUpload.DELETE("/images/:imageId", h.DeleteProductImage)    // 删除商品图片
		productUpload.PUT("/images/:imageId/order", h.UpdateImageOrder)   // 更新图片排序
	}
}

// UploadProductImages 上传商品图片
func (h *ProductUploadHandler) UploadProductImages(c *gin.Context) {
	productID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 获取图片类型参数
	imageType, _ := strconv.Atoi(c.DefaultPostForm("imageType", "0"))

	// 获取上传的文件
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "请选择要上传的文件"})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "请选择要上传的文件"})
		return
	}

	// 上传商品图片
	urls, err := h.productEnhancedService.UploadProductImages(c.Request.Context(), productID, files, int8(imageType))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"urls":    urls,
		"success": len(urls),
		"total":   len(files),
	})
}

// GetProductImages 获取商品图片
func (h *ProductUploadHandler) GetProductImages(c *gin.Context) {
	productID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 获取图片类型参数
	imageType, _ := strconv.Atoi(c.DefaultQuery("imageType", "-1"))

	images, err := h.productEnhancedService.GetProductImages(c.Request.Context(), productID, int8(imageType))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, images)
}

// DeleteProductImage 删除商品图片
func (h *ProductUploadHandler) DeleteProductImage(c *gin.Context) {
	imageID, err := ParseParamID(c, "imageId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.productEnhancedService.DeleteProductImage(c.Request.Context(), imageID); err != nil {
		if err == service.ErrProductImageNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "商品图片不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "图片删除成功"})
}

// UpdateImageOrderRequest 更新图片排序请求
type UpdateImageOrderRequest struct {
	SortOrder int `json:"sortOrder" binding:"required"`
}

// UpdateImageOrder 更新图片排序
func (h *ProductUploadHandler) UpdateImageOrder(c *gin.Context) {
	imageID, err := ParseParamID(c, "imageId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateImageOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.productEnhancedService.UpdateImageOrder(c.Request.Context(), imageID, req.SortOrder); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "图片排序更新成功"})
}
