package api

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/websocket"
)

// AuctionWebSocketHandler 拍卖WebSocket处理器
type AuctionWebSocketHandler struct {
	auctionDAO dao.AuctionDAO
	wsHub      *websocket.Hub
}

// NewAuctionWebSocketHandler 创建拍卖WebSocket处理器
func NewAuctionWebSocketHandler(wsHub *websocket.Hub) *AuctionWebSocketHandler {
	return &AuctionWebSocketHandler{
		auctionDAO: dao.NewAuctionDAO(),
		wsHub:      wsHub,
	}
}

// GetAuctionList 获取拍卖列表
func (h *AuctionWebSocketHandler) GetAuctionList(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>uer<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	status := c.Query("status")

	// 构建查询参数
	params := dao.AuctionQueryParams{
		Page:     page,
		PageSize: pageSize,
	}

	// 如果有状态参数，转换为int8指针
	if status != "" {
		if statusInt, err := strconv.Atoi(status); err == nil {
			statusInt8 := int8(statusInt)
			params.Status = &statusInt8
		}
	}

	ctx := context.Background()
	auctions, total, err := h.auctionDAO.ListAuctionsWithFilter(ctx, params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取拍卖列表失败",
			"error":   err.Error(),
		})
		return
	}

	// 转换为响应格式
	var auctionList []gin.H
	for _, auction := range auctions {
		auctionList = append(auctionList, gin.H{
			"id":               auction.ID,
			"title":            auction.Name,
			"description":      auction.Description,
			"status":           auction.Status,
			"startTime":        auction.StartTime,
			"endTime":          auction.EndTime,
			"timeRemaining":    calculateTimeRemaining(auction.EndTime),
			"participantCount": h.wsHub.GetAuctionParticipantCount(strconv.FormatInt(auction.ID, 10)),
			"createdAt":        auction.CreatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"auctions": auctionList,
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// GetAuctionDetail 获取拍卖详情
func (h *AuctionWebSocketHandler) GetAuctionDetail(c *gin.Context) {
	auctionIDStr := c.Param("id")
	auctionID, err := strconv.ParseInt(auctionIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的拍卖ID",
		})
		return
	}

	ctx := context.Background()
	auction, err := h.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil || auction == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "拍卖不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"id":               auction.ID,
			"title":            auction.Name,
			"description":      auction.Description,
			"status":           auction.Status,
			"startTime":        auction.StartTime,
			"endTime":          auction.EndTime,
			"timeRemaining":    calculateTimeRemaining(auction.EndTime),
			"participantCount": h.wsHub.GetAuctionParticipantCount(auctionIDStr),
			"location":         auction.Location,
			"createdAt":        auction.CreatedAt,
		},
	})
}

// StartAuction 开始拍卖
func (h *AuctionWebSocketHandler) StartAuction(c *gin.Context) {
	auctionIDStr := c.Param("id")
	auctionID, err := strconv.ParseInt(auctionIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的拍卖ID",
		})
		return
	}

	ctx := context.Background()
	auction, err := h.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil || auction == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "拍卖不存在",
		})
		return
	}

	// 更新拍卖状态为进行中
	auction.Status = 1 // 1-进行中
	err = h.auctionDAO.UpdateAuction(ctx, auction)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "开始拍卖失败",
			"error":   err.Error(),
		})
		return
	}

	// 通过WebSocket广播拍卖开始
	h.wsHub.BroadcastToAuction(auctionIDStr, "auction_started", gin.H{
		"auctionId": auctionIDStr,
		"startTime": time.Now().Format(time.RFC3339),
		"status":    1,
	})

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "拍卖已开始",
	})
}

// EndAuction 结束拍卖
func (h *AuctionWebSocketHandler) EndAuction(c *gin.Context) {
	auctionIDStr := c.Param("id")
	auctionID, err := strconv.ParseInt(auctionIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的拍卖ID",
		})
		return
	}

	ctx := context.Background()
	auction, err := h.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil || auction == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "拍卖不存在",
		})
		return
	}

	// 更新拍卖状态为已结束
	auction.Status = 2 // 2-已结束
	err = h.auctionDAO.UpdateAuction(ctx, auction)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "结束拍卖失败",
			"error":   err.Error(),
		})
		return
	}

	// 通过WebSocket广播拍卖结束
	h.wsHub.BroadcastToAuction(auctionIDStr, "auction_ended", gin.H{
		"auctionId": auctionIDStr,
		"endTime":   time.Now().Format(time.RFC3339),
		"status":    2,
	})

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "拍卖已结束",
	})
}

// RegisterRoutes 注册路由
func (h *AuctionWebSocketHandler) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api/v1")
	{
		// 使用不同的路由前缀避免冲突
		wsAuctions := api.Group("/ws-auctions")
		{
			wsAuctions.GET("/", h.GetAuctionList)
			wsAuctions.GET("/:id", h.GetAuctionDetail)
			wsAuctions.POST("/:id/start", h.StartAuction)
			wsAuctions.POST("/:id/end", h.EndAuction)
		}
	}
}

// calculateTimeRemaining 计算剩余时间（秒）
func calculateTimeRemaining(endTime time.Time) int64 {
	remaining := endTime.Sub(time.Now()).Seconds()
	if remaining < 0 {
		return 0
	}
	return int64(remaining)
}
