package main

import (
	"flag"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/shared/config"
	"github.com/putonghao/flower-auction/internal/shared/database"
	"github.com/putonghao/flower-auction/internal/shared/middleware"
	"github.com/putonghao/flower-auction/internal/shared/response"
	"github.com/putonghao/flower-auction/internal/user"
)

func main() {
	// 解析命令行参数
	var configPath = flag.String("config", "configs/user-service.yaml", "配置文件路径")
	flag.Parse()

	// 初始化配置
	if err := config.Init(*configPath); err != nil {
		log.Fatalf("Failed to initialize config: %v", err)
	}

	// 初始化数据库
	if err := database.InitDatabases(); err != nil {
		log.Fatalf("Failed to initialize databases: %v", err)
	}
	defer database.Close()

	// 创建用户仓库
	userRepo := user.NewRepository(database.GetUserDB())

	// 创建用户服务
	userService := user.NewService(userRepo)

	// 创建HTTP处理器
	userHandler := user.NewHandler(userService)

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.CORS())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		response.Success(c, gin.H{
			"service": "user-service",
			"status":  "healthy",
			"time":    time.Now(),
		})
	})

	// 注册用户路由
	userHandler.RegisterRoutes(r)

	// 启动服务器
	serverConfig := config.GetServer()
	server := &http.Server{
		Addr:         ":" + string(rune(serverConfig.Port)),
		Handler:      r,
		ReadTimeout:  time.Duration(serverConfig.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(serverConfig.WriteTimeout) * time.Second,
	}

	log.Printf("User service starting on port %d", serverConfig.Port)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Failed to start server: %v", err)
	}
}
