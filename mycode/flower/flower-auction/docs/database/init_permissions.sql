-- 花卉拍卖系统权限初始化SQL
-- 创建时间: 2024-12-04
-- 描述: 初始化系统权限和角色权限分配

USE user_db;

-- 清理现有权限数据（可选，谨慎使用）
-- DELETE FROM role_permission;
-- DELETE FROM permission;

-- 初始化权限数据
-- 用户管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(1, '用户管理', 'USER_MANAGE', '用户管理模块', 'user', 'manage', 'user', 1),
(2, '用户列表', 'USER_LIST', '查看用户列表', 'user', 'list', 'user', 1),
(3, '用户创建', 'USER_CREATE', '创建用户', 'user', 'create', 'user', 1),
(4, '用户编辑', 'USER_EDIT', '编辑用户', 'user', 'edit', 'user', 1),
(5, '用户删除', 'USER_DELETE', '删除用户', 'user', 'delete', 'user', 1),
(6, '用户导出', 'USER_EXPORT', '导出用户数据', 'user', 'export', 'user', 1),
(30, '查看用户', 'user:view', '查看用户信息', 'user', 'view', 'user', 1),
(31, '创建用户', 'user:create', '创建新用户', 'user', 'create', 'user', 1),
(32, '编辑用户', 'user:edit', '编辑用户信息', 'user', 'edit', 'user', 1),
(33, '删除用户', 'user:delete', '删除用户', 'user', 'delete', 'user', 1),
(34, '管理用户状态', 'user:status', '启用/禁用用户', 'user', 'status', 'user', 1);

-- 角色管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(7, '角色管理', 'ROLE_MANAGE', '角色管理模块', 'role', 'manage', 'role', 1),
(8, '角色列表', 'ROLE_LIST', '查看角色列表', 'role', 'list', 'role', 1),
(9, '角色创建', 'ROLE_CREATE', '创建角色', 'role', 'create', 'role', 1),
(10, '角色编辑', 'ROLE_EDIT', '编辑角色', 'role', 'edit', 'role', 1),
(11, '角色删除', 'ROLE_DELETE', '删除角色', 'role', 'delete', 'role', 1),
(12, '权限分配', 'ROLE_PERMISSION', '分配角色权限', 'role', 'permission', 'role', 1),
(35, '查看角色', 'role:view', '查看角色信息', 'role', 'view', 'role', 1),
(36, '创建角色', 'role:create', '创建新角色', 'role', 'create', 'role', 1),
(37, '编辑角色', 'role:edit', '编辑角色信息', 'role', 'edit', 'role', 1),
(38, '删除角色', 'role:delete', '删除角色', 'role', 'delete', 'role', 1),
(39, '分配权限', 'role:permission', '为角色分配权限', 'role', 'permission', 'role', 1);

-- 商品管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(13, '商品管理', 'PRODUCT_MANAGE', '商品管理模块', 'product', 'manage', 'product', 1),
(14, '商品列表', 'PRODUCT_LIST', '查看商品列表', 'product', 'list', 'product', 1),
(15, '商品创建', 'PRODUCT_CREATE', '创建商品', 'product', 'create', 'product', 1),
(16, '商品编辑', 'PRODUCT_EDIT', '编辑商品', 'product', 'edit', 'product', 1),
(17, '商品删除', 'PRODUCT_DELETE', '删除商品', 'product', 'delete', 'product', 1),
(18, '商品审核', 'PRODUCT_AUDIT', '审核商品', 'product', 'audit', 'product', 1),
(40, '查看商品', 'product:view', '查看商品信息', 'product', 'view', 'product', 1),
(41, '创建商品', 'product:create', '创建新商品', 'product', 'create', 'product', 1),
(42, '编辑商品', 'product:edit', '编辑商品信息', 'product', 'edit', 'product', 1),
(43, '删除商品', 'product:delete', '删除商品', 'product', 'delete', 'product', 1),
(44, '审核商品', 'product:audit', '审核商品', 'product', 'audit', 'product', 1);

-- 分类管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(19, '分类管理', 'CATEGORY_MANAGE', '分类管理模块', 'category', 'manage', 'category', 1),
(20, '分类列表', 'CATEGORY_LIST', '查看分类列表', 'category', 'list', 'category', 1),
(21, '分类创建', 'CATEGORY_CREATE', '创建分类', 'category', 'create', 'category', 1),
(22, '分类编辑', 'CATEGORY_EDIT', '编辑分类', 'category', 'edit', 'category', 1),
(23, '分类删除', 'CATEGORY_DELETE', '删除分类', 'category', 'delete', 'category', 1),
(66, '查看分类', 'category:view', '查看商品分类', 'category', 'view', 'category', 1),
(67, '创建分类', 'category:create', '创建商品分类', 'category', 'create', 'category', 1),
(68, '编辑分类', 'category:edit', '编辑分类信息', 'category', 'edit', 'category', 1),
(69, '删除分类', 'category:delete', '删除商品分类', 'category', 'delete', 'category', 1);

-- 拍卖管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(24, '拍卖管理', 'AUCTION_MANAGE', '拍卖管理模块', 'auction', 'manage', 'auction', 1),
(25, '拍卖列表', 'AUCTION_LIST', '查看拍卖列表', 'auction', 'list', 'auction', 1),
(26, '拍卖创建', 'AUCTION_CREATE', '创建拍卖', 'auction', 'create', 'auction', 1),
(27, '拍卖编辑', 'AUCTION_EDIT', '编辑拍卖', 'auction', 'edit', 'auction', 1),
(28, '拍卖删除', 'AUCTION_DELETE', '删除拍卖', 'auction', 'delete', 'auction', 1),
(29, '拍卖控制', 'AUCTION_CONTROL', '控制拍卖状态', 'auction', 'control', 'auction', 1),
(45, '查看拍卖', 'auction:view', '查看拍卖信息', 'auction', 'view', 'auction', 1),
(46, '创建拍卖', 'auction:create', '创建拍卖会', 'auction', 'create', 'auction', 1),
(47, '编辑拍卖', 'auction:edit', '编辑拍卖会信息', 'auction', 'edit', 'auction', 1),
(48, '删除拍卖', 'auction:delete', '删除拍卖会', 'auction', 'delete', 'auction', 1),
(49, '发布拍卖', 'auction:publish', '发布拍卖会', 'auction', 'publish', 'auction', 1),
(50, '取消拍卖', 'auction:cancel', '取消拍卖会', 'auction', 'cancel', 'auction', 1),
(51, '主持拍卖', 'auction:conduct', '主持拍卖会', 'auction', 'conduct', 'auction', 1),
(52, '控制拍卖', 'auction:control', '控制拍卖进程', 'auction', 'control', 'auction', 1),
(53, '钟号管理', 'auction:clock', '管理拍卖钟号', 'auction', 'clock', 'auction', 1),
(54, '竞价操作', 'auction:bid', '参与竞价', 'auction', 'bid', 'auction', 1),
(55, '确认竞价', 'auction:confirm_bid', '确认竞价结果', 'auction', 'confirm_bid', 'auction', 1);

-- 订单管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(56, '查看订单', 'order:view', '查看订单信息', 'order', 'view', 'order', 1),
(57, '处理订单', 'order:process', '处理订单', 'order', 'process', 'order', 1),
(58, '取消订单', 'order:cancel', '取消订单', 'order', 'cancel', 'order', 1),
(59, '退款处理', 'order:refund', '处理退款申请', 'order', 'refund', 'order', 1),
(60, '发货管理', 'order:ship', '管理订单发货', 'order', 'ship', 'order', 1);

-- 财务管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(61, '查看财务', 'finance:view', '查看财务信息', 'finance', 'view', 'finance', 1),
(62, '财务审核', 'finance:audit', '财务审核', 'finance', 'audit', 'finance', 1),
(63, '佣金管理', 'finance:commission', '管理拍卖佣金', 'finance', 'commission', 'finance', 1),
(64, '结算管理', 'finance:settlement', '管理资金结算', 'finance', 'settlement', 'finance', 1),
(65, '生成报表', 'finance:report', '生成财务报表', 'finance', 'report', 'finance', 1);

-- 报表统计模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(70, '销售报表', 'report:sales', '查看销售报表', 'report', 'sales', 'report', 1),
(71, '用户报表', 'report:user', '查看用户统计', 'report', 'user', 'report', 1),
(72, '拍卖报表', 'report:auction', '查看拍卖统计', 'report', 'auction', 'report', 1),
(73, '财务报表', 'report:finance', '查看财务报表', 'report', 'finance', 'report', 1),
(74, '导出报表', 'report:export', '导出报表数据', 'report', 'export', 'report', 1);

-- 系统管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(75, '系统配置', 'system:config', '系统配置管理', 'system', 'config', 'system', 1),
(76, '查看日志', 'system:log', '查看系统日志', 'system', 'log', 'system', 1),
(77, '系统监控', 'system:monitor', '系统监控', 'system', 'monitor', 'system', 1),
(78, '数据备份', 'system:backup', '数据备份与恢复', 'system', 'backup', 'system', 1);

-- 权限管理模块权限
INSERT IGNORE INTO `permission` (`id`, `name`, `code`, `description`, `module`, `action`, `resource`, `status`) VALUES
(79, '查看权限', 'permission:view', '查看权限列表', 'permission', 'view', 'permission', 1),
(80, '创建权限', 'permission:create', '创建新权限', 'permission', 'create', 'permission', 1),
(81, '编辑权限', 'permission:edit', '编辑权限信息', 'permission', 'edit', 'permission', 1),
(82, '删除权限', 'permission:delete', '删除权限', 'permission', 'delete', 'permission', 1);

-- ========================================
-- 角色权限分配
-- ========================================

-- 1. 系统管理员（角色ID: 1）- 拥有所有权限
DELETE FROM role_permission WHERE role_id = 1;
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10),
(1, 11), (1, 12), (1, 13), (1, 14), (1, 15), (1, 16), (1, 17), (1, 18), (1, 19), (1, 20),
(1, 21), (1, 22), (1, 23), (1, 24), (1, 25), (1, 26), (1, 27), (1, 28), (1, 29), (1, 30),
(1, 31), (1, 32), (1, 33), (1, 34), (1, 35), (1, 36), (1, 37), (1, 38), (1, 39), (1, 40),
(1, 41), (1, 42), (1, 43), (1, 44), (1, 45), (1, 46), (1, 47), (1, 48), (1, 49), (1, 50),
(1, 51), (1, 52), (1, 53), (1, 54), (1, 55), (1, 56), (1, 57), (1, 58), (1, 59), (1, 60),
(1, 61), (1, 62), (1, 63), (1, 64), (1, 65), (1, 66), (1, 67), (1, 68), (1, 69), (1, 70),
(1, 71), (1, 72), (1, 73), (1, 74), (1, 75), (1, 76), (1, 77), (1, 78), (1, 79), (1, 80),
(1, 81), (1, 82);

-- 2. 拍卖师（角色ID: 2）- 拍卖、商品、分类相关权限
DELETE FROM role_permission WHERE role_id = 2;
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
-- 商品管理权限
(2, 13), (2, 14), (2, 15), (2, 16), (2, 17), (2, 18), (2, 40), (2, 41), (2, 42), (2, 43), (2, 44),
-- 分类管理权限
(2, 19), (2, 20), (2, 21), (2, 22), (2, 23), (2, 66), (2, 67), (2, 68), (2, 69),
-- 拍卖管理权限
(2, 24), (2, 25), (2, 26), (2, 27), (2, 28), (2, 29), (2, 45), (2, 46), (2, 47), (2, 48),
(2, 49), (2, 50), (2, 51), (2, 52), (2, 53), (2, 54), (2, 55),
-- 订单查看权限
(2, 56), (2, 57),
-- 报表查看权限
(2, 70), (2, 71), (2, 72), (2, 74);

-- 3. 买家（角色ID: 3）- 基础查看和竞拍权限
DELETE FROM role_permission WHERE role_id = 3;
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
-- 商品查看权限
(3, 13), (3, 14), (3, 40),
-- 分类查看权限
(3, 19), (3, 20), (3, 66),
-- 拍卖查看和竞拍权限
(3, 24), (3, 25), (3, 45), (3, 54), (3, 55),
-- 订单查看权限
(3, 56), (3, 57);

-- 4. 质检员（角色ID: 4）- 商品质检相关权限
DELETE FROM role_permission WHERE role_id = 4;
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
-- 商品管理权限（重点是审核）
(4, 13), (4, 14), (4, 15), (4, 16), (4, 17), (4, 18), (4, 40), (4, 41), (4, 42), (4, 43), (4, 44),
-- 分类查看权限
(4, 19), (4, 20), (4, 66), (4, 67), (4, 68), (4, 69),
-- 拍卖查看权限
(4, 24), (4, 25), (4, 45), (4, 46);

-- 5. 显示屏操作员（角色ID: 5）- 大屏展示相关权限
DELETE FROM role_permission WHERE role_id = 5;
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
-- 商品查看权限
(5, 13), (5, 14), (5, 40),
-- 分类查看权限
(5, 19), (5, 20), (5, 66),
-- 拍卖查看权限
(5, 24), (5, 25), (5, 45),
-- 订单查看权限
(5, 56),
-- 报表查看权限
(5, 70), (5, 71), (5, 72), (5, 73), (5, 74);

-- 7. 财务官（角色ID: 7）- 财务相关权限
DELETE FROM role_permission WHERE role_id = 7;
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
-- 基础查看权限
(7, 1), (7, 2), (7, 3),
-- 拍卖查看权限
(7, 24), (7, 25), (7, 45), (7, 46),
-- 订单管理权限
(7, 56), (7, 57), (7, 58), (7, 59), (7, 60),
-- 财务管理权限
(7, 61), (7, 62), (7, 63), (7, 64), (7, 65),
-- 报表权限
(7, 70), (7, 71), (7, 72), (7, 73), (7, 74);

-- 8. 运营（角色ID: 8）- 拥有所有权限（超级管理员）
DELETE FROM role_permission WHERE role_id = 8;
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
(8, 1), (8, 2), (8, 3), (8, 4), (8, 5), (8, 6), (8, 7), (8, 8), (8, 9), (8, 10),
(8, 11), (8, 12), (8, 13), (8, 14), (8, 15), (8, 16), (8, 17), (8, 18), (8, 19), (8, 20),
(8, 21), (8, 22), (8, 23), (8, 24), (8, 25), (8, 26), (8, 27), (8, 28), (8, 29), (8, 30),
(8, 31), (8, 32), (8, 33), (8, 34), (8, 35), (8, 36), (8, 37), (8, 38), (8, 39), (8, 40),
(8, 41), (8, 42), (8, 43), (8, 44), (8, 45), (8, 46), (8, 47), (8, 48), (8, 49), (8, 50),
(8, 51), (8, 52), (8, 53), (8, 54), (8, 55), (8, 56), (8, 57), (8, 58), (8, 59), (8, 60),
(8, 61), (8, 62), (8, 63), (8, 64), (8, 65), (8, 66), (8, 67), (8, 68), (8, 69), (8, 70),
(8, 71), (8, 72), (8, 73), (8, 74), (8, 75), (8, 76), (8, 77), (8, 78), (8, 79), (8, 80),
(8, 81), (8, 82);
