-- 花卉拍卖系统初始数据
-- 创建时间: 2024-12-01
-- 描述: 系统初始化数据和测试数据

USE flower_auction;

-- 初始化钟号状态数据（1-12号钟）
INSERT IGNORE INTO `clock_status` (`clock_number`, `status`) VALUES
(1, 0), (2, 0), (3, 0), (4, 0), (5, 0), (6, 0),
(7, 0), (8, 0), (9, 0), (10, 0), (11, 0), (12, 0);

-- 初始化角色数据
INSERT IGNORE INTO `role` (`id`, `name`, `code`, `description`, `status`) VALUES
(1, '系统管理员', 'ADMIN', '系统管理员角色', 1),
(2, '拍卖师', 'AUCTIONEER', '拍卖师角色', 1),
(3, '买家', 'BUYER', '买家角色', 1),
(4, '质检员', 'QUALITY_INSPECTOR', '质检员角色', 1),
(7, '财务官', 'FINANCE', '财务官', 1),
(8, '运营', 'OPERATOR', '运营', 1);

-- 创建商品分类初始数据
INSERT IGNORE INTO `category` (`id`, `name`, `parent_id`, `level`, `sort_order`) VALUES
(1, '鲜切花', 0, 1, 1),
(2, '玫瑰', 1, 2, 1),
(3, '康乃馨', 1, 2, 2),
(4, '百合', 1, 2, 3),
(5, '菊花', 1, 2, 4),
(6, '郁金香', 1, 2, 5),
(7, '满天星', 1, 2, 6),
(8, '绿植', 0, 1, 2),
(9, '多肉植物', 8, 2, 1),
(10, '观叶植物', 8, 2, 2),
(11, '盆栽花卉', 0, 1, 3),
(12, '月季', 11, 2, 1),
(13, '茉莉', 11, 2, 2);

-- 创建示例商品数据
INSERT IGNORE INTO `product` (`id`, `name`, `category_id`, `description`, `unit`, `origin`, `variety`, `color`) VALUES
(1, '红玫瑰', 2, '优质红玫瑰，花朵饱满，颜色鲜艳', '支', '昆明', '卡罗拉', '红色'),
(2, '白玫瑰', 2, '纯白玫瑰，花型优美，适合婚庆', '支', '昆明', '雪山', '白色'),
(3, '粉玫瑰', 2, '粉色玫瑰，浪漫温馨', '支', '昆明', '戴安娜', '粉色'),
(4, '香槟玫瑰', 2, '香槟色玫瑰，高贵典雅', '支', '昆明', '香槟', '香槟色'),
(5, '红康乃馨', 3, '经典红色康乃馨，母亲节首选', '支', '昆明', '标准型', '红色'),
(6, '粉康乃馨', 3, '粉色康乃馨，温馨可爱', '支', '昆明', '标准型', '粉色'),
(7, '白百合', 4, '纯洁白百合，香气怡人', '支', '昆明', '东方百合', '白色'),
(8, '粉百合', 4, '粉色百合，优雅迷人', '支', '昆明', '东方百合', '粉色'),
(9, '黄菊花', 5, '金黄色菊花，寓意吉祥', '支', '昆明', '大菊', '黄色'),
(10, '白菊花', 5, '纯白菊花，清雅脱俗', '支', '昆明', '大菊', '白色'),
(11, '红郁金香', 6, '荷兰进口郁金香品种', '支', '荷兰', '达尔文', '红色'),
(12, '黄郁金香', 6, '明亮黄色郁金香', '支', '荷兰', '达尔文', '黄色'),
(13, '满天星', 7, '白色满天星，配花首选', '扎', '昆明', '标准型', '白色'),
(14, '粉满天星', 7, '粉色满天星，浪漫配花', '扎', '昆明', '标准型', '粉色'),
(15, '绿萝', 9, '室内净化空气植物', '盆', '广州', '黄金葛', '绿色');

-- 创建测试用户（密码都是123456，已加密）
INSERT IGNORE INTO `user` (`id`, `username`, `password`, `phone`, `real_name`, `user_type`, `company_name`, `balance`, `frozen_amount`) VALUES
(1, 'auctioneer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '13800001001', '张拍卖师', 1, '昆明花卉拍卖中心', 0.00, 0.00),
(2, 'buyer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '13800001002', '李购买商', 2, '云南花卉贸易公司', 50000.00, 5000.00),
(3, 'buyer02', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '13800001003', '王购买商', 2, '昆明花卉批发市场', 30000.00, 2000.00),
(4, 'admin01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '13800001004', '管理员', 3, '系统管理', 0.00, 0.00),
(5, 'buyer03', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '13800001005', '陈购买商', 2, '深圳花卉市场', 80000.00, 8000.00),
(6, 'buyer04', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '***********', '刘购买商', 2, '上海花卉集团', 120000.00, 12000.00),
(7, 'auctioneer02', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9P3.jkeExbNNJ4m', '***********', '赵拍卖师', 1, '昆明花卉拍卖中心', 0.00, 0.00);

-- 创建用户角色关联
INSERT IGNORE INTO `user_role` (`user_id`, `role_id`) VALUES
(1, 2), (2, 3), (3, 3), (4, 1), (5, 3), (6, 3), (7, 2);

-- 为测试用户创建账户
INSERT IGNORE INTO `account` (`user_id`, `account_type`, `balance`, `frozen_amount`) VALUES
(1, 1, 0.00, 0.00),
(2, 1, 50000.00, 5000.00),
(3, 1, 30000.00, 2000.00),
(4, 1, 0.00, 0.00),
(5, 1, 80000.00, 8000.00),
(6, 1, 120000.00, 12000.00),
(7, 1, 0.00, 0.00);

-- 创建示例拍卖会
INSERT IGNORE INTO `auction` (`id`, `name`, `description`, `start_time`, `status`, `total_items`) VALUES
(1, '2024年12月花卉拍卖会', '年末大型花卉拍卖会，品种丰富，质量上乘', '2024-12-01 09:00:00', 1, 50),
(2, '2024年12月精品花卉专场', '精选优质花卉，适合高端客户', '2024-12-02 14:00:00', 0, 30);

-- 创建示例拍卖商品
INSERT IGNORE INTO `auction_item` (`id`, `auction_id`, `product_id`, `batch_number`, `quantity`, `grade`, `start_price`, `step_price`, `current_price`, `clock_number`, `status`) VALUES
(1, 1, 1, 'B20241201001', 1000, 'A', 8.00, 0.50, 8.00, 1, 0),
(2, 1, 2, 'B20241201002', 800, 'A', 10.00, 0.50, 10.00, 2, 0),
(3, 1, 3, 'B20241201003', 1200, 'A', 9.00, 0.50, 9.00, 3, 0),
(4, 1, 4, 'B20241201004', 600, 'A', 12.00, 0.50, 12.00, 4, 0),
(5, 1, 5, 'B20241201005', 1500, 'A', 6.00, 0.30, 6.00, 5, 0),
(6, 1, 6, 'B20241201006', 1000, 'A', 7.00, 0.30, 7.00, 6, 0),
(7, 1, 7, 'B20241201007', 600, 'A', 15.00, 1.00, 15.00, 7, 0),
(8, 1, 8, 'B20241201008', 500, 'A', 18.00, 1.00, 18.00, 8, 0),
(9, 1, 9, 'B20241201009', 2000, 'A', 4.00, 0.20, 4.00, 9, 0),
(10, 1, 10, 'B20241201010', 1800, 'A', 5.00, 0.20, 5.00, 10, 0),
(11, 1, 11, 'B20241201011', 500, 'A', 12.00, 0.50, 12.00, 11, 0),
(12, 1, 12, 'B20241201012', 400, 'A', 14.00, 0.50, 14.00, 12, 0),
(13, 1, 13, 'B20241201013', 300, 'A', 8.00, 0.50, 8.00, 1, 0),
(14, 1, 14, 'B20241201014', 250, 'A', 10.00, 0.50, 10.00, 2, 0),
(15, 1, 15, 'B20241201015', 100, 'A', 25.00, 2.00, 25.00, 3, 0),
(16, 1, 1, 'B20241201016', 800, 'B', 7.00, 0.50, 7.00, 4, 0),
(17, 1, 2, 'B20241201017', 600, 'B', 9.00, 0.50, 9.00, 5, 0),
(18, 1, 3, 'B20241201018', 1000, 'B', 8.00, 0.50, 8.00, 6, 0),
(19, 1, 5, 'B20241201019', 1200, 'B', 5.50, 0.30, 5.50, 7, 0),
(20, 1, 7, 'B20241201020', 400, 'B', 13.00, 1.00, 13.00, 8, 0);

-- 创建一些示例竞价记录
INSERT IGNORE INTO `bid` (`auction_item_id`, `user_id`, `price`, `bid_time`) VALUES
(1, 2, 8.50, '2024-12-01 10:15:00'),
(1, 3, 9.00, '2024-12-01 10:16:00'),
(1, 5, 9.50, '2024-12-01 10:17:00'),
(1, 2, 10.00, '2024-12-01 10:18:00'),
(2, 2, 10.50, '2024-12-01 10:20:00'),
(2, 3, 11.00, '2024-12-01 10:21:00'),
(2, 6, 11.50, '2024-12-01 10:22:00'),
(3, 3, 9.50, '2024-12-01 10:25:00'),
(3, 5, 10.00, '2024-12-01 10:26:00'),
(4, 6, 12.50, '2024-12-01 10:30:00'),
(5, 2, 6.30, '2024-12-01 10:35:00'),
(5, 3, 6.60, '2024-12-01 10:36:00');

-- 创建一些关注记录
INSERT IGNORE INTO `watch_list` (`user_id`, `auction_item_id`) VALUES
(2, 1), (2, 2), (2, 5), (2, 7), (2, 10),
(3, 1), (3, 3), (3, 4), (3, 6), (3, 8),
(5, 2), (5, 4), (5, 6), (5, 9), (5, 11),
(6, 1), (6, 3), (6, 5), (6, 7), (6, 12);

-- 创建一些埋单记录
INSERT IGNORE INTO `pre_order` (`user_id`, `auction_item_id`, `price`, `quantity`) VALUES
(2, 1, 10.50, 500),
(3, 2, 12.00, 300),
(5, 3, 11.00, 600),
(6, 4, 15.00, 200),
(2, 5, 7.50, 800),
(3, 7, 18.00, 150);

-- 创建一些交易记录
INSERT IGNORE INTO `transaction` (`user_id`, `account_id`, `type`, `amount`, `balance_before`, `balance_after`, `description`) VALUES
(2, 2, 1, 50000.00, 0.00, 50000.00, '账户充值'),
(3, 3, 1, 30000.00, 0.00, 30000.00, '账户充值'),
(5, 5, 1, 80000.00, 0.00, 80000.00, '账户充值'),
(6, 6, 1, 120000.00, 0.00, 120000.00, '账户充值'),
(2, 2, 3, 5000.00, 50000.00, 45000.00, '竞价保证金冻结'),
(3, 3, 3, 2000.00, 30000.00, 28000.00, '竞价保证金冻结'),
(5, 5, 3, 8000.00, 80000.00, 72000.00, '竞价保证金冻结'),
(6, 6, 3, 12000.00, 120000.00, 108000.00, '竞价保证金冻结');

-- 创建视图：活跃拍卖商品
CREATE OR REPLACE VIEW `v_active_auction_items` AS
SELECT 
    ai.id,
    ai.batch_number,
    ai.quantity,
    ai.unit,
    ai.grade,
    ai.start_price,
    ai.current_price,
    ai.clock_number,
    ai.status,
    ai.start_time,
    ai.bid_count,
    p.name as product_name,
    p.origin,
    p.variety,
    p.color,
    c.name as category_name,
    a.name as auction_name
FROM auction_item ai
LEFT JOIN product p ON ai.product_id = p.id
LEFT JOIN category c ON p.category_id = c.id
LEFT JOIN auction a ON ai.auction_id = a.id
WHERE ai.status IN (0, 1, 4); -- 待拍、拍卖中、暂停

-- 创建视图：用户竞价统计
CREATE OR REPLACE VIEW `v_user_bid_stats` AS
SELECT 
    u.id as user_id,
    u.username,
    u.real_name,
    COUNT(b.id) as total_bids,
    COUNT(DISTINCT b.auction_item_id) as items_bid,
    COALESCE(MAX(b.price), 0) as max_bid_price,
    COALESCE(AVG(b.price), 0) as avg_bid_price,
    SUM(CASE WHEN b.is_winning = 1 THEN 1 ELSE 0 END) as winning_bids
FROM user u
LEFT JOIN bid b ON u.id = b.user_id
WHERE u.user_type = 2 -- 购买商
GROUP BY u.id, u.username, u.real_name;

-- 创建视图：钟号状态详情
CREATE OR REPLACE VIEW `v_clock_status_detail` AS
SELECT 
    cs.clock_number,
    cs.status,
    cs.updated_at,
    ai.id as current_item_id,
    ai.batch_number,
    ai.current_price,
    ai.bid_count,
    p.name as product_name,
    u.real_name as auctioneer_name
FROM clock_status cs
LEFT JOIN auction_item ai ON cs.current_item_id = ai.id
LEFT JOIN product p ON ai.product_id = p.id
LEFT JOIN user u ON cs.auctioneer_id = u.id;

-- 创建存储过程：更新拍卖商品当前价格
DELIMITER //
CREATE PROCEDURE UpdateAuctionItemPrice(
    IN p_item_id BIGINT,
    IN p_new_price DECIMAL(10,2)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 更新拍卖商品价格
    UPDATE auction_item 
    SET current_price = p_new_price,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_item_id;
    
    -- 增加竞价次数
    UPDATE auction_item 
    SET bid_count = bid_count + 1
    WHERE id = p_item_id;
    
    COMMIT;
END //
DELIMITER ;

-- 创建存储过程：完成拍卖
DELIMITER //
CREATE PROCEDURE CompleteAuction(
    IN p_item_id BIGINT,
    IN p_winner_id BIGINT,
    IN p_final_price DECIMAL(10,2)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 更新拍卖商品状态
    UPDATE auction_item 
    SET status = 2,
        winner_id = p_winner_id,
        final_price = p_final_price,
        end_time = CURRENT_TIMESTAMP
    WHERE id = p_item_id;
    
    -- 更新中标竞价记录
    UPDATE bid 
    SET is_winning = 1
    WHERE auction_item_id = p_item_id 
    AND user_id = p_winner_id 
    AND price = p_final_price;
    
    -- 释放钟号
    UPDATE clock_status 
    SET status = 0, current_item_id = NULL
    WHERE current_item_id = p_item_id;
    
    COMMIT;
END //
DELIMITER ;
