-- 花卉拍卖系统数据库结构
-- 创建时间: 2024-12-01
-- 描述: 完整的数据库表结构定义

-- 创建数据库
CREATE DATABASE IF NOT EXISTS flower_auction CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE flower_auction;

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `user_type` tinyint NOT NULL DEFAULT '2' COMMENT '用户类型：1拍卖师 2购买商 3管理员 4质检员',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0禁用 1启用',
  `company_name` varchar(200) DEFAULT NULL COMMENT '公司名称',
  `business_license` varchar(100) DEFAULT NULL COMMENT '营业执照号',
  `credit_level` tinyint DEFAULT '1' COMMENT '信用等级：1-5',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `frozen_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '冻结金额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
CREATE TABLE IF NOT EXISTS `role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0禁用 1启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 商品分类表
CREATE TABLE IF NOT EXISTS `category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID',
  `level` tinyint NOT NULL DEFAULT '1' COMMENT '分类层级',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0禁用 1启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 商品表
CREATE TABLE IF NOT EXISTS `product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(200) NOT NULL COMMENT '商品名称',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `description` text COMMENT '商品描述',
  `unit` varchar(20) DEFAULT '支' COMMENT '单位',
  `origin` varchar(100) DEFAULT NULL COMMENT '产地',
  `variety` varchar(100) DEFAULT NULL COMMENT '品种',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色',
  `grade_standard` text COMMENT '等级标准',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0禁用 1启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_name` (`name`),
  FOREIGN KEY (`category_id`) REFERENCES `category` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 拍卖会表
CREATE TABLE IF NOT EXISTS `auction` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '拍卖会ID',
  `name` varchar(200) NOT NULL COMMENT '拍卖会名称',
  `description` text COMMENT '拍卖会描述',
  `start_time` timestamp NOT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0待开始 1进行中 2已结束 3已取消',
  `total_items` int DEFAULT '0' COMMENT '总商品数',
  `completed_items` int DEFAULT '0' COMMENT '已完成商品数',
  `total_turnover` decimal(15,2) DEFAULT '0.00' COMMENT '总成交额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拍卖会表';

-- 拍卖商品表
CREATE TABLE IF NOT EXISTS `auction_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '拍卖商品ID',
  `auction_id` bigint NOT NULL COMMENT '拍卖会ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `batch_number` varchar(50) NOT NULL COMMENT '批次号',
  `quantity` int NOT NULL COMMENT '数量',
  `unit` varchar(20) DEFAULT '支' COMMENT '单位',
  `grade` varchar(20) DEFAULT 'A' COMMENT '等级',
  `start_price` decimal(10,2) NOT NULL COMMENT '起拍价',
  `step_price` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '加价幅度',
  `reserve_price` decimal(10,2) DEFAULT NULL COMMENT '保留价',
  `current_price` decimal(10,2) NOT NULL COMMENT '当前价格',
  `clock_number` tinyint DEFAULT NULL COMMENT '钟号：1-12',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0待拍 1拍卖中 2已成交 3流拍 4暂停',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始拍卖时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束拍卖时间',
  `winner_id` bigint DEFAULT NULL COMMENT '中标用户ID',
  `final_price` decimal(10,2) DEFAULT NULL COMMENT '成交价格',
  `bid_count` int DEFAULT '0' COMMENT '竞价次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_number` (`batch_number`),
  KEY `idx_auction_id` (`auction_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_clock_number` (`clock_number`),
  KEY `idx_status` (`status`),
  KEY `idx_winner_id` (`winner_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  FOREIGN KEY (`auction_id`) REFERENCES `auction` (`id`),
  FOREIGN KEY (`product_id`) REFERENCES `product` (`id`),
  FOREIGN KEY (`winner_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拍卖商品表';

-- 竞价记录表
CREATE TABLE IF NOT EXISTS `bid` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '竞价ID',
  `auction_item_id` bigint NOT NULL COMMENT '拍卖商品ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `price` decimal(10,2) NOT NULL COMMENT '竞价金额',
  `is_winning` tinyint NOT NULL DEFAULT '0' COMMENT '是否中标：0否 1是',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0无效 1有效',
  `bid_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '竞价时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_auction_item_id` (`auction_item_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_bid_time` (`bid_time`),
  KEY `idx_is_winning` (`is_winning`),
  KEY `idx_price` (`price`),
  FOREIGN KEY (`auction_item_id`) REFERENCES `auction_item` (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='竞价记录表';

-- 关注列表表
CREATE TABLE IF NOT EXISTS `watch_list` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关注ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `auction_item_id` bigint NOT NULL COMMENT '拍卖商品ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_item` (`user_id`, `auction_item_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auction_item_id` (`auction_item_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  FOREIGN KEY (`auction_item_id`) REFERENCES `auction_item` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关注列表表';

-- 埋单表
CREATE TABLE IF NOT EXISTS `pre_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '埋单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `auction_item_id` bigint NOT NULL COMMENT '拍卖商品ID',
  `price` decimal(10,2) NOT NULL COMMENT '埋单价格',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0待执行 1已执行 2已取消 3已过期',
  `executed_at` timestamp NULL DEFAULT NULL COMMENT '执行时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auction_item_id` (`auction_item_id`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  FOREIGN KEY (`auction_item_id`) REFERENCES `auction_item` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='埋单表';

-- 账户表
CREATE TABLE IF NOT EXISTS `account` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `account_type` tinyint NOT NULL DEFAULT '1' COMMENT '账户类型：1主账户 2保证金账户',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `frozen_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '冻结金额',
  `total_recharge` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值',
  `total_withdraw` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计提现',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0冻结 1正常',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_type` (`user_id`, `account_type`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户表';

-- 交易记录表
CREATE TABLE IF NOT EXISTS `transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '交易ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `account_id` bigint NOT NULL COMMENT '账户ID',
  `type` tinyint NOT NULL COMMENT '交易类型：1充值 2提现 3竞价冻结 4竞价解冻 5拍卖扣款 6退款',
  `amount` decimal(15,2) NOT NULL COMMENT '交易金额',
  `balance_before` decimal(15,2) NOT NULL COMMENT '交易前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '交易后余额',
  `description` varchar(500) DEFAULT NULL COMMENT '交易描述',
  `reference_id` bigint DEFAULT NULL COMMENT '关联ID（如拍卖商品ID）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0失败 1成功 2处理中',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_type_created` (`type`, `created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  FOREIGN KEY (`account_id`) REFERENCES `account` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易记录表';

-- 钟号状态表
CREATE TABLE IF NOT EXISTS `clock_status` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `clock_number` tinyint NOT NULL COMMENT '钟号：1-12',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0空闲 1活跃 2暂停 3错误',
  `current_item_id` bigint DEFAULT NULL COMMENT '当前拍卖商品ID',
  `auctioneer_id` bigint DEFAULT NULL COMMENT '拍卖师ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_clock_number` (`clock_number`),
  KEY `idx_status` (`status`),
  KEY `idx_current_item_id` (`current_item_id`),
  FOREIGN KEY (`current_item_id`) REFERENCES `auction_item` (`id`),
  FOREIGN KEY (`auctioneer_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钟号状态表';

-- 拍卖日志表
CREATE TABLE IF NOT EXISTS `auction_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `auction_item_id` bigint NOT NULL COMMENT '拍卖商品ID',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `old_value` text COMMENT '原值',
  `new_value` text COMMENT '新值',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_auction_item_id` (`auction_item_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`auction_item_id`) REFERENCES `auction_item` (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拍卖日志表';
