# 花卉拍卖系统配置文件
# 环境: development | testing | production

# 服务器配置
server:
  # 主域名配置 - 生产环境时修改为真实域名
  domain: "localhost"
  # API服务端口
  port: 8080
  # 运行模式: debug | release
  mode: "debug"
  # 读取超时时间(秒)
  read_timeout: 60
  # 写入超时时间(秒)
  write_timeout: 60

# 前端应用配置
frontend:
  # 拍卖师端
  auctioneer:
    # 访问地址
    url: "http://localhost:3001"
    # 构建输出目录
    dist_path: "./frontend/auctioneer/dist"
  
  # 购买商端
  buyer:
    # 访问地址
    url: "http://localhost:3002"
    # 构建输出目录
    dist_path: "./frontend/buyer/dist"
  
  # 投屏端
  display:
    # 访问地址
    url: "http://localhost:3003"
    # 构建输出目录
    dist_path: "./frontend/display/dist"

# API配置
api:
  # API基础路径
  base_url: "http://localhost:8080/api/v1"
  # 跨域配置
  cors:
    # 允许的源
    allowed_origins:
      - "http://localhost:3001"  # 拍卖师端
      - "http://localhost:3002"  # 购买商端
      - "http://localhost:3003"  # 投屏端
    # 允许的方法
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    # 允许的头部
    allowed_headers:
      - "Origin"
      - "Content-Type"
      - "Accept"
      - "Authorization"
      - "X-Requested-With"

# WebSocket配置
websocket:
  # WebSocket服务地址
  url: "ws://localhost:8080/ws"
  # 连接路径
  path: "/ws"
  # 心跳间隔(秒)
  heartbeat_interval: 30
  # 重连间隔(秒)
  reconnect_interval: 5
  # 最大重连次数
  max_reconnect_attempts: 10

# 数据库配置
database:
  # MySQL配置
  mysql:
    # 主机地址
    host: "localhost"
    # 端口
    port: 3306
    # 数据库名
    database: "flower_auction"
    # 用户名
    username: "root"
    # 密码 (生产环境使用环境变量)
    password: "123456"
    # 字符集
    charset: "utf8mb4"
    # 时区
    timezone: "Asia/Shanghai"
    # 连接池配置
    pool:
      # 最大空闲连接数
      max_idle: 10
      # 最大打开连接数
      max_open: 100
      # 连接最大生存时间(分钟)
      max_lifetime: 60

# Redis配置
redis:
  # 主机地址
  host: "localhost"
  # 端口
  port: 6379
  # 密码
  password: ""
  # 数据库索引
  database: 0
  # 连接池配置
  pool:
    # 最大空闲连接数
    max_idle: 10
    # 最大活跃连接数
    max_active: 100
    # 空闲连接超时时间(秒)
    idle_timeout: 300

# JWT配置
jwt:
  # 密钥 (生产环境使用环境变量)
  secret: "flower-auction-jwt-secret-key-2024"
  # 过期时间(小时)
  expires_in: 24
  # 刷新时间(小时)
  refresh_in: 168

# 日志配置
logging:
  # 日志级别: debug | info | warn | error
  level: "info"
  # 日志格式: json | text
  format: "json"
  # 日志输出: stdout | file
  output: "stdout"
  # 日志文件路径
  file_path: "./logs/app.log"
  # 日志文件最大大小(MB)
  max_size: 100
  # 保留的日志文件数量
  max_backups: 10
  # 日志文件保留天数
  max_age: 30

# 文件上传配置
upload:
  # 上传目录
  path: "./uploads"
  # 最大文件大小(MB)
  max_size: 10
  # 允许的文件类型
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "application/pdf"

# 邮件配置
email:
  # SMTP服务器
  smtp_host: "smtp.example.com"
  # SMTP端口
  smtp_port: 587
  # 用户名
  username: "<EMAIL>"
  # 密码 (生产环境使用环境变量)
  password: "email_password"
  # 发件人名称
  from_name: "花卉拍卖系统"

# 短信配置
sms:
  # 服务商: aliyun | tencent
  provider: "aliyun"
  # 访问密钥ID (生产环境使用环境变量)
  access_key_id: "your_access_key_id"
  # 访问密钥 (生产环境使用环境变量)
  access_key_secret: "your_access_key_secret"
  # 短信签名
  sign_name: "花卉拍卖"
  # 模板ID
  template_id: "SMS_123456789"

# 支付配置
payment:
  # 支付宝配置
  alipay:
    # 应用ID
    app_id: "your_app_id"
    # 私钥 (生产环境使用环境变量)
    private_key: "your_private_key"
    # 公钥
    public_key: "alipay_public_key"
    # 网关地址
    gateway_url: "https://openapi.alipay.com/gateway.do"
  
  # 微信支付配置
  wechat:
    # 应用ID
    app_id: "your_app_id"
    # 商户号
    mch_id: "your_mch_id"
    # 密钥 (生产环境使用环境变量)
    key: "your_wechat_key"
    # 证书路径
    cert_path: "./certs/wechat"

# 监控配置
monitoring:
  # 启用监控
  enabled: true
  # Prometheus指标端口
  metrics_port: 9090
  # 健康检查路径
  health_path: "/health"

# 安全配置
security:
  # 启用HTTPS
  enable_https: false
  # SSL证书路径
  cert_file: "./certs/server.crt"
  # SSL私钥路径
  key_file: "./certs/server.key"
  # 启用限流
  enable_rate_limit: true
  # 每分钟请求限制
  rate_limit: 1000

# 环境特定配置
environments:
  # 开发环境
  development:
    server:
      domain: "localhost"
      mode: "debug"
    logging:
      level: "debug"
      output: "stdout"
  
  # 测试环境
  testing:
    server:
      domain: "test.flower-auction.com"
      mode: "release"
    database:
      mysql:
        database: "flower_auction_test"
    logging:
      level: "info"
      output: "file"
  
  # 生产环境
  production:
    server:
      domain: "flower-auction.com"
      mode: "release"
    database:
      mysql:
        host: "${DB_HOST}"
        username: "${DB_USER}"
        password: "${DB_PASSWORD}"
    jwt:
      secret: "${JWT_SECRET}"
    logging:
      level: "warn"
      output: "file"
    security:
      enable_https: true
      enable_rate_limit: true
