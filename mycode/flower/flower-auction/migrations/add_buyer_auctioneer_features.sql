-- 扩展用户表以支持拍卖师和购买商功能
-- 执行时间: 2024-12-01

-- 1. 扩展用户表
ALTER TABLE `user` 
ADD COLUMN `clock_numbers` JSON COMMENT '拍卖师负责的钟号(JSON数组)',
ADD COLUMN `balance` DECIMAL(15,2) DEFAULT 0.00 COMMENT '账户余额',
ADD COLUMN `frozen_amount` DECIMAL(15,2) DEFAULT 0.00 COMMENT '冻结金额',
ADD COLUMN `company_name` VARCHAR(200) DEFAULT '' COMMENT '公司名称',
ADD COLUMN `credit_level` TINYINT DEFAULT 1 COMMENT '信用等级 1-5';

-- 2. 扩展拍卖商品表
ALTER TABLE `auction_item` 
ADD COLUMN `batch_number` VARCHAR(50) DEFAULT '' COMMENT '批次号',
ADD COLUMN `clock_number` INT DEFAULT 0 COMMENT '钟号',
ADD COLUMN `watch_count` INT DEFAULT 0 COMMENT '关注人数';

-- 3. 创建关注列表表
CREATE TABLE IF NOT EXISTS `watch_list` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `auction_item_id` BIGINT NOT NULL COMMENT '拍卖商品ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_auction_item_id` (`auction_item_id`),
  UNIQUE KEY `uk_user_item` (`user_id`, `auction_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关注列表';

-- 4. 创建埋单表
CREATE TABLE IF NOT EXISTS `pre_order` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `auction_item_id` BIGINT NOT NULL COMMENT '拍卖商品ID',
  `price` DECIMAL(10,2) NOT NULL COMMENT '埋单价格',
  `quantity` INT DEFAULT 1 COMMENT '数量',
  `status` TINYINT DEFAULT 0 COMMENT '状态: 0-待生效 1-生效中 2-已执行 3-已取消 4-已过期',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_auction_item_id` (`auction_item_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='埋单表';

-- 5. 创建通知表
CREATE TABLE IF NOT EXISTS `notification` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `type` TINYINT NOT NULL COMMENT '通知类型: 1-拍卖开始 2-价格变动 3-成交通知 4-系统通知',
  `title` VARCHAR(200) NOT NULL COMMENT '通知标题',
  `content` TEXT COMMENT '通知内容',
  `related_id` BIGINT COMMENT '关联ID(如拍卖商品ID)',
  `is_read` TINYINT DEFAULT 0 COMMENT '是否已读: 0-未读 1-已读',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_type` (`type`),
  INDEX `idx_is_read` (`is_read`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- 6. 创建钟号状态表
CREATE TABLE IF NOT EXISTS `clock_status` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `clock_number` INT NOT NULL COMMENT '钟号',
  `status` VARCHAR(20) DEFAULT 'idle' COMMENT '状态: idle-空闲 active-活跃 paused-暂停 error-错误',
  `current_item_id` BIGINT COMMENT '当前拍卖商品ID',
  `auctioneer_id` BIGINT COMMENT '当前拍卖师ID',
  `last_update` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  UNIQUE KEY `uk_clock_number` (`clock_number`),
  INDEX `idx_status` (`status`),
  INDEX `idx_auctioneer_id` (`auctioneer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钟号状态表';

-- 7. 初始化钟号状态数据 (1-12号钟)
INSERT INTO `clock_status` (`clock_number`, `status`) VALUES
(1, 'idle'), (2, 'idle'), (3, 'idle'), (4, 'idle'),
(5, 'idle'), (6, 'idle'), (7, 'idle'), (8, 'idle'),
(9, 'idle'), (10, 'idle'), (11, 'idle'), (12, 'idle')
ON DUPLICATE KEY UPDATE `status` = VALUES(`status`);

-- 8. 创建拍卖日志表
CREATE TABLE IF NOT EXISTS `auction_log` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `auction_item_id` BIGINT NOT NULL COMMENT '拍卖商品ID',
  `clock_number` INT NOT NULL COMMENT '钟号',
  `auctioneer_id` BIGINT NOT NULL COMMENT '拍卖师ID',
  `action` VARCHAR(50) NOT NULL COMMENT '操作: start-开始 pause-暂停 resume-恢复 stop-停止 adjust_price-调价',
  `old_value` VARCHAR(200) COMMENT '原值',
  `new_value` VARCHAR(200) COMMENT '新值',
  `reason` VARCHAR(500) COMMENT '操作原因',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX `idx_auction_item_id` (`auction_item_id`),
  INDEX `idx_clock_number` (`clock_number`),
  INDEX `idx_auctioneer_id` (`auctioneer_id`),
  INDEX `idx_action` (`action`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拍卖操作日志';

-- 9. 更新拍卖商品状态枚举
ALTER TABLE `auction_item` 
MODIFY COLUMN `status` TINYINT COMMENT '状态: 0-未开始 1-进行中 2-已成交 3-流拍 4-已取消 5-暂停';

-- 10. 创建索引优化查询性能
ALTER TABLE `auction_item` 
ADD INDEX `idx_batch_number` (`batch_number`),
ADD INDEX `idx_clock_number` (`clock_number`),
ADD INDEX `idx_status_clock` (`status`, `clock_number`);

-- 11. 创建用户钟号权限索引
ALTER TABLE `user` 
ADD INDEX `idx_user_type` (`user_type`),
ADD INDEX `idx_status` (`status`);

-- 12. 添加外键约束
ALTER TABLE `watch_list` 
ADD CONSTRAINT `fk_watch_list_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_watch_list_item` FOREIGN KEY (`auction_item_id`) REFERENCES `auction_item` (`id`) ON DELETE CASCADE;

ALTER TABLE `pre_order` 
ADD CONSTRAINT `fk_pre_order_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_pre_order_item` FOREIGN KEY (`auction_item_id`) REFERENCES `auction_item` (`id`) ON DELETE CASCADE;

ALTER TABLE `notification` 
ADD CONSTRAINT `fk_notification_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;

ALTER TABLE `auction_log` 
ADD CONSTRAINT `fk_auction_log_item` FOREIGN KEY (`auction_item_id`) REFERENCES `auction_item` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_auction_log_auctioneer` FOREIGN KEY (`auctioneer_id`) REFERENCES `user` (`id`) ON DELETE CASCADE;

-- 13. 创建测试数据
-- 创建测试拍卖师用户
INSERT INTO `user` (`username`, `password`, `real_name`, `phone`, `email`, `user_type`, `status`, `clock_numbers`, `created_at`) VALUES
('auctioneer1', '$2a$10$example_hash', '张拍卖师', '13800001001', '<EMAIL>', 1, 1, '[1,2,3]', 20241201),
('auctioneer2', '$2a$10$example_hash', '李拍卖师', '13800001002', '<EMAIL>', 1, 1, '[4,5,6]', 20241201)
ON DUPLICATE KEY UPDATE `username` = VALUES(`username`);

-- 创建测试购买商用户
INSERT INTO `user` (`username`, `password`, `real_name`, `phone`, `email`, `user_type`, `status`, `balance`, `company_name`, `credit_level`, `created_at`) VALUES
('buyer1', '$2a$10$example_hash', '王购买商', '13800002001', '<EMAIL>', 2, 1, 50000.00, '昆明花卉贸易公司', 3, 20241201),
('buyer2', '$2a$10$example_hash', '刘购买商', '13800002002', '<EMAIL>', 2, 1, 30000.00, '云南花卉批发中心', 2, 20241201)
ON DUPLICATE KEY UPDATE `username` = VALUES(`username`);

-- 更新现有拍卖商品添加批次号和钟号
UPDATE `auction_item` SET 
  `batch_number` = CONCAT('B', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(`id`, 3, '0')),
  `clock_number` = ((`id` - 1) % 12) + 1
WHERE `batch_number` = '' OR `batch_number` IS NULL;

COMMIT;
