# 测试环境配置文件

# 服务器配置
server:
  port: 8081
  mode: test

# 用户数据库配置
userDB:
  host: 127.0.0.1
  port: 3306
  user: root
  password: root
  database: user_db_test
  maxIdleConns: 5
  maxOpenConns: 10

# 商品数据库配置
productDB:
  host: 127.0.0.1
  port: 3306
  user: root
  password: root
  database: product_db_test
  maxIdleConns: 5
  maxOpenConns: 10

# 拍卖数据库配置
auctionDB:
  host: 127.0.0.1
  port: 3306
  user: root
  password: root
  database: auction_db_test
  maxIdleConns: 5
  maxOpenConns: 10

# 订单数据库配置
orderDB:
  host: 127.0.0.1
  port: 3306
  user: root
  password: root
  database: order_db_test
  maxIdleConns: 5
  maxOpenConns: 10

# Redis配置
redis:
  host: 127.0.0.1
  port: 6379
  password: ""
  db: 1  # 使用不同的数据库

# RabbitMQ配置
rabbitmq:
  url: "amqp://guest:guest@127.0.0.1:5672/"
  exchange: "flower_auction_test"
  
# JWT配置
jwt:
  secret: "flower_auction_test_secret_key"
  expireHours: 1

# 日志配置
log:
  level: debug
  file: "./logs/test.log"
