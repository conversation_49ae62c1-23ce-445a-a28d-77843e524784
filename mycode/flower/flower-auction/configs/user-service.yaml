# 用户服务配置
server:
  port: 8082
  mode: debug
  read_timeout: 60
  write_timeout: 60

database:
  user:
    host: 127.0.0.1
    port: 3306
    username: root
    password: 
    database: flower_auction_user
    charset: utf8mb4

redis:
  host: 127.0.0.1
  port: 6379
  password: ""
  db: 1

log:
  level: info
  filename: ./logs/user-service.log
  max_size: 100
  max_age: 30
  max_backups: 10
  compress: true
