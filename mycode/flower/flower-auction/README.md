# 🌸 花卉拍卖系统 - 后端服务

基于Go语言开发的高性能花卉拍卖后端服务，支持实时竞价、多钟号并行拍卖、WebSocket通信等核心功能。

## 🚀 技术栈

- **Go 1.21+** - 高性能后端语言
- **Gin** - 轻量级Web框架
- **GORM** - 强大的ORM框架
- **MySQL 8.0+** - 主数据库
- **Redis 6.0+** - 缓存和会话存储
- **WebSocket** - 实时通信
- **JWT** - 身份认证

## 📁 项目结构

```
flower-auction/
├── cmd/                           # 应用程序入口
├── internal/                      # 内部包
│   ├── api/                      # API处理器
│   │   ├── auth.go              # 认证接口
│   │   ├── auction.go           # 拍卖接口
│   │   ├── auctioneer.go        # 拍卖师接口
│   │   ├── buyer.go             # 购买商接口
│   │   └── common.go            # 通用接口
│   ├── model/                    # 数据模型
│   │   ├── user.go              # 用户模型
│   │   ├── auction.go           # 拍卖模型
│   │   ├── finance.go           # 财务模型
│   │   └── product.go           # 商品模型
│   ├── service/                  # 业务逻辑
│   │   ├── auction_service.go   # 拍卖服务
│   │   ├── user_service.go      # 用户服务
│   │   └── finance_service.go   # 财务服务
│   ├── dao/                      # 数据访问层
│   └── middleware/               # 中间件
├── pkg/                          # 公共包
├── configs/                      # 配置文件
├── docs/                         # 文档
├── migrations/                   # 数据库迁移
├── main.go                       # 程序入口
└── README.md                     # 项目说明
```

## 🔧 核心功能

### ✅ 已实现功能

#### **用户管理系统**
- 🔐 用户注册、登录、JWT认证
- 👥 多角色权限管理（拍卖师、购买商、管理员）
- 🏢 企业认证和个人实名认证
- 💳 账户余额和资金管理
- 🎯 拍卖师钟号权限控制

#### **拍卖核心功能**
- 🎪 多钟号并行拍卖系统
- ⚡ 实时竞价和WebSocket通信
- 📊 拍卖状态管理和控制
- 💰 价格调整和流拍处理
- 📋 埋单功能和关注管理

#### **数据管理**
- 🗄️ 完整的数据库设计
- 📈 实时统计和数据分析
- 📝 拍卖日志和操作记录
- 🔍 高级搜索和筛选功能

#### **API接口**
- 🌐 RESTful API设计
- 📱 拍卖师端专用接口
- 🛒 购买商端专用接口
- 📺 投屏端数据接口
- 🔒 完整的权限验证

## ⚡ 快速开始

### 环境要求
- **Go 1.21+**
- **MySQL 8.0+**
- **Redis 6.0+**

### 1. 安装依赖
```bash
go mod download
```

### 2. 配置数据库
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE flower_auction CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 执行迁移脚本
mysql -u root -p flower_auction < docs/database/migration/V1__init_schema.sql
mysql -u root -p flower_auction < docs/database/migration/V2__add_buyer_auctioneer_features.sql
```

### 3. 配置文件
```yaml
# configs/config.yaml
server:
  port: 8081
  mode: debug

database:
  host: localhost
  port: 3306
  username: root
  password: your_password
  database: flower_auction

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

jwt:
  secret: your_jwt_secret
  expire: 24h
```

### 4. 启动服务
```bash
# 开发模式
go run main.go

# 编译运行
go build -o bin/flower-auction main.go
./bin/flower-auction
```

服务器将在 `http://localhost:8081` 启动

## 🔧 API接口

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/refresh` - 刷新Token
- `GET /api/v1/auth/user` - 获取用户信息

### 拍卖师端接口
- `GET /api/v1/auctioneer/dashboard/statistics` - 控制台统计
- `GET /api/v1/auctioneer/clocks/status` - 钟号状态
- `PUT /api/v1/auctioneer/clocks/:clockNumber/status` - 更新钟号状态
- `GET /api/v1/auctioneer/batches` - 获取批次列表
- `GET /api/v1/auctioneer/batches/:id` - 获取批次详情
- `PUT /api/v1/auctioneer/batches/:id/assign-clock` - 分配批次到钟号
- `POST /api/v1/auctioneer/auction/start` - 开始拍卖
- `POST /api/v1/auctioneer/auction/pause` - 暂停拍卖
- `POST /api/v1/auctioneer/auction/resume` - 恢复拍卖
- `POST /api/v1/auctioneer/auction/stop` - 停止拍卖
- `POST /api/v1/auctioneer/auction/unsold` - 标记流拍
- `POST /api/v1/auctioneer/auction/adjust-price` - 调整价格

### 购买商端接口
- `GET /api/v1/buyer/batches` - 获取批次列表
- `GET /api/v1/buyer/batches/:id` - 获取批次详情
- `GET /api/v1/buyer/batches/search` - 搜索批次
- `POST /api/v1/buyer/watchlist` - 添加关注
- `DELETE /api/v1/buyer/watchlist/:itemId` - 移除关注
- `GET /api/v1/buyer/watchlist` - 获取关注列表
- `POST /api/v1/buyer/bids` - 出价
- `GET /api/v1/buyer/bids/my` - 获取我的竞价记录
- `GET /api/v1/buyer/bids/item/:itemId` - 获取商品竞价记录
- `POST /api/v1/buyer/pre-orders` - 创建埋单
- `PUT /api/v1/buyer/pre-orders/:id` - 更新埋单
- `DELETE /api/v1/buyer/pre-orders/:id` - 取消埋单
- `GET /api/v1/buyer/account/balance` - 获取账户余额
- `GET /api/v1/buyer/account/transactions` - 获取交易记录
- `POST /api/v1/buyer/account/recharge` - 充值

### 投屏端接口
- `GET /api/v1/display/clocks/status` - 获取钟号状态
- `GET /api/v1/display/auction/current` - 获取当前拍卖商品
- `GET /api/v1/display/auction/bids/:itemId` - 获取竞价历史
- `GET /api/v1/display/statistics/market` - 获取市场统计
- `GET /api/v1/display/realtime` - 获取实时数据

### WebSocket接口
- `ws://localhost:8081/ws/auctioneer` - 拍卖师端WebSocket
- `ws://localhost:8081/ws/buyer` - 购买商端WebSocket
- `ws://localhost:8081/ws/display` - 投屏端WebSocket

## 开发指南

### 代码结构

项目采用分层架构：

1. **API层** (`internal/api/`): 处理HTTP请求和响应
2. **Service层** (`internal/service/`): 业务逻辑处理
3. **DAO层** (`internal/dao/`): 数据访问操作
4. **Model层** (`internal/model/`): 数据模型定义

### 添加新功能

1. 在 `internal/model/` 中定义数据模型
2. 在 `internal/dao/` 中实现数据访问接口
3. 在 `internal/service/` 中实现业务逻辑
4. 在 `internal/api/` 中实现HTTP接口
5. 在 `main.go` 中注册路由

### 数据库迁移

使用 `docs/database/migration/` 目录下的SQL文件进行数据库迁移

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t flower-auction .

# 运行容器
docker run -p 8080:8080 flower-auction
```

### 生产环境配置

1. 修改 `configs/config.yaml` 中的配置
2. 设置环境变量 `GIN_MODE=release`
3. 配置反向代理（如Nginx）
4. 设置日志轮转
5. 配置监控和告警

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题，请联系项目维护者。
