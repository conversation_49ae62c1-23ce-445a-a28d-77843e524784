#!/bin/bash

# 昆明花卉拍卖系统 - 完整微服务启动脚本
# 包含WebSocket实时拍卖功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 服务配置
MAIN_SERVICE_PORT=8081
MAIN_SERVICE_CONFIG="configs/config.yaml"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

log_service() {
    echo -e "${PURPLE}[SERVICE]${NC} $1"
}

log_websocket() {
    echo -e "${CYAN}[WEBSOCKET]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "Go 环境未安装或未配置"
        exit 1
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装"
        exit 1
    fi
    
    # 检查lsof
    if ! command -v lsof &> /dev/null; then
        log_error "lsof 未安装"
        exit 1
    fi
    
    log_info "✓ 系统依赖检查通过"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口 $port 已被占用"
        local pid=$(lsof -ti:$port)
        log_warn "占用进程 PID: $pid"
        
        # 询问是否终止占用进程
        read -p "是否终止占用端口 $port 的进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "终止进程 $pid..."
            kill -9 $pid 2>/dev/null || true
            sleep 2
            return 0
        else
            log_error "端口 $port 被占用，无法启动 $service"
            return 1
        fi
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service=$2
    local max_attempts=5
    local attempt=1
    
    log_info "等待 $service 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url/health" > /dev/null 2>&1; then
            log_info "✓ $service 启动成功"
            return 0
        fi
        
        log_debug "尝试 $attempt/$max_attempts: $service 尚未就绪"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "✗ $service 启动失败或超时"
    return 1
}

# 启动单个服务
start_service() {
    local service_name=$1
    local port=$2
    local config=$3
    
    log_service "启动 $service_name (端口: $port)..."
    
    # 检查端口
    if ! check_port $port $service_name; then
        return 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 根据服务类型选择启动方式
    if [ "$service_name" = "main-service" ]; then
        # 主服务使用根目录的main.go
        log_service "启动主服务 (包含WebSocket功能)..."
        nohup go run main.go > logs/$service_name.log 2>&1 &
    else
        # 微服务使用cmd目录下的服务
        if [ -f "cmd/$service_name/main.go" ]; then
            nohup go run cmd/$service_name/main.go -config=$config > logs/$service_name.log 2>&1 &
        else
            log_error "服务 $service_name 的main.go文件不存在"
            return 1
        fi
    fi
    
    local pid=$!
    
    # 保存PID
    echo $pid > logs/$service_name.pid
    
    log_service "$service_name 已启动，PID: $pid"
    
    # 等待服务就绪
    wait_for_service "http://localhost:$port" "$service_name"
    
    return $?
}

# 停止所有服务
stop_all_services() {
    log_info "停止所有服务..."
    
    if [ -d "logs" ]; then
        for pidfile in logs/*.pid; do
            if [ -f "$pidfile" ]; then
                local service=$(basename "$pidfile" .pid)
                local pid=$(cat "$pidfile")
                
                if kill -0 "$pid" 2>/dev/null; then
                    log_info "停止 $service (PID: $pid)..."
                    kill -TERM "$pid" 2>/dev/null || true
                    sleep 2
                    
                    # 如果进程仍在运行，强制终止
                    if kill -0 "$pid" 2>/dev/null; then
                        log_warn "强制终止 $service (PID: $pid)..."
                        kill -9 "$pid" 2>/dev/null || true
                    fi
                fi
                
                rm -f "$pidfile"
            fi
        done
    fi
    
    log_info "所有服务已停止"
}

# 显示服务状态
show_status() {
    log_info "服务状态检查..."
    echo

    if curl -s "http://localhost:$MAIN_SERVICE_PORT/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} main-service - http://localhost:$MAIN_SERVICE_PORT (运行中)"
    else
        echo -e "${RED}✗${NC} main-service - http://localhost:$MAIN_SERVICE_PORT (未运行)"
    fi

    echo
    log_websocket "WebSocket 端点:"
    echo -e "  ${CYAN}ws://localhost:$MAIN_SERVICE_PORT/ws/auction${NC} (实时拍卖)"
    echo
}

# 显示日志
show_logs() {
    local service=$1

    if [ -z "$service" ]; then
        log_info "可用的服务日志:"
        echo "  - main-service"
        echo
        echo "使用方法: $0 logs <service-name>"
        return
    fi

    if [ -f "logs/$service.log" ]; then
        log_info "显示 $service 的日志 (按 Ctrl+C 退出):"
        tail -f "logs/$service.log"
    else
        log_error "服务 $service 的日志文件不存在"
    fi
}

# 主函数
main() {
    local action=${1:-start}
    
    case $action in
        "start")
            log_info "🚀 启动昆明花卉拍卖系统所有服务..."
            echo
            
            # 检查依赖
            check_dependencies
            
            # 检查Go模块
            log_info "检查Go模块依赖..."
            go mod tidy
            
            # 创建必要的目录
            mkdir -p logs configs
            
            # 启动主服务
            log_info "开始启动服务..."
            echo

            # 启动主服务 (包含WebSocket和完整API)
            if start_service "main-service" "$MAIN_SERVICE_PORT" "$MAIN_SERVICE_CONFIG"; then
                log_websocket "✓ 主服务启动成功 (包含WebSocket实时拍卖功能)"
            else
                log_error "✗ 主服务启动失败"
                exit 1
            fi

            echo
            log_info "🎉 所有服务启动完成！"
            echo
            log_info "服务访问地址："
            echo -e "  ${GREEN}主服务 (含WebSocket):${NC} http://localhost:$MAIN_SERVICE_PORT"
            echo -e "  ${GREEN}健康检查:${NC}           http://localhost:$MAIN_SERVICE_PORT/health"
            echo -e "  ${CYAN}WebSocket端点:${NC}       ws://localhost:$MAIN_SERVICE_PORT/ws/auction"
            echo -e "  ${CYAN}WebSocket测试页面:${NC}   http://localhost:3002/websocket-test.html"
            echo
            log_info "常用命令："
            echo "  查看状态:    $0 status"
            echo "  查看日志:    $0 logs main-service"
            echo "  停止服务:    $0 stop"
            echo
            ;;
            
        "stop")
            stop_all_services
            ;;
            
        "status")
            show_status
            ;;
            
        "logs")
            show_logs $2
            ;;
            
        "restart")
            log_info "重启所有服务..."
            stop_all_services
            sleep 3
            main start
            ;;
            
        *)
            echo "昆明花卉拍卖系统 - 服务管理脚本"
            echo
            echo "用法: $0 {start|stop|restart|status|logs [service-name]}"
            echo
            echo "命令:"
            echo "  start    - 启动所有服务"
            echo "  stop     - 停止所有服务"
            echo "  restart  - 重启所有服务"
            echo "  status   - 查看服务状态"
            echo "  logs     - 查看服务日志"
            echo
            echo "示例:"
            echo "  $0 start                    # 启动所有服务"
            echo "  $0 logs main-service        # 查看主服务日志"
            echo "  $0 status                   # 查看所有服务状态"
            ;;
    esac
}

# 信号处理
trap 'log_warn "收到中断信号，正在停止服务..."; stop_all_services; exit 0' INT TERM

# 执行主函数
main "$@"
