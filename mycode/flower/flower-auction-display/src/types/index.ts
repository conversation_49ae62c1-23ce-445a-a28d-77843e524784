// 投屏端类型定义

export interface ClockData {
  clockNumber: number;
  status: 'idle' | 'active' | 'paused' | 'error';
  currentItem?: AuctionItem;
  auctioneer?: string;
  lastUpdate: string;
}

export interface AuctionItem {
  id: number;
  batchNumber: string;
  productName: string;
  category: string;
  grade: string;
  quantity: number;
  unit: string;
  startPrice: number;
  currentPrice: number;
  stepPrice: number;
  status: 'pending' | 'active' | 'sold' | 'unsold' | 'paused';
  startTime: string;
  endTime?: string;
  bidCount: number;
  watchCount: number;
  clockNumber: number;
}

export interface BidRecord {
  id: number;
  userId: number;
  username: string;
  price: number;
  quantity: number;
  timestamp: string;
  isWinning: boolean;
}

export interface MarketStatistics {
  totalClocks: number;
  activeClocks: number;
  todayAuctions: number;
  todayTurnover: number;
  onlineUsers: number;
  completedBatches: number;
  pendingBatches: number;
  averagePrice: number;
  topCategories: Array<{
    category: string;
    count: number;
    turnover: number;
  }>;
}

export interface PriceHistory {
  timestamp: string;
  price: number;
  volume: number;
}

export interface DisplaySettings {
  theme: 'light' | 'dark';
  autoRefresh: boolean;
  refreshInterval: number;
  showBidHistory: boolean;
  showPriceChart: boolean;
  clockLayout: 'grid' | 'list';
  fontSize: 'small' | 'medium' | 'large';
}

export interface WebSocketMessage {
  type: 'clock_update' | 'bid_update' | 'auction_start' | 'auction_end' | 'price_change' | 'statistics_update';
  data: any;
  timestamp: string;
}

export interface ClockLayout {
  clockNumber: number;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  visible: boolean;
}

export interface AuctionSession {
  id: number;
  name: string;
  startTime: string;
  endTime: string;
  status: 'upcoming' | 'active' | 'completed';
  totalItems: number;
  completedItems: number;
  totalTurnover: number;
}
