import apiClient from './api';

interface LoginRequest {
  username: string;
  password: string;
}

interface LoginResponse {
  token: string;
  refreshToken: string;
  user: any;
}

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

class AuthService {
  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await apiClient.post('/auth/login', credentials);
      
      if (response.data.success) {
        const { token, refreshToken, user } = response.data.data;
        
        // 存储认证信息（使用display前缀区分）
        localStorage.setItem('display_token', token);
        localStorage.setItem('display_refreshToken', refreshToken);
        localStorage.setItem('display_user', JSON.stringify(user));
        
        return response.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('display_token');
      localStorage.removeItem('display_refreshToken');
      localStorage.removeItem('display_user');
      
      // 跳转到登录页
      window.location.href = '/login';
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.get('/auth/me');
      
      if (response.data.success) {
        // 更新本地存储的用户信息
        localStorage.setItem('display_user', JSON.stringify(response.data.data));
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Get current user error:', error);
      throw error;
    }
  }

  /**
   * 刷新令牌
   */
  async refreshToken(): Promise<ApiResponse<{ token: string; refreshToken: string }>> {
    try {
      const refreshToken = localStorage.getItem('display_refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiClient.post('/auth/refresh', {
        refreshToken: refreshToken,
      });

      if (response.data.success) {
        const { token, refreshToken: newRefreshToken } = response.data.data;
        
        // 更新存储的令牌
        localStorage.setItem('display_token', token);
        localStorage.setItem('display_refreshToken', newRefreshToken);
      }

      return response.data;
    } catch (error: any) {
      console.error('Refresh token error:', error);
      throw error;
    }
  }

  /**
   * 获取存储的用户信息
   */
  getStoredUser(): any | null {
    try {
      const userStr = localStorage.getItem('display_user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Parse stored user error:', error);
      return null;
    }
  }

  /**
   * 获取存储的令牌
   */
  getStoredToken(): string | null {
    return localStorage.getItem('display_token');
  }

  /**
   * 获取存储的刷新令牌
   */
  getStoredRefreshToken(): string | null {
    return localStorage.getItem('display_refreshToken');
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('display_token');
    const user = localStorage.getItem('display_user');
    
    if (!token || !user) {
      return false;
    }

    try {
      // 检查token是否过期
      const tokenPayload = this.parseJWT(token);
      if (tokenPayload && tokenPayload.exp) {
        const expirationTime = tokenPayload.exp * 1000;
        const currentTime = Date.now();
        
        // 如果token在5分钟内过期，认为需要刷新
        if (expirationTime - currentTime < 5 * 60 * 1000) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  /**
   * 解析JWT令牌
   */
  private parseJWT(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('JWT parse error:', error);
      return null;
    }
  }

  /**
   * 清除认证信息
   */
  clearAuth(): void {
    localStorage.removeItem('display_token');
    localStorage.removeItem('display_refreshToken');
    localStorage.removeItem('display_user');
  }
}

export const authService = new AuthService();
export default authService;
