import axios from 'axios';
import { ClockData, AuctionItem, BidRecord, PriceHistory, MarketStatistics, AuctionSession } from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081/api/v1',
  timeout: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT || '10000'),
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('display_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);

    // 处理认证错误
    if (error.response?.status === 401) {
      // 清除认证信息并跳转到登录页
      localStorage.removeItem('display_token');
      localStorage.removeItem('display_refreshToken');
      localStorage.removeItem('display_user');

      // 如果不在登录页，则跳转到登录页
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// 投屏端API接口
export const displayApi = {
  // 获取钟号状态
  getClocksStatus: (): Promise<{ data: ClockData[] }> => {
    return api.get('/display/clocks/status');
  },

  // 获取当前拍卖商品
  getCurrentAuctionItems: (): Promise<{ data: AuctionItem[] }> => {
    return api.get('/display/auction/current');
  },

  // 获取竞价历史
  getBidHistory: (itemId: number): Promise<{ data: BidRecord[] }> => {
    return api.get(`/display/auction/bids/${itemId}`);
  },

  // 获取价格历史
  getPriceHistory: (itemId: number): Promise<{ data: PriceHistory[] }> => {
    return api.get(`/display/auction/price-history/${itemId}`);
  },

  // 获取市场统计
  getMarketStatistics: (): Promise<{ data: MarketStatistics }> => {
    return api.get('/display/statistics/market');
  },

  // 获取小时数据
  getHourlyData: (date: string): Promise<{ data: any[] }> => {
    return api.get(`/display/statistics/hourly?date=${date}`);
  },

  // 获取分类统计
  getCategoryStatistics: (): Promise<{ data: any[] }> => {
    return api.get('/display/statistics/categories');
  },

  // 获取拍卖会话
  getAuctionSessions: (): Promise<{ data: AuctionSession[] }> => {
    return api.get('/display/auction/sessions');
  },

  // 获取实时数据
  getRealtimeData: (): Promise<{ data: any }> => {
    return api.get('/display/realtime');
  },

  // 获取钟号详细信息
  getClockDetail: (clockNumber: number): Promise<{ data: any }> => {
    return api.get(`/display/clocks/${clockNumber}`);
  },

  // 获取今日概览
  getTodayOverview: (): Promise<{ data: any }> => {
    return api.get('/display/overview/today');
  },

  // 获取热门商品
  getPopularItems: (): Promise<{ data: AuctionItem[] }> => {
    return api.get('/display/auction/popular');
  },

  // 获取成交记录
  getTransactionHistory: (params?: {
    page?: number;
    pageSize?: number;
    startTime?: string;
    endTime?: string;
  }): Promise<{ data: any }> => {
    return api.get('/display/transactions', { params });
  },

  // 获取用户在线统计
  getOnlineUserStats: (): Promise<{ data: any }> => {
    return api.get('/display/statistics/online-users');
  },

  // 获取拍卖进度
  getAuctionProgress: (): Promise<{ data: any }> => {
    return api.get('/display/auction/progress');
  },
};

// WebSocket连接管理
export class WebSocketManager {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private onMessage?: (message: any) => void;
  private onConnect?: () => void;
  private onDisconnect?: () => void;
  private onError?: (error: any) => void;

  constructor(url: string) {
    this.url = url || import.meta.env.VITE_WS_URL || 'ws://localhost:8081/ws/display';
  }

  connect(callbacks: {
    onMessage?: (message: any) => void;
    onConnect?: () => void;
    onDisconnect?: () => void;
    onError?: (error: any) => void;
  }) {
    this.onMessage = callbacks.onMessage;
    this.onConnect = callbacks.onConnect;
    this.onDisconnect = callbacks.onDisconnect;
    this.onError = callbacks.onError;

    try {
      this.ws = new WebSocket(this.url);
      
      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
        this.onConnect?.();
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.onMessage?.(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.onDisconnect?.();
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.onError?.(error);
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.onError?.(error);
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect({
          onMessage: this.onMessage,
          onConnect: this.onConnect,
          onDisconnect: this.onDisconnect,
          onError: this.onError,
        });
      }, this.reconnectInterval);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  getSocket() {
    return this.ws;
  }

  isConnected() {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

export default api;
