import { configureStore } from '@reduxjs/toolkit';
import clocksReducer from './slices/clocksSlice';
import auctionReducer from './slices/auctionSlice';
import statisticsReducer from './slices/statisticsSlice';
import settingsReducer from './slices/settingsSlice';
import websocketReducer from './slices/websocketSlice';

export const store = configureStore({
  reducer: {
    clocks: clocksReducer,
    auction: auctionReducer,
    statistics: statisticsReducer,
    settings: settingsReducer,
    websocket: websocketReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['websocket/setSocket'],
        ignoredPaths: ['websocket.socket'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
