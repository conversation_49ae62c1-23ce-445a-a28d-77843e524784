import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AuctionItem, BidRecord, PriceHistory, AuctionSession } from '../../types';
import { displayApi } from '../../services/api';

interface AuctionState {
  currentItems: Record<number, AuctionItem>; // clockNumber -> AuctionItem
  bidHistory: Record<number, BidRecord[]>; // itemId -> BidRecord[]
  priceHistory: Record<number, PriceHistory[]>; // itemId -> PriceHistory[]
  sessions: AuctionSession[];
  currentSession: AuctionSession | null;
  loading: boolean;
  error: string | null;
}

const initialState: AuctionState = {
  currentItems: {},
  bidHistory: {},
  priceHistory: {},
  sessions: [],
  currentSession: null,
  loading: false,
  error: null,
};

// 异步获取当前拍卖商品
export const fetchCurrentItems = createAsyncThunk(
  'auction/fetchCurrentItems',
  async () => {
    const response = await displayApi.getCurrentAuctionItems();
    return response.data;
  }
);

// 异步获取竞价历史
export const fetchBidHistory = createAsyncThunk(
  'auction/fetchBidHistory',
  async (itemId: number) => {
    const response = await displayApi.getBidHistory(itemId);
    return { itemId, bids: response.data };
  }
);

// 异步获取价格历史
export const fetchPriceHistory = createAsyncThunk(
  'auction/fetchPriceHistory',
  async (itemId: number) => {
    const response = await displayApi.getPriceHistory(itemId);
    return { itemId, history: response.data };
  }
);

// 异步获取拍卖会话
export const fetchAuctionSessions = createAsyncThunk(
  'auction/fetchSessions',
  async () => {
    const response = await displayApi.getAuctionSessions();
    return response.data;
  }
);

const auctionSlice = createSlice({
  name: 'auction',
  initialState,
  reducers: {
    updateAuctionItem: (state, action: PayloadAction<AuctionItem>) => {
      const item = action.payload;
      state.currentItems[item.clockNumber] = item;
    },
    
    updateMultipleItems: (state, action: PayloadAction<AuctionItem[]>) => {
      action.payload.forEach(item => {
        state.currentItems[item.clockNumber] = item;
      });
    },
    
    addBidRecord: (state, action: PayloadAction<{ itemId: number; bid: BidRecord }>) => {
      const { itemId, bid } = action.payload;
      if (!state.bidHistory[itemId]) {
        state.bidHistory[itemId] = [];
      }
      state.bidHistory[itemId].unshift(bid);
      
      // 保持最新的50条记录
      if (state.bidHistory[itemId].length > 50) {
        state.bidHistory[itemId] = state.bidHistory[itemId].slice(0, 50);
      }
    },
    
    addPricePoint: (state, action: PayloadAction<{ itemId: number; point: PriceHistory }>) => {
      const { itemId, point } = action.payload;
      if (!state.priceHistory[itemId]) {
        state.priceHistory[itemId] = [];
      }
      state.priceHistory[itemId].push(point);
      
      // 保持最新的100个数据点
      if (state.priceHistory[itemId].length > 100) {
        state.priceHistory[itemId] = state.priceHistory[itemId].slice(-100);
      }
    },
    
    updateItemStatus: (state, action: PayloadAction<{ clockNumber: number; status: AuctionItem['status'] }>) => {
      const { clockNumber, status } = action.payload;
      if (state.currentItems[clockNumber]) {
        state.currentItems[clockNumber].status = status;
      }
    },
    
    updateItemPrice: (state, action: PayloadAction<{ clockNumber: number; price: number; bidCount: number }>) => {
      const { clockNumber, price, bidCount } = action.payload;
      if (state.currentItems[clockNumber]) {
        state.currentItems[clockNumber].currentPrice = price;
        state.currentItems[clockNumber].bidCount = bidCount;
      }
    },
    
    setCurrentSession: (state, action: PayloadAction<AuctionSession>) => {
      state.currentSession = action.payload;
    },
    
    updateSessionProgress: (state, action: PayloadAction<{ completedItems: number; totalTurnover: number }>) => {
      if (state.currentSession) {
        state.currentSession.completedItems = action.payload.completedItems;
        state.currentSession.totalTurnover = action.payload.totalTurnover;
      }
    },
    
    clearAuctionData: (state) => {
      state.currentItems = {};
      state.bidHistory = {};
      state.priceHistory = {};
    },
    
    clearError: (state) => {
      state.error = null;
    },
  },
  
  extraReducers: (builder) => {
    builder
      .addCase(fetchCurrentItems.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCurrentItems.fulfilled, (state, action) => {
        state.loading = false;
        // 将数组转换为以clockNumber为key的对象
        const itemsMap: Record<number, AuctionItem> = {};
        action.payload.forEach((item: AuctionItem) => {
          itemsMap[item.clockNumber] = item;
        });
        state.currentItems = itemsMap;
      })
      .addCase(fetchCurrentItems.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取拍卖商品失败';
      })
      .addCase(fetchBidHistory.fulfilled, (state, action) => {
        const { itemId, bids } = action.payload;
        state.bidHistory[itemId] = bids;
      })
      .addCase(fetchPriceHistory.fulfilled, (state, action) => {
        const { itemId, history } = action.payload;
        state.priceHistory[itemId] = history;
      })
      .addCase(fetchAuctionSessions.fulfilled, (state, action) => {
        state.sessions = action.payload;
        // 设置当前活跃的会话
        const activeSession = action.payload.find((session: AuctionSession) => session.status === 'active');
        if (activeSession) {
          state.currentSession = activeSession;
        }
      });
  },
});

export const {
  updateAuctionItem,
  updateMultipleItems,
  addBidRecord,
  addPricePoint,
  updateItemStatus,
  updateItemPrice,
  setCurrentSession,
  updateSessionProgress,
  clearAuctionData,
  clearError,
} = auctionSlice.actions;

export default auctionSlice.reducer;

// Selectors
export const selectCurrentItems = (state: { auction: AuctionState }) => state.auction.currentItems;
export const selectItemByClock = (state: { auction: AuctionState }, clockNumber: number) =>
  state.auction.currentItems[clockNumber];
export const selectBidHistory = (state: { auction: AuctionState }, itemId: number) =>
  state.auction.bidHistory[itemId] || [];
export const selectPriceHistory = (state: { auction: AuctionState }, itemId: number) =>
  state.auction.priceHistory[itemId] || [];
export const selectCurrentSession = (state: { auction: AuctionState }) => state.auction.currentSession;
export const selectAuctionSessions = (state: { auction: AuctionState }) => state.auction.sessions;
export const selectAuctionLoading = (state: { auction: AuctionState }) => state.auction.loading;
export const selectAuctionError = (state: { auction: AuctionState }) => state.auction.error;
