import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ClockData, ClockLayout } from '../../types';
import { displayApi } from '../../services/api';

interface ClocksState {
  clocks: ClockData[];
  layouts: ClockLayout[];
  loading: boolean;
  error: string | null;
  selectedClock: number | null;
  lastUpdate: string;
}

const initialState: ClocksState = {
  clocks: [],
  layouts: [],
  loading: false,
  error: null,
  selectedClock: null,
  lastUpdate: '',
};

// 异步获取钟号状态
export const fetchClocksStatus = createAsyncThunk(
  'clocks/fetchStatus',
  async () => {
    const response = await displayApi.getClocksStatus();
    return response.data;
  }
);

// 异步更新钟号布局
export const updateClockLayout = createAsyncThunk(
  'clocks/updateLayout',
  async (layouts: ClockLayout[]) => {
    // 这里可以调用API保存布局到后端
    return layouts;
  }
);

const clocksSlice = createSlice({
  name: 'clocks',
  initialState,
  reducers: {
    updateClockData: (state, action: PayloadAction<ClockData>) => {
      const index = state.clocks.findIndex(
        clock => clock.clockNumber === action.payload.clockNumber
      );
      if (index !== -1) {
        state.clocks[index] = action.payload;
      } else {
        state.clocks.push(action.payload);
      }
      state.lastUpdate = new Date().toISOString();
    },
    
    updateMultipleClocks: (state, action: PayloadAction<ClockData[]>) => {
      action.payload.forEach(clockData => {
        const index = state.clocks.findIndex(
          clock => clock.clockNumber === clockData.clockNumber
        );
        if (index !== -1) {
          state.clocks[index] = clockData;
        } else {
          state.clocks.push(clockData);
        }
      });
      state.lastUpdate = new Date().toISOString();
    },
    
    selectClock: (state, action: PayloadAction<number | null>) => {
      state.selectedClock = action.payload;
    },
    
    setClockLayouts: (state, action: PayloadAction<ClockLayout[]>) => {
      state.layouts = action.payload;
    },
    
    updateClockLayout: (state, action: PayloadAction<ClockLayout>) => {
      const index = state.layouts.findIndex(
        layout => layout.clockNumber === action.payload.clockNumber
      );
      if (index !== -1) {
        state.layouts[index] = action.payload;
      } else {
        state.layouts.push(action.payload);
      }
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    resetClocks: (state) => {
      state.clocks = [];
      state.selectedClock = null;
      state.error = null;
    },
  },
  
  extraReducers: (builder) => {
    builder
      .addCase(fetchClocksStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClocksStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.clocks = action.payload;
        state.lastUpdate = new Date().toISOString();
      })
      .addCase(fetchClocksStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取钟号状态失败';
      })
      .addCase(updateClockLayout.fulfilled, (state, action) => {
        state.layouts = action.payload;
      });
  },
});

export const {
  updateClockData,
  updateMultipleClocks,
  selectClock,
  setClockLayouts,
  updateClockLayout: updateSingleClockLayout,
  clearError,
  resetClocks,
} = clocksSlice.actions;

export default clocksSlice.reducer;

// Selectors
export const selectAllClocks = (state: { clocks: ClocksState }) => state.clocks.clocks;
export const selectClockByNumber = (state: { clocks: ClocksState }, clockNumber: number) =>
  state.clocks.clocks.find(clock => clock.clockNumber === clockNumber);
export const selectActiveClocks = (state: { clocks: ClocksState }) =>
  state.clocks.clocks.filter(clock => clock.status === 'active');
export const selectSelectedClock = (state: { clocks: ClocksState }) => state.clocks.selectedClock;
export const selectClockLayouts = (state: { clocks: ClocksState }) => state.clocks.layouts;
export const selectClocksLoading = (state: { clocks: ClocksState }) => state.clocks.loading;
export const selectClocksError = (state: { clocks: ClocksState }) => state.clocks.error;
