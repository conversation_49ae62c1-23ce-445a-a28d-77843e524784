import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DisplaySettings } from '../../types';

interface SettingsState extends DisplaySettings {
  isFullscreen: boolean;
  soundEnabled: boolean;
  notificationEnabled: boolean;
  highlightWinningBids: boolean;
  showClockNumbers: boolean;
  compactMode: boolean;
}

const initialState: SettingsState = {
  theme: 'dark',
  autoRefresh: true,
  refreshInterval: 1000, // 1秒
  showBidHistory: true,
  showPriceChart: true,
  clockLayout: 'grid',
  fontSize: 'medium',
  isFullscreen: false,
  soundEnabled: true,
  notificationEnabled: true,
  highlightWinningBids: true,
  showClockNumbers: true,
  compactMode: false,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    updateSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      Object.assign(state, action.payload);
    },
    
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    
    toggleAutoRefresh: (state) => {
      state.autoRefresh = !state.autoRefresh;
    },
    
    setRefreshInterval: (state, action: PayloadAction<number>) => {
      state.refreshInterval = action.payload;
    },
    
    toggleFullscreen: (state) => {
      state.isFullscreen = !state.isFullscreen;
    },
    
    setClockLayout: (state, action: PayloadAction<'grid' | 'list'>) => {
      state.clockLayout = action.payload;
    },
    
    setFontSize: (state, action: PayloadAction<'small' | 'medium' | 'large'>) => {
      state.fontSize = action.payload;
    },
    
    toggleSound: (state) => {
      state.soundEnabled = !state.soundEnabled;
    },
    
    toggleNotification: (state) => {
      state.notificationEnabled = !state.notificationEnabled;
    },
    
    toggleBidHistory: (state) => {
      state.showBidHistory = !state.showBidHistory;
    },
    
    togglePriceChart: (state) => {
      state.showPriceChart = !state.showPriceChart;
    },
    
    toggleHighlightWinningBids: (state) => {
      state.highlightWinningBids = !state.highlightWinningBids;
    },
    
    toggleClockNumbers: (state) => {
      state.showClockNumbers = !state.showClockNumbers;
    },
    
    toggleCompactMode: (state) => {
      state.compactMode = !state.compactMode;
    },
    
    resetSettings: () => initialState,
  },
});

export const {
  updateSettings,
  setTheme,
  toggleAutoRefresh,
  setRefreshInterval,
  toggleFullscreen,
  setClockLayout,
  setFontSize,
  toggleSound,
  toggleNotification,
  toggleBidHistory,
  togglePriceChart,
  toggleHighlightWinningBids,
  toggleClockNumbers,
  toggleCompactMode,
  resetSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;

// Selectors
export const selectSettings = (state: { settings: SettingsState }) => state.settings;
export const selectTheme = (state: { settings: SettingsState }) => state.settings.theme;
export const selectAutoRefresh = (state: { settings: SettingsState }) => state.settings.autoRefresh;
export const selectRefreshInterval = (state: { settings: SettingsState }) => state.settings.refreshInterval;
export const selectIsFullscreen = (state: { settings: SettingsState }) => state.settings.isFullscreen;
export const selectClockLayout = (state: { settings: SettingsState }) => state.settings.clockLayout;
export const selectFontSize = (state: { settings: SettingsState }) => state.settings.fontSize;
