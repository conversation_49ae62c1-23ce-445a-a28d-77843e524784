import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { WebSocketMessage } from '../../types';

interface WebSocketState {
  socket: WebSocket | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastMessage: WebSocketMessage | null;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  reconnectInterval: number;
  messageHistory: WebSocketMessage[];
}

const initialState: WebSocketState = {
  socket: null,
  isConnected: false,
  isConnecting: false,
  error: null,
  lastMessage: null,
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
  reconnectInterval: 3000, // 3秒
  messageHistory: [],
};

const websocketSlice = createSlice({
  name: 'websocket',
  initialState,
  reducers: {
    setSocket: (state, action: PayloadAction<WebSocket | null>) => {
      state.socket = action.payload;
    },
    
    setConnecting: (state, action: PayloadAction<boolean>) => {
      state.isConnecting = action.payload;
    },
    
    setConnected: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
      if (action.payload) {
        state.reconnectAttempts = 0;
        state.error = null;
      }
    },
    
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isConnected = false;
      state.isConnecting = false;
    },
    
    addMessage: (state, action: PayloadAction<WebSocketMessage>) => {
      state.lastMessage = action.payload;
      state.messageHistory.unshift(action.payload);
      
      // 保持最新的100条消息
      if (state.messageHistory.length > 100) {
        state.messageHistory = state.messageHistory.slice(0, 100);
      }
    },
    
    incrementReconnectAttempts: (state) => {
      state.reconnectAttempts += 1;
    },
    
    resetReconnectAttempts: (state) => {
      state.reconnectAttempts = 0;
    },
    
    setReconnectInterval: (state, action: PayloadAction<number>) => {
      state.reconnectInterval = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    clearMessageHistory: (state) => {
      state.messageHistory = [];
      state.lastMessage = null;
    },
    
    disconnect: (state) => {
      if (state.socket) {
        state.socket.close();
        state.socket = null;
      }
      state.isConnected = false;
      state.isConnecting = false;
      state.reconnectAttempts = 0;
    },
  },
});

export const {
  setSocket,
  setConnecting,
  setConnected,
  setError,
  addMessage,
  incrementReconnectAttempts,
  resetReconnectAttempts,
  setReconnectInterval,
  clearError,
  clearMessageHistory,
  disconnect,
} = websocketSlice.actions;

export default websocketSlice.reducer;

// Selectors
export const selectSocket = (state: { websocket: WebSocketState }) => state.websocket.socket;
export const selectIsConnected = (state: { websocket: WebSocketState }) => state.websocket.isConnected;
export const selectIsConnecting = (state: { websocket: WebSocketState }) => state.websocket.isConnecting;
export const selectWebSocketError = (state: { websocket: WebSocketState }) => state.websocket.error;
export const selectLastMessage = (state: { websocket: WebSocketState }) => state.websocket.lastMessage;
export const selectMessageHistory = (state: { websocket: WebSocketState }) => state.websocket.messageHistory;
export const selectReconnectAttempts = (state: { websocket: WebSocketState }) => state.websocket.reconnectAttempts;
export const selectMaxReconnectAttempts = (state: { websocket: WebSocketState }) => state.websocket.maxReconnectAttempts;
