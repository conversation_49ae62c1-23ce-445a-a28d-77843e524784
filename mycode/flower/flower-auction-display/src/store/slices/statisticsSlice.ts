import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { MarketStatistics } from '../../types';
import { displayApi } from '../../services/api';

interface StatisticsState {
  marketStats: MarketStatistics | null;
  hourlyData: Array<{
    hour: string;
    auctions: number;
    turnover: number;
    avgPrice: number;
  }>;
  categoryStats: Array<{
    category: string;
    count: number;
    turnover: number;
    avgPrice: number;
  }>;
  realtimeMetrics: {
    currentBids: number;
    onlineUsers: number;
    activeAuctions: number;
    totalTurnover: number;
  };
  loading: boolean;
  error: string | null;
  lastUpdate: string;
}

const initialState: StatisticsState = {
  marketStats: null,
  hourlyData: [],
  categoryStats: [],
  realtimeMetrics: {
    currentBids: 0,
    onlineUsers: 0,
    activeAuctions: 0,
    totalTurnover: 0,
  },
  loading: false,
  error: null,
  lastUpdate: '',
};

// 异步获取市场统计
export const fetchMarketStatistics = createAsyncThunk(
  'statistics/fetchMarketStats',
  async () => {
    const response = await displayApi.getMarketStatistics();
    return response.data;
  }
);

// 异步获取小时数据
export const fetchHourlyData = createAsyncThunk(
  'statistics/fetchHourlyData',
  async (date: string) => {
    const response = await displayApi.getHourlyData(date);
    return response.data;
  }
);

// 异步获取分类统计
export const fetchCategoryStatistics = createAsyncThunk(
  'statistics/fetchCategoryStats',
  async () => {
    const response = await displayApi.getCategoryStatistics();
    return response.data;
  }
);

const statisticsSlice = createSlice({
  name: 'statistics',
  initialState,
  reducers: {
    updateMarketStats: (state, action: PayloadAction<MarketStatistics>) => {
      state.marketStats = action.payload;
      state.lastUpdate = new Date().toISOString();
    },
    
    updateRealtimeMetrics: (state, action: PayloadAction<Partial<StatisticsState['realtimeMetrics']>>) => {
      state.realtimeMetrics = {
        ...state.realtimeMetrics,
        ...action.payload,
      };
      state.lastUpdate = new Date().toISOString();
    },
    
    incrementBidCount: (state) => {
      state.realtimeMetrics.currentBids += 1;
    },
    
    updateOnlineUsers: (state, action: PayloadAction<number>) => {
      state.realtimeMetrics.onlineUsers = action.payload;
    },
    
    updateActiveAuctions: (state, action: PayloadAction<number>) => {
      state.realtimeMetrics.activeAuctions = action.payload;
    },
    
    addTurnover: (state, action: PayloadAction<number>) => {
      state.realtimeMetrics.totalTurnover += action.payload;
      if (state.marketStats) {
        state.marketStats.todayTurnover += action.payload;
      }
    },
    
    updateHourlyPoint: (state, action: PayloadAction<{
      hour: string;
      auctions: number;
      turnover: number;
      avgPrice: number;
    }>) => {
      const existingIndex = state.hourlyData.findIndex(
        item => item.hour === action.payload.hour
      );
      
      if (existingIndex !== -1) {
        state.hourlyData[existingIndex] = action.payload;
      } else {
        state.hourlyData.push(action.payload);
        // 保持24小时数据
        if (state.hourlyData.length > 24) {
          state.hourlyData = state.hourlyData.slice(-24);
        }
      }
    },
    
    updateCategoryStats: (state, action: PayloadAction<StatisticsState['categoryStats']>) => {
      state.categoryStats = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    resetStatistics: (state) => {
      state.marketStats = null;
      state.hourlyData = [];
      state.categoryStats = [];
      state.realtimeMetrics = {
        currentBids: 0,
        onlineUsers: 0,
        activeAuctions: 0,
        totalTurnover: 0,
      };
      state.error = null;
    },
  },
  
  extraReducers: (builder) => {
    builder
      .addCase(fetchMarketStatistics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMarketStatistics.fulfilled, (state, action) => {
        state.loading = false;
        state.marketStats = action.payload;
        state.lastUpdate = new Date().toISOString();
      })
      .addCase(fetchMarketStatistics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取市场统计失败';
      })
      .addCase(fetchHourlyData.fulfilled, (state, action) => {
        state.hourlyData = action.payload;
      })
      .addCase(fetchCategoryStatistics.fulfilled, (state, action) => {
        state.categoryStats = action.payload;
      });
  },
});

export const {
  updateMarketStats,
  updateRealtimeMetrics,
  incrementBidCount,
  updateOnlineUsers,
  updateActiveAuctions,
  addTurnover,
  updateHourlyPoint,
  updateCategoryStats,
  clearError,
  resetStatistics,
} = statisticsSlice.actions;

export default statisticsSlice.reducer;

// Selectors
export const selectMarketStats = (state: { statistics: StatisticsState }) => state.statistics.marketStats;
export const selectHourlyData = (state: { statistics: StatisticsState }) => state.statistics.hourlyData;
export const selectCategoryStats = (state: { statistics: StatisticsState }) => state.statistics.categoryStats;
export const selectRealtimeMetrics = (state: { statistics: StatisticsState }) => state.statistics.realtimeMetrics;
export const selectStatisticsLoading = (state: { statistics: StatisticsState }) => state.statistics.loading;
export const selectStatisticsError = (state: { statistics: StatisticsState }) => state.statistics.error;
export const selectLastUpdate = (state: { statistics: StatisticsState }) => state.statistics.lastUpdate;
