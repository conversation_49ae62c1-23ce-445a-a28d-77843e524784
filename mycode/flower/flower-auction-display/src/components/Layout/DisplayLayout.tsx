import React, { useEffect, useState } from 'react';
import { Layout, Row, Col, Card, Button, Space, Dropdown, MenuProps, Modal, message } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  SettingOutlined,
  ReloadOutlined,
  WifiOutlined,
  DisconnectOutlined,
  UserOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import authService from '../../services/authService';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import {
  selectIsFullscreen,
  selectClockLayout,
  toggleFullscreen,
  setClockLayout,
} from '../../store/slices/settingsSlice';
import {
  selectIsConnected,
  selectIsConnecting,
} from '../../store/slices/websocketSlice';
import {
  fetchClocksStatus,
  selectAllClocks,
} from '../../store/slices/clocksSlice';
import {
  fetchCurrentItems,
  fetchAuctionSessions,
} from '../../store/slices/auctionSlice';
import {
  fetchMarketStatistics,
} from '../../store/slices/statisticsSlice';
import ClockGrid from '../Clock/ClockGrid';
import StatisticsPanel from '../Statistics/StatisticsPanel';
import AuctionOverview from '../Auction/AuctionOverview';
import './DisplayLayout.css';

const { Header, Content } = Layout;

const DisplayLayout: React.FC = () => {
  const dispatch = useAppDispatch();
  const isFullscreen = useAppSelector(selectIsFullscreen);
  const clockLayout = useAppSelector(selectClockLayout);
  const isConnected = useAppSelector(selectIsConnected);
  const isConnecting = useAppSelector(selectIsConnecting);
  const clocks = useAppSelector(selectAllClocks);

  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  // 初始化数据和用户信息
  useEffect(() => {
    const initializeData = async () => {
      try {
        await Promise.all([
          dispatch(fetchClocksStatus()),
          dispatch(fetchCurrentItems()),
          dispatch(fetchAuctionSessions()),
          dispatch(fetchMarketStatistics()),
        ]);
        setLastRefresh(new Date());
      } catch (error) {
        console.error('Failed to initialize data:', error);
      }
    };

    // 获取当前用户信息
    const getCurrentUser = async () => {
      try {
        const user = authService.getStoredUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('Failed to get current user:', error);
      }
    };

    initializeData();
    getCurrentUser();
  }, [dispatch]);

  // 全屏切换
  const handleFullscreenToggle = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    dispatch(toggleFullscreen());
  };

  // 手动刷新数据
  const handleRefresh = async () => {
    try {
      await Promise.all([
        dispatch(fetchClocksStatus()),
        dispatch(fetchCurrentItems()),
        dispatch(fetchMarketStatistics()),
      ]);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to refresh data:', error);
    }
  };

  // 处理登出
  const handleLogout = async () => {
    try {
      await authService.logout();
      message.success('登出成功');
    } catch (error) {
      console.error('Logout error:', error);
      message.error('登出失败');
    } finally {
      setLogoutModalVisible(false);
    }
  };

  // 布局切换菜单
  const layoutMenuItems: MenuProps['items'] = [
    {
      key: 'grid',
      label: '网格布局',
      onClick: () => dispatch(setClockLayout('grid')),
    },
    {
      key: 'list',
      label: '列表布局',
      onClick: () => dispatch(setClockLayout('list')),
    },
  ];

  // 用户菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '用户信息',
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => setLogoutModalVisible(true),
    },
  ];

  // 连接状态指示器
  const ConnectionStatus = () => (
    <Space>
      {isConnecting ? (
        <WifiOutlined spin style={{ color: '#faad14' }} />
      ) : isConnected ? (
        <WifiOutlined style={{ color: '#52c41a' }} />
      ) : (
        <DisconnectOutlined style={{ color: '#ff4d4f' }} />
      )}
      <span style={{ fontSize: '12px', color: '#666' }}>
        {isConnecting ? '连接中...' : isConnected ? '已连接' : '未连接'}
      </span>
    </Space>
  );

  return (
    <Layout className={`display-layout ${isFullscreen ? 'fullscreen' : ''}`}>
      <Header className="display-header">
        <div className="header-left">
          <h1 className="display-title">昆明花卉拍卖大屏</h1>
          <ConnectionStatus />
        </div>
        
        <div className="header-center">
          <Space size="large">
            <span>活跃钟号: {clocks.filter(c => c.status === 'active').length}</span>
            <span>总钟号: {clocks.length}</span>
            <span>最后更新: {lastRefresh.toLocaleTimeString()}</span>
          </Space>
        </div>

        <div className="header-right">
          <Space>
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              title="刷新数据"
            />

            <Dropdown menu={{ items: layoutMenuItems }} placement="bottomRight">
              <Button
                type="text"
                icon={<SettingOutlined />}
                title="布局设置"
              />
            </Dropdown>

            <Button
              type="text"
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={handleFullscreenToggle}
              title={isFullscreen ? '退出全屏' : '进入全屏'}
            />

            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Button
                type="text"
                icon={<UserOutlined />}
                title={currentUser?.username || '用户'}
              >
                {currentUser?.username || '用户'}
              </Button>
            </Dropdown>
          </Space>
        </div>
      </Header>

      <Content className="display-content">
        <Row gutter={[16, 16]} style={{ height: '100%' }}>
          {/* 左侧统计面板 */}
          <Col xs={24} lg={6} className="statistics-column">
            <Card className="statistics-card" bodyStyle={{ padding: '12px' }}>
              <StatisticsPanel />
            </Card>
          </Col>

          {/* 中间钟号区域 */}
          <Col xs={24} lg={12} className="clocks-column">
            <Card 
              className="clocks-card" 
              bodyStyle={{ padding: '12px', height: '100%' }}
              title="拍卖钟号"
            >
              <ClockGrid layout={clockLayout} />
            </Card>
          </Col>

          {/* 右侧拍卖概览 */}
          <Col xs={24} lg={6} className="overview-column">
            <Card className="overview-card" bodyStyle={{ padding: '12px' }}>
              <AuctionOverview />
            </Card>
          </Col>
        </Row>
      </Content>

      {/* 登出确认弹窗 */}
      <Modal
        title="确认登出"
        open={logoutModalVisible}
        onOk={handleLogout}
        onCancel={() => setLogoutModalVisible(false)}
        okText="确认"
        cancelText="取消"
        okType="danger"
      >
        <p>您确定要退出登录吗？</p>
      </Modal>
    </Layout>
  );
};

export default DisplayLayout;
