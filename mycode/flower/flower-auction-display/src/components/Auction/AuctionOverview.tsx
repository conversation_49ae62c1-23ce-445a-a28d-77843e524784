import React from 'react';
import { Card, List, Typography, Tag, Space, Progress, Divider, Empty } from 'antd';
import {
  ClockCircleOutlined,
  DollarOutlined,
  EyeOutlined,
  TrophyOutlined,
  FireOutlined,
} from '@ant-design/icons';
import { useAppSelector } from '../../hooks/redux';
import {
  selectCurrentSession,
  selectCurrentItems,
} from '../../store/slices/auctionSlice';
import {
  selectActiveClocks,
} from '../../store/slices/clocksSlice';
import './AuctionOverview.css';

const { Title, Text } = Typography;

const AuctionOverview: React.FC = () => {
  const currentSession = useAppSelector(selectCurrentSession);
  const currentItems = useAppSelector(selectCurrentItems);
  const activeClocks = useAppSelector(selectActiveClocks);

  // 获取活跃的拍卖商品
  const activeItems = Object.values(currentItems).filter(item => 
    item && item.status === 'active'
  );

  // 获取热门商品（按关注数排序）
  const popularItems = Object.values(currentItems)
    .filter(item => item && item.watchCount > 0)
    .sort((a, b) => b.watchCount - a.watchCount)
    .slice(0, 5);

  // 获取高价商品（按当前价格排序）
  const highValueItems = Object.values(currentItems)
    .filter(item => item && item.currentPrice > item.startPrice)
    .sort((a, b) => b.currentPrice - a.currentPrice)
    .slice(0, 5);

  // 格式化价格
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(price);
  };

  // 计算会话进度
  const getSessionProgress = () => {
    if (!currentSession) return 0;
    return (currentSession.completedItems / currentSession.totalItems) * 100;
  };

  return (
    <div className="auction-overview">
      {/* 当前会话信息 */}
      {currentSession && (
        <div className="session-info">
          <Title level={5} className="panel-title">当前拍卖会</Title>
          <Card size="small" className="session-card">
            <div className="session-header">
              <Text strong>{currentSession.name}</Text>
              <Tag color={currentSession.status === 'active' ? 'green' : 'blue'}>
                {currentSession.status === 'active' ? '进行中' : '待开始'}
              </Tag>
            </div>
            
            <div className="session-progress">
              <Text type="secondary">进度</Text>
              <Progress
                percent={getSessionProgress()}
                size="small"
                format={() => `${currentSession.completedItems}/${currentSession.totalItems}`}
              />
            </div>
            
            <div className="session-stats">
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div className="stat-row">
                  <Text type="secondary">总成交额:</Text>
                  <Text strong style={{ color: '#f5222d' }}>
                    {formatPrice(currentSession.totalTurnover)}
                  </Text>
                </div>
                <div className="stat-row">
                  <Text type="secondary">开始时间:</Text>
                  <Text>{new Date(currentSession.startTime).toLocaleTimeString()}</Text>
                </div>
              </Space>
            </div>
          </Card>
        </div>
      )}

      <Divider style={{ margin: '16px 0' }} />

      {/* 活跃拍卖 */}
      <div className="active-auctions">
        <Title level={5} className="panel-title">
          <Space>
            <FireOutlined style={{ color: '#f5222d' }} />
            活跃拍卖 ({activeItems.length})
          </Space>
        </Title>
        
        {activeItems.length > 0 ? (
          <List
            size="small"
            dataSource={activeItems}
            renderItem={(item) => (
              <List.Item className="active-item">
                <div className="item-content">
                  <div className="item-header">
                    <Text strong ellipsis style={{ maxWidth: '120px' }}>
                      {item.productName}
                    </Text>
                    <Tag color="red">钟号 {item.clockNumber}</Tag>
                  </div>
                  
                  <div className="item-price">
                    <Space>
                      <DollarOutlined style={{ color: '#52c41a' }} />
                      <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
                        {formatPrice(item.currentPrice)}
                      </Text>
                    </Space>
                  </div>
                  
                  <div className="item-stats">
                    <Space size="small">
                      <Text type="secondary">{item.bidCount} 次出价</Text>
                      <Text type="secondary">{item.watchCount} 关注</Text>
                    </Space>
                  </div>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <Empty 
            description="暂无活跃拍卖" 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ margin: '20px 0' }}
          />
        )}
      </div>

      <Divider style={{ margin: '16px 0' }} />

      {/* 热门商品 */}
      <div className="popular-items">
        <Title level={5} className="panel-title">
          <Space>
            <EyeOutlined style={{ color: '#1890ff' }} />
            热门关注
          </Space>
        </Title>
        
        {popularItems.length > 0 ? (
          <List
            size="small"
            dataSource={popularItems}
            renderItem={(item, index) => (
              <List.Item className="popular-item">
                <div className="item-content">
                  <div className="item-rank">
                    <Tag color={index === 0 ? 'gold' : index === 1 ? 'silver' : 'default'}>
                      {index + 1}
                    </Tag>
                  </div>
                  
                  <div className="item-info">
                    <Text strong ellipsis style={{ maxWidth: '100px' }}>
                      {item.productName}
                    </Text>
                    <div className="item-meta">
                      <Space size="small">
                        <Text type="secondary">{item.watchCount} 关注</Text>
                        <Text type="secondary">{formatPrice(item.currentPrice)}</Text>
                      </Space>
                    </div>
                  </div>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <Empty 
            description="暂无热门商品" 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ margin: '20px 0' }}
          />
        )}
      </div>

      <Divider style={{ margin: '16px 0' }} />

      {/* 高价商品 */}
      <div className="high-value-items">
        <Title level={5} className="panel-title">
          <Space>
            <TrophyOutlined style={{ color: '#faad14' }} />
            高价商品
          </Space>
        </Title>
        
        {highValueItems.length > 0 ? (
          <List
            size="small"
            dataSource={highValueItems}
            renderItem={(item, index) => (
              <List.Item className="high-value-item">
                <div className="item-content">
                  <div className="item-rank">
                    <Tag color={index === 0 ? 'gold' : index === 1 ? 'silver' : 'default'}>
                      {index + 1}
                    </Tag>
                  </div>
                  
                  <div className="item-info">
                    <Text strong ellipsis style={{ maxWidth: '100px' }}>
                      {item.productName}
                    </Text>
                    <div className="item-meta">
                      <Space size="small">
                        <Text style={{ color: '#f5222d', fontWeight: 'bold' }}>
                          {formatPrice(item.currentPrice)}
                        </Text>
                        <Text type="secondary">
                          +{((item.currentPrice - item.startPrice) / item.startPrice * 100).toFixed(1)}%
                        </Text>
                      </Space>
                    </div>
                  </div>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <Empty 
            description="暂无高价商品" 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ margin: '20px 0' }}
          />
        )}
      </div>
    </div>
  );
};

export default AuctionOverview;
