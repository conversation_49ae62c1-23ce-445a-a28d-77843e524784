import React, { useEffect, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { WebSocketManager } from '../../services/api';
import {
  setSocket,
  setConnected,
  setConnecting,
  setError,
  addMessage,
  incrementReconnectAttempts,
} from '../../store/slices/websocketSlice';
import {
  updateClockData,
  updateMultipleClocks,
} from '../../store/slices/clocksSlice';
import {
  updateAuctionItem,
  addBidRecord,
  addPricePoint,
  updateItemPrice,
  updateItemStatus,
} from '../../store/slices/auctionSlice';
import {
  updateMarketStats,
  updateRealtimeMetrics,
  incrementBidCount,
  updateOnlineUsers,
  addTurnover,
} from '../../store/slices/statisticsSlice';
import { selectAutoRefresh } from '../../store/slices/settingsSlice';

interface WebSocketProviderProps {
  children: React.ReactNode;
}

const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const autoRefresh = useAppSelector(selectAutoRefresh);
  const wsManagerRef = useRef<WebSocketManager | null>(null);

  useEffect(() => {
    if (!autoRefresh) return;

    // 创建WebSocket连接
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8080/ws/display';
    wsManagerRef.current = new WebSocketManager(wsUrl);

    const handleMessage = (message: any) => {
      dispatch(addMessage(message));

      // 根据消息类型处理不同的数据更新
      switch (message.type) {
        case 'clock_update':
          if (Array.isArray(message.data)) {
            dispatch(updateMultipleClocks(message.data));
          } else {
            dispatch(updateClockData(message.data));
          }
          break;

        case 'auction_item_update':
          dispatch(updateAuctionItem(message.data));
          break;

        case 'bid_update':
          dispatch(addBidRecord({
            itemId: message.data.itemId,
            bid: message.data.bid,
          }));
          dispatch(incrementBidCount());
          
          // 更新商品价格
          if (message.data.bid.isWinning) {
            dispatch(updateItemPrice({
              clockNumber: message.data.clockNumber,
              price: message.data.bid.price,
              bidCount: message.data.bidCount,
            }));
          }
          break;

        case 'price_change':
          dispatch(addPricePoint({
            itemId: message.data.itemId,
            point: {
              timestamp: message.data.timestamp,
              price: message.data.price,
              volume: message.data.volume,
            },
          }));
          break;

        case 'auction_start':
          dispatch(updateItemStatus({
            clockNumber: message.data.clockNumber,
            status: 'active',
          }));
          break;

        case 'auction_end':
          dispatch(updateItemStatus({
            clockNumber: message.data.clockNumber,
            status: message.data.status, // 'sold' or 'unsold'
          }));
          
          if (message.data.status === 'sold') {
            dispatch(addTurnover(message.data.finalPrice));
          }
          break;

        case 'statistics_update':
          if (message.data.marketStats) {
            dispatch(updateMarketStats(message.data.marketStats));
          }
          if (message.data.realtimeMetrics) {
            dispatch(updateRealtimeMetrics(message.data.realtimeMetrics));
          }
          break;

        case 'online_users_update':
          dispatch(updateOnlineUsers(message.data.count));
          break;

        default:
          console.log('Unknown message type:', message.type);
      }
    };

    const handleConnect = () => {
      console.log('WebSocket connected to display service');
      dispatch(setConnected(true));
      dispatch(setConnecting(false));
      
      // 发送订阅消息
      wsManagerRef.current?.send({
        type: 'subscribe',
        channels: ['clocks', 'auctions', 'bids', 'statistics'],
      });
    };

    const handleDisconnect = () => {
      console.log('WebSocket disconnected from display service');
      dispatch(setConnected(false));
      dispatch(incrementReconnectAttempts());
    };

    const handleError = (error: any) => {
      console.error('WebSocket error:', error);
      dispatch(setError('WebSocket连接错误'));
      dispatch(setConnected(false));
      dispatch(setConnecting(false));
    };

    // 连接WebSocket
    dispatch(setConnecting(true));
    wsManagerRef.current.connect({
      onMessage: handleMessage,
      onConnect: handleConnect,
      onDisconnect: handleDisconnect,
      onError: handleError,
    });

    // 设置socket到store
    dispatch(setSocket(wsManagerRef.current.getSocket()));

    // 清理函数
    return () => {
      if (wsManagerRef.current) {
        wsManagerRef.current.disconnect();
        dispatch(setSocket(null));
        dispatch(setConnected(false));
      }
    };
  }, [dispatch, autoRefresh]);

  return <>{children}</>;
};

export default WebSocketProvider;
