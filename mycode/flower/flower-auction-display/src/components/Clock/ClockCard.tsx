import React from 'react';
import { Card, Badge, Typography, Space, Tag, Progress, Statistic } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { ClockData, AuctionItem } from '../../types';
import './ClockCard.css';

const { Text, Title } = Typography;

interface ClockCardProps {
  clock: ClockData;
  currentItem?: AuctionItem;
  layout: 'grid' | 'list';
}

const ClockCard: React.FC<ClockCardProps> = ({ clock, currentItem, layout }) => {
  // 状态图标映射
  const getStatusIcon = (status: ClockData['status']) => {
    switch (status) {
      case 'active':
        return <PlayCircleOutlined style={{ color: '#52c41a' }} />;
      case 'paused':
        return <PauseCircleOutlined style={{ color: '#faad14' }} />;
      case 'idle':
        return <StopOutlined style={{ color: '#d9d9d9' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <StopOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  // 状态颜色映射
  const getStatusColor = (status: ClockData['status']) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'paused':
        return 'warning';
      case 'idle':
        return 'default';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  // 状态文本映射
  const getStatusText = (status: ClockData['status']) => {
    switch (status) {
      case 'active':
        return '拍卖中';
      case 'paused':
        return '暂停';
      case 'idle':
        return '空闲';
      case 'error':
        return '错误';
      default:
        return '未知';
    }
  };

  // 计算拍卖进度
  const getAuctionProgress = () => {
    if (!currentItem || currentItem.status !== 'active') return 0;
    
    const startTime = new Date(currentItem.startTime).getTime();
    const now = Date.now();
    const elapsed = now - startTime;
    const estimatedDuration = 5 * 60 * 1000; // 假设每个商品拍卖5分钟
    
    return Math.min((elapsed / estimatedDuration) * 100, 100);
  };

  // 格式化价格
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(price);
  };

  const cardTitle = (
    <div className="clock-card-title">
      <Space>
        <Badge
          status={getStatusColor(clock.status) as any}
          text={
            <span className="clock-number">
              {getStatusIcon(clock.status)}
              <span className="clock-number-text">钟号 {clock.clockNumber}</span>
            </span>
          }
        />
        <Tag color={getStatusColor(clock.status)}>
          {getStatusText(clock.status)}
        </Tag>
      </Space>
    </div>
  );

  const cardContent = currentItem ? (
    <div className="clock-card-content">
      {/* 商品信息 */}
      <div className="item-info">
        <Title level={5} className="item-name" ellipsis={{ tooltip: true }}>
          {currentItem.productName}
        </Title>
        <Text type="secondary" className="batch-number">
          批次: {currentItem.batchNumber}
        </Text>
      </div>

      {/* 价格信息 */}
      <div className="price-info">
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div className="price-row">
            <Statistic
              title="当前价格"
              value={currentItem.currentPrice}
              formatter={(value) => formatPrice(Number(value))}
              valueStyle={{ 
                color: clock.status === 'active' ? '#52c41a' : '#666',
                fontSize: layout === 'grid' ? '18px' : '16px',
                fontWeight: 'bold',
              }}
            />
          </div>
          
          <div className="price-details">
            <Space split={<span>|</span>}>
              <Text type="secondary">
                起拍: {formatPrice(currentItem.startPrice)}
              </Text>
              <Text type="secondary">
                加价: {formatPrice(currentItem.stepPrice)}
              </Text>
            </Space>
          </div>
        </Space>
      </div>

      {/* 拍卖统计 */}
      <div className="auction-stats">
        <Space>
          <Space size="small">
            <DollarOutlined />
            <Text>{currentItem.bidCount} 次出价</Text>
          </Space>
          <Space size="small">
            <EyeOutlined />
            <Text>{currentItem.watchCount} 关注</Text>
          </Space>
        </Space>
      </div>

      {/* 进度条 */}
      {clock.status === 'active' && (
        <div className="auction-progress">
          <Progress
            percent={getAuctionProgress()}
            size="small"
            status="active"
            showInfo={false}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </div>
      )}

      {/* 商品详情 */}
      <div className="item-details">
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div className="detail-row">
            <Text type="secondary">规格: </Text>
            <Text>{currentItem.quantity} {currentItem.unit}</Text>
          </div>
          <div className="detail-row">
            <Text type="secondary">等级: </Text>
            <Tag color="blue">{currentItem.grade}</Tag>
          </div>
        </Space>
      </div>
    </div>
  ) : (
    <div className="clock-card-empty">
      <Text type="secondary">暂无拍卖商品</Text>
    </div>
  );

  return (
    <Card
      className={`clock-card clock-card-${layout} clock-status-${clock.status}`}
      title={cardTitle}
      size="small"
      hoverable
      bodyStyle={{ 
        padding: layout === 'grid' ? '12px' : '16px',
        height: layout === 'grid' ? 'auto' : '200px',
      }}
    >
      {cardContent}
    </Card>
  );
};

export default ClockCard;
