import React from 'react';
import { Card, Statistic, Row, Col, Progress, Typography, Space, Divider } from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { useAppSelector } from '../../hooks/redux';
import {
  selectMarketStats,
  selectRealtimeMetrics,
  selectHourlyData,
  selectCategoryStats,
} from '../../store/slices/statisticsSlice';
import './StatisticsPanel.css';

const { Title, Text } = Typography;

const StatisticsPanel: React.FC = () => {
  const marketStats = useAppSelector(selectMarketStats);
  const realtimeMetrics = useAppSelector(selectRealtimeMetrics);
  const hourlyData = useAppSelector(selectHourlyData);
  const categoryStats = useAppSelector(selectCategoryStats);

  // 格式化金额
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // 小时数据图表配置
  const getHourlyChartOption = () => ({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: hourlyData.map(item => item.hour),
      axisLabel: {
        fontSize: 10,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '成交额',
        position: 'left',
        axisLabel: {
          formatter: (value: number) => `${(value / 10000).toFixed(0)}万`,
          fontSize: 10,
        },
      },
      {
        type: 'value',
        name: '拍卖数',
        position: 'right',
        axisLabel: {
          fontSize: 10,
        },
      },
    ],
    series: [
      {
        name: '成交额',
        type: 'bar',
        yAxisIndex: 0,
        data: hourlyData.map(item => item.turnover),
        itemStyle: {
          color: '#1890ff',
        },
      },
      {
        name: '拍卖数',
        type: 'line',
        yAxisIndex: 1,
        data: hourlyData.map(item => item.auctions),
        itemStyle: {
          color: '#52c41a',
        },
      },
    ],
  });

  // 分类统计图表配置
  const getCategoryChartOption = () => ({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    series: [
      {
        name: '分类统计',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 12,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: categoryStats.map(item => ({
          value: item.turnover,
          name: item.category,
        })),
      },
    ],
  });

  return (
    <div className="statistics-panel">
      {/* 实时指标 */}
      <div className="realtime-metrics">
        <Title level={5} className="panel-title">实时数据</Title>
        <Row gutter={[8, 8]}>
          <Col span={12}>
            <Card size="small" className="metric-card">
              <Statistic
                title="在线用户"
                value={realtimeMetrics.onlineUsers}
                prefix={<UserOutlined />}
                valueStyle={{ fontSize: '16px', color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" className="metric-card">
              <Statistic
                title="活跃拍卖"
                value={realtimeMetrics.activeAuctions}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ fontSize: '16px', color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" className="metric-card">
              <Statistic
                title="当前出价"
                value={realtimeMetrics.currentBids}
                prefix={<RiseOutlined />}
                valueStyle={{ fontSize: '16px', color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" className="metric-card">
              <Statistic
                title="今日成交"
                value={realtimeMetrics.totalTurnover}
                formatter={(value) => formatCurrency(Number(value))}
                prefix={<DollarOutlined />}
                valueStyle={{ fontSize: '14px', color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>
      </div>

      <Divider style={{ margin: '16px 0' }} />

      {/* 市场概览 */}
      {marketStats && (
        <div className="market-overview">
          <Title level={5} className="panel-title">市场概览</Title>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <div className="overview-item">
              <Text type="secondary">今日拍卖:</Text>
              <Text strong>{marketStats.todayAuctions} 场</Text>
            </div>
            <div className="overview-item">
              <Text type="secondary">今日成交额:</Text>
              <Text strong style={{ color: '#f5222d' }}>
                {formatCurrency(marketStats.todayTurnover)}
              </Text>
            </div>
            <div className="overview-item">
              <Text type="secondary">平均价格:</Text>
              <Text strong>{formatCurrency(marketStats.averagePrice)}</Text>
            </div>
            <div className="overview-item">
              <Text type="secondary">完成批次:</Text>
              <Text strong>{marketStats.completedBatches}</Text>
            </div>
            <div className="overview-item">
              <Text type="secondary">待拍批次:</Text>
              <Text strong>{marketStats.pendingBatches}</Text>
            </div>
          </Space>
        </div>
      )}

      <Divider style={{ margin: '16px 0' }} />

      {/* 钟号状态 */}
      {marketStats && (
        <div className="clock-status">
          <Title level={5} className="panel-title">钟号状态</Title>
          <div className="clock-progress">
            <div className="progress-item">
              <Text>活跃钟号</Text>
              <Progress
                percent={(marketStats.activeClocks / marketStats.totalClocks) * 100}
                size="small"
                status="active"
                format={() => `${marketStats.activeClocks}/${marketStats.totalClocks}`}
              />
            </div>
          </div>
        </div>
      )}

      <Divider style={{ margin: '16px 0' }} />

      {/* 小时趋势图 */}
      {hourlyData.length > 0 && (
        <div className="hourly-chart">
          <Title level={5} className="panel-title">小时趋势</Title>
          <ReactECharts
            option={getHourlyChartOption()}
            style={{ height: '200px' }}
            opts={{ renderer: 'svg' }}
          />
        </div>
      )}

      <Divider style={{ margin: '16px 0' }} />

      {/* 分类统计 */}
      {categoryStats.length > 0 && (
        <div className="category-chart">
          <Title level={5} className="panel-title">分类占比</Title>
          <ReactECharts
            option={getCategoryChartOption()}
            style={{ height: '180px' }}
            opts={{ renderer: 'svg' }}
          />
        </div>
      )}
    </div>
  );
};

export default StatisticsPanel;
