/* 统计面板样式 */

.statistics-panel {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
}

.panel-title {
  margin: 0 0 12px 0 !important;
  color: #1890ff;
  font-size: 14px !important;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 实时指标 */
.realtime-metrics .metric-card {
  background: rgba(24, 144, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.realtime-metrics .metric-card:hover {
  background: rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.realtime-metrics .ant-statistic-title {
  font-size: 11px;
  margin-bottom: 4px;
}

.realtime-metrics .ant-statistic-content {
  font-size: 14px;
}

/* 市场概览 */
.market-overview {
  font-size: 12px;
}

.overview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.overview-item:last-child {
  border-bottom: none;
}

/* 钟号状态 */
.clock-status {
  font-size: 12px;
}

.clock-progress {
  margin-top: 8px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-item .ant-progress {
  margin: 0;
}

/* 图表容器 */
.hourly-chart,
.category-chart {
  margin-top: 8px;
}

.hourly-chart .echarts-for-react,
.category-chart .echarts-for-react {
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .statistics-panel {
    padding: 4px;
  }
  
  .panel-title {
    font-size: 13px !important;
    margin-bottom: 8px !important;
  }
  
  .realtime-metrics .ant-col {
    margin-bottom: 8px;
  }
}

/* 暗色主题适配 */
[data-theme='dark'] .realtime-metrics .metric-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .overview-item {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .hourly-chart .echarts-for-react,
[data-theme='dark'] .category-chart .echarts-for-react {
  background: rgba(0, 0, 0, 0.3);
}

/* 滚动条样式 */
.statistics-panel::-webkit-scrollbar {
  width: 4px;
}

.statistics-panel::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.statistics-panel::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.3);
  border-radius: 2px;
}

.statistics-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.5);
}
