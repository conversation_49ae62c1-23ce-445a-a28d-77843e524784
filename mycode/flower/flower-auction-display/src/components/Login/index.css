.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: backgroundMove 20s ease-in-out infinite;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
}

@keyframes backgroundMove {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(5deg);
  }
}

@keyframes cardFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.login-card {
  width: 420px;
  max-width: 90vw;
  padding: 48px;
  border-radius: 20px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.96);
  border: 1px solid rgba(255, 255, 255, 0.25);
  position: relative;
  z-index: 1;
  animation: cardFadeIn 0.6s ease-out;
  transition: all 0.3s ease;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-icon {
  font-size: 52px;
  color: #1890ff;
  margin-bottom: 20px;
  display: block;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.login-title {
  margin-bottom: 8px !important;
  color: #262626;
  font-weight: 600;
}

.login-subtitle {
  color: #8c8c8c;
  font-size: 14px;
}

.login-form {
  margin-top: 24px;
}

.login-form .ant-form-item {
  margin-bottom: 24px;
}

.login-form .ant-input-affix-wrapper,
.login-form .ant-input,
.login-form .ant-input-password {
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  background: #ffffff !important;
  color: #262626 !important;
  transition: all 0.3s ease;
  height: 52px;
  font-size: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.login-form .ant-input-affix-wrapper .ant-input,
.login-form .ant-input-password .ant-input {
  background: transparent !important;
  color: #262626 !important;
  font-size: 15px;
  font-weight: 400;
}

.login-form .ant-input-affix-wrapper .anticon,
.login-form .ant-input-password .anticon {
  color: #8c8c8c;
  font-size: 16px;
}

/* 专门针对密码输入框的样式 */
.login-form .ant-input-password {
  background: #ffffff !important;
}

.login-form .ant-input-password .ant-input {
  background: #ffffff !important;
  color: #262626 !important;
}

.login-form .ant-input-password .ant-input-suffix {
  background: transparent;
}

.login-form .ant-input-password .ant-input-suffix .anticon {
  color: #8c8c8c;
}

.login-form .ant-input-affix-wrapper:hover,
.login-form .ant-input:hover,
.login-form .ant-input-password:hover {
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(64, 169, 255, 0.15);
  background: #ffffff !important;
  transform: translateY(-1px);
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused,
.login-form .ant-input:focus,
.login-form .ant-input-password:focus,
.login-form .ant-input-password.ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  background: #ffffff !important;
}

.login-form .ant-input::placeholder {
  color: #bfbfbf;
  font-size: 14px;
  font-weight: 400;
  transition: color 0.3s ease;
}

.login-form .ant-input:focus::placeholder {
  color: #d9d9d9;
}

.login-button {
  height: 52px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.login-button:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);
  transform: translateY(-2px);
}

.login-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.login-footer .ant-typography {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-card {
    width: 100%;
    max-width: 320px;
    padding: 24px;
    margin: 20px;
  }
  
  .login-icon {
    font-size: 36px;
  }
  
  .login-title {
    font-size: 20px !important;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(20, 20, 20, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .login-title {
    color: #ffffff;
  }

  .login-subtitle {
    color: #a6a6a6;
  }

  .login-footer {
    border-top: 1px solid #303030;
  }

  .login-form .ant-input-affix-wrapper,
  .login-form .ant-input,
  .login-form .ant-input-password {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #262626 !important;
  }

  .login-form .ant-input-affix-wrapper .ant-input,
  .login-form .ant-input-password .ant-input {
    background: transparent !important;
    color: #262626 !important;
  }

  .login-form .ant-input-affix-wrapper:hover,
  .login-form .ant-input:hover,
  .login-form .ant-input-password:hover {
    background: #ffffff !important;
    border-color: #40a9ff;
  }

  .login-form .ant-input-affix-wrapper:focus,
  .login-form .ant-input-affix-wrapper-focused,
  .login-form .ant-input:focus,
  .login-form .ant-input-password:focus,
  .login-form .ant-input-password.ant-input-affix-wrapper-focused {
    background: #ffffff !important;
    border-color: #1890ff;
  }
}
