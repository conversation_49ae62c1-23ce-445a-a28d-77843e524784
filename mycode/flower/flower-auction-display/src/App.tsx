import React from 'react';
import { Provider } from 'react-redux';
import { ConfigProvider, theme } from 'antd';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { store } from './store';
import { useAppSelector } from './hooks/redux';
import { selectTheme } from './store/slices/settingsSlice';
import DisplayLayout from './components/Layout/DisplayLayout';
import WebSocketProvider from './components/WebSocket/WebSocketProvider';
import AuthGuard from './components/AuthGuard';
import Login from './components/Login';
import './App.css';

const AppContent: React.FC = () => {
  const currentTheme = useAppSelector(selectTheme);

  return (
    <ConfigProvider
      theme={{
        algorithm: currentTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route
            path="/*"
            element={
              <AuthGuard>
                <WebSocketProvider>
                  <DisplayLayout />
                </WebSocketProvider>
              </AuthGuard>
            }
          />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

export default App;
