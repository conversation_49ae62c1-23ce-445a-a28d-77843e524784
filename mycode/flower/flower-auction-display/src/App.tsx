import React from 'react';
import { Provider } from 'react-redux';
import { ConfigProvider, theme } from 'antd';
import { store } from './store';
import { useAppSelector } from './hooks/redux';
import { selectTheme } from './store/slices/settingsSlice';
import DisplayLayout from './components/Layout/DisplayLayout';
import WebSocketProvider from './components/WebSocket/WebSocketProvider';
import './App.css';

const AppContent: React.FC = () => {
  const currentTheme = useAppSelector(selectTheme);

  return (
    <ConfigProvider
      theme={{
        algorithm: currentTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <WebSocketProvider>
        <DisplayLayout />
      </WebSocketProvider>
    </ConfigProvider>
  );
};

function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

export default App;
