# buglist - 执行状态跟踪

## 首先请理解项目 README.md,原始需求:sell_flower.md,以及严格执行架构目录docs下设计
✅ **已完成** - 已理解项目架构和需求

## 拍卖师端
- ✅ **已完成** - ui交互在pc端（chrome浏览器）显示有问题，优化为需要兼容支持手机端ios/android和pc端大多数主流浏览器
- 🔄 **进行中** - 请实现工作台前后端相关完整逻辑
- ⏳ **待处理** - 请实现批次管理后端相关完整逻辑
- ⏳ **待处理** - 请实现统计管理后端相关完整逻辑
- ⏳ **待处理** - 请实现系统设置后端相关完整逻辑

## 购买商端
- ✅ **已完成** - ui交互在pc端（chrome浏览器）显示有问题,优化为需要兼容支持手机端ios/android和pc端大多数主流浏览器
- 🔄 **进行中** - 请实现首页前后端相关完整逻辑,并解决接口500错误问题:http://localhost:8081/api/v1/batches?page=1&size=20&status=0,回包:{
    "success": false,
    "error": "Error 1146 (42S02): Table 'product_db.batch' doesn't exist",
    "message": ""
}
- ⏳ **待处理** - 请实现我的关注页前后端相关完整逻辑
- ⏳ **待处理** - 请实现我的订单页前后端相关逻辑
- ⏳ **待处理** - 请实现我的出价页前后端相关逻辑
- ⏳ **待处理** - 请实现账户管理前后端相关逻辑

## 显示屏端.
- 🔄 **进行中** - 登陆页面,用户名输入框,密码输入框黑色太丑,请优化ui
- 🔄 **进行中** - 显示屏端登陆账号如何设置角色,权限?
