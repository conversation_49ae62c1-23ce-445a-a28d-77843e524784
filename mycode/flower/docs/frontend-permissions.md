# 前端权限管理系统

## 概述

花卉拍卖系统的前端权限管理基于后端的角色权限系统，确保不同角色的用户只能访问相应的功能模块。

## 角色与前端应用映射

| 角色 | 角色代码 | 主要前端应用 | 权限范围 |
|------|----------|--------------|----------|
| 系统管理员 | ADMIN | 管理端 | 所有功能 |
| 拍卖师 | AUCTIONEER | 拍卖师端 | 拍卖控制、商品管理 |
| 买家 | BUYER | 购买商端 | 竞拍、查看 |
| 质检员 | QUALITY_INSPECTOR | 管理端 | 商品审核 |
| 显示屏操作员 | DISPLAY_OPERATOR | 投屏端 | 数据展示 |
| 财务官 | FINANCE | 管理端 | 财务管理 |
| 运营 | OPERATOR | 管理端 | 所有功能 |

## 前端权限实现

### 1. 管理端 (flower-auction-admin)

#### 权限Hook
```typescript
// 使用权限Hook
import { usePermissions } from '../hooks/usePermissions';

const { hasPermission, hasRole, isSuperAdmin } = usePermissions();

// 检查权限
if (hasPermission('USER_CREATE')) {
  // 显示创建用户按钮
}
```

#### 权限保护组件
```typescript
import PermissionGuard from '../components/PermissionGuard';

<PermissionGuard permission="USER_MANAGE">
  <UserManagement />
</PermissionGuard>

<PermissionGuard role="ADMIN" fallback={<div>权限不足</div>}>
  <AdminPanel />
</PermissionGuard>
```

#### 权限按钮
```typescript
import { PermissionButton } from '../components/PermissionGuard';

<PermissionButton permission="USER_DELETE">
  <Button danger>删除用户</Button>
</PermissionButton>
```

### 2. 拍卖师端 (flower-auction-auctioneer)

#### 角色检查
```typescript
import { authService } from '../services/authService';

// 检查是否为拍卖师
if (authService.isAuctioneer(user)) {
  // 允许访问拍卖控制功能
}

// 检查钟号权限
if (authService.hasClockPermission(clockNumber, user)) {
  // 允许操作该钟号
}
```

#### AuthGuard组件
```typescript
<AuthGuard requireAuctioneer requiredClockNumbers={[1, 2]}>
  <AuctionControl />
</AuthGuard>
```

### 3. 购买商端 (flower-auction-buyer)

#### 买家权限检查
```typescript
import { authService } from '../services/authService';

// 检查是否为买家
if (authService.isBuyer(user)) {
  // 允许竞拍功能
}

// 检查余额
if (authService.hasEnoughBalance(amount, user)) {
  // 允许出价
}
```

### 4. 投屏端 (flower-auction-display)

#### 显示权限检查
```typescript
import { authService } from '../services/authService';

// 检查投屏权限
if (authService.hasDisplayPermission(user)) {
  // 允许访问投屏功能
}
```

## 权限常量定义

### 权限代码
```typescript
export const PERMISSIONS = {
  // 用户管理
  USER_MANAGE: 'USER_MANAGE',
  USER_CREATE: 'USER_CREATE',
  USER_EDIT: 'USER_EDIT',
  USER_DELETE: 'USER_DELETE',
  
  // 角色管理
  ROLE_MANAGE: 'ROLE_MANAGE',
  ROLE_CREATE: 'ROLE_CREATE',
  ROLE_EDIT: 'ROLE_EDIT',
  
  // 商品管理
  PRODUCT_MANAGE: 'PRODUCT_MANAGE',
  PRODUCT_CREATE: 'PRODUCT_CREATE',
  PRODUCT_AUDIT: 'PRODUCT_AUDIT',
  
  // 拍卖管理
  AUCTION_MANAGE: 'AUCTION_MANAGE',
  AUCTION_CONTROL: 'AUCTION_CONTROL',
  
  // 财务管理
  FINANCE_VIEW: 'finance:view',
  FINANCE_AUDIT: 'finance:audit',
  
  // 报表统计
  REPORT_SALES: 'report:sales',
  REPORT_EXPORT: 'report:export'
};
```

### 角色代码
```typescript
export const ROLES = {
  ADMIN: 'ADMIN',
  AUCTIONEER: 'AUCTIONEER',
  BUYER: 'BUYER',
  QUALITY_INSPECTOR: 'QUALITY_INSPECTOR',
  DISPLAY_OPERATOR: 'DISPLAY_OPERATOR',
  FINANCE: 'FINANCE',
  OPERATOR: 'OPERATOR'
};
```

## 权限同步机制

### 1. 登录时获取权限
```typescript
// 登录成功后获取用户权限
const response = await authService.login(credentials);
if (response.success) {
  // 获取用户权限
  const permissions = await permissionService.getUserPermissions(user.id);
  // 存储到状态管理中
  dispatch(setUserPermissions(permissions));
}
```

### 2. 权限缓存策略
- 权限信息在登录时获取并缓存
- 权限变更时需要重新登录或刷新权限
- 使用localStorage进行本地缓存

### 3. 权限验证流程
1. 前端检查本地权限缓存
2. 如果权限不足，显示相应提示
3. 关键操作时向后端验证权限
4. 后端返回权限验证结果

## 最佳实践

### 1. 权限检查原则
- 前端权限检查主要用于UI展示控制
- 后端必须进行权限验证，确保安全性
- 敏感操作需要双重验证

### 2. 错误处理
```typescript
// 权限不足时的处理
const handleUnauthorized = () => {
  message.error('权限不足，请联系管理员');
  // 可选：跳转到权限申请页面
};

<PermissionGuard 
  permission="USER_DELETE" 
  onUnauthorized={handleUnauthorized}
>
  <DeleteButton />
</PermissionGuard>
```

### 3. 动态权限更新
```typescript
// 权限变更后刷新
const refreshPermissions = async () => {
  const permissions = await permissionService.getUserPermissions(user.id);
  dispatch(setUserPermissions(permissions));
};
```

## 调试和测试

### 1. 权限调试
```typescript
// 开发环境下显示权限信息
if (process.env.NODE_ENV === 'development') {
  console.log('User permissions:', userPermissions);
  console.log('User roles:', getUserRoles());
}
```

### 2. 权限测试
- 使用不同角色的测试账号验证权限
- 确保权限边界正确
- 测试权限变更后的同步

## 注意事项

1. **安全性**: 前端权限检查不能替代后端验证
2. **一致性**: 前后端权限逻辑必须保持一致
3. **性能**: 合理缓存权限信息，避免频繁请求
4. **用户体验**: 权限不足时提供友好的提示信息
5. **维护性**: 权限常量统一管理，便于维护
