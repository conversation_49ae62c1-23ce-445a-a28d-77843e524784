# 花卉拍卖系统 - 环境变量配置指南

## 📋 概述

本文档详细说明了花卉拍卖系统中所有前端项目的环境变量配置方法，包括统一配置管理、检查验证和最佳实践。

## 🎯 设计目标

- **统一管理**: 所有前端项目使用统一的环境变量配置方式
- **环境隔离**: 支持开发、测试、生产环境的配置分离
- **易于部署**: 通过修改环境变量快速适配不同部署环境
- **避免硬编码**: 消除代码中的硬编码域名和端口

## 🏗️ 项目结构

```
flower-auction-system/
├── flower-auction-admin/          # 管理端 (React)
│   ├── .env                      # 开发环境配置
│   └── .env.production           # 生产环境配置
├── flower-auction-auctioneer/     # 拍卖师端 (Vue + Vite)
│   ├── .env                      # 开发环境配置
│   └── .env.production           # 生产环境配置
├── flower-auction-buyer/          # 购买商端 (Vue + Vite)
│   ├── .env                      # 开发环境配置
│   └── .env.production           # 生产环境配置
├── flower-auction-display/        # 投屏端 (Vue + Vite)
│   ├── .env                      # 开发环境配置
│   └── .env.production           # 生产环境配置
└── scripts/
    ├── update-env.sh             # 统一配置脚本
    └── check-env.sh              # 配置检查脚本
```

## 🔧 环境变量说明

### 管理端 (React)

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `REACT_APP_API_BASE_URL` | API服务地址 | `http://localhost:8081/api/v1` |
| `REACT_APP_WS_URL` | WebSocket地址 | `ws://localhost:8081/ws` |
| `REACT_APP_TITLE` | 应用标题 | `花卉拍卖系统 - 管理端` |
| `REACT_APP_VERSION` | 应用版本 | `1.0.0` |
| `REACT_APP_ENV` | 环境标识 | `development` / `production` |
| `REACT_APP_DEBUG` | 调试模式 | `true` / `false` |
| `REACT_APP_REQUEST_TIMEOUT` | 请求超时时间(ms) | `10000` |

### 拍卖师端/购买商端/投屏端 (Vue + Vite)

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `VITE_API_BASE_URL` | API服务地址 | `http://localhost:8081/api/v1` |
| `VITE_WS_URL` | WebSocket地址 | `ws://localhost:8081/ws` |
| `VITE_APP_TITLE` | 应用标题 | `花卉拍卖系统 - 拍卖师端` |
| `VITE_APP_VERSION` | 应用版本 | `1.0.0` |
| `VITE_APP_ENV` | 环境标识 | `development` / `production` |
| `VITE_DEBUG` | 调试模式 | `true` / `false` |
| `VITE_REQUEST_TIMEOUT` | 请求超时时间(ms) | `10000` |

## 🚀 快速开始

### 1. 统一配置所有项目

```bash
# 交互式配置所有前端项目的环境变量
bash scripts/update-env.sh

# 按提示输入配置信息：
# - 域名 (默认: localhost)
# - API服务端口 (默认: 8080)
# - 各前端服务端口
# - 环境类型 (开发/生产)
```

### 2. 检查配置状态

```bash
# 检查所有项目的环境变量配置
bash scripts/check-env.sh

# 查看配置摘要表格
bash scripts/check-env.sh summary

# 检查是否有硬编码的localhost
bash scripts/check-env.sh hardcode

# 执行所有检查
bash scripts/check-env.sh all
```

## 📝 配置示例

### 开发环境配置

```bash
# 管理端 (.env)
REACT_APP_API_BASE_URL=http://localhost:8081/api/v1
REACT_APP_WS_URL=ws://localhost:8081/ws
REACT_APP_TITLE=花卉拍卖系统 - 管理端
REACT_APP_VERSION=1.0.0
REACT_APP_ENV=development
REACT_APP_DEBUG=true
REACT_APP_REQUEST_TIMEOUT=10000

# 拍卖师端 (.env)
VITE_API_BASE_URL=http://localhost:8081/api/v1
VITE_WS_URL=ws://localhost:8081/ws
VITE_APP_TITLE=花卉拍卖系统 - 拍卖师端
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development
VITE_DEBUG=true
VITE_REQUEST_TIMEOUT=10000
```

### 生产环境配置

```bash
# 管理端 (.env.production)
REACT_APP_API_BASE_URL=https://api.flower-auction.com/api/v1
REACT_APP_WS_URL=wss://api.flower-auction.com/ws
REACT_APP_TITLE=花卉拍卖系统 - 管理端
REACT_APP_VERSION=1.0.0
REACT_APP_ENV=production
REACT_APP_DEBUG=false
REACT_APP_REQUEST_TIMEOUT=30000

# 拍卖师端 (.env.production)
VITE_API_BASE_URL=https://api.flower-auction.com/api/v1
VITE_WS_URL=wss://api.flower-auction.com/ws
VITE_APP_TITLE=花卉拍卖系统 - 拍卖师端
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production
VITE_DEBUG=false
VITE_REQUEST_TIMEOUT=30000
```

## 🔍 代码中的使用方式

### React项目中使用

```typescript
// API客户端配置
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1',
  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),
});

// WebSocket连接
const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8081/ws';
const socket = new WebSocket(wsUrl);
```

### Vue + Vite项目中使用

```typescript
// API客户端配置
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081/api/v1',
  timeout: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT || '10000'),
});

// WebSocket连接
const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8081/ws';
const socket = new WebSocket(wsUrl);
```

## 🛠️ 部署配置

### 开发环境部署

1. 确保所有`.env`文件使用localhost配置
2. 运行配置检查：`bash scripts/check-env.sh`
3. 启动所有服务：`bash start-all.sh`

### 生产环境部署

1. 修改所有`.env.production`文件中的域名
2. 或使用配置脚本：`bash scripts/update-env.sh`
3. 构建生产版本：
   ```bash
   cd flower-auction-admin && npm run build
   cd flower-auction-auctioneer && npm run build
   cd flower-auction-buyer && npm run build
   cd flower-auction-display && npm run build
   ```

### Docker部署

```dockerfile
# 在Dockerfile中设置环境变量
ENV REACT_APP_API_BASE_URL=https://api.flower-auction.com/api/v1
ENV REACT_APP_WS_URL=wss://api.flower-auction.com/ws
```

## 🔧 故障排除

### 常见问题

1. **环境变量不生效**
   - 确保变量名前缀正确（React: `REACT_APP_`, Vite: `VITE_`）
   - 重启开发服务器
   - 检查`.env`文件格式（无空格、无引号）

2. **WebSocket连接失败**
   - 检查协议一致性（HTTP配HTTPS会有问题）
   - 确认WebSocket地址正确
   - 检查防火墙和代理设置

3. **API请求失败**
   - 验证API地址是否可访问
   - 检查CORS配置
   - 确认端口号正确

### 调试命令

```bash
# 检查环境变量是否正确加载
bash scripts/check-env.sh summary

# 查找硬编码的localhost
bash scripts/check-env.sh hardcode

# 验证配置文件格式
cat flower-auction-admin/.env | grep -v "^#" | grep "="
```

## 📚 最佳实践

1. **使用配置脚本**: 优先使用`update-env.sh`统一配置
2. **定期检查**: 使用`check-env.sh`定期验证配置
3. **避免硬编码**: 所有URL都通过环境变量配置
4. **环境隔离**: 开发和生产环境使用不同的配置文件
5. **版本控制**: `.env.example`文件加入版本控制，实际`.env`文件不加入
6. **安全考虑**: 生产环境的敏感配置通过CI/CD或容器环境变量注入

## 🔄 配置更新流程

1. **修改配置**: 运行`bash scripts/update-env.sh`
2. **验证配置**: 运行`bash scripts/check-env.sh`
3. **重启服务**: 重启相关前端服务
4. **测试功能**: 验证API和WebSocket连接正常

## 📞 技术支持

如果遇到配置问题：

1. 查看本文档的故障排除部分
2. 运行配置检查脚本获取详细信息
3. 检查浏览器控制台的错误信息
4. 联系开发团队获取支持
