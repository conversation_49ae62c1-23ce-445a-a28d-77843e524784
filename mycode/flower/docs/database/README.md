# 昆明花卉拍卖系统数据库说明文档

## 目录结构

```
docs/database/
├── design.md           # 数据库设计文档
├── init.sql           # 数据库初始化脚本
├── flyway.conf       # Flyway配置文件
├── migration/        # 数据库迁移脚本目录
│   ├── V1__init_schema.sql    # 初始化数据库架构
│   └── V2__init_data.sql      # 初始化基础数据
└── README.md         # 本说明文档
```

## 数据库架构

系统采用分库设计，共分为四个数据库：

1. user_db：用户相关数据
2. product_db：商品相关数据
3. auction_db：拍卖相关数据
4. order_db：订单相关数据

## 初始化步骤

1. 确保已安装 MySQL 8.0 或以上版本
2. 安装 Flyway 命令行工具
3. 修改 flyway.conf 中的数据库连接信息
4. 执行数据库迁移

### 执行迁移

```bash
# 初始化用户数据库
flyway -configFiles=flyway.conf -schemas=user_db migrate

# 初始化商品数据库
flyway -configFiles=flyway.conf -schemas=product_db migrate

# 初始化拍卖数据库
flyway -configFiles=flyway.conf -schemas=auction_db migrate

# 初始化订单数据库
flyway -configFiles=flyway.conf -schemas=order_db migrate
```

## 分库分表说明

### 分库策略

- user_db：用户相关数据
  * 用户表
  * 角色表
  * 用户角色关联表

- product_db：商品相关数据
  * 商品表
  * 商品类别表

- auction_db：拍卖相关数据
  * 拍卖会表
  * 拍卖商品表
  * 竞价记录表（按月分表）

- order_db：订单相关数据
  * 订单表（按用户ID范围分表）
  * 支付记录表（按月分表）

### 分表说明

1. 竞价记录表（bid）
   - 按月分表
   - 表名格式：bid_YYYYMM
   - 例如：bid_202401, bid_202402

2. 订单表（order）
   - 按用户ID范围分表
   - 表名格式：order_N（N为0-9）
   - 分表规则：user_id % 10

3. 支付记录表（payment）
   - 按月分表
   - 表名格式：payment_YYYYMM
   - 例如：payment_202401, payment_202402

## 数据库维护

### 添加新的迁移脚本

1. 在 migration 目录下创建新的迁移脚本
2. 文件命名规则：V{version}__{description}.sql
3. version 必须大于已有版本号
4. 执行 flyway migrate 命令应用新的迁移

### 回滚操作

如需回滚，请使用以下命令：

```bash
# 回滚到指定版本
flyway -configFiles=flyway.conf -schemas=user_db undo -target=1
```

### 查看迁移历史

```bash
# 查看迁移历史
flyway -configFiles=flyway.conf -schemas=user_db info
```

## 注意事项

1. 执行迁移前请确保已备份数据库
2. 生产环境中请修改默认密码
3. 定期维护分表，清理历史数据
4. 监控数据库性能，适时优化索引
5. 保持数据库文档的及时更新 