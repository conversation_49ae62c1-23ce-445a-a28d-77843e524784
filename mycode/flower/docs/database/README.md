# 数据库文档

## 概述
花卉拍卖系统数据库设计文档，包含完整的表结构定义和初始化数据。

## 📁 文件说明

### 核心文件
- **`schema.sql`** - 完整的数据库表结构定义
- **`init_data.sql`** - 初始化数据脚本（包含角色、权限、用户等）

### 文档文件
- `design.md` - 数据库设计文档
- `category_codes_summary.md` - 分类编码说明
- `product_audit_feature.md` - 商品审核功能说明
- `flyway.conf` - Flyway数据库迁移配置（备用）

## 🗄️ 数据库结构

### 主要数据库
1. **flower_auction** - 主业务数据库
   - 用户表 (user)
   - 商品表 (product) 
   - 分类表 (category)
   - 拍卖表 (auction)
   - 拍卖商品表 (auction_item)
   - 竞价记录表 (bid)
   - 关注列表表 (watch_list)
   - 埋单表 (pre_order)
   - 钟号状态表 (clock_status)
   - 拍卖日志表 (auction_log)

2. **user_db** - 用户权限数据库
   - 角色表 (role) - 7个角色
   - 权限表 (permission) - 82个权限
   - 用户角色关联表 (user_role)
   - 角色权限关联表 (role_permission)

3. **order_db** - 订单数据库
   - 订单分表 (order_0 ~ order_9) - 10个分表
   - 财务账户表 (finance_accounts)
   - 财务交易表 (finance_transactions)
   - 财务佣金表 (finance_commissions)

## 🚀 使用方法

### 快速初始化（推荐）
```bash
# 进入项目根目录
cd flower

# 一键初始化数据库结构和数据
mysql -u root -p < docs/database/schema.sql
mysql -u root -p < docs/database/init_data.sql
```

### 分步初始化
```bash
# 1. 仅创建数据库结构
mysql -u root -p < docs/database/schema.sql

# 2. 仅导入初始数据
mysql -u root -p < docs/database/init_data.sql
```

### 使用权限配置脚本
```bash
# 使用自动化脚本初始化权限
cd flower-auction
bash scripts/setup-permissions.sh all
```

## 📊 初始化数据说明

### 角色数据（7个角色）
- **系统管理员** (ADMIN) - 所有权限
- **拍卖师** (AUCTIONEER) - 拍卖控制权限
- **买家** (BUYER) - 竞拍权限
- **质检员** (QUALITY_INSPECTOR) - 商品审核权限
- **显示屏操作员** (DISPLAY_OPERATOR) - 数据展示权限
- **财务官** (FINANCE) - 财务管理权限
- **运营** (OPERATOR) - 所有权限

### 权限数据（82个权限）
- 用户管理模块 (11个权限)
- 角色管理模块 (11个权限)
- 商品管理模块 (11个权限)
- 分类管理模块 (9个权限)
- 拍卖管理模块 (17个权限)
- 订单管理模块 (5个权限)
- 财务管理模块 (5个权限)
- 报表统计模块 (5个权限)
- 系统管理模块 (4个权限)
- 权限管理模块 (4个权限)

### 测试用户数据
- **admin** (密码: admin123) - 系统管理员
- **auctioneer1** (密码: admin123) - 拍卖师
- **buyer1-4** (密码: admin123) - 买家用户

### 基础数据
- 商品分类（鲜切花、盆栽花卉等）
- 钟号状态（1-12号钟）
- 财务账户（用户主账户和保证金账户）

## ⚠️ 注意事项

### 环境要求
- MySQL版本 >= 5.7
- 字符集: utf8mb4
- 排序规则: utf8mb4_unicode_ci
- 时区: Asia/Shanghai

### 安全建议
1. 生产环境请修改默认密码
2. 建议开启binlog用于数据恢复
3. 定期备份数据库
4. 限制数据库访问权限

### 性能优化
1. 订单表采用分表设计（10个分表）
2. 关键字段已添加索引
3. 建议根据业务量调整分表策略

## 🔧 维护命令

### 检查数据库状态
```bash
# 检查权限配置
mysql -u root -p -e "USE user_db; SELECT COUNT(*) as permission_count FROM permission WHERE status = 1;"

# 检查角色权限分配
mysql -u root -p -e "USE user_db; SELECT r.name, COUNT(rp.permission_id) as permission_count FROM role r LEFT JOIN role_permission rp ON r.id = rp.role_id GROUP BY r.id, r.name;"
```

### 重置权限数据
```bash
# 重新导入权限数据
mysql -u root -p -e "USE user_db; DELETE FROM role_permission; DELETE FROM permission;"
mysql -u root -p < docs/database/init_data.sql
```
