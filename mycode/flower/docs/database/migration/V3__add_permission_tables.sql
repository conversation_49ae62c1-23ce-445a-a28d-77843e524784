-- V3__add_permission_tables.sql
-- 添加权限相关表

USE `user_db`;

-- 创建权限表
CREATE TABLE IF NOT EXISTS `permission` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `name` varchar(100) NOT NULL COMMENT '权限名称',
    `code` varchar(100) NOT NULL COMMENT '权限编码',
    `description` varchar(200) DEFAULT NULL COMMENT '权限描述',
    `module` varchar(50) NOT NULL COMMENT '模块名称',
    `action` varchar(50) NOT NULL COMMENT '操作类型',
    `resource` varchar(100) NOT NULL COMMENT '资源名称',
    `parent_id` bigint(20) DEFAULT NULL COMMENT '父权限ID',
    `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '权限类型：1-菜单 2-按钮 3-接口',
    `path` varchar(200) DEFAULT NULL COMMENT '路径',
    `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_module` (`module`),
    KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 创建角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permission` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`,`permission_id`),
    KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 插入基础权限数据
INSERT INTO `permission` (`name`, `code`, `description`, `module`, `action`, `resource`, `type`, `path`, `sort_order`) VALUES
-- 用户管理权限
('用户管理', 'USER_MANAGE', '用户管理模块', 'user', 'manage', 'user', 1, '/users', 1),
('用户列表', 'USER_LIST', '查看用户列表', 'user', 'list', 'user', 2, '/users/list', 11),
('用户创建', 'USER_CREATE', '创建用户', 'user', 'create', 'user', 2, '/users/create', 12),
('用户编辑', 'USER_EDIT', '编辑用户', 'user', 'edit', 'user', 2, '/users/edit', 13),
('用户删除', 'USER_DELETE', '删除用户', 'user', 'delete', 'user', 2, '/users/delete', 14),
('用户导出', 'USER_EXPORT', '导出用户数据', 'user', 'export', 'user', 2, '/users/export', 15),

-- 角色管理权限
('角色管理', 'ROLE_MANAGE', '角色管理模块', 'role', 'manage', 'role', 1, '/roles', 2),
('角色列表', 'ROLE_LIST', '查看角色列表', 'role', 'list', 'role', 2, '/roles/list', 21),
('角色创建', 'ROLE_CREATE', '创建角色', 'role', 'create', 'role', 2, '/roles/create', 22),
('角色编辑', 'ROLE_EDIT', '编辑角色', 'role', 'edit', 'role', 2, '/roles/edit', 23),
('角色删除', 'ROLE_DELETE', '删除角色', 'role', 'delete', 'role', 2, '/roles/delete', 24),
('权限分配', 'ROLE_PERMISSION', '分配角色权限', 'role', 'permission', 'role', 2, '/roles/permission', 25),

-- 商品管理权限
('商品管理', 'PRODUCT_MANAGE', '商品管理模块', 'product', 'manage', 'product', 1, '/products', 3),
('商品列表', 'PRODUCT_LIST', '查看商品列表', 'product', 'list', 'product', 2, '/products/list', 31),
('商品创建', 'PRODUCT_CREATE', '创建商品', 'product', 'create', 'product', 2, '/products/create', 32),
('商品编辑', 'PRODUCT_EDIT', '编辑商品', 'product', 'edit', 'product', 2, '/products/edit', 33),
('商品删除', 'PRODUCT_DELETE', '删除商品', 'product', 'delete', 'product', 2, '/products/delete', 34),
('商品审核', 'PRODUCT_AUDIT', '审核商品', 'product', 'audit', 'product', 2, '/products/audit', 35),

-- 分类管理权限
('分类管理', 'CATEGORY_MANAGE', '分类管理模块', 'category', 'manage', 'category', 1, '/categories', 4),
('分类列表', 'CATEGORY_LIST', '查看分类列表', 'category', 'list', 'category', 2, '/categories/list', 41),
('分类创建', 'CATEGORY_CREATE', '创建分类', 'category', 'create', 'category', 2, '/categories/create', 42),
('分类编辑', 'CATEGORY_EDIT', '编辑分类', 'category', 'edit', 'category', 2, '/categories/edit', 43),
('分类删除', 'CATEGORY_DELETE', '删除分类', 'category', 'delete', 'category', 2, '/categories/delete', 44),

-- 拍卖管理权限
('拍卖管理', 'AUCTION_MANAGE', '拍卖管理模块', 'auction', 'manage', 'auction', 1, '/auctions', 5),
('拍卖列表', 'AUCTION_LIST', '查看拍卖列表', 'auction', 'list', 'auction', 2, '/auctions/list', 51),
('拍卖创建', 'AUCTION_CREATE', '创建拍卖', 'auction', 'create', 'auction', 2, '/auctions/create', 52),
('拍卖编辑', 'AUCTION_EDIT', '编辑拍卖', 'auction', 'edit', 'auction', 2, '/auctions/edit', 53),
('拍卖删除', 'AUCTION_DELETE', '删除拍卖', 'auction', 'delete', 'auction', 2, '/auctions/delete', 54),
('拍卖控制', 'AUCTION_CONTROL', '控制拍卖状态', 'auction', 'control', 'auction', 2, '/auctions/control', 55);

-- 设置权限父子关系
UPDATE `permission` SET `parent_id` = (SELECT id FROM (SELECT id FROM `permission` WHERE code = 'USER_MANAGE') AS p) WHERE code IN ('USER_LIST', 'USER_CREATE', 'USER_EDIT', 'USER_DELETE', 'USER_EXPORT');
UPDATE `permission` SET `parent_id` = (SELECT id FROM (SELECT id FROM `permission` WHERE code = 'ROLE_MANAGE') AS p) WHERE code IN ('ROLE_LIST', 'ROLE_CREATE', 'ROLE_EDIT', 'ROLE_DELETE', 'ROLE_PERMISSION');
UPDATE `permission` SET `parent_id` = (SELECT id FROM (SELECT id FROM `permission` WHERE code = 'PRODUCT_MANAGE') AS p) WHERE code IN ('PRODUCT_LIST', 'PRODUCT_CREATE', 'PRODUCT_EDIT', 'PRODUCT_DELETE', 'PRODUCT_AUDIT');
UPDATE `permission` SET `parent_id` = (SELECT id FROM (SELECT id FROM `permission` WHERE code = 'CATEGORY_MANAGE') AS p) WHERE code IN ('CATEGORY_LIST', 'CATEGORY_CREATE', 'CATEGORY_EDIT', 'CATEGORY_DELETE');
UPDATE `permission` SET `parent_id` = (SELECT id FROM (SELECT id FROM `permission` WHERE code = 'AUCTION_MANAGE') AS p) WHERE code IN ('AUCTION_LIST', 'AUCTION_CREATE', 'AUCTION_EDIT', 'AUCTION_DELETE', 'AUCTION_CONTROL');

-- 为管理员角色分配所有权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM `role` r, `permission` p 
WHERE r.code = 'ADMIN';

-- 为拍卖师角色分配相关权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM `role` r, `permission` p 
WHERE r.code = 'AUCTIONEER' 
AND p.code IN ('PRODUCT_LIST', 'AUCTION_MANAGE', 'AUCTION_LIST', 'AUCTION_CREATE', 'AUCTION_EDIT', 'AUCTION_CONTROL');

-- 为买家角色分配基础权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM `role` r, `permission` p 
WHERE r.code = 'BUYER' 
AND p.code IN ('PRODUCT_LIST', 'AUCTION_LIST');

-- 为质检员角色分配相关权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM `role` r, `permission` p 
WHERE r.code = 'QUALITY_INSPECTOR' 
AND p.code IN ('PRODUCT_LIST', 'PRODUCT_AUDIT');
