# 数据库迁移文件说明

## 迁移文件执行顺序

按照以下顺序执行迁移文件：

### 1. V1__init_schema.sql
- 创建基础数据库结构
- 包含用户、角色、商品、分类、拍卖等核心表

### 2. V2__init_data.sql
- 插入基础数据
- 包含角色数据、测试用户、基础商品分类
- **注意**：此版本的分类数据不包含 `code` 字段

### 3. V3__add_permission_tables.sql
- 添加权限管理相关表
- 包含权限表、角色权限关联表
- 插入基础权限数据

### 4. V4__add_category_code.sql
- 为商品分类表添加 `code` 字段
- 为现有分类数据添加编码
- 添加唯一索引

### 5. V5__category_data_with_codes.sql
- 插入完整的商品分类数据（包含编码）
- 包含更丰富的分类层级结构
- 支持前端下拉选择框功能

## 分类编码说明

### 一级分类编码
- `FLOWER` - 鲜花
- `GREEN_PLANT` - 绿植
- `DRIED_FLOWER` - 干花
- `ACCESSORIES` - 花束配件

### 二级分类编码示例
- `ROSE` - 玫瑰
- `LILY` - 百合
- `CARNATION` - 康乃馨
- `POTTED` - 盆栽
- `FOLIAGE` - 观叶植物

### 三级分类编码示例
- `RED_ROSE` - 红玫瑰
- `WHITE_ROSE` - 白玫瑰
- `PINK_ROSE` - 粉玫瑰

## 使用方法

### 全新安装
```bash
# 按顺序执行所有迁移文件
mysql -u root -p < V1__init_schema.sql
mysql -u root -p < V2__init_data.sql
mysql -u root -p < V3__add_permission_tables.sql
mysql -u root -p < V4__add_category_code.sql
mysql -u root -p < V5__category_data_with_codes.sql
```

### 已有数据库升级
如果已经执行了 V1-V3，只需要执行：
```bash
mysql -u root -p < V4__add_category_code.sql
mysql -u root -p < V5__category_data_with_codes.sql
```

### 仅添加分类编码功能
如果只需要为现有分类添加编码功能：
```bash
mysql -u root -p < V4__add_category_code.sql
```

## 注意事项

1. **数据备份**：执行迁移前请备份数据库
2. **执行顺序**：严格按照版本号顺序执行
3. **重复执行**：V4 和 V5 可以安全地重复执行
4. **数据冲突**：V5 会插入新的分类数据，如果有重复可能会报错

## 前端集成

执行 V4 和 V5 迁移后，前端可以：
- 使用分类编码作为下拉选择框的值
- 通过编码进行分类筛选和搜索
- 支持多级分类展示

## 相关文件

- `V4__add_category_code.sql` - 添加分类编码字段
- `V5__category_data_with_codes.sql` - 完整分类数据（含编码）
- `V2__init_data.sql` - 基础分类数据（不含编码）
