-- V1__init_schema.sql
-- 初始化数据库架构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `user_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE IF NOT EXISTS `product_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE IF NOT EXISTS `auction_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE IF NOT EXISTS `order_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 用户数据库表
USE `user_db`;

CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `user_type` tinyint NOT NULL COMMENT '用户类型：1-拍卖师 2-买家 3-管理员 4-质检员',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
  `created_at` int NOT NULL COMMENT '创建日期(格式:20060102)',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `phone` (`phone`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_created_at` (`created_at`) COMMENT '创建日期索引，方便按日期查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';

CREATE TABLE IF NOT EXISTS `role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `name` varchar(50) NOT NULL COMMENT '角色名称',
    `code` varchar(50) NOT NULL COMMENT '角色编码',
    `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

CREATE TABLE IF NOT EXISTS `user_role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`,`role_id`),
    KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 商品数据库表
USE `product_db`;

CREATE TABLE IF NOT EXISTS `product` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
    `name` varchar(100) NOT NULL COMMENT '商品名称',
    `category_id` bigint(20) NOT NULL COMMENT '类别ID',
    `description` text COMMENT '商品描述',
    `quality_level` tinyint(4) NOT NULL COMMENT '品质等级：1-优 2-良 3-中',
    `origin` varchar(100) NOT NULL COMMENT '产地',
    `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-下架 1-上架',
    `audit_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核 approved-已通过 rejected-已拒绝',
    `audit_reason` text COMMENT '审核意见',
    `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
    `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核员ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category_id`),
    KEY `idx_supplier` (`supplier_id`),
    KEY `idx_audit_status` (`audit_status`),
    KEY `idx_audit_time` (`audit_time`),
    KEY `idx_auditor` (`auditor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 创建商品审核历史表（用于记录审核历史）
CREATE TABLE IF NOT EXISTS `product_audit_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '审核历史ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `audit_status` varchar(20) NOT NULL COMMENT '审核状态：pending-待审核 approved-已通过 rejected-已拒绝',
    `audit_reason` text COMMENT '审核意见',
    `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核员ID',
    `audit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_product` (`product_id`),
    KEY `idx_auditor` (`auditor_id`),
    KEY `idx_audit_time` (`audit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品审核历史表';

CREATE TABLE IF NOT EXISTS `category` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类别ID',
    `name` varchar(50) NOT NULL COMMENT '类别名称',
    `parent_id` bigint(20) DEFAULT NULL COMMENT '父类别ID',
    `level` tinyint(4) NOT NULL COMMENT '层级',
    `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品类别表';

-- 拍卖数据库表
USE `auction_db`;

CREATE TABLE IF NOT EXISTS `auction` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '拍卖会ID',
    `name` varchar(100) NOT NULL COMMENT '拍卖会名称',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime NOT NULL COMMENT '结束时间',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始 1-进行中 2-已结束',
    `auctioneer_id` bigint(20) NOT NULL COMMENT '拍卖师ID',
    `description` text COMMENT '拍卖会描述',
    `location` varchar(100) NOT NULL DEFAULT '' COMMENT '拍卖会地点',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_auctioneer` (`auctioneer_id`),
    KEY `idx_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拍卖会表';

CREATE TABLE IF NOT EXISTS `auction_item` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '拍卖商品ID',
    `auction_id` bigint(20) NOT NULL COMMENT '拍卖会ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `start_price` decimal(10,2) NOT NULL COMMENT '起拍价',
    `current_price` decimal(10,2) NOT NULL COMMENT '当前价格',
    `step_price` decimal(10,2) NOT NULL COMMENT '加价幅度',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始 1-进行中 2-已成交 3-流拍',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `winner_id` bigint(20) DEFAULT NULL COMMENT '中标用户ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_auction` (`auction_id`),
    KEY `idx_product` (`product_id`),
    KEY `idx_winner` (`winner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拍卖商品表';

-- 订单数据库表
USE `order_db`;

CREATE TABLE IF NOT EXISTS `order_0` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    `order_no` varchar(32) NOT NULL COMMENT '订单编号',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `auction_item_id` bigint(20) NOT NULL COMMENT '拍卖商品ID',
    `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
    `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user` (`user_id`),
    KEY `idx_auction_item` (`auction_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表0';