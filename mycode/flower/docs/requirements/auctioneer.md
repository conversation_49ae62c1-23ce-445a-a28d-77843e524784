# 昆明花卉拍卖系统 - 拍卖师端需求文档

## 1. 功能概述

拍卖师端是花卉拍卖系统的核心操作界面，主要供拍卖师使用，用于管理和控制拍卖流程，确保拍卖活动的顺利进行。

## 2. 用户角色

- 主拍卖师：负责主要拍卖活动的控制和管理
- 助理拍卖师：协助主拍卖师工作，可执行部分拍卖操作
- 实习拍卖师：仅可查看和学习，无实际操作权限

## 3. 功能需求

### 3.1 注册登录模块

#### 3.1.1 账号验证
- 支持工号+密码登录
- 对接HR系统校验身份有效性
- 支持指纹识别登录
- 异地登录需二次验证

#### 3.1.2 多端同步
- 支持PC端与移动端登录
- 自动同步拍卖员角色权限
- 多设备登录状态提醒

#### 3.1.3 安全审计
- 登录记录实时写入日志系统
- 异常登录触发短信预警
- 记录登录IP和设备信息

#### 3.1.4 权限初始化
- 首次登录自动绑定负责的钟号范围
- 可申请调整权限范围
- 权限变更需要审批流程

### 3.2 批次管理模块

#### 3.2.1 批次创建
- 手动创建供货批次信息
- 支持Excel批量导入
- 生成唯一拍卖批次号
- 关联仓储库位信息

#### 3.2.2 状态跟踪
- 实时显示批次状态（待起拍/竞拍中/已成交/流拍）
- 支持批量修改批次属性
- 状态变更自动记录日志

#### 3.2.3 信息管理
- 查看批次对应的供货商信息
- 查看质检报告详情
- 查看购买商关注数据
- 标注重点批次功能

#### 3.2.4 归档管理
- 自动归档已结束批次
- 保留3年历史数据
- 支持按钟号、品类快速检索
- 导出归档数据报表

### 3.3 拍卖控制模块

#### 3.3.1 起拍功能
- 配置起拍价、加价幅度
- 设置转速、最小购买量
- 支持预设模板快速应用
- 系统自动验证参数合法性
- 提前10分钟预加载批次信息
- 支持多钟并行配置
- 起拍指令实时广播
- 异常自动重试机制

#### 3.3.2 流拍功能
- 手动触发流拍操作
- 选择流拍原因
- 自动触发流拍条件设置
- 流拍结果同步处理
- 流拍批次后续处理
- 防误触保护机制

#### 3.3.3 控价功能
- 手动降低当前价格
- 设定最低成交价
- 紧急停拍功能
- 调整钟号转速
- 价格变动日志记录

#### 3.3.4 成交功能
- 确认最高价购买商
- 处理埋单交易
- 生成分货单
- 成交结果广播
- 自动触发结算流程

### 3.4 数据查询模块

#### 3.4.1 实时数据
- 当前交易批次信息
- 实时竞价记录
- 在线购买商数量
- 系统性能指标

#### 3.4.2 历史数据
- 按日期查询交易记录
- 按钟号筛选历史数据
- 按品类统计交易量
- 导出数据报表

## 4. 非功能需求

### 4.1 性能需求
- 页面响应时间≤1秒
- 竞价操作延迟≤50ms
- 数据刷新频率≥10次/秒
- 支持100个并发用户

### 4.2 安全需求
- 操作日志完整记录
- 敏感操作二次确认
- 权限精细化控制
- 数据传输加密

### 4.3 可用性需求
- 系统可用性≥99.9%
- 故障自动切换机制
- 操作界面简洁直观
- 提供操作指引和帮助

### 4.4 兼容性需求
- 支持主流浏览器
- 支持Windows/Mac系统
- 支持移动端适配
- 支持4K高清显示

## 5. 界面要求

### 5.1 布局设计
- 多钟号并行显示
- 关键信息突出展示
- 操作按钮醒目位置
- 状态信息实时更新

### 5.2 交互设计
- 快捷键操作支持
- 拖拽调整布局
- 手势操作支持
- 声光提醒机制

## 6. 集成要求

### 6.1 系统集成
- 对接HR系统
- 对接库存系统
- 对接结算系统
- 对接日志系统

### 6.2 数据集成
- 实时数据同步
- 历史数据迁移
- 数据一致性保证
- 异常数据处理

## 7. 其他要求

### 7.1 培训需求
- 新手引导功能
- 操作手册文档
- 视频教程支持
- 在线帮助系统

### 7.2 运维需求
- 系统监控告警
- 性能分析工具
- 问题诊断功能
- 配置管理支持 