<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-03-21T08:00:00.000Z" agent="Mozilla/5.0" version="21.1.9" type="device">
  <diagram id="flower-auction-system" name="花卉拍卖系统架构图">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 客户端层 -->
        <mxCell id="2" value="客户端层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1080" height="160" as="geometry" />
        </mxCell>
        <mxCell id="3" value="拍卖师端&#xa;(Web/Desktop)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="40" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="4" value="买家端&#xa;(Web/Mobile)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="240" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="5" value="大屏显示端&#xa;(Web)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="440" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6" value="管理后台&#xa;(Web)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="640" y="40" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="7" value="品控端&#xa;(Mobile)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="840" y="40" width="160" height="80" as="geometry" />
        </mxCell>

        <!-- 接入层 -->
        <mxCell id="8" value="接入层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="240" width="1080" height="120" as="geometry" />
        </mxCell>
        <mxCell id="9" value="CDN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="8">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="10" value="SLB负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="8">
          <mxGeometry x="240" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="11" value="API网关" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="8">
          <mxGeometry x="440" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="12" value="WebSocket集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="8">
          <mxGeometry x="640" y="40" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 应用层 -->
        <mxCell id="13" value="应用层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="400" width="1080" height="160" as="geometry" />
        </mxCell>
        <mxCell id="14" value="拍卖服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="15" value="用户服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="240" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="16" value="订单服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="440" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="支付服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="640" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="18" value="商品服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="840" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="19" value="统计服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="40" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="20" value="消息服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="240" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="21" value="日志服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="440" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="22" value="监控服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="13">
          <mxGeometry x="640" y="100" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 中间件层 -->
        <mxCell id="23" value="中间件层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="600" width="1080" height="120" as="geometry" />
        </mxCell>
        <mxCell id="24" value="Redis集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="23">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="25" value="RocketMQ集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="23">
          <mxGeometry x="240" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="26" value="ElasticSearch集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="23">
          <mxGeometry x="440" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="27" value="Seata" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="23">
          <mxGeometry x="640" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="28" value="Sentinel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="23">
          <mxGeometry x="840" y="40" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 存储层 -->
        <mxCell id="29" value="存储层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="760" width="1080" height="120" as="geometry" />
        </mxCell>
        <mxCell id="30" value="MySQL主从集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="29">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="31" value="MinIO对象存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="29">
          <mxGeometry x="240" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="32" value="时序数据库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="29">
          <mxGeometry x="440" y="40" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 连接线 -->
        <!-- 客户端到接入层 -->
        <mxCell id="33" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="3" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="34" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="4" target="10">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="35" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="5" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="36" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="6" target="12">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>

        <!-- 接入层到应用层 -->
        <mxCell id="37" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="11" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="38" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="12" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>

        <!-- 应用层到中间件层 -->
        <mxCell id="39" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="14" target="24">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="40" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="15" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>

        <!-- 中间件层到存储层 -->
        <mxCell id="41" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="24" target="30">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="42" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="25" target="31">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 