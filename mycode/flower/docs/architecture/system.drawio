<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-03-21T12:00:00.000Z" agent="Mozilla/5.0" version="21.1.9" type="device">
  <diagram id="system-architecture" name="系统整体架构图">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 客户端层 -->
        <mxCell id="2" value="客户端层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1080" height="160" as="geometry" />
        </mxCell>
        
        <!-- 客户端应用 -->
        <mxCell id="3" value="拍卖师端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4" value="购买商端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="200" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5" value="投屏端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6" value="管理后台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="520" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7" value="质检端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="680" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 网关层 -->
        <mxCell id="8" value="网关层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="240" width="1080" height="120" as="geometry" />
        </mxCell>
        
        <!-- 网关服务 -->
        <mxCell id="9" value="API网关" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="8">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="10" value="WebSocket网关" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="8">
          <mxGeometry x="200" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="11" value="负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="8">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 业务服务层 -->
        <mxCell id="12" value="业务服务层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="400" width="1080" height="200" as="geometry" />
        </mxCell>
        
        <!-- 业务服务 -->
        <mxCell id="13" value="用户服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="12">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="14" value="拍卖服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="12">
          <mxGeometry x="200" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="15" value="订单服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="12">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="16" value="支付服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="12">
          <mxGeometry x="520" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="17" value="质检服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="12">
          <mxGeometry x="680" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="18" value="数据服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="12">
          <mxGeometry x="840" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 公共服务 -->
        <mxCell id="19" value="认证授权" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="12">
          <mxGeometry x="40" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="20" value="消息服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="12">
          <mxGeometry x="200" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="21" value="日志服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="12">
          <mxGeometry x="360" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="22" value="监控服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="12">
          <mxGeometry x="520" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 数据层 -->
        <mxCell id="23" value="数据层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="640" width="1080" height="160" as="geometry" />
        </mxCell>
        
        <!-- 数据存储 -->
        <mxCell id="24" value="MySQL集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="23">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="25" value="Redis集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="23">
          <mxGeometry x="200" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="26" value="MongoDB集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="23">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="27" value="消息队列" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="23">
          <mxGeometry x="520" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="28" value="对象存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="23">
          <mxGeometry x="680" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <!-- 客户端到网关 -->
        <mxCell id="29" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="3" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="30" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="4" target="10">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <!-- 网关到业务服务 -->
        <mxCell id="31" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="9" target="13">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="32" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="10" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <!-- 业务服务到数据层 -->
        <mxCell id="33" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="13" target="24">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="34" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="14" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 