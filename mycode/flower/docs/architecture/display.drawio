<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-03-21T11:00:00.000Z" agent="Mozilla/5.0" version="21.1.9" type="device">
  <diagram id="display-system" name="投屏端架构图">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 展示层 -->
        <mxCell id="2" value="展示层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1080" height="200" as="geometry" />
        </mxCell>
        
        <!-- 页面组件 -->
        <mxCell id="3" value="拍卖大屏" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4" value="价格显示" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="200" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5" value="商品信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6" value="买家信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="520" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7" value="交易动态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="680" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8" value="系统状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
          <mxGeometry x="840" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 公共组件 -->
        <mxCell id="9" value="布局组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="2">
          <mxGeometry x="40" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="10" value="动画组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="2">
          <mxGeometry x="200" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="11" value="图表组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="2">
          <mxGeometry x="360" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="12" value="状态组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="2">
          <mxGeometry x="520" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 业务逻辑层 -->
        <mxCell id="13" value="业务逻辑层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="280" width="1080" height="200" as="geometry" />
        </mxCell>
        
        <!-- 业务模块 -->
        <mxCell id="14" value="数据接收模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="15" value="数据处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
          <mxGeometry x="200" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="16" value="显示控制模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="17" value="动画控制模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
          <mxGeometry x="520" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="18" value="状态管理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
          <mxGeometry x="680" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19" value="错误处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
          <mxGeometry x="840" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 公共服务 -->
        <mxCell id="20" value="WebSocket服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="13">
          <mxGeometry x="40" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="21" value="状态管理服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="13">
          <mxGeometry x="200" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="22" value="缓存服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="13">
          <mxGeometry x="360" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="23" value="日志服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="13">
          <mxGeometry x="520" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 数据访问层 -->
        <mxCell id="24" value="数据访问层" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="520" width="1080" height="120" as="geometry" />
        </mxCell>
        
        <!-- API接口 -->
        <mxCell id="25" value="WebSocket API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="24">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="26" value="RESTful API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="24">
          <mxGeometry x="200" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="27" value="本地存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="24">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <!-- 展示层到业务逻辑层 -->
        <mxCell id="28" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="3" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="29" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="4" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="30" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="5" target="16">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        
        <!-- 业务逻辑层到数据访问层 -->
        <mxCell id="31" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="14" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="32" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="20" target="26">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="33" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="22" target="27">
          <mxGeometry width="50" height="50" relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 