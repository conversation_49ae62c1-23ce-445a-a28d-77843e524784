# 🌸 昆明花卉拍卖系统

一个基于现代技术栈的专业花卉拍卖平台，支持实时竞价、多钟号并行拍卖、大屏展示等完整功能。

## 🎯 系统概述

本系统是为昆明花卉拍卖市场设计的完整解决方案，包含拍卖师端、购买商端、投屏端三个前端应用和统一的后端服务，实现了专业的花卉拍卖业务流程。

### 🏗️ 系统架构

```mermaid
graph TB
    subgraph "前端应用层"
        A[拍卖师端<br/>🎯 蓝色主题<br/>• 钟号管理<br/>• 拍卖控制<br/>• 价格调整<br/>• 状态监控]
        B[购买商端<br/>🛍️ 粉色主题<br/>• 批次浏览<br/>• 竞价出价<br/>• 关注管理<br/>• 账户管理]
        C[投屏端<br/>📺 大屏展示<br/>• 实时监控<br/>• 数据统计<br/>• 状态展示<br/>• 趋势分析]
        D[管理端<br/>⚙️ 后台管理<br/>• 用户管理<br/>• 商品管理<br/>• 系统配置<br/>• 数据报表]
    end

    subgraph "网关层"
        E[API网关<br/>🚪 统一入口<br/>• 路由转发<br/>• 负载均衡<br/>• 限流熔断<br/>• 安全认证]
    end

    subgraph "微服务层"
        F[用户服务<br/>👤 User Service<br/>• 认证授权<br/>• 用户管理<br/>• 权限控制]
        G[拍卖服务<br/>🔨 Auction Service<br/>• 拍卖控制<br/>• 竞价处理<br/>• 钟号管理]
        H[商品服务<br/>📦 Product Service<br/>• 商品管理<br/>• 批次管理<br/>• 库存管理]
        I[支付服务<br/>💰 Payment Service<br/>• 资金管理<br/>• 交易结算<br/>• 账户余额]
        J[通知服务<br/>📢 Notification Service<br/>• 消息推送<br/>• 实时通知<br/>• 邮件短信]
    end

    subgraph "中间件层"
        K[消息队列<br/>📨 Message Queue<br/>• 异步处理<br/>• 削峰填谷<br/>• 解耦服务]
        L[缓存层<br/>⚡ Redis Cluster<br/>• 数据缓存<br/>• 会话存储<br/>• 分布式锁]
        M[搜索引擎<br/>🔍 Elasticsearch<br/>• 商品搜索<br/>• 日志分析<br/>• 数据统计]
    end

    subgraph "数据存储层"
        N[主数据库<br/>🗄️ MySQL Cluster<br/>• 业务数据<br/>• 事务处理<br/>• 读写分离]
        O[文件存储<br/>📁 OSS/MinIO<br/>• 图片存储<br/>• 文档管理<br/>• 静态资源]
        P[时序数据库<br/>📈 InfluxDB<br/>• 监控数据<br/>• 性能指标<br/>• 业务统计]
    end

    subgraph "基础设施层"
        Q[容器编排<br/>🐳 Kubernetes<br/>• 服务部署<br/>• 自动扩缩<br/>• 健康检查]
        R[服务网格<br/>🕸️ Istio<br/>• 流量管理<br/>• 安全策略<br/>• 可观测性]
        S[监控告警<br/>📊 Prometheus+Grafana<br/>• 系统监控<br/>• 业务监控<br/>• 告警通知]
    end

    %% 连接关系
    A --> E
    B --> E
    C --> E
    D --> E

    E --> F
    E --> G
    E --> H
    E --> I
    E --> J

    F --> K
    G --> K
    H --> K
    I --> K
    J --> K

    F --> L
    G --> L
    H --> L
    I --> L
    J --> L

    G --> M
    H --> M

    F --> N
    G --> N
    H --> N
    I --> N
    J --> N

    H --> O
    J --> O

    G --> P
    I --> P

    %% WebSocket 实时连接
    A -.->|WebSocket| G
    B -.->|WebSocket| G
    C -.->|WebSocket| G

    %% 样式定义
    classDef frontend fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gateway fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef microservice fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef middleware fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef infrastructure fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class A,B,C,D frontend
    class E gateway
    class F,G,H,I,J microservice
    class K,L,M middleware
    class N,O,P storage
    class Q,R,S infrastructure
```

## 🚀 技术栈

### 后端技术
- **Go 1.21+** - 高性能后端语言
- **Gin** - 轻量级Web框架
- **GORM** - 强大的ORM框架
- **MySQL 8.0+** - 主数据库
- **Redis 6.0+** - 缓存和会话存储
- **WebSocket** - 实时通信
- **JWT** - 身份认证

### 前端技术
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全
- **Ant Design** - 企业级UI组件库
- **Redux Toolkit** - 状态管理
- **Vite** - 快速构建工具
- **ECharts** - 数据可视化

## 📁 项目结构

```
km/
├── flower-auction/                 # 后端服务
│   ├── cmd/                       # 应用程序入口
│   ├── internal/                  # 内部包
│   │   ├── api/                  # API处理器
│   │   ├── model/                # 数据模型
│   │   ├── service/              # 业务逻辑
│   │   └── dao/                  # 数据访问层
│   ├── pkg/                      # 公共包
│   ├── configs/                  # 配置文件
│   ├── docs/                     # 文档
│   └── migrations/               # 数据库迁移
├── flower-auction-auctioneer/     # 拍卖师端
│   ├── src/                      # 源代码
│   │   ├── components/           # 组件
│   │   ├── pages/                # 页面
│   │   ├── store/                # 状态管理
│   │   └── services/             # API服务
│   └── public/                   # 静态资源
├── flower-auction-buyer/          # 购买商端
│   ├── src/                      # 源代码
│   │   ├── components/           # 组件
│   │   ├── pages/                # 页面
│   │   ├── store/                # 状态管理
│   │   └── services/             # API服务
│   └── public/                   # 静态资源
├── flower-auction-display/        # 投屏端
│   ├── src/                      # 源代码
│   │   ├── components/           # 组件
│   │   ├── store/                # 状态管理
│   │   └── services/             # API服务
│   └── public/                   # 静态资源
└── docs/                          # 项目文档
    ├── database/                 # 数据库文档
    ├── api/                      # API文档
    └── deployment/               # 部署文档
```

## ⚡ 快速开始

### 环境要求
- **Go 1.21+**
- **Node.js 18+**
- **MySQL 8.0+**
- **Redis 6.0+**

### 环境配置
系统使用环境变量进行配置管理，支持开发和生产环境：

#### 后端配置
- 配置文件：`flower-auction/config/config.yaml`
- 环境变量：`DB_HOST`, `DB_PASSWORD`, `JWT_SECRET`, `DOMAIN`

#### 前端配置
每个前端项目都有独立的环境配置文件：
- **开发环境**：`.env` (默认使用localhost:8080)
- **生产环境**：`.env.production` (使用真实域名)

主要环境变量：
- `VITE_API_BASE_URL` / `REACT_APP_API_BASE_URL`: API服务地址
- `VITE_WS_URL` / `REACT_APP_WS_URL`: WebSocket服务地址
- `PORT`: 前端服务端口号

#### 统一配置管理
```bash
# 统一配置所有前端项目的环境变量
bash scripts/update-env.sh

# 检查环境变量配置
bash scripts/check-env.sh

# 查看配置摘要
bash scripts/check-env.sh summary

# 检查硬编码的localhost
bash scripts/check-env.sh hardcode
```

### 1. 克隆项目
```bash
git clone <repository-url>
cd km
```

### 2. 后端服务启动

#### 微服务架构启动（推荐）
```bash
cd flower-auction

# 安装依赖
go mod download

# 配置数据库
./scripts/migrate.sh dev init

# 编译所有微服务
bash scripts/build-all.sh

# 启动所有微服务
bash scripts/start-microservices.sh

# 查看微服务状态
bash scripts/start-microservices.sh status

# 停止所有微服务
bash scripts/start-microservices.sh stop
```

**注意**: 请使用 `bash` 命令执行脚本，不要使用 `sh`，因为脚本使用了bash特性。

#### 单体服务启动
```bash
cd flower-auction

# 启动单体服务
./run.sh
# 或者直接运行: go run main.go
```

### 3. 前端应用启动

#### 管理端
```bash
cd flower-auction-admin
./run.sh
# 或者: npm install && npm start
# 访问: http://localhost:3000
```

#### 拍卖师端
```bash
cd flower-auction-auctioneer
./run.sh
# 或者: npm install && npm run dev
# 访问: http://localhost:3001
```

#### 购买商端
```bash
cd flower-auction-buyer
./run.sh
# 或者: npm install && npm run dev
# 访问: http://localhost:3002
```

#### 投屏端
```bash
cd flower-auction-display
./run.sh
# 或者: npm install && npm run dev
# 访问: http://localhost:3004
```

### 4. 一键启动脚本

#### 全局启动（推荐）
```bash
# 一键启动所有服务
./start-all.sh

# 查看服务状态
./start-all.sh status

# 停止所有服务
./start-all.sh stop

# 重启所有服务
./start-all.sh restart

# 查看服务日志
./start-all.sh logs
```

#### 单独启动
```bash
# 后端微服务
cd flower-auction && bash scripts/start-microservices.sh

# 前端应用（在不同终端窗口中运行）
cd flower-auction-admin && bash run.sh       # 管理端
cd flower-auction-auctioneer && bash run.sh  # 拍卖师端
cd flower-auction-buyer && bash run.sh       # 购买商端
cd flower-auction-display && bash run.sh     # 投屏端
```

## 🎨 系统特色

### 拍卖师端 (蓝色主题)
- 🎯 **钟号管理** - 12个钟号并行控制
- 🎮 **拍卖控制** - 开始/暂停/停止/调价
- 📊 **实时监控** - 竞价状态、用户活动
- 📈 **数据统计** - 成交统计、收益分析

### 购买商端 (粉色主题)
- 🛍️ **批次浏览** - 智能筛选、搜索功能
- 💰 **实时竞价** - 一键出价、自动出价
- ⭐ **关注管理** - 收藏商品、价格提醒
- 💳 **账户管理** - 余额查询、交易记录

### 投屏端 (大屏展示)
- 📺 **大屏适配** - 全屏模式、响应式布局
- 🔄 **实时同步** - 毫秒级数据更新
- 📊 **数据可视化** - 图表展示、趋势分析
- 🎨 **视觉效果** - 动画效果、状态指示

## 🔧 核心功能

### 拍卖功能
- ✅ 多钟号并行拍卖
- ✅ 实时竞价系统
- ✅ 价格调整机制
- ✅ 流拍处理
- ✅ 埋单功能

### 用户管理
- ✅ 角色权限控制
- ✅ 实名认证
- ✅ 企业认证
- ✅ 信用等级管理

### 资金管理
- ✅ 账户余额管理
- ✅ 资金冻结/解冻
- ✅ 交易记录
- ✅ 保证金管理

### 数据统计
- ✅ 实时交易统计
- ✅ 市场行情分析
- ✅ 用户行为分析
- ✅ 财务报表

## 📚 文档链接

- [后端API文档](./flower-auction/README.md)
- [拍卖师端文档](./flower-auction-auctioneer/README.md)
- [购买商端文档](./flower-auction-buyer/README.md)
- [投屏端文档](./flower-auction-display/README.md)
- [数据库设计](./docs/database/README.md)
- [部署指南](./docs/deployment/README.md)

## 🔗 访问地址

| 服务 | 开发环境 | 生产环境 |
|------|----------|----------|
| 后端API | http://localhost:8081 | https://api.flower-auction.com |
| 管理端 | http://localhost:3001 | https://admin.flower-auction.com |
| 拍卖师端 | http://localhost:3002 | https://auctioneer.flower-auction.com |
| 购买商端 | http://localhost:3003 | https://buyer.flower-auction.com |
| 投屏端 | http://localhost:3004 | https://display.flower-auction.com |

## 🔐 权限管理系统

### 权限初始化
系统提供完整的权限初始化脚本，包含82个权限项，覆盖所有功能模块：

```bash
# 初始化权限数据
mysql -u root -p < flower-auction/docs/database/init_permissions.sql

# 或通过API接口初始化
curl -X POST http://localhost:8081/api/v1/permissions/init
```

### 🎭 角色定义

| 角色ID | 角色名称 | 角色代码 | 描述 | 权限数量 |
|--------|----------|----------|------|----------|
| 1 | 系统管理员 | ADMIN | 系统管理员角色，拥有所有权限 | 82个 |
| 2 | 拍卖师 | AUCTIONEER | 负责主持拍卖会，控制拍卖流程 | 35个 |
| 3 | 买家 | BUYER | 参与拍卖竞价的买家 | 11个 |
| 4 | 质检员 | QUALITY_INSPECTOR | 负责商品质量检验 | 17个 |
| 7 | 财务官 | FINANCE | 财务管理人员 | 18个 |
| 8 | 运营 | OPERATOR | 运营人员，拥有所有权限 | 82个 |

### 📋 权限模块

系统权限按功能模块划分，共10个模块：

#### 1. 用户管理模块 (11个权限)
- 用户查看、创建、编辑、删除
- 用户状态管理、数据导出

#### 2. 角色管理模块 (11个权限)
- 角色查看、创建、编辑、删除
- 权限分配管理

#### 3. 商品管理模块 (11个权限)
- 商品查看、创建、编辑、删除
- 商品审核管理

#### 4. 分类管理模块 (9个权限)
- 分类查看、创建、编辑、删除

#### 5. 拍卖管理模块 (17个权限)
- 拍卖会管理、控制、主持
- 竞价操作、钟号管理

#### 6. 订单管理模块 (5个权限)
- 订单查看、处理、取消
- 退款处理、发货管理

#### 7. 财务管理模块 (5个权限)
- 财务查看、审核
- 佣金管理、结算管理、报表生成

#### 8. 报表统计模块 (5个权限)
- 销售报表、用户报表、拍卖报表
- 财务报表、数据导出

#### 9. 系统管理模块 (4个权限)
- 系统配置、日志查看
- 系统监控、数据备份

#### 10. 权限管理模块 (4个权限)
- 权限查看、创建、编辑、删除

### 📊 权限矩阵

| 角色 | 用户管理 | 角色管理 | 商品管理 | 分类管理 | 拍卖管理 | 订单管理 | 财务管理 | 报表统计 | 系统管理 | 权限管理 |
|------|----------|----------|----------|----------|----------|----------|----------|----------|----------|----------|
| 系统管理员 | ✅ 全部(11) | ✅ 全部(11) | ✅ 全部(11) | ✅ 全部(9) | ✅ 全部(17) | ✅ 全部(5) | ✅ 全部(5) | ✅ 全部(5) | ✅ 全部(4) | ✅ 全部(4) |
| 拍卖师 | ❌ 无 | ❌ 无 | ✅ 全部(11) | ✅ 全部(9) | ✅ 全部(17) | 🔍 查看(2) | ❌ 无 | 🔍 部分(4) | ❌ 无 | ❌ 无 |
| 买家 | ❌ 无 | ❌ 无 | 🔍 查看(3) | 🔍 查看(3) | 🛒 竞拍(5) | 🔍 查看(2) | ❌ 无 | ❌ 无 | ❌ 无 | ❌ 无 |
| 质检员 | ❌ 无 | ❌ 无 | ✅ 全部(11) | 🔍 查看(4) | 🔍 查看(2) | ❌ 无 | ❌ 无 | ❌ 无 | ❌ 无 | ❌ 无 |
| 财务官 | 🔍 查看(3) | ❌ 无 | ❌ 无 | ❌ 无 | 🔍 查看(2) | ✅ 全部(5) | ✅ 全部(5) | ✅ 全部(5) | ❌ 无 | ❌ 无 |
| 运营 | ✅ 全部(11) | ✅ 全部(11) | ✅ 全部(11) | ✅ 全部(9) | ✅ 全部(17) | ✅ 全部(5) | ✅ 全部(5) | ✅ 全部(5) | ✅ 全部(4) | ✅ 全部(4) |

### 🔧 权限配置管理

#### 通过API接口管理
```bash
# 查看角色权限
curl -X GET http://localhost:8081/api/v1/roles/{roleId}/permissions

# 分配角色权限
curl -X PUT http://localhost:8081/api/v1/roles/{roleId}/permissions \
  -H "Content-Type: application/json" \
  -d '{"permissionIds": [1,2,3,...]}'

# 查看所有权限
curl -X GET http://localhost:8081/api/v1/permissions?page=1&pageSize=100
```

#### 通过管理后台
管理员可以在后台管理系统中：
- 查看所有权限项和权限模块
- 为角色分配或移除权限
- 管理用户角色关联
- 查看权限使用统计

### 📁 相关文件

- **权限初始化脚本**: `flower-auction/docs/database/init_permissions.sql`
- **角色数据脚本**: `flower-auction/docs/database/init_data.sql`
- **权限管理API**: `flower-auction/internal/api/permission.go`
- **角色管理API**: `flower-auction/internal/api/role.go`

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 开发团队

- **后端开发** - Go微服务架构
- **前端开发** - React生态系统
- **UI/UX设计** - 专业拍卖界面设计
- **数据库设计** - 高性能数据架构

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 技术支持: [Support Email]

---

🌸 **让花卉拍卖更加智能、高效、透明！**