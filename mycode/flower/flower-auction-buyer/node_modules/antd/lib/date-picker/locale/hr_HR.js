"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _hr_HR = _interopRequireDefault(require("rc-picker/lib/locale/hr_HR"));
var _hr_HR2 = _interopRequireDefault(require("../../time-picker/locale/hr_HR"));
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: 'Odaberite datum',
    yearPlaceholder: 'Odaberite godinu',
    quarterPlaceholder: 'Odaberite četvrtinu',
    monthPlaceholder: 'Odaberite mjesec',
    weekPlaceholder: 'Odaberite tjedan',
    rangePlaceholder: ['Početni datum', 'Završni datum'],
    rangeYearPlaceholder: ['Početna godina', 'Završna godina'],
    rangeMonthPlaceholder: ['Početni mjesec', '<PERSON>avr<PERSON><PERSON> mjesec'],
    rangeWeekPlaceholder: ['Početni tjedan', 'Završ<PERSON> tjedan']
  }, _hr_HR.default),
  timePickerLocale: Object.assign({}, _hr_HR2.default)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
var _default = exports.default = locale;