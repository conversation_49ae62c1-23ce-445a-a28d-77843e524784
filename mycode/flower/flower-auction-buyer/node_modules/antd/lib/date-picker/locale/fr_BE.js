"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _fr_BE = _interopRequireDefault(require("rc-picker/lib/locale/fr_BE"));
var _fr_BE2 = _interopRequireDefault(require("../../time-picker/locale/fr_BE"));
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: 'Sélectionner une date',
    yearPlaceholder: 'Sélectionner une année',
    quarterPlaceholder: 'Sélectionner un trimestre',
    monthPlaceholder: 'Sélectionner un mois',
    weekPlaceholder: 'Sélectionner une semaine',
    rangePlaceholder: ['Date de début', 'Date de fin'],
    rangeYearPlaceholder: ['<PERSON><PERSON> de début', '<PERSON><PERSON> de fin'],
    rangeMonthPlaceholder: ['<PERSON><PERSON> de début', '<PERSON><PERSON> de fin'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON> de début', '<PERSON><PERSON><PERSON> de fin']
  }, _fr_BE.default),
  timePickerLocale: Object.assign({}, _fr_BE2.default)
};
// All settings at:
// https://github.com/ant-design/ant-design/issues/424
var _default = exports.default = locale;