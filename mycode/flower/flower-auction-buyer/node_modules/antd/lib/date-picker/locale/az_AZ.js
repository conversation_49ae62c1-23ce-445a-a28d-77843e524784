"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _az_AZ = _interopRequireDefault(require("rc-picker/lib/locale/az_AZ"));
var _az_AZ2 = _interopRequireDefault(require("../../time-picker/locale/az_AZ"));
const locale = {
  lang: Object.assign({
    placeholder: 'Tarix seçin',
    rangePlaceholder: ['Başlama tarixi', '<PERSON>m<PERSON> tarixi'],
    yearPlaceholder: 'İl seçin',
    quarterPlaceholder: 'Rüb seçin',
    monthPlaceholder: 'Ay seçin',
    weekPlaceholder: 'Həftə seçin',
    rangeYearPlaceholder: ['Başlama il', 'Bitmə il'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON>a rüb', 'Bit<PERSON>ə rüb'],
    rangeMonthPlaceholder: ['<PERSON><PERSON><PERSON>a ay', 'Bitmə ay'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON>a həftə', '<PERSON><PERSON>ə həftə']
  }, _az_AZ.default),
  timePickerLocale: Object.assign({}, _az_AZ2.default)
};
var _default = exports.default = locale;