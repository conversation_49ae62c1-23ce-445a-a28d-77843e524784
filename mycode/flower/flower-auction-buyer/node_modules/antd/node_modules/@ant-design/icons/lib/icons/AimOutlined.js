"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _AimOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/AimOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var AimOutlined = function AimOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _AimOutlined.default
  }));
};

/**![aim](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NTIgNDc0SDgyOS44QzgxMi41IDMyNy42IDY5Ni40IDIxMS41IDU1MCAxOTQuMlY3MmMwLTQuNC0zLjYtOC04LThoLTYwYy00LjQgMC04IDMuNi04IDh2MTIyLjJDMzI3LjYgMjExLjUgMjExLjUgMzI3LjYgMTk0LjIgNDc0SDcyYy00LjQgMC04IDMuNi04IDh2NjBjMCA0LjQgMy42IDggOCA4aDEyMi4yQzIxMS41IDY5Ni40IDMyNy42IDgxMi41IDQ3NCA4MjkuOFY5NTJjMCA0LjQgMy42IDggOCA4aDYwYzQuNCAwIDgtMy42IDgtOFY4MjkuOEM2OTYuNCA4MTIuNSA4MTIuNSA2OTYuNCA4MjkuOCA1NTBIOTUyYzQuNCAwIDgtMy42IDgtOHYtNjBjMC00LjQtMy42LTgtOC04ek01MTIgNzU2Yy0xMzQuOCAwLTI0NC0xMDkuMi0yNDQtMjQ0czEwOS4yLTI0NCAyNDQtMjQ0IDI0NCAxMDkuMiAyNDQgMjQ0LTEwOS4yIDI0NC0yNDQgMjQ0eiIgLz48cGF0aCBkPSJNNTEyIDM5MmMtMzIuMSAwLTYyLjEgMTIuNC04NC44IDM1LjItMjIuNyAyMi43LTM1LjIgNTIuNy0zNS4yIDg0LjhzMTIuNSA2Mi4xIDM1LjIgODQuOEM0NDkuOSA2MTkuNCA0ODAgNjMyIDUxMiA2MzJzNjIuMS0xMi41IDg0LjgtMzUuMkM2MTkuNCA1NzQuMSA2MzIgNTQ0IDYzMiA1MTJzLTEyLjUtNjIuMS0zNS4yLTg0LjhBMTE4LjU3IDExOC41NyAwIDAwNTEyIDM5MnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(AimOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AimOutlined';
}
var _default = exports.default = RefIcon;