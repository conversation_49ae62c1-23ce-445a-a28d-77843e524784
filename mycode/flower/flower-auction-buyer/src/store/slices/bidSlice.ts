import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Bid, BidRequest, AutoBidConfig, PreOrder, PreOrderStatus } from '../../types';
import apiClient from '../../services/api';

interface BidState {
  myBids: Bid[];
  recentBids: Bid[];
  autoBidConfigs: AutoBidConfig[];
  preOrders: PreOrder[];
  loading: boolean;
  bidding: boolean;
  error: string | null;
}

const initialState: BidState = {
  myBids: [],
  recentBids: [],
  autoBidConfigs: [],
  preOrders: [],
  loading: false,
  bidding: false,
  error: null,
};

// 异步actions
export const placeBidAsync = createAsyncThunk(
  'bid/placeBid',
  async (bidData: BidRequest, { rejectWithValue }) => {
    try {
      const response = await apiClient.post('/bids', bidData);
      if (response.data.success) {
        return response.data.data as Bid;
      } else {
        return rejectWithValue(response.data.message || '出价失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '出价失败');
    }
  }
);

export const getMyBidsAsync = createAsyncThunk(
  'bid/getMyBids',
  async (params: { page?: number; size?: number; batchId?: number }, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/bids/my', { params });
      if (response.data.success) {
        return response.data.data.items as Bid[];
      } else {
        return rejectWithValue(response.data.message || '获取我的出价失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取我的出价失败');
    }
  }
);

export const getBatchBidsAsync = createAsyncThunk(
  'bid/getBatchBids',
  async (batchId: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(`/batches/${batchId}/bids`);
      if (response.data.success) {
        return response.data.data as Bid[];
      } else {
        return rejectWithValue(response.data.message || '获取批次出价失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取批次出价失败');
    }
  }
);

export const setAutoBidAsync = createAsyncThunk(
  'bid/setAutoBid',
  async (config: Omit<AutoBidConfig, 'enabled'>, { rejectWithValue }) => {
    try {
      const response = await apiClient.post('/bids/auto-bid', { ...config, enabled: true });
      if (response.data.success) {
        return response.data.data as AutoBidConfig;
      } else {
        return rejectWithValue(response.data.message || '设置自动出价失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '设置自动出价失败');
    }
  }
);

export const cancelAutoBidAsync = createAsyncThunk(
  'bid/cancelAutoBid',
  async (batchId: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.delete(`/bids/auto-bid/${batchId}`);
      if (response.data.success) {
        return batchId;
      } else {
        return rejectWithValue(response.data.message || '取消自动出价失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '取消自动出价失败');
    }
  }
);

export const createPreOrderAsync = createAsyncThunk(
  'bid/createPreOrder',
  async (preOrderData: { batchId: number; price: number; quantity: number }, { rejectWithValue }) => {
    try {
      const response = await apiClient.post('/pre-orders', preOrderData);
      if (response.data.success) {
        return response.data.data as PreOrder;
      } else {
        return rejectWithValue(response.data.message || '创建埋单失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建埋单失败');
    }
  }
);

export const getMyPreOrdersAsync = createAsyncThunk(
  'bid/getMyPreOrders',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/pre-orders/my');
      if (response.data.success) {
        return response.data.data as PreOrder[];
      } else {
        return rejectWithValue(response.data.message || '获取我的埋单失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取我的埋单失败');
    }
  }
);

export const cancelPreOrderAsync = createAsyncThunk(
  'bid/cancelPreOrder',
  async (preOrderId: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.delete(`/pre-orders/${preOrderId}`);
      if (response.data.success) {
        return preOrderId;
      } else {
        return rejectWithValue(response.data.message || '取消埋单失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '取消埋单失败');
    }
  }
);

const bidSlice = createSlice({
  name: 'bid',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addRecentBid: (state, action: PayloadAction<Bid>) => {
      state.recentBids.unshift(action.payload);
      // 保持最近20条记录
      if (state.recentBids.length > 20) {
        state.recentBids = state.recentBids.slice(0, 20);
      }
    },
    updateBidStatus: (state, action: PayloadAction<{ bidId: number; status: number }>) => {
      const bid = state.myBids.find(b => b.id === action.payload.bidId);
      if (bid) {
        bid.status = action.payload.status;
      }
    },
    updatePreOrderStatus: (state, action: PayloadAction<{ preOrderId: number; status: PreOrderStatus }>) => {
      const preOrder = state.preOrders.find(p => p.id === action.payload.preOrderId);
      if (preOrder) {
        preOrder.status = action.payload.status;
      }
    },
    clearRecentBids: (state) => {
      state.recentBids = [];
    },
  },
  extraReducers: (builder) => {
    // 出价
    builder
      .addCase(placeBidAsync.pending, (state) => {
        state.bidding = true;
        state.error = null;
      })
      .addCase(placeBidAsync.fulfilled, (state, action) => {
        state.bidding = false;
        state.myBids.unshift(action.payload);
        state.recentBids.unshift(action.payload);
        state.error = null;
      })
      .addCase(placeBidAsync.rejected, (state, action) => {
        state.bidding = false;
        state.error = action.payload as string;
      });

    // 获取我的出价
    builder
      .addCase(getMyBidsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMyBidsAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.myBids = action.payload;
        state.error = null;
      })
      .addCase(getMyBidsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 获取批次出价
    builder
      .addCase(getBatchBidsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBatchBidsAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.recentBids = action.payload;
        state.error = null;
      })
      .addCase(getBatchBidsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 设置自动出价
    builder
      .addCase(setAutoBidAsync.fulfilled, (state, action) => {
        const index = state.autoBidConfigs.findIndex(config => config.batchId === action.payload.batchId);
        if (index !== -1) {
          state.autoBidConfigs[index] = action.payload;
        } else {
          state.autoBidConfigs.push(action.payload);
        }
      })
      .addCase(setAutoBidAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // 取消自动出价
    builder
      .addCase(cancelAutoBidAsync.fulfilled, (state, action) => {
        state.autoBidConfigs = state.autoBidConfigs.filter(config => config.batchId !== action.payload);
      })
      .addCase(cancelAutoBidAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // 创建埋单
    builder
      .addCase(createPreOrderAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPreOrderAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.preOrders.unshift(action.payload);
        state.error = null;
      })
      .addCase(createPreOrderAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 获取我的埋单
    builder
      .addCase(getMyPreOrdersAsync.fulfilled, (state, action) => {
        state.preOrders = action.payload;
      })
      .addCase(getMyPreOrdersAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // 取消埋单
    builder
      .addCase(cancelPreOrderAsync.fulfilled, (state, action) => {
        state.preOrders = state.preOrders.filter(preOrder => preOrder.id !== action.payload);
      })
      .addCase(cancelPreOrderAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  addRecentBid,
  updateBidStatus,
  updatePreOrderStatus,
  clearRecentBids,
} = bidSlice.actions;

export default bidSlice.reducer;
