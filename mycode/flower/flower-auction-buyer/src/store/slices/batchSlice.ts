import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Batch, BatchQueryParams, PaginatedResponse, BatchStatus, WatchList } from '../../types';
import apiClient from '../../services/api';

interface BatchState {
  batches: Batch[];
  currentBatch: Batch | null;
  watchList: WatchList[];
  total: number;
  page: number;
  size: number;
  loading: boolean;
  error: string | null;
  queryParams: BatchQueryParams;
  categories: Array<{ id: number; name: string }>;
}

const initialState: BatchState = {
  batches: [],
  currentBatch: null,
  watchList: [],
  total: 0,
  page: 1,
  size: 20,
  loading: false,
  error: null,
  queryParams: {
    page: 1,
    size: 20,
  },
  categories: [],
};

// 异步actions
export const getBatchesAsync = createAsyncThunk(
  'batch/getBatches',
  async (params: BatchQueryParams, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/batches', { params });
      if (response.data.success) {
        return response.data.data as PaginatedResponse<Batch>;
      } else {
        return rejectWithValue(response.data.message || '获取批次列表失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取批次列表失败');
    }
  }
);

export const getBatchByIdAsync = createAsyncThunk(
  'batch/getBatchById',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(`/batches/${id}`);
      if (response.data.success) {
        return response.data.data as Batch;
      } else {
        return rejectWithValue(response.data.message || '获取批次详情失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取批次详情失败');
    }
  }
);

export const getWatchListAsync = createAsyncThunk(
  'batch/getWatchList',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/batches/watch-list');
      if (response.data.success) {
        return response.data.data as WatchList[];
      } else {
        return rejectWithValue(response.data.message || '获取关注列表失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取关注列表失败');
    }
  }
);

export const addToWatchListAsync = createAsyncThunk(
  'batch/addToWatchList',
  async (batchId: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.post(`/batches/${batchId}/watch`);
      if (response.data.success) {
        return { batchId, watchItem: response.data.data };
      } else {
        return rejectWithValue(response.data.message || '添加关注失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '添加关注失败');
    }
  }
);

export const removeFromWatchListAsync = createAsyncThunk(
  'batch/removeFromWatchList',
  async (batchId: number, { rejectWithValue }) => {
    try {
      const response = await apiClient.delete(`/batches/${batchId}/watch`);
      if (response.data.success) {
        return batchId;
      } else {
        return rejectWithValue(response.data.message || '取消关注失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '取消关注失败');
    }
  }
);

export const getCategoriesAsync = createAsyncThunk(
  'batch/getCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get('/categories');
      if (response.data.success) {
        return response.data.data;
      } else {
        return rejectWithValue(response.data.message || '获取分类列表失败');
      }
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取分类列表失败');
    }
  }
);

const batchSlice = createSlice({
  name: 'batch',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setQueryParams: (state, action: PayloadAction<Partial<BatchQueryParams>>) => {
      state.queryParams = { ...state.queryParams, ...action.payload };
    },
    setCurrentBatch: (state, action: PayloadAction<Batch | null>) => {
      state.currentBatch = action.payload;
    },
    updateBatchInList: (state, action: PayloadAction<Batch>) => {
      const index = state.batches.findIndex(batch => batch.id === action.payload.id);
      if (index !== -1) {
        state.batches[index] = action.payload;
      }
      if (state.currentBatch?.id === action.payload.id) {
        state.currentBatch = action.payload;
      }
    },
    updateBatchPrice: (state, action: PayloadAction<{ batchId: number; price: number }>) => {
      const batch = state.batches.find(b => b.id === action.payload.batchId);
      if (batch) {
        batch.currentPrice = action.payload.price;
      }
      if (state.currentBatch?.id === action.payload.batchId) {
        state.currentBatch.currentPrice = action.payload.price;
      }
    },
    updateBatchStatus: (state, action: PayloadAction<{ batchId: number; status: BatchStatus }>) => {
      const batch = state.batches.find(b => b.id === action.payload.batchId);
      if (batch) {
        batch.status = action.payload.status;
      }
      if (state.currentBatch?.id === action.payload.batchId) {
        state.currentBatch.status = action.payload.status;
      }
    },
    toggleWatchStatus: (state, action: PayloadAction<{ batchId: number; isWatched: boolean }>) => {
      const batch = state.batches.find(b => b.id === action.payload.batchId);
      if (batch) {
        batch.isWatched = action.payload.isWatched;
        batch.watchCount += action.payload.isWatched ? 1 : -1;
      }
      if (state.currentBatch?.id === action.payload.batchId) {
        state.currentBatch.isWatched = action.payload.isWatched;
        state.currentBatch.watchCount += action.payload.isWatched ? 1 : -1;
      }
    },
  },
  extraReducers: (builder) => {
    // 获取批次列表
    builder
      .addCase(getBatchesAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBatchesAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.batches = action.payload.items;
        state.total = action.payload.total;
        state.page = action.payload.page;
        state.size = action.payload.size;
        state.error = null;
      })
      .addCase(getBatchesAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 获取批次详情
    builder
      .addCase(getBatchByIdAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getBatchByIdAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.currentBatch = action.payload;
        state.error = null;
      })
      .addCase(getBatchByIdAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 获取关注列表
    builder
      .addCase(getWatchListAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getWatchListAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.watchList = action.payload;
        state.error = null;
      })
      .addCase(getWatchListAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 添加关注
    builder
      .addCase(addToWatchListAsync.fulfilled, (state, action) => {
        const { batchId, watchItem } = action.payload;
        state.watchList.push(watchItem);
        
        // 更新批次的关注状态
        const batch = state.batches.find(b => b.id === batchId);
        if (batch) {
          batch.isWatched = true;
          batch.watchCount += 1;
        }
        if (state.currentBatch?.id === batchId) {
          state.currentBatch.isWatched = true;
          state.currentBatch.watchCount += 1;
        }
      })
      .addCase(addToWatchListAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // 取消关注
    builder
      .addCase(removeFromWatchListAsync.fulfilled, (state, action) => {
        const batchId = action.payload;
        state.watchList = state.watchList.filter(item => item.batchId !== batchId);
        
        // 更新批次的关注状态
        const batch = state.batches.find(b => b.id === batchId);
        if (batch) {
          batch.isWatched = false;
          batch.watchCount = Math.max(0, batch.watchCount - 1);
        }
        if (state.currentBatch?.id === batchId) {
          state.currentBatch.isWatched = false;
          state.currentBatch.watchCount = Math.max(0, state.currentBatch.watchCount - 1);
        }
      })
      .addCase(removeFromWatchListAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // 获取分类列表
    builder
      .addCase(getCategoriesAsync.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      .addCase(getCategoriesAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setQueryParams,
  setCurrentBatch,
  updateBatchInList,
  updateBatchPrice,
  updateBatchStatus,
  toggleWatchStatus,
} = batchSlice.actions;

export default batchSlice.reducer;
