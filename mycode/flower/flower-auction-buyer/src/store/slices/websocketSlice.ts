import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AuctionRealTimeData } from '../../types';

interface WebSocketState {
  connected: boolean;
  reconnectAttempts: number;
  auctionData: Record<number, AuctionRealTimeData>; // batchId -> data
  subscribedBatches: number[];
  subscribedClocks: number[];
  lastMessage: any;
  error: string | null;
}

const initialState: WebSocketState = {
  connected: false,
  reconnectAttempts: 0,
  auctionData: {},
  subscribedBatches: [],
  subscribedClocks: [],
  lastMessage: null,
  error: null,
};

const websocketSlice = createSlice({
  name: 'websocket',
  initialState,
  reducers: {
    setConnected: (state, action: PayloadAction<boolean>) => {
      state.connected = action.payload;
      if (action.payload) {
        state.reconnectAttempts = 0;
        state.error = null;
      }
    },
    setReconnectAttempts: (state, action: PayloadAction<number>) => {
      state.reconnectAttempts = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    updateAuctionData: (state, action: PayloadAction<AuctionRealTimeData>) => {
      state.auctionData[action.payload.batchId] = action.payload;
    },
    removeAuctionData: (state, action: PayloadAction<number>) => {
      delete state.auctionData[action.payload];
    },
    addSubscribedBatch: (state, action: PayloadAction<number>) => {
      if (!state.subscribedBatches.includes(action.payload)) {
        state.subscribedBatches.push(action.payload);
      }
    },
    removeSubscribedBatch: (state, action: PayloadAction<number>) => {
      state.subscribedBatches = state.subscribedBatches.filter(id => id !== action.payload);
    },
    addSubscribedClock: (state, action: PayloadAction<number>) => {
      if (!state.subscribedClocks.includes(action.payload)) {
        state.subscribedClocks.push(action.payload);
      }
    },
    removeSubscribedClock: (state, action: PayloadAction<number>) => {
      state.subscribedClocks = state.subscribedClocks.filter(id => id !== action.payload);
    },
    setLastMessage: (state, action: PayloadAction<any>) => {
      state.lastMessage = action.payload;
    },
    clearAuctionData: (state) => {
      state.auctionData = {};
    },
    clearSubscriptions: (state) => {
      state.subscribedBatches = [];
      state.subscribedClocks = [];
    },
  },
});

export const {
  setConnected,
  setReconnectAttempts,
  setError,
  updateAuctionData,
  removeAuctionData,
  addSubscribedBatch,
  removeSubscribedBatch,
  addSubscribedClock,
  removeSubscribedClock,
  setLastMessage,
  clearAuctionData,
  clearSubscriptions,
} = websocketSlice.actions;

export default websocketSlice.reducer;
