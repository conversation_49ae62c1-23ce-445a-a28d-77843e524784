import React from 'react';
import { Card, Tag, Button, Typography, Space, Image, Badge, Tooltip } from 'antd';
import {
  HeartOutlined,
  HeartFilled,
  EyeOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  FireOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { Batch, BatchStatus } from '../../types';
import dayjs from 'dayjs';
import './index.css';

const { Text, Title } = Typography;

interface BatchCardProps {
  batch: Batch;
  onWatch: (batchId: number, isWatched: boolean) => void;
  onClick: (batchId: number) => void;
}

const BatchCard: React.FC<BatchCardProps> = ({ batch, onWatch, onClick }) => {
  const getStatusColor = (status: BatchStatus) => {
    switch (status) {
      case BatchStatus.PENDING:
        return 'default';
      case BatchStatus.BIDDING:
        return 'processing';
      case BatchStatus.SOLD:
        return 'success';
      case BatchStatus.UNSOLD:
        return 'warning';
      case BatchStatus.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: BatchStatus) => {
    switch (status) {
      case BatchStatus.PENDING:
        return '待起拍';
      case BatchStatus.BIDDING:
        return '竞拍中';
      case BatchStatus.SOLD:
        return '已成交';
      case BatchStatus.UNSOLD:
        return '流拍';
      case BatchStatus.CANCELLED:
        return '已取消';
      default:
        return '未知';
    }
  };

  const formatTime = (time?: string) => {
    if (!time) return '';
    return dayjs(time).format('MM-DD HH:mm');
  };

  const getTimeRemaining = () => {
    if (!batch.auctionStartTime || batch.status !== BatchStatus.PENDING) {
      return null;
    }
    
    const now = dayjs();
    const startTime = dayjs(batch.auctionStartTime);
    const diff = startTime.diff(now);
    
    if (diff <= 0) return '即将开始';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}天后开拍`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟后开拍`;
    } else {
      return `${minutes}分钟后开拍`;
    }
  };

  const handleWatchClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onWatch(batch.id, batch.isWatched || false);
  };

  const handleCardClick = () => {
    onClick(batch.id);
  };

  const isHot = batch.watchCount > 50 || (batch.currentPrice && batch.currentPrice > batch.startPrice * 1.5);
  const timeRemaining = getTimeRemaining();

  return (
    <Card
      className={`batch-card ${batch.status === BatchStatus.BIDDING ? 'bidding' : ''}`}
      hoverable
      onClick={handleCardClick}
      cover={
        <div className="card-cover">
          <Image
            alt={batch.productName}
            src={batch.images?.[0] || '/placeholder-flower.jpg'}
            preview={false}
            fallback="/placeholder-flower.jpg"
          />
          <div className="card-overlay">
            <div className="overlay-top">
              <Tag color={getStatusColor(batch.status)} className="status-tag">
                {getStatusText(batch.status)}
              </Tag>
              {isHot && (
                <Tag color="red" icon={<FireOutlined />} className="hot-tag">
                  热门
                </Tag>
              )}
            </div>
            <div className="overlay-bottom">
              <Button
                type="text"
                icon={batch.isWatched ? <HeartFilled /> : <HeartOutlined />}
                className={`watch-btn ${batch.isWatched ? 'watched' : ''}`}
                onClick={handleWatchClick}
              />
            </div>
          </div>
        </div>
      }
      actions={[
        <Tooltip title="关注人数">
          <Space>
            <EyeOutlined />
            <Text>{batch.watchCount}</Text>
          </Space>
        </Tooltip>,
        <Tooltip title="钟号">
          <Space>
            <ClockCircleOutlined />
            <Text>钟{batch.clockNumber}</Text>
          </Space>
        </Tooltip>,
        <Tooltip title="质量等级">
          <Space>
            <TrophyOutlined />
            <Text>等级{batch.qualityLevel}</Text>
          </Space>
        </Tooltip>
      ]}
    >
      <Card.Meta
        title={
          <div className="card-title">
            <Tooltip title={batch.productName}>
              <Text strong ellipsis className="product-name">
                {batch.productName}
              </Text>
            </Tooltip>
            <Text type="secondary" className="batch-number">
              #{batch.batchNumber}
            </Text>
          </div>
        }
        description={
          <div className="card-description">
            <div className="supplier-info">
              <Text type="secondary" ellipsis>
                {batch.supplierName}
              </Text>
            </div>
            
            <div className="quantity-info">
              <Text>
                数量: <Text strong>{batch.quantity} {batch.unit}</Text>
              </Text>
            </div>

            <div className="price-info">
              <div className="price-row">
                <Text type="secondary">起拍价:</Text>
                <Text className="start-price">¥{batch.startPrice}</Text>
              </div>
              {batch.currentPrice && batch.currentPrice > batch.startPrice && (
                <div className="price-row">
                  <Text type="secondary">当前价:</Text>
                  <Text className="current-price">¥{batch.currentPrice}</Text>
                </div>
              )}
            </div>

            {timeRemaining && (
              <div className="time-info">
                <ClockCircleOutlined className="time-icon" />
                <Text className="time-text">{timeRemaining}</Text>
              </div>
            )}

            {batch.status === BatchStatus.BIDDING && batch.auctionEndTime && (
              <div className="time-info urgent">
                <ClockCircleOutlined className="time-icon" />
                <Text className="time-text">
                  {formatTime(batch.auctionEndTime)} 结束
                </Text>
              </div>
            )}
          </div>
        }
      />
    </Card>
  );
};

export default BatchCard;
