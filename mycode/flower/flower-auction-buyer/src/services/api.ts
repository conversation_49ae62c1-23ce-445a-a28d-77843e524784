import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { message } from 'antd';

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081/api/v1';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT || '10000'),
  headers: {
    'Content-Type': 'application/json',
  },
});

// 防止重复刷新token的标志
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (reason?: any) => void;
}> = [];

// 处理失败队列
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

// 请求拦截器
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('buyer_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    const { response } = error;

    if (response) {
      switch (response.status) {
        case 401:
          // token过期或无效
          if (originalRequest._retry) {
            // 已经重试过，直接跳转登录页
            localStorage.removeItem('buyer_token');
            localStorage.removeItem('buyer_refreshToken');
            localStorage.removeItem('buyer_user');
            if (window.location.pathname !== '/login') {
              window.location.href = '/login';
              message.error('登录已过期，请重新登录');
            }
            return Promise.reject(error);
          }

          if (isRefreshing) {
            // 正在刷新token，将请求加入队列
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject });
            }).then(token => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return apiClient(originalRequest);
            }).catch(err => {
              return Promise.reject(err);
            });
          }

          originalRequest._retry = true;
          isRefreshing = true;

          const refreshToken = localStorage.getItem('buyer_refreshToken');
          if (refreshToken) {
            try {
              // 尝试刷新token
              const refreshResponse = await axios.post(
                `${API_BASE_URL}/auth/refresh`,
                { refreshToken }
              );

              if (refreshResponse.data.success) {
                const newToken = refreshResponse.data.data.token;
                const newRefreshToken = refreshResponse.data.data.refreshToken;
                
                // 更新token
                localStorage.setItem('buyer_token', newToken);
                localStorage.setItem('buyer_refreshToken', newRefreshToken);

                // 处理队列中的请求
                processQueue(null, newToken);
                isRefreshing = false;

                // 重新发送原请求
                originalRequest.headers.Authorization = `Bearer ${newToken}`;
                return apiClient(originalRequest);
              }
            } catch (refreshError) {
              console.error('Token refresh failed:', refreshError);
              processQueue(refreshError, null);
            }
          }

          // 刷新失败，清除认证信息并跳转到登录页
          isRefreshing = false;
          localStorage.removeItem('buyer_token');
          localStorage.removeItem('buyer_refreshToken');
          localStorage.removeItem('buyer_user');

          // 只有当前不在登录页时才跳转，避免无限重定向
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
            message.error('登录已过期，请重新登录');
          }
          break;

        case 403:
          message.error('权限不足，无法访问该资源');
          break;

        case 404:
          message.error('请求的资源不存在');
          break;

        case 429:
          message.error('请求过于频繁，请稍后再试');
          break;

        case 500:
          message.error('服务器内部错误，请稍后再试');
          break;

        default:
          if (response.data?.message) {
            message.error(response.data.message);
          } else {
            message.error('网络请求失败，请检查网络连接');
          }
          break;
      }
    } else {
      // 网络错误
      if (error.code === 'ECONNABORTED') {
        message.error('请求超时，请稍后再试');
      } else {
        message.error('网络连接失败，请检查网络设置');
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
