import apiClient from './api';
import { LoginRequest, RegisterRequest, LoginResponse, User, ApiResponse } from '../types';

class AuthService {
  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await apiClient.post('/auth/login', credentials);
      
      if (response.data.success) {
        const { token, refreshToken, user } = response.data.data;
        
        // 存储认证信息（使用buyer前缀区分）
        localStorage.setItem('buyer_token', token);
        localStorage.setItem('buyer_refreshToken', refreshToken);
        localStorage.setItem('buyer_user', JSON.stringify(user));
        
        return response.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * 用户注册
   */
  async register(registerData: RegisterRequest): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.post('/auth/register', registerData);
      return response.data;
    } catch (error: any) {
      console.error('Register error:', error);
      throw error;
    }
  }

  /**
   * 发送验证码
   */
  async sendVerificationCode(phone: string): Promise<ApiResponse> {
    try {
      const response = await apiClient.post('/auth/send-code', { phone });
      return response.data;
    } catch (error: any) {
      console.error('Send verification code error:', error);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('buyer_token');
      localStorage.removeItem('buyer_refreshToken');
      localStorage.removeItem('buyer_user');
      
      // 跳转到登录页
      window.location.href = '/login';
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.get('/auth/me');
      
      if (response.data.success) {
        // 更新本地存储的用户信息
        localStorage.setItem('buyer_user', JSON.stringify(response.data.data));
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Get current user error:', error);
      throw error;
    }
  }

  /**
   * 刷新token
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> {
    try {
      const response = await apiClient.post('/auth/refresh', { refreshToken });
      
      if (response.data.success) {
        const { token, refreshToken: newRefreshToken } = response.data.data;
        
        // 更新本地存储
        localStorage.setItem('buyer_token', token);
        localStorage.setItem('buyer_refreshToken', newRefreshToken);
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Refresh token error:', error);
      throw error;
    }
  }

  /**
   * 修改密码
   */
  async changePassword(oldPassword: string, newPassword: string): Promise<ApiResponse> {
    try {
      const response = await apiClient.post('/auth/change-password', {
        oldPassword,
        newPassword
      });
      
      return response.data;
    } catch (error: any) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('buyer_token');
    const user = localStorage.getItem('buyer_user');
    
    if (!token || !user) {
      return false;
    }

    try {
      // 检查token是否过期
      const tokenPayload = this.parseJWT(token);
      if (tokenPayload && tokenPayload.exp) {
        const expirationTime = tokenPayload.exp * 1000;
        const currentTime = Date.now();
        
        // 如果token在5分钟内过期，认为需要刷新
        if (expirationTime - currentTime < 5 * 60 * 1000) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  /**
   * 获取本地存储的用户信息
   */
  getStoredUser(): User | null {
    try {
      const userStr = localStorage.getItem('buyer_user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Parse stored user error:', error);
      return null;
    }
  }

  /**
   * 获取本地存储的token
   */
  getStoredToken(): string | null {
    return localStorage.getItem('buyer_token');
  }

  /**
   * 解析JWT token
   */
  private parseJWT(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Failed to parse JWT:', error);
      return null;
    }
  }

  /**
   * 检查用户是否为购买商
   */
  isBuyer(user?: User): boolean {
    const currentUser = user || this.getStoredUser();
    if (!currentUser) return false;
    
    // 检查用户类型是否为购买商 (userType = 2)
    return currentUser.userType === 2;
  }

  /**
   * 检查用户余额是否足够
   */
  hasEnoughBalance(amount: number, user?: User): boolean {
    const currentUser = user || this.getStoredUser();
    if (!currentUser) return false;
    
    return currentUser.balance >= amount;
  }

  /**
   * 获取用户可用余额
   */
  getAvailableBalance(user?: User): number {
    const currentUser = user || this.getStoredUser();
    if (!currentUser) return 0;
    
    return currentUser.balance - currentUser.frozenAmount;
  }
}

export const authService = new AuthService();
export default authService;
