import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Table, 
  Tag, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  Empty,
  message,
  Modal,
  Descriptions,
  Statistic,
  Row,
  Col,
  Tooltip,
  Progress
} from 'antd';
import { 
  TrophyOutlined, 
  EyeOutlined, 
  ReloadOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  FireOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { getMyBidsAsync, getMyPreOrdersAsync } from '../../store/slices/bidSlice';
import { Bid, PreOrder, BidStatus } from '../../types';
import './index.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Bids: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { myBids, myPreOrders, loading, error } = useSelector((state: RootState) => state.bid);
  const [activeTab, setActiveTab] = useState('bids');
  const [selectedBid, setSelectedBid] = useState<Bid | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  useEffect(() => {
    loadData();
  }, [dispatch, activeTab, currentPage]);

  const loadData = () => {
    if (activeTab === 'bids') {
      dispatch(getMyBidsAsync({ page: currentPage, size: pageSize }));
    } else {
      dispatch(getMyPreOrdersAsync());
    }
  };

  const getBidStatusTag = (bid: Bid) => {
    if (bid.isWinning) {
      return <Tag color="green" icon={<TrophyOutlined />}>中标</Tag>;
    }
    
    switch (bid.status) {
      case BidStatus.ACTIVE:
        return <Tag color="blue" icon={<FireOutlined />}>竞价中</Tag>;
      case BidStatus.OUTBID:
        return <Tag color="orange" icon={<ExclamationCircleOutlined />}>被超价</Tag>;
      case BidStatus.WON:
        return <Tag color="green" icon={<CheckCircleOutlined />}>中标</Tag>;
      case BidStatus.LOST:
        return <Tag color="red" icon={<CloseCircleOutlined />}>未中标</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  const getPreOrderStatusTag = (status: number) => {
    const statusConfig = {
      0: { color: 'orange', text: '待生效' },
      1: { color: 'blue', text: '生效中' },
      2: { color: 'green', text: '已执行' },
      3: { color: 'red', text: '已取消' },
      4: { color: 'default', text: '已过期' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const handleViewDetail = (bid: Bid) => {
    setSelectedBid(bid);
    setDetailModalVisible(true);
  };

  const bidColumns = [
    {
      title: '商品信息',
      key: 'productInfo',
      render: (record: Bid) => (
        <div className="product-info">
          <div className="product-name">
            <Text strong>{record.batch?.productName || '未知商品'}</Text>
          </div>
          <div className="product-details">
            <Text type="secondary">
              批次: {record.batch?.batchNumber} | 
              规格: {record.batch?.grade} | 
              钟号: {record.batch?.clockNumber}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '出价信息',
      key: 'bidInfo',
      render: (record: Bid) => (
        <div className="bid-info">
          <div className="bid-price">
            <Text strong style={{ color: '#ff6b9d', fontSize: '16px' }}>
              ¥{record.price.toFixed(2)}
            </Text>
          </div>
          <div className="bid-time">
            <Text type="secondary">
              <ClockCircleOutlined /> {new Date(record.createdAt).toLocaleString()}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '当前价格',
      key: 'currentPrice',
      render: (record: Bid) => (
        <div className="current-price">
          <Text strong style={{ fontSize: '16px' }}>
            ¥{record.batch?.currentPrice?.toFixed(2) || '0.00'}
          </Text>
          {record.batch?.currentPrice && record.price < record.batch.currentPrice && (
            <div>
              <Text type="danger" style={{ fontSize: '12px' }}>
                已被超价 ¥{(record.batch.currentPrice - record.price).toFixed(2)}
              </Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Bid) => getBidStatusTag(record),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Bid) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  const preOrderColumns = [
    {
      title: '商品信息',
      key: 'productInfo',
      render: (record: PreOrder) => (
        <div className="product-info">
          <div className="product-name">
            <Text strong>{record.batch?.productName || '未知商品'}</Text>
          </div>
          <div className="product-details">
            <Text type="secondary">
              批次: {record.batch?.batchNumber} | 
              规格: {record.batch?.grade}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '埋单价格',
      key: 'price',
      render: (record: PreOrder) => (
        <Text strong style={{ color: '#ff6b9d', fontSize: '16px' }}>
          ¥{record.price.toFixed(2)}
        </Text>
      ),
    },
    {
      title: '数量',
      key: 'quantity',
      render: (record: PreOrder) => (
        <Text>{record.quantity}{record.batch?.unit || '件'}</Text>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: PreOrder) => getPreOrderStatusTag(record.status),
    },
    {
      title: '创建时间',
      key: 'createdAt',
      render: (record: PreOrder) => (
        <Text type="secondary">
          {new Date(record.createdAt).toLocaleString()}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: PreOrder) => (
        <Space>
          <Button type="link" size="small">编辑</Button>
          <Button type="link" size="small" danger>取消</Button>
        </Space>
      ),
    },
  ];

  // 计算统计数据
  const bidStats = {
    totalBids: myBids.length,
    winningBids: myBids.filter(bid => bid.isWinning).length,
    activeBids: myBids.filter(bid => bid.status === BidStatus.ACTIVE).length,
    totalAmount: myBids.reduce((sum, bid) => sum + bid.price, 0),
  };

  return (
    <div className="bids-container">
      {/* 页面头部 */}
      <div className="bids-header">
        <div className="header-left">
          <Title level={2} className="page-title">
            <TrophyOutlined className="title-icon" />
            我的出价
          </Title>
          <Text type="secondary">
            管理您的竞价记录和埋单
          </Text>
        </div>
        <div className="header-right">
          <Button 
            icon={<ReloadOutlined />} 
            onClick={loadData}
            loading={loading}
          >
            刷新
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="stats-cards">
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="总出价次数"
              value={bidStats.totalBids}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="中标次数"
              value={bidStats.winningBids}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="竞价中"
              value={bidStats.activeBids}
              prefix={<FireOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card>
            <Statistic
              title="出价总额"
              value={bidStats.totalAmount}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#ff6b9d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 标签页 */}
      <Card className="bids-tabs">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={[
            { 
              key: 'bids', 
              label: `竞价记录 (${myBids.length})`,
              children: (
                <div className="bids-table">
                  {myBids.length === 0 && !loading ? (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无竞价记录"
                    >
                      <Button type="primary" href="/">
                        去首页参与竞价
                      </Button>
                    </Empty>
                  ) : (
                    <Table
                      columns={bidColumns}
                      dataSource={myBids}
                      rowKey="id"
                      loading={loading}
                      pagination={{
                        current: currentPage,
                        pageSize: pageSize,
                        total: myBids.length,
                        onChange: setCurrentPage,
                        showSizeChanger: false,
                        showQuickJumper: true,
                        showTotal: (total, range) => 
                          `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                      }}
                    />
                  )}
                </div>
              )
            },
            { 
              key: 'preorders', 
              label: `埋单记录 (${myPreOrders.length})`,
              children: (
                <div className="preorders-table">
                  {myPreOrders.length === 0 && !loading ? (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无埋单记录"
                    >
                      <Button type="primary" href="/">
                        去首页设置埋单
                      </Button>
                    </Empty>
                  ) : (
                    <Table
                      columns={preOrderColumns}
                      dataSource={myPreOrders}
                      rowKey="id"
                      loading={loading}
                      pagination={false}
                    />
                  )}
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 出价详情模态框 */}
      <Modal
        title="出价详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedBid && (
          <div className="bid-detail">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="商品名称" span={2}>
                {selectedBid.batch?.productName}
              </Descriptions.Item>
              <Descriptions.Item label="批次号">
                {selectedBid.batch?.batchNumber}
              </Descriptions.Item>
              <Descriptions.Item label="钟号">
                {selectedBid.batch?.clockNumber}
              </Descriptions.Item>
              <Descriptions.Item label="我的出价">
                <Text strong style={{ color: '#ff6b9d', fontSize: '16px' }}>
                  ¥{selectedBid.price.toFixed(2)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="当前价格">
                <Text strong style={{ fontSize: '16px' }}>
                  ¥{selectedBid.batch?.currentPrice?.toFixed(2) || '0.00'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="起拍价">
                ¥{selectedBid.batch?.startPrice?.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="加价幅度">
                ¥{selectedBid.batch?.stepPrice?.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="出价状态">
                {getBidStatusTag(selectedBid)}
              </Descriptions.Item>
              <Descriptions.Item label="竞价次数">
                {selectedBid.batch?.bidCount || 0} 次
              </Descriptions.Item>
              <Descriptions.Item label="出价时间">
                {new Date(selectedBid.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="拍卖状态">
                <Tag color={selectedBid.batch?.status === 'completed' ? 'green' : 'blue'}>
                  {selectedBid.batch?.status === 'completed' ? '已结束' : '进行中'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Bids;
