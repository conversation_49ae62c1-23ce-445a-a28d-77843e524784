.bids-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.bids-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left .page-title {
  margin-bottom: 8px !important;
  color: #ff6b9d;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #ff6b9d;
  font-size: 28px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-cards .ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.stats-cards .ant-statistic-title {
  color: #8c8c8c;
  font-size: 14px;
}

.stats-cards .ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

.bids-tabs {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.bids-tabs .ant-card-body {
  padding: 0;
}

.bids-tabs .ant-tabs {
  margin: 0;
}

.bids-tabs .ant-tabs-tab {
  padding: 16px 24px;
  font-size: 16px;
}

.bids-table,
.preorders-table {
  padding: 24px;
}

.product-info {
  min-width: 200px;
}

.product-name {
  margin-bottom: 4px;
}

.product-details {
  font-size: 12px;
}

.bid-info {
  min-width: 150px;
}

.bid-price {
  margin-bottom: 4px;
}

.bid-time {
  font-size: 12px;
}

.current-price {
  text-align: right;
  min-width: 120px;
}

.bid-detail .ant-descriptions-item-label {
  background: #fafafa;
  font-weight: 500;
}

/* 状态标签样式优化 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 主题色调整 */
.bids-container .ant-btn-primary {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.bids-container .ant-btn-primary:hover {
  background: linear-gradient(135deg, #ff4d7d 0%, #ff3366 100%);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
}

.bids-container .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #ff6b9d;
}

.bids-container .ant-tabs-ink-bar {
  background: #ff6b9d;
}

.bids-container .ant-pagination-item-active {
  border-color: #ff6b9d;
  background: #ff6b9d;
}

.bids-container .ant-pagination-item-active a {
  color: #fff;
}

.bids-container .ant-pagination-item:hover {
  border-color: #ff6b9d;
}

.bids-container .ant-pagination-item:hover a {
  color: #ff6b9d;
}

/* 统计卡片动画 */
.stats-cards .ant-card {
  transition: all 0.3s ease;
}

.stats-cards .ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bids-container {
    padding: 16px;
  }

  .bids-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left .page-title {
    font-size: 20px !important;
  }

  .header-right {
    justify-content: flex-end;
  }

  .stats-cards {
    margin-bottom: 16px;
  }

  .bids-tabs .ant-tabs-tab {
    padding: 12px 16px;
    font-size: 14px;
  }

  .bids-table,
  .preorders-table {
    padding: 16px;
  }

  .product-info,
  .bid-info,
  .current-price {
    min-width: auto;
  }

  .bids-table .ant-table-tbody > tr > td,
  .preorders-table .ant-table-tbody > tr > td {
    padding: 12px 8px;
  }
}

@media (max-width: 576px) {
  .bids-container {
    padding: 12px;
  }

  .bids-header {
    padding: 16px;
  }

  .header-left .page-title {
    font-size: 18px !important;
  }

  .title-icon {
    font-size: 24px;
  }

  .stats-cards .ant-statistic-content {
    font-size: 18px;
  }

  .bids-tabs .ant-tabs-tab {
    padding: 10px 12px;
    font-size: 13px;
  }

  .bids-table,
  .preorders-table {
    padding: 12px;
  }

  /* 移动端表格优化 */
  .bids-table .ant-table,
  .preorders-table .ant-table {
    font-size: 12px;
  }

  .bids-table .ant-table-thead > tr > th,
  .preorders-table .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .bids-table .ant-table-tbody > tr > td,
  .preorders-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
  }

  .product-details,
  .bid-time {
    font-size: 11px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bids-table .ant-table-tbody > tr,
.preorders-table .ant-table-tbody > tr {
  animation: fadeIn 0.3s ease-out;
}

/* 操作按钮样式 */
.bids-table .ant-btn-link,
.preorders-table .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 12px;
}

.bids-table .ant-btn-link:hover,
.preorders-table .ant-btn-link:hover {
  color: #ff6b9d;
}

/* 空状态样式 */
.bids-table .ant-empty,
.preorders-table .ant-empty {
  padding: 60px 20px;
}

.bids-table .ant-empty-description,
.preorders-table .ant-empty-description {
  color: #8c8c8c;
}

/* 模态框样式优化 */
.bid-detail .ant-descriptions {
  margin-top: 16px;
}

.bid-detail .ant-descriptions-item-content {
  word-break: break-all;
}

/* 价格高亮 */
.bid-price .ant-typography,
.current-price .ant-typography {
  font-weight: 600;
}

/* 状态图标动画 */
.ant-tag .anticon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 中标状态特殊样式 */
.ant-tag-green {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  border: none;
  color: #fff;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}
