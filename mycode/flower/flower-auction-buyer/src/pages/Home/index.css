.home-container {
  min-height: 100vh;
  background: #f0f2f5;
}

.home-header {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
  color: white;
  padding: 24px 0;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left .page-title {
  color: white !important;
  margin: 0 !important;
  margin-bottom: 8px !important;
}

.header-left .ant-typography {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
  min-width: 80px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: white;
  line-height: 1.2;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4px;
}

.home-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.search-bar {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
}

.search-bar .ant-input-search {
  border-radius: 8px;
}

.search-bar .ant-input-search .ant-input {
  border-radius: 8px 0 0 8px;
}

.search-bar .ant-input-search .ant-input-search-button {
  border-radius: 0 8px 8px 0;
  background: #ff6b9d;
  border-color: #ff6b9d;
}

.search-bar .ant-input-search .ant-input-search-button:hover {
  background: #ff4d7d;
  border-color: #ff4d7d;
}

.search-bar .ant-select {
  border-radius: 8px;
}

.search-bar .ant-btn {
  border-radius: 8px;
  border-color: #ff6b9d;
  color: #ff6b9d;
}

.search-bar .ant-btn:hover {
  border-color: #ff4d7d;
  color: #ff4d7d;
}

.batch-grid {
  min-height: 400px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  display: block;
}

.empty-state .ant-typography-h4 {
  color: #8c8c8c;
  margin-bottom: 8px !important;
}

.empty-state .ant-typography {
  color: #bfbfbf;
  max-width: 400px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    padding: 0 16px;
  }
  
  .header-stats {
    gap: 24px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .home-content {
    padding: 0 16px;
  }
  
  .search-bar {
    padding: 16px;
  }
  
  .search-bar .ant-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-bar .ant-col {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  .search-bar .ant-space {
    width: 100%;
    justify-content: space-between;
  }
  
  .search-bar .ant-space .ant-space-item {
    flex: 1;
  }
  
  .search-bar .ant-select,
  .search-bar .ant-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .home-header {
    padding: 16px 0;
    margin-bottom: 16px;
  }
  
  .header-content {
    padding: 0 12px;
  }
  
  .header-left .page-title {
    font-size: 20px !important;
  }
  
  .header-stats {
    gap: 16px;
  }
  
  .stat-item {
    min-width: 60px;
  }
  
  .stat-number {
    font-size: 18px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .home-content {
    padding: 0 12px;
  }
  
  .search-bar {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .search-bar .ant-space {
    flex-direction: column;
    width: 100%;
  }
  
  .search-bar .ant-space .ant-space-item {
    width: 100%;
  }
  
  .empty-state {
    padding: 40px 16px;
  }
  
  .empty-icon {
    font-size: 48px;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-grid .ant-col {
  animation: slideInUp 0.3s ease-out;
}

.batch-grid .ant-col:nth-child(1) { animation-delay: 0.1s; }
.batch-grid .ant-col:nth-child(2) { animation-delay: 0.2s; }
.batch-grid .ant-col:nth-child(3) { animation-delay: 0.3s; }
.batch-grid .ant-col:nth-child(4) { animation-delay: 0.4s; }

/* 搜索框聚焦效果 */
.search-bar .ant-input-search:focus-within {
  box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
}

/* 选择器聚焦效果 */
.search-bar .ant-select:focus-within {
  box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
}

/* 按钮悬停效果 */
.search-bar .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 107, 157, 0.2);
}
