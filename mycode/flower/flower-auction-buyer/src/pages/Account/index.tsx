import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Button, 
  Table, 
  Tag, 
  Typography, 
  Tabs, 
  Modal, 
  Form, 
  InputNumber, 
  Select, 
  message,
  Space,
  Descriptions,
  Progress,
  Alert
} from 'antd';
import { 
  WalletOutlined, 
  PlusOutlined, 
  ReloadOutlined,
  DollarOutlined,
  BankOutlined,
  CreditCardOutlined,
  SafetyOutlined,
  HistoryOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { buyerApi } from '../../services/api';
import { Account, Transaction, TransactionType, TransactionStatus } from '../../types';
import './index.css';

const { Title, Text } = Typography;
const { Option } = Select;

const AccountPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [account, setAccount] = useState<Account | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [rechargeModalVisible, setRechargeModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [form] = Form.useForm();

  useEffect(() => {
    loadAccountData();
  }, []);

  useEffect(() => {
    if (activeTab === 'transactions') {
      loadTransactions();
    }
  }, [activeTab, currentPage]);

  const loadAccountData = async () => {
    setLoading(true);
    try {
      const response = await buyerApi.getAccountBalance();
      if (response.success) {
        setAccount(response.data);
      }
    } catch (error: any) {
      message.error(error.message || '获取账户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async () => {
    setLoading(true);
    try {
      const response = await buyerApi.getTransactionHistory({
        page: currentPage,
        size: pageSize,
      });
      if (response.success) {
        setTransactions(response.data.list || []);
        setTotal(response.data.total || 0);
      }
    } catch (error: any) {
      message.error(error.message || '获取交易记录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRecharge = async (values: any) => {
    try {
      const response = await buyerApi.recharge({
        amount: values.amount,
        paymentMethod: values.paymentMethod,
      });
      if (response.success) {
        message.success('充值申请提交成功');
        setRechargeModalVisible(false);
        form.resetFields();
        loadAccountData();
      }
    } catch (error: any) {
      message.error(error.message || '充值失败');
    }
  };

  const getTransactionTypeTag = (type: TransactionType) => {
    const typeConfig = {
      [TransactionType.RECHARGE]: { color: 'green', text: '充值', icon: <PlusOutlined /> },
      [TransactionType.WITHDRAW]: { color: 'orange', text: '提现', icon: <BankOutlined /> },
      [TransactionType.BID_FREEZE]: { color: 'blue', text: '竞价冻结', icon: <SafetyOutlined /> },
      [TransactionType.BID_UNFREEZE]: { color: 'cyan', text: '竞价解冻', icon: <SafetyOutlined /> },
      [TransactionType.PAYMENT]: { color: 'red', text: '支付', icon: <DollarOutlined /> },
      [TransactionType.REFUND]: { color: 'purple', text: '退款', icon: <DollarOutlined /> },
    };
    const config = typeConfig[type];
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  const getTransactionStatusTag = (status: TransactionStatus) => {
    const statusConfig = {
      [TransactionStatus.PENDING]: { color: 'orange', text: '待处理' },
      [TransactionStatus.SUCCESS]: { color: 'green', text: '成功' },
      [TransactionStatus.FAILED]: { color: 'red', text: '失败' },
      [TransactionStatus.CANCELLED]: { color: 'default', text: '已取消' },
    };
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const transactionColumns = [
    {
      title: '交易类型',
      key: 'type',
      render: (record: Transaction) => getTransactionTypeTag(record.type),
    },
    {
      title: '金额',
      key: 'amount',
      render: (record: Transaction) => (
        <Text 
          strong 
          style={{ 
            color: record.type === TransactionType.RECHARGE || record.type === TransactionType.REFUND 
              ? '#52c41a' 
              : '#ff4d4f',
            fontSize: '14px'
          }}
        >
          {record.type === TransactionType.RECHARGE || record.type === TransactionType.REFUND ? '+' : '-'}
          ¥{record.amount.toFixed(2)}
        </Text>
      ),
    },
    {
      title: '余额',
      key: 'balance',
      render: (record: Transaction) => (
        <Text>¥{record.balance.toFixed(2)}</Text>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Transaction) => getTransactionStatusTag(record.status),
    },
    {
      title: '描述',
      key: 'description',
      dataIndex: 'description',
    },
    {
      title: '时间',
      key: 'createdAt',
      render: (record: Transaction) => (
        <Text type="secondary">
          {new Date(record.createdAt).toLocaleString()}
        </Text>
      ),
    },
  ];

  // 计算账户使用率
  const accountUsageRate = account ? 
    ((account.balance + account.frozenAmount) / account.creditLimit * 100) : 0;

  return (
    <div className="account-container">
      {/* 页面头部 */}
      <div className="account-header">
        <div className="header-left">
          <Title level={2} className="page-title">
            <WalletOutlined className="title-icon" />
            账户管理
          </Title>
          <Text type="secondary">
            管理您的账户余额和交易记录
          </Text>
        </div>
        <div className="header-right">
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadAccountData}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary"
              icon={<PlusOutlined />} 
              onClick={() => setRechargeModalVisible(true)}
            >
              充值
            </Button>
          </Space>
        </div>
      </div>

      {/* 账户概览卡片 */}
      <Row gutter={16} className="account-cards">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="可用余额"
              value={account?.balance || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="冻结金额"
              value={account?.frozenAmount || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总余额"
              value={(account?.balance || 0) + (account?.frozenAmount || 0)}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="信用额度"
              value={account?.creditLimit || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 账户状态 */}
      {account && (
        <Card className="account-status" title="账户状态">
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <div className="status-item">
                <Text strong>账户使用率</Text>
                <Progress 
                  percent={accountUsageRate} 
                  status={accountUsageRate > 80 ? 'exception' : 'active'}
                  strokeColor={accountUsageRate > 80 ? '#ff4d4f' : '#1890ff'}
                />
              </div>
            </Col>
            <Col xs={24} md={12}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="账户状态">
                  <Tag color="green">正常</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="最后更新">
                  {new Date(account.updatedAt).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
          
          {accountUsageRate > 80 && (
            <Alert
              message="账户使用率较高"
              description="您的账户使用率已超过80%，建议及时充值以确保正常使用。"
              type="warning"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </Card>
      )}

      {/* 标签页内容 */}
      <Card className="account-tabs">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={[
            {
              key: 'overview',
              label: '账户概览',
              children: (
                <div className="overview-content">
                  <Row gutter={16}>
                    <Col xs={24} md={12}>
                      <Card title="快速操作" size="small">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Button 
                            type="primary" 
                            icon={<PlusOutlined />} 
                            block
                            onClick={() => setRechargeModalVisible(true)}
                          >
                            账户充值
                          </Button>
                          <Button 
                            icon={<HistoryOutlined />} 
                            block
                            onClick={() => setActiveTab('transactions')}
                          >
                            查看交易记录
                          </Button>
                        </Space>
                      </Card>
                    </Col>
                    <Col xs={24} md={12}>
                      <Card title="安全提示" size="small">
                        <Space direction="vertical" size="small">
                          <Text type="secondary">
                            • 请妥善保管您的账户信息
                          </Text>
                          <Text type="secondary">
                            • 充值前请确认支付金额
                          </Text>
                          <Text type="secondary">
                            • 如有异常交易请及时联系客服
                          </Text>
                        </Space>
                      </Card>
                    </Col>
                  </Row>
                </div>
              )
            },
            {
              key: 'transactions',
              label: `交易记录 (${total})`,
              children: (
                <div className="transactions-content">
                  <Table
                    columns={transactionColumns}
                    dataSource={transactions}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                      current: currentPage,
                      pageSize: pageSize,
                      total: total,
                      onChange: setCurrentPage,
                      showSizeChanger: false,
                      showQuickJumper: true,
                      showTotal: (total, range) => 
                        `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    }}
                  />
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 充值模态框 */}
      <Modal
        title="账户充值"
        open={rechargeModalVisible}
        onCancel={() => {
          setRechargeModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleRecharge}
        >
          <Form.Item
            name="amount"
            label="充值金额"
            rules={[
              { required: true, message: '请输入充值金额' },
              { type: 'number', min: 1, message: '充值金额不能小于1元' },
              { type: 'number', max: 50000, message: '单次充值金额不能超过50000元' }
            ]}
          >
            <InputNumber 
              style={{ width: '100%' }} 
              placeholder="请输入充值金额"
              min={1}
              max={50000}
              step={1}
              addonAfter="元"
            />
          </Form.Item>

          <Form.Item
            name="paymentMethod"
            label="支付方式"
            rules={[{ required: true, message: '请选择支付方式' }]}
          >
            <Select placeholder="请选择支付方式">
              <Option value="alipay">
                <Space>
                  <CreditCardOutlined style={{ color: '#1890ff' }} />
                  支付宝
                </Space>
              </Option>
              <Option value="wechat">
                <Space>
                  <CreditCardOutlined style={{ color: '#52c41a' }} />
                  微信支付
                </Space>
              </Option>
              <Option value="bank">
                <Space>
                  <BankOutlined style={{ color: '#722ed1' }} />
                  银行卡
                </Space>
              </Option>
            </Select>
          </Form.Item>

          <Alert
            message="充值说明"
            description="充值金额将在支付成功后实时到账，如有问题请联系客服。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => {
                setRechargeModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确认充值
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AccountPage;
