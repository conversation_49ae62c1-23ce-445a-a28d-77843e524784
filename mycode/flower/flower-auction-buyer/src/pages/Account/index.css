.account-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left .page-title {
  margin-bottom: 8px !important;
  color: #ff6b9d;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #ff6b9d;
  font-size: 28px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-cards {
  margin-bottom: 24px;
}

.account-cards .ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.account-cards .ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.account-cards .ant-statistic-title {
  color: #8c8c8c;
  font-size: 14px;
  margin-bottom: 8px;
}

.account-cards .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.account-status {
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-item {
  margin-bottom: 16px;
}

.status-item .ant-typography {
  display: block;
  margin-bottom: 8px;
}

.account-tabs {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.account-tabs .ant-card-body {
  padding: 0;
}

.account-tabs .ant-tabs {
  margin: 0;
}

.account-tabs .ant-tabs-tab {
  padding: 16px 24px;
  font-size: 16px;
}

.overview-content,
.transactions-content {
  padding: 24px;
}

.overview-content .ant-card {
  height: 100%;
}

.transactions-content .ant-table {
  background: #fff;
}

/* 交易记录表格样式 */
.transactions-content .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 主题色调整 */
.account-container .ant-btn-primary {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.account-container .ant-btn-primary:hover {
  background: linear-gradient(135deg, #ff4d7d 0%, #ff3366 100%);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
}

.account-container .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #ff6b9d;
}

.account-container .ant-tabs-ink-bar {
  background: #ff6b9d;
}

.account-container .ant-pagination-item-active {
  border-color: #ff6b9d;
  background: #ff6b9d;
}

.account-container .ant-pagination-item-active a {
  color: #fff;
}

.account-container .ant-pagination-item:hover {
  border-color: #ff6b9d;
}

.account-container .ant-pagination-item:hover a {
  color: #ff6b9d;
}

.account-container .ant-progress-bg {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-container {
    padding: 16px;
  }

  .account-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left .page-title {
    font-size: 20px !important;
  }

  .header-right {
    justify-content: flex-end;
  }

  .account-cards {
    margin-bottom: 16px;
  }

  .account-cards .ant-statistic-content {
    font-size: 20px;
  }

  .account-tabs .ant-tabs-tab {
    padding: 12px 16px;
    font-size: 14px;
  }

  .overview-content,
  .transactions-content {
    padding: 16px;
  }

  .status-item {
    margin-bottom: 12px;
  }
}

@media (max-width: 576px) {
  .account-container {
    padding: 12px;
  }

  .account-header {
    padding: 16px;
  }

  .header-left .page-title {
    font-size: 18px !important;
  }

  .title-icon {
    font-size: 24px;
  }

  .account-cards .ant-statistic-content {
    font-size: 18px;
  }

  .account-tabs .ant-tabs-tab {
    padding: 10px 12px;
    font-size: 13px;
  }

  .overview-content,
  .transactions-content {
    padding: 12px;
  }

  /* 移动端表格优化 */
  .transactions-content .ant-table {
    font-size: 12px;
  }

  .transactions-content .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .transactions-content .ant-table-tbody > tr > td {
    padding: 8px 4px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.account-cards .ant-card,
.account-status,
.account-tabs {
  animation: fadeIn 0.3s ease-out;
}

.account-cards .ant-card:nth-child(1) { animation-delay: 0.1s; }
.account-cards .ant-card:nth-child(2) { animation-delay: 0.2s; }
.account-cards .ant-card:nth-child(3) { animation-delay: 0.3s; }
.account-cards .ant-card:nth-child(4) { animation-delay: 0.4s; }

/* 统计数字动画 */
.account-cards .ant-statistic-content-value {
  transition: all 0.3s ease;
}

/* 进度条样式优化 */
.account-container .ant-progress-line {
  margin-bottom: 8px;
}

.account-container .ant-progress-text {
  font-weight: 600;
}

/* 充值模态框样式 */
.account-container .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.account-container .ant-modal-title {
  color: #ff6b9d;
  font-weight: 600;
}

.account-container .ant-form-item-label > label {
  font-weight: 500;
}

.account-container .ant-select-selector {
  border-radius: 6px;
}

.account-container .ant-input-number {
  border-radius: 6px;
}

/* 快速操作按钮样式 */
.overview-content .ant-btn {
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
}

/* 安全提示样式 */
.overview-content .ant-typography {
  line-height: 1.6;
}

/* 表格行悬停效果 */
.transactions-content .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 金额显示样式 */
.transactions-content .ant-typography {
  font-family: 'Roboto Mono', monospace;
}

/* 状态标签图标动画 */
.ant-tag .anticon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
