.watchlist-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.watchlist-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left .page-title {
  margin-bottom: 8px !important;
  color: #ff6b9d;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #ff6b9d;
  font-size: 28px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.watchlist-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: #fff;
  border-radius: 8px;
  margin: 24px;
}

.error-card {
  margin-bottom: 24px;
  border-left: 4px solid #ff4d4f;
}

.empty-card {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.watchlist-grid {
  margin-bottom: 24px;
}

.watchlist-item {
  position: relative;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.watchlist-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.watch-info {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.watch-time {
  font-size: 12px;
}

.remove-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

.remove-btn:hover {
  background: #fff2f0;
  border-color: #ff4d4f;
}

.watchlist-pagination {
  display: flex;
  justify-content: center;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .watchlist-container {
    padding: 16px;
  }

  .watchlist-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left .page-title {
    font-size: 20px !important;
  }

  .header-right {
    justify-content: flex-end;
  }

  .watch-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .remove-btn {
    align-self: flex-end;
  }
}

@media (max-width: 576px) {
  .watchlist-container {
    padding: 12px;
  }

  .watchlist-header {
    padding: 16px;
  }

  .header-left .page-title {
    font-size: 18px !important;
  }

  .title-icon {
    font-size: 24px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.watchlist-item {
  animation: fadeIn 0.3s ease-out;
}

.watchlist-item:nth-child(1) { animation-delay: 0.1s; }
.watchlist-item:nth-child(2) { animation-delay: 0.2s; }
.watchlist-item:nth-child(3) { animation-delay: 0.3s; }
.watchlist-item:nth-child(4) { animation-delay: 0.4s; }

/* 主题色调整 */
.watchlist-container .ant-btn-primary {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.watchlist-container .ant-btn-primary:hover {
  background: linear-gradient(135deg, #ff4d7d 0%, #ff3366 100%);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
}

.watchlist-container .ant-pagination-item-active {
  border-color: #ff6b9d;
  background: #ff6b9d;
}

.watchlist-container .ant-pagination-item-active a {
  color: #fff;
}

.watchlist-container .ant-pagination-item:hover {
  border-color: #ff6b9d;
}

.watchlist-container .ant-pagination-item:hover a {
  color: #ff6b9d;
}
