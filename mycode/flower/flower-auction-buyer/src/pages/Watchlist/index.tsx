import React, { useEffect, useState } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Empty, 
  Spin, 
  Button, 
  Typography, 
  Space,
  Pagination,
  message,
  Modal
} from 'antd';
import { 
  HeartOutlined, 
  DeleteOutlined, 
  ReloadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { getWatchListAsync, removeFromWatchListAsync } from '../../store/slices/batchSlice';
import BatchCard from '../../components/BatchCard';
import './index.css';

const { Title, Text } = Typography;
const { confirm } = Modal;

const Watchlist: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { watchList, loading, error } = useSelector((state: RootState) => state.batch);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);

  useEffect(() => {
    loadWatchlist();
  }, [dispatch, currentPage]);

  const loadWatchlist = () => {
    dispatch(getWatchListAsync());
  };

  const handleRemoveFromWatchlist = (batchId: number, batchName: string) => {
    confirm({
      title: '确认取消关注',
      icon: <ExclamationCircleOutlined />,
      content: `确定要取消关注"${batchName}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(removeFromWatchListAsync(batchId)).unwrap();
          message.success('已取消关注');
          loadWatchlist(); // 重新加载列表
        } catch (error: any) {
          message.error(error || '取消关注失败');
        }
      },
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRefresh = () => {
    setCurrentPage(1);
    loadWatchlist();
  };

  if (loading && watchList.length === 0) {
    return (
      <div className="watchlist-loading">
        <Spin size="large" />
        <Text style={{ marginTop: 16, display: 'block', textAlign: 'center' }}>
          正在加载关注列表...
        </Text>
      </div>
    );
  }

  return (
    <div className="watchlist-container">
      {/* 页面头部 */}
      <div className="watchlist-header">
        <div className="header-left">
          <Title level={2} className="page-title">
            <HeartOutlined className="title-icon" />
            我的关注
          </Title>
          <Text type="secondary">
            共关注了 {watchList.length} 个批次
          </Text>
        </div>
        <div className="header-right">
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <Card className="error-card">
          <Text type="danger">{error}</Text>
          <Button 
            type="link" 
            onClick={handleRefresh}
            style={{ marginLeft: 8 }}
          >
            重试
          </Button>
        </Card>
      )}

      {/* 关注列表 */}
      {watchList.length === 0 && !loading ? (
        <Card className="empty-card">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <div>
                <Text type="secondary">还没有关注任何批次</Text>
                <br />
                <Text type="secondary">去首页看看有什么感兴趣的吧</Text>
              </div>
            }
          >
            <Button type="primary" href="/">
              去首页看看
            </Button>
          </Empty>
        </Card>
      ) : (
        <>
          <Row gutter={[16, 16]} className="watchlist-grid">
            {watchList.map((item) => (
              <Col xs={24} sm={12} md={8} lg={6} xl={6} key={item.id}>
                <div className="watchlist-item">
                  <BatchCard 
                    batch={{
                      id: item.batchId,
                      batchNumber: item.batchNumber || `批次${item.batchId}`,
                      productName: item.productName || '未知商品',
                      category: item.category || '其他',
                      grade: item.grade || 'A',
                      quantity: item.quantity || 0,
                      unit: item.unit || '支',
                      startPrice: item.startPrice || 0,
                      currentPrice: item.currentPrice || item.startPrice || 0,
                      stepPrice: item.stepPrice || 0.1,
                      status: item.status || 'pending',
                      startTime: item.startTime || new Date().toISOString(),
                      endTime: item.endTime,
                      bidCount: item.bidCount || 0,
                      watchCount: item.watchCount || 0,
                      clockNumber: item.clockNumber || 1,
                      qualityLevel: item.qualityLevel || 'A',
                      isWatched: true, // 关注列表中的都是已关注的
                      imageUrl: item.imageUrl,
                    }}
                    onWatchToggle={() => handleRemoveFromWatchlist(item.batchId, item.productName || `批次${item.batchId}`)}
                  />
                  
                  {/* 关注时间显示 */}
                  <div className="watch-info">
                    <Text type="secondary" className="watch-time">
                      关注时间: {new Date(item.createdAt || '').toLocaleDateString()}
                    </Text>
                    <Button
                      type="text"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleRemoveFromWatchlist(item.batchId, item.productName || `批次${item.batchId}`)}
                      className="remove-btn"
                    >
                      取消关注
                    </Button>
                  </div>
                </div>
              </Col>
            ))}
          </Row>

          {/* 分页 */}
          {watchList.length > 0 && (
            <div className="watchlist-pagination">
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={watchList.length}
                onChange={handlePageChange}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
              />
            </div>
          )}
        </>
      )}

      {/* 加载更多时的loading */}
      {loading && watchList.length > 0 && (
        <div className="loading-more">
          <Spin />
          <Text style={{ marginLeft: 8 }}>加载中...</Text>
        </div>
      )}
    </div>
  );
};

export default Watchlist;
