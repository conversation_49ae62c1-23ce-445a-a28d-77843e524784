import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Table, 
  Tag, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  Empty,
  message,
  Modal,
  Descriptions,
  Image,
  Tooltip
} from 'antd';
import { 
  ShoppingCartOutlined, 
  EyeOutlined, 
  PayCircleOutlined,
  TruckOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { Order, OrderStatus, PaymentStatus, DeliveryStatus } from '../../types';
import './index.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

const Orders: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    loadOrders();
  }, [activeTab, currentPage]);

  const loadOrders = async () => {
    setLoading(true);
    try {
      // TODO: 调用API获取订单列表
      // const response = await orderApi.getMyOrders({
      //   page: currentPage,
      //   size: pageSize,
      //   status: activeTab === 'all' ? undefined : getStatusByTab(activeTab)
      // });
      
      // 模拟数据
      const mockOrders: Order[] = [
        {
          id: 1,
          orderNumber: 'ORD202412010001',
          userId: 1,
          batchId: 1,
          price: 15.50,
          quantity: 100,
          totalAmount: 1550.00,
          status: OrderStatus.CONFIRMED,
          paymentStatus: PaymentStatus.PAID,
          deliveryStatus: DeliveryStatus.SHIPPED,
          createdAt: '2024-12-01T10:30:00Z',
          updatedAt: '2024-12-01T14:20:00Z',
          batch: {
            id: 1,
            batchNumber: 'B20241201001',
            productName: '红玫瑰',
            category: '玫瑰',
            grade: 'A',
            quantity: 100,
            unit: '支',
            startPrice: 10.00,
            currentPrice: 15.50,
            stepPrice: 0.50,
            status: 'completed',
            startTime: '2024-12-01T09:00:00Z',
            endTime: '2024-12-01T10:30:00Z',
            bidCount: 25,
            watchCount: 50,
            clockNumber: 1,
            qualityLevel: 'A',
            isWatched: false,
          }
        },
        {
          id: 2,
          orderNumber: 'ORD202412010002',
          userId: 1,
          batchId: 2,
          price: 8.80,
          quantity: 200,
          totalAmount: 1760.00,
          status: OrderStatus.PENDING,
          paymentStatus: PaymentStatus.UNPAID,
          deliveryStatus: DeliveryStatus.PENDING,
          createdAt: '2024-12-01T11:15:00Z',
          updatedAt: '2024-12-01T11:15:00Z',
          batch: {
            id: 2,
            batchNumber: 'B20241201002',
            productName: '白百合',
            category: '百合',
            grade: 'A',
            quantity: 200,
            unit: '支',
            startPrice: 6.00,
            currentPrice: 8.80,
            stepPrice: 0.20,
            status: 'completed',
            startTime: '2024-12-01T10:30:00Z',
            endTime: '2024-12-01T11:15:00Z',
            bidCount: 18,
            watchCount: 32,
            clockNumber: 2,
            qualityLevel: 'A',
            isWatched: true,
          }
        }
      ];

      setOrders(mockOrders);
      setTotal(mockOrders.length);
    } catch (error: any) {
      message.error(error.message || '获取订单列表失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusByTab = (tab: string) => {
    switch (tab) {
      case 'pending': return OrderStatus.PENDING;
      case 'confirmed': return OrderStatus.CONFIRMED;
      case 'cancelled': return OrderStatus.CANCELLED;
      case 'completed': return OrderStatus.COMPLETED;
      default: return undefined;
    }
  };

  const getOrderStatusTag = (status: OrderStatus) => {
    const statusConfig = {
      [OrderStatus.PENDING]: { color: 'orange', text: '待确认' },
      [OrderStatus.CONFIRMED]: { color: 'blue', text: '已确认' },
      [OrderStatus.CANCELLED]: { color: 'red', text: '已取消' },
      [OrderStatus.COMPLETED]: { color: 'green', text: '已完成' },
    };
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getPaymentStatusTag = (status: PaymentStatus) => {
    const statusConfig = {
      [PaymentStatus.UNPAID]: { color: 'red', text: '未支付' },
      [PaymentStatus.PAID]: { color: 'green', text: '已支付' },
      [PaymentStatus.REFUNDED]: { color: 'orange', text: '已退款' },
    };
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getDeliveryStatusTag = (status: DeliveryStatus) => {
    const statusConfig = {
      [DeliveryStatus.PENDING]: { color: 'default', text: '待发货' },
      [DeliveryStatus.SHIPPED]: { color: 'blue', text: '已发货' },
      [DeliveryStatus.DELIVERED]: { color: 'green', text: '已送达' },
      [DeliveryStatus.PICKED_UP]: { color: 'green', text: '已提货' },
    };
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const handleViewDetail = (order: Order) => {
    setSelectedOrder(order);
    setDetailModalVisible(true);
  };

  const handlePayment = (order: Order) => {
    confirm({
      title: '确认支付',
      icon: <ExclamationCircleOutlined />,
      content: `确定要支付订单 ${order.orderNumber} 吗？金额：¥${order.totalAmount}`,
      okText: '确定支付',
      cancelText: '取消',
      onOk: async () => {
        try {
          // TODO: 调用支付API
          message.success('支付成功');
          loadOrders();
        } catch (error: any) {
          message.error(error.message || '支付失败');
        }
      },
    });
  };

  const handleConfirmDelivery = (order: Order) => {
    confirm({
      title: '确认收货',
      icon: <ExclamationCircleOutlined />,
      content: `确定已收到订单 ${order.orderNumber} 的货物吗？`,
      okText: '确认收货',
      cancelText: '取消',
      onOk: async () => {
        try {
          // TODO: 调用确认收货API
          message.success('确认收货成功');
          loadOrders();
        } catch (error: any) {
          message.error(error.message || '确认收货失败');
        }
      },
    });
  };

  const columns = [
    {
      title: '订单信息',
      key: 'orderInfo',
      render: (record: Order) => (
        <div className="order-info">
          <div className="order-number">
            <Text strong>{record.orderNumber}</Text>
          </div>
          <div className="order-time">
            <Text type="secondary">
              下单时间: {new Date(record.createdAt).toLocaleString()}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '商品信息',
      key: 'productInfo',
      render: (record: Order) => (
        <div className="product-info">
          <div className="product-name">
            <Text strong>{record.batch?.productName}</Text>
          </div>
          <div className="product-details">
            <Text type="secondary">
              批次: {record.batch?.batchNumber} | 
              规格: {record.batch?.grade} | 
              数量: {record.quantity}{record.batch?.unit}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '金额',
      key: 'amount',
      render: (record: Order) => (
        <div className="amount-info">
          <div className="total-amount">
            <Text strong style={{ color: '#ff6b9d', fontSize: '16px' }}>
              ¥{record.totalAmount.toFixed(2)}
            </Text>
          </div>
          <div className="unit-price">
            <Text type="secondary">
              单价: ¥{record.price.toFixed(2)}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Order) => (
        <div className="status-info">
          <div>{getOrderStatusTag(record.status)}</div>
          <div>{getPaymentStatusTag(record.paymentStatus)}</div>
          <div>{getDeliveryStatusTag(record.deliveryStatus)}</div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Order) => (
        <Space direction="vertical" size="small">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>
          {record.paymentStatus === PaymentStatus.UNPAID && (
            <Button 
              type="link" 
              icon={<PayCircleOutlined />} 
              onClick={() => handlePayment(record)}
            >
              立即支付
            </Button>
          )}
          {record.deliveryStatus === DeliveryStatus.DELIVERED && (
            <Button 
              type="link" 
              icon={<CheckCircleOutlined />} 
              onClick={() => handleConfirmDelivery(record)}
            >
              确认收货
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const filteredOrders = activeTab === 'all' 
    ? orders 
    : orders.filter(order => order.status === getStatusByTab(activeTab));

  return (
    <div className="orders-container">
      {/* 页面头部 */}
      <div className="orders-header">
        <div className="header-left">
          <Title level={2} className="page-title">
            <ShoppingCartOutlined className="title-icon" />
            我的订单
          </Title>
          <Text type="secondary">
            共 {total} 个订单
          </Text>
        </div>
        <div className="header-right">
          <Button 
            icon={<ReloadOutlined />} 
            onClick={loadOrders}
            loading={loading}
          >
            刷新
          </Button>
        </div>
      </div>

      {/* 订单标签页 */}
      <Card className="orders-tabs">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={[
            { key: 'all', label: '全部订单' },
            { key: 'pending', label: '待确认' },
            { key: 'confirmed', label: '已确认' },
            { key: 'completed', label: '已完成' },
            { key: 'cancelled', label: '已取消' },
          ]}
        />
      </Card>

      {/* 订单列表 */}
      <Card className="orders-table">
        {filteredOrders.length === 0 && !loading ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无订单"
          >
            <Button type="primary" href="/">
              去首页看看
            </Button>
          </Empty>
        ) : (
          <Table
            columns={columns}
            dataSource={filteredOrders}
            rowKey="id"
            loading={loading}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: total,
              onChange: setCurrentPage,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
          />
        )}
      </Card>

      {/* 订单详情模态框 */}
      <Modal
        title="订单详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div className="order-detail">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="订单号" span={2}>
                {selectedOrder.orderNumber}
              </Descriptions.Item>
              <Descriptions.Item label="商品名称">
                {selectedOrder.batch?.productName}
              </Descriptions.Item>
              <Descriptions.Item label="批次号">
                {selectedOrder.batch?.batchNumber}
              </Descriptions.Item>
              <Descriptions.Item label="规格等级">
                {selectedOrder.batch?.grade}
              </Descriptions.Item>
              <Descriptions.Item label="数量">
                {selectedOrder.quantity}{selectedOrder.batch?.unit}
              </Descriptions.Item>
              <Descriptions.Item label="单价">
                ¥{selectedOrder.price.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="总金额">
                <Text strong style={{ color: '#ff6b9d', fontSize: '16px' }}>
                  ¥{selectedOrder.totalAmount.toFixed(2)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="订单状态">
                {getOrderStatusTag(selectedOrder.status)}
              </Descriptions.Item>
              <Descriptions.Item label="支付状态">
                {getPaymentStatusTag(selectedOrder.paymentStatus)}
              </Descriptions.Item>
              <Descriptions.Item label="配送状态">
                {getDeliveryStatusTag(selectedOrder.deliveryStatus)}
              </Descriptions.Item>
              <Descriptions.Item label="下单时间">
                {new Date(selectedOrder.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(selectedOrder.updatedAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Orders;
