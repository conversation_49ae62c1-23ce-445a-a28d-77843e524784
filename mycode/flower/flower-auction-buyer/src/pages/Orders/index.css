.orders-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left .page-title {
  margin-bottom: 8px !important;
  color: #ff6b9d;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #ff6b9d;
  font-size: 28px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.orders-tabs {
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.orders-tabs .ant-card-body {
  padding: 0;
}

.orders-tabs .ant-tabs {
  margin: 0;
}

.orders-tabs .ant-tabs-tab {
  padding: 16px 24px;
  font-size: 16px;
}

.orders-table {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.order-info {
  min-width: 180px;
}

.order-number {
  margin-bottom: 4px;
}

.order-time {
  font-size: 12px;
}

.product-info {
  min-width: 200px;
}

.product-name {
  margin-bottom: 4px;
}

.product-details {
  font-size: 12px;
}

.amount-info {
  text-align: right;
  min-width: 120px;
}

.total-amount {
  margin-bottom: 4px;
}

.unit-price {
  font-size: 12px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 100px;
}

.order-detail .ant-descriptions-item-label {
  background: #fafafa;
  font-weight: 500;
}

/* 主题色调整 */
.orders-container .ant-btn-primary {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff4d7d 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.orders-container .ant-btn-primary:hover {
  background: linear-gradient(135deg, #ff4d7d 0%, #ff3366 100%);
  box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
}

.orders-container .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #ff6b9d;
}

.orders-container .ant-tabs-ink-bar {
  background: #ff6b9d;
}

.orders-container .ant-pagination-item-active {
  border-color: #ff6b9d;
  background: #ff6b9d;
}

.orders-container .ant-pagination-item-active a {
  color: #fff;
}

.orders-container .ant-pagination-item:hover {
  border-color: #ff6b9d;
}

.orders-container .ant-pagination-item:hover a {
  color: #ff6b9d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .orders-container {
    padding: 16px;
  }

  .orders-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left .page-title {
    font-size: 20px !important;
  }

  .header-right {
    justify-content: flex-end;
  }

  .orders-tabs .ant-tabs-tab {
    padding: 12px 16px;
    font-size: 14px;
  }

  .order-info,
  .product-info,
  .amount-info,
  .status-info {
    min-width: auto;
  }

  .orders-table .ant-table-tbody > tr > td {
    padding: 12px 8px;
  }
}

@media (max-width: 576px) {
  .orders-container {
    padding: 12px;
  }

  .orders-header {
    padding: 16px;
  }

  .header-left .page-title {
    font-size: 18px !important;
  }

  .title-icon {
    font-size: 24px;
  }

  .orders-tabs .ant-tabs-tab {
    padding: 10px 12px;
    font-size: 13px;
  }

  /* 移动端表格优化 */
  .orders-table .ant-table {
    font-size: 12px;
  }

  .orders-table .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .orders-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
  }

  .product-details,
  .order-time,
  .unit-price {
    font-size: 11px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.orders-table .ant-table-tbody > tr {
  animation: fadeIn 0.3s ease-out;
}

/* 状态标签样式优化 */
.status-info .ant-tag {
  margin: 2px 0;
  border-radius: 4px;
  font-size: 11px;
  padding: 2px 6px;
}

/* 操作按钮样式 */
.orders-table .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 12px;
}

.orders-table .ant-btn-link:hover {
  color: #ff6b9d;
}

/* 空状态样式 */
.orders-table .ant-empty {
  padding: 60px 20px;
}

.orders-table .ant-empty-description {
  color: #8c8c8c;
}

/* 模态框样式优化 */
.order-detail .ant-descriptions {
  margin-top: 16px;
}

.order-detail .ant-descriptions-item-content {
  word-break: break-all;
}
