// 购买商端类型定义

// 用户相关类型
export interface User {
  id: number;
  username: string;
  realName: string;
  userType: number; // 2: 购买商
  status: number;
  phone?: string;
  email?: string;
  companyName?: string;
  creditLevel?: number;
  balance: number;
  frozenAmount: number;
  roles: Role[];
}

export interface Role {
  id: number;
  name: string;
  description: string;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  confirmPassword: string;
  realName: string;
  phone: string;
  email?: string;
  companyName?: string;
  verificationCode: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: number;
}

// 批次相关类型
export interface Batch {
  id: number;
  batchNumber: string;
  productName: string;
  categoryId: number;
  categoryName: string;
  quantity: number;
  unit: string;
  qualityLevel: number;
  startPrice: number;
  currentPrice?: number;
  status: BatchStatus;
  clockNumber: number;
  supplierId: number;
  supplierName: string;
  warehouseLocation: string;
  qualityReport?: QualityReport;
  images: string[];
  description?: string;
  createdAt: string;
  updatedAt: string;
  auctionStartTime?: string;
  auctionEndTime?: string;
  isWatched?: boolean; // 是否关注
  watchCount: number; // 关注人数
}

export enum BatchStatus {
  PENDING = 0,      // 待起拍
  BIDDING = 1,      // 竞拍中
  SOLD = 2,         // 已成交
  UNSOLD = 3,       // 流拍
  CANCELLED = 4     // 已取消
}

export interface QualityReport {
  id: number;
  batchId: number;
  inspector: string;
  qualityLevel: number;
  defects: string[];
  notes: string;
  images: string[];
  createdAt: string;
}

// 竞价相关类型
export interface Bid {
  id: number;
  batchId: number;
  userId: number;
  username: string;
  price: number;
  quantity: number;
  timestamp: string;
  status: number;
  isMyBid?: boolean;
}

export interface BidRequest {
  batchId: number;
  price: number;
  quantity: number;
}

export interface AutoBidConfig {
  batchId: number;
  maxPrice: number;
  quantity: number;
  priceStep: number;
  enabled: boolean;
}

// 埋单相关类型
export interface PreOrder {
  id: number;
  batchId: number;
  userId: number;
  price: number;
  quantity: number;
  status: PreOrderStatus;
  createdAt: string;
  updatedAt: string;
  batch?: Batch;
}

export enum PreOrderStatus {
  PENDING = 0,    // 待生效
  ACTIVE = 1,     // 生效中
  EXECUTED = 2,   // 已执行
  CANCELLED = 3,  // 已取消
  EXPIRED = 4     // 已过期
}

// 关注相关类型
export interface WatchList {
  id: number;
  batchId: number;
  userId: number;
  createdAt: string;
  batch?: Batch;
  notifyOnStart?: boolean;
  notifyOnPriceChange?: boolean;
  notifyOnEnd?: boolean;
}

// 资金相关类型
export interface Account {
  id: number;
  userId: number;
  balance: number;
  frozenAmount: number;
  totalRecharge: number;
  totalWithdraw: number;
  creditLimit: number;
  updatedAt: string;
}

export interface Transaction {
  id: number;
  userId: number;
  type: TransactionType;
  amount: number;
  balance: number;
  description: string;
  relatedId?: number;
  status: TransactionStatus;
  createdAt: string;
}

export enum TransactionType {
  RECHARGE = 1,     // 充值
  WITHDRAW = 2,     // 提现
  BID_FREEZE = 3,   // 竞价冻结
  BID_UNFREEZE = 4, // 竞价解冻
  PAYMENT = 5,      // 支付
  REFUND = 6        // 退款
}

export enum TransactionStatus {
  PENDING = 0,    // 待处理
  SUCCESS = 1,    // 成功
  FAILED = 2,     // 失败
  CANCELLED = 3   // 已取消
}

// 订单相关类型
export interface Order {
  id: number;
  orderNumber: string;
  userId: number;
  batchId: number;
  price: number;
  quantity: number;
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  deliveryStatus: DeliveryStatus;
  createdAt: string;
  updatedAt: string;
  batch?: Batch;
}

export enum OrderStatus {
  PENDING = 0,    // 待确认
  CONFIRMED = 1,  // 已确认
  CANCELLED = 2,  // 已取消
  COMPLETED = 3   // 已完成
}

export enum PaymentStatus {
  UNPAID = 0,     // 未支付
  PAID = 1,       // 已支付
  REFUNDED = 2    // 已退款
}

export enum DeliveryStatus {
  PENDING = 0,    // 待发货
  SHIPPED = 1,    // 已发货
  DELIVERED = 2,  // 已送达
  PICKED_UP = 3   // 已提货
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'bid' | 'auction_start' | 'auction_end' | 'price_update' | 'status_update' | 'notification';
  data: any;
  timestamp: string;
  clockNumber?: number;
  batchId?: number;
}

// 通知相关类型
export interface Notification {
  id: number;
  userId: number;
  type: NotificationType;
  title: string;
  content: string;
  relatedId?: number;
  isRead: boolean;
  createdAt: string;
}

export enum NotificationType {
  AUCTION_START = 1,    // 拍卖开始
  PRICE_CHANGE = 2,     // 价格变动
  AUCTION_END = 3,      // 拍卖结束
  BID_SUCCESS = 4,      // 出价成功
  BID_OUTBID = 5,       // 被超价
  ORDER_CONFIRM = 6,    // 订单确认
  PAYMENT_SUCCESS = 7,  // 支付成功
  SYSTEM_NOTICE = 8     // 系统通知
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
}

// 分页类型
export interface PaginationParams {
  page: number;
  size: number;
  total?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  totalPages: number;
}

// 查询参数类型
export interface BatchQueryParams extends PaginationParams {
  status?: BatchStatus;
  clockNumber?: number;
  categoryId?: number;
  supplierId?: number;
  startDate?: string;
  endDate?: string;
  keyword?: string;
  priceMin?: number;
  priceMax?: number;
  qualityLevel?: number;
  onlyWatched?: boolean;
}

// 统计类型
export interface BuyerStatistics {
  totalBids: number;
  successfulBids: number;
  totalSpent: number;
  averagePrice: number;
  watchedBatches: number;
  activePreOrders: number;
  todayStats: {
    bids: number;
    spent: number;
    orders: number;
  };
}

// 实时拍卖数据
export interface AuctionRealTimeData {
  batchId: number;
  clockNumber: number;
  currentPrice: number;
  bidCount: number;
  onlineBuyers: number;
  timeRemaining: number;
  lastBid?: Bid;
  priceHistory: Array<{
    price: number;
    timestamp: string;
  }>;
}
